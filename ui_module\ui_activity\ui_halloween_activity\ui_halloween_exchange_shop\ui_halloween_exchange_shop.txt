local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local os = os
local UIUtil = CS.Common_Util.UIUtil
local Color = CS.UnityEngine.Color
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local shining_market_define = require "shining_market_define"
local binding = require "ui_halloween_exchange_shop_binding"
local card_sprite_asset = require "card_sprite_asset"
local color_palette = require "color_palette"
local time_util = require "time_util"
local game_scheme = require "game_scheme"
--region View Life
module("ui_halloween_exchange_shop")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self:InitScrollRectTable() 

end


--初始化商品列表
function UIView:InitScrollRectTable() 
    self.srt_shopListContent.onItemRender = function(...)
        self:OnItemRender(...)
    end
    self.srt_shopListContent.onItemDispose =function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data  then
          --rect_table Dispose时 Item上相关的资源是否需要Dispose
            if scroll_rect_item.data["itemUI"] then
                scroll_rect_item.data["itemUI"]:Dispose()
                scroll_rect_item.data["itemUI"] = nil
            end
        end       
    end
end

function UIView:OnItemRender(scroll_rect_item,index,dataItem) 
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local data = dataItem
    local flag = scroll_rect_item:Get("flag")
    local iconRoot = scroll_rect_item:Get("iconRoot")
    local limit_num = scroll_rect_item:Get("limit_num")
    local icon = scroll_rect_item:Get("icon")
    local price = scroll_rect_item:Get("price")
    local red = scroll_rect_item:Get("red")
    local mask = scroll_rect_item:Get("mask")
    local flag_txt = scroll_rect_item:Get("flag_txt")
    local priceOutline = scroll_rect_item:Get("priceOutline")

    if data.flagType then 
        flag.transform:SetActive(true)
        local condition = data.flagType - 1 == 0 and true or false
        flag:Switch(condition and 0 or 1) --超值 or 稀有 标签
        if data.flagType == shining_market_define.GoodsFlagType.rare then 
            flag_txt.text = lang.Get(1000910)
        elseif data.flagType == shining_market_define.GoodsFlagType.superValue then
            flag_txt.text = lang.Get(1000912)
        end
    else
        flag.transform:SetActive(false)
    end
     
     --点击道具显示信息 生成物品对象
    local OnShowDetail = function(entity, id, attachData)
        local iui_item_detail = require "iui_item_detail"
        local item_data =require "item_data"
        iui_item_detail.Show(id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil, attachData)
    end 
    local goods_item_new = require "goods_item_new"
    if scroll_rect_item.data["itemUI"] then
        scroll_rect_item.data["itemUI"]:Dispose()
    end   
    scroll_rect_item.data["itemUI"] = goods_item_new.CGoodsItem():Init(iconRoot.transform,nil,0.75)
    scroll_rect_item.data["itemUI"]:SetGoods(nil,data.rewardId,data.rewardNum,OnShowDetail,nil,nil)
    iconRoot.transform:SetActive(true)
    --限购
    -- if data.curCount then 
    --     Limit.transform:SetActive(true)
    --     limit_num.text = string.format( "%s/%s",data.curCount,data.maxBuyNum)
    -- else
    --     Limit.transform:SetActive(false)
    -- end
    if data.limitType == 0 then
        limit_num.text = string.format( lang.Get(1003117),data.maxBuyNum - data.curCount)
    elseif data.limitType == 1 then
        limit_num.text = string.format( lang.Get(1003118),data.maxBuyNum - data.curCount)
    elseif data.limitType == 2 then
        limit_num.text = string.format( lang.Get(1001353),data.curCount,data.maxBuyNum)
    else
        limit_num.text = ""
    end
    --兑换货币信息 Icon 和 数量
    if data.priceInfo then 
        local itemCfg = game_scheme:Item_0(data.priceInfo.costItem)
        if itemCfg then 
            self:CreateSubSprite("CreateSpriteAsset",icon,itemCfg.icon)
        end
        price.text = data.priceInfo.num
        price.color = data.canBuyItem and Color.white or color_palette.HexToColor("#FF3D3D")
        priceOutline.effectColor = data.canBuyItem and color_palette.HexToColor("#833607") or color_palette.HexToColor("#572100")
    end
    --售罄遮罩
    if data.canBuyState then 
        mask.transform:SetActive(false)
    else
        mask.transform:SetActive(true)
    end
    self:SetActive(red,data.isExchangeTip and data.canBuyItem and data.canBuyState)
    scroll_rect_item.InvokeFunc = function(funcname)
        --注意  这里的事件是存在多种Event的 
        if dataItem[funcname] then
            dataItem[funcname](index,dataItem)
        end
    end
end


--更新商品列表
function UIView:UpdateShopScrollList(data,len) 
    if not data then 
        return
    end
    self.srt_shopListContent:SetData(data,len or #data)
    self.srt_shopListContent:Refresh(0, -1)
end

function UIView:ShowTopInfo(data) 
    local ui_gwtop_resource = require "ui_gwtop_resource"
    --设置倒计时
    if self.VData.topResource then
        self.VData.topResource:OnHide()
    else
        self.VData.topResource = ui_gwtop_resource.new()
    end
    self.isExchangeTip = data.isExchangeTip
    self.tog_ExchangeTipSwitch.isOn = self.isExchangeTip
    self.VData.topResource:Init(self.rtf_TopResouce, {[1]=data.topAddItemID},{showAddBtn= { [data.topAddItemID]=true }})
    self:SetTopTime(data.endTime)
end
--设置顶部倒计时
function UIView:SetTopTime(endTime) 
    if self.Timer then 
        self:RemoveTimer(self.Timer)
        self.Timer = nil
    end
    self.Timer=self:CreateTimer(1,function() 
        local curTime = os.server_time()
        if endTime > 0 then 
            local offset = endTime - curTime
            --print("CreateTimer ,offset,endTime,curTime",offset,endTime,curTime)
            if offset > 0 and not util.IsObjNull(self.txt_TimeText) then
                self.txt_TimeText.text = time_util.FormatTime5(offset)
            else
                self:RemoveTimer(self.Timer)
                self.Timer = nil
            end    
        else
            self:RemoveTimer(self.Timer)
            self.Timer = nil
        end
    end)
end

function UIView:OnTogExchangeTipSwitchValueChange(state) 
    self.tog_ExchangeTipSwitch.isOn = state
end


--显示建筑
function UIView:DispalyBuilding(path,effectParams,effectBottomParams,moduleOpen)
    local module_item = require "ui_module_item"
    self.VData.moduleItem = module_item:new():Init(self.rImg_building.gameObject, nil)
    self.VData.moduleItem:SetChangeModel(path)
    if moduleOpen then
        self.VData.moduleItem:SetChangeModelEffect(effectParams)        
        self.VData.moduleItem:SetChangeModelBottomEffect(effectBottomParams)
    end

    self.VData.moduleItem:SetCameraDistance(6)
    self.VData.moduleItem:SetLookAtConstraintWeight(1)
end
function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil

    if self.Timer then 
        self:RemoveTimer(self.Timer)
        self.Timer = nil
    end
    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
