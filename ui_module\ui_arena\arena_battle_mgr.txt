-- arena_battle_mgr.txt ------------------------------------------
-- author:  hym
-- date:    2025/09/12
-- ver:     1.0
-- desc:    竞技场战斗mgr
--------------------------------------------------------------

--暂时是竞技场的，不确定是否通用

local math      = math
local string      = string
local table      = table
local ipairs      = ipairs
local pairs      = pairs
local require      = require
local ui_util = require "ui_util"
local util = require "util"
local game_scheme = require "game_scheme"
local lang = require "lang"
local flow_text = require "flow_text"
local ui_window_mgr = require "ui_window_mgr"
local event = require "event"
local event_arena_common_define = require "event_arena_common_define"


module("arena_battle_mgr")
local logger = require("logger").new("arena_battle_mgr",1)
IsLogLevel = logger.IsLogLevel
Warning = logger.Warning        --log
Warning0 = logger.Warning0      --error


---@see 打开上阵界面（车轮战进攻时）
--[[
战斗结构
attackInfo，进攻阵容
defendInfo，防守阵容（应该就是敌方防守阵容
enemyIndex，敌方阵容索引(对应敌方第几队
battleType，战斗类型
teamNum, 队伍数量
enemyInfo = {
roleID,
name,
level,
faceID,
frameID,
power,
scores
},敌方信息
lineMark, 阵容标识(保存本地的string
battleReq, 战斗请求
ParseAttackLineUpByCache， 解析进攻阵容并保存
]]
function SetUpBattleWheel(battleData)
    SetupSelectHero(battleData)
    local ui_select_hero = require "ui_select_hero"
    local setCurLineup = function()
        local battle_manager = require "battle_manager"
        local selectedHeroes = {}
        local selectedWeapon = {}
        local ishaveHero = false

        selectedHeroes, selectedWeapon = GetLineUpHeroData(battleData.teamNum)
        local lineupList = ParseLineupDataPalByLocal(selectedHeroes, battleData.teamNum)
        for i, v in pairs(lineupList) do
            if v then
                local mark = battleData.lineMark .. i
                battle_manager.SaveHeroFormation(team, mark, true)
            end
        end
        battleData.ParseAttackLineUpByCache(lineupList, true)
        event.Trigger(event_arena_common_define.UPDATE_ARENA_ATTACK_LINEUP)
        return selectedHeroes, selectedWeapon, lineupList, ishaveHero
    end
    ui_select_hero.SetQuickFightBtnTxt(nil, setCurLineup, nil, true)
    if not battleData.defendInfo then
        ui_select_hero.SetShowFormatSetting(true)
    end
    local window = ui_window_mgr:ShowModule("ui_select_hero")
    if window ~= nil then
        window.onCloseEvent = function(window)
            if window ~= nil then
                window.onCloseEvent = nil
                window.onFightEvent = nil
                ui_window_mgr:UnloadModule("ui_select_hero")
            end
        end

        window.onFightEvent = function(window)
            if window ~= nil then
                local selectedHeroes, selectedWeapon, lineupList, ishaveHero = setCurLineup()
                if (not ishaveHero) then
                    flow_text.Add(lang.Get(675098))     --您没有上阵英雄，请先上阵英雄
                    return
                end
                local lineUp = ParseLineupDataByLocal(lineupList, selectedWeapon)
                ui_window_mgr:UnloadModule("ui_select_hero")

                if battleData.attackInfo then
                    local battle_manager = require "battle_manager"
                    battle_manager.SetCanSkipBattle(function()
                        return true
                    end)
                end
                --请求战斗
                battleData.battleReq(lineUp, battleData)
            end
        end
    end

end

--设置布阵界面显示
function SetupSelectHero(battleData)
    local player_mgr = require "player_mgr"
    local ui_select_model_node = require "ui_select_model_node"
    local ui_select_hero = require "ui_select_hero"

    ui_select_model_node.SetBattleType(battleData.battleType)

    ui_select_hero.SetCurEnemyIndex(battleData.enemyIndex)
    --ui_select_hero.SetBattleType(data.attackInfo and ui_select_hero.ATTACK or ui_select_hero.DEFENCE)     --应该没用，3v3才要吧
    ui_select_hero.SetSaveBattleType(battleData.battleType)
    for p, h in pairs(battleData.attackInfo) do
        local hData = {}
        if h.pals then
            for i = 1, #h.pals do
                local hero = h.pals[i]
                local heroEntity = player_mgr.GetPalPartDataBySid(hero.heroSid)
                if (heroEntity) then
                    hData[i - 1] = heroEntity
                else
                    Warning0("current not have this hero :", i, hero.heroSid)
                end
            end
        else
            h.pals = {}
        end
        ui_select_hero.SetSaveHeroData(hData, nil, nil, nil, p)
    end
    ui_select_hero.SetChangeTeam(battleData.teamNum > 1)

    if battleData.defendInfo then
        local entityArr = {}
        local totalPower = {}
        local hideNum = math.ceil(battleData.teamNum / 2)
        local show = nil
        if battleData.teamNum == 1 then
            show = true
        end
        for k, v in pairs(battleData.defendInfo) do
            entityArr[k] = {}
            for _k, _v in pairs(v.pals) do
                _v.show = show or (k < hideNum)
                --3v3改为不隐藏
                _v.show = true
                if _v.show then
                    entityArr[k][_k - 1] = _v
                end
            end
            totalPower[k] = v.power
            ui_select_hero.SetEnemyData(entityArr[k], totalPower[k], true, false, k)

        end
    end

    --己方信息
    local leftRoleInfo = GetMyRoleInfo()

    --己方阵容
    leftRoleInfo.team = {}
    for k, v in pairs(battleData.attackInfo) do
        leftRoleInfo.team[k] = leftRoleInfo.team[k] or {}
        for _k, _v in pairs(v.pals) do
            local heroEntity = player_mgr.GetPalPartDataBySid(_v.heroSid)
            if (heroEntity) then
                table.insert(leftRoleInfo.team, _v)
            else
                Warning0("player not have this hero :", _v.heroSid)
            end
        end
    end

    --敌方信息
    local rightRoleInfo = nil
    if battleData.enemyInfo then
        rightRoleInfo = battleData.enemyInfo
        rightRoleInfo.team = {}
        for k, v in pairs(battleData.defendInfo) do
            rightRoleInfo.team[k] = rightRoleInfo.team[k] or {}
            for _k, _v in pairs(v.pals) do
                table.insert(rightRoleInfo.team[k], _v)
            end
        end
    end
    ui_select_hero.SetTeamCount(battleData.teamNum)
    ui_select_hero.SetAdjustBtnVisible(true, false)
    ui_select_hero.SetRoleInfos(leftRoleInfo, rightRoleInfo, nil, battleData.attackInfo ~= nil)
    --ui_select_hero.SetSaveBattleType(common_pb.AllianceTrain)     --重复了
end


---@see 打开上阵界面（车轮战进攻时）
--[[
战斗结构
attackInfo，进攻阵容
defendInfo，防守阵容（应该就是敌方防守阵容
enemyIndex，敌方阵容索引(对应敌方第几队
battleType，战斗类型
teamNum, 队伍数量
enemyInfo = {
roleID,
name,
level,
faceID,
frameID,
power,
scores
},敌方信息
lineMark, 阵容标识(保存本地的string
battleReq, 战斗请求
ParseAttackLineUpByCache， 解析进攻阵容并保存
onFightCloseCallback, 战斗关闭回调
onModuleShow, 打开编队界面回调
]]
function SetUpBattleSingle(battleData)
    SetupSelectHero(battleData)
    local ui_select_hero = require "ui_select_hero"
    local setCurLineup = function()
        local battle_manager = require "battle_manager"
        local selectedHeroes = {}
        local selectedWeapon = {}
        local ishaveHero = false

        selectedHeroes, selectedWeapon = GetLineUpHeroData(battleData.teamNum)
        local lineupList = ParseLineupDataPalByLocal(selectedHeroes, battleData.teamNum)
        for i, v in pairs(lineupList) do
            if v then
                local mark = battleData.lineMark .. i
                battle_manager.SaveHeroFormation(team, mark, true)
            end
        end
        battleData.ParseAttackLineUpByCache(lineupList, true)
        event.Trigger(event_arena_common_define.UPDATE_ARENA_ATTACK_LINEUP)
        return selectedHeroes, selectedWeapon, lineupList, ishaveHero
    end
    ui_select_hero.SetQuickFightBtnTxt(nil, setCurLineup, nil, true)
    if not battleData.defendInfo then
        ui_select_hero.SetShowFormatSetting(true)
    end
    local window = ui_window_mgr:ShowModule("ui_select_hero",battleData.onModuleShow)
    if window ~= nil then
        window:SetCloseEvent(function(window)
            if window ~= nil then
                window.onCloseEvent = nil
                window.onFightEvent = nil
                ui_window_mgr:UnloadModule("ui_select_hero")
                if battleData.onFightCloseCallback then
                    battleData.onFightCloseCallback(battleData)
                end
            end
        end)

        window:SetFightEvent(function(window,isGrind)
            local selectedHeroes, selectedWeapon, lineupList, ishaveHero = setCurLineup()
            if (not ishaveHero) then
                flow_text.Add(lang.Get(675098))     --您没有上阵英雄，请先上阵英雄
                return
            end
            local lineUp = ParseLineupDataByLocal(lineupList, selectedWeapon)
            ui_window_mgr:UnloadModule("ui_select_hero")
            
            --请求战斗
            battleData.battleReq(lineUp, battleData)
        end)

    end
    local battle_manager = require "battle_manager"
    if window ~= nil then
        battle_manager.SetPrepareBattle(function()
            if battleData.SetPrepareBattle then
                battleData.SetPrepareBattle()
            end
        end)
        battle_manager.SetCanSkipBattle(function()
            return battleData.skipBattle
        end)
        battle_manager.SetFinalizeBattle(battleData.battleType, function(victory, showuFi)
            if battleData.onFightCloseCallback then
                battleData.onFightCloseCallback(battleData)
            end
        end)
    end
end

--获取布阵界面英雄数据
function GetLineUpHeroData(teamNum)
    local ui_select_hero = require "ui_select_hero"
    local selectedHeroes = {}
    local selectedWeapon = {}
    for i = 1, teamNum do
        local TabData = ui_select_hero.GetTabDataByLine(i)
        if TabData and TabData.selectedHero then
            local Heroes = TabData.selectedHero
            if not selectedHeroes[i] then
                selectedHeroes[i] = {}
            end
            for j, k in pairs(Heroes) do
                if (not ishaveHero) then
                    ishaveHero = true
                end
                selectedHeroes[i][j] = k
            end
            if TabData.usedWeapon and TabData.usedWeapon.weaponId ~= 0 then
                selectedWeapon[i] = TabData.usedWeapon.weaponId
            end
        end
    end
    return selectedHeroes, selectedWeapon
end

---@see 打开上阵界面（设置防守阵容时）
-----[[
--战斗结构
--battleType，战斗类型
--teamNum, 队伍数量
--enemyIndex，敌方阵容索引(对应敌方第几队
--defendInfo，防守阵容
--setDefendReq, 设置防守阵容请求
--]]
function SetUpDefenseBattle(defendData)
    local selectedHero = {}
    local weaponData = {}
    local teamNum = defendData.teamNum
    local weapon_data = require "weapon_data"

    if defendData.defendInfo then
        for k, v in ipairs(defendData.defendInfo or {}) do
            selectedHero[k] = {}
            for _k, _v in ipairs(v.pals) do
                selectedHero[k][_k - 1] = _v
            end
            weaponData[k] = v.weaponID
        end
    end

    local ui_select_model_node = require "ui_select_model_node"
    ui_select_model_node.SetBattleType(defendData.battleType)
    local ui_select_hero = require "ui_select_hero"
    ui_select_hero.SetCurEnemyIndex(defendData.enemyIndex)
    --ui_select_hero.SetBattleType(ui_select_hero.DEFENCE)
    ui_select_hero.SetSaveBattleType(defendData.battleType)
    for p, h in ipairs(selectedHero) do
        ui_select_hero.SetSaveHeroData(h, nil, nil, nil, p)
        local wData = {
            stage = defendData.battleType,
            weaponId = weaponData[p] or 0,
            lineupIdx = p - 1,
            arenaType1 = defendData.battleType,
        }
        weapon_data.SetlocalizedWeaponData(defendData.battleType, weaponData[p] or 0, p - 1, defendData.battleType)
        local dontUpdateServer = true
        ui_select_hero.SetSaveWeaponData(wData, nil, dontUpdateServer, false, p)

    end
    ui_select_hero.SetResetBtnVisible(true)
    ui_select_hero.SetChangeTeam(defendData.teamNum > 1)
    ui_select_hero.SetFightBtnTxt(lang.Get(defendData.fightBtnLang))

    local setLineup = function(teamIndex)
        local lineupList = {}
        for i = 1, teamNum do
            local team = SetDefendLineupByPB(teamIndex)
            if team then
                table.insert(lineupList, team)
            end
        end
        defendData.setDefendReq(lineupList, defendData)
    end
    ui_select_hero.SetQuickFightBtnTxt(lang.Get(675092), setLineup, true)
    local leftRoleInfo = GetMyRoleInfo()
    leftRoleInfo.team = {}
    for k, v in pairs(selectedHero) do
        leftRoleInfo.team[k] = leftRoleInfo.team[k] or {}
        for _k, _v in pairs(v) do
            table.insert(leftRoleInfo.team, _v)
        end
    end

    local rightRoleInfo = nil
    ui_select_hero.SetTeamCount(teamNum)

    --屏蔽星级限制
    --ui_select_hero.SetAdjustBtnVisible(true,false)
    ui_select_hero.SetRoleInfos(leftRoleInfo, rightRoleInfo, nil, false)
    --ui_select_hero.SetSaveBattleType(common_new_pb.AllianceTrain)
    if not defendData.defendInfo then
        ui_select_hero.SetShowFormatSetting(true)
    end
    local window = ui_window_mgr:ShowModule("ui_select_hero")

    if window ~= nil then
        window.onCloseEvent = function(window)
            if window ~= nil then
                ---点击返回按钮时保存防守阵容
                for i = 1, teamNum do
                    setLineup(i)
                end
                window.onCloseEvent = nil
                window.onFightEvent = nil
                ui_window_mgr:UnloadModule("ui_select_hero")
            end
        end

        window.onFightEvent = function(window)
            if window ~= nil then
                local selectedHeroes = {}
                local selectedWeapon = {}
                ---------------------获取武器/阵容----------------------------
                local battle_data = require("battle_data")
                selectedHeroes, selectedWeapon = GetLineUpHeroData(teamNum)
                local lineupList = ParseLineupDataPalByLocal(selectedHeroes, teamNum)
                local lineupListPower = {}

                local lineup = {}
                local gw_home_soldier_data = require "gw_home_soldier_data"
                local gw_hero_data = require "gw_hero_data"
                local gw_power_mgr = require "gw_power_mgr"
                local gw_hero_mgr = require "gw_hero_mgr"
                local gw_home_drone_data = require "gw_home_drone_data"

                local maxSoldierLevel = gw_home_soldier_data.GetCampusSoldierLevel()
                local maxLevelSoldierCfg = game_scheme:Soldier_1(maxSoldierLevel)

                for k, v in ipairs(lineupList) do
                    local data = {}
                    data.pals = v
                    data.weaponId = selectedWeapon[k]
                    data.power = gw_hero_mgr.GetHeroesPower(v)--英雄战力
                    if v and #v > 0 then
                        ---每个队伍增加召唤兽的战力
                        data.power = data.power + gw_home_drone_data.GetDroneLocalPower()
                        ---增加每个队伍英雄的带兵数量战力
                        for _, _v in pairs(v) do
                            local entity = gw_hero_data.GetHeroEntityByCfgId(v.heroID)
                            local carrySoldierNum = entity and entity:GetHeroSoldierNum() or 0
                            if maxLevelSoldierCfg then
                                data.power = data.power + gw_power_mgr.GetSoliderForceInQueue(carrySoldierNum, maxLevelSoldierCfg.soldierID)
                            end
                        end
                    end

                    table.insert(lineup, data)
                end
                local curEnemyIndex = ui_select_hero.GetCurEnemyIndex()
                setLineup(curEnemyIndex)
                if teamNum > 1  then
                    defendData.FightBtnFunc(defendData, lineup)
                end
                --ui_window_mgr:ShowModule("ui_alliance_train_switch", nil, nil, switchData)
            end
        end
    end

end

--获取玩家自己数据
function GetMyRoleInfo()
    local player_mgr = require "player_mgr"
    local leftRoleInfo = {
        roleID = player_mgr.GetPlayerRoleID(),
        name = player_mgr.GetRoleName(),
        level = player_mgr.GetPlayerLV(),
        faceID = GetMyFaceStr(player_mgr.GetRoleFaceID()),
        frameID = player_mgr.GetPlayerFrameID(),
        power = player_mgr.GetPlayerPower(),
        scores = 0,
        worldStr = ui_util.GetWorldIDToShowWorldID(player_mgr.GetCreateRoleWorldID(),nil,ui_util.WorldIDRangeType.Normal)
    }
    return leftRoleInfo
end

--获取玩家自己的头像
function GetMyFaceStr(faceStr)
    local custom_avatar_data = require "custom_avatar_data"
    local customHeadData = custom_avatar_data.GetMyAvatar()
    if customHeadData then
        faceStr = customHeadData.remoteUrl
    end
    return faceStr
end




--- @see 阵容的数据中间层转换(msg ) BattleLineUp
---@param defendLineupList table<TRAINS_LINEUP_TEAM> 防御队伍
function ParseLineupDataByPB(defendLineupList)
    ---@type table<TRAINS_LINEUP_TEAM>
    local lineup = {}
    for index, value in ipairs(defendLineupList) do
        ---@type TRAINS_LINEUP_TEAM
        local team = ParseLineupTeamDataByPB(value, index)
        table.insert(lineup, team)
    end
    return lineup
end

--- @see 阵容的数据中间层转换(msg ) TSimpleTeam
---@param defendLineup TRAINS_LINEUP_TEAM 防御队伍
function ParseLineupTeamDataByPB(defendLineup, i)
    ---@type TRAINS_LINEUP_TEAM
    local team = {}
    team.order = defendLineup.order or 0
    team.weaponModelId = defendLineup.weaponModelId or 0
    team.power = defendLineup.power or 0
    team.ce = defendLineup.ce or 0
    team.dbid = defendLineup.dbid or 0
    team.weaponID = defendLineup.weaponID or 0
    team.index = defendLineup.index or i
    team.pals = {}
    team.palList = {}
    if defendLineup.pals then
        for k, v in ipairs(defendLineup.pals) do
            ---@type TRAINS_LINEUP_HERO
            local hero = {}
            hero.heroSid = v.heroSid
            hero.col = v.col
            hero.heroPower = v.heroPower
            hero.row = v.row
            hero.remainHP = v.remainHP
            hero.skinID = v.skinID
            hero.heroStar = v.heroStar
            hero.heroLevel = v.heroLevel
            hero.soldierCapacity = v.soldierCapacity
            hero.heroID = v.heroID
            table.insert(team.pals, hero)
            table.insert(team.palList, hero)
        end
    end
    return team
end

---@see 布阵界面数据转换服务器队伍(BattleLineUp)
---@param lineupList table<TRAINS_LINEUP_TEAM> 布阵界面队伍(已转换）
---@param selectedWeapon table<TRAINS_LINEUP_TEAM> 布阵界面召唤兽选择
function ParseLineupDataByLocal(lineupList, selectedWeapon)
    local lineUp = {}
    for k, v in ipairs(lineupList) do
        local team = {}
        team.palList = v
        team.weaponId = selectedWeapon[k]
        table.insert(lineUp, team)
    end
    return lineUp
end

---@see 布阵界面数据转换服务器队伍(BattleLineUp)
function SetDefendLineupByPB(teamIndex)
    local ui_select_hero = require "ui_select_hero"
    local team = ui_select_hero.GetTabDataByLine(teamIndex)

    local selectedHeroes = {}
    local selectedWeapon = {}

    if team and team.selectedHero then
        local Heroes = team.selectedHero
        if not selectedHeroes[teamIndex] then
            selectedHeroes[teamIndex] = {}
        end
        for j, k in pairs(Heroes) do
            selectedHeroes[teamIndex][j] = k
        end
        if team.usedWeapon and team.usedWeapon.weaponId ~= 0 then
            selectedWeapon[teamIndex] = team.usedWeapon.weaponId
        end
    end
    local defendLineupList = {}
    defendLineupList.weaponId = selectedWeapon[teamIndex] or 0
    defendLineupList.palList = {}
    local battle_data = require "battle_data"
    for _k, heroEntity in pairs(selectedHeroes[teamIndex]) do
        local row, col = battle_data.GetRowAndColByIndex(_k + 1)
        table.insert(defendLineupList.palList,
                {
                    palId = heroEntity.heroSid,
                    row = row,
                    col = col
                })
    end
    return defendLineupList
end


---@see 布阵界面数据转换服务器队伍部分palList(BattleLineUp)
---@param lineup table<TRAINS_LINEUP_TEAM> 布阵界面队伍
function ParseLineupDataPalByLocal(lineup, teamNum)
    local lineupList = {}
    for index, team in pairs(lineup) do
        if team and index <= teamNum then
            local teamInfo = {}
            for _k, heroEntity in pairs(team) do
                if heroEntity then
                    local battle_data = require "battle_data"
                    local _row, _col = battle_data.GetRowAndColByIndex(_k + 1)
                    local line = {
                        heroSid = heroEntity.heroSid,
                        heroID = heroEntity.heroID,
                        palId = heroEntity.heroSid,
                        power = heroEntity.heroPower or 0,
                        skinID = heroEntity.skinID,
                        heroStar = heroEntity.heroStar,
                        heroLevel = heroEntity.heroLevel,
                        soldierCapacity = heroEntity.soldierCapacity,
                        remainHP = heroEntity.remainHP,
                        row = _row,
                        col = _col,
                        numProp = {
                            starLv = heroEntity.numProp.starLv,
                            lv = heroEntity.numProp.lv,
                        },
                    }
                    table.insert(teamInfo, line)
                end
            end
            table.insert(lineupList, teamInfo)
        end
    end
    return lineupList
end

---@see 设置队伍的Item信息
---@param item ScrollRectItem 预设
---@param key number 队伍下标
---@param data table 队伍数据(TSimpleTeam)
---@param hList table 存在的队伍数据
---@param isShowGold boolean 是否展示金色底(单队击败使用)
---@param scale number heroitem的大小
---@param isGray number 是否灰度
---@param isShowAdd number  是否显示+号

---@return table<hero_item_new> heroList 英雄数据
function SetTeamHeroItemInfo(item, key, data, hList, isShowGold, scale, isGray, isShowAdd)
    if not key or not item or not data then
        Warning0(" set lineuo info fail data exit nil ", key, item, data)
        return
    end
    if (not isShowGold) then
        isShowGold = false
    end
    if (not scale) then
        scale = 0.62
    end
    if (not isGray) then
        isGray = false
    end
    if (not isShowAdd) then
        isShowAdd = false
    end

    local hData = data.pals or {}
    local teamIndex = item:Get("teamindex")
    local power = item:Get("power")
    local hero = item:Get("hero")
    local goldIndex = item:Get("goldindex")
    local imgGray_item = item:Get("imggray_item")
    local imgGray_power = item:Get("imggray_power")
    local imgGray_tmpBg_l = item:Get("imggray_tmbg_l")
    local img_1 = item:Get("img_1")
    local img_2 = item:Get("img_2")
    local img_3 = item:Get("img_3")
    local img_4 = item:Get("img_4")
    local img_5 = item:Get("img_5")

    imgGray_item:SetEnable(isGray)
    imgGray_power:SetEnable(isGray)
    imgGray_tmpBg_l:SetEnable(isGray)
    img_1:SetEnable(isGray)
    img_2:SetEnable(isGray)
    img_3:SetEnable(isGray)
    img_4:SetEnable(isGray)
    img_5:SetEnable(isGray)

    imgGray_tmpBg_l.gameObject:SetActive(isShowGold)
    local battle_data = require "battle_data"
    local heroList = hList or {}
    local hasHeroList = {}
    --local power = 0
    local hero_item_new = require "hero_item_new"
    --可以点击设置防守阵容和下标没有英雄的时候需显示+号
    local showIndex = {}
    for k, v in ipairs(hData) do
        local index = battle_data.GetIndexByRowAndCol(v.row, v.col)
        hasHeroList[index] = true
        heroList[index] = heroList[index] or hero_item_new.CHeroItem(nil, "ui_intercity_alliancetrain")
        heroList[index]:Init(hero:GetChild(index), function()
            heroList[index]:DisplayInfo()
            heroList[index]:GrayHeroIcon(isGray)
        end, scale)
        showIndex[index] = false
        local heroData = {
            heroSid = v.heroSid,
            heroID = v.heroID,
            numProp = {
                starLv = v.heroStar,
                lv = v.heroLevel,
            }
        }
        heroList[index]:SetHero(heroData, nil)
    end
    power.text = util.PriceConvert(data.power or 0)
    teamIndex.text = key
    for i = 0, 4 do
        if not hasHeroList[i] and heroList[i] then
            heroList[i]:Dispose()
            heroList[i] = nil
        end
        if (showIndex[i] == nil) then
            showIndex[i] = true
        end
        hero:GetChild(i):GetChild(0).gameObject:SetActive(isShowAdd and showIndex[i])
    end
    return heroList
end

--计算mvp玩家
function CalculateMvp(heroData, isVictory)
    local mvpGrade = game_scheme:InitBattleProp_0(854).szParam.data
    local mvpIndex = 0
    local curGrade = 0
    --前六位为进攻方英雄，后六位为防守方英雄
    --mvp只能在胜利方
    local min = isVictory and 0, 6
    local max = isVictory and 6, 12
    for i = min, max do
        local data = heroData[i]
        local grade = mvpGrade[0] * data.statistics.damage + mvpGrade[1] * data.statistics.hurt + mvpGrade[2] * data.statistics.heal
        if curGrade < grade then
            curGrade = grade
            mvpIndex = data.pos
        end
    end
    return mvpIndex
end

