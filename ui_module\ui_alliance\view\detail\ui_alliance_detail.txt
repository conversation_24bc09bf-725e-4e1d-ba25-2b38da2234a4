--@region FileHead
-- ui_alliance_detail.txt ---------------------------------
-- author:  马睿
-- date:    2024/6/25 9:15:46
-- ver:     1.0
-- desc:    联盟详情业务层
-------------------------------------------------
--@endregion 

--@region Require
local require = require
local type = type
local typeof = typeof
local string = string

local util = require "util"
local alliance_data = require "alliance_data"
local enum_define = require "enum_define"
local class = require "class"
local ui_base = require "ui_base"
local lang = require "lang"
local alliance_pb = require "alliance_pb"
--local ui_util = require "ui_util"
local alliance_ui_util = require "alliance_ui_util"
local ImageGray = CS.War.UI.ImageGray
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local card_sprite_asset = require "card_sprite_asset"
--@endregion 

--@region ModuleDeclare
module("ui_alliance_detail")
local ui_path = "ui/prefabs/gw/alliancesystem/uialliancedetail.prefab"
local window = nil
local UIAllianceDetail = {}
--@endregion 

--@region WidgetTable
UIAllianceDetail.widget_table = {

    --查看成员按钮
    Btn_Member = { path = "bottomUI/btnMember", type = "Button", event_name = "OnBtn_MemberClicked" },
    --练习盟主按钮
    Btn_CallLeader = { path = "bottomUI/btnCallLeader", type = "Button", event_name = "OnBtn_CallLeaderClicked" },
    --点击空白处关闭
    Btn_Close = { path = "Auto_closeBtn", type = "Button", event_name = "OnBtn_closeClickedProxy" },
    --加入按钮
    Btn_Join = { path = "bottomUI/Auto_JoinBtn", type = "Button", event_name = "OnBtn_JoinBtnClicked" },
    --申请按钮
    Btn_Apply = { path = "bottomUI/Auto_applyBtn", type = "Button", event_name = "OnBtn_ApplyBtnClicked" },
    --ImgGray_Apply = { path = "bottomUI/Auto_applyBtn", type = ImageGray },
    --翻译按钮
    Btn_translate = { path = "Auto_translateBtn", type = "Button", event_name = "OnBtn_translateClickedProxy" },
    

    Sprite_Apply = { path = "bottomUI/Auto_applyBtn", type = SpriteSwitcher },

    Text_apply = { path = "bottomUI/Auto_applyBtn/Text", type = "Text" },

    --join_Text = { path = "bottomUI/Auto_JoinBtn/Text", type = "Text" },
    --徽章按钮
    -- TODO

    --联盟名字，简称
    nameText = { path = "TopUI/nameText", type = "Text" },
    --盟主名字
    bossNameText = { path = "TopUI/bossNameText", type = "Text" },
    --联盟总战力
    powerText = { path = "TopUI/powerText", type = "Text" },

    giftLevel = { path = "TopUI/giftLevelText", type = "Text" },

    --联盟旗帜
    flagImage = { path = "TopUI/flagIcon", type = "Image" },
    natinoalFlagImage = { path = "bottomUI/allianceLangText/Img_nationalFlag", type = "Image" },

    --联盟等级限制
    levelLimitText = { path = "bottomUI/levelLimitText", type = "Text" },
    --联盟战力限制
    powerLimitText = { path = "bottomUI/powerLimitText", type = "Text" },
    --联盟人员
    allianceNumberText = { path = "bottomUI/allianceNumberText", type = "Text" },
    --联盟语言
    allianceLangText = { path = "bottomUI/allianceLangText", type = "Text" },
    --联盟加入方式
    joinModeText = { path = "bottomUI/joinModeText", type = "Text" },
    --公告Text
    noticeText = { path = "noticeView/Viewport/noticeText", type = "Text" },

   
    --@region User

    --@endregion 
}
--@endregion 


--@function 设置View-Controller模式的UI
--@ return type  ---- 未定义/VC/纯V   
--@ 注意，View-Controller模式的ui必须要重写这个接口
function UIAllianceDetail:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

--@endregion 

--@endregion 

--@region WindowInit
--[[窗口初始化]]

--@region WindowInit
--[[窗口初始化]]
function UIAllianceDetail:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    --@region User
    self.sprite_asset = self.sprite_asset or card_sprite_asset.CreateLeagueAsset()
    --self.UIRoot:GetComponent(typeof(Canvas)).sortingLayerName = "Popups"
    --@endregion 
end --///<<< function

--@endregion 


--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIAllianceDetail:OnShow()


end --///<<< function

--@endregion 

--@region WindowOnHide
--[[界面隐藏时调用]]
function UIAllianceDetail:OnHide()
    --@region User
    --@endregion 
end --///<<< function

--@endregion 


--@region WindowClose
function UIAllianceDetail:Close()
    self.__base:Close()
    window = nil

    --@region User
    if self.sprite_asset then
        self.sprite_asset:Dispose()
        self.sprite_asset = nil
    end
    --@endregion 
end --///<<< function

--@endregion 

---********************功能函数区**********---

--显示联盟信息
function UIAllianceDetail:UpdateDetailInfo(dataItem)

    --联盟 1423
    --self.nameText.text = string.format("%s:【%s】%s", lang.Get(1423), dataItem.shortName, dataItem.allianceName)

    --设置联盟旗帜
    local flagData = alliance_data.GetFlagIdData(dataItem.flag)
    self:SetAllianceFlagIcon(flagData.iconID)
    
    self.nameText.text = string.format("[%s]%s", dataItem.shortName, dataItem.allianceName)
    --盟主 600111
    if dataItem.r5Info and dataItem.r5Info.strName then
        self.bossNameText.text = string.format("%s: %s", lang.Get(600111), dataItem.r5Info.strName)
    end

    local powerText = alliance_ui_util.KiloSeparator(dataItem.power)

    self.powerText.text = string.format("%s: %s", lang.Get(600178), powerText)

    self.giftLevel.text = string.format("%s: Lv.%s", lang.Get(600115), dataItem.giftLv)

    self.noticeText.text = dataItem.announcement

    self.levelLimitText.text = dataItem.lvLimit

    local ceLimit = alliance_ui_util.KiloSeparator(dataItem.ceLimit)
    self.powerLimitText.text = ceLimit
    self.allianceNumberText.text = dataItem.count .. "/" .. "100"

    self.allianceLangText.text = lang.Get(dataItem.language)

    local joinLang = dataItem.applySet == alliance_pb.emAllianceApplyType_Apply and 600156 or 600155
    self.joinModeText.text = lang.Get(joinLang)
    --self.joinModeText.color = { r = 242, g = 172 / 255, b = 57 / 255, a = 255 }

    --if dataItem.apply == alliance_pb.emAllianceApplyType_Apply then
    --    self:RefreshBtnState(false)
    --elseif dataItem.applySet == alliance_pb.emAllianceApplyType_Auto  then
    --    self:RefreshBtnState(true)
    --end

    if dataItem.isJoin then
        self:RefreshBtnState(-1)
    else
        if dataItem.apply then
            self:RefreshBtnState(2)
        elseif dataItem.applySet == alliance_pb.emAllianceApplyType_Auto then
            self:RefreshBtnState(3)
        else
            self:RefreshBtnState(1)
        end
    end

    -- 设置国旗
    local national_flag = require "national_flag_mgr"
    if dataItem.nationalFlagID == 0 then
        dataItem.nationalFlagID = 217
    end
    national_flag.SetNationalFlagInfo(self, self.natinoalFlagImage, dataItem.nationalFlagID)
end

---@public RefreshBtnState 切换申请/加入 按钮状态
---@param index number 1申请 2已申请 3加入
function UIAllianceDetail:RefreshBtnState(index)

    if index == 1 then
        self.Text_apply.text = lang.Get(600368) --申请
        self:SetActive(self.Btn_Join, false)
        self:SetActive(self.Btn_Apply, true)
        --self.ImgGray_Apply:SetEnable(false)
        self.Sprite_Apply:Switch(0)
    elseif index == 2 then
        self.Text_apply.text = lang.Get(600376) --已申请
        self:SetActive(self.Btn_Join, false)
        self:SetActive(self.Btn_Apply, true)
        --self.ImgGray_Apply:SetEnable(true)
        self.Sprite_Apply:Switch(1)
    elseif index == 3 then
        self:SetActive(self.Btn_Join, true)
        self:SetActive(self.Btn_Apply, false)
    elseif index == -1 then
        self:SetActive(self.Btn_Join, false)
        self:SetActive(self.Btn_Apply, false)
        self:SetActive(self.Text_apply, false)
    end

end


--显示成员面版UI
function UIAllianceDetail:ShowMeberWindow(bool)
    --跳转成员界面
    --self.ImgGray_Apply:SetEnable(bool)
    self.Sprite_Apply:Switch(bool and 0 or 1)
end
--显示与盟主私聊面板
function UIAllianceDetail:ShowChatWindow()
    --跳转盟主私聊界面
end

--设置旗帜ID
function UIAllianceDetail:SetAllianceFlagIcon(iconID)
    --根据旗帜ID设置旗帜图标 dataItem.flag
    if self.sprite_asset then
        self.sprite_asset:GetSprite("qizhi" .. iconID, function(sprite)
            if sprite then
                self.flagImage.sprite = sprite
            end
        end)
    end
end

function UIAllianceDetail:RefereshNotify(str)
    if not util.IsObjNull(self.noticeText) then
        self.noticeText.text = str
    end
end

---********************end功能函数区**********---


--@region WindowInherited
local CUIAllianceDetail = class(ui_base, nil, UIAllianceDetail)
--@endregion 

--@region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程

function Show(data)
    if data and data["uipath"] then
        ui_path = data["uipath"];
    end
    if window == nil then
        window = CUIAllianceDetail()
        window._NAME = _NAME
        window:LoadUIResource(ui_path, nil, nil, nil)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    --UIAllianceDetail:UpdateDetailInfo(data)

    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--@endregion







