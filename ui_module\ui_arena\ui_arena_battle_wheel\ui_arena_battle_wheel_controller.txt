local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local event_arena_common_define = require "event_arena_common_define"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_arena_battle_wheel_controller")
local controller = nil
local UIController = newClass("ui_arena_battle_wheel_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    self.battleData = data
    self:OnRefreshHeroData()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
    local RefreshBattleProcess = function(_, msg)
        self:TriggerUIEvent("RefreshBattleProcess", msg)
    end
    self:RegisterEvent(event_arena_common_define.TMSG_NEW_ARENA_ENTER_BATTLE_RSP, RefreshBattleProcess)
    
    local UpdateAttackTeam = function()
        self:OnRefreshHeroData()
    end
    self:RegisterEvent(event_arena_common_define.UPDATE_ARENA_ATTACK_LINEUP, UpdateAttackTeam)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnLeftBtnClickedProxy()
    self:CloseView()
end

function  UIController:OnBtnSwitch1ClickedProxy()
    if self.battleData.SwitchAttackLineUpByIndex then
        self.battleData.SwitchAttackLineUpByIndex(1,2)
    end
    self:OnRefreshHeroData()
end
function  UIController:OnBtnSwitch2ClickedProxy()
    if self.battleData.SwitchAttackLineUpByIndex then
        self.battleData.SwitchAttackLineUpByIndex(2,3)
    end
    self:OnRefreshHeroData()
end
function  UIController:OnBtnStartFightClickedProxy()
    if self.battleData.battleStartFunc then
        local battle_manager = require "battle_manager"
        local attackHeroData = self.battleData.GetAttackLineUpTeamList()
        for i = 1, #attackHeroData do
            local mark = self.battleData.lineMark .. i
            battle_manager.SaveHeroFormation(attackHeroData[i].pals, mark, true)
        end
        self.battleData.battleStartFunc()
    end
end

function UIController:OnRefreshHeroData()
    local heroData = self.battleData.GetAttackLineUpTeamList()
    local enemyInfo = self.battleData.enemyInfo
    local OnClickTeamItem = self.battleData.OnClickTeamItem
    self:TriggerUIEvent("OnRefreshHeroData",heroData, enemyInfo, OnClickTeamItem)
end



--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
