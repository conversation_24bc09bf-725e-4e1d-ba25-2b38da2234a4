local require = require
local print = print
local _G = _G
local ipairs = ipairs
local pairs = pairs
local xpcall = xpcall
local table = table
local tostring = tostring
local json = require "dkjson"
local event = require "event"
local IOSystem = CS.War.Script.IOSystem
local Utility = CS.War.Script.Utility
local LogHelp = CS.LogHelp
local Time = CS.UnityEngine.Time
local hashRemote = CS.War.Base.AssetBundleManager.hashRemote
local m_AssetBundleManifest = CS.War.Base.AssetBundleManager.m_AssetBundleManifest
local debug = debug
module("iosystem_load")

OLD_LOAD_TYPE = false

local cache = {
    _callback = {ind = 1},
    __callbackInfo = {
        cache_ab = nil,
        prefab = nil,
        obj = nil
    }
}
local callbackInfo = cache.__callbackInfo
local isInitLog = false
local log = {}

function InitLog()
    if not isInitLog then
        ---logger.new放外面会出现Loop require，所以放在InitLog方法里
        local logger = require("logger").new("sw_iosystem_load_log", 0, 1)
        log.Error = logger.Warning0       --error
        log.Warning  = logger.Warning     --info
        log.Warning1 = logger.Warning1    --warning
        isInitLog  = true
    end
end

function LoadAssetAsync(abName, assetName, callback, tag)
    InitLog()
    if OLD_LOAD_TYPE then
        IOSystem.LoadAssetAsync(abName, assetName, callback)
        if true then
            return
        end
    end
    if not abName then
        log.Error("IOSystem.LoadAssetAsync中abName字段不能为空")
        return
    end
    if not tag then
        tag = ""
        log.Error("请补充IOSystem.LoadAssetAsync中tag字段")
    end
    if not cache[abName] then
        cache[abName] = {
            __cbs = {ind = 1},
            totalcount = 0
        }
    end
    if not cache[abName][tag] then
        cache[abName][tag] = 0
    end
    local cache_ab = cache[abName]

    cache_ab[tag] = cache_ab[tag] + 1
    if cache_ab[tag] == 1 then
        cache_ab.totalcount = cache_ab.totalcount + 1
    end

    --做一层封装，加载成功过一次资源不再经过底层引用计数，减少引用计数计算，如果前面已经调用卸载，这里先处理检查回调，再向底层请求加载
    if not IsObjNull(cache_ab.proto) then
        log.Warning(4, "iosystem_load-skip",abName,tag,cache_ab[tag], cache_ab.totalcount)
        IndAddObj(cache._callback, {tag, callback, abName, Time.frameCount})
    else
        log.Warning(4, "iosystem_load-LoadAssetAsync",abName,tag,cache_ab[tag], cache_ab.totalcount)
        IndAddObj(cache_ab.__cbs, {tag, callback, abName, Time.frameCount})
    end

    if cache_ab.totalcount == 1 then
        cache[abName].state = 0
        IOSystem.LoadAssetAsync(
            abName,
            assetName,
            function(UIPrefab)
                log.Warning(4, "iosystem_load-cb", abName, tag, cache_ab[tag])
                cache[abName].proto = UIPrefab
                cache[abName].state = 1
                on_callback(abName)
            end
        )
        

    end
end
function SetEditor(b)
    --isEditor = b
end
function IsObjNull(obj)
    local b = Utility.IsObjNull(obj)
    -- print ("IsObjNull",b)
    return b
end
function exe_callback()
    local v = callbackInfo.obj
    local callback = v[2]
    local UIPrefab = callbackInfo.prefab
    -- local tag = v[1]
    -- local count = cache_ab[tag]
    -- local abName = v[3]
    callback(UIPrefab)
end
function exe_callback_err(err)
    local v = callbackInfo.obj
    local callback = v[2]
    local UIPrefab = callbackInfo.prefab
    local tag = v[1]
    local abName = v[3]
    local count = cache[abName][tag]
    log.Error("on_one_callback_error", abName, tag, count, UIPrefab, err)

    local files_version_mgr = require "files_version_mgr"
    local resVer = files_version_mgr.GetCurResourceVersion()

    event.Trigger(
        event.GAME_EVENT_REPORT,
        "lua_err",
        {
            abName = abName,
            type = "asset_callback_err",
            tag = tag,
            count = count,
            err = err,
            resVer = resVer or "unknown",
            isnull = IsObjNull(UIPrefab),
            trackback = debug.traceback(),
        }
    )
end
function on_one_callback(v, cache_ab, UIPrefab, allowcheck)
    InitLog()
    
    local tag = v[1]
    local callback = v[2]

    if not callback then
        print("on_one_callback skip", tag, v[3])
        return
    end

    local count = cache_ab[tag]
    if count == nil or count <= 0 then
        log.Warning(3, "on_one_callback null count", abName, tag, cache_ab[tag])
        return
    end
    local abName = v[3]
    if IsObjNull(UIPrefab) then
        IndAddObj(cache_ab.__cbs, v)
        log.Error("on_one_callback null uiprefab", abName, tag, cache_ab[tag])
        return
    end

    log.Warning(4, "on_one_callback user framecount", abName, tag, Time.frameCount - v[4])

    if callback then
        callbackInfo.cache_ab = cache_ab
        callbackInfo.prefab = UIPrefab
        callbackInfo.obj = v
        cache_ab.lastCbTime = Time.realtimeSinceStartup
        --local check,result = xpcall(exe_callback, exe_callback_err)
        if allowcheck then
            -- local check,result = xpcall(function (  )
            -- 	callback(UIPrefab)
            -- end, function( err)
            -- 	log.Error("on_one_callback callback error",abName,tag,cache_ab[tag],UIPrefab,err)
            -- end)
            log.Warning(4, "on_one_callback xpcall_exe_callback", abName, tag, cache_ab[tag], UIPrefab)
            local check, result = xpcall(exe_callback, exe_callback_err)
        else
            log.Warning(4, "on_one_callback exe_callback", abName, tag, cache_ab[tag], UIPrefab)
            exe_callback()
        end
        --执行完删除回调，配合回调列表处理中执行后才清理列表的逻辑，允许报错后重新检查回调，无回调或已执行回调自动跳过
        v[2] = nil
    -- callback(UIPrefab)
    end
end
function on_callback(abName)
    local cache_ab = cache[abName]
    local cbs = cache_ab.__cbs
    local UIPrefab = cache_ab.proto

    cache_ab.__cbs = {ind = 1}

    --todo fix call the least when error
    for i, v in ipairs(cbs) do
        on_one_callback(v, cache_ab, UIPrefab, true)
        -- local tag = v[1]
        -- local callback = v[2]
        -- local count = cache_ab[tag]
        -- if count == nil or count <= 0 then
        -- 	return
        -- end

        -- if callback then
        -- 	callback(UIPrefab)
        -- end
    end
end

function IndAddObj(t, obj)
    t[t.ind] = obj
    t.ind = t.ind + 1
end

function UnloadAssetBundle(abName, tag, unloadAll)
    if OLD_LOAD_TYPE then
        IOSystem.UnloadAssetBundle(abName, unloadAll or false)
        if true then
            return
        end
    end
    if not abName then
        log.Error("IOSystem.UnloadAssetBundles中abName字段不能为空")
        return
    end
    if not tag then
        log.Error("请补充IOSystem.UnloadAssetBundle中tag字段")
        return
    end
    local cache_ab = cache[abName]

    if not cache_ab or not cache_ab[tag] then
        log.Error("没有可卸载的资源, abName=" .. tostring(abName) .. ", paramStr=" .. tag)
        return
    end
    if cache_ab[tag] <= 0 then
        cache_ab[tag] = 0
        log.Error("卸载资源引用计数错误, abName=" .. abName .. ", paramStr=" .. tag)
        return
    end

    cache_ab[tag] = cache_ab[tag] - 1
    if cache_ab[tag] == 0 then
        cache_ab.totalcount = cache_ab.totalcount - 1
    end
    if cache_ab.totalcount == 0 then
        -- cache_ab.proto = nil
        log.Warning(4, "iosystem_load-UnloadAssetBundle", abName, tag)
        IOSystem.UnloadAssetBundle(abName, unloadAll or false)
    end
end

function Init()
    event.Register(event.BEFORE_SEND_BUG_REPORT, function()
        PrintCacheInfo()
        PrintCacheList()
    end)
    
    local timer_mgr = require "timer_mgr"
    timer_mgr:AddTimer(0,
            TimerUpdateFun
    )
end

function TimerUpdateFun()
        local cbs = cache._callback
        if cbs.ind == 1 then
            return 0
        end
        --避免回调操作对cbs容器影响，重置容器
        cache._callback = {ind = 1}
        for k, v in ipairs(cbs) do
            local abName = v[3]
            local cache_ab = cache[abName]
            local UIPrefab = cache_ab.proto
            on_one_callback(v, cache_ab, UIPrefab, true)
        end
        return 0
end
Init()
function PrintCacheInfo()
    local dump = _G["Edump"]
    if not dump then
        return
    end
    local d = dump(cache)
    LogHelp.clipboard = d
    log.Warning1(0, d)
end
function GetList()
    local skiplist = {
        _callback = 1,
        __callbackInfo = 1,
        __cbs = 1,
        proto = 1,
        ind = 1,
        state = 1,
        lastCbTime = 1
    }
    local list = {}
     -- {ind=1}
    for k, v in pairs(cache) do
        if skiplist[k] == nil then
            for kk, vv in pairs(v) do
                if skiplist[kk] == nil then
                    if vv > 0 then
                        list[kk] = list[kk] or {ind = 1}
                        IndAddObj(list[kk], k)
                    -- IndAddObj(list[kk],table.concat({k,vv},"|"))
                    end
                end
            end
        end
    end
    return list
end
function PrintCacheList()
    local dump = _G["Edump"]
    if not dump then
        return
    end
    local list = GetList()
    local listD = {}
    listD.size = 0

    if hashRemote then
        for k, v in pairs(list) do
            for ii, vv in ipairs(v) do
                if not listD[vv] then
                    listD[vv] = 1
                    listD.size = listD.size + hashRemote:GetSize(vv)

                    local deps = m_AssetBundleManifest:GetAllDependencies(vv)

                    local len = deps.Length
                    for i = 0, len - 1 do
                        if not listD[deps[i]] then
                            listD[deps[i]] = 1
                            listD.size = listD.size + hashRemote:GetSize(deps[i])
                        end
                    end
                end
            end
        end
    end
    list.size = listD.size
    list.sizeMark = require("idle").toeNumber(listD.size)
    local d = json.encode(list)
    LogHelp.clipboard = d
    log.Warning1(0, d)
end
