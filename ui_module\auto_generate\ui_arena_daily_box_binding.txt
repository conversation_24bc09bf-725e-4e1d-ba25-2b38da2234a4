local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local SpriteSwitcher = CS.War.UI.SpriteSwitcher


module("ui_arena_daily_box_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenadailybox.prefab"

WidgetTable ={
	btn_close = { path = "btn_close", type = Button, event_name = "OnBtnCloseClickedProxy"},
	txt_challengeNum = { path = "Main/txt_challengeNum", type = Text, },
	rtf_progress = { path = "Main/TaskProgress/progressBG/rtf_progress", type = RectTransform, },
	txt_BoxItemNum_1 = { path = "Main/TaskProgress/BoxItemList/BoxItem_1/Image/txt_BoxItemNum_1", type = Text, },
	ss_BoxItem_1 = { path = "Main/TaskProgress/BoxItemList/BoxItem_1/ss&btn_BoxItem_1", type = SpriteSwitcher, },
	btn_BoxItem_1 = { path = "Main/TaskProgress/BoxItemList/BoxItem_1/ss&btn_BoxItem_1", type = Button, event_name = "OnBtnBoxItem_1ClickedProxy"},
	txt_BoxItemNum_2 = { path = "Main/TaskProgress/BoxItemList/BoxItem_2/Image/txt_BoxItemNum_2", type = Text, },
	ss_BoxItem_2 = { path = "Main/TaskProgress/BoxItemList/BoxItem_2/ss&btn_BoxItem_2", type = SpriteSwitcher, },
	btn_BoxItem_2 = { path = "Main/TaskProgress/BoxItemList/BoxItem_2/ss&btn_BoxItem_2", type = Button, event_name = "OnBtnBoxItem_2ClickedProxy"},
	txt_BoxItemNum_3 = { path = "Main/TaskProgress/BoxItemList/BoxItem_3/Image/txt_BoxItemNum_3", type = Text, },
	ss_BoxItem_3 = { path = "Main/TaskProgress/BoxItemList/BoxItem_3/ss&btn_BoxItem_3", type = SpriteSwitcher, },
	btn_BoxItem_3 = { path = "Main/TaskProgress/BoxItemList/BoxItem_3/ss&btn_BoxItem_3", type = Button, event_name = "OnBtnBoxItem_3ClickedProxy"},

}
