local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

local selectType

--region Controller Life
module("ui_national_flag_setting_controller")
local controller = nil
local UIController = newClass("ui_national_flag_setting_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)

    if data then
        selectType = data.selectType or 1
    else
        selectType = 1
    end
end

function UIController:OnShow()
    self.__base.OnShow(self)
    self:TriggerUIEvent("SetType", selectType)
end

function UIController:Close(data)
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
end

function UIController:AutoUnsubscribeEvents()
end
--endregion

--region Controller Logic

function UIController:OnBtnCloseClickedProxy()
    ui_window_mgr:UnloadModule("ui_national_flag_setting")
end

function UIController:OnBtnConfirmClickedProxy()
    self:TriggerUIEvent("OnConfirm", selectType)
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
