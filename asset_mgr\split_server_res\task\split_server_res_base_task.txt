local require = require
local string = string
local tostring = tostring
local setmetatable = setmetatable
local util = require "util"
local table = table
local pairs = pairs
local Time = CS.UnityEngine.Time
local event = require "event"
local os = os
local AssetBundleManager = CS.War.Base.AssetBundleManager
local SplitServerFilesRequester = CS.War.Res.SplitServerFilesRequester
local SplitServerHashChecker = CS.War.Res.SplitServerHashChecker
local SplitServerPathMgr = CS.War.Res.SplitServerPathMgr
local SplitServerFinishFileRecorder = CS.War.Res.SplitServerFinishFileRecorder
local split_server_res_download_mgr = require "split_server_res_download_mgr"
local split_server_res_ver_mgr = require "split_server_res_ver_mgr"
local split_define = require "split_server_res_define"
local Warning = split_define.logger.Warning

local ClassTable = {}
local VerTaskType = split_define.VerTaskType
local DownloadErrorCode = split_define.DownloadErrorCode
local VerTaskState = split_define.VerTaskState
local TaskState = split_define.TaskState
local reportData = split_define.reportData

function ClassTable.New(...)
    local newObj = {}
    setmetatable(newObj, {
        __index = ClassTable
    })
    newObj:ctor(...)
    return newObj
end

function ClassTable:ctor(version, serverId)
    self.version = version
    self:AddServerId(serverId)
    self.addLangRes = true
    self.isPauseTask = true
    self.finishSize = 0
    self.finishSizeOnPause = 0
    self.totalSize = 0  
    self.curVerTaskState = VerTaskState.Waiting
    self.requestFiles2Count = 0
    self.verTaskType = self.verTaskType or VerTaskType.SwitchServerResDownload
    
    self.needWifi = split_define.needWifi
    self.maxDownloadingCount = split_define.maxDownloadCount
    self.finishFileRecorderList = {}
    self.downloadTaskList = {}
    self.finishFileRecorderCount = 0
    
    self.reportData = {}
    for key, val in pairs(reportData) do
        self.reportData[key] = val
    end
    
    Warning(2,"create version task: ", self.version, serverId, self.verTaskType, self.maxDownloadingCount)
end

function ClassTable:AddServerId(serverId)
    self.serverList = self.serverList or {}
    for i = 1, #self.serverList do
        if self.serverList[i] == serverId then
            return
        end
    end
    table.insert(self.serverList, serverId)

    Warning(2,"add serverId: ", self.version, serverId)
end

function ClassTable:RequestFiles2(isTimeout)
    if self.curVerTaskState == VerTaskState.Waiting or isTimeout  then
        Warning(2,"version task step:", self.curVerTaskState, self.version, self.requestFiles2Count)
        self.curVerTaskState = VerTaskState.RequestFiles2

        if self.requestFiles2Count >= split_define.requestFiles2MaxCount then
            self.curVerTaskState = VerTaskState.RequestFiles2Timeout
            Warning(1,"version task timeout 5 times:", self.curVerTaskState, self.version, self.requestFiles2Count)
            split_server_res_download_mgr.CheckPingNet() 
            return
        end
        
        self:RemoveRequestFiles2Timer()
        self.requestFiles2Timer =  util.DelayCallOnce(split_define.requestFiles2Timeout, function()
            if self.curVerTaskState < VerTaskState.RequestFiles2Finish then
                self:RequestFiles2(true)
            end
        end)

        self.requestFiles2Count = self.requestFiles2Count + 1
        Warning(2,"version task step:", self.curVerTaskState, self.version, self.requestFiles2Count)
        --Files2比较大luajson解析比较耗时,放C#解析
        SplitServerFilesRequester.RequestByVersion(self.version, function(hashRemote)
            if self.curVerTaskState < VerTaskState.RequestFiles2Finish and not util.IsObjNull(hashRemote)
                    and not util.IsObjNull(hashRemote.list)  then
                self.curVerTaskState = VerTaskState.RequestFiles2Finish
                self.hashRemote = hashRemote
                self:RemoveRequestFiles2Timer()

                Warning(2,"version task step:", self.curVerTaskState, self.version, self.requestFiles2Count)
            end
        end, 5) 
    end
end

function ClassTable:GetVersionTask()
    if self.curVerTaskState == VerTaskState.RequestFiles2Finish then
        if self.isPauseTask then
            return
        end
        self.curVerTaskState = VerTaskState.GetVersionTask
        Warning(2,"version task step:", self.curVerTaskState, self.version, self.addLangRes)
        SplitServerHashChecker.GetVersionTaskAsync(self.hashRemote, self.version, self.addLangRes, function(versionTask)
            if self.curVerTaskState == VerTaskState.GetVersionTask then
                if not util.IsObjNull(versionTask) and versionTask.totalSize > 0 then
                    self:InitDownloadTaskData(versionTask)
                    Warning(2,"version task step:", self.curVerTaskState, self.version)
                else
                    self:OnFinishAll()
                end 
            end
        end, function()
            Warning(0,"GetVersionTaskAsync error:", self.curVerTaskState, self.version)
            self.curVerTaskState = VerTaskState.GetVersionTaskError   --重新拉取files2
        end) 
    end
end

function ClassTable:InitDownloadTaskData(versionTask)
    self.versionTaskData = versionTask
    self.totalSize = versionTask.totalSize
    self.totalCount = versionTask.fileCount
    self.finishSize = 0
    self.finishCount = 0
    self.failCount = 0
    self.downloadingCount = 0
    self.startTime = os.clock()

    self:DelayProgressChange()
    self:InitDownloadTaskList()
    self.curVerTaskState = VerTaskState.Downloading
    split_server_res_download_mgr.RefreshMainBtn()
end

function ClassTable:InitDownloadTaskList()
    local taskList = self.versionTaskData.taskList
    for i = 0, taskList.Count - 1 do
        local taskFileInfo = taskList[i]
        if not taskFileInfo.isExistInRecord then
            local task =
            {
                taskFileInfo = taskFileInfo,
                abName = taskFileInfo.abName,
                abSize = taskFileInfo.abSize,
                downloadUrl = taskFileInfo.downloadUrl,
                savePath = taskFileInfo.savePath,
                state = TaskState.WAITING,
                isCheckSum = taskFileInfo.checkSum ~= 0, --是否CheckSum，等于0则跳过校验
            }
            self.downloadTaskList[task.savePath] = task
        end
        --table.insert(self.downloadTaskList, task)
    end
end

function ClassTable:Tick(deltaTime)
    if self.isPauseTask or self.isDispose then
        return
    end

    if self.curVerTaskState == VerTaskState.Waiting then
        self:RequestFiles2()
        
    elseif self.curVerTaskState == VerTaskState.RequestFiles2Timeout or 
            self.curVerTaskState == VerTaskState.GetVersionTaskError then
        self.requestFiles2Count = 0
        self:RequestFiles2(true)  --重试
        
    elseif self.curVerTaskState == VerTaskState.RequestFiles2Finish then
        self:GetVersionTask()
        
    elseif self.curVerTaskState == VerTaskState.Downloading then
        self:DownloadTick(deltaTime)
    end

    if self:CheckFinishFileRecord() then --检测到有新的file完成记录，则return延后一帧检测版本任务是否完成
        return
    end

    self:CheckIsFinishAll()
end

function  ClassTable:DownloadTick()
    if (self.needWifi and (util.GetNetworkType() ~= util.ENETWORK_TYPE.WIFI)
            or (util.GetNetworkType() == util.ENETWORK_TYPE.NO_NETWORK)) then
        return
    end
    
    for _, task in pairs(self.downloadTaskList) do
        if (not task.reDownloadTime or Time.realtimeSinceStartup > task.reDownloadTime) and
                (task.state == TaskState.WAITING or task.state == TaskState.CANCEL
                        or task.state == TaskState.ERROR) then
            
            local downloadingCount = split_server_res_download_mgr.GetProcessingCount()
            if (downloadingCount >= self.maxDownloadingCount) then
                split_server_res_download_mgr.SetTickInterval(0.1)
                break
            end

            if SplitServerHashChecker.CheckIsNeedDownloadBeforeRequest(self.hashRemote, task.taskFileInfo, self.version) then
                if task.state == TaskState.WAITING then --首次Download前CheckFileSum，防止有文件下载成功但没写入记录文件
                    task.state = TaskState.DOWNLOADING
                    Warning(4,"download file is exist to check sum, abName: ", self.version, task.abName, task.downloadUrl, task.savePath)
                    split_server_res_download_mgr.CheckFileSum(task, true)
                else
                    task.state = TaskState.DOWNLOADING
                    if split_server_res_download_mgr.IsSwitchDns() then  --触发切换dns, 获取新的下载链接
                        task.downloadUrl = SplitServerPathMgr.GetResDownloadUrl()
                    end

                    Warning(2,"request downloading file, abName: ", self.version, task.abName, task.downloadUrl, task.savePath)
                    split_server_res_download_mgr.RequestTask(task, self.hashRemote)
                end
            end
        end
    end
end

function ClassTable:CheckFinishFileRecord()
    if self.finishFileRecorderCount > 0 and self.finishFileRecorderList then
        for i = 1, #self.finishFileRecorderList do
            SplitServerFinishFileRecorder.AddFile(self.version, self.finishFileRecorderList[i])
        end
        while #self.finishFileRecorderList > 0 do
            table.remove(self.finishFileRecorderList, #self.finishFileRecorderList)
        end
        self.finishFileRecorderCount = 0
        return true
    end
    return false
end

function ClassTable:CheckIsFinishAll()
    if self.curVerTaskState == VerTaskState.Downloading and self.totalCount <= self.finishCount then
        self.curVerTaskState = VerTaskState.SaveHashRemoteVirtual
        Warning(1, "version task saveHashRemoteVirtual start step:", self.curVerTaskState, self.version)
        --self.versionTaskData传nil则save hashRemote整个列表, 和旧的下载逻辑保持一致，save整个列表
        SplitServerHashChecker.SaveHashRemoteVirtualOfDownLoadVersion(self.hashRemote, self.version,
                split_define.saveHashRemoteVirtualTag, nil, function()
                    Warning(1, "saveHashRemoteVirtual success")
                    self:OnFinishAll()
                end)
    end
end

function ClassTable:OnFinishAll()
    if self.curVerTaskState ~= VerTaskState.Finish then
        self.curVerTaskState = VerTaskState.Finish
        Warning(1, "version task step:", self.curVerTaskState, self.version)
        split_server_res_download_mgr.CheckToNextVersionTask()
        split_server_res_ver_mgr.AddDownloadFinishVersion(self.version)
        if self.OnFinish then
            self:OnFinish()
        end
        self:Dispose()
    end
end

function ClassTable:OnTaskError(savePath, errorCode, errorDes, isFirst)
    local task = self.downloadTaskList[savePath]
    if not task then
        return
    end
    if not isFirst then
        Warning(2,"OnTaskError", savePath,  errorCode, errorDes) 
    else
        Warning(2,"first checksum fail2", savePath,  errorCode, errorDes)
    end
    if errorCode == DownloadErrorCode.SaveFileOccupied then --文件被占用延时重试
        task.reDownloadTime = Time.realtimeSinceStartup + split_define.reDownloadTimeOnSaveFileOccupied

    elseif errorCode == DownloadErrorCode.CheckSumFail or errorCode == DownloadErrorCode.RenameFail then    
        task.reDownloadTime = Time.realtimeSinceStartup + split_define.reDownloadTimeOnChangeType
        
    elseif task.state == TaskState.ERROR then 
        if split_server_res_download_mgr.CheckPingNet() then  --再次失败检测网络
            task.reDownloadTime = Time.realtimeSinceStartup + split_define.reDownloadTimeOnChangeType --修改下载方式延时重试
        end
    end
    
    task.state = TaskState.ERROR
end

function ClassTable:IsFinish()
    return self.curVerTaskState == VerTaskState.Finish
end

function ClassTable:OnTaskFinish(savePath)
    local task = self.downloadTaskList[savePath]
    if not task or task.state == TaskState.FINISH then
        return
    end

    Warning(2,"OnTaskFinish", savePath)
    if self.version == split_server_res_ver_mgr.GetCurUseFilesVer() then
        AssetBundleManager.SaveCachingHash(task.abName)
        Warning(3,"SaveCachingHash", task.savePath)
    end
    
    task.state = TaskState.FINISH
    self.downloadTaskList[savePath] = nil
    self.finishCount = self.finishCount + 1
    self.finishSize = self.finishSize + task.abSize
    
    table.insert(self.finishFileRecorderList, task.taskFileInfo)
    self.finishFileRecorderCount = self.finishFileRecorderCount + 1
    task.taskFileInfo = nil

    self:DelayProgressChange(task)
end

function ClassTable:OnTaskCancel(savePath)
    local task = self.downloadTaskList[savePath]
    if not task then
        return
    end
    
    Warning(2,"OnTaskCancel", savePath)
    task.state = TaskState.CANCEL
end

function ClassTable:StartTask()
    self.isPauseTask = false
end

function ClassTable:PauseTask()
    self.isPauseTask = true
    self.finishSizeOnPause = self.finishSize
end

function ClassTable:IsPause()
    return self.isPauseTask
end

function ClassTable:ResumeTask()
    self.isPauseTask = false
end

function ClassTable:RemoveRequestFiles2Timer()
    if self.requestFiles2Timer then
        util.RemoveDelayCall(self.requestFiles2Timer)
        self.requestFiles2Timer = nil
    end
end

function ClassTable:GetDownloadParam()
    local finishSize = self.isPauseTask and self.finishSizeOnPause or self.finishSize
    return finishSize, self.totalSize, not self.isPauseTask
end

function ClassTable:Dispose()
    if self.isDispose then
       return
    end

    if not util.IsObjNull(self.versionTaskData) then
        SplitServerHashChecker.RecycleTasks(self.versionTaskData.taskList) 
    end
    self:RemoveRequestFiles2Timer()
    self.isDispose = true
    self.isPauseTask = true
    self.finishSizeOnPause = self.finishSize
    self.versionTaskData = nil
end

------------------------ 上报打点相关---------------------
----------------------------------------------------------
function ClassTable:ProgressChange()
    print("ProgressChange()")
    util.DelayOneCallNoCoroutine("ProgressChange", function()
        self:DelayProgressChange()
    end, 0.1)
end

function ClassTable:DelayProgressChange(task)
    if self.progressChangeFun then
        self.progressChangeFun(self.finishCount, self.totalCount, self.finishSize, self.totalSize)
    end

    -- 后台下载打点上报 进度百分比打点 每10%上报一次
    if self.totalCount > 0 then
        local percent = string.format("%d%%", self.finishCount/self.totalCount*100)
        if self.reportData[percent] and self.reportData[percent] == 0 then
            self.reportData[percent] = 1
            local nowTime = os.clock()
            local durationTime = self.lastTime and (nowTime - self.lastTime) or (self.startTime and (nowTime - self.startTime))
            self.lastTime = nowTime
            -- local properties = string.format("{\"durationTime\":%f,\"progress\":\"%s\",\"total_time\":%f,\"finishCount\":%d,\"totalCount\":%d}", durationTime, percent, nowTime - startTime, finishCount, totalCount)
            local properties = {
                durationTime = durationTime,
                progress = percent,
                total_time = nowTime - self.startTime,
                finishCount = self.finishCount,
                totalCount = self.totalCount,
                finishSize = self.finishSize,
                totalSize = self.totalSize,
                downloadWay = task and task.downloadWay or ""
            }
            --Debug.LogWarning("SetDownloadData:" .. percent.."properties:"..properties.."verTaskType:"..verTaskType)
            self:Report(self.verTaskType, properties)
            --idle.PrintMM()
        end
    end
end

function ClassTable:Report(eName, obj)
    if (not eName) or (not obj) then
        return
    end
    local player_mgr = require "player_mgr"
    local playerProp = player_mgr.GetPlayerProp()

    local game_config 	= require "game_config"
    local q1sdk 	= require "q1sdk"
    local ui_login_main_mgr = require "ui_login_main_mgr"
    obj.pid = game_config.CHANNEL_ID
    obj.uuid = q1sdk.GetUUID()
    obj.verTaskType = self.verTaskType
    obj.level = playerProp and playerProp.lv
    obj.server_id = ui_login_main_mgr.GetLoginServerID()
    obj.role_id = player_mgr.GetPlayerRoleID()
    obj.since_start_time = Time.realtimeSinceStartup
    obj.res_ver_server_list = table.concat(self.serverList, "," )
    obj.res_version = self.version
    event.Trigger(event.GAME_EVENT_REPORT, eName, obj)
end

return ClassTable