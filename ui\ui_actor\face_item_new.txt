--简易头像 只有头像、品质框
local print = print
local require = require
local typeof = typeof
local type = type
local tonumber = tonumber

local util = require "util"
local game_scheme = require "game_scheme"
local sprite_asset = require "card_sprite_asset"
local device_param_util = require "device_param_util"
local render_tool = require "render_tool"
local Common_Util = CS.Common_Util.UIUtil

local GameObject = CS.UnityEngine.GameObject
local RectTransform = CS.UnityEngine.RectTransform
local EventTriggerListener = CS.War.UI.EventTriggerListener
local ImageGray = CS.War.UI.ImageGray
local CModelViewer = require "modelviewer"
local ParticleSystem = CS.UnityEngine.ParticleSystem
local ParticleSystemRenderer = CS.UnityEngine.ParticleSystemRenderer
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
local SkinnedMeshRenderer = CS.UnityEngine.SkinnedMeshRenderer
local MeshRenderer = CS.UnityEngine.MeshRenderer

local typeof = typeof
local string = string
local GameObject = CS.UnityEngine.GameObject
local Vector3 = CS.UnityEngine.Vector3
local SpriteRenderer = CS.UnityEngine.SpriteRenderer
local log = require "log"
local http_inst = require "http_inst"

module("face_item_new")

local entityResPath = "ui/prefabs/simple_face_item_new.prefab"
local faceSpriteAsset = nil
local goodsSpriteAsset = nil
local nationalFlagAsset = nil
local M = {}

M.widget_table = {
    --self
    iconBtn = { path = "", type = EventTriggerListener },
    --baseLayer
    icon = { path = "baseLayer/Image/icon", type = "Image" },
    iconBg = { path = "baseLayer/iconBg", type = "Image" },
    frame = { path = "baseLayer/Frame", type = "Image" },
    --bottomLayer
    levelBg = { path = "bottomLayer/levelBg", type = "Image" },
    levelText = { path = "bottomLayer/levelBg/levelText", type = "Text" },

    bigLevelText = {path = "bottomLayer/bigLevelText", type = "Text" },
    
    --mountPoint
    frameRt = { path = "mountPoint/effect/rt", type = "RawImage" },
    frameRtMaterial = { path = "mountPoint/effect/rt", type = ImageGray },
    effectInImg = { path = "mountPoint/effect/effectInImg", type = "Image" }, --低配机特效置换用
    
    --country
    img_country = { path = "baseLayer/img_Country", type = "Image" },
}

function M:ctor(selfType)
    faceSpriteAsset = faceSpriteAsset or sprite_asset.CreateHeroAsset()
    goodsSpriteAsset = goodsSpriteAsset or sprite_asset.CreateSpriteAsset()
    nationalFlagAsset = nationalFlagAsset or sprite_asset.CreateNationalFlagAsset()
    self.__base:ctor(selfType, "face_item_new")
    self.faceID = nil             -- 头像配置id
    self.actorLv = 0              -- 玩家等级
    self.isShowLv = false          -- 是否显示等级
    self.showBigLv = false
    self.frameID = nil
    self.isShowFrame = nil
    self.clickCallback = nil      -- 物品点击
    self.enableState = true          -- 亮状态

    self.showEffect = false  -- 是否显示特效
    self.maskType = 0        -- 特效的masking类型
    self.effectOrder = 0     -- 层级

    self.monsterFaceID = nil  --怪物头像配置id

    self.anchoredPosition = { x = 0, y = 0 }
    self.worldPosition = nil
    
    self.customAvatarData = nil
    self.nationalFlagID = nil
end

function M:SetActive(enable)
    if self.UIRoot then
        self.UIRoot:SetActive(enable)
    end
end

function M:Init(parentTrans, callback, scale)
    self:LoadResource(entityResPath, "",
            function()
                if callback then
                    callback()
                end
                self.scale = scale or 1
                if scale then
                    self.transform.localScale = { x = scale, y = scale, z = scale }
                else
                    self.transform.localScale = Vector3.one
                end

                self.transform.anchoredPosition3D = { x = 0, y = 0, z = 0 }
                self.transform.anchorMin = { x = 0.5, y = 0.5 }
                self.transform.anchorMax = { x = 0.5, y = 0.5 }

                self.rectTransform = self.transform:GetComponent(typeof(RectTransform))
                self:SetAnchoredPosition(self.anchoredPosition.x, self.anchoredPosition.y)
                if self.worldPosition then
                    self:SetWorldPosition(self.worldPosition)
                end
                
                if self.faceID or self.monsterFaceID or self.customAvatarData then
                    self:DisplayInfo()
                end
                self:UnRegistEvents()
                self:RegistEvents()
            end,
            true,
            parentTrans
    )
    return self
end

---空实现,避免老的face_item切换到新的报错
function M:SetNewBg()

end

--[[设置状态，是否灰色]]
function M:SetEnableState(enable, btnEnable)
    self.enableState = enable
    if self.UIRoot then
        self.btnInteractable = btnEnable and btnEnable or enable
        --self.iconBtn.interactable = btnEnable and btnEnable or enable
        local childs = self.UIRoot:GetComponentsInChildren(typeof(ImageGray))
        for i = 0, childs.Length - 1 do
            childs[i]:SetEnable(not enable)
        end
    end
end

function M:RegistEvents()
    self.OnDownItem = function()
        self.isLongClick = nil
        self.tick = util.DelayCall(0.6, function()
            self.isLongClick = true
            --长按
            if (not self.faceID and not self.monsterFaceID) or (not self.btnInteractable) then
                return
            end
            if self.longClickBack then
                self.longClickBack()
            end
        end)
    end
    self.OnUpItem = function(go, evt)
        if self.tick then
            util.RemoveDelayCall(self.tick)
            self.tick = nil
        end
        if not self.isLongClick then
            --短按
            if (not self.faceID and not self.monsterFaceID and not self.customAvatarData) or (not self.btnInteractable) then
                return
            end
            if evt ~= nil and evt.dragging then
                return
            end
            if self.clickCallback then
                if self.customAvatarData and self.customAvatarData.url then
                    self.clickCallback(self.customAvatarData.localUrl)
                else
                    self.clickCallback(self.faceID)
                end

            end
        end
    end
    self.iconBtn:onDown('+', self.OnDownItem)
    self.iconBtn:onUp('+', self.OnUpItem)
end

function M:UnRegistEvents()
    if self.OnDownItem then
        self.iconBtn:onDown('-', self.OnDownItem)
        self.OnDownItem = nil
    end
    if self.OnUpItem then
        self.iconBtn:onUp('-', self.OnUpItem)
        self.OnUpItem = nil
    end
end

function M:SetCustomFaceInfo(data, callback, parent)
    self.customAvatarData = data
    self.faceID = nil
    self.clickCallback = callback

    if self.UIRoot then
        self:DisplayInfo()
        if parent then
            self.UIRoot.transform:SetParent(parent, false)
        end
    end
end

function M:SetNationalFlagID(nationalFlagID)
    self.nationalFlagID = nationalFlagID
end

--[[设置头像数据
@param faceID 头像配置ID
@param callback 点击回调函数
]]
function M:SetFaceInfo(faceID, callback, parent)
    self.customAvatarData = nil
    self.faceID = faceID
    self.clickCallback = callback

    if self.UIRoot then
        self:DisplayInfo()
        if parent then
            self.UIRoot.transform:SetParent(parent, false)
        end
    end
end

--[[设置怪物头像数据
@param monsterFaceID 头像配置ID
@param callback 点击回调函数
]]
function M:SetMonsterFaceInfo(faceID, callback, parent)
    self.monsterFaceID = faceID
    self.clickCallback = callback

    if self.UIRoot then
        self:DisplayInfo()
        if parent then
            self.UIRoot.transform:SetParent(parent, false)
        end
    end
end


-- [[头像数据显示]]
function M:DisplayInfo()
    if not self.faceID and not self.monsterFaceID and not self.customAvatarData then
        return
    end
    if self.faceID and type(self.faceID) == "number" then
        self:SetActorIcon(self.faceID)
    elseif self.faceID and self.faceID.localUrl and type(self.faceID.localUrl) == "string" then
        self:SetAvatar(self.faceID.localUrl)
    elseif self.faceID and type(self.faceID) == "string" then
        local res = tonumber(self.faceID)
        if res then
            self:SetActorIcon(res)
        else
            self:SetAvatar(self.faceID)
        end
    elseif self.monsterFaceID then
        self:SetActorIcon()
    elseif self.customAvatarData and self.customAvatarData.localUrl then
        self:SetAvatar(self.customAvatarData.localUrl)
    end
    self:SetBgState(true)
    self:SetEnableState(self.enableState, true)            --是否变灰
    self:SetFrameID(self.frameID, self.isShowFrame)
    self:SetActorLvText(self.isShowLv, self.actorLv,self.showBigLv)    --玩家等级
    self:SetPlayerNationalFlag()
end

function M:SetBgState(enable)
    self.iconBg:SetActive(enable)
end


--[[更改自定义头像]]
function M:SetAvatar(remoteUrl)
    if not self.UIRoot then
        return
    end
    local custom_avatar_mgr = require "custom_avatar_mgr"
    local custom_avatar_data = require "custom_avatar_data"
    --TODO 添加localURl判断
    -- local FileName = custom_avatar_mgr.ExtractFilename(localUrl)
    -- local imageId = FileName:match("(.+)%..+$")
    -- custom_avatar_mgr.CheckAvatarLocalUrl(localUrl,imageId)
    local imageID = custom_avatar_mgr.ExtractFilename(remoteUrl):match("(.+)%..+$")
    local islocalUrl = string.find(remoteUrl, "^file://", 1) == 1
    if islocalUrl then
        custom_avatar_mgr.SetAvatarIcon(self.icon, remoteUrl)
    else
        local callback = function(url)
            if url and not string.IsNullOrEmpty(url) then
                custom_avatar_mgr.SetAvatarIcon(self.icon, url)
            end
        end
        custom_avatar_mgr.CheckAvatarLocalUrl(remoteUrl, imageID, callback)
    end
end

--[[更改头像图标]]
function M:SetActorIcon(faceid)
    --self.faceID = faceid or face_data.GetRoleFrameID()
    self.faceID = faceid
    if not self.UIRoot then
        return
    end
    local defaultFaceID = game_scheme:InitBattleProp_0(8127).szParam.data[0]
    self.faceID = (self.faceID == 0 or self.faceID == "0") and defaultFaceID or self.faceID --无头像框的情况下显示默认头像框
    local faceImage = nil
    if self.faceID and self.faceID > 0 then
        local cfg_face = game_scheme:RoleFace_0(self.faceID)
        if not cfg_face then
            ----                 --print("<color=#FF0000>未找到对应头像配置，faceID = "..self.faceID..",临时用headID = 9201的头像代替</color>")
            cfg_face = game_scheme:RoleFace_0(defaultFaceID)
        end
        if not cfg_face then
            log.Error("请检查头像配置,找不到9201的默认头像配置")
            return
        end
        faceImage = cfg_face.rivalType
        --elseif self.faceID == -1 then
        --    faceImage = 700
    elseif self.monsterFaceID and self.monsterFaceID ~= 0 then
        --特殊处理怪物头像
        faceImage = self.monsterFaceID
    else
        local cfg_face = game_scheme:RoleFace_0(defaultFaceID)
        faceImage = cfg_face.rivalType
    end

    faceSpriteAsset:GetSprite(faceImage, function(sprite)
        if self and self.icon and not self.icon:IsNull() and faceImage then
            self.icon.sprite = sprite
        end
    end)
end

--[[设置头像框]]
function M:SetFrameID(frameID, isShow)
    local face_data = require "actor_face_data"
    self.frameID = frameID or face_data.GetRoleFrameID()
    local defaultframeID = game_scheme:InitBattleProp_0(1228).szParam.data[0]
    self.frameID = (self.frameID == 0 or self.frameID == "0") and defaultframeID or self.frameID --无头像框的情况下显示默认头像框
    self.isShowFrame = isShow
    if not self.UIRoot then
        return
    end

    if self.frameID and self.frameID > 0 and self.isShowFrame then
        local frameCfg = game_scheme:RoleFrame_0(self.frameID)

        if not frameCfg then
            frameCfg = game_scheme:RoleFrame_0(defaultframeID)
        end

        if frameCfg then
            faceSpriteAsset:GetSprite(frameCfg.rivalType, function(sp)
                if self.frame then
                    self.frame.sprite = sp
                    self.frame.gameObject:SetActive(self.isShowFrame)
                end
            end)

            -- 刷新特效的显示
            self:UnLoadFrameEffect()
            if not frameCfg.Effect then
                self.showEffect = false
            end
            if self.showEffect == true then
                if frameCfg.Effect then
                    if device_param_util.JudgeDeviceLevel() < device_param_util.eMidLevel then
                        if frameCfg.EffectInImg and frameCfg.EffectInImg > 0 then
                            self:LoadFrameInImg(frameCfg.EffectInImg)
                        end
                    else
                        self:LoadFrameEffect(frameCfg.Effect, frameCfg.materialType or 0, frameCfg.rt or 0)
                    end
                end
            end
        else
            if self.frame.gameObject.activeSelf == true then
                self.frame.gameObject:SetActive(false)
            end
        end
    else
        if self.frame.gameObject.activeSelf == true then
            self.frame.gameObject:SetActive(false)
        end
    end
end

--[[设置玩家等级]]
function M:SetActorLvText(isShow, lv,showBigLv)
    self.showBigLv = showBigLv == true
    self.isShowLv = isShow == true
    self.actorLv = lv or 1
    if not self.UIRoot then
        return
    end

    if isShow == true then
        if self.showBigLv then
            self.bigLevelText.text = self.actorLv
        else
            self.levelText.text = self.actorLv
        end
    else
        self.levelText.text = ""
        self.bigLevelText.text = ""
    end
    Common_Util.SetActive(self.levelBg,self.isShowLv and not self.showBigLv)
    Common_Util.SetActive(self.bigLevelText,self.isShowLv and self.showBigLv)
end

--- 设置玩家国旗
function M:SetPlayerNationalFlag()
    if not self.UIRoot then
        return
    end
    if self.nationalFlagID == nil then
        return
    end

    if self.nationalFlagID == 0 then
        self.nationalFlagID = 217
    end
    if require("national_flag_mgr").CheckNationalFlagIsOpen() then
        nationalFlagAsset :GetSprite(self.nationalFlagID, function(sp)
            if self.img_country then
                self.img_country.sprite = sp
                self.img_country.gameObject:SetActive(true)
            end
        end)
    else
        self.img_country.gameObject:SetActive(false)
    end
end

--[[是否显示特效
@param bool 是否显示
@param maskType render的显示类型，主要处理滑块光效穿透问题
]]
function M:FrameEffectEnable(bool, order, maskType)
    self.showEffect = bool
    self.maskType = maskType or 0
    self.effectOrder = order
end

--[[设置头像框特效]]
function M:LoadFrameEffect(resPath, materialType, rt)
    if not resPath or resPath == "" then
        return
    end
    resPath = string.lower(resPath)
    -- 根据屏幕分辨率适配
    if rt > 0 then
        if self.GeneratedMtr then
            self.GeneratedMtr = false
            GameObject.Destroy(self.frameRt.material)
        end

        local targetMtr = materialType == 2 and self.frameRtMaterial.grayMaterial or self.frameRtMaterial.normalMaterial

        local stValue = self.frame.materialForRendering:GetInt("_Stencil")
        local writeMaskValue = self.frame.materialForRendering:GetInt("_StencilWriteMask")
        local compValue = self.frame.materialForRendering:GetInt("_StencilComp")

        local trueMtr = GameObject.Instantiate(targetMtr)
        trueMtr:SetInt("_Stencil", stValue)
        trueMtr:SetInt("_StencilWriteMask", writeMaskValue)
        trueMtr:SetInt("_StencilComp", compValue)
        self.frameRt.material = trueMtr

        self.GeneratedMtr = true

        self.targetEffectModelViewerResPath = resPath
        self.ownerModuleName = "fame_item_" .. (self.frameID or 0)
        render_tool.LoadResource(resPath, function(assetbundleName, renderGameObject)
            if self:IsDisposed() then
                -- 界面已经销毁,不再加载资源
                renderGameObject.Dispose()
                return
            end
            if self.targetEffectModelViewerResPath ~= assetbundleName or util.IsObjNull(self.frameRt) then
                --界面不再需要加载完成的资源，或已销毁
                renderGameObject.Dispose()
                return
            end
            self.frameRt:SetActive(true)
            self.targetEffectModelViewerResPath = nil

            renderGameObject.SetScale({ x = 0.8 * 0.02, y = 0.8 * 0.02, z = 0.8 * 0.02 })

            self.renderTxInfo = render_tool.GetRenderTextureByTag(resPath, self.ownerModuleName)
            if self.renderTxInfo then
                self.effectModelViewerResPath = resPath
                self.renderTxInfo:SetImage(self.frameRt, self.frameRt, true)
                renderGameObject.Show()
                return
            end

            renderGameObject.SetMaxParticleSize(100)
            self.effectModelViewerResPath = resPath

            self.renderTxInfo = renderGameObject.RenderGameObject(resPath, 300, 300, render_tool.RenderMode.Single, 0, 0, 60, nil, self.ownerModuleName)
            if self.renderTxInfo then
                self.renderTxInfo:SetImage(self.frameRt, self.frameRt, true)
            end

            local obj = renderGameObject.GetObj()
            if not util.IsObjNull(obj) then
                local ps = obj.transform:GetComponent(typeof(ParticleSystem))
                if not util.IsObjNull(ps) then
                    ps:Play()
                end
            end
        end)
    else
        self.frameRt:SetActive(false)
        local scaleFactor = 1--(Screen.width / Screen.height) / (720/1280) * self.scale * 0.8
        if self.effectModelViewer == nil then
            self.effectModelViewer = CModelViewer()
            self.effectModelViewer:Init(self.frame.transform, function()
                self.effectModelViewer:ShowGameObject(resPath, function(goEffect)
                    goEffect.gameObject.transform.localScale = { x = scaleFactor, y = scaleFactor, z = scaleFactor }
                    local childs = goEffect:GetComponentsInChildren(typeof(ParticleSystemRenderer))
                    for i = 0, childs.Length - 1 do
                        --childs[i].gameObject.transform.localScale = {x = scaleFactor, y = scaleFactor, z = scaleFactor}
                        childs[i].maskInteraction = self.maskType or 0
                    end
                    local spriteRendererChilds = goEffect:GetComponentsInChildren(typeof(SpriteRenderer))
                    for i = 0, spriteRendererChilds.Length - 1 do
                        spriteRendererChilds[i].maskInteraction = self.maskType or 0
                    end
                    if util.IsObjNull(goEffect:GetComponent(typeof(SortingGroup))) then
                        goEffect:AddComponent(typeof(SortingGroup))
                    end
                    goEffect:GetComponent(typeof(SortingGroup)).sortingOrder = self.effectOrder or 100

                    --SkinnedMeshRenderer + MeshRenderer 特殊处理 (遮挡效果)
                    SpecialRendererProcess(self.maskType == 1, goEffect:GetComponentsInChildren(typeof(SkinnedMeshRenderer)))
                    SpecialRendererProcess(self.maskType == 1, goEffect:GetComponentsInChildren(typeof(MeshRenderer)))
                    SpecialRendererProcess(self.maskType == 1, goEffect:GetComponentsInChildren(typeof(SpriteRenderer)), true)
                end)
            end)
            self.effectModelViewer:SetRenderOrder(self.effectOrder or 100, true)
        end
    end
end

function SpecialRendererProcess(maskType, Renderer, onlyMaterial)
    local stencil = maskType and 1 or 0
    local stencilComp = maskType and 3 or 8
    for i = 0, Renderer.Length - 1 do
        if onlyMaterial then
            Renderer[i].material = InstantiateMaterial(Renderer[i].material, stencil, stencilComp)
        else
            local mats = Renderer[i].materials
            for j = 0, mats.Length - 1 do
                mats[j] = InstantiateMaterial(mats[j], stencil, stencilComp)
            end
            Renderer[i].materials = mats
        end

    end
end

---复制一个material
function InstantiateMaterial(material, stencil, stencilComp)
    local trueMtr = GameObject.Instantiate(material)
    trueMtr:SetInt("_Stencil", stencil)
    trueMtr:SetInt("_StencilComp", stencilComp)
    return trueMtr
end

--[[头像框特效(图片方式)]]
function M:LoadFrameInImg(iconId)
    goodsSpriteAsset:GetSprite(iconId, function(sprite)
        --if self.iconImg and not util.IsObjNull(self.iconImg) then
        if not util.IsObjNull(self.effectInImg) then
            self.effectInImg.sprite = sprite
            self.effectInImg:SetActive(true)
        end
    end)
end

function M:SetRenderOrder(_sorting)
    if self:IsValid() and self.sorting_group then
        self.sorting_group.enabled = true
        self.sorting_group.sortingOrder = _sorting
    end
end

function M:SetAnchoredPosition(x, y)
    if not x or not y then
        return
    end
    self.anchoredPosition = { x = x, y = y }
    if self:IsValid() and self.rectTransform then
        self.rectTransform.anchoredPosition = self.anchoredPosition
    end
end

function M:SetWorldPosition(position)
    if not position then
        return
    end
    self.worldPosition = position
    if self:IsValid() and self.rectTransform then
        self.rectTransform.position = self.worldPosition
    end
end

function M:UnLoadFrameEffect()
    if self.effectModelViewer then
        self.effectModelViewer:Dispose()
        self.effectModelViewer = nil
    end

    if self.effectModelViewerResPath then
        if not util.IsObjNull(self.renderTxInfo) then
            self.renderTxInfo:SetImage(self.frameRt, self.frameRt, false)
            render_tool.ReleaseRenderGameObject(self.effectModelViewerResPath, self.ownerModuleName, self.renderTxInfo)
            self.effectModelViewerResPath = nil
            self.renderTxInfo = nil
            self.frameRt:SetActive(false)
        end
    end

    if self.GeneratedMtr then
        self.GeneratedMtr = false
        if not util.IsObjNull(self.frameRt) then
            GameObject.Destroy(self.frameRt.material)
        end
    end

    if not util.IsObjNull(self.effectInImg) then
        self.effectInImg:SetActive(false)
    end
end

function M:Dispose()
    if self:IsValid() then
        self:UnRegistEvents()
    end
    self.isDisposed = true
    self.faceID = nil             -- 头像配置id
    self.actorLv = 0              -- 玩家等级
    self.isShowLv = false          -- 是否显示等级
    self.showBigLv = false
    self.frameID = nil
    self.isShowFrame = nil
    self.clickCallback = nil
    self.customAvatarData = nil
    self.nationalFlagID = nil
    self:UnLoadFrameEffect()
    self.__base:Dispose()
end

local class = require "class"
local base_game_object = require "base_game_object"
CFaceItem = class(base_game_object, nil, M)