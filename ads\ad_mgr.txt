-- ad_mgr.txt -------------------------------------------------
-- author:  李婉璐
-- date:    2020.11.11
-- ver:     1.0
-- desc:    广告管理器，目前包括admob谷歌sdk和AN FB SDK两种
--------------------------------------------------------------
local require = require
local ipairs = ipairs
local table = table
local game_config = require "game_config"
local CAdmob = require "admob"
local CHuawei_ads = require "huawei_ads"
local log = require "log"
local print = print
local event = require "event"
local version_mgr = require "version_mgr"
local ui_pop_mgr = require "ui_pop_mgr"
local lottery_data = require "lottery_data"
local ad_config = require "ad_config"
local const        = require "const"
local ReviewingUtil = require "ReviewingUtil"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local MobileAds = CS.GoogleMobileAds.AdMob
local HuaWeiAds = CS.HuaWeiAds.Ads
local Application = CS.UnityEngine.Application
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
module("ad_mgr")

local rewardedAds = {}  -- 谷歌广告
local isInit = false
local isInitCallback = false
------------预加载优化-------------
local preLoadAds = {}  -- 预加载广告列表

-------------------广告加载优化，最多加载2个广告，签到优先占一个----------------
local maxLoadCount = 0  -- 最多加载广告个数
local maxLoadRound = 2  -- 最多加载广告轮数，如果第一轮失败，可以再重试一轮
local retryTimes = 0    -- 加载失败重试次数

local loadedRewardedAds = {} -- 已经加载的广告列表 最多只有2个广告
local waitingForLoads ={}    -- 等待加载的广告列表
local failedRewardedAds = {} -- 加载失败的广告列表
local testAdid = nil
local ids = nil

--该渠道包是否弹出CMP隐私协议
function IsCMPPackage()
	print("Admob CMPSDK IsCMPPackage,CHANNEL_TAG"..game_config.CHANNEL_TAG)	
	local ret = false
	if MobileAds.Instance.SetSendCmpDataTure and not ReviewingUtil.IsReviewing() then
		ret = const.IsEuropeanUnionChannels()
	end
    return ret
end
function InitSdk()
	-- if Application.isEditor then return end
	isInit = true

	-- 谷歌admob广告
	if game_config.ENABLE_ADMOB then
		if version_mgr.CheckSvnTrunkVersion(46818) then
			if IsCMPPackage() then
				DoSendCmpDataSwitch()
				MobileAds.Instance:InitializeWithCMP(function()
					isInitCallback = true
				end)
			else
				--没加隐私协议cmp之前的逻辑
				MobileAds.Instance:Initialize(function()
					isInitCallback = true
				end)
			end
		else
			isInitCallback = true
			MobileAds.Instance:Initialize()
		end
	end

	-- 华为广告
	if game_config.ENABLE_HUAWEI then
		if version_mgr.CheckSvnTrunkVersion(53338) then
			HuaWeiAds.Instance:Initialize()
		end
	end
end

function DoSendCmpDataSwitch()
	print("DoSendCmpDataSwitch")
	local send_cmp_data = PlayerPrefs.GetInt("send_cmp_data", 0)
	if MobileAds.Instance.SetSendCmpDataTure and send_cmp_data == 0 then
		local const = require "const"
		if const.IsEuropeanUnionChannels() then
			MobileAds.Instance:SetSendCmpDataTure()
			PlayerPrefs.SetInt("send_cmp_data", 1)
			print("DoSendCmpDataSwitch game_config.CHANNEL_TAG if")
		else
			print("DoSendCmpDataSwitch game_config.CHANNEL_TAG else")
		end
	end
end
function Start()
	-- 2020/11/26 包体中默认开放了 ENABLE_ADMOB。但 2020/12/4 功能正式对外，需屏蔽 11/26 版本
	-- 2021/2/26  包体中增加了华为广告。

	-- if Application.isEditor then return end
	if not isInit then
		InitSdk()
	end

	-- admob  预加载 一个一个预加载
	if ad_config.IsEnableAd() then
		InitData()
		if not game_config.UNABLE_PRELOAD_ADS and isInitCallback == true then
			CreateRewardedAd()
		end
	end
end

function InitData()
	-- 签到活动开启了
	if ui_pop_mgr.CheckIsOpen(237, false) then
		maxLoadCount = 1
	end
	-- 广告开启了
	if lottery_data.CheckModuleIsOpen(3) then
		maxLoadCount = 2
	end

	if game_config.ENABLE_TEST_LOGIN_ADMOB then
		maxLoadCount = 8
	end
	-- 初始化数据
	waitingForLoads = {1, 2, 3, 4, 5, 6}
	loadedRewardedAds = {}

	ids = ad_config.GetRewardedAdids()
	testAdid = ad_config.GetTestAdid()
end

function CreateRewardedAd(idx)
	if not ad_config.IsEnableAd() then
		return
	end

	if #loadedRewardedAds >= maxLoadCount then
		return
	end
	if #waitingForLoads <= 0 then
		log.Error("Admob loadingAds is null")
		return
	end

	preLoadAds = {}
	for i, index in ipairs(waitingForLoads) do
		if i > maxLoadCount-#loadedRewardedAds then
			break
		end
		table.insert(preLoadAds, index)
	end

	LoadRewardedAd()
	
end

function LoadRewardedAd()
	if #preLoadAds <= 0 then
		return
	end
	-- 一个一个加载
	for i, index in ipairs(preLoadAds) do
		if i == 1 then
			local ad_instance = nil
			if game_config.ENABLE_ADMOB then
				ad_instance = CAdmob()
				ad_instance:RequestAndLoadRewardedAd(testAdid or ids[index], index)
			elseif game_config.ENABLE_HUAWEI then
				ad_instance = CHuawei_ads()
				ad_instance:RequestAndLoadRewardedAd(testAdid or ids[index], index)
			end

			if ad_instance then
				local preAds = rewardedAds[index]
				if preAds then
					preAds:Dispose()
				end
				rewardedAds[index] = ad_instance
				break
			end
		end
	end
end

function ShowRewardedAd(idx)
	if not ad_config.IsEnableAd() then
		return
	end

	local index = IsRewardedAdLoaded()
	if index then
		local ad_instance = rewardedAds[index]
		ad_instance:ShowRewardedAd()

		for i, v in ipairs(loadedRewardedAds) do
			if v == index then
				table.remove(loadedRewardedAds, i)
				break
			end
		end	
		table.insert(waitingForLoads, index)
	end
end

function IsRewardedAdLoaded(idx)
	if not ad_config.IsEnableAd() then
		return nil
	end

	local index = nil
	for i, v in ipairs(loadedRewardedAds) do
		if i == 1 then
			index = v
			break
		end
	end

	if index then
		local ad_instance = rewardedAds[index]
		if ad_instance and ad_instance:IsLoaded() then
			return index
		end
	end
	return nil
end

-- 收到广告回调事件
function RewardedAdCallback(eventName, tag1, index, msg)
	if tag1 == 1 or tag1 == 2 then
		for i, v in ipairs(waitingForLoads) do
			if v == index then
				table.remove(waitingForLoads, i)
				break
			end
		end	
	end
	
	if tag1 == 1 then
		-- 广告加载完成 清除失败列表，重新插入到等待加载列表
		table.insert(loadedRewardedAds, index)
		for i, v in ipairs(failedRewardedAds) do
			table.insert(waitingForLoads, 1, v)
		end
		failedRewardedAds = {}
		retryTimes = 0

		-- 下一次加载
		table.remove(preLoadAds, 1)
		LoadRewardedAd()
	end
	if tag1 == 2 then
		-- 广告加载失败，如果全部失败，再循环一次
		table.insert(failedRewardedAds, index)
		if #failedRewardedAds >= #ids then
			waitingForLoads = failedRewardedAds
			failedRewardedAds = {}
			retryTimes = retryTimes + 1
		end
		if retryTimes < maxLoadRound then
			CreateRewardedAd()
		else
			retryTimes = 0
		end
	end

	if tag1 == 5 then
		-- 广告关闭时
		if game_config.ENABLE_ADMOB and rewardedAds[index] then
			rewardedAds[index]:Dispose()
			CreateRewardedAd(index)
		end
	end

	if tag1 == 6 then
		-- 在用户因观看视频而应获得奖励时
		if game_config.ENABLE_HUAWEI and rewardedAds[index] then
			rewardedAds[index]:Dispose()
			CreateRewardedAd(index)
		end
	end
end
event.Register(event.EVENT_REWARDEDAD_CALLBACK, RewardedAdCallback)

-- 展示广告测试套件
function ShowMediationTestSuite()
	if not game_config.ENABLE_ADMOB then
		return
	end
	--if game_config.ENABLE_Q1_DEBUG_MODE  == true then
		MobileAds.ShowMediationTestSuite()
	--end	
end

function OnMonthCardUpdate(e,data)
	local isShow = PlayerPrefs.GetInt("UserEuropeanUnionTerms", 0) == 0
	log.Warning("Admob UserEuropeanUnionTerms",PlayerPrefs.GetInt("UserEuropeanUnionTerms", 0),"isShow",isShow,"const.IsEuropeanUnionChannels()",const.IsEuropeanUnionChannels())
	if not ReviewingUtil.IsReviewing() and not MobileAds.Instance.SetSendCmpDataTure and const.IsEuropeanUnionChannels() and isShow then
		local ui_window_mgr = require "ui_window_mgr"
		ui_window_mgr:ShowModule("ui_european_union_terms")	
	end
end
event.Register(event.UPDATE_MONTHCARD_INFO,OnMonthCardUpdate)