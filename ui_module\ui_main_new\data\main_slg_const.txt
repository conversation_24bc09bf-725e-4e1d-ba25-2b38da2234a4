module("main_slg_const")

--主界面所有的按钮组类型,包括左上、左下、右上、右下、左下第二组
MainButtonGroupType = {
    LeftTop = 1,
    LeftBottom = 2,
    RightTop = 3,
    RightBottom = 4,
    LeftBottom2 = 5, --左下的第二组按钮,用于治疗等特殊按钮
    bottom = 6, --底部按钮
    bubble = 7, --气泡按钮
    RightTop2 = 8,--右侧第二组按钮，活动、礼包
    RightTop3 = 9,--右侧第三组按钮，下载，调查问卷，跳转商店下载链接
    LeftBottom4 = 10, --左下的第三组按钮
    LeftBottom5 = 11, --左下的第四组按钮,丧尸宝藏  ---此排只支持预定义的按钮（不能无脑扩展），也就是不支持动态加进去 
}

--场景类型
MainSceneType = {
    None = 0,
    CitySceneOnly = 1,
    SandSceneOnly = 2,
    DesertStormSceneOnly = 3, --沙漠风暴专属
}


---@description 主界面显示状态
MainShowState = {
    None=0, --默认
    OpenAll=1, --全部打开
    TopRemain=2, --顶部剩余
    CloseAll=3, --全部关闭
}

MainExperimentType = {
    None = 0,
    CityRecovery = 1, --城市复兴
    Arena_New = 2,  --竞技场
    CampTrial = 3, --阵营试炼，地牢
    Adventure = 4, --冒险之旅
    Arena_Weekend = 5, --巅峰竞技场
    Arena_3v3 = 6, --3v3竞技场
    Arena_Storm = 7, --风暴竞技场

}


--主界面所有配置的按钮类型
MainButtonType = {
    ---@description 建筑队列
    buildQueue = "buildQueue",
    ---@description  科研
    research = "research",
    ---@description 沙盘搜索
    sandSearch = "sandSearch",
    ---@description 建造列表
    buildList = "buildList",
    ---@description 情报
    info = "info",
    ---@description 货车
    vehicle = "vehicle",
    ---@description 货车
    allianceTrain = "allianceTrain",
    ---@description 酒馆任务
    bar = "bar",
    ---@description 治疗
    heal = "heal",
    ---@description 将军的试炼
    generalTrail = "generalTrail",
    ---@description 幸存者
    survivor = "survivor",
    ---@description 幸存者
    survivor2 = "survivor2",--左下方右侧按钮的幸存者；注意当前需要幸存者动态切换区域，因此设计俩个区域都有
    ---@description 登录好礼
    loginGift = "loginGift",
    ---@description 待办日程
    schedule = "schedule",
    ---@description 狗屋奖励，左下方按钮，幸存者按钮隐藏并且狗屋有奖励才会显示
    dogHouse = "dogHouse",
    ---@description 狗屋奖励，左下方右侧按钮，左下方按钮满时才显示（后续优化）
    dogHouse2 = "dogHouse2",

    ----左下区域----
    ZombieTreasure = "ZombieTreasure", --丧尸宝藏
    ---end左下 -----
    -------------------右上按钮区域  begin----注意 这里只包括默认的按钮，活动按钮是动态变化的-------------
    ---@description 特惠商城,对应礼包
    gift = "gift",  
    ---@description 超值活动,对应福利
    Benefits = "Benefits",
    ---@description 日常活动
    daily = "daily",
    ---@description 同盟对决
    allianceDuel = "allianceDuel",
    ---@description 限时礼包
    limitGift = "limitGift",
    ------------------活动入口  end-----------------
    ---@description 背包
    bag = "bag",
    ---@description 邮件
    mail = "mail",
    ---@description 联盟
    union = "union",
    ---@description 英雄
    hero = "hero",
    ---@description 小游戏
    miniGame = "miniGame",
    ---@description 试炼
    trial = "trial",
    
    ---气泡按钮类型
    ---@description 联盟帮助气泡
    unionHelpBubble = "unionHelpBubble",
    ---@description 联盟邀请气泡
    chatAllianceMass = "chatAllianceMass",
    ---@description 联盟战争气泡
    unionWarBubble = "unionWarBubble",
    ---@deprecated 联盟提示加入气泡
    unionJoinBubble = "unionJoinBubble",
    
    ---@description 总统邮件气泡
    mailPresidentBubble = "mailPresidentBubble",
    ---@description 盟主邮件气泡
    mailR5Bubble = "mailR5Bubble",
    ---@description 系统重要邮件气泡
    mailSystemImportantBubble = "mailSystemImportantBubble",
    ---@description 攻略邮件气泡
    mailStrategyBubble = "mailStrategyBubble",
    
    ---@deprecated 竞技场提示气泡
    arenaChallengeBubble = "arenaChallengeBubble",
    
    ---@deprecated 城市竞赛气泡
    citySiegeBubble = "citySiegeBubble",
    ---@deprecated 联盟邀请函气泡
    invitationBubble = "invitationBubble",
    
    --region 聊天气泡
    chatDigTreasureBubble = "chatDigTreasureBubble",
    chatAllianceNoticeBubble = "chatAllianceNoticeBubble",--联盟公告
    chatPrivateBubble = "chatPrivateBubble",--私聊气泡
    surpriseBagBubble = "surpriseBagBubble",--惊喜盒气泡
   
    --endregion
    
    --region  (已弃用)右上第二列礼包
    duelGiftPackage = "duelGiftPackage",
    --endregion
    
    --region  右上第三列礼包
    resourceDownload = "resourceDownload", --资源下载
    questionnaire = "questionnaire", --调查问卷
    --endregion
    
    --region 沙漠风暴专用
    DefenceQueue = "DefenceQueue", --编辑城防
    SetSquad = "SetSquad", --编辑队伍
    NormalHospital = "NormalHospital", --我方普通医院入口
    FieldHospital = "FieldHospital", --战地医院入口
    CityRelocation = "CityRelocation",--迁城
    
    RewardChess = "RewardChess",--奖励宝箱
    --endregion
    changePackage = "changePackage",--换包入口
}

--主界面Mask图标类型
MainMaskIconType = {
    None = 0,
    soldier = 1,
}

---@field 主界面聊天气泡显示类型
MainChatBubbleIconType = {
    None = 0,
    SingleImage = 1, --单独一张图片
    BgAndIcon = 2,   --背景和图标组合
}

---@public 审核服主界面开关
ReviewingMainShowType = {
    [MainButtonType.buildQueue] = true,
    [MainButtonType.research] = true,
    [MainButtonType.sandSearch] = true,
    [MainButtonType.buildList] = true,
    [MainButtonType.info] = true,
    [MainButtonType.vehicle] = true,
    [MainButtonType.allianceTrain] = true,
    [MainButtonType.bar] = true,
    [MainButtonType.heal] = true,
    [MainButtonType.generalTrail] = true,
    [MainButtonType.survivor] = true,
    [MainButtonType.survivor2] = true,
    [MainButtonType.loginGift] = true,
    [MainButtonType.gift] = true,
    [MainButtonType.Benefits] = true,
    [MainButtonType.daily] = true,
    [MainButtonType.allianceDuel] = true,
    [MainButtonType.dogHouse] = true,
    [MainButtonType.dogHouse2] = true,
    
    [MainButtonType.bag] = true,
    [MainButtonType.mail] = true,
    [MainButtonType.union] = true,
    [MainButtonType.hero] = true,
    [MainButtonType.miniGame] = true,
    [MainButtonType.trial] = true,
    
    [MainButtonType.unionHelpBubble] = true,
    [MainButtonType.chatAllianceMass] = true,
    [MainButtonType.unionWarBubble] = true,
    [MainButtonType.unionJoinBubble] = true,
    
    [MainButtonType.mailPresidentBubble] = true,
    [MainButtonType.mailR5Bubble] = true,
    [MainButtonType.mailSystemImportantBubble] = true,
    [MainButtonType.mailStrategyBubble] = true,
    
    [MainButtonType.arenaChallengeBubble] = true,
    [MainButtonType.citySiegeBubble] = true,
    [MainButtonType.invitationBubble] = true,
    
    [MainButtonType.chatDigTreasureBubble] = true,
    [MainButtonType.chatAllianceNoticeBubble] = true,
    [MainButtonType.chatPrivateBubble] = true,
    [MainButtonType.surpriseBagBubble] = true,
    
    [MainButtonType.duelGiftPackage] = true,
    
    [MainButtonType.DefenceQueue] = true,
    [MainButtonType.SetSquad] = true,
    [MainButtonType.NormalHospital] = true,
    [MainButtonType.FieldHospital] = true,
    [MainButtonType.CityRelocation] = true,
    [MainButtonType.RewardChess] = true,
}