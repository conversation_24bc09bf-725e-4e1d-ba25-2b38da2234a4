-- union_ad.txt -------------------------------------------------
-- author:  李婉璐
-- date:    2022.1.6
-- ver:     1.0
-- desc:    优量汇广告
--------------------------------------------------------------
local require = require
local debug = debug
local xpcall = xpcall
local log = require "log"
local event = require "event"
local puzzlegame_mgr = require "puzzlegame_mgr"

local Application = CS.UnityEngine.Application
local GDTSDKManager = CS.Tencent.GDT.GDTSDKManager
local UnionRewardAd = CS.Tencent.GDT.UnionRewardAd
module("union_ad")

local isInit = false
local rewardedAd = nil  -- 激励视频广告
local isVideoFinished = false
local showAd = false -- 是否点击了播放广告

local rewardAdid = "9032586197641403"
local appid_gdt = "1200361285"

function InitSdk()
	-- iOS优量汇广告sdk功能
	if Application.isEditor then
		return false
	end

	if not puzzlegame_mgr.IsSockPackage() then
		return false
	end

	if isInit then
		return false
	end

	isInit = true
	local f,res = xpcall(function()
		if GDTSDKManager.Init then
			local isInitGDTS = GDTSDKManager.Init(appid_gdt)
			if isInitGDTS then
				log.Warning("GDT 初始化优量汇sdk 成功")
				LoadRewardedAd()
			else
				log.Error("GDT 初始化优量汇sdk 失败")
			end
			return isInitGDTS
		end
    end, debug.traceback)
    if not f then
        log.Error(res)
    end
	return false
end

function LoadRewardedAd()
	if not isInit then
		log.Error("GDT 请先初始化sdk")
		return
	end

	if rewardedAd then
		UnionRewardAd.Instance:DestroyRewardAd()
		rewardedAd = nil
	end

	rewardedAd = UnionRewardAd.Instance:LoadRewardVideoAd(rewardAdid, function(value, tag1, tag2, msg)
		if tag1 == 1 then
			-- 广告加载完成
			log.Warning("GDT, OnAdLoaded:")
			if showAd == true then
				showAd = false
				ShowRewardedAd()
			end
		end
		if tag1 == 2 then
			-- 各种错误信息回调
			log.Error("GDT, OnAdError:"..tag2..", msg:"..msg)
		end
		if tag1 == 3 then
			-- 广告开始展示
			log.Warning("GDT, OnAdOpening:")
		end
		if tag1 == 5 then
			-- 广告关闭回调
			log.Warning("GDT, OnAdClosed:")
			event.Trigger(event.UNION_AD_GET_REWARDED, isVideoFinished)
			LoadRewardedAd()
		end
		if tag1 == 6 then
			-- 广告播放完成，获取激励视频奖励
			log.Warning("GDT, OnAdReward:")
			isVideoFinished = true
		end
		event.Trigger(event.EVENT_REWARDEDAD_CALLBACK, tag1, nil, msg)
	end)
end

function ShowRewardedAd()
	local isAdLoaded = IsRewardedAdLoaded()
	-- 广告没加载，先加载再显示
    if not isAdLoaded then
        SetNeedShowAd(true)
        LoadRewardedAd()
    else
		if not rewardedAd then
			log.Error("Reward Ad is null")
			return
		end
		UnionRewardAd.Instance:ShowRewardVideoAd(rewardedAd)
    end
end

function IsRewardedAdLoaded()
	if not rewardedAd then
		return false
	end
	if UnionRewardAd.Instance:IsLoaded(rewardedAd) then
		return true
	end
end

function SetNeedShowAd(value)
	showAd = value
end