-- local print = print
local require = require
-- local pairs = pairs
-- local table = table
local string = string

local log = require "log"
local util = require "util"
-- local asset_loader = require "asset_loader"
local files_version_mgr = require "files_version_mgr"
-- local event = require "event"
-- local dkjson = require "dkjson"	
local tostring= tostring
local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
-- local HybridCLRManager = CS.HybridCLRManager
-- local File = CS.System.IO.File
-- local Regex = CS.System.Text.RegularExpressions.Regex

module("new_tinygame_manager")

-- --- 资源配置
-- local tinyConfig = nil
-- --- 下载器
-- local downhander = nil
-- --- 是否加成功
-- local isLoadTinyFail = false
-- --- 加载成功回调
-- local MiniGameEndCb = nil
-- --- 元补充数据列表
-- local metaDatas = {}
-- --- 热更程序集列表
-- local tinyDatas = {}
-- --- 上报事件
-- local reportEvent = "TINY_GAME_INFO"
-- --- 当前加载的小游戏
-- local gameKey = ""
--- 华佗游戏标志
local tinyToken = "_Tiny"
--- 华佗游戏根文件夹
local gameFolder = "casualgame_tiny"
-- --- 游戏热更代码版本
-- local gameTinyVersion = {}
-- --- 已经加载完成的小游戏
-- local hadLoadGame = {}

function GetTinyToken()
    return tinyToken
end

function IsNewTinyGame(resKey)
    return string.find(resKey, tinyToken) ~= nil
end

function IsEnableTiny()
    local v=tostring(files_version_mgr.ApkUpdateConfigTryGetValue("p_EnableHybridCLR"))
    if v=="true" or v=="True" then
        return true
    end
    return false
end

--- 获取小游戏资源的跟路径
function GetCasualGameRoot(resKey)
    if IsNewTinyGame(resKey) then
        return gameFolder
    end
    return "casualgame"
end

function GetPlatformLow()
    local platform = "android"
    if util.IsSimulateHybridCLR() then
        platform = util.GetSimulateHybridCLRTinyPlatformString()
    else
        if Application.platform == RuntimePlatform.Android then
            platform = "android"
        elseif Application.platform == RuntimePlatform.IPhonePlayer then
            platform = "ios"
        elseif Application.platform == RuntimePlatform.WebGLPlayer then
            platform = "webgl"
        end
    end
    
    return platform
end

-- --- 设置游戏代码的版本
-- function SetTinyDataVersion(resKey, version)
--     gameTinyVersion[resKey] = version
-- end

-- --- 移除其他版本的代码资源ab
-- function RemoveOtherVersionTinyData(fileConfig, resKey)
--     -- 没有设置版本跳过
--     if not gameTinyVersion[resKey] then
--         return
--     end

--     local versionKey = "_"..gameTinyVersion[resKey]
    
--     if not fileConfig or not fileConfig["Entries"] then
--         print("[tiny]fileConfig is null")
--         return
--     end

--     local reg = GetTinyDataRegex(resKey)

--     local entries = fileConfig["Entries"]
--     for k, v in pairs(entries) do
--         local match = reg:Match(v.Name)
--         if match.Success then
--             local matchVersion = match.Groups[2].Value
--             if matchVersion ~= versionKey then
--                 entries[k] = nil
--             end
--         end
--     end
-- end

-- function GetTinyDataRegex(resKey)
--     local lower_res_key = string.lower(resKey)
--     local tinyDataName = string.format("%s/%s/tinydata/%s/",gameFolder,lower_res_key,GetPlatformLow())
--     return Regex(tinyDataName.."(tiny[^_.]+)(\\S*?).bytes")
-- end


-- function FixTinyDataVersion(resKey, tinyConfig)
--     -- 没有设置版本跳过
--     if not gameTinyVersion[resKey] then
--         return
--     end

--     local reg = GetTinyDataRegex(resKey)
--     local versionKey = "_"..gameTinyVersion[resKey]

--     for k, v in pairs(tinyConfig.TinyDatas) do
--         local match = reg:Match(v)
--         -- 一个配置中只有一个会匹配
--         if match.Success then
--             local fileName = match.Groups[1].Value
--             tinyConfig.TinyDatas[k] = string.gsub(v, fileName, fileName..versionKey)
--         end
--     end
-- end

-- --- 加载小游戏华佗热更数据
-- function LoadTinyGame(resKey, miniGameEndCb)

--     if hadLoadGame[resKey] then
--         miniGameEndCb()
--         return
--     end

--     log.Warning("[tiny]start LoadTinyGame")
--     gameKey = resKey

--     ReportLoadEvent("load_start")

--     MiniGameEndCb = miniGameEndCb
--     local casualgame_mgr = require "casualgame_mgr"
--     downhander = casualgame_mgr.GetOrCreateDownHander(resKey)
--     local lower_res_key = string.lower(resKey)
--     isLoadTinyFail = false

--     local platformLow = GetPlatformLow()
--     --- 加载热更配置文件
--     local asset_name = string.format("%s/%s/tinydata/%s/tinyconfig.json", gameFolder, lower_res_key, platformLow)
--     downhander:LoadAllAssets(asset_name, function(abName, asset)

--         if asset and asset[0] then
--             log.Warning("[tiny]load update config complete", asset_name)
--             tinyConfig = dkjson.decode(asset[0].text)
--             FixTinyDataVersion(resKey, tinyConfig)
--             LoadAOTMetas()
--         else
--             isLoadTinyFail = true
--             log.Error("[tiny]load update config fail", asset_name)
--             LoadOver()
--         end

--         downhander:UnloadAsset(asset_name)
--     end)
-- end

-- function Test_LoadTinyDatas()
--     local serverPath = "http://172.16.126.185:3127/minigame_hybridclr"

--     local dlls = {}
--     table.insert(dlls, serverPath .. "/Tiny13.bytes")
--     table.insert(dlls, serverPath .. "/Tiny1.bytes")
--     table.insert(dlls, serverPath .. "/Tiny21.bytes")
--     table.insert(dlls, serverPath .. "/Tiny22.bytes")
--     local count = 0
--     for i = 1, #dlls do
--         local path = dlls[i]
--         HybridCLRManager.Instance:LoadCasualGameByPath(path, function()
--             log.Warning("[tiny]Test_LoadTinyData complete:"..path)
--             count = count + 1
--             if count == #dlls then
--                 LoadOver()
--             end
--         end)
--     end
-- end

-- function Test_LoadConfig(path)
--     local content = File.ReadAllText(path)
--     return dkjson.decode(content)
-- end

-- local aotMetasCount = 0
-- --- 加载华佗热更元补充数据
-- function LoadAOTMetas()
--     local patchedAOTAssemblyList = tinyConfig.AOTMetas
--     aotMetasCount = #patchedAOTAssemblyList
-- -- 这里同步到h5时注意下，超能国内的launch场景已经加载过补充元数据了，所以这里就直接跳过了
--     if aotMetasCount == 0 or util.IsSimulateHybridCLR() then
--         -- 没有需要加载的元补充数据，直接加载小游戏热更代码
--         LoadTinyDatas()
--         return
--     end

--     for i = 1, #patchedAOTAssemblyList do
--         local metaPath = patchedAOTAssemblyList[i]
--         if not metaDatas[metaPath] then
--             local loader = asset_loader(metaPath, "tiny")
--             loader:load(
--                 function(res)

--                     if res and res.asset then
--                         log.Warning("[tiny]metaPath load complete", metaPath)
--                         ReportDownloadEvent("download_complete", metaPath)
--                         metaDatas[metaPath] = true
--                         HybridCLRManager.Instance:LoadMetadataForAOTAssembly(res.asset.bytes)
--                     else
--                         isLoadTinyFail = true
--                         ReportDownloadEvent("download_fail", metaPath)
--                         log.Error("[tiny]load metaPath fail", metaPath)
--                     end

--                     LoadAOTMetaComplete()
--                 end
--             )
--         else
--             LoadAOTMetaComplete()
--         end
--     end
-- end

-- function LoadAOTMetaComplete()
--     aotMetasCount = aotMetasCount - 1
--     if aotMetasCount <= 0 then
--         LoadTinyDatas()
--     end
-- end

-- local tinyDatasCount = 0
-- --- 加载小游戏热更代码
-- function LoadTinyDatas()
--     local hotUpdateDllList = tinyConfig.TinyDatas
--     tinyDatasCount = #hotUpdateDllList
--     if tinyDatasCount == 0 then
--         -- 没有需要加载小游戏热更代码
--         LoadOver()
--         return
--     end

--     for i = 1, #hotUpdateDllList do
--         local tinyPath = hotUpdateDllList[i]
--         if not tinyDatas[tinyPath] then
--             downhander:LoadAllAssets(tinyPath, function(abName, asset)

--                 if asset and asset[0] then
--                     log.Warning("[tiny]tinyPath load complete",tinyPath)
--                     ReportDownloadEvent("download_complete", tinyPath)
--                     tinyDatas[tinyPath] = true
--                     local bytes = HybridCLRManager.Instance:Decryptor(asset[0].bytes, tinyPath)
--                     HybridCLRManager.Instance:LoadAssembly(bytes)
--                 else
--                     isLoadTinyFail = true
--                     ReportDownloadEvent("download_fail", tinyPath)
--                     log.Error("[tiny]tinyPath load fail", tinyPath)
--                 end
    
--                 downhander:UnloadAsset(tinyPath)
--                 LoadTinyDataComplete()
--             end)
--         else
--             LoadTinyDataComplete()
--         end
--     end
-- end

-- function LoadTinyDataComplete()
--     tinyDatasCount = tinyDatasCount - 1
--     if tinyDatasCount <= 0 then
--         LoadOver()
--     end
-- end


-- function LoadOver()
    
--     local data = {
--         -- 事件状态
--         event_name = "load_over",
--         -- 小游戏名称
--         game_key = gameKey,
--         -- 是否失败
--         load_fail = isLoadTinyFail
--     }

--     event.Trigger(event.GAME_EVENT_REPORT, reportEvent, data)

--     if not isLoadTinyFail then
--         hadLoadGame[gameKey] = true
--     end

--     if MiniGameEndCb then
--         MiniGameEndCb()
--     end
    
-- end

-- --- 每次应用启动时上报
-- function ReportLaunch()
--     local data = {
--         -- 事件状态
--         event_name = "launch",
--         -- 是否启用华佗功能
--         enable = IsEnableTiny()
--     }
--     event.Trigger(event.GAME_EVENT_REPORT, reportEvent, data)
-- end

-- --- 开始加载华佗小游戏时上报
-- function ReportLoadEvent(eventName)
--     local data = {
--         -- 事件状态
--         event_name = eventName,
--         -- 小游戏名称
--         game_key = gameKey
--     }
--     event.Trigger(event.GAME_EVENT_REPORT, reportEvent, data)
-- end

-- --- 开始加载华佗小游戏时上报
-- function ReportDownloadEvent(eventName, assetName)
--     local data = {
--         -- 事件状态
--         event_name = eventName,
--         -- 小游戏名称
--         game_key = gameKey,
--         -- 资源名称
--         asset_name = assetName
--     }
--     event.Trigger(event.GAME_EVENT_REPORT, reportEvent, data)
-- end