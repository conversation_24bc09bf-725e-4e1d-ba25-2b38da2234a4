-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
module('metabase_pb')


V1M=V(4,"AllianceEvent_Declare",0,1)
V2M=V(4,"AllianceEvent_OpenAllianceBoss",1,2)
V3M=V(4,"AllianceEvent_Carriage",2,3)
V4M=V(4,"AllianceEvent_AttackCity",3,4)
V5M=V(4,"AllianceEvent_PVPBase_Att",4,5)
V6M=V(4,"AllianceEvent_PVPBase_Def",5,6)
V7M=V(4,"AllianceEvent_Announcement",6,7)
V8M=V(4,"AllianceEvent_PresidentMail",7,8)
V9M=V(4,"AllianceEvent_PlunderPCarriage",8,9)
V10M=V(4,"AllianceEvent_StartPCarriage",9,10)
V11M=V(4,"AllianceEvent_PowerGrowth",10,11)
V12M=V(4,"AllianceEvent_PhysicalStrengthCost",11,12)
V13M=V(4,"AllianceEvent_Radar",12,13)
V14M=V(4,"AllianceEvent_TavernMission",13,14)
V15M=V(4,"AllianceEvent_AllianceDuelBox",14,15)
V16M=V(4,"AllianceEvent_ArmsRaceEmblem",15,16)
V17M=V(4,"AllianceEvent_JoinWorldBoss",16,17)
V18M=V(4,"AllianceEvent_JoinAllianceBoss",17,18)
V19M=V(4,"AllianceEvent_Chat",18,19)
V20M=V(4,"AllianceEvent_EnterAlliance",19,20)
V21M=V(4,"AllianceEvent_LeaveAlliance",20,21)
V22M=V(4,"AllianceEvent_Donate",21,22)
V23M=V(4,"AllianceEvent_Assemble",22,23)
E1M=E(3,"AllianceEcologicalEvent",".CSMsg.AllianceEcologicalEvent")

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M}

AllianceEvent_AllianceDuelBox = 15
AllianceEvent_Announcement = 7
AllianceEvent_ArmsRaceEmblem = 16
AllianceEvent_Assemble = 23
AllianceEvent_AttackCity = 4
AllianceEvent_Carriage = 3
AllianceEvent_Chat = 19
AllianceEvent_Declare = 1
AllianceEvent_Donate = 22
AllianceEvent_EnterAlliance = 20
AllianceEvent_JoinAllianceBoss = 18
AllianceEvent_JoinWorldBoss = 17
AllianceEvent_LeaveAlliance = 21
AllianceEvent_OpenAllianceBoss = 2
AllianceEvent_PVPBase_Att = 5
AllianceEvent_PVPBase_Def = 6
AllianceEvent_PhysicalStrengthCost = 12
AllianceEvent_PlunderPCarriage = 9
AllianceEvent_PowerGrowth = 11
AllianceEvent_PresidentMail = 8
AllianceEvent_Radar = 13
AllianceEvent_StartPCarriage = 10
AllianceEvent_TavernMission = 14

