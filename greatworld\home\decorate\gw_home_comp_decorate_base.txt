---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by hans<PERSON><PERSON>.
--- DateTime: 2024/10/30 11:11
--- Des: 装饰基类 
local require = require
local newclass = newclass
local tonumber = tonumber
local Application = CS.UnityEngine.Application
local UIUtil = CS.Common_Util.UIUtil
local string = string
local table = table
-- local Edump = Edump
local event = require "event"
local gw_home_decorate_data = require "gw_home_decorate_data"
local gw_ed = require "gw_ed"
local util = require "util"
local log = require "log"
local unit_base_object = require "unit_base_object"
local game_scheme = require "game_scheme"
local GWConst = require "gw_const"
local gw_home_grid_data = require "gw_home_grid_data"
local gw_sand_animator_helper = require "gw_sand_animator_helper"
local GWG = GWG
local GWHomeNode = GWHomeNode
local gw_home_decorate_const = require "gw_home_decorate_const"
local monsters_approaching_group_define = require "monsters_approaching_group_define"
local monsters_approaching_group_mgr = require "monsters_approaching_group_mgr"
local gw_home_chapter_data = require "gw_home_chapter_data"
local typeof = typeof
local Animator = CS.UnityEngine.Animator
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
local asset_loader = require "asset_loader"
local EntityManager = require "entity_manager"
 local url_operation_mgr = require "url_operation_mgr"
local entityMode = EntityManager.HomeEcs
local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility
local SpriteRenderer = CS.UnityEngine.SpriteRenderer
local val = require("val")
local isHomeECSLoader = val.IsTrue("sw_home_ecs_loader", 1)
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWDecorateBase : unit_base 事件基类
---@field __base UnitBaseObject
module("gw_home_comp_decorate_base")
local GWDecorateObject = newclass("gw_home_comp_decorate_base", unit_base_object)
--- 构造器
function GWDecorateObject:ctor()
    self:ClearData()
    unit_base_object.ctor(self)
end

--- @public 设置数据 （现必须基成设置数据）
---@see override
function GWDecorateObject:InitData(mapId)
    self.mapId = mapId
    self.mapCfg = game_scheme:BuildMaincityMap_0(self.mapId)
    self.bubbleShowType,self.bubbleShowType_param = self:BubbleShowTypeSplit(self.mapCfg.bubbleShowType.data)
    
    self.selectLangIndex = 0

    if not self.mapCfg then
        log.Error("没有找到建筑配置", self.mapId)
        return
    end

    if not self.mapCfg.BeautifyRes then
        log.Error("没有找到建筑模型配置", self.mapId)
        return
    end

    local res = (self.mapCfg.BeautifyRes)

    local loader = asset_loader(res,"gw_home_decorate_base")

    loader:load(function(asset)
        local prefab = asset.asset
        self.enableECS = isHomeECSLoader and entityMode
        local sprite = prefab:GetComponentInChildren(typeof(SpriteRenderer),true)
        if sprite then
            self.enableECS = false
        end
        local animator = prefab:GetComponentInChildren(typeof(Animator),true)
        if animator then
            self.enableECS = false
        end

        if self.enableECS then
            self.entityDefaultScale = prefab.transform.localScale
            self.entityGOName = prefab.name
        end

        if self.mapCfg and self.mapCfg.layer and self.mapCfg.layer.count > 0 then
            local parentIndex = self.mapCfg.layer.data[0]
            local parentPath = gw_home_decorate_data.EHomeDecorateOrderKey[parentIndex]
            self:InstantiateModelAsync(res, GWHomeNode.GetNode(parentPath))
        else
            self:InstantiateModelAsync(res, GWG.GWHomeNode.decorateNode())
        end
    end)
end

--- @public 基类方法实例化模型
---@see override
function GWDecorateObject:InstantiateModelAsync(path, parent)
    unit_base_object.InstantiateModelAsync(self, path, parent)
end

--- 实例化成功 （现必须基成设置名字和组件Id）
---@see override
function GWDecorateObject:InstantiateSuccess(_obj)
    unit_base_object.InstantiateSuccess(self, _obj)
    self.timer = nil

    if not self.enableECS then
        self.sortingGroup = UIUtil.GetComponent(self.transform, "SortingGroup")
    end

    if self.mapCfg then
        self:SetGridPos(self.mapCfg.x, self.mapCfg.y)
        local x, y, z = gw_home_grid_data.GetPosByGridXY(self.mapCfg.x, self.mapCfg.y)
        local offset = self.mapCfg.offset
        if offset and offset.count == 3 then
            x = x + offset.data[0]
            y = y + offset.data[1]
            z = z + offset.data[2]
        end
            self:SetPosition(x, y, z)
            self:SetRotation(0, self.mapCfg.angel, 0)
            if self.mapCfg.ResScale ~= 0 then
                self:SetScale(self.mapCfg.ResScale, true)
            end
        if Application.isEditor then
            if not util.IsObjNull(self.transform) then
                local name = self.transform.name
                self.transform.name = name .. "_" .. self.mapCfg.MapID
            end    
        end
    end
    self:EventRefreshBubbleShow()
    self:EventRefreshShow()
    self:GetModelAnimator()
    self:LoadBoxName()
    self:ActivityRefreshShow()
end

---@public 根据条件判断是否设置点击name
function GWDecorateObject:LoadBoxName()
    --判断是否可以点击
    if not self.mapCfg then
        return
    end
    if self.mapCfg.bubbleParam then
        if self.mapCfg.bubbleParam.count >= 3 and self.mapCfg.bubbleParam.data[2] == 1 then
            self:SetBoxName()
        end
    end
    if self.mapCfg.shBubbleParam then
        if self.mapCfg.shBubbleParam.count > 0 and self.mapCfg.shBubbleParam.data[0] ~= 0 then
            self:SetBoxName()
        end
    end
end
---@public 设置boxName
function GWDecorateObject:SetBoxName()
    if entityMode then
        --查找boxCollider
        if util.IsObjNull(self.transform) or self.transform.childCount <= 0 then
            return
        end
        local boxParentTf = self.transform:GetChild(0);
        if util.IsObjNull(boxParentTf) then
            return
        end
        self.box = UIUtil.GetComponent(boxParentTf, "BoxCollider")
        if util.IsObjNull(self.box) then
            return
        end
        self.box.name = "build_" .. self.compId
        return
    end
    --查找boxCollider
    local boxParentTf = UIUtil.GetChild(self.transform, 0)
    if util.IsObjNull(boxParentTf) then
        log.Error(boxParentTf.name)
        return
    end
    local boxTr = UIUtil.GetChild(boxParentTf, 0)
    if util.IsObjNull(boxTr) then
        log.Error(boxParentTf.name)
        return
    end
    self.box = UIUtil.GetComponent(boxTr, "BoxCollider")
    if util.IsObjNull(self.box) then
        return
    end
    self.box.name = "build_" .. self.compId
end

function GWDecorateObject:GetModelAnimator()
    if self.enableECS then
        if not self.mapCfg or string.empty(self.mapCfg.act) then
            return
        end
        self._animator = gw_sand_animator_helper.new()
        self._animator:AddEntity(self.mapCfg.BeautifyRes,self.hybridEntity)
        if self.mapCfg.actParam.count == 1 then
            if self._animator then
                self._animator:SetAttackLoopInterval(false)
            end
            return
        end
        if self._animator then
            local time = self.mapCfg.actParam.data[1]
            self._animator:SetAttackLoopInterval(true, time)
        end
    else
        local modelParent = UIUtil.GetTrans(self.transform, "modelRes")
        if util.IsObjNull(modelParent) then
            return
        end
        local modelTr = UIUtil.GetChild(modelParent, 1)
        if util.IsObjNull(modelTr) then
            return
        end
        if not self.mapCfg or string.empty(self.mapCfg.act) then
            return
        end
        self._animator = gw_sand_animator_helper.new()
        self._animator:AddAnimator(modelTr)
        if self.mapCfg.actParam.count == 1 then
            if self._animator then
                self._animator:SetAttackLoopInterval(false)
            end
            return
        end
        if self._animator then
            local time = self.mapCfg.actParam.data[1]
            self._animator:SetAttackLoopInterval(true, time)
        end
    end
end

---@public 设置格子
function GWDecorateObject:SetGridPos(x, y)
    self:SetLayer(x, y)
    self.curGridX = x
    self.curGridY = y
    -- 获取该位置的建造状态
    local buildState = gw_home_grid_data.GetCanBuildGridStateForLord(x, y)
    if buildState == 0 or buildState == 3 then
        --如果是在城建城墙内
        if self.mapId and gw_home_decorate_const.GridSetTypeId[self.mapId] then
            --设置 格子标记
            self.preGridState = gw_home_grid_data.GetGridState(x, y)
            gw_home_grid_data.SetGridStateByBuilding(x, y, self.compId, 4)
            self.gridState = true
        end
    end
end

---@public 点击
function GWDecorateObject:OnClickGrid(names, gridX, gridY)
    local cfg = game_scheme:BuildMaincityMap_0(self.mapId)
    if not cfg then
        return
    end
    --如果有配置点击函数
    if not string.IsNullOrEmpty(cfg.clickFunStr) then
        local clickFun = gw_home_decorate_const.DecorateClickFunction[cfg.clickFunStr]
        if clickFun then
            clickFun(cfg)
            return
        end
    end
    --判断是否显示泡泡
    local cfg_util = require "cfg_util"
    local bubbleAppearID = cfg_util.StringToArray(cfg.bubbleAppearID, ";", "#")
    local bubbleAppearLen = util.get_len(bubbleAppearID)
    --local isShow = false
    --local selectId = -1
    --for i = 1, bubbleAppearLen do
    --    local bubbleAppearIdLen = util.get_len(bubbleAppearID[i])
    --    if bubbleAppearIdLen >= 2 then
    --        local eventType = tonumber(bubbleAppearID[i][1])
    --        local needEventId = tonumber(bubbleAppearID[i][2])
    --        local chapterDataEntity = nil
    --        if eventType == 3 then
    --            --严格来说3类型不应该进入这里，但还是做一下处理
    --        elseif eventType == 1 then
    --            chapterDataEntity = GWG.GWHomeMgr.chapterData
    --        else
    --            chapterDataEntity = GWG.GWHomeMgr.noviceChapterData
    --        end
    --        if chapterDataEntity.CheckJustPassEvent(needEventId) then
    --            isShow = true
    --            selectId = i
    --            break
    --        end
    --    end
    --end
    --if not isShow then
    --    return
    --end

    local langId = cfg.bubbleLang.data[self.selectLangIndex]--selectId - 1]
    self.selectLangIndex = self.selectLangIndex + 1
    if self.selectLangIndex >= bubbleAppearLen then
        self.selectLangIndex = 0
    end
    local iconId = cfg.bubbleID.data[0]
    local iconType = cfg.bubbleID.data[1]
    local compName = GWG.GWCompName.gw_home_comp_hud_head_bubble
    if self:GetComponent(compName) then
        self:RemoveComponent(compName)
    end
    if self.timer1 then
        util.RemoveDelayCall(self.timer1)
        self.timer1 = nil
    end
    local showBubbleFun = function()
        local id, comp = GWG.GWAdmin.GWHomeHudUtil.InitMoveHudComponent(compName, self.transform)
        local x = 0
        local y = 0
        local order = 0
        if cfg.bubblePos and cfg.bubblePos.count > 0 then
            if cfg.bubblePos.data[0] then
                x = cfg.bubblePos.data[0]
            end
            if cfg.bubblePos.data[1] then
                y = cfg.bubblePos.data[1]
            end
            if cfg.bubblePos.data[2] then
                order = cfg.bubblePos.data[2]
            end
        end
        comp:SetOffset(35 + x, 10 + y,order)
        comp:SetOffsetScale(1)
        comp:SetText(langId, iconId, iconType)
        self:AddComponent(compName, comp)
    end
    if cfg.bubbleParam.count <= 0 then
        return
    end
    if cfg.bubbleParam.data[0] > 0 then
        self.timer1 = util.DelayCallOnce(cfg.bubbleParam.data[0], showBubbleFun)
    else
        showBubbleFun()
    end

    self:AddCustomTimer(cfg.bubbleParam.data[0] + cfg.bubbleParam.data[1], function()
        local comp = self:GetComponent(compName)
        if comp then
            comp:PlayAnim("HideGuideBubble")
        end
        if self.timer1 then
            util.RemoveDelayCall(self.timer1)
            self.timer1 = nil
        end
        self.timer1 = util.DelayCallOnce(0.5, function()
            self:RemoveComponent(compName)
            if self.timer1 then
                util.RemoveDelayCall(self.timer1)
                self.timer1 = nil
            end
        end)
    end,self.mapId)
end

function GWDecorateObject:PlayAnim(aniName)
    if self._animator and aniName then
        self._animator:SetTrigger(aniName)
    end
end

---清楚数据
---@see override
function GWDecorateObject:ClearData()
    if self.gridState and self.mapCfg then
        gw_home_grid_data.SetGridStateByBuilding(self.mapCfg.x, self.mapCfg.y, 0, 4)
        self.gridState = nil
    end
    self.mapId = nil
    self.box = nil
    self.sortingGroup = nil
    if self.showEffect then
        GWG.GWHomeMgr.effectMgr.RemoveEffect(self.showEffect)
        self.showEffect = nil
    end
    if self.timer then
        util.RemoveDelayCall(self.timer)
        self.timer = nil
    end
    if self.timer1 then
        util.RemoveDelayCall(self.timer1)
        self.timer1 = nil
    end
    if self._animator then
        self._animator:SetAttackLoopInterval(false)
        self._animator:Dispose()
        self._animator = nil
    end

    if self.hybridEntity then
        EntityHybridUtility.DettachFormTransform(self.hybridEntity)
        EntityHybridUtility.DestroyEntity(self.hybridEntity)
        self.hybridEntity = nil
    end
end

function GWDecorateObject:SetLayer(gridX, gridY)
    if self.mapCfg and self.mapCfg.layer and self.mapCfg.layer.count > 0 and self.mapCfg.layer.data[0]  == 6  then
        self.sortingGroup = UIUtil.GetOrAddComponent(self.transform, typeof(SortingGroup))
        --设置对应的层级
        if util.IsObjNull(self.sortingGroup) then
            return
        end
        --强制新增sortgroup
        if self.mapCfg.layer.count > 1 then
            self.sortingGroup.sortingOrder = self.mapCfg.layer.data[1]
        else
            self.sortingGroup.sortingOrder = GWG.GWAdmin.HomeCommonUtil.GetCurSortingOrder(gridX, gridY)
        end
        return
    end
    --设置对应的层级
    if util.IsObjNull(self.sortingGroup) then
        return
    end
    if self.mapCfg and self.mapCfg.layer and self.mapCfg.layer.count > 1 then
        self.sortingGroup.sortingOrder = self.mapCfg.layer.data[1]
    else
        self.sortingGroup.sortingOrder = GWG.GWAdmin.HomeCommonUtil.GetCurSortingOrder(gridX, gridY)
    end
end

function GWDecorateObject:EventRefreshBubbleShow()
    self:CreateBubbleHud(self.mapId)
end

function GWDecorateObject:EventRefreshShow()
    local force_guide_system = require "force_guide_system"
    local guideCfg = force_guide_system.GetCurGuide()
    local bubbleShow = false
    local isShow = false
    if not guideCfg or guideCfg.guideId > 359 then
        isShow = GWG.GWAdmin.GWHomeCfgUtil.GetMapCityShow(self.mapId)
        if self.enableECS then
            EntityHybridUtility.SetActive(self.hybridEntity, isShow)
        end
        UIUtil.SetActive(self.gameObject, isShow)
        bubbleShow = isShow
    else
        if GWG.GWAdmin.GWHomeCfgUtil.GetItemIsFence(self.mapId) then
            --栅栏，不走这里进行显隐
        else
            isShow = GWG.GWAdmin.GWHomeCfgUtil.GetMapCityShow(self.mapId)
            if self.enableECS then
                EntityHybridUtility.SetActive(self.hybridEntity, isShow)
            end
            UIUtil.SetActive(self.gameObject, isShow)
            bubbleShow = isShow
        end
    end
    if isShow then
        if self.mapCfg then
            self:SetGridPos(self.mapCfg.x, self.mapCfg.y)
        end
    else
        if self.gridState and self.mapCfg then
            if self.preGridState then
                gw_home_grid_data.SetGridStateByBuilding(self.mapCfg.x, self.mapCfg.y, self.preGridState, 4)
            else
                gw_home_grid_data.SetGridStateByBuilding(self.mapCfg.x, self.mapCfg.y, 0, 4)
            end
            self.preGridState  = nil 
            self.gridState = nil
        end
    end
    if bubbleShow and self.mapCfg.conditionalHiding.count <= 0 then
        self:CreateCommonBubble()
    end
    if not bubbleShow then
        self:DisposeBubble()
    end
    self:ActivityRefreshShow()
end

---@public 活动刷新显示
function GWDecorateObject:ActivityRefreshShow()
    if not self.mapCfg then
        return
    end
    if not self.mapCfg.conditionalHiding then
        return
    end
    if self.mapCfg.conditionalHiding.count <= 0 then
        return
    end

    local showId = self.mapCfg.conditionalHiding.data[0]
    --根据活动id 开启
    local data = monsters_approaching_group_mgr.GetShowBossData()
    -- local activityOpen = monsters_approaching_group_mgr.CheckIsChangeBuilding()
    if not data then
        --活动未开启的状态下显示所有未配置活动id的
        if showId == -1 then
            local isShow = GWG.GWAdmin.GWHomeCfgUtil.GetMapCityShow(self.mapId)
            self:SetActive(isShow)
        elseif showId == 1 then
            self:SetActive(false)
            self:DisposeBubble()
        end
        return
    end
    
    local activityIdShow = false
    --活动id
    local actId = data.nActiveStateID
    local show = showId == 1
    for i = 1, self.mapCfg.conditionalHiding.count - 1 do
        --活动id = 配置id的时候 Attack
        if actId == self.mapCfg.conditionalHiding.data[i] then
            activityIdShow = true
            break
        end
    end
    if activityIdShow then
        self:SetActive(show)
        self:CreateCommonBubble(actId)
    else
        self:SetActive(not show)
    end
end

---@public 创建怪物攻城活动气泡
function GWDecorateObject:CreateActivityBubbleType(actId)
    self:DisposeBubble()
    local monstersApproachingCfg = game_scheme:MonstersApproaching_0(actId)
    if not monstersApproachingCfg then
        return
    end
    if not self.mapCfg or not self.mapCfg.shBubbleParam or self.mapCfg.shBubbleParam.count <= 0 then
        return
    end
    local bubbleType = self.mapCfg.shBubbleParam.data[0]
    if not bubbleType and bubbleType == 0 then
        return
    end
    local bubbleCfg = game_scheme:BubbleManager_0(bubbleType)
    local iconPath = bubbleCfg and bubbleCfg.icon
    if not iconPath then
        local rewardId = monstersApproachingCfg.passtiRewardID
        if not rewardId then
            return
        end
        local reward_mgr = require "reward_mgr"
        local dataList = reward_mgr.GetRewardGoodsList2(rewardId)
        if #dataList <= 0 then
            return
        end
        local itemId = dataList[1].id
        local itemCfg = game_scheme:Item_0(itemId)
        if not itemCfg then
            return
        end
        iconPath = itemCfg.icon
    end
    local clickFunc = function()
        self:OnClickGrid()
    end
    self.bubbleId = GWG.GWHomeMgr.bubbleMgr.BindBubbleEntity(nil, bubbleType, self.transform, clickFunc, nil);
    local offsetY = 0
    local offsetX = 0
    if self.mapCfg.shBubbleParam.count >= 2 then
        offsetY = self.mapCfg.shBubbleParam.data[1]
    end
    if self.mapCfg.offset.count >= 3 then
        offsetX = self.mapCfg.offset.data[0] * 100
    end
    --读取活动图片、偏移
    local data = { iconData = { icon = iconPath }, offsetData = { x = offsetX, y = offsetY } }
    GWG.GWHomeMgr.bubbleMgr.BubbleUpdateEntity(self.bubbleId, data);
end
---@public 创建通用气泡
function GWDecorateObject:CreateCommonBubble(actId)
    self:DisposeBubble()
    if not self.mapCfg or not self.mapCfg.shBubbleParam or self.mapCfg.shBubbleParam.count <= 0 or not self:CheckBubbleShowCondition() then
        return
    end
    local bubbleType = self.mapCfg.shBubbleParam.data[0]
    if not bubbleType and bubbleType == 0 then
        return
    end
    --如果是怪物工程气泡 直接用活动方法
    if bubbleType == gw_home_decorate_const.MonsterBubbleType then
        self:CreateActivityBubbleType(actId)
        return
    end
    local clickFunc = function()
        self:OnClickGrid()
    end
    self.bubbleId = GWG.GWHomeMgr.bubbleMgr.BindBubbleEntity(nil, bubbleType, self.transform, clickFunc, nil);
    local offsetY = 0
    local offsetX = 0
    if self.mapCfg.shBubbleParam.count >= 2 then
        offsetY = self.mapCfg.shBubbleParam.data[1]
    end
    if self.mapCfg.shBubbleParam.count >= 3 then
        offsetX = self.mapCfg.shBubbleParam.data[2]
    end
    --读取偏移
    local data = { offsetData = { x = offsetX, y = offsetY } }
    GWG.GWHomeMgr.bubbleMgr.BubbleUpdateEntity(self.bubbleId, data);
end

--@public 清除气泡
function GWDecorateObject:DisposeBubble()
    if self.bubbleId then
        GWG.GWHomeMgr.bubbleMgr.DisposeBubbleEntity(self.bubbleId)
        self.bubbleId = nil
    end
end

---@public 注册监听相关
---@see override
function GWDecorateObject:RegisterListener()
    self.HomeEventCenter = function(eventName, funcName, ...)
        if funcName and self[funcName] then
            self[funcName](self, ...)
        end
    end
    
    self.EventRefresh = function(eventName, eventId)
        self:EventRefreshBubbleShow()
        self:EventRefreshShow()
    end
    gw_ed.mgr:Register(gw_ed.GW_HOME_EVENT_UPDATE, self.EventRefresh)

    if self.mapCfg and self.mapCfg.RechargeGoods and self.mapCfg.RechargeGoods > 0 then
        self.RechargeRefresh = function(eventName, msg)
            self:EventRefreshShow()
        end
        event.Register(event.GW_REFRESH_RECHARGE_GOODS, self.RechargeRefresh)
    end

    if self.mapCfg and self.mapCfg.ModuleOpen and self.mapCfg.ModuleOpen > 0 then
        self.ModuleOpenRefresh = function()
            self:EventRefreshShow()
        end
        event.Register(event.UPDATE_MODULE_OPEN, self.ModuleOpenRefresh)
    end
    
    self.OnShowFence = function()
        if GWG.GWAdmin.GWHomeCfgUtil.GetItemIsFence(self.mapId) then
            local isShow = GWG.GWAdmin.GWHomeCfgUtil.GetMapCityShow(self.mapId)
            if isShow then
                if self.showEffect then
                    GWG.GWHomeMgr.effectMgr.RemoveEffect(self.showEffect)
                    self.showEffect = nil
                end
                local pos = { x = 0, y = -1.3, z = 0 }
                self.showEffect = GWG.GWHomeMgr.effectMgr.CreateEffects(GWConst.HomeEffectType.NoviceModelShowEffect, self.transform, pos)
            end
            if isShow then
                if self.timer then
                    util.RemoveDelayCall(self.timer)
                    self.timer = nil
                end
                self.timer = util.DelayCallOnce(0.2, function()
                    UIUtil.SetActive(self.gameObject, true)
                end)
            else
                UIUtil.SetActive(self.gameObject, false)
            end

        end
    end
    gw_ed.mgr:Register(gw_ed.GW_HOME_EVENT_SHOW_FENCE, self.OnShowFence)
    self.OnHideAllEffect = function()
        if self.showEffect then
            GWG.GWHomeMgr.effectMgr.RemoveEffect(self.showEffect)
            self.showEffect = nil
        end
    end
    gw_ed.mgr:Register(gw_ed.GW_HOME_EVENT_HIDE_EFFECT, self.OnHideAllEffect)
    --- 展示类型3的气泡
    self.ShowTypeThreeBubble = function()
        self:ShowType3Bubble(self.mapId)
    end
    gw_ed.mgr:Register(gw_ed.GW_HOME_SHOW_TYPE_THREE_BUBBLE, self.ShowTypeThreeBubble)
    self.UnlockMonsterAttack = function()
        self:ActivityRefreshShow()
    end 
    event.Register(monsters_approaching_group_define.UPDATE_MONSTER_BUBBLE_GROUP, self.UnlockMonsterAttack)
    unit_base_object.RegisterListener(self)
    self:BubbleShowTypeRegister()
    
end

---@public  注销监听相关
---@see override
function GWDecorateObject:UnregisterListener()
    gw_ed.mgr:Unregister(gw_ed.GW_HOME_EVENT_UPDATE, self.EventRefresh)
    gw_ed.mgr:Unregister(gw_ed.GW_HOME_EVENT_SHOW_FENCE, self.OnShowFence)
    gw_ed.mgr:Unregister(gw_ed.GW_HOME_EVENT_HIDE_EFFECT, self.OnHideAllEffect)
    gw_ed.mgr:Unregister(gw_ed.GW_HOME_SHOW_TYPE_THREE_BUBBLE, self.ShowTypeThreeBubble)
    event.Unregister(monsters_approaching_group_define.UPDATE_MONSTER_BUBBLE_GROUP, self.UnlockMonsterAttack)
    event.Unregister(event.GW_REFRESH_RECHARGE_GOODS, self.RechargeRefresh)
    event.Unregister(event.UPDATE_MODULE_OPEN, self.ModuleOpenRefresh)
    self:BubbleShowTypeUnregister()
    unit_base_object.UnregisterListener(self)
    
end

--region气泡展示类型相关
--bubbleShowType参数分割函数，通过#分割，第一个参数表示展示类型，后边参数表示参数
function GWDecorateObject:BubbleShowTypeSplit(bubbleShowType)
    if not bubbleShowType or string.IsNullOrEmpty(bubbleShowType) then
        return nil
    end 
    local bubbleShowTypeList = bubbleShowType
    local len= util.get_len(bubbleShowTypeList)
    if len <= 0 then
        return nil
    end
    local bubbleType = tonumber(bubbleShowTypeList[0])
    if not bubbleType then
        return nil
    end
    local bubbleParam = {}
    for i = 1, len do
        local param = tonumber(bubbleShowTypeList[i])
        if param then
            table.insert(bubbleParam, param)
        end
    end
    return bubbleType, bubbleParam
end

--在创建气泡的时候检测是否通过气泡展示条件
function GWDecorateObject:CheckBubbleShowCondition()
    local bubbleShowType = self.bubbleShowType
    if not bubbleShowType then 
        return true
    end
    if self.BubbleShowCondition[bubbleShowType] then
        return self.BubbleShowCondition[bubbleShowType](self)
    end
    return false
end

GWDecorateObject.BubbleShowCondition={
    [GWConst.EBubbleShowType.None]=function(self)
        return true
    end,
    [GWConst.EBubbleShowType.AreaUnlock]=function(self)
        return self:BubbleShowCondition_AreaUnlock()
    end,
}

function GWDecorateObject:BubbleShowCondition_AreaUnlock()
    local param = self.bubbleShowType_param
    if not param or #param <= 0 then
        return false
    end
    local areaId=param[1]
    local flag=gw_home_chapter_data.GetAreaFlag(areaId)
    local enCityFlag = GWG.GWAdmin.HomeCommonUtil.GetCityFlagState(flag, GWG.GWConst.enCityFlag.enCityFlag_AreaUnlock)
    local areaCompleteAll = GWG.GWAdmin.HomeCommonUtil.GetCityFlagState(flag, GWG.GWConst.enCityFlag.enCityFlag_AreaCompleteAll)
    return enCityFlag and not areaCompleteAll
end

---根据GWConst.EBubbleShowType，生成不同枚举情况下需要注册的函数监听
function GWDecorateObject:BubbleShowTypeRegister()
    if not self.mapCfg or not self.mapCfg.bubbleShowType then
        return
    end
    local bubbleShowType = self.bubbleShowType
    if self.BubbleShowType_Register[bubbleShowType] then
        self.BubbleShowType_Register[bubbleShowType](self)
    end
end

---根据气泡展示类型拓展，事件注册
GWDecorateObject.BubbleShowType_Register={
    [GWConst.EBubbleShowType.None]= nil,
    [GWConst.EBubbleShowType.AreaUnlock]=function(self)
        if self:CheckBubbleShowCondition() then
          return  
        end
        event:Register(gw_ed.GW_HOME_AREA_UNLOCK_EVENT, self)
    end,
}

function GWDecorateObject.GW_HOME_AREA_UNLOCK_EVENT(self)
    self:CreateCommonBubble()
end

function GWDecorateObject:BubbleShowTypeUnregister()
    if not self.mapCfg or not self.mapCfg.bubbleShowType then
        return
    end
    local bubbleShowType = self.bubbleShowType
    if self.BubbleShowType_Unregister[bubbleShowType] then
        self.BubbleShowType_Unregister[bubbleShowType](self)
    end
end

---根据气泡展示类型拓展，事件注册
GWDecorateObject.BubbleShowType_Unregister={
    [GWConst.EBubbleShowType.None]= nil,
    [GWConst.EBubbleShowType.AreaUnlock]=function(self)
        event:Unregister(gw_ed.GW_HOME_AREA_UNLOCK_EVENT,self)
    end,
}

--endregion

--- 弃置
---@see override
function GWDecorateObject:Dispose()
    self:ClearData()
    self:DisposeBubble()
    unit_base_object.Dispose(self)
end

--- 重置为了循环利用
---@see override
function GWDecorateObject:Recycle()
    self:ClearData()
    unit_base_object.Recycle(self)
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
return GWDecorateObject