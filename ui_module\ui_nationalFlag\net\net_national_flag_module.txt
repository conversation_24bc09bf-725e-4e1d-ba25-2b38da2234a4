local require = require
local ipairs = ipairs
local pairs = pairs
local table = table
local radar_data = require "radar_data"
local event = require "event"
local xManMsg_pb = require "xManMsg_pb"
local nationalFlag_pb = require "nationalFlag_pb"
local net = require "net"
local net_route = require "net_route"
local log = require "log"
local lang = require "lang"
local lua_pb = require "lua_pb"
local flow_text = require "flow_text"
local util = require "util"
local event_NationalFlag_define = require "event_NationalFlag_define"
local event_alliance_define = require "event_alliance_define"

module("net_national_flag_module")

-- 玩家登录客户端推送数据渠道PID，玩家登录IP
function MSG_LOGIN_NATIONAL_FLAG_REQ()
    local msg = nationalFlag_pb.TMSG_LOGIN_NATIONAL_FLAG_REQ()
    -- 给字段赋值
    msg.SKDChannelID = util.GetChannelTag()
    msg.countryID = require("national_flag_mgr").GetPlayerCountryIDByISO()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_LOGIN_NATIONAL_FLAG_REQ, msg)
end

-- 返回玩家登录国旗信息
function MSG_LOGIN_NATIONAL_FLAG_RSP(msg)
    if msg.errCode ~= 0 then
        return
    end
end

-- 请求切换国旗
function MSG_NATIONAL_FLAG_CHANGE_REQ(data)
    local msg = nationalFlag_pb.TMSG_NATIONAL_FLAG_CHANGE_REQ()
    -- 给字段赋值
    msg.flagType = data.flagType
    msg.nationalFlagID = data.countryID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_NATIONAL_FLAG_CHANGE_REQ, msg)
end

-- 返回玩家切换国旗
function MSG_NATIONAL_FLAG_CHANGE_RSP(msg)
    if msg.errCode ~= 0 then
        return
    end
    if msg.flagType == 2 then
        require("national_flag_mgr").SetAllianceNationalFlagInfoByServer(msg)
        event.Trigger(event_alliance_define.SET_ALLIANCE_NATIONAL_FLAG_DATA, msg)
    elseif msg.flagType == 1 then
        -- 发送 刷新玩家信息
        local roleID = require("player_mgr").GetPlayerRoleID()
        require("net_personalInfo").TMSG_ZONE_NEW_ROLEINFO_REQ(roleID)
    end
end

function TMSG_ROLD_NATIONAL_FLAG_NTF(msg)
    event.Trigger(event_NationalFlag_define.ON_NATIONAL_FLAG_REFRESH, msg)
end

local MessageTable = {
    --{ xManMsg_pb.MSG_LOGIN_NATIONAL_FLAG_REQ, MSG_LOGIN_NATIONAL_FLAG_REQ, nationalFlag_pb.TMSG_LOGIN_NATIONAL_FLAG_REQ },
    { xManMsg_pb.MSG_LOGIN_NATIONAL_FLAG_RSP, MSG_LOGIN_NATIONAL_FLAG_RSP, nationalFlag_pb.TMSG_LOGIN_NATIONAL_FLAG_RSP },
    --{ xManMsg_pb.MSG_NATIONAL_FLAG_CHANGE_REQ, MSG_NATIONAL_FLAG_CHANGE_REQ, nationalFlag_pb.TMSG_NATIONAL_FLAG_CHANGE_REQ },
    { xManMsg_pb.MSG_NATIONAL_FLAG_CHANGE_RSP, MSG_NATIONAL_FLAG_CHANGE_RSP, nationalFlag_pb.TMSG_NATIONAL_FLAG_CHANGE_RSP },
    { xManMsg_pb.MSG_ROLD_NATIONAL_FLAG_NTF, TMSG_ROLD_NATIONAL_FLAG_NTF, nationalFlag_pb.TMSG_ROLD_NATIONAL_FLAG_NTF },
}
net_route.RegisterMsgHandlers(MessageTable)


