---
--- Created by: yuannan.
--- DateTime: 2025/7/30.
--- Desc: sand_log.txt
---

local require = require
local debug = debug
local table = table
local select = select
local type = type
local pairs = pairs
local tostring = tostring
local pcall = pcall
local val = require "val"
local util = require "util"
local event = require "event"

local log = require "log"
local logger = require("logger").new("[sand_error]", 4)

local GWSandLog = {
    reportCache = {},
}

function GWSandLog.Log(msg, ...)
    local logContent = GWSandLog.GetLogContent(msg, ...)
    log.LogFormat("[sand_log]", logContent)
end

function GWSandLog.Warning(msg, ...)
    local logContent = GWSandLog.GetLogContent(msg, ...)
    log.LoginWarning("[sand_warning]", logContent)
end

function GWSandLog.Error(err, ...)
    local logContent = GWSandLog.GetLogContent(err, ...)
    logger.Warning0(logContent)
end

function GWSandLog.GetLogContent(err, ...)
    local parts = {}
    parts[#parts + 1] = (err == nil) and "nil" or (type(err) == "table" and "[table]" or tostring(err))
    for i = 1, select('#', ...) do
        local v = select(i, ...)
        if type(v) == "table" then
            parts[#parts + 1] = "[table]"
        else
            parts[#parts + 1] = (v == nil) and "nil" or tostring(v)
        end
    end
    local logContent = table.concat(parts, "\t")
    return logContent
end

function GWSandLog.ErrorAndReport(err, ...)
    local logContent = GWSandLog.GetLogContent(err, ...)
    logger.Warning0(logContent)

    if GWSandLog.AddReportCache(logContent) then
        local status, ret = pcall(function()
            util.AssetBundleManagerTrackEvent("lua_err", {
                type = "sand_scene_error",
                err = logContent,
                trackback = debug.traceback(),
            })
        end)
        if not status then
            logger.Warning0("TrackEvent failed:", ret)
        end
    end
end

-- 调试日志,需要开关打开之后才会显示
function GWSandLog.ExceptionLog(err, ...)
    local v = val.get("sw_sand_debug_log", 0)
    if v == 0 then
        return
    end

    local logContent = GWSandLog.GetLogContent(err, ...)
    GWSandLog.Error(logContent)
end

-- 调试日志,需要开关打开之后才会显示
function GWSandLog.ExceptionLog2(err, ...)
    local v = val.get("sw_sand_debug_log", 0)
    if v ~= 2 then
        return
    end
    local logContent = GWSandLog.GetLogContent(err, ...)
    GWSandLog.Error(logContent)
end

-- 调试日志,需要开关打开之后才会显示
function GWSandLog.GetLogLevel()
    local v = val.get("sw_sand_debug_log", 0)
    return v
end

-- 添加沙盘日志缓存
function GWSandLog.AddReportCache(logContent)
    if not GWSandLog.reportCache then
        GWSandLog.reportCache = {}
    end
    if #GWSandLog.reportCache > 127 then
        GWSandLog.CleanReportCache()
    end

    if GWSandLog.reportCache[logContent] then
        return false
    end

    GWSandLog.reportCache[logContent] = true
    return true
end

-- 清除沙盘日志缓存
function GWSandLog.CleanReportCache()
    GWSandLog.reportCache = {}
end

event.Register(event.USER_DATA_RESET, GWSandLog.CleanReportCache)

return GWSandLog

