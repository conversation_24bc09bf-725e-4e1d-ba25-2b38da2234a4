
local require = require
local table = table
local pairs = pairs
local string = string
local type = type
local typeof = typeof
local print = print 
local log  = require "log"
local lang = require "lang"
local util = require "util"
local asset_loader	= require "asset_loader"
local event = require "event"
local Sprite = CS.UnityEngine.Sprite
local Rect = CS.UnityEngine.Rect
local Vector2 = CS.UnityEngine.Vector2
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local pcall = pcall
module("oversea_res")  
local curResKey = nil
local b_test_oversea = false
util.RegisterConsole("oversea_res_test", 0, function(st)
    b_test_oversea = st == 1
end)

config={
    battle_huihe ="casualchannel/%s/ui/battleinui/battle_huihe.png",
    common_wz_new="casualchannel/%s/ui/returnplayer/common_wz_new.png",
    hdtc_chengzhang_zi ="casualchannel/%s/ui/chengzhanglibao_s/hdtc_chengzhang_zi.png",
    hdtc_yuekazi ="casualchannel/%s/ui/chengzhanglibao_s/hdtc_yuekazi.png",
    zaichong ="casualchannel/%s/ui/chongmuleichong/zaichong.png",
    xsbiaoqian ="casualchannel/%s/ui/common/xsbiaoqian.png",
    dytq_dabg_wz ="casualchannel/%s/ui/common2/dytq_dabg_wz.png",
    guaji_biaoti_pic ="casualchannel/%s/ui/common2/guaji_biaoti_pic.png",
    shengli ="casualchannel/%s/ui/common2/shengli.png",
    shibai ="casualchannel/%s/ui/common2/shibai.png",
    huizhang_banner_01 ="casualchannel/%s/ui/festival/activity/huizhang_banner_01.png",
    huizhang_banner_02 ="casualchannel/%s/ui/festival/activity/huizhang_banner_02.png",
    huizhang_libao_bg ="casualchannel/%s/ui/festival/activity/huizhang_libao_bg.png",
    mv_denglulibao_bg ="casualchannel/%s/ui/festival/activity/mv_denglulibao_bg.png",
    qixi_denglulibao_bg ="casualchannel/%s/ui/festival/activity/qixi_denglulibao_bg.png",
    topimage_1 ="casualchannel/%s/ui/festival/activity/topimage_1.png",
    yiji_libao_bg ="casualchannel/%s/ui/festival/activity/yiji_libao_bg.png",
    zhanjiabaodian_banner_02 ="casualchannel/%s/ui/festival/activity/zhanjiabaodian_banner_02.png",
    qiyuanlibao ="casualchannel/%s/ui/festivalsigil/qiyuanlibao.jpg",
    qiyuanrenwu ="casualchannel/%s/ui/festivalsigil/qiyuanrenwu.jpg",
    qiyuanzhouka ="casualchannel/%s/ui/festivalsigil/qiyuanzhouka.jpg",
    TCwenzi ="casualchannel/%s/ui/festivalsigil/tcwenzizhongwen.png",
    huanyingwenzi ="casualchannel/%s/ui/festivalsigil/huanyingwenzi.png",

    zhimingpozhan ="casualchannel/%s/ui/festivalsigil/zhimingpozhan.png",
    shouhuzhe ="casualchannel/%s/ui/festivalsigil/shouhuzhe.png",
    anci ="casualchannel/%s/ui/festivalsigil/anci.png",
    huoxing ="casualchannel/%s/ui/festivalsigil/huoxing.png",
    meimo ="casualchannel/%s/ui/festivalsigil/meimo.png",
    zheguang ="casualchannel/%s/ui/festivalsigil/zheguang.png",
    kuangzhan ="casualchannel/%s/ui/festivalsigil/kuangzhan.png",
    lengjing ="casualchannel/%s/ui/festivalsigil/lengjing.png",

    xshd_bg ="casualchannel/%s/ui/festival_activity2/xshd_bg.png",
    xshd_day_card ="casualchannel/%s/ui/festival_activity2/xshd_day_card.png",
    xshd_leichong_bg ="casualchannel/%s/ui/festival_activity2/xshd_leichong_bg.png",
    xshd_leichong_card ="casualchannel/%s/ui/festival_activity2/xshd_leichong_card.png",
    xshd_zuanshi_bg ="casualchannel/%s/ui/festival_activity2/xshd_zuanshi_bg.png",
    xshd_zuanshi_card ="casualchannel/%s/ui/festival_activity2/xshd_zuanshi_card.png",
    wmyjbanner2 ="casualchannel/%s/ui/festival_shengxiayiji/wmyjbanner2.png",
    wmyjbanner3 ="casualchannel/%s/ui/festival_shengxiayiji/wmyjbanner3.png",
    wmyjbanner4 ="casualchannel/%s/ui/festival_shengxiayiji/wmyjbanner4.png",
    herosummon1 ="casualchannel/%s/ui/herosummon/herosummon1.png",
    herosummon2 ="casualchannel/%s/ui/herosummon/herosummon2.png",
    getherointimes ="casualchannel/%s/ui/herosummon/getherointimes.png",
    getheronexttime ="casualchannel/%s/ui/herosummon/getheronexttime.png",
    zhzd_yxzh_text_ay ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_ay.png",
    zhzd_yxzh_text_bcyzyx ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_bcyzyx.png",
    zhzd_yxzh_text_czhbdhd ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_czhbdhd.png",
    zhzd_yxzh_text_jt ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_jt.png",
    zhzd_yxzh_text_lbc ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_lbc.png",
    zhzd_yxzh_text_rl ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_rl.png",
    zhzd_yxzh_text_sc ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_sc.png",
    zhzd_yxzh_text_sjhdsyyx ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_sjhdsyyx.png",
    zhzd_yxzh_text_sl ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_sl.png",
    zhzd_yxzh_text_smxk ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_smxk.png",
    zhzd_yxzh_text_sz ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_sz.png",
    zhzd_yxzh_text_yx ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_yx.png",
    zhzd_yxzh_text_zhlbdhd ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_zhlbdhd.png",
    zhzd_yxzh_text_zyyx ="casualchannel/%s/ui/herosummon/zhzd_yxzh_text_zyyx.png",
    fuli_czlb_ssj ="casualchannel/%s/ui/huodong/fuli_czlb_ssj.png",
    newpoptitlech3 ="casualchannel/%s/ui/huodong/newpoptitlech3.png",
    titlech3 ="casualchannel/%s/ui/huodong/titlech3.png",
    titlech7 ="casualchannel/%s/ui/huodong/titlech7.png",
    ws_text_10lc ="casualchannel/%s/ui/jewelryworkshopdraws/ws_text_10lc.png",
    jiesuan_sheng_pic ="casualchannel/%s/ui/jiesuan_ss/jiesuan_sheng_pic.png",
    lose_shibai ="casualchannel/%s/ui/jiesuan_ss/lose_shibai.png",
    victory_shengli ="casualchannel/%s/ui/jiesuan_ss/victory_shengli.png",
    huigong_zi_01 ="casualchannel/%s/ui/lianmeng/huigong_zi_01.png",
    huigong_zi_02 ="casualchannel/%s/ui/lianmeng/huigong_zi_02.png",
    fl_zhlb_32bei ="casualchannel/%s/ui/libao/fl_zhlb_32bei.png",
    fl_zhlb_36bei ="casualchannel/%s/ui/libao/fl_zhlb_36bei.png",
    fl_zhlb_40bei ="casualchannel/%s/ui/libao/fl_zhlb_40bei.png",
    libao_daylibao_title ="casualchannel/%s/ui/libao/libao_daylibao_title.png",
    libao_yuelibao_title ="casualchannel/%s/ui/libao/libao_yuelibao_title.png",
    libao_zhoulibao_title ="casualchannel/%s/ui/libao/libao_zhoulibao_title.png",
    zuanshistore_title_pic ="casualchannel/%s/ui/libao/zuanshistore_title_pic.png",
    bg28 ="casualchannel/%s/ui/loadingbg/bg28.png",
    bg29 ="casualchannel/%s/ui/loadingbg/bg29.png",
    bg31 ="casualchannel/%s/ui/loadingbg/bg31.png",
    gjtxztk_dbj_wz ="casualchannel/%s/ui/passport/gjtxztk_dbj_wz.png",
    yxtxz_banner_wz ="casualchannel/%s/ui/passport/yxtxz_banner_wz.png",
    yxtxz_banner_wz_jytxz ="casualchannel/%s/ui/passport/yxtxz_banner_wz_jytxz.png",
    swtxz_banner_wz  ="casualchannel/%s/ui/passport/swtxz_banner_wz.png",
    denglu_meishuzi ="casualchannel/%s/ui/qiandao_s/denglu_meishuzi.png",
    dlnewdengluhaoli ="casualchannel/%s/ui/libao/soldout.png",
    yangcheng_leijititle_pic ="casualchannel/%s/ui/rookiecontinuerecharge/yangcheng_leijititle_pic.png",
    yangcheng_meirititle_pic ="casualchannel/%s/ui/rookiecontinuerecharge/yangcheng_meirititle_pic.png",
    yingxiongshengxingpic ="casualchannel/%s/ui/rookietshengxingzhilu/yingxiongshengxingpic.png",
    zjm_7day_text_mtdljs ="casualchannel/%s/ui/sevenday/beijing/zjm_7day_text_mtdljs.png",
    zjm_7day_text_zdl2 ="casualchannel/%s/ui/sevenday/beijing/zjm_7day_text_zdl2.png",
    zjm_7day_text_zdl3 ="casualchannel/%s/ui/sevenday/beijing/zjm_7day_text_zdl3.png",
    zjm_7day_text_zdl4 ="casualchannel/%s/ui/sevenday/beijing/zjm_7day_text_zdl4.png",

    sgmb_login_bg = "casualchannel/%s/ui/fatekoi/sgmb_login_bg.png",
    
    ch_h65 ="casualchannel/%s/ui/sevenday/huode/ch_h65.png",
    ch_s10 ="casualchannel/%s/ui/sevenday/huode/ch_s10.png",

    --ch_h73 ="casualchannel/%s/ui/sevenday/huode/ch_h73.png",
    ch_h73 ="casualchannel/%s/ui/sevenday/qitianwenzi/ch_h73.png",
    --ch_h58 ="casualchannel/%s/ui/sevenday/huode/ch_h58.png",
    zjm_7day_juexing_nk = "casualchannel/%s/ui/sevenday/qitianwenzi/zjm_7day_juexing_nk.png",
    --ch_h73_2 ="casualchannel/%s/ui/sevenday/huode/ch_h73_2.png",
    zjm_7day_juexing_lkk = "casualchannel/%s/ui/sevenday/qitianwenzi/zjm_7day_juexing_lkk.png",
    
    ch_2 ="casualchannel/%s/ui/sevenday/qitianwenzi/ch_2.png",
    ch_3 ="casualchannel/%s/ui/sevenday/qitianwenzi/ch_3.png",
    ch_4 ="casualchannel/%s/ui/sevenday/qitianwenzi/ch_4.png",

    zjm_7day_baosong_lkk ="casualchannel/%s/ui/sevenday/qitianwenzi/zjm_7day_baosong_lkk.png",
    zjm_7day_baosong_nk ="casualchannel/%s/ui/sevenday/qitianwenzi/zjm_7day_baosong_nk.png",
    
    qiaozhan_dashi ="casualchannel/%s/ui/sevendaychallenge/qiaozhan_dashi.png",
    qiaozhan_jingying ="casualchannel/%s/ui/sevendaychallenge/qiaozhan_jingying.png",
    qiaozhan_xinbing ="casualchannel/%s/ui/sevendaychallenge/qiaozhan_xinbing.png",
    qitian_biaoti ="casualchannel/%s/ui/sevendaychallenge/qitian_biaoti.png",
    qitian_biaoti_02 ="casualchannel/%s/ui/sevendaychallenge/qitian_biaoti_02.png",
    qitian_biaoti_03 ="casualchannel/%s/ui/sevendaychallenge/qitian_biaoti_03.png",
    qitian_cike ="casualchannel/%s/ui/sevendaychallenge/qitian_cike.png",
    qitian_fashi ="casualchannel/%s/ui/sevendaychallenge/qitian_fashi.png",
    qitian_shenqi_tanchuang ="casualchannel/%s/ui/sevendaychallenge/qitian_shenqi_tanchuang.png",
    label1 ="casualchannel/%s/ui/shenqi/label1.png",
    label2 ="casualchannel/%s/ui/shenqi/label2.png",
    label3 ="casualchannel/%s/ui/shenqi/label3.png",
    perfect_zh ="casualchannel/%s/ui/shijieditu/perfect_zh.png",
    dlsl_meishuzi ="casualchannel/%s/ui/shilianchou_s/dlsl_meishuzi.png",
    shouchong_libao_zi ="casualchannel/%s/ui/shouchong_s/shouchong_libao_zi.png",
    shouchong_zi_01 ="casualchannel/%s/ui/shouchong_s/shouchong_zi_01.png",
    shouchong_zi_02 ="casualchannel/%s/ui/shouchong_s/shouchong_zi_02.png",
    shouchong_zi_03 ="casualchannel/%s/ui/shouchong_s/shouchong_zi_03.png",
    jjc_cqjbs_pop_text_wzzbs ="casualchannel/%s/ui/topgamepop/jjc_cqjbs_pop_text_wzzbs.png",
    jjc_wzzb_pop_text_wzzbs ="casualchannel/%s/ui/topgamepop/jjc_wzzb_pop_text_wzzbs.png",
    jjd_dfdj_pop_text_dfdj ="casualchannel/%s/ui/topgamepop/jjd_dfdj_pop_text_dfdj.png",
    wangzhebj ="casualchannel/%s/ui/topgamepop/wangzhebj.png",
    vip_a_01 ="casualchannel/%s/ui/vipzuanshi/vip_a_01.png",
    vip_a_02 ="casualchannel/%s/ui/vipzuanshi/vip_a_02.png",
    xyc_libao_zi ="casualchannel/%s/ui/wishlist/xyc_libao_zi.png",
    xyc_meishuzi ="casualchannel/%s/ui/wishlist/xyc_meishuzi.png",
    fenghuang_mingzi ="casualchannel/%s/ui/yingxionghuodong/fenghuang_mingzi.png",
    fenghuang_shilian ="casualchannel/%s/ui/yingxionghuodong/fenghuang_shilian.png",
    fenghuang_tuihui ="casualchannel/%s/ui/yingxionghuodong/fenghuang_tuihui.png",
    hd_tehui ="casualchannel/%s/ui/yingxionghuodong/hd_tehui.png",
    hd_yxl_huizhang ="casualchannel/%s/ui/yingxionghuodong/hd_yxl_huizhang.png",
    shilian_banner ="casualchannel/%s/ui/yingxionghuodong/shilian_banner.png",
    xyx_fenghuang_tanchuang_zi ="casualchannel/%s/ui/yingxionghuodong/xyx_fenghuang_tanchuang_zi.png",
    yitongguan ="casualchannel/%s/ui/yingxionghuodong/yitongguan.png",
    zhanjiabaodian_zjbdic_pic ="casualchannel/%s/ui/yingxionghuodong/zhanjiabaodian_zjbdic_pic.png",
    coreskill_cn ="casualchannel/%s/ui/yingxiong_s/coreskill_cn.png",
    czjj_25bei ="casualchannel/%s/ui/yuedujijin/czjj_25bei.png",
    czjj_28bei ="casualchannel/%s/ui/yuedujijin/czjj_28bei.png",
    czjj_30bei ="casualchannel/%s/ui/yuedujijin/czjj_30bei.png",
    hfp_jjtip = "casualchannel/%s/ui/yuedujijin/jjtip.png",
    yk_banner_wz ="casualchannel/%s/ui/yueka/yk_banner_wz.png",
    zhaohuanlibaozhongwen ="casualchannel/%s/ui/zhaohuanlibao/zhaohuanlibaozhongwen.png",
    zhaohuan_xinyingxiong ="casualchannel/%s/ui/zhaohuanlibao/zhaohuan_xinyingxiong.png",
    zhuoyuezhongwen = "casualchannel/%s/ui/huodong/zhuoyuezhongwen.png",
    qiyuandenglu = "casualchannel/%s/ui/festivalsigil/qiyuandenglu.jpg",
    biaotizhongwen ="casualchannel/%s/ui/huodong/biaotizhongwen.png",
    natali_mingzi="casualchannel/%s/ui/yingxionghuodong/natali_mingzi.png",
    nalita_shilian ="casualchannel/%s/ui/yingxionghuodong/nalita_shilian.png",
    jiesuan_fu_pic = "casualchannel/%s/ui/jiesuan_ss/jiesuan_fu_pic.png",
    pingjia_tanchuang_zi = "casualchannel/%s/ui/pingfentiaozhuan/pingjia_tanchuang_zi.png",
    xyx_xiongnv_name = "casualchannel/%s/ui/yingxionghuodong_farinelli/xyx_xiongnv_name.png",
    xyx_xiongnv_tanchuang_zi = "casualchannel/%s/ui/yingxionghuodong_farinelli/xyx_xiongnv_tanchuang_zi.png",
    xiongnv_shilian = "casualchannel/%s/ui/yingxionghuodong_farinelli/xiongnv_shilian.png",
    xyx_natali_tanchuang_zi = "casualchannel/%s/ui/yingxionghuodong/xyx_natali_tanchuang_zi.png",
    xyx_oulila_tanchuang_zi = "casualchannel/%s/ui/yingxionghuodong/xyx_oulila_tanchuang_zi.png",
    xyx_fuxi_tanchuang_zi = "casualchannel/%s/ui/yingxionghuodong/xyx_fuxi_tanchuang_zi.png",
    xyx_duhunjisi_tanchuang_zi = "casualchannel/%s/ui/yingxionghuodong/xyx_duhunjisi_tanchuang_zi.png",
    shouhuzhe  = "casualchannel/%s/ui/festivalsigil/shouhuzhezw.png",
    yitaosixing  = "casualchannel/%s/ui/festivalsigil/yitaosixing.png",
    xiandingpifu_zh ="casualchannel/%s/ui/yingxionghuodong/xiandingpifu_zh.png",
    
    sd_yingwenzi_01 = "casualchannel/%s/ui/christmas_2022.spritepack",
    ["art/effects/prefabs/ui/ui_ersheng_cn.prefab"] = "casualchannel/%s/art/effects/prefabs/ui/ui_ersheng_cn.prefab",
    ["art/effects/prefabs/ui/ui_sansheng_cn.prefab"]="casualchannel/%s/art/effects/prefabs/ui/ui_sansheng_cn.prefab",
    ["art/effects/prefabs/ui/ui_sisheng_cn.prefab"]="casualchannel/%s/art/effects/prefabs/ui/ui_sisheng_cn.prefab",
    ["art/effects/prefabs/ui/ui_wusheng_cn.prefab"]="casualchannel/%s/art/effects/prefabs/ui/ui_wusheng_cn.prefab",
    ["art/effects/prefabs/ui/ui_liusheng_cn.prefab"]="casualchannel/%s/art/effects/prefabs/ui/ui_liusheng_cn.prefab",
    qrj_libao_bg = "casualchannel/%s/ui/festival/activity/qrj_libao_bg.png",
    qrj_banner_zi = "casualchannel/%s/ui/festival/activity/qrj_zi.png",
    qrj_xin_zi = "casualchannel/%s/ui/festival/activity/qrj_xin_zi.png",
    -- 2023 春节活动
    cj_banner_02 = "casualchannel/%s/ui/festival_new_year_2023/cj_banner_02.png",
    cj_dalibao_zi = "casualchannel/%s/ui/festival_new_year_2023/cj_dalibao_zi.png",
    cj_yingchun_zi = "casualchannel/%s/ui/festival_new_year_2023/cj_yingchun_zi.png",
    cj_zhouka_zi = "casualchannel/%s/ui/festival_new_year_2023/cj_zhouka_zi.png",
    cj_zi_01 = "casualchannel/%s/ui/festival_new_year_2023/cj_zi_01.png",
    wy_zi_01 = "casualchannel/%s/ui/festival_laodongjie_2023/wy_zi_01.png",
    wy_zi_02 = "casualchannel/%s/ui/festival_laodongjie_2023/wy_zi_02.png",
    ldj_shougou_zi = "casualchannel/%s/ui/festival_laodongjie_2023/ldj_shougou_zi.png",

    xyx_qiri_libao_zi = "casualchannel/%s/ui/yingxionghuodong/xyx_qiri_libao_zi.png",
    ali_shilian = "casualchannel/%s/ui/yingxionghuodong_ali/ali_shilian.png",
    ali_qiri_zi = "casualchannel/%s/ui/yingxionghuodong_ali/ali_qiri_zi.png",
    xyx_ali_name = "casualchannel/%s/ui/yingxionghuodong_ali/xyx_ali_name.png",
    xyx_ali_tanchuang_zi = "casualchannel/%s/ui/yingxionghuodong_ali/xyx_ali_tanchuang_zi.png",

    --===========================================夏日活动start=======================================--
    xrj_7r_zi="casualchannel/%s/ui/xiarihuodong/xrj_7r_zi.png",
    xrwz_xsth="casualchannel/%s/ui/xiarihuodong/xrwz_xsth.png",
    xrj_bingo_zi="casualchannel/%s/ui/xiarihuodong/xrj_bingo_zi.png",
    --===========================================夏日活动end====================================--
    --===========================================新英雄活动start====================================--
    sibada_shilian="casualchannel/%s/ui/yingxionghuodong_sibada/sibada_shilian.png",
    xyx_sibada_name="casualchannel/%s/ui/yingxionghuodong_sibada/xyx_sibada_name.png",
    xyx_sibada_tanchuang_zi="casualchannel/%s/ui/yingxionghuodong_sibada/xyx_sibada_tanchuang_zi.png",
    xyx_suvada_tehui_zi="casualchannel/%s/ui/yingxionghuodong_sibada/xyx_suvada_tehui_zi.png",

    xyx_andelie_name="casualchannel/%s/ui/yingxionghuodong_andelie/xyx_andelie_name.png",
    xyx_andelie_tanchuang_zi="casualchannel/%s/ui/yingxionghuodong_andelie/xyx_andelie_tanchuang_zi.png",
    andelie_shilian="casualchannel/%s/ui/yingxionghuodong_andelie/andelie_shilian.png",
    andelie_qiri="casualchannel/%s/ui/yingxionghuodong_andelie/andelie_qiri.png",

    xln_text_lhmnxln="casualchannel/%s/ui/yingxionghuodong_xilina/xln_text_lhmnxln.png",
    xln_text_20shilian="casualchannel/%s/ui/yingxionghuodong_xilina/xln_text_20shilian.png",
    xln_text_qrhl="casualchannel/%s/ui/yingxionghuodong_xilina/xln_text_qrhl.png",
    xln_text_wcyxlhdmln="casualchannel/%s/ui/yingxionghuodong_xilina/xln_text_wcyxlhdmln.png",

    xyx_oulila_name = "casualchannel/%s/ui/yingxionghuodong_oulila/xyx_oulila_name.png",
    oll_text_20shilian = "casualchannel/%s/ui/yingxionghuodong_oulila/oll_text_20shilian.png",
    oll_text_qrhl = "casualchannel/%s/ui/yingxionghuodong_oulila/oll_text_qrhl.png",
    oll_text_wcyxlhdmln = "casualchannel/%s/ui/yingxionghuodong_oulila/oll_text_wcyxlhdmln.png",

    sfy_tehui="casualchannel/%s/ui/yingxionghuodong_suofeiya/sfy_tehui.png",
    suofeiya_shilian="casualchannel/%s/ui/yingxionghuodong_suofeiya/suofeiya_shilian.png",
    xyx_suofeiya_name="casualchannel/%s/ui/yingxionghuodong_suofeiya/xyx_suofeiya_name.png",
    xyx_suofeiya_tanchuang_zi="casualchannel/%s/ui/yingxionghuodong_suofeiya/xyx_suofeiya_tanchuang_zi.png",
    --===========================================新英雄活动end====================================--
    --===========================================年中大促start====================================--
    nzdc_zi_02="casualchannel/%s/ui/festival_nianzhongdacu_2023/nzdc_zi_02.png",
    wy_zi_htxr="casualchannel/%s/ui/festival_nianzhongdacu_2023/wy_zi_htxr.png",
    nzdc_thlibao_wz="casualchannel/%s/ui/festival_nianzhongdacu_2023/nzdc_thlibao_wz.png",
    --===========================================年中大促end====================================--
    --===========================================宠物活动start====================================--
    cwly_text_cwbingo="casualchannel/%s/ui/chongwu/cwly_text_cwbingo.png",
    --===========================================宠物活动end====================================--
    --===========================================秋日活动start====================================--
    qrhd_text_qrbingo = "casualchannel/%s/ui/festival_qiurihuodong_2023/qrhd_text_qrbingo.png",
    --===========================================秋日活动end====================================--
    --===========================================周年庆start====================================
    znq_tanchuang_zi = "casualchannel/%s/ui/anniversary/znq_tanchuang_zi.png",
    znq_huodong_banner = "casualchannel/%s/ui/anniversary/znq_huodong_banner.png",
    znq_fuzi = "casualchannel/%s/ui/anniversary/znq_fuzi.png",
    znq_pf_fagong = "casualchannel/%s/ui/anniversary/znq_pf_fagong.png",
    znq_pf_jianji = "casualchannel/%s/ui/anniversary/znq_pf_jianji.png",
    znq_shequ = "casualchannel/%s/ui/anniversary/znq_shequ.png",
    --===========================================周年庆end====================================
    --===========================================新英雄米拉娜start====================================
    mln_text_20shilian = "casualchannel/%s/ui/milana/mln_text_20shilian.png",
    mln_text_qrhl = "casualchannel/%s/ui/milana/mln_text_qrhl.png",
    mln_text_wcyxlhdmln = "casualchannel/%s/ui/milana/mln_text_wcyxlhdmln.png",
    mln_text_ymlsmln = "casualchannel/%s/ui/milana/mln_text_ymlsmln.png",
    --===========================================新英雄米拉娜end====================================
    skingift_name_fenghuang = "casualchannel/%s/ui/skinactivity/xyx_fenghuang_name_xd.png",
    xyx_fenghuang_name_xd = "casualchannel/%s/ui/skinactivity/xyx_fenghuang_name_xd.png",
    --==========================================心愿抽英雄沙漠之兴start============================
    xyc_shamozhixing_tanchuang_zi = "casualchannel/%s/ui/yingxionghuodong/xyc_shamozhixing_tanchuang_zi.png",
    --==========================================心愿抽英雄沙漠之兴end============================
    
    ----伏羲
    xyx_fuxi_name = "casualchannel/%s/ui/yingxionghuodong_fuxi/xyx_fuxi_name.png",
    xyx_fuxi_tanchuang_zi1 = "casualchannel/%s/ui/yingxionghuodong_fuxi/xyx_fuxi_tanchuang_zi1.png",
    --==========================================双旦皮肤礼包start============================
    sd_lunhuidiji_name = "casualchannel/%s/ui/festival_shuangdan_2023/sd_lunhuidiji_name.png",
    sd_linhunmonv_name = "casualchannel/%s/ui/festival_shuangdan_2023/sd_linhunmonv_name.png",


    --庇佑之灵
    xyx_aeln_tanchuang_zi = "casualchannel/%s/ui/yingxionghuodong/xyx_aeln_tanchuang_zi.png",

    --电龙
    baolong_pifu_zi = "casualchannel/%s/ui/yingxiongpifu_s/baolong_pifu_zi.png",
    --庇佑之灵
    sd_nalita_name = "casualchannel/%s/ui/yingxionghuodong/sd_nalita_name.png",

    --===========================================新英雄大荒蛮神start====================================
    xyx_dh_name = "casualchannel/%s/ui/yingxionghuodong_dahuang/xyx_dh_name.png",
    xyx_dh_tanchuang_zi = "casualchannel/%s/ui/yingxionghuodong_dahuang/xyx_dh_tanchuang_zi.png",
    xyx_shilian = "casualchannel/%s/ui/yingxionghuodong/xyx_shilian.png",
    xyx_qiri_zi = "casualchannel/%s/ui/yingxionghuodong/xyx_qiri_zi.png",
    --===========================================新英雄大荒蛮神end====================================

    liaotian_help02 = "casualchannel/%s/ui/common/liaotian_help02.png",
    allhero = "casualchannel/%s/ui/common/allhero.png",
    skcy_pic_hot = "casualchannel/%s/ui/common/skcy_pic_hot.png",

    -- 司徒霸主
    situbazhu_name = "casualchannel/%s/ui/yingxionghuodong_situbazhu/situbazhu_name.png",
    situbazhu_desc = "casualchannel/%s/ui/yingxionghuodong_situbazhu/situbazhu_desc.png",

    binggotitle = "casualchannel/%s/ui/nvshenjie2023/binggotitle.png",
    chunrixiehou = "casualchannel/%s/ui/nvshenjie2023/chunrixiehou.png",

    -- 莫妮卡
    xyx_monika_name = "casualchannel/%s/ui/yingxionghuodong_monika/xyx_monika_name.png",
    xyx_tangchuang_monika_zi = "casualchannel/%s/ui/yingxionghuodong_monika/xyx_tangchuang_monika_zi.png",
    
    --萤火-巫女
    shouchong_wunv = "casualchannel/%s/ui/yingxionghuodong/shouchong_wunv.png",
    --萤火-梦蝶
    yinghuo_name = "casualchannel/%s/ui/pifutanchuang/yinghuo_pf_zi.png", 
    --索菲亚皮肤
    cj_sfy_name = "casualchannel/%s/ui/yingxionghuodong/cj_sfy_name.png",
    --虚空神娥
    xyx_ysed_tanchuang_zi = "casualchannel/%s/ui/yingxionghuodong/xyx_ysed_tanchuang_zi.png",
    xyx_yesd_tanchuang_zi = "casualchannel/%s/ui/yingxionghuodong/xyx_yesd_tanchuang_zi.png",

    --英雄试用
    sy_xyx_zi = "casualchannel/%s/ui/yingxiongshiyong/sy_xyx_zi.png",
    sy_xyx_zi_02 = "casualchannel/%s/ui/yingxiongshiyong/sy_xyx_zi_02.png",
    
    --英雄特惠
    hero_preferential = "casualchannel/%s/ui/yingxionghuodong/hero_preferential.png",
    --英雄挑战
    hero_battle = "casualchannel/%s/ui/yingxionghuodong/hero_battle.png",
    yyx_arlnpf_sjzi = "casualchannel/%s/ui/yingxionghuodong_common/yyx_arlnpf_sjzi.png",
	--青瓷龙拳
    xyx_longquan_name = "casualchannel/%s/ui/yingxionghuodong/xyx_longquan_name.png",
    xyx_lq_tanchuang_zi = "casualchannel/%s/ui/yingxionghuodong/xyx_lq_tanchuang_zi.png",
    --英雄试用
    sy_xyx_zi = "casualchannel/%s/ui/yingxiongshiyong/sy_xyx_zi.png",
    sy_xyx_zi_02 = "casualchannel/%s/ui/yingxiongshiyong/sy_xyx_zi_02.png",

    sold_out="casualchannel/%s/ui/libao/soldout.png",
    
    ---骁羽武神
    xyx_nvwushen_name = "casualchannel/%s/ui/yingxionghuodong_nvwushen/xyx_nvwushen_name.png",
    xyx_nvwushen_tanchuang_zi1 = "casualchannel/%s/ui/yingxionghuodong_nvwushen/xyx_nvwushen_tanchuang_zi1.png",
    
    --大荒蛮神皮肤
    znq_dh_pf_name = "casualchannel/%s/ui/yingxionghuodong/znq_dh_pf_name.png",
    hd_yxl_huizhang_newplayer="casualchannel/%s/ui/newreturnplayer/hd_yxl_huizhang_newplayer.png",

    --------XMan------------------
    --招募
    yingxiong_txt_zhnbdhdsjyx = "casualchannel/%s/ui/gw/summon/yingXiong_txt_zhnbdhdsjyx.png",
    yingxiong_txt_zhnbdhdsjxcz = "casualchannel/%s/ui/gw/summon/yingXiong_txt_zhnbdhdsjxcz.png",
}

oversealang = {
    [lang.JA] = 1,
    [lang.KR] = 2,
    [lang.FR] = 3,
    [lang.FA] = 4,
    [lang.TH] = 5,
    [lang.DE] = 6,
    [lang.FR] = 7,
    [lang.ES] = 8,
    [lang.PO] = 9,
    [lang.IND] = 10,
    [lang.TR] = 11,
    [lang.IT] = 12,
    [lang.VI] = 13,
    [lang.AR]=14,
}
--根据语言设置组件属性
function SetTextParam(text,langtypes,extern)
    if text==nil or util.IsObjNull(text) then
        return
    end
    local setParam = function(langtype,extern)
        if langtype == lang.USE_LANG then
            if extern and type(extern)=="table" then
                for i,v in pairs(extern)do
                    if not pcall(function()text[i]=v end) then
                        log.Warning(text,"没有属性",i,"=",v)
                    end
                end
            end
        end
    end
    if type(langtypes)=="table" then
        for i,v in pairs(langtypes)do
            setParam(v,extern)
        end
    else
        setParam(langtypes,extern)
    end
end

function GetResPath(res,isEnCnUseOverSea)
    if(res==nil or type(res)~="string")then
        log.Warning("res = nil")
        return
    end
    local res = string.lower(res)
    if res  and config[res] then
        if b_test_oversea then
            curResKey = lang.JA --lang.USE_LANG
        else
            curResKey = lang.USE_LANG

        end

        --土耳其 意大利用英语资源
        if lang.USE_LANG == lang.TR or lang.USE_LANG == lang.IT then
            if isEnCnUseOverSea then
                curResKey = "en"
                return string.format(config[res],curResKey)
            else
                return nil
            end
        end
        
        if curResKey and oversealang[curResKey] then
            if curResKey == lang.JA then
                curResKey = "ja" --使用原来文件夹名
            end
            --print(string.format(config[res],curResKey))
            return string.format(config[res],curResKey)
        elseif lang.USE_LANG == lang.EN and isEnCnUseOverSea then
            curResKey = "en"
            return string.format(config[res],curResKey)
        elseif lang.USE_LANG == lang.ZH and isEnCnUseOverSea then
            curResKey = "zh"
            return string.format(config[res],curResKey)
        end

    end
end

---判断是否多语言并且有这个多语言资源
---@param resKey string 这个是这个脚本config这个table对应的key，也是填写在配置表里面的
---@param isEnCnUseOverSea boolean isEnCnUseOverSea中英文是否也走oversea流程
---@return boolean true表示是多语言并且拥有这个对语言对应的资源图片
function IsOverSeaLangAndHaveRes(resKey, isEnCnUseOverSea)
    if ISOverSeaLang(isEnCnUseOverSea) then
        if not string.empty(resKey) then
            local abName = GetResPath(resKey, isEnCnUseOverSea)
            if not string.empty(abName) then
                return util.IsAssetBundlExist(abName)--只有有多语言图片资源的才算，不然还是返回false
            end
        end
    end
    return false
end

function ISOverSeaLang(isEnCnUseOverSea)
    local uselang
    if b_test_oversea then
        uselang = lang.JA --lang.USE_LANG
    else
        uselang = lang.USE_LANG
        if uselang == lang.TR or uselang == lang.IT then
            uselang = lang.EN 
        end
    end
    if uselang and oversealang[uselang] then
        return true
    elseif uselang and (uselang == lang.ZH or uselang == lang.EN) and isEnCnUseOverSea then
        return true
    end
    return false
end

function  IsUseOther(res)
    if ISOverSeaLang() and GetResPath(res) then
        return  true
    end
end

Loadercallbacks = {}
loaders = {}
--参数依次为 图片资源地址、loadKey,callback,是否是sprite精灵默认是,isEnCnUseOverSea中英文是否也走oversea流程
function LoadSprite(resPathStr,loadkey,callback,isNotSp,isEnCnUseOverSea)
    local resPath = GetResPath(resPathStr,isEnCnUseOverSea)
    if not resPath or not util.IsAssetBundlExist(resPath) then
        return true
    end
    if ISOverSeaLang(isEnCnUseOverSea) and resPath then
        local clist = Loadercallbacks[resPath] or {}
        table.insert(clist,callback)
        Loadercallbacks[resPath] = clist
        loaders[loadkey] = loaders[loadkey] or {}
        local loader = loaders[loadkey][resPath] or asset_loader(resPath, resPath)
        loaders[loadkey][resPath] = loader
        local r = resPath
        local key = loadkey
        loader:load(function (obj)
            if obj and obj.asset then
                local ModelPrefab = obj.asset
                if not isNotSp then
                    local sprite = Sprite.Create(ModelPrefab,Rect(0,0,ModelPrefab.width,ModelPrefab.height),Vector2(0.5,0.5))
                    if sprite then
                        local list = Loadercallbacks[r] or {}
                        Loadercallbacks[r] = nil
                        if list then
                            for rr, cb in pairs(list) do
                                if cb then
                                    cb(sprite)
                                end
                            end
                        end

                    end
                else
                    local list = Loadercallbacks[r] or {}
                    Loadercallbacks[r] = nil
                    if list then
                        for rr, cb in pairs(list) do
                            if cb then
                                cb(ModelPrefab)
                            end
                        end
                    end

                end
            end
        end,true)
    else
        return true
    end
end

--中英文是否走oversea流程
function ChangeLangResNew(imageName,moudleName,imageRoot,isEnCnUseOverSea, cb)
    local callBack = function(asset)
        if not util.IsObjNull(imageRoot) then
            imageRoot.enabled = false
            if asset then
                --加载多语言图片成功后的回调
                imageRoot.sprite = asset
                imageRoot.enabled = true
                imageRoot:SetNativeSize()
                if cb then cb(imageRoot) end
            end
        end
    end
    return LoadSprite(imageName,moudleName,callBack,nil,isEnCnUseOverSea)
end

--仅适用节点挂SpriteSwitcher组件用于显示中英文，其他语言用海外资源加载方式
function ChangeLangRes(imageName,moudleName,imageRoot)
    if imageName and moudleName and not util.IsObjNull(imageRoot) then
        if lang.USE_LANG == lang.ZH or lang.USE_LANG == lang.EN or lang.USE_LANG == lang.TR or lang.USE_LANG == lang.IT then
            local sw = imageRoot.gameObject:GetComponent(typeof(SpriteSwitcher))
            if not util.IsObjNull(sw) then
                local index = lang.USE_LANG == lang.ZH and 0 or 1
                imageRoot.enabled = true
                sw:Switch(index)
                imageRoot:SetNativeSize()
            end
        else
            local callBack = function(asset)
                if not util.IsObjNull(imageRoot) then
                    if asset then
                        --加载多语言图片成功后的回调
                        imageRoot.sprite = asset
                        imageRoot.enabled = true
                        imageRoot:SetNativeSize()
                    end
                end
            end
            LoadSprite(imageName,"moudleName",callBack)
        end

    end
end

function DisposeLoader(loadkey)
    if loaders[loadkey]  then
        for k,v in pairs(loaders[loadkey]) do
            if v and v.Dispose then
                v:Dispose()
            end
        end
        loaders[loadkey] = nil
    end
end

function  CheckCloseLoader(evenName,moduleName)
    if moduleName then
        DisposeLoader(moduleName)
    end
end
event.Register(event.UI_MODULE_PREPARE_CLOSE, CheckCloseLoader)

function DisposeAll()
    for k ,v in pairs(loaders) do
        if v and v.Dispose then
            v:Dispose()
            v=nil
        end
    end
    Loadercallbacks = {}
    loaders = {}
end

event.Register(event.SCENE_DESTROY, DisposeAll)