---@class kingshot_scene_mgr : fusion_mgrbase
---@field lifeScope kingshot_lifescope
local mgr = bc_Class("kingshot_scene_mgr", Fusion.MgrBase)
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
---@type kingshot_res_mgr
mgr.resMgr = nil
---@type fusion_mono
mgr.mono = nil
---@type kingshot_ui_mgr
mgr.uiMgr = nil

mgr.DataSrc = nil
mgr.LevelRoot = nil
mgr.PoolParent = nil
mgr.NavMesh = nil
---@type kingshot_player
mgr.playerCtrl = nil
---@type kingshot_camera
mgr.cameraCtrl = nil
---@type kingshot_team
mgr.teamCtrl = nil
---@type kingshot_prop
mgr.propCtrl = nil
---@type kingshot_building
mgr.buildingCtrl = nil
---@type kingshot_soldier
mgr.soldierCtrl = nil

mgr.readyFlag = nil
---@type fusion_bindable boolean是否在战斗中
mgr.BattleFlagBind = nil
---@type fusion_bindable number计时器
mgr.TimerBind = nil
---@type fusion_bindable number计时器秒数
mgr.TimeSecondBind = nil

-- 最大游戏时长（秒），超过则自动结束并结算
mgr.GameMaxTime = 120


---@type kingshot_enemyData[] 记录随时间生成的怪物
mgr.TeamDatasByTime = nil
---@type kingshot_enemyData[] 记录随时间生成的BOSS
mgr.BossDatasByTime = nil
---@type kingshot_enemyData[] 记录固定位置生成的怪物
mgr.TeamDatasByPos = nil
---@type kingshot_playerData 玩家数据
mgr.PlayerData = nil
---@type kingshot_propData[] 记录随时间生成的道具
mgr.PropDatasByTime = nil
---@type kingshot_propData[] 记录固定位置生成的道具
mgr.PropDatasByPos = nil
---@type kingshot_soldierData[] 记录固定位置生成的士兵
mgr.SoldierDatasByPos = nil
---@type fusion_bindable 任务数量计数
mgr.TaskNumBind = nil
---@type number 任务数量最大值
mgr.TaskNumMax = nil
---@type fusion_bindable 金数量计数
mgr.CoinNumBind = nil
--怪物的基地
mgr.enemyBaseDatasByPos = nil
--怪物的行走路径数组
mgr.enemyPathArr = nil
--怪物的行走路径GO数组
mgr.enemyPathGOArr = nil
--怪物的烘焙路径数据
mgr.enemyBakedPathArr = nil
---@type kingshot_buildingSkillData[] 建筑物技能数组
mgr.buildingSkillArr = nil

---@type kingshot_buildingData[] 记录生成的建筑
mgr.BuildingDatasByPos = nil
---@type kingshot_buildingData[] 记录主城建筑的skill
mgr.BuildingDatasBySkill = nil
--主基地的位置
mgr.homePos = nil
--敌方出生点浮空图标数组
mgr.enemyFloatingIcons = nil

mgr.tween_Win = nil

--游戏空场景
mgr.emptyScene = nil

mgr.enemyBossNumBind = nil
mgr.bossTimerBind = nil

function mgr:Init(lifeScope)
    self.lifeScope = lifeScope
    self.resMgr = self.lifeScope:GetMgr("kingshot_res_mgr")
    self.mono = self.lifeScope:GetMgr("fusion_mono")
    self.uiMgr = self.lifeScope:GetMgr("kingshot_ui_mgr")
    self.BattleFlagBind = require("fusion_bindable").New(false)
    self.TimerBind = require("fusion_bindable").New(0)
    self.TimeSecondBind = require("fusion_bindable").New(-1)
    self.TaskNumBind = require("fusion_bindable").New(0)
    self.CoinNumBind = require("fusion_bindable").New(0)
    -- 记录所有绑定以便统一注销，避免闭包持有mgr导致内存泄漏
    self._bind_unregs = {}

    local unreg
    unreg = self.TaskNumBind:Register(function(value)
        --任务目标全部消灭，胜利
        if self.readyFlag and self.BattleFlagBind.value and value < 1 then
            cysoldierssortie_DelayCallOnce(1, function()
                            self:GameOver(true)
            end)
        end
    end)
    table.insert(self._bind_unregs, unreg)

    unreg = self.CoinNumBind:Register(function(value)
        --Fusion.Error("coin change ",value)
    end)
    table.insert(self._bind_unregs, unreg)

    self.enemyBossNumBind = require("fusion_bindable").New(0)
    self.bossTimerBind = require("fusion_bindable").New(0)
end

function mgr:Ready()
    self.readyFlag = false
    self:InitLevelData()
    local neeGameEntry = KingShot_Define.CS.GameObject.Instantiate(self.resMgr.NeeGameEntryPrefab)
    neeGameEntry.transform:SetParent(self.mono.globalMonoGO.transform)
    neeGameEntry.transform.localPosition = KingShot_Define.CacheVector3.Zero
    neeGameEntry.transform.localScale = KingShot_Define.CacheVector3.One
    --通过luaMono来设置luaclass，GameLuaBehavior之间的绑定
    KingShot_Define.CS.NeeGame.Instance.createLuaComponentDel = function(go)
        local luaMono = go:GetComponent(KingShot_Define.TypeOf.LuaMono)
        local src = luaMono.luaBehaviour
        local str = luaMono.luaName
        local tempClass = require(str).New(luaMono, luaMono.referCol, luaMono.luaData)
        luaMono.luaComp = tempClass
        tempClass.luaMono = luaMono
        if luaMono.isStaticType then
            cysoldierssortie_AddMgr(luaMono.luaName, luaMono.luaComp)
        end
        src.enabled = true
        src:BindLuaTable(tempClass)
        if luaMono.hasLuaMonoEvent then
            local comps = luaMono:GetComponents(KingShot_Define.TypeOf.LuaMonoEvent)
            for i = 0, comps.Length - 1 do
                comps[i]:Bind(luaMono)
            end
        end
    end
    local mainScene = KingShot_Define.CS.GameObject.Instantiate(self.resMgr.MainScenePrefab)
    mainScene.transform:SetParent(self.mono.globalMonoGO.transform)
    mainScene.transform.localPosition = KingShot_Define.CacheVector3.Zero
    mainScene.transform.localScale = KingShot_Define.CacheVector3.One
    self.PoolParent = KingShot_Define.CS.GameObject("PoolParent").transform
    self.PoolParent:SetParent(self.mono.globalMonoGO.transform)
    self.PoolParent.localPosition = KingShot_Define.CacheVector3.Zero
    self.PoolParent.localScale = KingShot_Define.CacheVector3.One

    self.DataSrc = {}
    local neeRefer = mainScene:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)
    self.LevelRoot = self.DataSrc.LevelRoot

    if cysoldierssortie_KingShot_HOME then
        self.DataSrc.mainCam.gameObject:SetActive(false)
    else
        self.DataSrc.mainCamHome.gameObject:SetActive(false)
    end

    --生成场景
    self.emptyScene = KingShot_Define.CS.GameObject.Instantiate(self.resMgr.ScenePrefab)
    self.emptyScene.transform:SetParent(self.LevelRoot)
    self.emptyScene.transform.localPosition = KingShot_Define.CacheVector3.Zero
    self.emptyScene.transform.localScale = KingShot_Define.CacheVector3.One

    self.SceneDataSrc = {}
    local sceneRefer = self.emptyScene:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
    sceneRefer:Bind(self.SceneDataSrc)
    self.NavMesh = KingShot_Define.NavMesh.AddNavMeshData(self.resMgr.SceneNavMesh)
    --添加怪物路径
    self.enemyPathGOArr = {}
    for _,v in ipairs(self.enemyPathArr) do
        local path = self.resMgr.PathPrefabArr[v.PathName]
        if path ~= nil then
            local pathGO = KingShot_Define.CS.GameObject.Instantiate(path)
            pathGO.transform:SetParent(self.LevelRoot)
            pathGO.transform.localPosition =  KingShot_Define.CS.Vector3(v.Pos.x, 0.1, v.Pos.z)
            pathGO.transform.localScale = KingShot_Define.CacheVector3.One
            pathGO.gameObject:SetActive(false)
            self.enemyPathGOArr[v.PathName] = pathGO
        else
            Fusion.Error("预加载的怪物路径 is nil 检查下level表",v.PathName)
        end
    end
    --self.bakedPrefab = KingShot_Define.CS.GameObject.Instantiate(self.resMgr.PathPrefabArr["1_a"])
    --self.bakedPrefab.transform:SetParent(self.LevelRoot)
    --self.bakedPrefab.transform.localPosition =  KingShot_Define.CS.Vector3(0, 0.1, 0)
    --self.bakedPrefab.transform.localScale = KingShot_Define.CacheVector3.One
    --self.bakedPrefab.gameObject:SetActive(false)
    self.enemyBakedPathArr = {}
    for _,v in pairs(self.enemyPathGOArr) do
        local data = {
            BakedPath = v:GetComponent(KingShot_Define.TypeOf.BakedPath),
            PathName = _
        }
        self.enemyBakedPathArr[#self.enemyBakedPathArr + 1] = data
    end

    --添加怪物基地显示
    for i = 1, #self.enemyBaseDatasByPos do
        local data = self.enemyBaseDatasByPos[i]
        local baseGO = KingShot_Define.CS.GameObject.Instantiate(self.resMgr.monsterBaseEffect)
        --强制设置y是0.1
        data.Pos.y = 0.1
        baseGO.transform:SetParent(self.LevelRoot)
        baseGO.transform.localPosition = data.Pos
        baseGO.transform.localScale = { x = 0.7, y = 0.7, z = 0.7 }
        local Fx = baseGO:GetComponent(KingShot_Define.TypeOf.ParticleSystem)
        Fx:Play()
        --baseGO.transform.localScale = KingShot_Define.CacheVector3.One
    end

    --添加敌方出生点浮空图标
    self:CreateEnemyFloatingIcons()
    self:InitEnv()
    --注册出战按钮事件
    self._onFight = function()
        self.playerCtrl:ShowSelectHeroUI(false)
        self.playerCtrl:SlotToTargetPoint()
        for pathName, v in pairs(self.enemyPathGOArr) do
            v.gameObject:SetActive(true)
            local HidePath = self.resMgr.CurLvConfig.HidePath
            if HidePath and #HidePath > 0 then
                local isHidden = false
                for _, path in pairs(HidePath) do
                    if path == pathName then
                        isHidden = true
                        break
                    end
                end
                if isHidden then
                    v.gameObject:SetActive(false)
                end
            end
        end

        --显示敌方浮空图标
        self:SetEnemyFloatingIconsVisible(true)
        --self.bakedPrefab.gameObject:SetActive(true)
        --self.playerCtrl:SlotToTargetPoint()
        self.playerCtrl:CalculateColliderSize()
        self.cameraCtrl:ReadyToBattle(function()
            KingShot_Define.minigame_mgr.SetStartGame()
            self.BattleFlagBind:SetValue(true)
        end)
    end
    KingShot_Define.event.Register(KingShot_Define.event.SOLDIER_FIGHT, self._onFight)

    self.playerCtrl = require("kingshot_player").New(self, self.DataSrc.Player)
    local isSpecial = false
    if self.resMgr.CurLvConfig.Map == "scene_4" then
        isSpecial = false
    end

    if cysoldierssortie_KingShot_HOME or isSpecial then
        self.cameraCtrl = require("kingshot_camera_home").New(self)
    else
        self.cameraCtrl = require("kingshot_camera").New(self)
    end
    self.teamCtrl = require("kingshot_team").New(self, self.resMgr)
    self.propCtrl = require("kingshot_prop").New(self, self.resMgr)
    if cysoldierssortie_KingShot_HOME then
        self.buildingCtrl = require("kingshot_building_home").New(self, self.resMgr)
    else
        self.buildingCtrl = require("kingshot_building").New(self, self.resMgr)
    end
    self.soldierCtrl = require("kingshot_soldier").New(self, self.resMgr)

end

-- 获取建筑资源路径
function mgr:GetBuildingAssetPath(buildingId, level, flag)
    -- 参考gw_home_entity_gather中的GetAssetPath实现
    local entityGather = require("gw_home_entity_gather")
    if entityGather and entityGather[GWG.GWConst.EHomeEntityType.Building] then
        local gather = entityGather[GWG.GWConst.EHomeEntityType.Building]
        if gather and gather.GetAssetPath then
            return gather.GetAssetPath(buildingId, level, flag)
        end
    end

    -- 如果无法获取正确路径，使用默认路径或映射
    local buildingConfig = self.resMgr:GetBuildingConfigById(buildingId)
    if buildingConfig and buildingConfig.PrefabPath then
        return buildingConfig.PrefabPath
    end

    return nil
end

-- 转换网格坐标到世界坐标
function mgr:ConvertGridToWorldPosition(gridX, gridY)
    -- 根据您的游戏设计调整网格到世界坐标的转换
    -- 这里假设每个网格单元是1x1的世界单位
    local gridSize = 1.0
    local offsetX = 0  -- 可能需要一个偏移来对齐网格
    local offsetZ = 0

    local worldX = gridX * gridSize + offsetX
    local worldZ = gridY * gridSize + offsetZ

    return KingShot_Define.CS.Vector3(worldX, 0, worldZ)
end

-- 创建敌方出生点浮空图标
function mgr:CreateEnemyFloatingIcons()
    self.enemyFloatingIcons = {}

    for i = 1, #self.enemyBaseDatasByPos do
        local data = self.enemyBaseDatasByPos[i]

        -- 实例化浮空图标预制体
        local iconGO = KingShot_Define.CS.GameObject.Instantiate(self.resMgr.FloatingIconPrefab)
        iconGO.transform:SetParent(self.LevelRoot)

        -- 创建浮空图标组件实例
        local floatingIcon = require("kingshot_floating_icon").New(iconGO.transform)

        -- 设置位置
        floatingIcon:SetPosition(data.Pos)

        -- 显示图标并开始浮动
        floatingIcon:SetVisible(true)

        -- 保存到数组中
        self.enemyFloatingIcons[i] = floatingIcon
    end
end

-- 显示/隐藏所有敌方浮空图标
function mgr:SetEnemyFloatingIconsVisible(visible)
    if self.enemyFloatingIcons then
        for _, icon in ipairs(self.enemyFloatingIcons) do
            if icon then
                icon:SetVisible(visible)
            end
        end
    end
end

-- 更新浮空图标
function mgr:UpdateEnemyFloatingIcons(deltaTime)
    if self.enemyFloatingIcons then
        for _, icon in ipairs(self.enemyFloatingIcons) do
            if icon then
                icon:Update()
            end
        end
    end
end


function mgr:InitEnv()
    -- local RenderSettings = CS.UnityEngine.RenderSettings
    -- RenderSettings.ambientMode = 3
    -- RenderSettings.ambientLight = { r = 0.4705, g = 0.4705, b = 0.4705, a = 1.0 }
    -- RenderSettings.fog = false
    -- CS.MiniGame.Fog.EngineHeightFog.Instance:EnableFog(false)
    -- RenderSettings.fogMode = 1
    -- RenderSettings.fogColor = { r = 0.3254, g = 0.6235294, b = 0.2666, a = 1.0 }
    -- RenderSettings.fogDensity = 0.01
    -- RenderSettings.fogStartDistance = 33
    -- RenderSettings.fogEndDistance = 84
    -- RenderSettings.defaultReflectionMode = 0
end

function mgr:GameStart()
    self.TaskNumBind:SetValue(self.TaskNumMax)
    self.CoinNumBind:SetValue(0)
    if cysoldierssortie_KingShot_DEBUG then
        self.CoinNumBind:SetValue(30)
    end

    if #self.BossDatasByTime>0 then
        self.enemyBossNumBind:SetValue(#self.BossDatasByTime)
        --计算boss出现的时间，self.BossDatasByTime里先取delay时间短的
        table.sort(self.BossDatasByTime, function(a, b)
            return a.Delay < b.Delay
        end)
        self.bossTimer = self.BossDatasByTime[1].Delay
        self.bossTimerBind:SetValue(self.bossTimer)
    else
        self.enemyBossNumBind:SetValue(0)
        self.bossTimerBind:SetValue(0)
    end

    self.BattleFlagBind:SetValue(false)
    self.TimerBind:SetValue(0)
    self.TimeSecondBind:SetValue(-1)
    self.playerCtrl:Reset()
    self.cameraCtrl:Reset()
    self.teamCtrl:Reset()
    self.propCtrl:Reset()
    self.buildingCtrl:Reset()
    self.soldierCtrl:Reset()
    self.readyFlag = true

    self:InitSkillData()

end

---处理技能数据的覆写
function mgr:InitSkillData()
    --先读取res mgr的 skill配置，遍历之后 先看看overridepos是不是空的，如果不是，那么取里面的SpecialEffectsPath，
    --来查询KingShot_Define.bullet_collider_size_set里的key，如果KingShot_Define.bullet_collider_size_set有
    --那么直接重写下对应的value值里面的pos
    --如果在KingShot_Define.bullet_collider_size_set里面查不到的，那么从bullet_collider_size_set里复制一个到KingShot_Define.bullet_collider_size_set里，再重写下pos
    --最后遍历一次KingShot_Define.bullet_collider_size_set，里面的覆盖到bullet_collider_size_set

    local skillConfigs = self.resMgr.SkillConfigs
    for _, v in pairs(skillConfigs) do
        if v.OverridePos then
            local key = v.SpecialEffectsPath
            if KingShot_Define.bullet_collider_size_set[key] then
                KingShot_Define.bullet_collider_size_set[key].pos = v.OverridePos.pos
            else
                KingShot_Define.bullet_collider_size_set[key] = {
                    pos = v.OverridePos.pos,
                    size = bullet_collider_size_set[key].size,
                    center = bullet_collider_size_set[key].center,
                }
            end
        end
    end
    --我需要把KingShot_Define.bullet_collider_size_set 里面的覆盖到bullet_collider_size_set
    for k,v in pairs(KingShot_Define.bullet_collider_size_set) do
        bullet_collider_size_set[k] = v
    end
end


--- 处理关卡数据
function mgr:InitLevelData()
    local allEnemyFlag = self.resMgr.CurLvConfig.PassType == KingShot_Define.LevelPassType.AllEnemy
    self.TaskNumMax = 0
    local tmpPlayerData = self.resMgr.LevelData.Player
    ---@class kingshot_playerData
    self.PlayerData = {
        Pos = tmpPlayerData.Pos
    }
    self.enemyBaseDatasByPos = {}
    local enemyBaseDatas = self.resMgr.LevelData.EnemyBase
    local tmpIndex = 0
    for _, v in ipairs(enemyBaseDatas) do
        local data = {
            TeamID = v.ID, --id
            Pos = v.Pos,         --生成坐标
        }
        tmpIndex = tmpIndex + 1
        self.enemyBaseDatasByPos[tmpIndex] = data
    end
    self.TeamDatasByTime = {}
    self.BossDatasByTime = {}
    self.TeamDatasByPos = {}
    local tmpTimeIndex = 0
    local tmpPosIndex = 0
    local enemyDatas = self.resMgr.LevelData.EnemyBirth
    for _, v in ipairs(enemyDatas) do
        ---@class kingshot_enemyData
        local data = {
            TeamID = v.TeamID, --队伍id
            Type = v.Type,     --生成类型
            Pos = nil,         --生成坐标
            Delay = nil,       --生成延迟
            PathName = self.resMgr.CurLvConfig.LevelId .. "_" .. v.Path,  --路径名称
        }
        if data.Type == KingShot_Define.TeamSpawnType.Pos then
            data.Pos = v.Pos
            tmpPosIndex = tmpPosIndex + 1
            self.TeamDatasByPos[tmpPosIndex] = data
        elseif data.Type == KingShot_Define.TeamSpawnType.Timer then
            data.Pos = v.Pos
            data.Delay = v.Delay
            tmpTimeIndex = tmpTimeIndex + 1
            self.TeamDatasByTime[tmpTimeIndex] = data
            local teamConfig = self.resMgr:GetTeamConfigById(data.TeamID)
            if teamConfig and teamConfig.IsBoss == 1 then
                self.BossDatasByTime[#self.BossDatasByTime + 1] = data
            end
        end
        --local tmpTeamConfig = self.resMgr:GetTeamConfigById(data.TeamID)
        -----@param v troopclash_TeamUnitData
        --for i, v in ipairs(tmpTeamConfig.UnitDatas) do
        --    if allEnemyFlag then
        --        self.TaskNumMax = self.TaskNumMax + v.Count
        --    else
        --        local unitConfig = self.resMgr:GetUnitConfigById(v.UnitId)
        --        if unitConfig.UnitType == cysoldierssortie_unit_type.BossEnemy then
        --            self.TaskNumMax = self.TaskNumMax + v.Count
        --        end
        --    end
        --end
        self.TaskNumMax = self.resMgr.CurLvConfig.PassNum
    end
    self.PropDatasByTime = {}
    self.PropDatasByPos = {}
    local tmpPropTimeIndex = 0
    local tmpPropPosIndex = 0
    local propDatas = self.resMgr.LevelData.Props
    for _, v in ipairs(propDatas) do
        ---@class kingshot_propData
        local data = {
            PropID = v.PropID, --队伍id
            Type = v.Type,     --生成类型
            Pos = nil,         --生成坐标
            Delay = nil,       --生成延迟
        }
        if data.Type == KingShot_Define.TeamSpawnType.Pos then
            data.Pos = v.Pos
            tmpPropPosIndex = tmpPropPosIndex + 1
            self.PropDatasByPos[tmpPropPosIndex] = data
        elseif data.Type == KingShot_Define.TeamSpawnType.Timer then
            data.Delay = v.Delay
            tmpPropTimeIndex = tmpPropTimeIndex + 1
            self.PropDatasByTime[tmpPropTimeIndex] = data
        end
    end

    self.BuildingDatasByPos = {}
    self.BuildingDatasBySkill = {}
    local tmpBuildingPosIndex = 0
    local buildingDatas = self.resMgr.LevelData.Buildings
    for _, v in ipairs(buildingDatas) do
        ---@class kingshot_buildingData
        local data = {
            BuildingID = v.BuildingID, --队伍id
            Type = v.Type,     --生成类型
            Pos = nil,         --生成坐标
            SkillUid = v.SkillUid,       --配对的技能ID
        }
        if data.Type == KingShot_Define.TeamSpawnType.Pos then
            data.Pos = v.Pos
            tmpBuildingPosIndex = tmpBuildingPosIndex + 1
            self.BuildingDatasByPos[tmpBuildingPosIndex] = data
        end
        if data.Type == KingShot_Define.TeamSpawnType.Skill then
            self.BuildingDatasBySkill[#self.BuildingDatasBySkill+1] = data
        end
        if data.Main then
            self.homePos = data.Pos
        end
    end
    --怪物的行走路线处理
    self.enemyPathArr = {}
    local pathDatas = self.resMgr.LevelData.EnemyPath
    local tmpPathIndex = 0
    for _, v in ipairs(pathDatas) do
        local data = {
            PathName = v.ID, --路径名称
            Pos =  v.Pos,         --生成坐标
        }
        tmpPathIndex = tmpPathIndex + 1
        self.enemyPathArr[tmpPathIndex] = data
    end
    --建筑物的技能绑定
    self.buildingSkillArr = {}
    --士兵的固定出生
    self.SoldierDatasByPos = {}
    local buildingSkillDatas = self.resMgr.LevelData.SoldierBirth
    local tmpskillIndex = 0
    if buildingSkillDatas then
        for _, v in ipairs(buildingSkillDatas) do
            if v.Skill~=0 then
                ---@class kingshot_buildingSkillData
                local data = {
                    BuildingID = v.ID, --建筑物ID
                    SkillID =  v.Skill,         --技能ID
                    Uid = v.Uid,           --唯一标识
                    Pos =  v.Pos,         --生成坐标
                }
                if v.Uid then
                    self.buildingSkillArr[v.Uid] = data
                end
            else
                ---@class kingshot_soldierData
                local data = {
                    SoldiderUnitID = v.ID, --士兵ID
                    Uid = v.Uid,           --唯一标识
                    Pos =  v.Pos,         --生成坐标
                }
                tmpskillIndex = tmpskillIndex + 1
                self.SoldierDatasByPos[tmpskillIndex] = data
            end

        end
    end
end

function mgr:EnemyDead(character)
    --检查是否是BOSS
    local teamUnit = self.teamCtrl.TeamUnitWithCharacter[character]
    if teamUnit and teamUnit.teamData and teamUnit.teamData.TeamID then
        local teamConfig = self.resMgr:GetTeamConfigById(teamUnit.teamData.TeamID)
        if teamConfig and teamConfig.IsBoss == 1 then
            self.enemyBossNumBind:SetValue(self.enemyBossNumBind.value - 1)
        end
    end
    self.teamCtrl:EnemyDie(character)
    self.TaskNumBind:SetValue(self.TaskNumBind.value - 1)
end

function mgr:ChangeCoin(value)
    self.CoinNumBind:SetValue(self.CoinNumBind.value + value)
end

function mgr:GetBuildingSkillConfig(uid)
    for _, v in pairs(self.buildingSkillArr) do
        if v.Uid == uid then
            return self.resMgr:GetBuildingSkillConfigById(v.SkillID)
        end
    end
    return nil
end

function mgr:GetBakedPath(id)
    for _, v in ipairs(self.enemyBakedPathArr) do
        if v.PathName == id then
            return v.BakedPath
        end
    end
    return nil
end
---获取到寻路网格内的安全位置
function mgr:GetNavMeshPosition(pos)
    local res, hit = KingShot_Define.NavMesh.SamplePosition(pos, 1000, -1)
    return res, res and hit.position or pos
end

function mgr:GetSpawnTeamPos()
    local viewCenterPos = self.cameraCtrl.ViewCenterWorldPos
    local spawnPos = nil
    local minX = viewCenterPos.x - self.cameraCtrl.camBorderSizeHalf.x
    local maxX = viewCenterPos.x + self.cameraCtrl.camBorderSizeHalf.x
    local minZ = viewCenterPos.z - self.cameraCtrl.camBorderSizeHalf.y
    local maxZ = viewCenterPos.z + self.cameraCtrl.camBorderSizeHalf.y
    local sideList = { 1, 1, 2, 2, 2, 2, 3, 3, 4, 4, 4, 4 }
    local spawnDir = sideList[math.random(1, #sideList)]
    -- 上方
    if spawnDir == 4 then
        spawnPos = KingShot_Define.CS.Vector3(math.random(minX, maxX), 0, maxZ)
    elseif spawnDir == 2 then
        -- 下方
        spawnPos = KingShot_Define.CS.Vector3(math.random(minX, maxX), 0, minZ)
    elseif spawnDir == 1 then
        -- 左方
        spawnPos = KingShot_Define.CS.Vector3(minX, 0, math.random(minZ, maxZ))
    else
        -- 右方
        spawnPos = KingShot_Define.CS.Vector3(maxX, 0, math.random(minZ, maxZ))
    end
    _, spawnPos = self:GetNavMeshPosition(spawnPos)
    spawnPos.y = 0
    return spawnPos
end

function mgr:GetSpawnPropPos()
    local boundMin = 0.2
    local boundMax = 0.3
    -- 1/4屏随机位置
    local rPos = KingShot_Define.CS.Vector3(math.random(boundMin, boundMax), 0,
        math.random(boundMin, boundMax))
    local spawnDir = math.random()
    -- 左下角
    if spawnDir < 0.25 then
    elseif spawnDir < 0.5 then
        -- 左上角
        rPos.z = 1 - rPos.z
    elseif spawnDir < 0.75 then
        -- 右下角
        rPos.x = 1 - rPos.x
    else
        -- 右上角
        rPos.x = 1 - rPos.x
        rPos.z = 1 - rPos.z
    end
    local viewCenterPos = self.cameraCtrl.ViewCenterWorldPos
    local minX = viewCenterPos.x - self.cameraCtrl.camBorderSizeHalf.x
    local maxX = viewCenterPos.x + self.cameraCtrl.camBorderSizeHalf.x
    local minZ = viewCenterPos.z - self.cameraCtrl.camBorderSizeHalf.y
    local maxZ = viewCenterPos.z + self.cameraCtrl.camBorderSizeHalf.y
    rPos.x = math.lerp(minX, maxX, rPos.x)
    rPos.z = math.lerp(minZ, maxZ, rPos.z)
    _, rPos = self:GetNavMeshPosition(rPos)
    rPos.y = 0
    return rPos
end

function mgr:OnUpdate(deltaTime)
    if not self.readyFlag or not self.BattleFlagBind.value then
        return
    end
    self.TimerBind:SetValue(self.TimerBind.value + deltaTime)
    self.bossTimerBind:SetValue(self.bossTimerBind.value - deltaTime)
    self.TimeSecondBind:SetValue(math.floor(self.TimerBind.value))
    -- 超时自动结算（默认120秒），按当前任务情况结算：任务清空为胜利，否则失败
    if self.TimerBind.value >= self.GameMaxTime then
        local win = self.TaskNumBind.value <= 0
        self:GameOver(win)
        return
    end
    self.playerCtrl:Update(deltaTime)
    self.teamCtrl:Update(deltaTime)
    self.propCtrl:Update(deltaTime)
    self.buildingCtrl:Update(deltaTime)
    self.soldierCtrl:Update(deltaTime)

    -- 更新敌方浮空图标
    self:UpdateEnemyFloatingIcons(deltaTime)

    -- 更新攻击锁定图标
    self.uiMgr:UpdateAllAttackLockIcons()

    self:GetSpawnTeamPos()
end

function mgr:OnLateUpdate(deltaTime)
    if not self.readyFlag or not self.BattleFlagBind.value then
        return
    end
    self.cameraCtrl:LateUpdate(deltaTime)
end

function mgr:GameOver(win)
    if not self.readyFlag then
        return
    end
    self.readyFlag = false
    self.playerCtrl:StopAll()
    self.BattleFlagBind:SetValue(false)
    self:KillTween_Win()
    if win then
        cysoldierssortie_PlaySfx(cysoldierssortie_FxName.gameWin)
    else
        cysoldierssortie_PlaySfx(cysoldierssortie_FxName.gameFail)
    end
    --等待结算界面进度条增长后显示胜利or失败界面
    self.tween_Win = KingShot_Define.DOVirtual.DelayedCall(0.5, function()
        if win then
            KingShot_Define.minigame_mgr.BroadcastMsg(cysoldierssortie_MsgCode.STAGE_SUCCESS)
        else
            KingShot_Define.minigame_mgr.BroadcastMsg(cysoldierssortie_MsgCode.STAGE_FAIL)
        end
    end)
end

function mgr:KillTween_Win()
    if self.tween_Win ~= nil then
        self.tween_Win:Kill()
        self.tween_Win = nil
    end
end

function mgr:__delete()
    self:KillTween_Win()

    -- 注销事件
    if self._onFight then
        KingShot_Define.event.Unregister(KingShot_Define.event.SOLDIER_FIGHT, self._onFight)
        self._onFight = nil
    end

    -- 注销所有fusion_bindable回调
    if self._bind_unregs then
        for _, ur in ipairs(self._bind_unregs) do
            if ur and ur.UnRegister then
                ur:UnRegister()
            end
        end
        self._bind_unregs = nil
    end

    self.readyFlag = false
    self.BattleFlagBind = false

    -- 清理敌方浮空图标
    if self.enemyFloatingIcons then
        for _, icon in ipairs(self.enemyFloatingIcons) do
            if icon then
                icon:__delete()
            end
        end
        self.enemyFloatingIcons = nil
    end

    if self.NavMesh ~= nil then
        self.NavMesh:Remove()
        self.NavMesh = nil
    end
    if self.playerCtrl ~= nil then
        self.playerCtrl:Delete()
        self.playerCtrl = nil
    end
    if self.cameraCtrl ~= nil then
        self.cameraCtrl:Delete()
        self.cameraCtrl = nil
    end
    if self.teamCtrl ~= nil then
        self.teamCtrl:Delete()
        self.teamCtrl = nil
    end
    if self.propCtrl ~= nil then
        self.propCtrl:Delete()
        self.propCtrl = nil
    end
    if self.buildingCtrl ~= nil then
        self.buildingCtrl:Delete()
        self.buildingCtrl = nil
    end
    if self.soldierCtrl ~= nil then
        self.soldierCtrl:Delete()
        self.soldierCtrl = nil
    end
end

return mgr
