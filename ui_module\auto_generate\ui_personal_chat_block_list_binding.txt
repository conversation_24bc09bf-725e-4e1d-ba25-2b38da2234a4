local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local RectTransform = CS.UnityEngine.RectTransform


module("ui_personal_chat_block_list_binding")

UIPath = "ui/prefabs/gw/gw_personalinfo/uipersonalchatblocklist.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	srt_Content = { path = "content/List/Viewport/srt_Content", type = ScrollRectTable, },
	scrItem_ListItem = { path = "content/List/Viewport/srt_Content/scrItem_ListItem", type = ScrollRectItem, },
	rtf_Nolist = { path = "content/List/rtf_Nolist", type = RectTransform, },

}
