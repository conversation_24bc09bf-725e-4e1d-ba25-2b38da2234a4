-- arena_common_mgr.txt ------------------------------------------
-- author:  hym
-- date:    2025/08/28
-- ver:     1.0
-- desc:    竞技场通用mgr
--------------------------------------------------------------
local require 	= require
local math = math
local table = table
local ipairs = ipairs
local string = string
local os = os
local ui_util = require "ui_util"
local cfg_util = require "cfg_util"
local player_prefs = require "player_prefs"
local event_arena_common_define = require "event_arena_common_define"
local game_scheme = require "game_scheme"
local event = require "event"
local arena_common_const = require "arena_common_const"
local arena_common_data = require "arena_common_data"
local ui_window_mgr = require "ui_window_mgr"
module("arena_common_mgr")
local logger = require("logger").new("arena_common_mgr",1)
IsLogLevel = logger.IsLogLevel
Warning = logger.Warning        --log
Warning0 = logger.Warning0      --error

function Init()
    event.Register(event.FIRST_LOGIN_CREATE_DATA_FINISH, LoginReqArenaRankData)
    event.Register(event_arena_common_define.TMSG_NEW_ARENA_ENTER_BATTLE_RSP, SetBattleInfoByRsp)
end

--请求竞技场排行榜数据
function LoginReqArenaRankData()
    for k, v in ipairs(arena_common_const.loginReqArenaType) do
        ReqArenaRankData(nil, v)
    end
end

function ReqArenaRankData(arenaID, arenaType, nPageID)
    local data = {
        nArenaType = arenaType,
    }
    arenaID = arenaID or GetCurArenaID(arenaType)
    if arenaID then
        data.arenaID = arenaID
        data.nPageID = nPageID or GetPlayerRankPage(arenaID)
    end
    local net_arena_common = require "net_arena_common"
    net_arena_common.MSG_NEW_ARENA_GET_RANK_INFO_REQ(data)
end

--根据竞技场类型获取当前竞技场ID
function GetCurArenaID(arenaType)
    return arena_common_data.GetCurArenaID(arenaType)
end

--获取玩家排名所在页码
function GetPlayerRankPage(arenaID)
    local curRank = arena_common_data.GetPlayerCurRank(arenaID)
    if not curRank then
        return 1
    end
    return math.ceil(curRank / arena_common_const.SingleRankCount)
    --arena_common_const.SingleRankCount
end 

--获取玩家当前排名
function GetPlayerCurRank(arenaID)
    return arena_common_data.GetPlayerCurRank(arenaID)
end

--获取玩家最后一次打开竞技场排名
function GetPlayerOldRank(arenaID)
    return arena_common_data.GetPlayerOldRank(arenaID)
end

--设置玩家最后一次打开竞技场界面排名
function SetPlayerOldRank(arenaType, rank)
    local curArenaID = arena_common_data.GetCurArenaID(arenaType)
    if not curArenaID then
        return
    end
    arena_common_data.SetPlayerOldRank(curArenaID, rank)
end

--设置竞技场数据
function SetArenaData(msg)
    arena_common_data.SetArenaData(msg)
    arena_common_data.SetCurArenaID(msg.nArenaType, msg.nArenaID)
    if msg.nUpgradeArenaID then
        SetArenaSwitchData(msg.nArenaID, msg.nUpgradeArenaID) 
    end
    event.Trigger(event_arena_common_define.UPDATE_ARENA_RANK, msg)
end 

--获取玩家是否能够参与竞技场
function IsCanJoinArena(arenaType)
    local curArenaID = arena_common_data.GetCurArenaID(arenaType)
    if not curArenaID then
        return false
    end
    return arena_common_data.IsCanJoinArena(curArenaID)
end

--获取竞技场是否开启
function IsArenaOpen(arenaType)
    local festival_activity_mgr = require "festival_activity_mgr"
    local festival_activity_cfg = require "festival_activity_cfg"
    local headingCode = arena_common_const.ArenaHeadingCode[arenaType]
    if not headingCode then
        return false
    end
    local actIsOpen = festival_activity_mgr.GetIsOpenByHeadingCode(headingCode, true)
    if not actIsOpen then
        return false
    end
    local curArenaID = arena_common_data.GetCurArenaID(arenaType)
    if not curArenaID then
        return false
    end
    local startTime = arena_common_data.GetArenaOpenTime(curArenaID)
    if not startTime then
        return false
    end
    if startTime > os.server_time() then
        return false
    end
    return true
end

--打开竞技场界面
function OpenArenaUI(arenaType, closeFunc, headingCode)
    headingCode = headingCode or arena_common_const.ArenaHeadingCode[arenaType]
    if not headingCode then
        return
    end
    local festival_activity_mgr = require "festival_activity_mgr"
    local param = {}
    param.activityCenterBackBtnFun = closeFunc
    param.arenaType = arenaType
    festival_activity_mgr.OpenActivityUIByActivityCodeType(headingCode, param)
end

--获取竞技场配置
function GetArenaCfg(arenaID)
    return arena_common_data.GetArenaConfig(arenaID)
end 

--获取竞技场开启时间
function GetArenaOpenTime(arenaID)
    return arena_common_data.GetArenaOpenTime(arenaID)
end

--获取竞技场结束时间
function GetArenaEndTime(arenaID)
    return arena_common_data.GetArenaEndTime(arenaID)
end

--获取竞技场匹配区服字符串
function GetArenaMatchServerStr(arenaID)
    local serverIdList = arena_common_data.GetArenaMatchServer(arenaID)
    if not serverIdList then
        return ""
    end
    local strTable = {}
    for k, v in ipairs(serverIdList) do
        local str = string.format("#%s", ui_util.GetWorldIDToShowWorldID(v,nil,ui_util.WorldIDRangeType.Normal))
        table.insert(strTable, str)
    end
    return table.concat(strTable, " ")
end 

--获取排行榜前三数据
function GetTopRankData(arenaType, rankData)
    local arenaId = GetCurArenaID(arenaType)
    if not arenaId then
        Warning0("can not get arena id arenaType:", arenaType)
        return
    end
    if not rankData then
        return
    end
    local arenaRank = arena_common_data.GetTopRankData(arenaId)
    local rankList = {}
    local hasRank = arenaRank and #arenaRank > 0
    local tmpList = hasRank and arenaRank or rankData
    for k, v in ipairs(tmpList) do
        local roleInfo = hasRank and v.tRoleInfo or v
        if roleInfo then
            local rankInfo = {
                roleLv = hasRank and roleInfo.level or roleInfo.roleLv,
                frameID = roleInfo.frameID,
                roleId = hasRank and roleInfo.roleID or roleInfo.dbid,
                unionShortName = roleInfo.leagueShortName,
                name = roleInfo.name,
            }
            rankInfo.faceStr = roleInfo.faceID
            if rankInfo.faceStr and not string.IsNullOrEmpty(roleInfo.faceStr) then
                rankInfo.faceStr = roleInfo.faceStr
            end
            local click_liking_mgr = require "click_liking_mgr"
            local likeConfigType = arena_common_const.LikeConfigType[arenaType]
            rankInfo.itemInfo = click_liking_mgr.GetRewardInfo(likeConfigType)
            rankInfo.worldStr = string.format("#%d", ui_util.GetWorldIDToShowWorldID(roleInfo.worldId,nil,ui_util.WorldIDRangeType.Normal))
            table.insert(rankList, rankInfo)
        end

    end
    return rankList
end

--获取排行榜数据
function GetRankData(arenaType)
    local arenaId = GetCurArenaID(arenaType)
    if not arenaId then
        Warning0("can not get arena id arenaType:", arenaType)
        return
    end
    return arena_common_data.GetArenaData(arenaId)
end

--解析排行榜数据
function ParseRankData(arenaRank, arenaType, isSingle)
    if not arenaRank then
        return
    end
    local rankList = {}
    local SetDataFun = function(info)
        local roleInfo = info.tRoleInfo
        local rankInfo = ParseRoleInfo(roleInfo, arenaType)
        if not rankInfo then
            return
        end
        rankInfo.score = info.nScore
        rankInfo.rank = info.nRank
        rankInfo.power = info.nDefenseLineUpPower
        rankInfo.itemInfo = nil
        return rankInfo
    end
    if not isSingle then
        for k, v in ipairs(arenaRank) do
            local rankInfo = SetDataFun(v)
            table.insert(rankList, rankInfo)
        end
        return rankList
    else
        return SetDataFun(arenaRank)
    end
end

--解析角色数据（结构为PlayerBasicInfo）
function ParseRoleInfo(roleInfo, arenaType)
    if not roleInfo then
        return
    end
    local roleData = {
        roleLv = roleInfo.level,
        frameID = roleInfo.frameID,
        roleId = roleInfo.roleID,
        unionShortName = roleInfo.leagueShortName,
        unionName = roleInfo.leagueName,
        name = roleInfo.name,
        soldierLv = roleInfo.maxSoldierLv,
        roleID = roleInfo.roleID,
    }
    local soldierCfg = game_scheme:Soldier_1(roleData.soldierLv)
    if soldierCfg then
        roleData.soldierIcon = soldierCfg.soldierID
    end
    roleData.faceStr = roleInfo.faceID
    if roleInfo.faceStr and not string.IsNullOrEmpty(roleInfo.faceStr) then
        roleData.faceStr = roleInfo.faceStr
    end
    local click_liking_mgr = require "click_liking_mgr"
    local likeConfigType = arena_common_const.LikeConfigType[arenaType]
    roleData.itemInfo = click_liking_mgr.GetRewardInfo(likeConfigType)
    roleData.worldStr = string.format("#%d", ui_util.GetWorldIDToShowWorldID(roleInfo.worldId,nil,ui_util.WorldIDRangeType.Normal))
    return roleData
end

--请求点赞数据
function RequestLikeData(allRankInfo, arenaType)
    local net_click_liking = require "net_click_liking"
    local likeType = arena_common_const.LikeServerType[arenaType]
    local specialPlayerGroup = {}
    for k, v in ipairs(allRankInfo) do
        table.insert(specialPlayerGroup, v.roleId)
    end
    net_click_liking.MSG_GET_SP_LIKENUM_REQ(likeType,specialPlayerGroup)
end 

--请求点赞方法
function RequestLike(roleID, arenaType)
    local data_personalInfo = require "data_personalInfo"
    local click_liking_mgr = require "click_liking_mgr"
    local lang = require "lang"
    local likeServerType = arena_common_const.LikeServerType[arenaType]
    local likeConfigType = arena_common_const.LikeConfigType[arenaType]
    local likeClientType = arena_common_const.LikeClientType[arenaType]
    local canclickTimes = data_personalInfo.GetPersonalInfoValue(likeClientType)
    local LikeMaxCount = click_liking_mgr.GetArenaMaxLikeCount(likeConfigType)
    if canclickTimes and canclickTimes >= LikeMaxCount then
        local flow_text = require "flow_text"
        flow_text.Add(string.format2(lang.Get(1007309),LikeMaxCount))
        return
    end
    local net_click_liking = require "net_click_liking"
    net_click_liking.MSG_ZONE_ROLE_PRAISE_UPDATE_REQ(roleID, likeServerType)
end

--获取配置的任务数据
function GetDailyTaskList(activityId, arenaType)
    local arenaID = GetCurArenaID(arenaType)
    local arenaCfg = GetArenaCfg(arenaID)
    if not arenaCfg then
        return
    end
    local subActiveId = 0
    local ActivityMain = game_scheme:ActivityMain_0(activityId)
    if not ActivityMain then
        return
    end
    if ActivityMain.bindActivity.count == 0 or  ActivityMain.bindActivity.data[0] ~= 1 then
        Warning0("GetBaseGiftList 主活动关联不对 actId = ", activityId)
        return
    end
    local define = arena_common_const.GetDailyRewardConfig[arenaType]
    if define then
        subActiveId = define(arenaCfg, ActivityMain.bindActivity)
    else
        subActiveId = ActivityMain.bindActivity.data[1]
    end
    if not subActiveId then
        Warning0("can not get subActiveId activityId:", activityId)
        return
    end
    local subActiveCfg = game_scheme:festivalActivity_0(subActiveId)
    if not subActiveCfg then
        Warning0("can not get subActiveCfg subActiveId:", subActiveId)
        return
    end
    local taskList = cfg_util.ArrayToLuaArray(subActiveCfg.ctnID1)
    return taskList
end

--请求挑战记录
function RequestChallengeRecord(arenaType, nPageID)
    local arenaId = GetCurArenaID(arenaType)
    if not arenaId then
        Warning0("can not get arena id arenaType:", arenaType)
        return
    end
    local net_arena_common = require "net_arena_common"
    net_arena_common.MSG_NEW_ARENA_GET_BATTLE_RECORDS_REQ({nArenaID = arenaId, nPageID = nPageID})
end

---@deprecated 打开竞技场奖励界面
---@param activityId number 活动id（竞技场主活动的id
---@param arenaType number 竞技场类型
---@param allianceRewardTip number 联盟奖励提示lang
function OpenArenaRewardUI(activityId, arenaType, allianceRewardTip, arenaId)
    arenaId = arenaId or GetCurArenaID(arenaType)
    if not arenaId then
        Warning0("can not get arena id arenaType:", arenaType)
        return
    end
    local arenaCfg = GetArenaCfg(arenaId)
    if not arenaCfg then
        return
    end
    local data = {
        personRewardType = arenaCfg.PersonalRank,
        allianceRewardType = arenaCfg.LeagueRank,
        allianceTip = allianceRewardTip,
        dailyTask = GetDailyTaskList(activityId, arenaType),
    }
    ui_window_mgr:ShowModule("ui_arena_reward", nil, nil, data)
end

---@deprecated 打开战斗记录界面
---@param arenaType number 竞技场类型
function OpenBattleRecordUI(arenaType)
    local data = {
        arenaType = arenaType
    } 
    ui_window_mgr:ShowModule("ui_arena_battle_history",nil, nil, data)
end

---@deprecated 打开竞技场提示界面
---@param arenaType number 竞技场类型
function OpenArenaHelpUI(arenaType)
    local arenaId = GetCurArenaID(arenaType)
    local arenaCfg = GetArenaCfg(arenaId)
    if not arenaCfg or not arenaCfg.ArenaHelpTipsID then
        return
    end
    local ui_help = require "ui_help"
    ui_help.ShowWithDate(arenaCfg.ArenaHelpTipsID)
end


--竞技场进入弹窗
function OpenArenaEnterUI(arenaType, msg)
    local isShowFirst = IsShowArenaEnterUI(arenaType, msg)
    local isShowPromote = msg.nUpgradeArenaID and msg.nUpgradeArenaID ~= 0
    if isShowFirst then
        OpenArenaRankChangeUI(arenaType, msg, function()
            OpenPromoteArenaUI(arenaType)
        end)
        return
    else
        OpenPromoteArenaUI(arenaType)
        return
    end
    if not isShowFirst and not isShowPromote then
        --TODO没有定级/排名变化弹窗、晋级弹窗的情况下才显示点赞弹窗(策划决定暂时不做
        
    end
end

--打开点赞弹窗
function OpenArenaLikeUI(msg)
    local click_liking_mgr = require "click_liking_mgr"
    local canShow = click_liking_mgr.CheckCanShowLike(msg)
    if canShow then
        --刷新主页上的点赞数
        ui_window_mgr:ShowModule("ui_click_liking_panel",nil,nil,msg)
    end
    
end

--是否显示定级或者排名变化弹窗
function IsShowArenaRankChangeUI(arenaType, msg)
    if IsFirstEnterArena(arenaType) then
        return true
    end
    if not msg or not msg.tSelfRankInfo or not msg.nArenaID then
        return
    end
    local oldRank = GetPlayerOldRank(msg.nArenaID)
    local curRank = msg.tSelfRankInfo.nRank
    if not oldRank or oldRank == 0 or curRank == oldRank then
        return
    end
    return true
end


---@deprecated 打开竞技场排名变化界面
---@param arenaType number 竞技场类型
function OpenArenaRankChangeUI(arenaType, msg)
    if not msg or not msg.tSelfRankInfo or not msg.nArenaID then
        return
    end
    local data = {
        curRank = msg.tSelfRankInfo.nRank,
        playerName = msg.tSelfRankInfo.tRoleInfo.name,
        frameID = msg.tSelfRankInfo.tRoleInfo.frameID,
    }
    data.faceStr = msg.tSelfRankInfo.tRoleInfo.faceID
    if data.faceStr and not string.IsNullOrEmpty(msg.tSelfRankInfo.tRoleInfo.faceStr) then
        data.faceStr = msg.tSelfRankInfo.tRoleInfo.faceStr
    end
    if IsFirstEnterArena(arenaType) then
        ui_window_mgr:ShowModule("ui_arena_first_enter", nil, nil, data)
    else
        data.oldRank = GetPlayerOldRank(msg.nArenaID)
        if not data.oldRank or data.oldRank == 0 or data.curRank == data.oldRank then
            return
        end
        data.isUp = data.curRank > data.oldRank
        local changeData = {
            roleData = data,
            rankData = msg,
        }
        ui_window_mgr:ShowModule("ui_arena_rank_change", nil, nil, changeData)
        return true
    end
end

--获取是否第一次进入竞技场
function IsFirstEnterArena(arenaType)
    local lastStartTime = arena_common_data.GetEnterArenaTime(arenaType)
    if lastStartTime == 0 then
        return true
    end
    local arenaId = GetCurArenaID(arenaType)
    if not arenaId then
        return false
    end
    local curStartTime = GetArenaOpenTime(arenaId)
    return lastStartTime <= curStartTime
end

--设置竞技场切换数据
function SetArenaSwitchData(nOldArenaID, nNewArenaID)
    arena_common_data.SetArenaSwitchData(nOldArenaID, nNewArenaID)
end

--切换竞技场成功
function ChangeArenaSuccess(nArenaType, nArenaID)
    arena_common_data.SetCurArenaID(nArenaType, nArenaID)
    ReqArenaRankData(nArenaID, nArenaType, 1)
end

--请求晋升竞技场
function RequestPromoteArena(arenaType)
    local arenaId = GetCurArenaID(arenaType)
    if not arenaId then
        return
    end
    local newArenaId = arena_common_data.GetArenaSwitchData(arenaId)
    if not newArenaId then
        return
    end
    local net_arena_common = require "net_arena_common"
    net_arena_common.MSG_NEW_ARENA_CHANGE_ARENA_REQ({nOldArenaID = arenaId, nNewArenaID = newArenaId})
end

--打开晋升弹窗
function OpenPromoteArenaUI(arenaType)
    local arenaId = GetCurArenaID(arenaType)
    if not arenaId then
        return
    end
    local arenaCfg = GetArenaCfg(arenaId)
    if not arenaCfg then
        return
    end
    local newArenaId = arena_common_data.GetArenaSwitchData(arenaId)
    if not newArenaId then
        return
    end
    local newArenaCfg = GetArenaCfg(newArenaId)
    if not newArenaCfg then
        return
    end
    local define = arena_common_const.GetPromoteRewardConfig[arenaType]
    if not define then
        return
    end
    local reward = define()
    if not reward then
        return
    end
    local data = {
        reward = reward,
        arenaType = arenaType,
        rank = newArenaCfg.HeroPowerRank.count == 2 and newArenaCfg.HeroPowerRank.data[1] or newArenaCfg.HeroPowerRank.data[0],
    }
    ui_window_mgr:ShowModule("ui_arena_promotion", nil, nil, data)
    return true
end

--是否显示晋升弹窗
function CanShowPromoteArenaUI(arenaType)
    local arenaId = GetCurArenaID(arenaType)
    if not arenaId then
        return false
    end
    local arenaCfg = GetArenaCfg(arenaId)
    if not arenaCfg then
        return false
    end
    local newArenaId = arena_common_data.GetArenaSwitchData(arenaId)
    return newArenaId and newArenaId ~= 0
end

--请求挑战列表
function RequestChallengeList(arenaType, isRefresh)
    local arenaId = GetCurArenaID(arenaType)
    local net_arena_common = require "net_arena_common"
    net_arena_common.MSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_REQ({nArenaID = arenaId, bRefresh = isRefresh})
end

--打开挑战界面
---@param arenaData table 竞技场数据
--[[
    local data = {
        buyRefreshExpend,       --购买刷新需要的钻石
        arenaType,              --竞技场类型
        maxFreeRefreshTime,     --可购买的最大免费刷新次数
        needChallengeNum,       --跳过战斗需要的挑战次数
        canShowSkip,            --是否显示跳过战斗
        maxChallengedNum,       --最大挑战次数
    }
--]]
function OpenArenaChallengeUI(arenaData)
    local arenaId = GetCurArenaID(arenaData.arenaType)
    if not arenaId then
        return
    end
    local arenaCfg = GetArenaCfg(arenaId)
    if not arenaCfg or not arenaCfg.ArenaHelpTipsID then
        return
    end
    local refreshExpend = cfg_util.ArrayToLuaArray(arenaCfg.RefreshExpend)
    if not refreshExpend or not refreshExpend[1] then
        return
    end
    local serData = GetRankData(arenaData.arenaType)
    if not serData then
        return
    end
    if not arenaData.canShowSkip then
        arenaData.canShowSkip = serData.nTotalChallengedNum <= arenaCfg.ChallengeTime
    end
    arenaData.buyRefreshExpend = refreshExpend[2]
    arenaData.maxChallengedNum = arenaCfg.ChallengeTime
    arenaData.maxFreeRefreshTime = arenaCfg.FreeRefreshTime
    ui_window_mgr:ShowModule("ui_arena_challenge", nil, nil, arenaData)
end

--设置跳过战斗选择
function SetSkipBattle(arenaType, isSkip)
    arena_common_data.SetSkipBattle(arenaType, isSkip)
end

--获取是否跳过战斗
function GetSkipBattle(arenaType)
    return arena_common_data.GetSkipBattle(arenaType)
end

--通过活动id获取竞技场类型
function GetArenaTypeByActivityId(activityId)
    local num = game_scheme:ArenaCommonInfo_nums()
    for i = 1, num - 1 do
        local cfg = GetArenaCfg(i)
        local cfgActID = cfg and cfg.ActId
        if cfgActID then
            local actCfg = game_scheme:ActivityMain_0(cfgActID)
            if actCfg then
                if actCfg.bindActivity.count ~= 0 then
                    if actCfg.bindActivity.data[0] == 1 and actCfg.ActID == activityId then
                        return cfg.ArenaType
                    elseif actCfg.bindActivity.data[0] == 2 and activityId == actCfg.bindActivity.data[1] then
                        return cfg.ArenaType
                    end
                end
            end
        end
    end
end


--region 战斗
--请求战斗
function RequestBattle(data)
    local battleData = {
        nArenaID = data.nArenaID,
        nRivalRoleID = data.enemyInfo.roleID,
        lineUp = data.attackInfo,
    }
    local arena_battle_mgr = require "arena_battle_mgr"
    local curBattleData = arena_common_data.GetBattleData(data.nArenaID) or {}
    curBattleData.attackLineup = data.attackInfo
    curBattleData.attackInfo = arena_battle_mgr.GetMyRoleInfo()
    curBattleData.defendInfo = data.enemyInfo
    curBattleData.defendLineup = data.defendInfo
    curBattleData.arenaType = data.arenaType
    arena_common_data.SetBattleData(data.nArenaID, curBattleData)

    local net_arena_common = require "net_arena_common"
    net_arena_common.MSG_NEW_ARENA_ENTER_BATTLE_REQ(battleData)
end

--请求防守阵容
function RequestDefendLineUp(arenaType, nTargetRoleID)
    local arenaId = GetCurArenaID(arenaType)
    if not arenaId then
        return
    end
    
    local net_arena_common = require "net_arena_common"
    net_arena_common.MSG_NEW_ARENA_GET_DEFENCE_LINEUP_REQ({nArenaID = arenaId, nTargetRoleID = nTargetRoleID})
end

--打开设置防守阵容界面
function OpenDefendLineUpUI(arenaType, msg)
    local arena_battle_mgr = require "arena_battle_mgr"
    local battleType = arena_common_const.ArenaBattleType[arenaType]
    if not battleType then
        Warning0("can not get arena battle type arenaType:", arenaType)
        return
    end
    local arenaId = GetCurArenaID(arenaType)
    if not arenaId then
        Warning0("can not get arena id arenaType:", arenaType)
        return
    end
    local arenaCfg = GetArenaCfg(arenaId)
    if not arenaCfg then
        Warning0("can not get arena cfg arenaType:", arenaType)
        return
    end

    local teamNum = arenaCfg.TeamNum
    local defendLineupList = {}
    if teamNum > 1 then
        --车轮战
        for i = 1, teamNum do
            if msg.arrDefenseLineup[i] then
                defendLineupList[i] = arena_battle_mgr.ParseLineupTeamDataByPB(msg.arrDefenseLineup[i], i)
            else
                defendLineupList[i] = arena_battle_mgr.ParseLineupTeamDataByPB({}, i)
            end
        end
    else
        defendLineupList[1] = arena_battle_mgr.ParseLineupTeamDataByPB(msg.arrDefenseLineup[1] or {}, 1)
    end
    SetDefendLineUp(msg.nArenaID, defendLineupList)
    
    local defendData = {
        battleType = battleType,
        teamNum = arenaCfg.TeamNum,
        nArenaID = arenaId,
        enemyIndex = 1,     --防守应该是默认第一个
        defendInfo = defendLineupList,
        setDefendReq = RequestSaveDefendLineUp,
    }
    defendData.FightBtnFunc = function(data, lineup)
        if teamNum > 1 then
            data.defendInfo = lineup
            ui_window_mgr:ShowModule("ui_arena_battle_wheel_switch", nil, nil, data)
        else
            ui_window_mgr:UnloadModule("ui_select_hero")
        end
    end
    defendData.SwitchDefenseLineUp = function(arenaID, oldIndex, newIndex)
        arena_common_data.SwitchDefenseLineUp(arenaID, oldIndex, newIndex)
        local lineup = {}
        defendData.defendInfo = arena_common_data.GetDefenseLineUp(arenaID)
        for k, v in ipairs(defendData.defendInfo) do
            local team = {}
            team.palList = v
            team.weaponId = v.weaponID
            table.insert(lineUp, team)
        end
        RequestSaveDefendLineUp(arenaType, lineup)
    end
    defendData.GetDefendLineUp = arena_common_data.GetDefenseLineUp
    --1008  保存阵容        675093 调整顺序
    defendData.fightBtnLang = teamNum > 1 and 675093 or 1008
    arena_battle_mgr.SetUpDefenseBattle(defendData)
end

--请求保存防守阵容
function RequestSaveDefendLineUp(lineUp, defendData)
    local net_arena_common = require "net_arena_common"
    net_arena_common.MSG_NEW_ARENA_SET_DEFENCE_LINEUP_REQ({nArenaID = defendData.nArenaID, lineUp = lineUp})
end

--设置防守阵容
function SetDefendLineUp(nArenaID, defendLineupList)
    arena_common_data.SetDefenseLineUp(nArenaID, defendLineupList)
end

--进入战斗
function SetupStormChallenge(arenaType, enemyInfo, defendInfo, canSkip)
    local arenaId = GetCurArenaID(arenaType)
    if not arenaId then
        Warning0("can not get arena id arenaType:", arenaType)
        return
    end
    local arenaCfg = GetArenaCfg(arenaId)
    if not arenaCfg then
        Warning0("can not get arena cfg arenaType:", arenaType)
        return
    end
    
    local teamNum = arenaCfg.TeamNum
    local arena_battle_mgr = require "arena_battle_mgr"
    local defendLineupList = {}
    if teamNum > 1 then
        --车轮战
        for i = 1, teamNum do
            if defendInfo[i] then
                defendLineupList[i] = arena_battle_mgr.ParseLineupTeamDataByPB(defendInfo[i], i)
            else
                defendLineupList[i] = arena_battle_mgr.ParseLineupTeamDataByPB({}, i)
            end
        end
        if not canSkip then
            --打开车轮战界面
            ShowBattleWheel(arenaType, enemyInfo, defendLineupList)
        else
            --TODO 跳过战斗,直接请求战斗，看结算界面
        end
    else
        --单人，打开布阵界面
        defendLineupList[1] = arena_battle_mgr.ParseLineupTeamDataByPB(defendInfo[1] or {}, 1)
        SetupStormChallengeSingle(arenaType, enemyInfo, defendLineupList, canSkip)
    end
    
end

function GetSetLineupData(arenaType, enemyInfo, defendInfo, enemyIndex)
    local arenaId = GetCurArenaID(arenaType)
    if not arenaId then
        Warning0("can not get arena id arenaType:", arenaType)
        return
    end
    local arenaCfg = GetArenaCfg(arenaId)
    if not arenaCfg then
        Warning0("can not get arena cfg arenaType:", arenaType)
        return
    end
    local lineMark = arena_common_const.ArenaAttackLineup[arenaType]
    if not lineMark then
        Warning0("can not get arena line mark arenaType:", arenaType)
        return
    end
    local attackInfo = arena_common_data.GetAttackLineUpTeamList(arenaType, arenaId, arenaCfg.TeamNum)
    if not attackInfo then
        Warning0("can not get arena attack info arenaType:", arenaType)
        return
    end
    local battleType = arena_common_const.ArenaBattleType[arenaType]
    if not battleType then
        Warning0("can not get arena battle type arenaType:", arenaType)
        return
    end
    local battleData = {
        arenaType = arenaType,
        nArenaID = arenaId,
        teamNum = arenaCfg.TeamNum,
        attackInfo = attackInfo,
        defendInfo = defendInfo,
        enemyIndex = enemyIndex or 1,
        battleType = battleType,
        lineMark = lineMark,
        battleReq = RequestBattle,
        arenaType = arenaType,
        canSkip = canSkip,
        ParseAttackLineUpByCache = arena_common_data.ParseAttackLineUpByCache,
        enemyInfo = {
            roleID = enemyInfo.roleID,
            name = enemyInfo.name,
            level = enemyInfo.roleLv,
            faceStr = enemyInfo.faceStr,
            frameID = enemyInfo.frameID,
            power = enemyInfo.power,
            scores = 0,     --暂时不需要这个字段
            worldStr = enemyInfo.worldStr,
        },
    }
    return battleData
end

--竞技场单人战斗
function SetupStormChallengeSingle(arenaType, enemyInfo, defendInfo, canSkip)

    local battleData = GetSetLineupData(arenaType, enemyInfo, defendInfo, canSkip)
    if not battleData then
        return
    end
    battleData.onFightCloseCallback = function(data)
        OpenArenaUI(data.arenaType)
    end
    battleData.onModuleShow = function()
        
    end
    battleData.SetPrepareBattle = arena_common_const.BattleSetPrepareBattle[arenaType]
    local arena_battle_mgr = require "arena_battle_mgr"
    arena_battle_mgr.SetUpBattleWheel(battleData)
end

--车轮战界面显示
function ShowBattleWheel(arenaType, enemyInfo, defendInfo)
    local data = GetSetLineupData(arenaType, enemyInfo, defendInfo)
    if not data then
        return
    end
    --(i, self.heroData, self.trainTeamItem, self.enemyInfo)
    data.OnClickTeamItem = function(curEnemyIndex, attackInfo)
        SetupStormChallengeWheel(data.arenaType, data.enemyInfo, data.defendInfo, curEnemyIndex, attackInfo)
    end
    data.SwitchAttackLineUpByIndex = arena_common_data.SwitchAttackLineUpByIndex
    data.GetAttackLineUpTeamList = function()
        return arena_common_data.GetAttackLineUpTeamList(data.nArenaID, data.arenaType, data.teamNum)
    end
    data.battleStartFunc = function()
        local lineUp = arena_common_data.GetAttackLineUpTeamList(data.nArenaID, data.arenaType, data.teamNum)
        data.attackInfo = lineUp
        data.battleReq(data)
        if arena_common_const.BattleSetPrepareBattle[data.arenaType] then
            arena_common_const.BattleSetPrepareBattle[data.arenaType]()
        end
    end
    ui_window_mgr:ShowModule("ui_arena_battle_wheel", nil, nil, data)
end

--竞技场车轮战(单队上阵）
function SetupStormChallengeWheel(arenaType, enemyInfo, defendInfo, enemyIndex, attackInfo)
    local battleData = GetSetLineupData(arenaType, enemyInfo, defendInfo, enemyIndex)
    if not battleData then
        return
    end
    battleData.attackInfo = attackInfo
    local arena_battle_mgr = require "arena_battle_mgr"
    arena_battle_mgr.SetUpBattleWheel(battleData)
end

function GetIsArenaBattle(battleType)
    for k , v in pairs(arena_common_const.ArenaBattleType) do
        if v == battleType then
            return v
        end
    end
    return
end

--从战报中获取战斗数据
function OpenNormalBattleResult(arenaType)
    local curBattleData = arena_common_data.GetBattleData()
    if not curBattleData then
        Warning0("can not get arena battle data")
        return
    end
    if not curBattleData.isGetBattleRsp then
        Warning0("can not get arena battle rsp")
        return
    end
    local battle_data = require "battle_data"
    local data = {
        attackerId = battle_data.attacker,
        defenderId = battle_data.defender,
        HeroData = {},
        closeCallback = function() 
           --关闭结算回调 
            local arenaId = GetCurArenaID(arenaType)
            if not arenaId then
                return
            end
            arena_common_data.ClearBattleData(arenaId)
            OpenArenaUI(arenaType)
        end,
        isVictory = battle_data.IsVictory(),
    }
    local heros = battle_data.GetHeros()
    for i = 0, 14 do
        local hero = heros[i]
        if hero ~= nil then
            data.heroData[i] = hero
        end
    end
    data.attackInfo = curBattleData.attackInfo
    data.attackInfo.oldScore = curBattleData.oldScore
    data.attackInfo.scoreChange = curBattleData.newScore - curBattleData.oldScore
    data.defendInfo = curBattleData.defendInfo
    data.defendInfo.oldScore = curBattleData.nRivalOldScore
    data.defendInfo.scoreChange = curBattleData.nRivalNewScore - curBattleData.nRivalOldScore
    data.oldRank = curBattleData.oldRank
    data.curRank = curBattleData.curRank
    ui_window_mgr:ShowModule("ui_arena_battle_result_normal", nil, nil, data)
end


function SetBattleInfoByRsp(msg)
    local curBattleData = arena_common_data.GetBattleData(msg.nArenaID) or {}
    if not curBattleData.enemyInfo 
            or not curBattleData.enemyInfo.roleID 
            or curBattleData.enemyInfo.roleID ~= msg.nRivalRoleID then
        Warning0("enemyData is error, msg.nRivalRoleID:" .. msg.nRivalRoleID)
        return
    end
    curBattleData.isVictory = msg.bWinFlag
    curBattleData.oldScore = msg.nOldScore
    curBattleData.newScore = msg.nNewScore
    curBattleData.nRivalOldScore = msg.nRivalOldScore
    curBattleData.nRivalNewScore = msg.nRivalNewScore
    curBattleData.oldRank = msg.nOldRank
    curBattleData.curRank = msg.nNewRank
    curBattleData.isGetBattleRsp = true
    arena_common_data.SetBattleData(msg.nArenaID, curBattleData)
    
    local arenaCfg = GetArenaCfg(msg.nArenaID)
    if not arenaCfg then
        return
    end
    if arenaCfg.TeamNum <= 1 then
        --除了车轮战走正常逻辑
        return
    end
    --车轮战
    OpenStormBattleResult(msg, curBattleData)
end

--打开车轮战结算界面
function OpenStormBattleResult(msg, curBattleData)
    GetSkipBattle(curBattleData.arenaType)
    local serData = {}
    serData.msg = msg
    serData.attack_team = curBattleData.attackLineup
    serData.defend_team = curBattleData.defendLineup
    serData.enemyInfo = curBattleData.enemyInfo
    ui_window_mgr:ShowModule("ui_arena_battle_result_wheel",nil,nil, serData)
end

--endregion

function ClearData()
    arena_common_data.Clear()
end
event.Register(event.USER_DATA_RESET, ClearData)




