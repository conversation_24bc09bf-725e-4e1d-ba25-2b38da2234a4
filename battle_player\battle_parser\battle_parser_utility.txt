local math     = math
local string   = string
local tostring = tostring
local table    = table
local pairs    = pairs
local ipairs   = ipairs
local require  = require
local print    = print

local battle_data = require "battle_data"
local game_scheme = require "game_scheme"
local battle_config = require "battle_config"
local lang = require "lang"
local const = require "const"
local log = require "log"
local util = require "util"
local version_mgr = require "version_mgr"

local BattleEvent = CS.War.Battle.BattleEvent
local BattlePlayer = CS.War.Battle.BattlePlayer
local Debug = CS.UnityEngine.Debug
local Application = CS.UnityEngine.Application

-- 英雄阵营克制
local CampRestraint = nil

module("battle_parser_utility")

damageHpOffset = {x = 0, y = 0}
multiDamageHpOffset = {x = 0, y = 100}

local DODGE_ID = 48

function OnRequireBubbleText(bubbleId)
    local config = game_scheme:BubbleMap_0(bubbleId)
    if config then

        local c = 0
        for i,d in pairs(config.nLangID.data) do
            c = c+1
        end

        if c > 0 then
            local index = BattlePlayer.Instance:RandomRange(0, c)
            return lang.Get(config.nLangID.data[index])
        else
            return nil
        end
    end
end
--BattleEvent.OnRequireBubbleText = OnRequireBubbleText

function SetRewards(winnerId, loserId, winnerRewards, loserRewards)
    battle_data.winner = winnerId
    battle_data.loser = loserId
    --log.Error("SetRewards winnerId:"..winnerId..",loserId:"..loserId..",winnerRewards:")
    local battle_manager = require "battle_manager"
    local id = battle_data.GetRoleID()
    local playerId = battle_data.GetMyRoldID()
    -- print("SetRewards winnerId:"..winnerId..",loserId:"..loserId..",roleId:"..id)
    if winnerId == id or winnerId == playerId then
        battle_data.victory = true

        
    local battle_manager = require "battle_manager"
    local common_pb = require "common_new_pb"
    if not (battle_manager.GetLastStageType() == common_pb.GameGoal or battle_manager.GetLastStageType() == common_pb.EquipEctype
            or battle_manager.GetLastStageType() == common_pb.EquipEctypeZhuanShu) then
        if winnerRewards then
            
                for i = 1, #winnerRewards do
                    local rewardContext = winnerRewards[i]
                    local reward_mgr = require "reward_mgr"
                    if rewardContext.itemID == 0 and rewardContext.rewardID~=0 then
                        local rewards = reward_mgr.GetRewardGoodsList(rewardContext.rewardID)
                        battle_data.rewards = reward_mgr.MergeRewardList(battle_data.rewards,rewards)
                    else
                        table.insert(battle_data.rewards, {id = rewardContext.itemID, num = rewardContext.number, rewardID = rewardContext.rewardID, ItemFlag = rewardContext.ItemFlag})
                    end
                end
        end
    end

    elseif loserId == id then
        battle_data.victory = false

        if loserRewards then 
            for i = 1, #loserRewards do
                local rewardContext = loserRewards[i]
                table.insert(battle_data.rewards, {id = rewardContext.itemID, num = rewardContext.number, rewardID = rewardContext.rewardID,ItemFlag = rewardContext.ItemFlag})
            end
        end
    end
end


--------------------------------------------------------------
function AppendHudAverage(player, palID, segment, deltaHp, deltaMp, deltaSp, from, dmg)
    local dhp = math.floor(deltaHp/segment)
    local dmp = math.floor(deltaMp/segment)
    local dsp = math.floor(deltaSp/segment)

    -- if(deltaHp ~= 0) then
    --     Debug.LogError("平均血量变化 targets:"..tostring(palID)..",总血量:"..tostring(deltaHp))
    -- end
    for i = 1, segment - 1 do
        if version_mgr.CheckSvnTrunkVersion(25380) then
            -- if(deltaHp ~= 0) then
            --     Debug.LogError("平均血量变化 targets:"..tostring(palID)..",segment:"..tostring(i)..",deltaHp:"..tostring(dhp)..",from:"..tostring(from))
            -- end
            player:AppendHud(palID, dhp, dmp, dsp, i-1, from)
        else
            player:AppendHud(palID, dhp, dmp, dsp, i-1)
        end
    end
    if version_mgr.CheckSvnTrunkVersion(25380) then
        -- if(deltaHp ~= 0) then
        --     Debug.LogError("平均血量变化 targets:"..tostring(palID)..",segment:"..tostring(segment)..",deltaHp:"..tostring(deltaHp-dhp*(segment-1))..",from:"..tostring(from))
        -- end
        player:AppendHud(palID, deltaHp-dhp*(segment-1), deltaMp-dmp*(segment-1), deltaSp-dsp*(segment-1), segment-1, from, dmg)
    else
        player:AppendHud(palID, deltaHp-dhp*(segment-1), deltaMp-dmp*(segment-1), deltaSp-dsp*(segment-1), segment-1)
    end
end

function AppendMultiSegmentFloatTextInfo(player, palID, segment, style, strNumber, offsetY, multiSegmentStyle, multiSegmentFloatText)
    if not const.Open_Battle_Damage_MultiSegment_Statistics then
        return
    end

    -- Application.version, '1.0.48',2020/3/4
    if not player.AppendMultiSegmentFloatTextInfo then
        return
    end

    local segmentIdx = segment - 1
    player:AppendMultiSegmentFloatTextInfo(palID, style, strNumber, 0, 0, offsetY, multiSegmentFloatText, segmentIdx, multiSegmentStyle)
end

function AppendMultiSegmentFloatText(player)
    if not const.Open_Battle_Damage_MultiSegment_Statistics then
        return
    end

    -- Application.version, '1.0.48',2020/3/4
    if not player.AppendMultiSegmentFloatText then
        return
    end
    return player:AppendMultiSegmentFloatText()
end

function CombineNumberPrefix(number, prefix)
    if prefix == nil then
        return tostring(number)
    else
        return prefix..tostring(number)
    end
end

function CombineStringPrefix(number, prefix)
    if prefix == nil then
        return tostring(number)
    else
        return prefix..tostring(number)
    end
end

function ApppendFloatTextAverage(player, palID, segment, style, number, offsetY)
    ApppendFloatTextAverageWithPrefix(player, palID, segment, style, number, nil, offsetY)
end

function ApppendFloatTextAverageWithPrefix(player, palID, segment, style, number, prefix, offsetY)
    if number ~= 0 then
        if offsetY == nil then
            offsetY = 30
        end
        local multiSegmentFloatText = AppendMultiSegmentFloatText(player)
        local segmentIdx,multiStyle

        local averageNumber = math.floor(number/segment)
        local strAverageNumber = tostring(averageNumber)
        local i
        for i = 1, segment - 1 do
            player:AppendFloatText(palID, style, CombineStringPrefix(strAverageNumber, prefix), 0, 0, offsetY, i-1)

            if const.Open_Battle_Damage_MultiSegment_Statistics then
                if i == 1 then
                    multiStyle = "multistart"
                else
                    multiStyle = "multikeep"
                end
                AppendMultiSegmentFloatTextInfo(player, palID, i, style, 
                    CombineNumberPrefix(averageNumber * i, prefix), multiDamageHpOffset.y, multiStyle, multiSegmentFloatText)
            end
        end
        player:AppendFloatText(palID, style, CombineNumberPrefix(number - averageNumber*(segment-1), prefix),
         0, 0, offsetY, segment-1) 

        if const.Open_Battle_Damage_MultiSegment_Statistics then
            AppendMultiSegmentFloatTextInfo(player, palID, segment, style, 
                    CombineNumberPrefix(number, prefix), multiDamageHpOffset.y, "multiend", multiSegmentFloatText)
        end
    end
end

function GetFloatTextSign(number)
    if number >= 0 then
        return "+"
    else
        return ""
    end
end

-- 暴击飘字样式
function GetFloatTextCriticalStyle(critical,breakTrend)
    -- if not version_mgr.CheckSvnTrunkVersion(106612) then--TODO 版本号根据出包时确定的客户端版本号填写
    if critical and breakTrend then
        return "breakTrend_critical"
    elseif critical then
        return "critical"
    elseif breakTrend then
        return "breakTrend"
    end
    -- else
    --     if critical then
    --         return "critical"
    --     end
    -- end
    return nil
end

local Language = 
{
    [lang.ZH] = lang.ZH,
    [lang.EN] = lang.EN,
}

function GetMissTextStyle()
    local style, text
    local ftCfg = game_scheme:FloatText_0(DODGE_ID)
    if ftCfg ~= nil then
        local lang = require "lang"
        style = ftCfg.strStyle .. "_" .. (Language[lang.USE_LANG] or lang.EN)
        text = lang.Get(ftCfg.strText)
    end
    return style, text
end

function ParCampRestraint()
    if CampRestraint ~= nil then
        return
    end
    CampRestraint = {}
    -- 2#3#4#1#6#5
    local restraintIds = game_scheme:InitBattleProp_0(302)
    if restraintIds == nil then
        Debug.LogError("阵营克制关系(302)未配置")
        return nil
    end
    -- 阵营克制关系链（1堕落2超人3科技4自然5宇宙6神明）,(1-6表示从左到右的列位置所表示的阵营所克制的阵营)
    -- 由于位置关系目前与英雄阵营一一对应（1堕落，2超人，3机械，4自然，5律者，6神明），为简化代码
    -- 直接使用位置关系替代阵营，若后期策划修改阵营id（小概率）或阵营克制关系配置方式再修改算法
    local i
    local count = restraintIds.szParam.count
    local data = restraintIds.szParam.data
    for i=0,count - 1 do
        --由位置获得阵营
        local campIdx = i + 1
        local restraintMap = CampRestraint[campIdx]
        if restraintMap == nil then
            restraintMap = {}
            CampRestraint[campIdx] = restraintMap
        end
        restraintMap[data[i]] = true
    end
end

-- 克制飘字样式
function GetFloatTextRestraintStyle(caster, palID)
    if CampRestraint == nil then
        ParCampRestraint()
    end
    local restraintMap = CampRestraint[caster]
    if restraintMap == nil then
        return nil
    end
    if restraintMap[palID] then
        return "camprestriant"
    end
end

function GetFloatTextStyle(caster, palID, critical, breakTrend)
    -- print("GetFloatTextStyle caster:"..tostring(caster)..",palID:"..palID..",critical:"..tostring(critical))
    local style = GetFloatTextCriticalStyle(critical, breakTrend)
    if style == nil then
        local casterHero = battle_data.GetHeroByID(caster)
        local palHero = battle_data.GetHeroByID(palID)
        if casterHero and palHero then
            style = GetFloatTextRestraintStyle(casterHero.numProp.Type, palHero.numProp.Type)
        end
    end
    if style == nil then
        style = "normal"
    end
    return style
end

function GetHitRes(modulCfg, defaultHitRes, skillCfg)
    local hitRes = nil 
    if util.CanEnableMoveAttack() then
        hitRes = battle_config.GetConfigField(skillCfg, "nSkillKind", nil)
    end
    if string.empty(hitRes) then
        hitRes = battle_config.GetConfigField(modulCfg, "hit", defaultHitRes)
    end

    return hitRes
end

function GetSkillRes(skillCfg, defaultSkillRes, skinID)
    -- 读取技能表现资源
    local skillRes = battle_config.GetConfigField(skillCfg, "strPath", battle_config.defaultAtk)
    if not util.CanEnableMoveAttack() then
        -- 没有卡牌碰撞功能的老包再额外读取技能资源，与新包的技能表现不一致，没有配置则使用原始数据
        skillRes = battle_config.GetConfigField(skillCfg, "addStrPath", skillRes)
    end
    if skinID and skinID>0 then
        local skinCfg = game_scheme:Skin_0(skinID)
        if skinCfg then
            local SkillNum = battle_config.GetConfigField(skinCfg, "SkillNum")
            if SkillNum then
                local skinSkillCfg = game_scheme:SkinSkill_0(skillCfg.nSkillID)
                skillRes = battle_config.GetConfigField(skinSkillCfg, "strPath"..SkillNum, skillRes)
            end
        end
    end
    return skillRes
end

function AppendHitForSegments(player, palID, segment, modulCfg, skillCfg)
    local hitRes = GetHitRes(modulCfg, battle_config.defaultHit, skillCfg)

    for i = 1, segment do
        player:AppendAction(palID, hitRes, i-1)
    end
end

function AppendHitAndDieForSegments(player, palID, segment, modulCfg, defaultDieRes, skillCfg)
    local hitRes = GetHitRes(modulCfg, battle_config.defaultHit, skillCfg)
    local dieRes = battle_config.GetConfigField(modulCfg, "die", defaultDieRes)

    for i = 1, segment - 1 do
        player:AppendAction(palID, hitRes, i-1)
    end
    AppendDieForSegment(player, palID, segment, dieRes)
end

--添加多端伤害和假死行为
function AppendHitAndFakeDeadForSegments(player, palID, segment, modulCfg, defaultFakeDeadRes, skillCfg)
    local hitRes = GetHitRes(modulCfg, battle_config.defaultHit, skillCfg)
    local fakeDeadRes = defaultFakeDeadRes

    for i = 1, segment - 1 do
        player:AppendAction(palID, hitRes, i-1)
    end
    local episodeAction = player:AppendAction(palID, fakeDeadRes, segment-1)
    if episodeAction and episodeAction.SetSkillProperty then
        episodeAction:SetSkillProperty("die", 1)
    end
end
--添加假死行为
function AppendFakeDeadForSegments(player, palID, segment, defaultFakeDeadRes)
    local episodeAction = player:AppendAction(palID, defaultFakeDeadRes, segment - 1) -----假死多段伤害为0时，添加新的Episode
    -- local episodeAction = player:AppendAction(palID, fakeDeadRes, segment-1)
    if episodeAction and episodeAction.SetSkillProperty then
        episodeAction:SetSkillProperty("die", 1)
    end
end

function AppendDieForSegment(player, palID, segment, dieRes)
    local episodeAction = player:AppendAction(palID, dieRes, segment-1)
    if episodeAction and episodeAction.SetSkillProperty then
        episodeAction:SetSkillProperty("die", 1)
    end
end

-- 获取第 palKilled 次连胜资源路径
function GetWinningStreakResPath(srcPalKill)
    local killEffect = game_scheme:KillEffect_0(srcPalKill)
    if killEffect == nil then
        Debug.LogWarning("未配置连胜："..srcPalKill)
        return
    end
    local particleRes = killEffect["assetPath_"..lang.USE_LANG]
    if particleRes == nil then
        particleRes = killEffect["assetPath_en"]
    end
    local enParticleRes = killEffect["assetPath_en"]
    local zhParticleRes = killEffect["assetPath_zh"]
    local oversea_res =require"oversea_res"
    if(oversea_res.ISOverSeaLang())then
        particleRes = oversea_res.GetResPath(killEffect["assetPath_zh"]) or killEffect
    end
    if particleRes == nil then
        Debug.LogError("连胜次数："..srcPalKill.."未配置资源路径")
        return
    end
    return particleRes,enParticleRes,zhParticleRes
end

-- caster 对 palID 实现 srcPalKill 次连胜
function AppendWinningStreak(player, caster, palID, srcPalKill, segment, delayTime)
    if not player.AppendWinningStreak then
        return
    end
    if battle_data.IsWeapon(caster) then
        -- log.Error("武器:",caster,",不显示", srcPalKill, "连胜飘字")
        -- 武器不触发连胜飘字
        return
    end
    local casterPalSide = battle_data.GetPalSide(caster)
    local palSide = battle_data.GetPalSide(palID)
    if casterPalSide == palSide and casterPalSide ~= battle_data.PalSideType.Unknown then
        -- 同队英雄击杀不计入连胜，e.g. BOSS会击杀/吞噬自己小怪
        return
    end

    -- 2021/7/27 超过 6 连胜，显示 6 连胜
    if srcPalKill > 6 then
        srcPalKill = 6
    end
    local particleRes,enParticleRes,zhParticleRes = GetWinningStreakResPath(srcPalKill)
    if not particleRes then
        local casterHero = battle_data.GetHeroByID(caster)
        local palHero = battle_data.GetHeroByID(palID)
        if not casterHero then return log.Error("casterHero is nil") end
            log.Error("caster:",caster,lang.Get(casterHero.cfg.nameID),",pos:",casterHero.pos, "对",
            palID,lang.Get(palHero.cfg.nameID),",pos:",palHero.pos,"实现",srcPalKill, "次连胜未找到效果资源")
        return
    end
    local callBack = function(asset)
        if player then
            player:AppendWinningStreak(caster, palID, particleRes, delayTime, 0, 0, segment-1)
        end
    end
    local oversea_res=require"oversea_res"
    if oversea_res.IsOverSeaLangAndHaveRes(zhParticleRes) then 
        oversea_res.LoadSprite(zhParticleRes, "battle_parser_utility", callBack, true)
    else
        player:AppendWinningStreak(caster, palID, enParticleRes, delayTime, 0, 0, segment-1)
    end
end

--[[
获取 palID 击杀数
{
    {[id] = {kill = 0}},
}
]]
function AddPalKill(palInfoSet, palID)
    local palInfo = palInfoSet[palID]
    if palInfo == nil then
        palInfo =
        {
            kill = 0
        }
        palInfoSet[palID] = palInfo
    end
    palInfo.kill = palInfo.kill + 1
end

function GetPalKill(palInfoSet, palID)
    local palInfo = palInfoSet[palID]
    if palInfo == nil then
        return 0
    end
    return palInfo.kill
end

function AppendBuffEffectScore(buffID, buffLevel)
    local buffCfg = game_scheme:Buff_0(buffID, buffLevel)
    if buffCfg and buffCfg.unLightEfficiencyID and buffCfg.unLightEfficiencyID ~= 0 then
        local particleCfg = game_scheme:Particle_0(buffCfg.unLightEfficiencyID)
        if particleCfg and particleCfg.effectScore > 0 then
            if const.Open_Battle_SkillEffect_Log then
                Debug.LogError("AppendBuffEffectScore buffID:"..buffID..",buffLevel:"..buffLevel..","..tostring(particleCfg.strResPath))
            end
            battle_data.AppendSkillEffectScore(particleCfg.effectScore)
        end
    end
end

function AppendAddBuffPackage(player, targetId, buffId, buffLevel, statisticBuff,palID,segmentIndex)
    if not segmentIndex then
        segmentIndex = 0
    end
    local buffCfg = game_scheme:Buff_0(buffId, buffLevel)
    local icon = battle_config.GetBuffIconData(buffCfg)
    local particleType, particleRes = battle_config.GetBuffFxData(buffCfg)
    local style, text, colors = battle_config.GetBuffFloatTextData(buffCfg)

    local battle_data = require "battle_data"
    local hero = battle_data.GetHeroByID(palID or targetId)
    if hero and hero.skinID and hero.skinID > 0 then
        local skinCfg = game_scheme:Skin_0(hero.skinID)
        if skinCfg then
            local buffNum = battle_config.GetConfigField(skinCfg, "BuffNum")
            if buffNum and buffNum > 0 then
                local skinBuffConfig = game_scheme:SkinBuff_0(buffId,buffLevel)
                if skinBuffConfig then
                    local unLightEfficiencyID = battle_config.GetConfigField(skinBuffConfig, "unLightEfficiencyID" .. buffNum)
                    if unLightEfficiencyID and unLightEfficiencyID > 0 then
                        particleType, particleRes = battle_config.GetParticle(unLightEfficiencyID)
                    end
                end
            end
        end
    end
    --log.Error("AppendAddBuffPackage",targetId, buffId, particleType, style, text, buffLevel, statisticBuff, particleRes)
    if icon then
        local iconIds = util.SplitString(icon,"#")
        if player.AppendAddBuffArray then
            if iconIds and #iconIds > 1 then
                for k,v in ipairs(iconIds) do
                    player:AppendAddBuffArray(targetId, buffId, v, segmentIndex)
                end
            else
                player:AppendAddBuff(targetId, buffId, icon, segmentIndex)
            end
        else
            if iconIds and #iconIds > 1 then
                player:AppendAddBuff(targetId, buffId, iconIds[1], segmentIndex)
            else
                player:AppendAddBuff(targetId, buffId, icon, segmentIndex)
            end
        end
    end

    if particleType == 2 then
        player:AppendAddSustainedBuffFx(targetId, buffId, buffLevel, particleRes, segmentIndex)
    elseif particleType == 3 then
        player:AppendBuffFx(targetId, particleRes, segmentIndex)
    end

    if style then
        ------ print("AppendFloatText",palID, buffId,style, text, buffLevel, statisticBuff)
        --根据版本号调用接口
        if version_mgr.CheckSvnTrunkVersion(69027) then
            player:AppendFloatText(targetId, style, text, 0, 0, -20 + math.random(-15, 15), segmentIndex, colors)
        else
            player:AppendFloatText(targetId, style, text, 0, 0, -20 + math.random(-15, 15), segmentIndex)
        end
    end

    if statisticBuff and statisticBuff == true then
        AppendBuffEffectScore(buffId, buffLevel)
    end
end

function AppendRemoveBuffPackage(player, targetId, buffId, buffLevel, segmentIndex)
    if not segment then
        segment = 0
    end
    local buffCfg = game_scheme:Buff_0(buffId, buffLevel)
    local icon = battle_config.GetBuffIconData(buffCfg)
    local particleType, particleRes = battle_config.GetBuffFxData(buffCfg)
    if icon then
        if player.AppendRemoveBuffArray then
            local iconIds = util.SplitString(icon,"#")
            if iconIds and #iconIds > 1 then
                for k,v in ipairs(iconIds) do
                    player:AppendRemoveBuffArray(targetId, buffId, v, segment)
                end
            else
                player:AppendRemoveBuff(targetId, buffId, segment)
            end
        else
            player:AppendRemoveBuff(targetId, buffId, segment)
        end
    end

    if particleType == 2 then
        player:AppendRemoveSustainedBuffFx(targetId, buffId, buffLevel, segment)
    end
end

local bEnableModifyHp = nil
function IsValidModifyHp(player)
    if bEnableModifyHp == nil then
        bEnableModifyHp = player.AppendModifyMaxHp and const.OPEN_MODIFY_HP
    end
    return bEnableModifyHp
end

function AppendModifyMaxHp(player, palID, maxHp, segmentIndex)
    if not IsValidModifyHp(player) then
        return
    end

    battle_data.UpdateHeroMaxHp(palID, maxHp)
    player:AppendModifyMaxHp(palID, maxHp, segmentIndex - 1)
end

function AppendModifyHp( player, palID, hp, segmentIndex )
    if not IsValidModifyHp(player) then
        return
    end

    battle_data.UpdateHeroHp(palID, hp)
    player:AppendModifyHp(palID, hp, segmentIndex - 1)
end

local isDimEffectValid = nil
function AppendDimEffect( player, casterID, targets, operation, segmentIndex)
    if battle_data.skipBattle then
        -- 跳过战斗，则不解析战斗变暗效果，因为跳过战斗不解析英雄数据，不向 C# 注入英雄数据，此处做解析，会查找不英雄报错（但不影响流程）
        return
    end

    if isDimEffectValid == nil then
        isDimEffectValid = (not not player.AppendDimEffect) and const.OPEN_BATTLE_DIM_EFFECT
    end
    if not isDimEffectValid then
        return
    end

    local targetsList = {}
    if targets ~= nil then
        local count = #targets
        local targetId
        for i=1,count do
            targetId = targets[i]
            targetsList[i] = targetId
            battle_data.AddLightTarget(targetId, operation, segmentIndex)
        end
    end
    if #targetsList > 0 then
        player:AppendDimEffect(casterID, targetsList, operation, segmentIndex - 1)
    end
end

function AppendReviveEpisode( player, casterID, targets, segmentIndex )
    if not player.AppendReviveEpisode then
        return
    end

    if #targets > 0 then
        player:AppendReviveEpisode(casterID, targets, segmentIndex - 1)
    end
end

function AppendBuffHitPackage(player, palID, buffId, buffLevel, statisticBuff,skillId,segmentIndex)
    local buffCfg = game_scheme:Buff_0(buffId, buffLevel)
    local particleType, particleRes = battle_config.GetBuffFxData(buffCfg)

    local battle_data = require "battle_data"
    local hero = battle_data.GetHeroByID(skillId or palID)
    if not hero or not hero.skinID or hero.skinID == 0 then
        hero = battle_data.GetHeroByID(palID)
    end
    if hero and hero.skinID and hero.skinID > 0 then
        local skinCfg = game_scheme:Skin_0(hero.skinID)
        if skinCfg then
            local buffNum = battle_config.GetConfigField(skinCfg, "BuffNum")
            if buffNum and buffNum > 0 then
                local skinBuffConfig = game_scheme:SkinBuff_0(buffId,buffLevel)
                if skinBuffConfig then
                    local unLightEfficiencyID = battle_config.GetConfigField(skinBuffConfig, "unLightEfficiencyID" .. buffNum)
                    if unLightEfficiencyID and unLightEfficiencyID > 0 then
                        particleType, particleRes = battle_config.GetParticle(unLightEfficiencyID)
                    end
                end
            end
        end
    end
    if particleType == 1 then
        player:AppendBuffFx(palID, particleRes,segmentIndex)
    end

    if statisticBuff == true then
        AppendBuffEffectScore(buffId, buffLevel)
    end
end

function AppendBubbleSkillPackage(player, palID, skillId)
    local text, soundId = battle_config.GetBubbleSkillData(player, skillId, palID)
    if text or soundId then
        if not text then
            text = ""
        end
        player:AppendTalkingBubble(palID, text, -1, soundId)
    end
end

function AppendBubbleDiePackage(player, palID, appended)
    if appended == false then
        -- dead bubble
        local hero = battle_data.GetHeroByID(palID)
        if(hero)then
            local text, soundId = battle_config.GetBubbleHeroData(player, hero.heroID)

            if text then
                player:AppendTalkingBubble(palID, text, segment-1, soundId)
                appended = true
            end
        end
    end
    return appended
end

function AppendCountDamage(player, palID, hurtSource)
    if not hurtSource then
        return
    end
    if version_mgr.CheckSvnTrunkVersion(38136) then
        for k,v in pairs(hurtSource) do
            player:AppendCountDamage(palID, k, v)
        end
    end
end