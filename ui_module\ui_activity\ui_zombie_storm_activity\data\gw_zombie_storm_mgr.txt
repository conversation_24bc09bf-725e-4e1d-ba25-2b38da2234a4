--- Created by nyz.
--- DateTime: 2024/5/30 12:13
--- Des:丧尸灾变的管理器

local require = require
local pairs = pairs
local ipairs = ipairs
local debug = debug
local os = os
local xpcall = xpcall
local game_scheme = require "game_scheme"
local gw_const = require "gw_const"
local net_ZombieApocalypse_module = require "net_ZombieApocalypse_module"
local event_ZombieApocalypse_define = require("event_ZombieApocalypse_define")
local table = table
local event = require "event"
local string = string
local log = require "log"
local GWConst = require "gw_const"
local gw_sand_event_define = require"gw_sand_event_define"
local ui_window_mgr = require "ui_window_mgr"
local util = require "util"
local GWG = GWG
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class GWZombieStormMgr
module("gw_zombie_storm_mgr")
local GWZombieStormMgr = {}
local self = GWZombieStormMgr --简化写法，静态类中直接也用Self获取自身
local EVENT_HANDLERS = {}

local RedDot = false --报名阶段的弱红点，因为现有活动弱红点不能满足需求

function GWZombieStormMgr.Init()
    --活动相关的，不卸载
    EVENT_HANDLERS =
    {
        [event_ZombieApocalypse_define.TMSG_ZOMBIEAPOCALYPSE_ACTIVITY_DATA_NTF] = self.OnSetActivityData,
        [event_ZombieApocalypse_define.TMSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_RSP] = self.OnSetActivityData,
        [event_ZombieApocalypse_define.TMSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_RSP] = self.GetActivityDataReq,
        [event_ZombieApocalypse_define.TMSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_RSP] = self.GetActivityDataReq,
        [event_ZombieApocalypse_define.TMSG_ZOMBIEAPOCALYPSE_CHAT_CHANGE_NTF] = self.SetChatData,
        [gw_sand_event_define.GW_SAND_ENTER] = self.GetPoisonData,
        [event_ZombieApocalypse_define.TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_RSP] = self.SetPoisonData,
        [event_ZombieApocalypse_define.TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_NTF] = self.SetPoisonData,
        [gw_sand_event_define.GW_SAND_FUNCTION_TRIGGER] = self.CreatePreviewLine,
    }
    self.RegisterEvents()
    RedDot = true
end

function GWZombieStormMgr.CreatePreviewLine(_, event_name, msg)
    local gw_sand_preview_mgr = require "gw_sand_preview_mgr"
    if event_name == "EnterSceneSandBox" then
        --TODO 在这里生成行军线
        if GWG.GWMgr.curScene ~= GWConst.ESceneType.Sand then
            return
        end
        local activityData = self.GetActivityData()
        local activityBaseState = activityData.activityRunningState
        local activityState = activityData.activityState
        if activityState == GWConst.EnZombieApocalypseState.enZombieApocalypseState_Running and activityBaseState == GWConst.EnZombieApocalypsePointState.enZombieApocalypsePointState_PreAttack then
            local basePosition = activityData.zombieBasePos --基地的坐标
            if not GWZombieStormMgr.previewLine then
                GWZombieStormMgr.previewLine = {}
            end
            for i,v in ipairs(activityData.attackPlayerPosList) do
                local startPosition = { x = basePosition.nPosX, y = basePosition.nPosY }
                local endPosition = { x = v.nPosX, y = v.nPosY }
                local key = gw_sand_preview_mgr.CreatePreviewLine(startPosition,endPosition,nil,nil,"FF2929")
                log.Error("key:"..key)
                table.insert(GWZombieStormMgr.previewLine,key)
            end
        else --不满足条件，则清空行军线
            if GWZombieStormMgr.previewLine then
                for i,v in ipairs(GWZombieStormMgr.previewLine) do
                    gw_sand_preview_mgr.RemovePreviewObject(v)
                end
                GWZombieStormMgr.previewLine = {}
            end
        end
    elseif event_name == "ExitSandBox" then
        if GWZombieStormMgr.previewLine then
            --for i,v in ipairs(GWZombieStormMgr.previewLine) do
            --    gw_sand_preview_helper:RemovePreviewObject(v)
            --end
            GWZombieStormMgr.previewLine = {}
        end
    end
end

function GWZombieStormMgr.SetChatData(_,msg)
    self.GetZombieStormData().SetChatData(msg)
end

--进入沙盘后立即获取中毒信息
function GWZombieStormMgr.GetPoisonData()
    net_ZombieApocalypse_module.MSG_ZOMBIEAPOCALYPSE_POISONEDINFO_REQ()
end

function GWZombieStormMgr.SetPoisonData(_,msg)
    self.GetZombieStormData().SetPoisonData(msg)
end

function GWZombieStormMgr.GetPoisonInfo()
    return self.GetZombieStormData().GetPoisonData()
end

--打点通用
function GWZombieStormMgr.ReportEvent(eventName, params)
    params = params or {}
    event.Trigger(event.GAME_EVENT_REPORT, eventName, params)
end

function GWZombieStormMgr.SetRedDotState(value)
    RedDot = value
end

function GWZombieStormMgr.GetRedDotState()
    return RedDot
end

function GWZombieStormMgr.GetActivityDataReq()
    self.OnGetActivityDataReq()
end

---------获取相关部件----------

function GWZombieStormMgr.GetZombieStormData()
    if not GWZombieStormMgr.zombieStormData then
        local GWZombieStormData = require "gw_zombie_storm_data"
        GWZombieStormMgr.zombieStormData = GWZombieStormData
        GWZombieStormMgr.zombieStormData.Init()
    end
    return GWZombieStormMgr.zombieStormData
end

function GWZombieStormMgr.GetZombieWaveCfg()
    if not GWZombieStormMgr.zombieWaveCfg then
        GWZombieStormMgr.zombieWaveCfg = {}
        local len = game_scheme:ZombieApocalypseWave_nums()
        for i=0,len - 1 do
            local item = game_scheme:ZombieApocalypseWave(i)
            if not GWZombieStormMgr.zombieWaveCfg[item.Stage] then
                GWZombieStormMgr.zombieWaveCfg[item.Stage] = {}
            end
            table.insert(GWZombieStormMgr.zombieWaveCfg[item.Stage],item)
        end
    end
    return GWZombieStormMgr.zombieWaveCfg
end

function GWZombieStormMgr.GetZombieWaveCount(stage)
    return self.GetZombieWaveCfg()[stage] and #self.GetZombieWaveCfg()[stage] or 0
end

function GWZombieStormMgr.GetZombieWaveMonster(stage)
    if not GWZombieStormMgr.monsterList then
        GWZombieStormMgr.monsterList = {}
    end
    if not GWZombieStormMgr.monsterList[stage] then
        GWZombieStormMgr.monsterList[stage] = {}
        local cache = {}
        local cfgList = GWZombieStormMgr.GetZombieWaveCfg()
        if cfgList and cfgList[stage] then
            for i,v in ipairs(GWZombieStormMgr.zombieWaveCfg[stage]) do
                for j=1,3 do
                    local temp = v[string.format2("EnhancedMonster{%s1}Config",j)]
                    if temp and temp ~= 0 then
                        if not cache[j] then
                            cache[j] = true
                            local data =
                            {
                                data = temp,
                                index = j,
                            }
                            table.insert(GWZombieStormMgr.monsterList[stage],data)
                        end
                    end
                end
            end
        end
    end
    return GWZombieStormMgr.monsterList[stage]
end

---------协议相关-------------start
function GWZombieStormMgr.RegisterEvents()
    for eventName,handler in pairs(EVENT_HANDLERS) do
        event.Register(eventName,handler)
    end
end

function GWZombieStormMgr.UnregisterEvents()
    for eventName,handler in pairs(EVENT_HANDLERS) do
        event.Unregister(eventName,handler)
    end
end

function GWZombieStormMgr.GetActivityData()
    return self.GetZombieStormData().GetActivityData()
end

function GWZombieStormMgr.GetChatData(strChat)
    return self.GetZombieStormData().GetChatData(strChat)
end

function GWZombieStormMgr.OnGetActivityDataReq(activityID)
    --todo 临时处理，假数据
    --local msg =
    --{
    --    nActivityId = activityID,
    --    tActivityData =
    --    {
    --        nState = GWConst.EnZombieApocalypseState.enZombieApocalypseState_Open,
    --        nRunningState = GWConst.EnZombieApocalypsePointState.enZombieApocalypsePointState_Prepare,
    --        nEntitySid = -1,
    --        nCluePoint = 1000,
    --        nMaxUnlockDifficulty = 4,
    --        nCooldownTime = 0,
    --        nAppointmentTime = 0,
    --        nAppointmentDifficulty = 0,
    --        nWave = 0,
    --        nCountdown = 1753146384, --这里取2025-06-30 09:06:24，如果超了记得更新
    --    }
    --}
    --self.OnSetActivityData(nil,msg)
    --下面的和服务器联调时再放出来
    local activityId = activityID or self.GetZombieStormData().GetActivityId()
    if activityId then
        net_ZombieApocalypse_module.MSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_REQ(activityId)
    end
end

function GWZombieStormMgr.OnCheckWarningTimer()
    local activityData = self.GetZombieStormData().GetActivityData()
    if activityData.activityState == GWConst.EnZombieApocalypseState.enZombieApocalypseState_Running then
        local finishTime = activityData.clockDown - os.server_time()
        if finishTime > 0 then
            local temp = ui_window_mgr:GetWindowObj("ui_main_slg")
            local showData =
            {
                uiParent = temp and temp.rtsf_tipsTrans or nil,
            }
            if temp and temp.rtsf_tipsTrans then
                ui_window_mgr:ShowModule("ui_zombie_storm_warning_timer_panel",nil,nil,showData)
            end
            return;
        end
        --end
    end
    ui_window_mgr:UnloadModule("ui_zombie_storm_warning_timer_panel")
end

function GWZombieStormMgr.OnSetActivityData(_,msg)
    self.GetZombieStormData().SetActivityData(msg)
    self.StartTimer()
    self.CreatePreviewLine(nil,"EnterSceneSandBox")
    if msg.tActivityData.nState == GWConst.EnZombieApocalypseState.enZombieApocalypseState_Running then
            local finishTime = msg.tActivityData.nCountdown - os.server_time()
            if finishTime > 0 then
                local temp = ui_window_mgr:GetWindowObj("ui_main_slg")
                local showData =
                {
                    uiParent = temp and temp.rtsf_tipsTrans or nil,
                }
                if temp and temp.rtsf_tipsTrans then
                    ui_window_mgr:ShowModule("ui_zombie_storm_warning_timer_panel",nil,nil,showData)
                end
                return;
            end
        --end
    end
    ui_window_mgr:UnloadModule("ui_zombie_storm_warning_timer_panel")
end

function GWZombieStormMgr.OnGetWaveList(stage)
    local activityId = self.GetZombieStormData().GetActivityId()
    local targetCfg = game_scheme:ZombieApocalypseDifficulty_0(stage)
    local alliance_user_data = require "alliance_user_data"
    local allAllianceList = alliance_user_data.GetAllianceAllMemberData()
    local playerList = {}
    for i,v in pairs(allAllianceList) do
        if v.level >= targetCfg.PlayerLevelRequired then
            table.insert(playerList,v.roleId)
        end
    end
    local sendData =
    {
        nActivityId = activityId,
        nDifficulty = stage,
        tRoleList = playerList
    }
    net_ZombieApocalypse_module.MSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_REQ(sendData)
end

--三个地方，三个计时器，会导致肉眼可见的计时器偏差，所以由这里统一计时，刷新其他界面
function GWZombieStormMgr.StartTimer()
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil;
    end
    local activityData = self.GetActivityData()
    local timeLeft = activityData.clockDown - os.server_time()
    if timeLeft > 0 then
        self.timeTicker = util.IntervalCall(1,function()
            timeLeft = timeLeft - 1;
            event.Trigger(event_ZombieApocalypse_define.ZOMBIE_STORM_TIME_UPDATE,timeLeft)
            if timeLeft <= 0 then
                if self.timeTicker then
                    util.RemoveDelayCall(self.timeTicker)
                    self.timeTicker = nil;
                end
            end
        end)
    end
end
---------协议相关-------------end
function GWZombieStormMgr.Dispose()
    self.UnregisterEvents()
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil;
    end
    RedDot = false
end

GWZombieStormMgr.Init()
return GWZombieStormMgr