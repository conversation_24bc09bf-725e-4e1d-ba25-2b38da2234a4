local require = require
local typeof = typeof

local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Canvas = CS.UnityEngine.Canvas
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable


module("ui_arena_main_rank_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenamainrank.prefab"

WidgetTable ={
	txt_openServerID = { path = "TopShow/txt_openServerID", type = Text, },
	btn_tipBtn = { path = "TopShow/btn_tipBtn", type = Button, event_name = "OnBtnTipBtnClickedProxy"},
	txt_arenaEndTime = { path = "TopShow/TimeBG/txt_arenaEndTime", type = Text, },
	ss_showDailyReward = { path = "TopShow/ss&btn&canv_showDailyReward", type = SpriteSwitcher, },
	btn_showDailyReward = { path = "TopShow/ss&btn&canv_showDailyReward", type = Button, event_name = "OnBtnShowDailyRewardClickedProxy"},
	canv_showDailyReward = { path = "TopShow/ss&btn&canv_showDailyReward", type = Canvas, },
	btn_Promotion = { path = "TopShow/btn_Promotion", type = Button, event_name = "OnBtnPromotionClickedProxy"},
	txt_arenaName = { path = "TopShow/txt_arenaName", type = Text, },
	scrItem_PlayerItem_1 = { path = "TopPlayerShow/scrItem_PlayerItem_1", type = ScrollRectItem, },
	scrItem_PlayerItem_2 = { path = "TopPlayerShow/scrItem_PlayerItem_2", type = ScrollRectItem, },
	scrItem_PlayerItem_3 = { path = "TopPlayerShow/scrItem_PlayerItem_3", type = ScrollRectItem, },
	srt_rankList = { path = "ranking/Viewport/srt_rankList", type = ScrollRectTable, },
	scrItem_selfInfo = { path = "BottomShow/scrItem_selfInfo", type = ScrollRectItem, },
	btn_showArenaReward = { path = "BottomShow/btn_showArenaReward", type = Button, event_name = "OnBtnShowArenaRewardClickedProxy"},
	btn_showRecordList = { path = "BottomShow/btn_showRecordList", type = Button, event_name = "OnBtnShowRecordListClickedProxy"},
	btn_setDefense = { path = "BottomShow/btn_setDefense", type = Button, event_name = "OnBtnSetDefenseClickedProxy"},
	btn_challenge = { path = "BottomShow/btn_challenge", type = Button, event_name = "OnBtnChallengeClickedProxy"},
	txt_challengeNum = { path = "BottomShow/txt_challengeNum", type = Text, },

}
