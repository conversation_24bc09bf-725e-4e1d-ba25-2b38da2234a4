local print = print
local require = require
local pairs = pairs
local table = table
local string = string
local coroutine = coroutine
local tostring = tostring
local typeof = typeof
local try = try
local package = package
local loadstring = loadstring
local type = type
local ipairs = ipairs
local math = math

local File = CS.System.IO.File
local Path = CS.System.IO.Path
local BaseLoader = CS.War.Base.BaseLoader
local Directory = CS.System.IO.Directory
local GameObject = CS.UnityEngine.GameObject
local DirectoryInfo = CS.System.IO.DirectoryInfo
local LuaManager = CS.War.Script.LuaManager
local WaitForSeconds = CS.UnityEngine.WaitForSeconds
local AssetBundle = CS.UnityEngine.AssetBundle
local AssetDatabase = CS.UnityEditor.AssetDatabase
local Object = CS.UnityEngine.Object
local Application = CS.UnityEngine.Application
local event = require "event"
local setmetatable = setmetatable
local PlayerPrefs = CS.UnityEngine.PlayerPrefs

module("casualgame_download_read_mgr")

local casualgame_download = {}

local http_inst = require "http_inst"
local dkjson = require "dkjson"
local util = require "util"
local log = require "log"
local cs_coroutine = require "cs_coroutine"
local casualgame_progress = require "casualgame_progress"
local new_tinygame_manager = require "new_tinygame_manager"
local casualgame_global = require "casualgame_global"
local const = require "const"
local files_version_mgr = require "files_version_mgr"

local MAX_DOWNLOAD = 10

-- 加载单个AB，重试时长为8秒
local AB_REQUEST_TIMEOUT = 8
-- 加载URL，重试时长为5秒
local URL_REQUEST_TIMEOUT = 5

---@type tiny_entry
local tiny_entry = require "tiny_entry"
-- 在XXX\TinyRes\update.txt路径下的
local TotalUpdateInNewCaching = nil
-- 在SteamAsset\XXX\TinyRes\update.txt路径下的
local TotalUpdateInSteamAsset = nil

---@type boolean 使用Asset的资源
local useLocalAsset = util.IsSimulateMultiTiny() and util.GetSimulateHybridCLRTinyLocalAB()
---@type boolean 使用Asset内的Lua资源
local useLocalLua = util.IsSimulateMultiTiny() and util.GetSimulateHybridCLRTinyLocalLua()

--- 获取本地的旧版Update.txt文件 : XXX\TinyRes\update.txt
function FetchTotalLocalUpdateConfig(callBack)
    local taskCount = 2
    local taskCompleted = function()
        taskCount = taskCount - 1
        if taskCount < 1 then
            callBack()
        end
    end
    local fileName = casualgame_download.GetUpdateFileNameByApkUpdateJson()
    local inNewCachingPath = casualgame_download.GetPatchUpdatePath() .. fileName
    if File.Exists(inNewCachingPath) then
        local str = File.ReadAllText(inNewCachingPath)
        local tmpConfig = dkjson.decode(str)
        if tmpConfig ~= nil and tmpConfig.key2_mark_files_url_map ~= nil then
            TotalUpdateInNewCaching = {}
            for i, v in ipairs(tmpConfig.key2_mark_files_url_map) do
                TotalUpdateInNewCaching[v.GameName] = v.Version
            end
        end
    end
    -- 读取获取streamasset的资源
    local streamassetUpdate = Path.Combine(casualgame_global.GetStreamAssetPath(), fileName)
    -- local tmpFileExists = File.Exists(streamassetUpdate)
    -- tiny_entry.Warning("FetchTotalLocalUpdateConfig>> File.Exists(streamassetUpdate) = ", tmpFileExists,
    --     streamassetUpdate)
    tiny_entry.RequestFromURL(streamassetUpdate, 2, function(str, error, bytes)
        if string.IsNullOrEmpty(error) then
            local tmpConfig = dkjson.decode(bytes)
            if tmpConfig ~= nil and tmpConfig.key2_mark_files_url_map ~= nil then
                TotalUpdateInSteamAsset = {}
                for i, v in ipairs(tmpConfig.key2_mark_files_url_map) do
                    TotalUpdateInSteamAsset[v.GameName] = v.Version
                end
            end
        else
            tiny_entry.Warning("FetchTotalLocalUpdateConfig>> Get streamassetUpdate Error", error, streamassetUpdate)
        end
        taskCompleted()
    end)
    taskCompleted()
end

-- 下载小游戏
function casualgame_download:StartDownloadMiniGame(downloadOkCallback, downloadFailCallback, miniGameEndCb, onPropress)
    self.MiniGameEndCb = miniGameEndCb
    self.DownloadMinigameOkCallback = downloadOkCallback
    self.DownloadMiniGameFailCallback = downloadFailCallback
    self.onPropress = onPropress
    local taskCount = 2
    local taskCompleted = function()
        taskCount = taskCount - 1
        if taskCount < 1 then
            tiny_entry.Log("StartDownloadMiniGame>>", self.GameKey, "newKey2MarkFilesVer =", self.newKey2MarkFilesVer,
                "curKey2MarkFilesVer=", self.curKey2MarkFilesVer)
            -- 版本号检测完毕，检测file.txt文件
            if self.newKey2MarkFilesVer < 1 then
                self.newKey2MarkFilesVer = self.curKey2MarkFilesVer
            end
            self:CallFileMap(function()
                self:CheckFilesUpdate(function()
                    -- 这里如果进来，说明update.txt下载失败或者本地资源是最新版本，不需要下载
                    self:EndCb()
                end)
            end)
        end
    end
    -- 获取本地版本号
    self:GetUrlAndPath(taskCompleted)
    -- 获取服务器版本号
    self:CallUpdateJson(taskCompleted)
end

function casualgame_download:CallFileMap(callBack)
    tiny_entry.Log("CallFileMap>>>", self.GameKey)
    local taskCount = 2
    local taskCompleted = function()
        taskCount = taskCount - 1
        if taskCount < 1 then
            callBack()
        end
    end
    -- 获取本地file.txt文件
    self:CallLocalFileMap(taskCompleted)
    if self.newKey2MarkFilesVer > 0 then
        -- 获取服务器file.txt文件
        self:CallServerfileMap(taskCompleted)
    else
        taskCompleted()
    end
end

function casualgame_download:CallLocalFileMap(callBack)
    tiny_entry.Warning("CallLocalFileMap>>", self.GameKey, " curver version11-", self.curKey2MarkFilesVer,
        "-server version-", self.newKey2MarkFilesVer)
    if self.curKey2MarkFilesVer > 0 then                      -- 本地有update 文件还需判断有没有file文件
        if File.Exists(self.patchFilesFilePath) == false then -- or File.Exists(patchUpdateFilePath) == false
            tiny_entry.Warning("CallLocalFileMap>>", self.GameKey, "覆盖安装文件丢失")
            if Directory.Exists(self.rootPath) then
                Directory.Delete(self.rootPath, true)
            end
            if File.Exists(self.patchUpdateFilePath) then
                tiny_entry.Warning("CallLocalFileMap>>", self.GameKey, "删除update")
                File.Delete(self.patchUpdateFilePath)
            end
            self:GetPatchSaveRootPath()
        end
    end
    if File.Exists(self.patchFilesFilePath) then
        tiny_entry.Warning("CallLocalFileMap>>", self.GameKey, "可读写路径存在files ")
        self.LocalKey2MarkFilesMap = util.ReadJson(self.patchFilesFilePath)
    end

    -- 否则使用 StreamAsset内的
    local casualgame_global = require "casualgame_global"
    local streamassetFilePath = Path.Combine(casualgame_global.GetStreamAssetPath(), self.GameKey, "file.txt")
    -- local tmpFileExists = File.Exists(streamassetFilePath)
    -- tiny_entry.Warning("CallLocalFileMap>> File.Exists(tmpFileExists) = ", tmpFileExists, streamassetFilePath)
    tiny_entry.RequestFromURL(streamassetFilePath, 2, function(str, error, bytes)
        if string.IsNullOrEmpty(error) then
            self.SteamAssetKey2MarkFilesMap = dkjson.decode(bytes)
        else
            tiny_entry.Warning("CallLocalFileMap>>>>- streamassetFilePath Error=", error, self.GameKey,
                streamassetFilePath)
        end
        callBack()
    end)
end

function casualgame_download:IsReadyABListDone()
    local httpFinish, listDone = true, true

    for k, v in pairs(self.ReadyABList) do
        if v.waitForDone then
            httpFinish = false
        end
        if v.state == 0 then
            listDone = false
        end
    end
    return httpFinish, listDone
end

function casualgame_download:CheckDownload()
    self.checkdownloadcor = cs_coroutine.start(function()
        while true do
            local httpFinish, listDone = self:IsReadyABListDone()
            if httpFinish then
                if not listDone then
                    self:DownloadMiniGame() -- 重试
                else
                    break
                end
            else
                coroutine.yield(1)
            end
        end

        self:EndCb()
    end)
end

-- 小游戏下载
function casualgame_download:DownloadMiniGame()
    if self.LocalKey2MarkFilesMap == nil then
        self.LocalKey2MarkFilesMap = {}
    end

    for k, v in pairs(self.ReadyABList) do
        v.waitForDone = false
    end

    local HttpCount = 0
    for k, v in pairs(self.ReadyABList) do
        if v.state == 0 then
            if HttpCount == MAX_DOWNLOAD then
                break
            end
            HttpCount = HttpCount + 1
            v.waitForDone = true

            local abPath = self.rootPath .. k
            local requestAsync = http_inst.Request_Timeout(v.url, AB_REQUEST_TIMEOUT,
                function(text, errMsg, bytesData, downloadedBytes, request)
                    v.waitForDone = false

                    if string.empty(errMsg) then
                        local rDir = Path.GetDirectoryName(abPath)
                        if not File.Exists(rDir) then
                            local driectoryInfo = DirectoryInfo(rDir)
                            driectoryInfo:Create()
                        end

                        File.WriteAllBytes(abPath, bytesData)

                        local crc = util.Crc32File(abPath)
                        if tostring(crc) ~= tostring(v.value.CRC32) then
                            tiny_entry.Error("DownloadMiniGame error : ", self.GameKey, abPath, crc, v.value.CRC32)
                            self:ReportDownloadFailEvent(DownloadState.abRes, v.url)
                            self:DownLoadTimeOut()
                            return
                        else
                            v.state = 1
                            self.downloadSize = self.downloadSize + v.value.Size
                            self.downCount = self.downCount + 1
                            -- tiny_entry.Warning("trk--DownloadMiniGame-self.totalCount",self.totalCount)
                            if self.onPropress then
                                self.onPropress(self.downloadSize, self.totalSize, self.downCount, self.totalCount)
                            end
                            self.LocalKey2MarkFilesMap[v.key] = v.value

                            util.WriteJson(self.patchFilesFilePath, self.LocalKey2MarkFilesMap)
                        end
                    else
                        tiny_entry.Error(" DownloadMiniGame ", self.GameKey, v.url, errMsg, #bytesData, v.value.Size)
                        self:ReportDownloadFailEvent(DownloadState.abRes, v.url)
                        self:DownLoadTimeOut()
                        return
                    end
                end)
        end
    end
end

function casualgame_download:DownLoadTimeOut()
    tiny_entry.Warning("DownLoadTimeOutDownLoadTimeOut", self.GameKey)
    -- local files_version_mgr = require "files_version_mgr"
    -- files_version_mgr.ChangeStandAlongReskey("")
    -- files_version_mgr.SetLocalStandAloneReskey("")
    -- const.OPEN_MINIGAME = false
    if self.checkdownloadcor then
        cs_coroutine.stop(self.checkdownloadcor)
    end
    if self.MiniGameEndCb then
        self.MiniGameEndCb(0)
        self.MiniGameEndCb = nil
    end
    if self.DownloadMiniGameFailCallback then
        self.DownloadMiniGameFailCallback()
        self.DownloadMiniGameFailCallback = nil
    end
end

-- 比对 本地files 和服务器files 文件 筛选要下载的文件
function casualgame_download:CheckFilesUpdate(callBack)
    if tiny_entry.SkipPreload then
        tiny_entry.Log("CheckFilesUpdate>>>> SkipPreload", self.GameKey)
        callBack()
        return
    end
    tiny_entry.Log("CheckFilesUpdate>>>>", self.GameKey)
    local abListData = {}
    -- 找到本地的最高版本
    if self.LocalKey2MarkFilesMap ~= nil then -- 可读写路径下 files文件
        for k, v in pairs(self.LocalKey2MarkFilesMap) do
            local abName = v.Name
            abListData[abName] = { v.Version, v.Size }
        end
    end
    if self.SteamAssetKey2MarkFilesMap ~= nil then
        for k, v in pairs(self.SteamAssetKey2MarkFilesMap.Entries) do
            local streamABName = v.Name
            if not abListData[streamABName] or abListData[streamABName][1] < v.Version then
                abListData[streamABName] = { v.Version, v.Size }
            end
        end
    end

    self.ReadyABList = {}
    self.totalSize = 0
    self.totalCount = 0
    self.downCount = 0
    self.downloadSize = 0
    if self.ServerKey2MarkFilesMap ~= nil then
        for i = 1, #(self.ServerKey2MarkFilesMap.Entries) do
            local abName = self.ServerKey2MarkFilesMap.Entries[i].Name
            if not self.ReadyABList[abName] and
                (not abListData[abName] or abListData[abName][1] < self.ServerKey2MarkFilesMap.Entries[i].Version) then
                local url = Path.Combine(self.resourceUrl, self.GameKey, self.ServerKey2MarkFilesMap.Entries[i].Version,
                    abName)
                self.ReadyABList[abName] = {
                    url = url,
                    key = abName,
                    value = self.ServerKey2MarkFilesMap.Entries[i],
                    state = 0,
                    waitForDone = false
                }
                self.totalSize = self.totalSize + self.ServerKey2MarkFilesMap.Entries[i].Size
                self.totalCount = self.totalCount + 1
                -- tiny_entry.Warning("trk--ServerKey2MarkFilesMap-self.totalCount",self.totalCount)
            end
        end
    end

    tiny_entry.Warning("trk--CheckFilesUpdate-self.totalCount", self.totalCount, self.GameKey)
    -- 需要下载
    if self.totalCount > 0 then
        if self.onPropress then
            self.onPropress(self.downloadSize, self.totalSize, self.downCount, self.totalCount)
        end
        local casualgame_global = require "casualgame_global"
        casualgame_global.ReportMiniGameEvent(self.GameKey, true, "download_start", self.totalCount)

        -- 之后的步骤需要其他回调
        self:DownloadMiniGame()
        self:CheckDownload()
    else
        callBack()
    end
end

function casualgame_download:WritePesitFiles()
    tiny_entry.Log("WritePesitFiles>>>>", self.GameKey)
    local tmpFilesMap = {}
    for i = 1, #(self.ServerKey2MarkFilesMap.Entries) do
        tmpFilesMap[self.ServerKey2MarkFilesMap.Entries[i].Name] = self.ServerKey2MarkFilesMap.Entries[i]
    end
    util.WriteJson(self.patchFilesFilePath, tmpFilesMap)
    self.LocalKey2MarkFilesMap = tmpFilesMap

    self:Initialize()

    tiny_entry.Log("WritePesitFiles>>>>  newUpdateJsonMap=", self.newUpdateJsonMap, self.GameKey)
    if self.newUpdateJsonMap then
        util.WriteJson(self.patchUpdateFilePath, self.newUpdateJsonMap)
    end

    -- self:InitGameLua(nil)
end

function casualgame_download:EndCb()
    tiny_entry.Warning("trk---EndCb.totalCount", self.totalCount, self.GameKey)
    tiny_entry.Warning("trk---EndCb.downCount", self.downCount, self.GameKey)
    local casualgame_global = require "casualgame_global"
    casualgame_global.ReportMiniGameEvent(self.GameKey, true, "download_end", self.totalCount)

    if self.SteamAssetKey2MarkFilesMap == nil and self.LocalKey2MarkFilesMap == nil then
        if self.ServerKey2MarkFilesMap ~= nil and self.ServerKey2MarkFilesMap.Entries ~= nil then
            self:WritePesitFiles()
        end
    else
        self:Initialize()

        if self.newUpdateJsonMap then
            util.WriteJson(self.patchUpdateFilePath, self.newUpdateJsonMap)
        end

        -- self:InitGameLua(nil)
    end
    if self.downCount >= self.totalCount then
        if self.DownloadMinigameOkCallback then
            self.DownloadMinigameOkCallback()
            self.DownloadMinigameOkCallback = nil
        end
    else
        if self.DownloadMiniGameFailCallback then
            self.DownloadMiniGameFailCallback()
            self.DownloadMiniGameFailCallback = nil
        end
    end
end

function casualgame_download:LuaCb(rk)
    if Application.isEditor then
        -- -- 重定向Lua调用小游戏C#类型
        -- if util.IsSimulateHybridCLR() or util.IsSimulateMultiTiny() then
        --     util.SimulateHybridCLRRedirectionAssembly()
        -- end

        local scr_list = {}
        local scr_obj_list = {}

        local keys = rk.keys
        local len = keys.Count
        for i = 0, len - 1 do
            local kv = keys[i]
            scr_list[kv.key] = kv.value
        end

        local simulateLocalLua = util.GetSimulateHybridCLRTinyLocalLua()
        local loader = function(fileName)
            if scr_obj_list[fileName] then
                return scr_obj_list[fileName]
            end

            if simulateLocalLua then
                local script = util.SimulateHybridCLRLoadLocalLua(fileName)
                if script then
                    local res = loadstring(script, fileName)
                    scr_obj_list[fileName] = res
                    return res
                end
            else
                local script = scr_list[fileName]
                if script then
                    scr_list[fileName] = nil
                    local res = loadstring(script, fileName)
                    scr_obj_list[fileName] = res
                    return res
                end
            end

            return nil
        end
        table.insert(package.loaders, loader)
    else
        local LuaManagerInst = LuaManager.Instance
        local bAddRKLoader = LuaManagerInst and LuaManagerInst.AddRKLoader
        LuaManagerInst:AddRKLoader(rk)
    end

    if self.MiniGameEndCb then
        self.MiniGameEndCb(0)
        self.MiniGameEndCb = nil
    end

    local lower_res_key = string.lower(self.GameKey)
    try {
        function()
            local m = require(lower_res_key .. "_enter")
            if self.initOvercallBack then
                self.initOvercallBack(m)
            end
            tiny_entry.Warning("lower_res_key required", lower_res_key)
        end,
        catch = function(e)
            tiny_entry.Error("lower_res_key error", lower_res_key, e)
        end
    }
end

-- function casualgame_download:InitLua()
--     local casualGameRoot = new_tinygame_manager.GetCasualGameRoot(self.GameKey)
--     local asset_name = casualGameRoot..'/'..self.GameKey..'/'..self.GameKey..'_exec.asset'
--     -- MiniGameLoader.LoadAllAssets(asset_name, function (abName, asset)
--     --     LuaCb(asset[0])
--     -- end)
--     --
--     local tiny_stand_alone_mgr = require "tiny_stand_alone_mgr"
--     tiny_stand_alone_mgr.SetReskey(self.GameKey)
--     local minigamecontrol = tiny_stand_alone_mgr.GetTableFilePath("MiniGameControl")
--     local minimamemevelmontrol = tiny_stand_alone_mgr.GetTableFilePath("MiniGameLevelControl")
--     local lang = tiny_stand_alone_mgr.GetTableFilePath("Lang")

--     self:LoadAllAssets(minigamecontrol, function(abName, asset)
--         print("jydebug===","minigamecontrol ",minigamecontrol)
--         self:UnloadAsset(minigamecontrol)
--         self:LoadAllAssets(minimamemevelmontrol, function(abName, asset)
--             print("jydebug===","MiniGameLevelControl ",minimamemevelmontrol)
--             self:UnloadAsset(minimamemevelmontrol)
--             self:LoadAllAssets(lang, function(abName, asset)
--                 print("jydebug===","Lang ",lang)
--                 self:UnloadAsset(lang)
--                 self:LoadAllAssets(asset_name, function(abName, asset)
--                     print("jydebug===","asset_name ",asset_name)
--                     self:UnloadAsset(asset_name)
--                     if asset[0] then
--                         self:LuaCb(asset[0])
--                     end
--                 end)
--             end)
--         end)
--     end)
--     -- LoadAssetbundle("casualgame/drawbreak/prefab/levelcomponents/level_9.prefab", "casualgame/drawbreak/prefab/levelcomponents/level_9.prefab", function(abName, asset)
--     --     UnloadAsset("casualgame/drawbreak/prefab/levelcomponents/level_9.prefab")
--     --     GameObject.Instantiate(asset)
--     -- end)

-- end
function casualgame_download:InitGameLua(callback)
    --本地lua跳过加载_exec
    if Application.isEditor and useLocalLua then
        local scr_obj_list = {}

        local loader = function(fileName)
            if scr_obj_list[fileName] then
                return scr_obj_list[fileName]
            end
            local script = util.SimulateHybridCLRLoadLocalLua(fileName)
            if script then
                local res = loadstring(script, fileName)
                scr_obj_list[fileName] = res
                return res
            end
            return nil
        end
        table.insert(package.loaders, loader)
        if callback then
            callback()
        end
        return
    end
    local casualGameRoot = new_tinygame_manager.GetCasualGameRoot(self.GameKey)
    local asset_name = casualGameRoot .. '/' .. self.GameKey .. '/' .. self.GameKey .. '_exec.asset'

    self:LoadAllAssets(asset_name, function(abName, asset)
        print("jydebug===", "asset_name ", asset_name)
        self:UnloadAsset(asset_name)
        if asset[0] then
            self:LuaCb(asset[0])
            if callback then
                callback()
            end
        end
    end)
end

function casualgame_download:InitGameLua_Ip(callback)
    self.initOvercallBack = callback
    local asset_name = 'casualgame/' .. self.GameKey .. '/' .. self.GameKey .. '_exec.asset'

    self:LoadAllAssets(asset_name, function(abName, asset)
        print("jydebug===", "asset_name ", asset_name)
        self:UnloadAsset(asset_name)
        if asset[0] then
            self:LuaCb(asset[0])
        end
    end)
end

function casualgame_download:GetUpdateFileNameByApkUpdateJson()
    local files_version_mgr = require "files_version_mgr"
    local str = files_version_mgr.ApkUpdateConfigTryGetValue("p_stand_alone_env")
    return "update" .. (str == nil and "" or str) .. ".txt"
end

-- -- 检测小游戏完整性
-- function casualgame_download:CheckMiniGameComplete(checkCallback)
--     if checkCallback == nil then
--         print("传入checkCallback参数nil")
--         return
--     end
--     self.CheckMiniGameCompleteCallBack = checkCallback
--     self:GetUrlAndPath()
--     self:CallUpdateJson()
-- end
function casualgame_download:GetProgress()
    return self.downloadSize, self.totalSize, self.downCount, self.totalCount
end

function casualgame_download:ParseUpdateVersion(oriData)
    if oriData ~= nil and oriData.key2_mark_files_url_map ~= nil then
        for i, v in ipairs(oriData.key2_mark_files_url_map) do
            if v.GameName == self.GameKey then
                return v.Version
            end
        end
    end
    return 0
end

-- 获取本地版本
function casualgame_download:GetUrlAndPath(callBack)
    tiny_entry.Log("GetUrlAndPath>>>>", self.GameKey)
    local tmpPatchUpdate = nil
    local tmpSAUpdate = nil
    local taskCount = 2
    local taskCompleted = function()
        taskCount = taskCount - 1
        if taskCount < 1 then
            -- 获取本地最高版本
            local patchVersion = self:ParseUpdateVersion(tmpPatchUpdate)
            local SAVersion = self:ParseUpdateVersion(tmpSAUpdate)
            self.curKey2MarkFilesVer = math.max(patchVersion, SAVersion)
            if TotalUpdateInNewCaching ~= nil then
                self.curKey2MarkFilesVer =
                    math.max(self.curKey2MarkFilesVer, TotalUpdateInNewCaching[self.GameKey] or 0)
            end
            if TotalUpdateInSteamAsset ~= nil then
                self.curKey2MarkFilesVer =
                    math.max(self.curKey2MarkFilesVer, TotalUpdateInSteamAsset[self.GameKey] or 0)
            end
            tiny_entry.Log("getLocalUpdateCall>>> curKey2MarkFilesVer=", self.curKey2MarkFilesVer, "patchVersion=",
                patchVersion, "StreamAssetVersion=", SAVersion, self.GameKey)
            callBack()
        end
    end
    if File.Exists(self.patchUpdateFilePath) then
        tmpPatchUpdate = util.ReadJson(self.patchUpdateFilePath)
    end
    -- 读取获取streamasset的资源
    local streamassetUpdate = Path.Combine(casualgame_global.GetStreamAssetPath(), self.GameKey,
        self:GetUpdateFileNameByApkUpdateJson())
    -- local tmpFileExists = File.Exists(streamassetUpdate)
    -- tiny_entry.Warning("GetUrlAndPath>> File.Exists(tmpFileExists) = ", tmpFileExists, streamassetUpdate)
    tiny_entry.RequestFromURL(streamassetUpdate, 2, function(str, error, bytes)
        if string.IsNullOrEmpty(error) then
            tmpSAUpdate = dkjson.decode(bytes)
        else
            tiny_entry.Warning("GetUrlAndPath>>>>- streamassetUpdate Error=", error, self.GameKey, streamassetUpdate)
        end
        taskCompleted()
    end)
    taskCompleted()
end

-- 获取服务器file文件
function casualgame_download:CallServerfileMap(callBack)
    if tiny_entry.SkipPreload then
        tiny_entry.Log("CallServerfileMap>>>> SkipPreload", self.GameKey)
        callBack()
        return
    end
    self.newKey2MarkFilesVerUrl =
        self.resourceUrl .. "/" .. self.GameKey .. "/" .. tostring(self.newKey2MarkFilesVer) .. "/file.txt"
    tiny_entry.Log("CallServerfileMap>>>>", self.GameKey, self.newKey2MarkFilesVerUrl)
    http_inst.Request_Timeout(self.newKey2MarkFilesVerUrl, URL_REQUEST_TIMEOUT, function(str, hasError, bytes)
        if string.empty(hasError) then
            self.ServerKey2MarkFilesMap = dkjson.decode(bytes)
        else
            self:ReportDownloadFailEvent(DownloadState.fileJson, self.newKey2MarkFilesVerUrl)
        end
        callBack()
    end)
end

-- 获取服务器版本
function casualgame_download:CallUpdateJson(callBack)
    if tiny_entry.SkipPreload then
        tiny_entry.Log("CallUpdateJson>>>> SkipPreload", self.GameKey)
        callBack()
        return
    end
    tiny_entry.Log("CallUpdateJson>>>>", self.GameKey, self.updateJsonUrl)
    http_inst.Request_Timeout(self.updateJsonUrl, URL_REQUEST_TIMEOUT, function(str, hasError, bytes)
        if string.empty(hasError) then
            self.newUpdateJsonMap = dkjson.decode(bytes)
            self.newKey2MarkFilesVer = self:ParseUpdateVersion(self.newUpdateJsonMap)
            tiny_entry.Log("CallUpdateJson>>>>", self.GameKey, "newKey2MarkFilesVer==", self.newKey2MarkFilesVer)
        else
            local last_role_id = PlayerPrefs.GetFloat("last_role_id", -1)
            tiny_entry.Warning("trktest-hasError-last_role_id", last_role_id, self.GameKey)
            self:ReportDownloadFailEvent(DownloadState.updateJson, self.updateJsonUrl)
        end
        callBack()
    end)
end

-- 小游戏保存路径
function casualgame_download:GetPatchSaveRootPath()
    local miniGamePatchRootPath = self:GetPatchUpdatePath() .. self.GameKey .. "/"
    if not Directory.Exists(miniGamePatchRootPath) then
        Directory.CreateDirectory(miniGamePatchRootPath)
    end
    return miniGamePatchRootPath
end

-- 小游戏保存路径
function casualgame_download:GetPatchUpdatePath()
    local persistentRelativeUrl = BaseLoader.GetPersistentRelativeUrl()
    persistentRelativeUrl = string.gsub(persistentRelativeUrl, "file://", "")
    local newCachingPath = persistentRelativeUrl .. "/NewCaching/"
    local miniGamePatchRootPath = newCachingPath .. "TinyRes/"
    if not Directory.Exists(miniGamePatchRootPath) then
        Directory.CreateDirectory(miniGamePatchRootPath)
    end
    return miniGamePatchRootPath
end

function casualgame_download:CheckStreamAssetContainResKey(reskey)
    local path = casualgame_global.GetStreamAssetPath() .. reskey .. "/file.txt"
    local www = casualgame_global.DownLoadStreamAssetsRes(path, 0.5)
    if www then
        return true
    else
        return false
    end
end

------------------------- ab 加载部分 -----------

function casualgame_download:Initialize()
    self:OnUpdate()

    local filePatch = {}
    print("Initializeself.rootPath===", self.rootPath)
    print("Initializeself.patchFilesFilePath===", self.patchFilesFilePath)
    if self.LocalKey2MarkFilesMap ~= nil then
        local str = ""
        local count = 0
        for k, v in pairs(self.LocalKey2MarkFilesMap) do
            str = str .. "," .. k
            filePatch[k] = { v, false }
            count = count + 1
        end
        print("#LocalKey2MarkFilesMap", count)
        print("k str===", str)
    end

    if self.SteamAssetKey2MarkFilesMap ~= nil then
        for k, v in pairs(self.SteamAssetKey2MarkFilesMap.Entries) do
            local streamABName = v.Name
            if not self.LocalKey2MarkFilesMap or not self.LocalKey2MarkFilesMap[streamABName] or self.LocalKey2MarkFilesMap[streamABName].Version <
                v.Version then
                filePatch[streamABName] = { v, true }
            end
        end
    end

    local count = 0
    local abNamestr = ""
    for k, v in pairs(filePatch) do
        self:AddEntry(k, v[1].Dependencies, v[2], v[1].UnityCRC, v[1].Version)
        abNamestr = abNamestr .. "," .. k
        count = count + 1
    end
    print("abNamestr===", abNamestr)
    print("#ABEntries", count)
end

function casualgame_download:AddEntry(abName, Dependencies, streamingAssets, UnityCRC, Version)
    local rABLoadInfo = {}
    if streamingAssets then
        rABLoadInfo.ABPath = Path.Combine(casualgame_global.GetStreamAssetPath(false), self.GameKey, abName)
    else
        rABLoadInfo.ABPath = self.rootPath .. abName
    end
    rABLoadInfo.streamingAssets = streamingAssets
    rABLoadInfo.ABName = abName
    rABLoadInfo.ABDependNames = Dependencies
    rABLoadInfo.IsLoading = false
    rABLoadInfo.IsLoadCompleted = false
    rABLoadInfo.CacheAsset = nil
    rABLoadInfo.RefCount = 0
    rABLoadInfo.UnityCRC = UnityCRC
    rABLoadInfo.Version = Version

    self.ABEntries[abName] = rABLoadInfo
end

-----------------------------------------------------------
function casualgame_download:OnUpdate()
    -- cs_coroutine.start(function()
    --     self:UpdateEvent()
    --     coroutine.yield(WaitForSeconds(0.2))
    --     self:OnUpdate()
    -- end)
    if not self.intervalCall then
        self.intervalCall = util.IntervalCall(0.2, function()
            self:UpdateEvent()
        end)
    end
end

function casualgame_download:UpdateEvent()
    if self.mIsLoadingRefCount == 0 then
        for k, v in pairs(self.ABEntries) do
            self:AutoCheckUnloadAsset(v)
        end
    end
end

function casualgame_download:AutoCheckUnloadAsset(rAssetLoadEntry)
    if rAssetLoadEntry.RefCount == 0 then
        if rAssetLoadEntry.CacheAsset then
            tiny_entry.Log("---Auto Real unload assetbundle: ", rAssetLoadEntry.ABName, self.GameKey)
            rAssetLoadEntry.CacheAsset:Unload(true)
            rAssetLoadEntry.CacheAsset = nil
            rAssetLoadEntry.IsLoadCompleted = false
            rAssetLoadEntry.IsLoading = false
        end
    end
end

function casualgame_download:GetABEntryDependencies(rABLoadEntry)
    local rABAllDependenceEntries = {}
    self:GetABEntryAllDependencies(rABLoadEntry, rABAllDependenceEntries)
    return rABAllDependenceEntries
end

function casualgame_download:GetABEntryAllDependencies(rABLoadEntry, rABAllDependenceEntries)
    for i = 1, #(rABLoadEntry.ABDependNames) do
        local rDependenceEntry = self.ABEntries[rABLoadEntry.ABDependNames[i]]
        if rDependenceEntry then
            self:GetABEntryAllDependencies(rDependenceEntry, rABAllDependenceEntries)
        end
    end

    table.insert(rABAllDependenceEntries, rABLoadEntry)
end

function casualgame_download:LoadAsset_Async(rRequest)
    cs_coroutine.start(function()
        self.mIsLoadingRefCount = self.mIsLoadingRefCount + 1

        if not self.ABEntries[rRequest.Path] then
            print("LoadAsset_Async No Res ", rRequest.Path)
            rRequest.Asset = nil
            self.mIsLoadingRefCount = self.mIsLoadingRefCount - 1
            if rRequest.Completed then
                rRequest.Completed(rRequest)
            end
            return
        end

        -- 得到该资源的所有依赖项
        local rABAllDependenceEntries = self:GetABEntryDependencies(self.ABEntries[rRequest.Path])
        for i = 1, #rABAllDependenceEntries do
            rABAllDependenceEntries[i].RefCount = rABAllDependenceEntries[i].RefCount + 1
        end

        for i = 1, #rABAllDependenceEntries - 1 do
            -- 构建依赖项的Request
            local rDependenceLoaderRequest = {
                Path = rABAllDependenceEntries[i].ABName,
                AssetName = "",
                IsScene = false,
                IsSimulate = rRequest.IsSimulate,
                IsLoadAllAssets = rRequest.IsLoadAllAssets,
                Completed = nil
            }
            self:LoadAssetAsync_OneEntry(rDependenceLoaderRequest, rABAllDependenceEntries[i])
        end

        coroutine.yield(self:LoadAssetAsync_OneEntry(rRequest, rABAllDependenceEntries[#rABAllDependenceEntries]))
        self.mIsLoadingRefCount = self.mIsLoadingRefCount - 1
        print("---rRequest.Asset3: ", rRequest.Asset == nil)
    end)
end

function casualgame_download:LoadAssetAsync_OneEntry(rRequest, rABLoadEntry)
    cs_coroutine.start(function()
        -- 确认未加载完成并且正在被加载、一直等待其加载完成
        while (rABLoadEntry.IsLoading and not rABLoadEntry.IsLoadCompleted) do
            if (rABLoadEntry.RefCount == 0) then
                return
            end
            coroutine.yield(0)
        end

        -- 从缓存的Assetbundle里面加载资源
        rABLoadEntry.IsLoading = true
        rABLoadEntry.IsLoadCompleted = false
        coroutine.yield(self:LoadAssetObjectAsync(rRequest, rABLoadEntry))
        rABLoadEntry.IsLoading = false
        rABLoadEntry.IsLoadCompleted = true

        print("---rRequest.Asset2: ", (rRequest.Asset == nil))
        -- 如果判断此时的RefCount为0的话，那么就unload掉该项资源
        self:AutoCheckUnloadAsset(rABLoadEntry)
    end)
end

function casualgame_download:LoadAssetObjectAsync(rRequest, rAssetLoadEntry)
    cs_coroutine.start(function()
        local rAssetLoadUrl = rAssetLoadEntry.ABPath

        if rRequest.IsSimulate and Application.isEditor then
            print("---Simulate Load ab: ", rAssetLoadUrl)
            if rRequest.AssetName and rRequest.AssetName ~= "" then
                if (not rRequest.IsLoadAllAssets) then
                    local rAssetPaths = AssetDatabase.GetAssetPathsFromAssetBundleAndAssetName(rAssetLoadEntry.ABName,
                        rRequest.AssetName)
                    if rAssetPaths == nil or rAssetPaths.Length == 0 then
                        print("There is no asset with name \"" .. rRequest.AssetName .. "\" in " ..
                            rAssetLoadEntry.ABName)
                        return
                    end

                    -- SetEntityTag(rRequest.AssetName, rABRequest.asset)

                    if rRequest.Completed then
                        rRequest.Completed(rRequest)
                    end
                else
                    local rAssetPaths = AssetDatabase.GetAssetPathsFromAssetBundle(rAssetLoadEntry.ABName)
                    if rAssetPaths == nil or rAssetPaths.Length == 0 then
                        print("There is no asset with name \"" .. rRequest.AssetName .. "\" in " ..
                            rAssetLoadEntry.ABName)
                        return
                    end

                    for i = 0, rAssetPaths.Length - 1 do
                        local rAssetObj = AssetDatabase.LoadAssetAtPath(rAssetPaths:GetValue(i), typeof(Object))
                        rRequest.AllAssets = {}
                        if rAssetObj then
                            -- SetEntityTag(rAssetPaths:GetValue(i), rAssetObj)
                            rRequest.AllAssets[i] = rAssetObj
                        end
                    end

                    if rRequest.Completed then
                        rRequest.Completed(rRequest)
                    end
                end
            end
        else
            if not rAssetLoadEntry.CacheAsset then
                local ABEntry = self.ABEntries[rRequest.Path]
                -- 先加载本地的ab
                tiny_entry.Log("---Real Load ab: ", rAssetLoadUrl, ABEntry.UnityCRC,
                    "IsFromStreamingAssets=", ABEntry.streamingAssets)
                -- SteamingAsset特殊加载方式
                if ABEntry.streamingAssets then
                    local breakFlag = false
                    tiny_entry.RequestFromURL(rAssetLoadUrl, AB_REQUEST_TIMEOUT,
                        function(str, error, bytes)
                            if string.IsNullOrEmpty(error) then
                                rAssetLoadEntry.CacheAsset = AssetBundle.LoadFromMemory(bytes, ABEntry.UnityCRC)
                            end
                            breakFlag = true
                        end)

                    while not breakFlag do
                        coroutine.yield(0.01)
                    end
                else
                    local rABCreateRequest = AssetBundle.LoadFromFileAsync(rAssetLoadUrl,
                        self.ABEntries[rRequest.Path].UnityCRC)
                    -- 纠错
                    if not rABCreateRequest.assetBundle then
                        coroutine.yield(cs_coroutine.start(function()
                            local url = Path.Combine(self.resourceUrl, self.GameKey,
                                self.ABEntries[rRequest.Path].Version,
                                rRequest.Path)
                            local abPath = self.rootPath .. rRequest.Path
                            local checkState = true

                            local requestAsync = http_inst.Request_Timeout(url, AB_REQUEST_TIMEOUT,
                                function(text, errMsg, bytesData, downloadedBytes, request)
                                    checkState = false
                                    if string.empty(errMsg) then
                                        local rDir = Path.GetDirectoryName(abPath)
                                        if not File.Exists(rDir) then
                                            local driectoryInfo = DirectoryInfo(rDir)
                                            driectoryInfo:Create()
                                        end

                                        File.WriteAllBytes(abPath, bytesData)

                                        rABCreateRequest = AssetBundle.LoadFromFileAsync(abPath)
                                    else
                                        tiny_entry.Warning(" rABCreateRequest.assetBundle ", self.GameKey, errMsg,
                                            #bytesData)
                                    end
                                end)

                            while checkState do
                                coroutine.yield(0.01)
                            end
                        end))
                    end
                    coroutine.yield(rABCreateRequest)
                    rAssetLoadEntry.CacheAsset = rABCreateRequest.assetBundle
                end
            else
                print("---Load asset: ", rAssetLoadUrl)
            end

            -- 加载Object
            if rRequest.AssetName and rRequest.AssetName ~= "" then
                if (not rRequest.IsLoadAllAssets) then
                    local rABRequest = rAssetLoadEntry.CacheAsset:LoadAssetAsync(rRequest.AssetName)
                    coroutine.yield(rABRequest)

                    -- SetEntityTag(rRequest.AssetName, rABRequest.asset)

                    print("---rRequest.Asset1: ", (rABRequest.asset == nil), (rRequest.Asset == nil))
                    if rRequest.Completed then
                        rRequest.Completed(rRequest)
                    end
                else
                    coroutine.yield(self:LoadAllAssets_ByAssetbundle(rRequest, rAssetLoadEntry.CacheAsset))
                end
            end
        end
    end)
end

function casualgame_download:LoadAllAssets_ByAssetbundle(rRequest, rAssetbundle)
    cs_coroutine.start(function()
        if not util.IsObjNull(rAssetbundle) then
            local rAllAssetsRequest = rAssetbundle:LoadAllAssetsAsync()
            coroutine.yield(rAllAssetsRequest)

            local AssetNames = rAssetbundle:GetAllAssetNames()

            if rAllAssetsRequest.allAssets[0] then
                -- SetEntityTag(rRequest.Path, rAllAssetsRequest.allAssets[0])
            end
            rRequest.AllAssets = rAllAssetsRequest.allAssets

            if rRequest.Completed then
                rRequest.Completed(rRequest)
            end
        else
            if rRequest.Completed then
                rRequest.Completed(rRequest)
            end
            tiny_entry.Warning("rAssetbundle is nil", rRequest and rRequest.Path, self.GameKey)
        end
    end)
end

function casualgame_download:LoadAsset_FromFile(rRequest)
    self.mIsLoadingRefCount = self.mIsLoadingRefCount + 1

    if not self.ABEntries[rRequest.Path] then
        tiny_entry.Warning("Can not find assetbundle: --", rRequest.Path, self.GameKey)
        rRequest.Asset = nil
        self.mIsLoadingRefCount = self.mIsLoadingRefCount - 1
        return
    end

    -- 得到该资源的所有依赖项
    local rABAllDependenceEntries = self:GetABEntryDependencies(self.ABEntries[rRequest.Path])
    for i = 1, #rABAllDependenceEntries do
        rABAllDependenceEntries[i].RefCount = rABAllDependenceEntries[i].RefCount + 1
    end

    for i = 1, #rABAllDependenceEntries - 1 do
        -- 构建依赖项的Request
        local rDependenceLoaderRequest = {
            Path = rABAllDependenceEntries[i].ABName,
            AssetName = "",
            IsScene = false,
            IsSimulate = rRequest.IsSimulate,
            IsLoadAllAssets = rRequest.IsLoadAllAssets,
            Completed = nil
        }

        self:LoadAssetFromFile_OneEntry(rDependenceLoaderRequest, rABAllDependenceEntries[i])
    end

    self:LoadAssetFromFile_OneEntry(rRequest, rABAllDependenceEntries[#rABAllDependenceEntries])
    self.mIsLoadingRefCount = self.mIsLoadingRefCount - 1
end

function casualgame_download:LoadAssetFromFile_OneEntry(rRequest, rABLoadEntry)
    -- 从缓存的Assetbundle里面加载资源
    rABLoadEntry.IsLoading = true
    rABLoadEntry.IsLoadCompleted = false
    self:LoadAssetObjectFromFile(rRequest, rABLoadEntry)
    rABLoadEntry.IsLoading = false
    rABLoadEntry.IsLoadCompleted = true

    -- 如果判断此时的RefCount为0的话，那么就unload掉该项资源
    self:AutoCheckUnloadAsset(rABLoadEntry)
end

function casualgame_download:LoadAssetObjectFromFile(rRequest, rAssetLoadEntry)
    if (rAssetLoadEntry == nil) then
        tiny_entry.Error("[casualgame_mgr] LoadAssetObjectFromFile() > 'rAssetLoadEntry' is nil", self.GameKey)
        return
    end

    local rAssetLoadUrl = rAssetLoadEntry.ABPath

    if rRequest.IsSimulate and Application.isEditor then
        print("---Simulate Load ab: ", rAssetLoadUrl)
        if rRequest.AssetName and rRequest.AssetName ~= "" then
            if (not rRequest.IsLoadAllAssets) then
                local rAssetPaths = AssetDatabase.GetAssetPathsFromAssetBundleAndAssetName(rAssetLoadEntry.ABName,
                    rRequest.AssetName)
                if rAssetPaths == nil or rAssetPaths.Length == 0 then
                    print("There is no asset with name \"" .. rRequest.AssetName .. "\" in " .. rAssetLoadEntry.ABName)
                    return
                end
                rRequest.Asset = AssetDatabase.LoadMainAssetAtPath(rAssetPaths:GetValue(0))

                -- SetEntityTag(rAssetPaths:GetValue(0), rRequest.Asset)

                if rRequest.Completed then
                    rRequest.Completed(rRequest)
                end
            else
                local rAssetPaths = AssetDatabase.GetAssetPathsFromAssetBundle(rAssetLoadEntry.ABName)
                if rAssetPaths == nil or rAssetPaths.Length == 0 then
                    print("There is no asset with name \"" .. rRequest.AssetName .. "\" in " .. rAssetLoadEntry.ABName)
                    return
                end

                for i = 0, rAssetPaths.Length - 1 do
                    local rAssetObj = AssetDatabase.LoadAssetAtPath(rAssetPaths:GetValue(i), typeof(Object))
                    rRequest.AllAssets = {}
                    if rAssetObj then
                        rRequest.AllAssets[i] = rAssetObj
                    end
                end

                if rRequest.AllAssets[0] then
                    -- SetEntityTag(rRequest.Path, rRequest.AllAssets[0])
                end

                if rRequest.Completed then
                    rRequest.Completed(rRequest)
                end
            end
        end
    else
        if not rAssetLoadEntry.CacheAsset then
            print("---Real Load ab: ", rAssetLoadUrl)
            rAssetLoadEntry.CacheAsset = AssetBundle.LoadFromFile(rAssetLoadUrl)
        else
            print("---Load asset: ", rAssetLoadUrl)
        end

        -- 加载Object
        if rRequest.AssetName and rRequest.AssetName ~= "" then
            if (not rRequest.IsLoadAllAssets) then
                rRequest.Asset = rAssetLoadEntry.CacheAsset:LoadAsset(rRequest.AssetName)
                -- SetEntityTag(rRequest.AssetName, rRequest.Asset)
            else
                self:LoadAllAssets_ByAssetbundle_Sync(rRequest, rAssetLoadEntry.CacheAsset)
            end
        end
    end
end

function casualgame_download:LoadAllAssets_ByAssetbundle_Sync(rRequest, rAssetbundle)
    if not rAssetbundle then
        tiny_entry.Warning("rRequest.AssetName:nil name==", rRequest.AssetName, self.GameKey)
        return
    end
    rRequest.AllAssets = rAssetbundle:LoadAllAssets()
    if rRequest.AllAssets[0] then
        -- SetEntityTag(rRequest.Path, rRequest.AllAssets[0])
    end
end

------------------ 接口部分 ---------------
function casualgame_download:LoadAssetbundle(rAssetbundleName, rAssetName, callback)
    local rRequest = {
        Path = string.lower(rAssetbundleName),
        AssetName = string.lower(rAssetName),
        IsScene = false,
        IsSimulate = false,
        IsLoadAllAssets = false,
        Completed = function(data)
            callback(data.AssetName, data.Asset)
        end
    }

    self:LoadAsset_Async(rRequest)
end

function casualgame_download:LoadAssetbundleSimulate(rAssetbundleName, rAssetName, callback)
    local rRequest = {
        Path = string.lower(rAssetbundleName),
        AssetName = string.lower(rAssetName),
        IsScene = false,
        IsSimulate = true,
        IsLoadAllAssets = false,
        Completed = function(data)
            callback(data.AssetName, data.Asset)
        end
    }

    self:LoadAsset_Async(rRequest)
end

function casualgame_download:LoadAllAssets(rAssetbundleName, callback)
    local rRequest = {
        Path = string.lower(rAssetbundleName),
        AssetName = "AllAssets",
        IsScene = false,
        IsSimulate = false,
        IsLoadAllAssets = true,
        Completed = function(data)
            callback(data.AssetName, data.AllAssets)
        end
    }

    --加载Asset内的资源
    if useLocalAsset then
        local rAssetPaths = AssetDatabase.GetAssetPathsFromAssetBundle(rRequest.Path)
        if rAssetPaths ~= nil and rAssetPaths.Length > 0 then
            for i = 0, rAssetPaths.Length - 1 do
                local rAssetObj = AssetDatabase.LoadAssetAtPath(rAssetPaths:GetValue(i), typeof(Object))
                rRequest.AllAssets = {}
                if rAssetObj then
                    rRequest.AllAssets[i] = rAssetObj
                end
            end
        end
        if rRequest.Completed then
            rRequest.Completed(rRequest)
        end
        return
    end

    self:LoadAsset_Async(rRequest)
end

function casualgame_download:LoadAllAssetsSimulate(rAssetbundleName, callback)
    local rRequest = {
        Path = string.lower(rAssetbundleName),
        AssetName = "AllAssets",
        IsScene = false,
        IsSimulate = false,
        IsLoadAllAssets = true,
        Completed = function(data)
            callback(data.AssetName, data.AllAssets)
        end
    }

    self:LoadAsset_Async(rRequest)
end

function casualgame_download:LoadAssetFromFile(rAssetbundleName, rAssetName, callback)
    local rRequest = {
        Path = string.lower(rAssetbundleName),
        AssetName = string.lower(rAssetName),
        IsScene = false,
        IsSimulate = false,
        IsLoadAllAssets = false,
        Completed = nil
    }

    self:LoadAsset_FromFile(rRequest)

    callback(rRequest.AssetName, rRequest.Asset)
end

function casualgame_download:LoadAssetFromFileSimulate(rAssetbundleName, rAssetName, callback)
    local rRequest = {
        Path = string.lower(rAssetbundleName),
        AssetName = string.lower(rAssetName),
        IsScene = false,
        IsSimulate = true,
        IsLoadAllAssets = false,
        Completed = nil
    }

    self:LoadAsset_FromFile(rRequest)

    callback(rRequest.AssetName, rRequest.Asset)
end

function casualgame_download:LoadAllAssetsFromFile(rAssetbundleName, callback)
    local rRequest = {
        Path = string.lower(rAssetbundleName),
        AssetName = "AllAssets",
        IsScene = false,
        IsSimulate = false,
        IsLoadAllAssets = true,
        Completed = nil
    }

    self:LoadAsset_FromFile(rRequest)

    callback(rRequest.AssetName, rRequest.AllAssets)
end

function casualgame_download:LoadAllAssetsFromFileSimulate(rAssetbundleName, callback)
    local rRequest = {
        Path = string.lower(rAssetbundleName),
        AssetName = "AllAssets",
        IsScene = false,
        IsSimulate = true,
        IsLoadAllAssets = true,
        Completed = nil
    }

    self:LoadAsset_FromFile(rRequest)

    callback(rRequest.AssetName, rRequest.AllAssets)
end

function casualgame_download:UnloadAssetWithCall(rAssetbundleName, callBack)
    if not rAssetbundleName or not self.ABEntries[string.lower(rAssetbundleName)] then
        callBack()
        return
    end

    -- 得到该资源的所有依赖项
    local rABAllDependenceEntries = self:GetABEntryDependencies(self.ABEntries[string.lower(rAssetbundleName)])
    for i = 1, #rABAllDependenceEntries do
        rABAllDependenceEntries[i].RefCount = rABAllDependenceEntries[i].RefCount - 1
        if (rABAllDependenceEntries[i].RefCount < 0) then
            rABAllDependenceEntries[i].RefCount = 0
        end
        self:AutoCheckUnloadAsset(rABAllDependenceEntries[i])
    end
    callBack()
end

function casualgame_download:UnloadAsset(rAssetbundleName)
    if not rAssetbundleName then
        return
    end

    if not self.ABEntries[string.lower(rAssetbundleName)] then
        print("Can not find assetbundle: -- ", string.lower(rAssetbundleName))
        return
    end

    -- 得到该资源的所有依赖项
    local rABAllDependenceEntries = self:GetABEntryDependencies(self.ABEntries[string.lower(rAssetbundleName)])
    for i = 1, #rABAllDependenceEntries do
        rABAllDependenceEntries[i].RefCount = rABAllDependenceEntries[i].RefCount - 1
        -- print("===== rABAllDependenceEntries[i].RefCount : ", rABAllDependenceEntries[i].ABName, rABAllDependenceEntries[i].RefCount)
        if (rABAllDependenceEntries[i].RefCount < 0) then
            rABAllDependenceEntries[i].RefCount = 0
        end
    end
end

-- local path = GetEntityPath("tablecsv/Lang.csv")
-- path = "G:\zHero220216\Bin\Client\Assets\StreamingAssets\AssetBundles\CaualGame_Android_Assetbundles\SaveDog\tablecsv\Lang.csv"
--     or "G:\zHero220216\Bin\Client\NewCaching\TinyRes\SaveDog\tablecsv\Lang.csv"

function casualgame_download:GetEntityPath(abName)
    if not self.ABEntries[abName] then
        return ""
    end

    return self.ABEntries[abName].ABPath
end

-- function SetEntityTag(abName, asset)
--     -- if not GameTagInfo[abName] or not asset  then
--     --     return
--     -- end

--     -- for i=1, #GameTagInfo[abName] do
--     --     local obj = asset.transform:Find(GameTagInfo[abName][i].name)
--     --     obj.tag = GameTagInfo[abName][i].tag
--     -- end
-- end
function casualgame_download:ReportDownloadFailEvent(state, path)
    local reportMsg = {
        -- system_memory_size = util.GetSystemMemorySize(),
        mini_game_name = self.GameKey,
        mini_game_state = state, -- 下载失败的阶段
        path = path              -- 下载失败的资源路径
    }
    tiny_entry.Warning("上报 download_minigame_fail 事件", self.GameKey)
    event.EventReport("download_minigame_fail", reportMsg)
end

DownloadState = {
    updateJson = "updateJson", -- 下载update.txt 阶段
    fileJson = "fileJson",     -- 下载file.txt 阶段
    abRes = "abRes"            -- 下载ab资源阶段
}

local class = require "class"

local object = setmetatable({}, {
    __call = function()
        return {}
    end
})

local casualgame_download_Class = class(object, nil, casualgame_download)
--- 构造器
function casualgame_download:ctor(sha, reskey)
    self.ABEntries = {}
    self.rootPath = ""
    self.mIsLoadingRefCount = 0
    self.ReadyABList = {}
    self.ServerKey2MarkFilesMap = nil
    self.LocalKey2MarkFilesMap = nil
    self.SteamAssetKey2MarkFilesMap = nil
    self.newUpdateJsonMap = nil
    self.patchUpdateFilePath = nil
    self.patchFilesFilePath = nil
    self.curKey2MarkFilesVer = 0
    -- local patchTagFilePath
    self.downloadSize = 0
    self.totalSize = 0
    self.totalCount = 0
    self.downCount = 0

    self.updateJsonUrl = ""
    self.resourceUrl = ""
    self.MiniGameEndCb = nil
    self.newKey2MarkFilesVer = 0
    self.newKey2MarkFilesVerUrl = ""
    self.DownloadMinigameOkCallback = nil
    self.DownloadMiniGameFailCallback = nil
    self.onPropress = nil
    -- local GameTagInfo = {}

    self.GameKey = reskey
    self.initOvercallBack = nil
    self.intervalCall = nil
    self.checkdownloadcor = nil
    print("GameKeyGameKeyGameKey===", self.GameKey)

    self.rootPath = self:GetPatchSaveRootPath()
    self.patchUpdateFilePath = Path.Combine(self.rootPath, self:GetUpdateFileNameByApkUpdateJson())
    print("patchUpdateFilePath==============", self.patchUpdateFilePath)
    self.patchFilesFilePath = Path.Combine(self.rootPath, "files.txt")
    print("patchFilesFilePath==============", self.patchFilesFilePath)
    -- patchTagFilePath = Path.Combine(saveRootPath, "taginfo.txt")

    local url = files_version_mgr.GetStandAloneUrl()
    if url == nil or url == "" then -- 都没设置url 说明是内网或者editor
        self.updateJsonUrl = "http://172.16.126.185:3122/CasualGame_Pure_Res_105_002/TinyRes/TAndroid" .. "/" ..
            self.GameKey .. "/update.txt"
        self.resourceUrl = "http://172.16.126.185:3122/CasualGame_Pure_Res_105_002/TinyRes/TAndroid"
        print("server and Jenkins not set p_stand_alone_server_root_url", self.updateJsonUrl)
    else
        tiny_entry.Warning("finaly p_stand_alone_server_root_url==", url)
        local casualgame_global = require "casualgame_global"
        url = Path.Combine(url, casualgame_global.GetCurPlatformPath())
        self.updateJsonUrl = url .. "/" .. self.GameKey .. "/" .. self:GetUpdateFileNameByApkUpdateJson()
        self.resourceUrl = url
        tiny_entry.Warning("first start app read server update.tex", self.updateJsonUrl)
    end
end

function New(reskey)
    return casualgame_download_Class(reskey)
end
