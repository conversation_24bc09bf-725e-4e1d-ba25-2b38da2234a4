
local require = require
local table = table
local pairs = pairs
local ipairs = ipairs
local print = print 


local object = require "object"
local util = require "util"
local class = require "class"
local log = require "log"
local val = require "val"

local asset_loader = require "asset_loader"
local res_pool = require "res_pool"
local GameObjectPool = CS.War.Base.GameObjectPool
local CachedGameObjectPool = CS.War.Base.CachedGameObjectPool

local maxLoadingCount = 2

module("asset_load_mgr") 

local AssetLoadMgr  = {}

function AssetLoadMgr:ctor(selfType)
	

	self.loaders = {
		-- ["abname"] = {
		-- 	"asset_loader",
		-- 	1,
		-- 	withCache:true -- load state,
		-- },
	}
	self.loadingCount = 0

	-- self.waitLoadNextAsset = false
end
 
function SetMaxLoadingCount(count)
	maxLoadingCount = count
end

function GetAsyncWithCacheCallback(obj)
	--加载完成后直接释放，由 GameObjectPool 内部对其保持引用
	GameObjectPool.ReleaseWithCache(CachedGameObjectPool.Cached_Type_Character, obj)
	-- --print("Load Asset: GetAsyncWithCacheCallback -"..obj.name)
end

function AssetLoadMgr:LoadedAssetCallback(assetbundleName, assetName, assetObj)
	local loadvo  = self.loaders[assetbundleName]  

    if loadvo and loadvo.withCache then
        -- 请求对象池对象后直接release, 由对象池内部对其保持缓存，便于后续其他模块使用
        GameObjectPool.GetAsyncWithCache(CachedGameObjectPool.Cached_Type_Character, assetbundleName, nil,
            GetAsyncWithCacheCallback)
    end
	
	if loadvo and loadvo.withCacheLua then
        -- 请求对象池对象后直接release, 由对象池内部对其保持缓存，便于后续其他模块使用
		res_pool.get_res(assetbundleName, function(res_vo)
			res_pool.return_res(res_vo)
		end, nil, "asset_load_mgr")
	end
end

function AssetLoadMgr:DelayAutoLoadAsset()
	-- print("DelayAutoLoadAsset",util.get_len(self.loaders),self.loadingCount)
	-- util.DelayOneCallNoCoroutine("DelayAutoLoadAsset",function ()
	-- 	if self.loadingCount >= maxLoadingCount then
	-- 		self:DelayAutoLoadAsset()
	-- 	else
	-- 		self:AutoLoadAsset()
	-- 	end
	-- end,0.1)

	self.checkTimer = self.checkTimer or util.DelayCallOnce(0.1, function ()
		if self.loadingCount >= maxLoadingCount then
			return 0.1
		else
			self:AutoLoadAsset()
		end
		self.checkTimer = nil
	end)
end

function AssetLoadMgr:AutoLoadAsset()
	--print("AutoLoadAsset",util.get_len(self.loaders),self.loadingCount)

	local loopCount = maxLoadingCount - self.loadingCount

	self.onLoad = self.onLoad or function (ld)
		if ld.isDisposed then return end
		local abname = ld.assetPath
		--print("AutoLoadAsset cb",abname,util.get_len(self.loaders),self.loadingCount)

		self.loadingCount = self.loadingCount - 1
		self:LoadedAssetCallback(abname, nil, ld.asset)
		self:DelayAutoLoadAsset()
	end
	if loopCount <= 0 then
		return
	end

	-- self.loadingCount = 0
	for abname,vo in pairs(self.loaders) do
		if loopCount<=0 then 
			--print("AutoLoadAsset out",util.get_len(self.loaders),self.loadingCount)
			break
		end
		if not vo[1]:IsValid()  and vo[2]>0 then
			loopCount = loopCount - 1
			self.loadingCount = self.loadingCount + 1
			vo[1]:load(self.onLoad)
		end
	end
end

function AssetLoadMgr:LoadAssetAsyncInternal(assetbundleName, assetName, callback, bWithCache,bWithCacheLua)
	local loadvo  = self.loaders[assetbundleName] or {
		asset_loader(assetbundleName,"asset_load_mgr"),
		0
	}
	
	self.loaders[assetbundleName] = loadvo
	-- print("AutoLoadAsset LoadAssetAsyncInternal",assetbundleName,util.get_len(self.loaders),self.loadingCount,loadvo[1]:IsValid() )
    loadvo.withCache = bWithCache
    loadvo.withCacheLua = bWithCacheLua
	loadvo[2] = loadvo[2] + 1

	self:RemoveUnusedLoader()
	self:DelayAutoLoadAsset()
end

function AssetLoadMgr:LoadAssetAsync(assetbundleName, assetName, callback)
	-- log.Error("LoadAssetAsync:",assetbundleName,",assetName:",assetName)
	self:LoadAssetAsyncInternal(assetbundleName, assetName, callback, false)
end

function AssetLoadMgr:LoadAssetAsyncWithCache(assetbundleName, assetName, callback)
	-- log.Error("LoadAssetAsyncWithCache:",assetbundleName,",assetName:",assetName)
	self:LoadAssetAsyncInternal(assetbundleName, assetName, callback, true)
end

function AssetLoadMgr:LoadAssetAsyncWithCacheLua(assetbundleName, assetName, callback)
    -- log.Error("LoadAssetAsyncWithCacheLua:",assetbundleName)
	if val.IsTrue("sw_gameobject_pool") then
    	self:LoadAssetAsyncInternal(assetbundleName, assetName, callback, false,true)
	else
    	self:LoadAssetAsyncInternal(assetbundleName, assetName, callback, true)
	end
end

function AssetLoadMgr:LoadAssetListAsyncWithCacheLua(abNameList)
	local bWithCache, bWithCacheLua
	if val.IsTrue("sw_gameobject_pool") then
	    bWithCache = false
		bWithCacheLua = true
	else
		bWithCache = true
		bWithCacheLua = false
	end
	
	for _, abName in ipairs(abNameList) do
		local loadvo  = self.loaders[abName] or {asset_loader(abName,"asset_load_mgr"),  0}
		
		self.loaders[abName] = loadvo
		loadvo.withCache = bWithCache
		loadvo.withCacheLua = bWithCacheLua
		loadvo[2] = loadvo[2] + 1
	end

	self:RemoveUnusedLoader()
	self:DelayAutoLoadAsset()
end

function AssetLoadMgr:RemoveUnusedLoader()
	local len = util.get_len(self.loaders)
    if len > 100 then
        self.tmpList = self.tmpList or {}
        for abname, vo in pairs(self.loaders) do
            if not vo[1]:IsValid() and vo[2] <= 0 then
                table.insert(self.tmpList, abname)
            end
        end

        for i, abname in ipairs(self.tmpList) do
            self.loaders[abname] = nil
        end

        if util.get_len(self.tmpList) > 0 then
            self.tmpList = {}
        end
        --print("RemoveUnusedLoader",util.get_len(self.loaders))
    end
	
end
 

function AssetLoadMgr:GetLoader(assetbundleName)
    local vo = self.loaders[assetbundleName]
    if vo then
        return vo[1]
    end
end


function AssetLoadMgr:UnloadAssetBundle(assetbundleName)
	
	local loadvo  = self.loaders[assetbundleName] 
	if loadvo then
		loadvo[2] = loadvo[2] - 1
		if loadvo[2] == 0 then
			-- 正在加载中..
			if not loadvo[1]:IsReady() and loadvo[1]:IsValid() then
				--print("AutoLoadAsset UnloadAssetBundle",assetbundleName,util.get_len(self.loaders),self.loadingCount,loadvo[1]:IsValid())
				self.loadingCount = self.loadingCount - 1
			end
			loadvo[1]:Dispose()
		end
	end
	self:DelayAutoLoadAsset()
end

function AssetLoadMgr:AllDispose()
    for abname, vo in pairs(self.loaders) do
        if vo[1]:IsValid() then
            vo[1]:Dispose()
        end
    end
    self.loaders = {}
	self.loadingCount = 0
end
 


CAssetLoadMgr = class(object, nil, AssetLoadMgr)