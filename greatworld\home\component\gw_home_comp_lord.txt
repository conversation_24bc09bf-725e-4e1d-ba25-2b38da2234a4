
local GWG = GWG
local require = require
local newclass = newclass
local UIUtil = UIUtil
local table = require "table"
local util = require "util"
local math = math
local tostring = tostring
local typeof = typeof
local gw_home_config_mgr = require "gw_home_config_mgr"
local gw_home_comp_base_class = require "gw_home_comp_base_class"
local gw_home_astar_pathfinding = require "gw_home_astar_pathfinding"
local gw_home_lord_state_machine = require "gw_home_lord_state_machine"
local gw_home_grid_data = require "gw_home_grid_data"
local gw_ed = require "gw_ed"
local gw_gpu_animation_uitl = require "gw_gpu_animation_uitl"
local GWG = GWG
local Vector3 = CS.UnityEngine.Vector3
local DG   = CS.DG
local Tweening   = CS.DG.Tweening
local Time   = CS.UnityEngine.Time
local Quaternion   = CS.UnityEngine.Quaternion
local Animator   = CS.UnityEngine.Animator
local type = type
local ipairs = ipairs
local print = print
local GWAdmin = require "gw_admin"
local event				= require "event"
module("gw_home_comp_lord")

---@class GWHomeCompLord : GWHomeCompBaseClass
local GWHomeCompLord = newclass("gw_home_comp_lord", gw_home_comp_base_class)

local ClickAnimation = "Skill01"
-- 构造器
function GWHomeCompLord:ctor()
    gw_home_comp_base_class.ctor(self)
end

-- 初始化
function GWHomeCompLord:Init()
    gw_home_comp_base_class.Init(self)

    -- 初始化配置管理器
    gw_home_config_mgr.InitConfig()

    -- 获取领主配置
    local lordConfig = gw_home_config_mgr.GetLordConfig()

    -- 领主基本属性
    self.entityType = GWG.GWConst.EHomeEntityType.Lord
    self.compName = GWG.GWCompName.gw_home_comp_lord

    -- 巡逻相关参数（使用配置）
    self.patrolMinRadius = lordConfig.patrolMinRadius    -- 最小巡逻半径
    self.patrolMaxRadius = lordConfig.patrolMaxRadius    -- 最大巡逻半径
    -- 休息时间从最小值和最大值之间随机取
    self.restDuration = math.random(lordConfig.patrolMinRestTime * 10, lordConfig.patrolMaxRestTime * 10) / 10  -- 休息时间（秒）
    self.interactDuration = lordConfig.interactTime     -- 交互时间（秒）
    self.moveSpeed = lordConfig.moveSpeed               -- 移动速度（单位/秒）
    
    -- 当前状态数据
    self.currentPath = nil      -- 当前路径
    self.currentPathIndex = 1   -- 当前路径点索引
    self.targetGridX = nil      -- 目标网格X
    self.targetGridY = nil      -- 目标网格Y
    self.currentGridX = lordConfig.patrolBornX   -- 当前网格X（使用配置的出生位置）
    self.currentGridY = lordConfig.patrolBornY   -- 当前网格Y（使用配置的出生位置）
    
    -- 状态控制
    self.isInteracting = false
    self.isMovingToTarget = false
    self.manualTarget = nil     -- 玩家手动指定的目标点

    -- 初始化移动状态
    self.isInitializing = true  -- 是否正在初始化移动（从出生点到巡逻起点）
    self.hasReachedPatrolStart = false  -- 是否已到达巡逻起点
    
    -- 计时器
    self.restTimer = nil
    self.interactTimer = nil
    self.patrolTimer = nil
    self.moveTimer = nil
    
    -- 状态机
    self.stateMachine = nil
    
    -- 路径特效
    self.pathEffectId = nil
    self.targetEffectId = nil  -- 目标点特效

    -- GPU动画控制
    self.gpuAnimator = nil
    self.currentAnimation = ""

    -- 气泡组件
    self.bubbleComp = nil

    -- 转向相关
    self.isRotating = false        -- 是否正在转向
    self.rotationTween = nil       -- 转向动画
    self.targetRotation = nil      -- 目标旋转角度
    self.rotationCallback = nil    -- 转向完成回调
end

-- 加载完成回调
function GWHomeCompLord:OnLoaded()
    gw_home_comp_base_class.OnLoaded(self)

    -- 设置初始位置（出生点）
    self:SetGridPosition(self.currentGridX, self.currentGridY)

    -- 初始化时直接设置180度旋转
    if self.transform then
        local currentRotation = self.transform.rotation
        local currentEuler = currentRotation.eulerAngles
        --local newEuler = Vector3.New(currentEuler.x, currentEuler.y + 180, currentEuler.z)
        self.transform.rotation = Quaternion.Euler(currentEuler.x, currentEuler.y + 180, currentEuler.z)
    end

    -- 延迟一点时间等模型完全加载后再开始移动
    util.DelayCallOnce(0.5, function()
        GWG.GWAdmin.SwitchUtility.HomeLog("模型加载完成，开始初始化移动")
        -- 开始初始化移动：从出生点走到巡逻起点
        self:StartInitialMove()
    end)

    -- 获取GPU动画组件
    if self.transform then
        self.gpuAnimator = gw_gpu_animation_uitl.GetGpuAnimate(self.transform.gameObject)
        if self.gpuAnimator then
            -- 添加到GPU动画管理器
            gw_gpu_animation_uitl.AddAnimator("lord", self.gpuAnimator)
        end
    end

    -- 设置初始动画
    self:PlayAnimation("Stand")

    -- 创建状态机
    self.stateMachine = gw_home_lord_state_machine.CreateLordStateMachine(self)

    -- 创建气泡组件
    self:CreateBubbleComponent()

    -- 不再直接开始巡逻循环，改为开始初始化移动
    -- StartPatrolCycle() 将在 OnInitialMoveComplete() 中调用
end

-- 平滑转向到指定方向
function GWHomeCompLord:SmoothRotateTo(targetX, targetY, callback)
    if not self.transform then
        if callback then callback() end
        return
    end

    -- 计算目标方向
    local currentPos = self.transform.position
    local direction = Vector3(targetX, currentPos.y, targetY) - currentPos

    -- 如果距离太近，不需要转向
    if direction.magnitude < 0.1 then
        if callback then callback() end
        return
    end

    -- 计算目标旋转
    local targetRotation = Quaternion.LookRotation(direction)
    self:SmoothRotateToQuaternion(targetRotation, callback)
end

-- 平滑转向到指定的四元数
function GWHomeCompLord:SmoothRotateToQuaternion(targetRotation, callback)
    if not self.transform then
        if callback then callback() end
        return
    end

    -- 停止之前的转向动画
    self:StopRotation()

    local currentRotation = self.transform.rotation

    -- 计算角度差
    local angleDiff = Quaternion.Angle(currentRotation, targetRotation)

    -- 如果角度差很小，直接设置
    if angleDiff < 5 then
        self.transform.rotation = targetRotation
        if callback then callback() end
        return
    end

    -- 根据角度差计算转向时间（最小0.2秒，最大1.0秒）
    local rotationTime = math.min(1.0, math.max(0.2, angleDiff / 180 * 0.5))

    -- 设置转向状态
    self.isRotating = true
    self.targetRotation = targetRotation
    self.rotationCallback = callback

    -- 创建转向动画
    self.rotationTween = self.transform:DORotateQuaternion(targetRotation, rotationTime)
        :SetEase(DG.Tweening.Ease.OutCubic)
        :OnComplete(function()
            self:OnRotationComplete()
        end)
end

-- 转向完成回调
function GWHomeCompLord:OnRotationComplete()
    self.isRotating = false
    self.rotationTween = nil
    self.targetRotation = nil

    if self.rotationCallback then
        local callback = self.rotationCallback
        self.rotationCallback = nil
        callback()
    end
end

-- 停止转向动画
function GWHomeCompLord:StopRotation()
    if self.rotationTween then
        self.rotationTween:Kill()
        self.rotationTween = nil
    end

    self.isRotating = false
    self.targetRotation = nil
    self.rotationCallback = nil
end

-- 检查路径移动是否需要转向
function GWHomeCompLord:CheckNeedRotationForPath(nextPoint)
    if not self.transform or not nextPoint then
        return false
    end

    -- 计算到下一个点的方向
    local currentPos = self.transform.position
    local worldX, worldY, worldZ = gw_home_grid_data.GetPosByGridXY(nextPoint.x, nextPoint.y, true)
    local direction = Vector3(worldX, currentPos.y, worldZ) - currentPos

    -- 如果距离太近，不需要转向
    if direction.magnitude < 0.1 then
        return false
    end

    -- 计算目标旋转和当前旋转的角度差
    local targetRotation = Quaternion.LookRotation(direction)
    local currentRotation = self.transform.rotation
    local angleDiff = Quaternion.Angle(currentRotation, targetRotation)

    -- 如果角度差大于30度，需要转向
    return angleDiff > 30
end

-- 检查是否需要初始转向（大于90度才需要）
function GWHomeCompLord:CheckNeedInitialRotation()
    if not self.transform or not self.currentPath or #self.currentPath < 2 then
        return false
    end

    local firstPoint = self.currentPath[2]
    local currentPos = self.transform.position
    local worldX, worldY, worldZ = gw_home_grid_data.GetPosByGridXY(firstPoint.x, firstPoint.y, true)
    local direction = Vector3(worldX, currentPos.y, worldZ) - currentPos

    if direction.magnitude < 0.1 then
        return false
    end

    local targetRotation = Quaternion.LookRotation(direction)
    local currentRotation = self.transform.rotation
    local angleDiff = Quaternion.Angle(currentRotation, targetRotation)

    -- 只有大于90度才需要初始转向
    return angleDiff > 90
end

-- 平滑路径移动（恒定速度，不因拐弯降速）
function GWHomeCompLord:MoveAlongSmoothPath()
    GWG.GWAdmin.SwitchUtility.HomeLog("MoveAlongSmoothPath：当前路径索引"..self.currentPathIndex.." 路径总长度"..(self.currentPath and #self.currentPath or "nil"))

    if not self.currentPath or self.currentPathIndex > #self.currentPath then
        -- 路径完成
        GWG.GWAdmin.SwitchUtility.HomeLog("MoveAlongSmoothPath：路径完成，调用OnReachTarget")
        self:OnReachTarget()
        return
    end

    local nextPoint = self.currentPath[self.currentPathIndex]
    if not nextPoint then
        GWG.GWAdmin.SwitchUtility.HomeLog("MoveAlongSmoothPath：下一个点为空，调用OnReachTarget")
        self:OnReachTarget()
        return
    end

    -- 检查是否是当前位置，如果是则跳过
    if nextPoint.x == self.currentGridX and nextPoint.y == self.currentGridY then
        GWG.GWAdmin.SwitchUtility.HomeLog("MoveAlongSmoothPath：跳过当前位置点"..nextPoint.x..","..nextPoint.y)
        self.currentPathIndex = self.currentPathIndex + 1
        self:MoveAlongSmoothPath()
        return
    end

    GWG.GWAdmin.SwitchUtility.HomeLog("MoveAlongSmoothPath：移动到下一个点"..nextPoint.x..","..nextPoint.y)

    -- 平滑移动到下一个路径点，保持恒定速度
    self:MoveToGridPositionSmooth(nextPoint.x, nextPoint.y, function()
        -- 移动完成后继续下一个点
        GWG.GWAdmin.SwitchUtility.HomeLog("MoveAlongSmoothPath：到达点"..nextPoint.x..","..nextPoint.y.." 继续下一个点")
        self.currentPathIndex = self.currentPathIndex + 1
        self:MoveAlongSmoothPath()
    end)
end

-- 创建气泡组件
function GWHomeCompLord:CreateBubbleComponent()
    if not self.transform then
        return
    end

    -- 使用HUD工具类创建气泡组件，但不作为领主的子对象
    local gw_home_hud_util = require "gw_home_hud_util"
    local bubbleData = {
        parent = GWG.GWHomeNode.heroNode(), -- 放在heroNode下，与领主同级
        lordTransform = self.transform      -- 传递领主Transform用于跟随
    }
    local GWHomeHudConst = require "gw_home_hud_const"
    local MoveHudTypes = GWHomeHudConst.MoveHud
    local data = MoveHudTypes[ GWG.GWCompName.gw_home_comp_hud_lord_bubble]
    data.parent = GWG.GWHomeNode.heroNode()
    data.lordTransform =  self.transform
    
    local id = nil
    id , self.bubbleComp = gw_home_hud_util.InitMoveHudComponent(
        GWG.GWCompName.gw_home_comp_hud_lord_bubble,
        GWG.GWHomeNode.heroNode() -- 父节点设置为heroNode
    )
end

-- 播放GPU动画
function GWHomeCompLord:PlayAnimation(animationName, loop)
    if not self.gpuAnimator then
        return
    end

    -- 如果当前正在播放Attack动画，且要播放的也是Attack，则继续当前动画，不重新开始
    if self.currentAnimation == animationName then
        if animationName == ClickAnimation then
            GWG.GWAdmin.SwitchUtility.HomeLog("Attack动画正在播放中，继续当前动画")
        end
        return
    end

    self.currentAnimation = animationName

    if loop == nil then
        loop = true -- 默认循环播放
    end

    -- 播放GPU动画
    gw_gpu_animation_uitl.SetAnimatorState(self.gpuAnimator, animationName)

    -- Attack动画播放完后回到Stand（如果不循环播放）
    if animationName == ClickAnimation and not loop then
        -- 清理之前的延迟调用，避免多次点击时的冲突
        if self.attackAnimationTimer then
            util.RemoveDelayCall(self.attackAnimationTimer)
        end

        self.attackAnimationTimer = util.DelayCallOnce(0.33, function()
            if self.gpuAnimator then
                self:PlayAnimation("Stand")
                self.attackAnimationTimer = nil
            end
        end)
    end
end

-- 设置网格位置（瞬移）
function GWHomeCompLord:SetGridPosition(gridX, gridY)
    if not self.transform then
        return
    end

    self.currentGridX = gridX
    self.currentGridY = gridY

    -- 转换为世界坐标
    local worldX, worldY, worldZ = gw_home_grid_data.GetPosByGridXY(gridX, gridY, true)
    UIUtil.SetLocalPos(self.transform, worldX, worldY, worldZ)
end

-- 根据当前世界位置更新网格位置
function GWHomeCompLord:UpdateGridPosition()
    if not self.transform then
        return
    end

    -- 获取当前世界位置
    local currentPos = self.transform.position

    -- 转换为网格坐标（使用正确的方法名）
    local gridX, gridY = gw_home_grid_data.GetGridXYdByXZ(currentPos.x, currentPos.z)

    -- 更新网格位置记录
    self.currentGridX = gridX
    self.currentGridY = gridY
end

-- 平滑移动到网格位置
function GWHomeCompLord:MoveToGridPosition(gridX, gridY, onComplete)
    if not self.transform then
        return
    end

    -- 计算目标世界坐标
    local targetWorldX, targetWorldY, targetWorldZ = gw_home_grid_data.GetPosByGridXY(gridX, gridY, true)
    local targetPos = Vector3(targetWorldX, targetWorldY, targetWorldZ)

    -- 计算朝向
    local currentPos = self.transform.position
    local direction = targetPos - currentPos
    if direction.magnitude > 0.1 then
        -- 计算朝向角度（只考虑Y轴旋转）
        local targetRotation = Quaternion.LookRotation(direction)
        self.transform.rotation = targetRotation
    end

    -- 停止之前的移动动画
    if self.moveTween then
        self.moveTween:Kill()
        self.moveTween = nil
    end

    -- 尝试使用DOTween进行平滑移动
    local moveTime = direction.magnitude / self.moveSpeed
    local success = false

    -- 检查DOTween是否可用
    if DG and Tweening and self.transform.DOMove then
        self.moveTween = self.transform:DOMove(targetPos, moveTime):SetEase(Tweening.Ease.Linear)
        if self.moveTween then
            self.moveTween:OnUpdate(function()
                -- 动态销毁已走过的路径特效
                self:UpdatePathEffectDestroy()
            end):OnComplete(function()
                self.moveTween = nil
                self.currentGridX = gridX
                self.currentGridY = gridY
                if onComplete then
                    onComplete()
                end
            end)
            success = true
        end
    end

    -- 如果DOTween不可用，使用自定义的平滑移动
    if not success then
        self:StartCustomSmoothMove(currentPos, targetPos, moveTime, gridX, gridY, onComplete)
    end
end

-- 平滑移动到网格位置（恒定速度，带旋转）
function GWHomeCompLord:MoveToGridPositionSmooth(gridX, gridY, onComplete)
    GWG.GWAdmin.SwitchUtility.HomeLog("MoveToGridPositionSmooth：开始移动到"..gridX..","..gridY)

    if not self.transform then
        GWG.GWAdmin.SwitchUtility.HomeLog("MoveToGridPositionSmooth：transform为空，返回")
        return
    end

    -- 计算目标世界坐标
    local targetWorldX, targetWorldY, targetWorldZ = gw_home_grid_data.GetPosByGridXY(gridX, gridY, true)
    local targetPos = Vector3(targetWorldX, targetWorldY, targetWorldZ)
    local currentPos = self.transform.position

    GWG.GWAdmin.SwitchUtility.HomeLog("MoveToGridPositionSmooth：当前位置"..currentPos.x..","..currentPos.y..","..currentPos.z)
    GWG.GWAdmin.SwitchUtility.HomeLog("MoveToGridPositionSmooth：目标位置"..targetPos.x..","..targetPos.y..","..targetPos.z)

    -- 计算移动距离和时间（恒定速度）
    local distance = Vector3.Distance(currentPos, targetPos)
    local moveTime = distance / self.moveSpeed

    GWG.GWAdmin.SwitchUtility.HomeLog("MoveToGridPositionSmooth：移动距离"..distance.." 移动时间"..moveTime.." 移动速度"..self.moveSpeed)

    -- 如果距离太近，直接设置位置
    if distance < 0.1 then
        GWG.GWAdmin.SwitchUtility.HomeLog("MoveToGridPositionSmooth：距离太近，直接设置位置")
        self:SetGridPosition(gridX, gridY)
        if onComplete then
            onComplete()
        end
        return
    end

    -- 计算移动方向，用于旋转
    local direction = targetPos - currentPos
    direction.y = 0 -- 只考虑水平方向

    -- 停止之前的移动
    if self.moveTween then
        self.moveTween:Kill()
    end

    -- 同时进行移动和旋转
    if direction.magnitude > 0.1 then
        local targetRotation = Quaternion.LookRotation(direction)
        local currentRotation = self.transform.rotation
        local angleDiff = Quaternion.Angle(currentRotation, targetRotation)

        GWG.GWAdmin.SwitchUtility.HomeLog("平滑移动：到("..gridX..","..gridY..")，距离:"..distance.." 角度差:"..angleDiff.."度")

        -- 移动动画
        GWG.GWAdmin.SwitchUtility.HomeLog("MoveToGridPositionSmooth：创建DOMove动画，时间"..moveTime)
        self.moveTween = self.transform:DOMove(targetPos, moveTime)
            :SetEase(DG.Tweening.Ease.Linear)
            :OnUpdate(function()
                -- 更新网格位置
                self:UpdateGridPosition()
                -- 动态销毁已走过的路径特效
                self:UpdatePathEffectDestroy()
            end)
            :OnComplete(function()
                GWG.GWAdmin.SwitchUtility.HomeLog("MoveToGridPositionSmooth：DOMove动画完成")
                self.moveTween = nil
                -- 确保最终位置准确
                self:SetGridPosition(gridX, gridY)
                if onComplete then
                    onComplete()
                end
            end)

        if not self.moveTween then
            GWG.GWAdmin.SwitchUtility.HomeLog("MoveToGridPositionSmooth：警告！DOMove动画创建失败")
        end

        -- 只有角度差大于10度才进行旋转动画
        if angleDiff > 10 then
            -- 旋转动画（时间稍短，让旋转更快完成）
            local rotationTime = math.min(moveTime, 0.3)
            self.transform:DORotateQuaternion(targetRotation, rotationTime)
                :SetEase(DG.Tweening.Ease.OutCubic)
        end
    else
        -- 距离太近，直接移动
        self.moveTween = self.transform:DOMove(targetPos, moveTime)
            :SetEase(DG.Tweening.Ease.Linear)
            :OnUpdate(function()
                self:UpdateGridPosition()
                -- 动态销毁已走过的路径特效
                self:UpdatePathEffectDestroy()
            end)
            :OnComplete(function()
                self.moveTween = nil
                self:SetGridPosition(gridX, gridY)
                if onComplete then
                    onComplete()
                end
            end)
    end
end

-- 自定义平滑移动实现
function GWHomeCompLord:StartCustomSmoothMove(startPos, targetPos, duration, gridX, gridY, onComplete)
    local startTime = Time.time
    local endTime = startTime + duration

    -- 停止之前的移动
    if self.customMoveTimer then
        util.RemoveDelayCall(self.customMoveTimer)
        self.customMoveTimer = nil
    end

    local function updateMove()
        local currentTime = Time.time
        if currentTime >= endTime then
            -- 移动完成
            self.transform.position = targetPos
            self.currentGridX = gridX
            self.currentGridY = gridY
            self.customMoveTimer = nil
            if onComplete then
                onComplete()
            end
        else
            -- 插值移动
            local progress = (currentTime - startTime) / duration
            local currentPos = Vector3.Lerp(startPos, targetPos, progress)
            self.transform.position = currentPos

            -- 动态销毁已走过的路径特效
            self:UpdatePathEffectDestroy()

            -- 继续下一帧更新
            self.customMoveTimer = util.DelayCallOnce(0.02, updateMove) -- 50fps更新
        end
    end

    updateMove()
end

-- 开始初始化移动（从出生点到巡逻起点）
function GWHomeCompLord:StartInitialMove()
    local lordConfig = gw_home_config_mgr.GetLordConfig()

    GWG.GWAdmin.SwitchUtility.HomeLog("领主开始初始化移动: 从出生点("..self.currentGridX..","..self.currentGridY..") 到巡逻起点("..lordConfig.patrolStartX..","..lordConfig.patrolStartY..")")

    -- 检查是否已经在巡逻起点
    if self.currentGridX == lordConfig.patrolStartX and self.currentGridY == lordConfig.patrolStartY then
        GWG.GWAdmin.SwitchUtility.HomeLog("领主已在巡逻起点，直接开始正常状态机")
        -- 确保播放Stand动画（不需要移动时应该是站立状态）
        self:PlayAnimation("Stand")
        self:OnInitialMoveComplete()
        return
    end

    -- 设置初始化移动状态
    self.isInitializing = true
    self.hasReachedPatrolStart = false

    -- 确保播放Run动画（只有需要移动时才播放Run动画）
    self:PlayAnimation("Run")

    -- 移动到巡逻起点（不显示路径特效，不触发气泡）
    self:MoveToTarget(lordConfig.patrolStartX, lordConfig.patrolStartY, true) -- true表示是初始化移动
end

-- 初始化移动完成
function GWHomeCompLord:OnInitialMoveComplete()
    GWG.GWAdmin.SwitchUtility.HomeLog("领主初始化移动完成，开始正常状态机和玩家交互")

    -- 更新状态
    self.isInitializing = false
    self.hasReachedPatrolStart = true

    -- 确保播放Stand动画（初始化完成后应该是站立状态）
    self:PlayAnimation("Stand")

    -- 开始正常的巡逻循环
    self:StartPatrolCycle()
end

-- 开始巡逻循环
function GWHomeCompLord:StartPatrolCycle()
    if self.patrolTimer then
        util.RemoveDelayCall(self.patrolTimer)
    end

    -- 立即开始第一次巡逻
    self:StartPatrol()
end

-- 开始巡逻
function GWHomeCompLord:StartPatrol()
    -- 切换到巡逻状态
    if self.stateMachine then
        self.stateMachine:ChangeState(gw_home_lord_state_machine.LordState.Patrol)
    end

    -- 触发巡逻气泡
    if self.bubbleComp then
        self.bubbleComp:TriggerPatrolBubble()
    end

    -- 如果有手动目标，优先移动到手动目标
    if self.manualTarget then
        self:MoveToTarget(self.manualTarget.x, self.manualTarget.y)
        self.manualTarget = nil
        return
    end

    -- 生成随机巡逻目标点
    local targetX, targetY = gw_home_astar_pathfinding.GetRandomWalkablePoint(
        self.currentGridX, self.currentGridY,
        self.patrolMinRadius, self.patrolMaxRadius
    )

    if targetX and targetY then
        self:MoveToTarget(targetX, targetY)
    else
        -- 如果找不到合适的巡逻点，直接进入休息状态
        self:StartRest()
    end
end

-- 移动到目标点
function GWHomeCompLord:MoveToTarget(targetX, targetY, isInitialMove)
    self.targetGridX = targetX
    self.targetGridY = targetY
    self.isMovingToTarget = true

    -- 记录是否是初始化移动
    local isInitializing = isInitialMove or false

    -- 停止当前移动，从当前位置开始新的移动
    if self.isMovingToTarget then
        self:StopMovement()
    end

    -- 使用当前记录的网格位置作为起点
    local startX, startY = self.currentGridX, self.currentGridY

    -- 使用A*寻路
    local path = gw_home_astar_pathfinding.FindPath(
        startX, startY,
        targetX, targetY
    )

    if path then
        GWG.GWAdmin.SwitchUtility.HomeLog("原始路径点数:"..#path)

        -- 简化路径
        local simplifiedPath = gw_home_astar_pathfinding.SimplifyPath(path)
        GWG.GWAdmin.SwitchUtility.HomeLog("简化后路径点数:"..#simplifiedPath)

        -- 平滑路径，将直角拐弯变成弧形
        self.currentPath = gw_home_astar_pathfinding.SmoothPath(simplifiedPath, 0.4)
        GWG.GWAdmin.SwitchUtility.HomeLog("平滑后路径点数:"..#self.currentPath)
        self.currentPathIndex = 1

        -- 显示Debug路径（如果开启了Debug模式）
        gw_home_astar_pathfinding.ShowPathDebugView(self.currentPath)

        -- 显示路径特效（如果是手动指定的目标且不是初始化移动）
        if self.manualTarget and not isInitializing then
            GWG.GWAdmin.SwitchUtility.HomeLog("显示路径特效，使用路径点数:"..#self.currentPath)
            self:ShowPathEffect(self.currentPath)
            -- 触发移动气泡
            if self.bubbleComp then
                self.bubbleComp:TriggerMoveBubble()
            end
        elseif isInitializing then
            GWG.GWAdmin.SwitchUtility.HomeLog("初始化移动，不显示路径特效和气泡")
        end

        -- 直接开始平滑移动，让MoveToGridPositionSmooth处理转向
        self:PlayAnimation("Run")
        self:MoveAlongSmoothPath()
    else
        -- 寻路失败，进入休息状态
        self.isMovingToTarget = false
        self:StartRest()
    end
end

-- 沿路径移动
function GWHomeCompLord:MoveAlongPath()
    if not self.currentPath or self.currentPathIndex > #self.currentPath then
        -- 路径完成
        self:OnReachTarget()
        return
    end

    local nextPoint = self.currentPath[self.currentPathIndex]
    if not nextPoint then
        self:OnReachTarget()
        return
    end

    -- 检查是否需要转向
    local needRotation = self:CheckNeedRotationForPath(nextPoint)

    if needRotation and not self.isRotating then
        -- 需要转向，先转向再移动
        local worldX, worldY, worldZ = gw_home_grid_data.GetPosByGridXY(nextPoint.x, nextPoint.y, true)
        self:SmoothRotateTo(worldX, worldZ, function()
            -- 转向完成后移动
            self:MoveToGridPosition(nextPoint.x, nextPoint.y, function()
                -- 移动完成后继续下一个点
                self.currentPathIndex = self.currentPathIndex + 1
                self:MoveAlongPath()
            end)
        end)
    else
        -- 不需要转向或正在转向中，直接移动
        self:MoveToGridPosition(nextPoint.x, nextPoint.y, function()
            -- 移动完成后继续下一个点
            self.currentPathIndex = self.currentPathIndex + 1
            self:MoveAlongPath()
        end)
    end
end

-- 到达目标点
function GWHomeCompLord:OnReachTarget()
    self.isMovingToTarget = false
    self.currentPath = nil
    self.currentPathIndex = 1

    -- 播放站立动画
    self:PlayAnimation("Stand")

    -- 隐藏路径特效
    self:HidePathEffect()

    -- 检查是否是初始化移动完成
    if self.isInitializing then
        GWG.GWAdmin.SwitchUtility.HomeLog("领主到达巡逻起点，初始化移动完成")
        self:OnInitialMoveComplete()
        return
    end

    -- 清除点击位置的红色地块Debug（如果是手动目标）
    if self.manualTarget then
        local gw_home_lord_util = require "gw_home_lord_util"
        gw_home_lord_util.HideClickedGridDebug()
        self.manualTarget = nil
    end

    -- 进入休息状态
    self:StartRest()
end

-- 开始休息
function GWHomeCompLord:StartRest()
    -- 切换到休息状态
    if self.stateMachine then
        self.stateMachine:ChangeState(gw_home_lord_state_machine.LordState.Rest)
    end

    -- 触发休息气泡
    if self.bubbleComp then
        self.bubbleComp:TriggerRestBubble()
    end

    if self.restTimer then
        util.RemoveDelayCall(self.restTimer)
    end
    --刷新休息的随机时间
    local lordConfig = gw_home_config_mgr.GetLordConfig()
    self.restDuration = math.random(lordConfig.patrolMinRestTime * 10, lordConfig.patrolMaxRestTime * 10) / 10  -- 休息时间（秒）
    -- 休息指定时间后继续巡逻
    self.restTimer = util.DelayCallOnce(self.restDuration, function()
        if not self.isInteracting then
            self:StartPatrol()
        end
    end)
end

-- 开始交互
function GWHomeCompLord:StartInteract()
    -- 切换到交互状态
    if self.stateMachine then
        self.stateMachine:ChangeState(gw_home_lord_state_machine.LordState.Interact)
    end

    self.isInteracting = true

    -- 停止当前移动
    self:StopMovement()

    -- 交互指定时间后结束
    if self.interactTimer then
        util.RemoveDelayCall(self.interactTimer)
    end

    self.interactTimer = util.DelayCallOnce(self.interactDuration, function()
        self:EndInteract()
    end)
end

-- 结束交互
function GWHomeCompLord:EndInteract()
    self.isInteracting = false
    
    -- 刷新休息时间，重新开始休息
    self:StartRest()
end

-- 停止移动
function GWHomeCompLord:StopMovement()
    -- 停止DOTween动画
    if self.moveTween then
        self.moveTween:Kill()
        self.moveTween = nil
    end

    -- 停止转向动画
    self:StopRotation()

    -- 停止自定义移动计时器
    if self.customMoveTimer then
        util.RemoveDelayCall(self.customMoveTimer)
        self.customMoveTimer = nil
    end

    if self.moveTimer then
        util.RemoveDelayCall(self.moveTimer)
        self.moveTimer = nil
    end

    self.currentPath = nil
    self.currentPathIndex = 1
    self.isMovingToTarget = false

    -- 播放站立动画
    self:PlayAnimation("Stand")

    -- 隐藏路径特效
    self:HidePathEffect()
end

-- 处理点击事件
function GWHomeCompLord:OnClick()
    -- 初始化期间忽略点击交互
    if self.isInitializing then
        GWG.GWAdmin.SwitchUtility.HomeLog("领主正在初始化移动中，忽略点击交互")
        return
    end
    local properties = {LordType = 1}
    event.Trigger(event.GAME_EVENT_REPORT, "LordClick", properties)
    
    -- 播放攻击动画
    self:PlayAnimation(ClickAnimation, false) -- 不循环播放

    -- 触发交互气泡（强制显示，重置CD）
    if self.bubbleComp then
        self.bubbleComp:TriggerInteractBubble()
    end

    if self.isMovingToTarget then
        -- 如果正在巡逻，停止巡逻转为休息
        self:StopMovement()
        self:StartRest()
    elseif not self.isInteracting then
        -- 如果正在休息，开始交互
        self:StartInteract()
    else
        -- 如果正在交互，刷新交互时间
        if self.interactTimer then
            util.RemoveDelayCall(self.interactTimer)
        end
        self.interactTimer = util.DelayCallOnce(self.interactDuration, function()
            self:EndInteract()
        end)
    end
end

-- 设置手动目标点
function GWHomeCompLord:SetManualTarget(gridX, gridY)
    -- 初始化期间忽略玩家点击
    if self.isInitializing then
        GWG.GWAdmin.SwitchUtility.HomeLog("领主正在初始化移动中，忽略玩家点击("..gridX..","..gridY..")")
        return
    end

    self.manualTarget = {x = gridX, y = gridY}

    -- 打断当前状态，移动到新目标
    self:StopMovement()
    if self.restTimer then
        util.RemoveDelayCall(self.restTimer)
    end
    if self.interactTimer then
        util.RemoveDelayCall(self.interactTimer)
        self.isInteracting = false
    end

    -- 更新当前网格位置为实际位置，确保从当前位置寻路
    self:UpdateGridPosition()

    GWG.GWAdmin.SwitchUtility.HomeLog("设置新目标：从当前位置("..self.currentGridX..","..self.currentGridY..")到目标("..gridX..","..gridY..")")

    -- 短暂停顿0.1秒，确保位置同步后再寻路，避免绕路
    util.DelayCallOnce(0.1, function()
        if self.manualTarget and self.manualTarget.x == gridX and self.manualTarget.y == gridY then
            -- 确认目标没有改变，开始移动
            self:StartPatrol()
        end
    end)
end

-- 显示路径特效
function GWHomeCompLord:ShowPathEffect(path)
    if not path or #path <= 1 then
        return
    end

    -- 隐藏之前的特效
    self:HidePathEffect()

    GWG.GWAdmin.SwitchUtility.HomeLog("显示路径特效：路径点数"..#path)

    -- 验证领主当前位置
    if self.transform then
        local currentPos = self.transform.position
        local firstPathPoint = path[1]
        local firstGridWorldX, firstGridWorldY, firstGridWorldZ = gw_home_grid_data.GetPosByGridXY(firstPathPoint.x, firstPathPoint.y, true)
        local distance = math.sqrt((currentPos.x - firstGridWorldX)^2 + (currentPos.z - firstGridWorldZ)^2)
        GWG.GWAdmin.SwitchUtility.HomeLog("领主实际位置:"..currentPos.x..","..currentPos.y..","..currentPos.z)
        GWG.GWAdmin.SwitchUtility.HomeLog("路径第一点网格位置:"..firstGridWorldX..","..firstGridWorldY..","..firstGridWorldZ)
        GWG.GWAdmin.SwitchUtility.HomeLog("起点距离差:"..distance)
    end

    -- 获取特效配置
    local effectConfig = gw_home_config_mgr.GetPathEffectConfig()
    if not effectConfig then
        return
    end
    -- 使用配置的特效间隔和密度
    local interval = effectConfig.interval * effectConfig.density
    local scale = effectConfig.pathLineScale or 0.3 -- 默认缩放0.3

    -- 创建虚线特效
    local gw_home_effect_mgr = require "gw_home_effect_mgr"

    -- 计算路径总长度并按间隔放置特效
    local totalDistance = 0
    local pathSegments = {}

    -- 计算每段路径的长度
    -- 先显示完整路径，不跳过任何段
    GWG.GWAdmin.SwitchUtility.HomeLog("路径特效：计算路径段，总点数"..#path)

    for i = 1, #path - 1 do
        local startPoint = path[i]
        local endPoint = path[i + 1]

        local startWorldX, startWorldY, startWorldZ
        local endWorldX, endWorldY, endWorldZ

        -- 第一段路径使用领主当前实际位置作为起点，确保起点准确
        if i == 1 and self.transform then
            -- 使用领主当前实际世界位置
            local currentPos = self.transform.position
            startWorldX, startWorldY, startWorldZ = currentPos.x, currentPos.y, currentPos.z
            GWG.GWAdmin.SwitchUtility.HomeLog("路径段"..i.."：使用领主实际位置作为起点("..startWorldX..","..startWorldY..","..startWorldZ..")")
        else
            -- 其他段使用网格坐标转换
            startWorldX, startWorldY, startWorldZ = gw_home_grid_data.GetPosByGridXY(startPoint.x, startPoint.y, true)
        end

        -- 终点始终使用网格坐标转换
        endWorldX, endWorldY, endWorldZ = gw_home_grid_data.GetPosByGridXY(endPoint.x, endPoint.y, true)

        -- 使用世界坐标计算实际距离
        local segmentLength = math.sqrt((endWorldX - startWorldX)^2 + (endWorldZ - startWorldZ)^2)

        GWG.GWAdmin.SwitchUtility.HomeLog("路径段"..i.."：从世界坐标("..startWorldX..","..startWorldZ..")到("..endWorldX..","..endWorldZ..")，距离"..segmentLength)

        table.insert(pathSegments, {
            start = {x = startWorldX, y = startWorldY, z = startWorldZ, gridX = startPoint.x, gridY = startPoint.y},
            endPoint = {x = endWorldX, y = endWorldY, z = endWorldZ, gridX = endPoint.x, gridY = endPoint.y},
            length = segmentLength,
            totalStart = totalDistance
        })
        totalDistance = totalDistance + segmentLength
    end

    GWG.GWAdmin.SwitchUtility.HomeLog("路径特效：总距离"..totalDistance.." 特效间隔"..interval)

    -- 按间隔放置特效（使用配置的起点偏移）
    local startOffset = interval * effectConfig.startOffset
    local currentDistance = startOffset
    local effectCount = 0

    GWG.GWAdmin.SwitchUtility.HomeLog("路径特效：起点偏移"..startOffset.." 开始放置特效")

    while currentDistance < totalDistance do
        -- 找到当前距离对应的路径段
        local targetSegment = nil
        local segmentProgress = 0

        for _, segment in ipairs(pathSegments) do
            if currentDistance >= segment.totalStart and currentDistance <= segment.totalStart + segment.length then
                targetSegment = segment
                segmentProgress = (currentDistance - segment.totalStart) / segment.length
                break
            end
        end

        if targetSegment then
            -- 直接在世界坐标中进行插值，与领主实际移动轨迹完全一致
            local worldX = targetSegment.start.x + (targetSegment.endPoint.x - targetSegment.start.x) * segmentProgress
            local worldY = targetSegment.start.y + (targetSegment.endPoint.y - targetSegment.start.y) * segmentProgress
            local worldZ = targetSegment.start.z + (targetSegment.endPoint.z - targetSegment.start.z) * segmentProgress

            -- 调试输出
            effectCount = effectCount + 1
            GWG.GWAdmin.SwitchUtility.HomeLog("路径特效"..effectCount.."：世界坐标("..worldX..","..worldY..","..worldZ..")，距离进度"..currentDistance.."/"..totalDistance)

            -- 创建虚线特效（使用精确的世界坐标）
            local effectId = gw_home_effect_mgr.CreateEffects(
                GWG.GWConst.HomeEffectType.LordPathLine,
                nil,
                {x = worldX, y = worldY, z = worldZ}
            )

            if effectId then
                self.pathEffectId = self.pathEffectId or {}
                self.pathEffectData = self.pathEffectData or {}

                table.insert(self.pathEffectId, effectId)
                -- 存储特效的位置信息和距离信息，用于动态销毁
                table.insert(self.pathEffectData, {
                    id = effectId,
                    position = {x = worldX, y = worldY, z = worldZ},
                    distance = currentDistance,  -- 特效在路径上的距离
                    index = effectCount
                })

                GWG.GWAdmin.SwitchUtility.HomeLog("特效创建成功，ID:"..effectId.." 位置:"..worldX..","..worldY..","..worldZ.." 距离:"..currentDistance)
            else
                GWG.GWAdmin.SwitchUtility.HomeLog("特效创建失败，位置:"..worldX..","..worldY..","..worldZ)
            end
        end

        currentDistance = currentDistance + interval
    end

    -- 显示目标点特效（绿色特效）
    if #path > 0 then
        local targetPoint = path[#path] -- 路径的最后一个点
        local worldX, worldY, worldZ = gw_home_grid_data.GetPosByGridXY(targetPoint.x, targetPoint.y, true)

        -- 创建目标点特效
        local targetEffectId = gw_home_effect_mgr.CreateEffects(
            GWG.GWConst.HomeEffectType.LordPathTarget,
            nil,
            {x = worldX, y = worldY, z = worldZ}
        )

        if targetEffectId then
            self.targetEffectId = targetEffectId
        end
    end
end

-- 隐藏路径特效
function GWHomeCompLord:HidePathEffect()
    -- 清理路径虚线特效
    if self.pathEffectId then
        local gw_home_effect_mgr = require "gw_home_effect_mgr"

        if type(self.pathEffectId) == "table" then
            for _, effectId in ipairs(self.pathEffectId) do
                gw_home_effect_mgr.RemoveEffect(effectId)
            end
        else
            gw_home_effect_mgr.RemoveEffect(self.pathEffectId)
        end

        self.pathEffectId = nil
    end

    -- 清理路径特效数据
    if self.pathEffectData then
        self.pathEffectData = nil
    end

    -- 清理目标点特效
    if self.targetEffectId then
        local gw_home_effect_mgr = require "gw_home_effect_mgr"
        gw_home_effect_mgr.RemoveEffect(self.targetEffectId)
        self.targetEffectId = nil
    end
end

-- 动态销毁已走过的路径特效
function GWHomeCompLord:UpdatePathEffectDestroy()
    if not self.pathEffectData or not self.currentPath or not self.transform then
        GWG.GWAdmin.SwitchUtility.HomeLog("UpdatePathEffectDestroy: 数据检查失败 - pathEffectData:"..tostring(self.pathEffectData ~= nil).." currentPath:"..tostring(self.currentPath ~= nil).." transform:"..tostring(self.transform ~= nil))
        return
    end

    GWG.GWAdmin.SwitchUtility.HomeLog("UpdatePathEffectDestroy: 开始检查，特效数量:"..#self.pathEffectData.." 当前路径索引:"..tostring(self.currentPathIndex))

    -- 获取领主当前位置
    local lordPos = self.transform.position

    -- 简化算法：直接计算领主当前位置到路径起点的距离
    local startPoint = nil
    if self.currentPath and #self.currentPath > 0 then
        startPoint = self.currentPath[1]
    end

    if not startPoint then
        GWG.GWAdmin.SwitchUtility.HomeLog("UpdatePathEffectDestroy: 无法获取路径起点")
        return
    end

    -- 将网格坐标转换为世界坐标
    local startWorldX, startWorldY, startWorldZ = gw_home_grid_data.GetPosByGridXY(startPoint.x, startPoint.y, true)

    -- 计算领主当前位置到路径起点的直线距离
    local currentDistance = math.sqrt((lordPos.x - startWorldX)^2 + (lordPos.z - startWorldZ)^2)

    GWG.GWAdmin.SwitchUtility.HomeLog("UpdatePathEffectDestroy: 领主位置("..lordPos.x..","..lordPos.z..") 起点网格("..startPoint.x..","..startPoint.y..") 起点世界("..startWorldX..","..startWorldZ..") 当前距离:"..currentDistance)

    -- 销毁已走过的特效（添加一点缓冲距离，避免特效在领主脚下消失）
    local destroyDistance = currentDistance   -- 1.0单位的缓冲距离
    local gw_home_effect_mgr = require "gw_home_effect_mgr"
    local destroyedCount = 0

    GWG.GWAdmin.SwitchUtility.HomeLog("UpdatePathEffectDestroy: 销毁距离阈值:"..destroyDistance.." 特效总数:"..#self.pathEffectData)

    -- 从后往前遍历，安全删除
    for i = #self.pathEffectData, 1, -1 do
        local effectData = self.pathEffectData[i]
        if effectData then
            GWG.GWAdmin.SwitchUtility.HomeLog("UpdatePathEffectDestroy: 检查特效["..i.."] ID:"..effectData.id.." 距离:"..effectData.distance.." 是否<=销毁距离:"..destroyDistance)

            if effectData.distance <= destroyDistance then
                GWG.GWAdmin.SwitchUtility.HomeLog("UpdatePathEffectDestroy: 销毁特效 ID:"..effectData.id.." 距离:"..effectData.distance)

                -- 销毁特效
                gw_home_effect_mgr.RemoveEffect(effectData.id)

                -- 从pathEffectId中移除
                if self.pathEffectId then
                    for j = #self.pathEffectId, 1, -1 do
                        if self.pathEffectId[j] == effectData.id then
                            table.remove(self.pathEffectId, j)
                            break
                        end
                    end
                end

                -- 从pathEffectData中移除
                table.remove(self.pathEffectData, i)
                destroyedCount = destroyedCount + 1
            end
        end
    end

    GWG.GWAdmin.SwitchUtility.HomeLog("UpdatePathEffectDestroy: 本次销毁特效数量:"..destroyedCount.." 剩余特效数量:"..#self.pathEffectData)
end

-- 状态机相关方法已简化，改为手动切换状态

-- 状态机状态回调
function GWHomeCompLord:OnEnterIdleState()
    -- 进入空闲状态
end

function GWHomeCompLord:OnLeaveIdleState()
    -- 离开空闲状态
end

function GWHomeCompLord:OnEnterPatrolState()
    -- 进入巡逻状态
end

function GWHomeCompLord:OnLeavePatrolState()
    -- 离开巡逻状态
end

function GWHomeCompLord:OnEnterRestState()
    -- 进入休息状态
end

function GWHomeCompLord:OnLeaveRestState()
    -- 离开休息状态
end

function GWHomeCompLord:OnEnterInteractState()
    -- 进入交互状态
    -- 这里可以显示交互UI或特效
end

function GWHomeCompLord:OnLeaveInteractState()
    -- 离开交互状态
    -- 这里可以隐藏交互UI或特效
end

function GWHomeCompLord:OnEnterMoveToState()
    -- 进入移动状态
end

function GWHomeCompLord:OnLeaveMoveToState()
    -- 离开移动状态
end

-- 更新方法（如果需要的话）
function GWHomeCompLord:Update()
    if self.stateMachine then
        self.stateMachine:Update()
    end
end

-- 回收资源
function GWHomeCompLord:Recycle()
    -- 停止DOTween动画
    if self.moveTween then
        self.moveTween:Kill()
        self.moveTween = nil
    end

    -- 停止转向动画
    self:StopRotation()

    -- 停止自定义移动计时器
    if self.customMoveTimer then
        util.RemoveDelayCall(self.customMoveTimer)
        self.customMoveTimer = nil
    end

    -- 清理计时器
    if self.restTimer then
        util.RemoveDelayCall(self.restTimer)
        self.restTimer = nil
    end

    if self.interactTimer then
        util.RemoveDelayCall(self.interactTimer)
        self.interactTimer = nil
    end

    if self.patrolTimer then
        util.RemoveDelayCall(self.patrolTimer)
        self.patrolTimer = nil
    end

    if self.moveTimer then
        util.RemoveDelayCall(self.moveTimer)
        self.moveTimer = nil
    end

    -- 隐藏特效
    self:HidePathEffect()

    -- 清理GPU动画组件
    if self.gpuAnimator then
        gw_gpu_animation_uitl.RemoveAnimator(self.gpuAnimator)
        self.gpuAnimator = nil
    end

    -- 清理状态机
    if self.stateMachine then
        self.stateMachine:Dispose()
        self.stateMachine = nil
    end

    if self.entity then
        self.entity:Dispose()
        self.entity = nil
    end

    -- 清理气泡组件
    if self.bubbleComp then
        self.bubbleComp:ClearData()
        GWAdmin.PushComponent(self.bubbleComp)
        self.bubbleComp = nil
    end

    -- 清理攻击动画计时器
    if self.attackAnimationTimer then
        util.RemoveDelayCall(self.attackAnimationTimer)
        self.attackAnimationTimer = nil
    end

    -- 清理其他引用，避免内存泄漏
    self.transform = nil
    self.currentPath = nil
    self.manualTarget = nil
    self.pathEffectId = nil
    self.pathEffectData = nil  -- 清理路径特效数据

    -- 重置初始化状态
    self.isInitializing = false
    self.hasReachedPatrolStart = false

    -- 调用基类回收
    gw_home_comp_base_class.Recycle(self)
end

return GWHomeCompLord
