local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local Common_Util = require "Common_Util"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_arena_battle_wheel_switch_binding"

--region View Life
module("ui_arena_battle_wheel_switch")
local ui_path = binding.UIPath
local window = nil
local UIView = {}
local aniName1 = "Effect_UI_AllianceTrainSwitch_001"
local aniName2 = "Effect_UI_AllianceTrainSwitch_002"

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self.team_hero_l = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self:ResetSelectHeroPanel()
    self.animator.enabled = false
    window = nil
    if time_ticker then
        util.RemoveDelayCall(time_ticker)
        time_ticker = nil
    end
    self.team_hero_l = {}
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:InitTeamObjInfo(teamNum)
    if not self:IsValid() then
        return
    end
    self.teamLeftItem = {}
    self.maxTeamNum = teamNum
    for i = 1, self.maxTeamNum do
        local obj1 = self.rtf_teaminfoLeft.transform:GetChild(i-1)
        if(obj1)then
            local item1 = Common_Util.GetComponent(obj1, typeof(ScrollRectItem), "")
            table.insert(self.teamLeftItem,item1)
        end
    end
end

---刷新阵容信息
function UIView:OnRefreshDefendData(lineUp)
    if(not self.teamLeftItem)then
        self:InitTeamObjInfo()
    end
    local arena_battle_mgr = require "arena_battle_mgr"
    for i = 1, #self.teamLeftItem do
        local attackItem = self.teamLeftItem[i]
        if(self.team_hero_l and self.team_hero_l[i])then
            local heroList = arena_battle_mgr.SetTeamHeroItemInfo(attackItem,i,lineUp[i],self.team_hero_l[i],nil,0.4,nil,true)
            self.team_hero_l[i] = heroList
        else
            local heroList = arena_battle_mgr.SetTeamHeroItemInfo(attackItem,i,lineUp[i],nil,nil,0.4,nil,true)
            table.insert(self.team_hero_l,heroList)
        end
    end
end

---触发交换动画
function UIView:OnShowSwitchLineUpAni(refreshData)
    self.refreshData = refreshData
    local aniName =  aniName1
    if(self.animator)then
        self.animator.enabled = true
        if (refreshData.msg.teamIndex == 1) then
            self:ResetAnimation(aniName1)
        else
            aniName = aniName2
            self:ResetAnimation(aniName2)
        end
    end

    if time_ticker then
        util.RemoveDelayCall(time_ticker)
        time_ticker = nil
    end
    time_ticker = util.DelayCallOnce(1,function ()
        if (self:IsValid()) then
            self:OnRefreshDefendData(refreshData.lineUp)
        end
        self:ResetAnimation(aniName)
        self.animator.enabled = false
    end)
end

function UIView:ResetAnimation(clipName)
    self.animator:Play(clipName, -1, 0)
    self.animator:Update(0)
end

function UIView:ResetSelectHeroPanel()
    if self.refreshData then
        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:UnloadModule("ui_select_hero")
        local arena_battle_mgr = require "arena_battle_mgr"
        arena_battle_mgr.SetUpDefenseBattle(self.refreshData)
        self.refreshData = nil
    end
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
