--- Created by: 袁楠
--- DateTime: 2024/8/27
--- desc: 主页Controller
---

--region Require
local require = require
local newclass = newclass
local string = string
local os = os
local ipairs = ipairs

local face_transplant_const = require "face_transplant_const"
local unforced_guide_mgr = require "unforced_guide_mgr"
local event = require "event"
local controller_base = require "controller_base"
local flow_text = require "flow_text"
local allianceMgr = require "alliance_mgr"
local lang = require "lang"
local windowMgr = require "ui_window_mgr"
local mgr_personalInfo = require "mgr_personalInfo"
local const_personalInfo = require "const_personalInfo"
local data_personalInfo = require "data_personalInfo"
local event_personalInfo = require "event_personalInfo"
local custom_avatar_data = require "custom_avatar_data"
local net_personalInfo = require "net_personalInfo"
local net_click_liking = require "net_click_liking"
local red_const = require "red_const"
local red_system = require "red_system"
local click_liking_mgr = require "click_liking_mgr"
local log = require "log"
local event_NationalFlag_define = require "event_NationalFlag_define"

local PlayerPrefs = CS.UnityEngine.PlayerPrefs
--endregion

--region Controller Life
module("ui_personal_homepage_controller")
local controller = nil
local UIController = newclass("ui_personal_homepage_controller", controller_base)

local CONST_MAXMESSAGE = 100 
--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self:InitPersonalInfo()

    if data then
        self.isFaceTranslate = data.isFaceTranslate
        if data.isFaceTranslate == face_transplant_const.FaceTransplantGuideType.FaceTransplant then
            if unforced_guide_mgr.NewUnlock(25) then
                local unforced_guide_event_define = require "unforced_guide_event_define"
                event.Trigger(unforced_guide_event_define.start_person_return)
            end
        elseif data.isFaceTranslate == face_transplant_const.FaceTransplantGuideType.NameTransplant then
            if unforced_guide_mgr.NewUnlock(26) then
                local unforced_guide_event_define = require "unforced_guide_event_define"
                event.Trigger(unforced_guide_event_define.start_modify_name_return)
            end
        end
    end
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()
    self.onPlayerFaceEvent = function()
        self:OnBtnFaceEvent();
    end
    self.onPlayerBaseInfoChange = function()
        self:SetPlayerInfo()
        self:SetBattleInfo()
    end
    self.onPlayerSocialInfoChange = function()
        self:SetSocialInfo()
    end
    self.onPlayerSchlossInfoChange = function()
        self:SetPlayerInfo()
        --self:SetSchlossInfo()
    end
    self.checkLikeShow = function(eventName,data)
        self:OnShowLikePanel(data)
    end

    self:RegisterEvent(event_personalInfo.UPDATE_BASE_INFO, self.onPlayerBaseInfoChange)
    self:RegisterEvent(event_personalInfo.UPDATE_SOCIALIZE_INFO, self.onPlayerSocialInfoChange)
    self:RegisterEvent(event_personalInfo.UPDATE_PERSONALISED_INFO, self.onPlayerSchlossInfoChange)
    self:RegisterEvent(event_personalInfo.CUSTOM_AVATAR_USE, self.onPlayerSchlossInfoChange)
    self:RegisterEvent(event_personalInfo.REFRESH_LIKE_DATA, self.checkLikeShow)
    self:RegisterEvent(event_NationalFlag_define.ON_NATIONAL_FLAG_REFRESH, function()
        self:TriggerUIEvent("ShowNationalFlag")
    end)

    red_system.RegisterRedFunc(red_const.Enum.Personalised, function() 
        return self:CheckCanShowPersonalisedRedDot() 
    end)


    self.CheckFunc = function () 
        return self:CheckCanShowRedDot()
    end
    red_system.RegisterRedFunc(red_const.Enum.PersonalLikeRecord,self.CheckFunc)

    self.RefreshLikeRecord = function()
        --设置点赞记录按钮红点显示
        red_system.TriggerRed(red_const.Enum.PersonalLikeRecord)
    end
    self:RegisterEvent(event_personalInfo.OPEN_HISTORY_PANEL,self.RefreshLikeRecord)
end

function UIController:AutoUnsubscribeEvents()
    self.onPlayerFaceEvent = nil
    self.onPlayerBaseInfoChange = nil
    self.onPlayerSocialInfoChange = nil
    self.onPlayerSchlossInfoChange = nil
    self.checkLikeShow = nil
end
--endregion

--region 数据修改事件

function UIController:InitPersonalInfo()
    net_click_liking.MSG_LIKE_GET_ROLE_RECORD_REQ()
    net_click_liking.MSG_LIKE_GET_RECORD_REQ()
    self:SetPlayerInfo()
    self:SetSocialInfo()
    self:SetBattleInfo()
    self:SetWorldInfo()
    --self:SetSchlossInfo()
end

--检测是否显示点赞页面
function UIController:OnShowLikePanel(data)
    local click_liking_mgr = require "click_liking_mgr"
    local click_liking_data = require "click_liking_data"
    local canShow = click_liking_mgr.CheckCanShowLike(data)
    local likeNum = click_liking_data.GetCurLikeNum()
    if canShow then 
        --刷新主页上的点赞数
        local socialInfo = {
            sex = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleSex),
            praise = likeNum,
            roleID = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID),
        }
        self:TriggerUIEvent("UpdateSocialInfo", socialInfo)
        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:ShowModule("ui_click_liking_panel",nil,nil,data)
    end
end

function UIController:CheckCanShowPersonalisedRedDot()
    return require("helper_personalInfo").GetPersonalisedAllRedNum()
end

--检测是否存在未读记录
function UIController:CheckCanShowRedDot()
    local player_mgr = require "player_mgr"
    local key = string.format("CheckCanShowRedDot#%s",player_mgr.GetPlayerUserID())
    local cacha_ReadTime = PlayerPrefs.GetInt(key)
    local data = click_liking_mgr.GetLikeDataByMaxNum(CONST_MAXMESSAGE) -- 获取最新的100条点赞
    if cacha_ReadTime == 0 then
        -- log.Warning("CheckCanShowRedDot 首次进入点赞记录页面 无进入时间")
        if #data > 0 then 
            return 1
        end
        return 0
    else
        for i,v in ipairs(data) do 
            if cacha_ReadTime < v.occurTime then 
                -- log.Warning("CheckCanShowRedDot 存在未读的点赞记录")
                return 1
            end
        end
    end
    -- log.Warning("CheckCanShowRedDot 不存在未读的点赞记录")
    return 0
end

function UIController:SetPlayerInfo()
    local baseData = {
        faceID = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FaceID),
        frameID = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FrameID),
        level = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleLevel),
        name = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleName),
        customAvatarData = custom_avatar_data.GetMyAvatar(),
        faceClickEvent = self.onPlayerFaceEvent,
    }
    self:TriggerUIEvent("UpdateUserInfo", baseData)
end

function UIController:SetSocialInfo()
    local socialInfo = {
        sex = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleSex),
        praise = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RolePraise),
        roleID = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID),
    }
    self:TriggerUIEvent("UpdateSocialInfo", socialInfo)
end

function UIController:SetBattleInfo()
    local battleInfo = {
        battlePower = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RolePower),
        enemyKill = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleKillNum),
    }
    self:TriggerUIEvent("UpdateBattleInfo", battleInfo)
end

function UIController:SetWorldInfo()
    local frayInfo = {
        worldId = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.WorldId),
    }
    if allianceMgr.GetIsJoinAlliance() then
        frayInfo.allianceName = allianceMgr.GetUserAllianceShortName()
    end
    self:TriggerUIEvent("UpdateWorldInfo", frayInfo)
end

--[[打开个人信息修改界面]]
function UIController:OnBtnChangeEvent()
    if unforced_guide_mgr.GetCurGuide() == 26 and unforced_guide_mgr.GetCurStep() then
        local unforced_guide_event_define = require "unforced_guide_event_define"
        event.Trigger(unforced_guide_event_define.click_modify_name_return)
    end
    mgr_personalInfo.ShowPersonalChangeView(self.isFaceTranslate)
end

--[[打开头像修改界面]]
function UIController:OnBtnHeadChangeEvent()
    if unforced_guide_mgr.GetCurGuide() == 25 and unforced_guide_mgr.GetCurStep() then
        local unforced_guide_event_define = require "unforced_guide_event_define"
        event.Trigger(unforced_guide_event_define.click_person_return)
    end
    mgr_personalInfo.ShowPersonalised(const_personalInfo.PersonalisedTag.face)
end

--[[打开个性化城堡修改界面]]
function UIController:OnBtnFaceEvent()
    mgr_personalInfo.ShowPersonalised(const_personalInfo.PersonalisedTag.face)
end

--[[打开个性化城堡修改界面]]
function UIController:OnBtnSchlossChangeEvent()
    mgr_personalInfo.ShowPersonalised(const_personalInfo.PersonalisedTag.schloss)
end

--[[打开Rank界面]]
function UIController:OnBtnRankEvent()
    flow_text.Clear()
    local gw_all_rank_mgr = require "gw_all_rank_mgr"
    gw_all_rank_mgr.JumpToRankBase(function()
        windowMgr:UnloadModule("ui_personalInfo")
    end)
end

--[[打开个性化城堡修改界面]]
function UIController:OnBtnAllianceEvent()
    if allianceMgr.GetIsJoinAlliance() then
        mgr_personalInfo.ClosePersonalInfoView()
        windowMgr:ShowModule("ui_alliance_main")
    else
        flow_text.Clear()
        flow_text.Add(lang.Get(const_personalInfo.lang_JoinAlliance))
    end
end

--[[打开战区界面]]
function UIController:OnBtnWorldEvent()
    --flow_text.Clear()
    --flow_text.Add("战区官职功能，未解锁")
    windowMgr:ShowModule("ui_theater_position_panel");
    windowMgr:UnloadModule("ui_personalInfo")
end

function UIController:OnBtnLikeRecordEvent()
    local ui_window_mgr = require "ui_window_mgr"
    local curTime = os.server_time()
    local player_mgr = require "player_mgr"
    local key = string.format("CheckCanShowRedDot#%s",player_mgr.GetPlayerUserID())
    PlayerPrefs.SetInt(key,curTime)
    -- log.Warning("CheckCanShowRedDot 点击点赞记录按钮时记录打开时间",curTime)
    ui_window_mgr:ShowModule("ui_personal_like_history")
end
--endregion

--region ModuleFunction
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end

function Close()
    controller = nil;
end

--endregion
