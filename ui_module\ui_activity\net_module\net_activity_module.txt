
local require   = require
local ipairs    = ipairs
local table     = table
local gw_event_activity_define = require "gw_event_activity_define"
local windowMgr = require "ui_window_mgr"
local reward_mgr = require "reward_mgr"
local event         = require "event"
local xManMsg_pb = require "msg_pb"  --这里要使用以前的老协议，不要使用xManMsg_pb
local xManMsg_pb_new = require "xManMsg_pb"  --这里要使用以前的老协议，不要使用xManMsg_pb
local activity_pb       = require "activity_pb"
local msg_pb         = require "msg_pb"
local net           = require "net"
local net_route     = require "net_route"
local lang          = require "lang"
local flow_text        = require "flow_text"
local event_activity_define = require("event_activity_define")
module("net_activity_module")

-- 破碎时空扫荡完成后的通知  MSG_BROKENST_MOPUP_REWARD_NTF
function MSG_BROKENST_MOPUP_REWARD_NTF(msg)
    -- mopUpErr
    -- bType
    -- nStage
    -- remainHP
    -- rewardInfo
    -- battleType
    -- teamID
    -- arrMonsters
    event.Trigger(event_activity_define.TMSG_BROKENST_MOPUP_REWARD_NTF,msg)
end

-- 当月购买挑战徽章的数量  MSG_BROKENST_UPDATA_BADGES_NTF
function MSG_BROKENST_UPDATA_BADGES_NTF(msg)
    -- badgesNum
    event.Trigger(event_activity_define.TMSG_BROKENST_UPDATA_BADGES_NTF,msg)
end

-- MSG_ACTIVITY_COMPLETE_NTF  活动某个条目完成后的通知
function MSG_ACTIVITY_COMPLETE_NTF(msg)
    -- type
    -- ID
    -- content
    event.Trigger(event_activity_define.TMSG_ACTIVITY_COMPLETE_NTF,msg)
end

-- 请求圣诞节翻翻乐界面信息
-- function MSG_XMAS_FLIPPING_INFO_REQ(data)
--     -- atyID
--     local msg = activity_pb.TMSG_XMAS_FLIPPING_INFO_REQ()
--     net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XMAS_FLIPPING_INFO_REQ, msg)
-- end

-- 请求圣诞节翻翻乐界面信息
-- function MSG_XMAS_FLIPPING_INFO_RSP(msg)
    -- destID
    -- flippedIndexList
    -- destIDList
    -- flippedIDList
    -- level
    -- state
    -- errorcode
    -- event.Trigger(event_activity_define.TMSG_XMAS_FLIPPING_INFO_RSP,msg)
-- end

-- 圣诞节翻翻设置大奖
-- function MSG_XMAS_FLIPPING_DEST_REQ(data)
--     -- destID
--     -- atyID
--     local msg = activity_pb.TMSG_XMAS_FLIPPING_DEST_REQ()
--     net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XMAS_FLIPPING_DEST_REQ, msg)
-- end

-- 圣诞节翻翻乐界面信息
-- function MSG_XMAS_FLIPPING_DEST_RSP(msg)
--     -- errorcode
--     -- destID
--     event.Trigger(event_activity_define.TMSG_XMAS_FLIPPING_DEST_RSP,msg)
-- end

-- 圣诞节翻翻乐抽奖
-- function MSG_XMAS_FLIPPING_DRAW_REQ(data)
--     -- index
--     -- atyID
--     local msg = activity_pb.TMSG_XMAS_FLIPPING_DRAW_REQ()
--     net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XMAS_FLIPPING_DRAW_REQ, msg)
-- end

-- 圣诞节翻翻乐抽奖
-- function MSG_XMAS_FLIPPING_DRAW_RSP(msg)
--     -- prizeID
--     -- errorcode
--     -- index
--     event.Trigger(event_activity_define.TMSG_XMAS_FLIPPING_DRAW_RSP,msg)
-- end

-- 圣诞节翻翻乐 进入下一层
-- function MSG_XMAS_FLIPPING_NEXTLEVEL_REQ(data)
--     -- atyID
--     local msg = activity_pb.TMSG_XMAS_FLIPPING_NEXTLEVEL_REQ()
--     net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XMAS_FLIPPING_NEXTLEVEL_REQ, msg)
-- end

-- 圣诞节翻翻乐 进入下一层
-- function MSG_XMAS_FLIPPING_NEXTLEVEL_RSP(msg)
--     -- errorcode
--     -- level
--     event.Trigger(event_activity_define.TMSG_XMAS_FLIPPING_NEXTLEVEL_RSP,msg)
-- end

-- 小游戏过关
function MSG_XYX_PASS_LV_REQ(data)
    -- lvID
    -- LevelType
    -- unlockType
    -- nGameType
    -- nCollectionID
    local msg = activity_pb.TMSG_XYX_PASS_LV_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_PASS_LV_REQ, msg)
end

-- 小游戏过关回应
function MSG_XYX_PASS_LV_RSP(msg)
    -- errorcode
    -- lvID
    -- IsPass
    -- ItemNums
    event.Trigger(event_activity_define.TMSG_XYX_PASS_LV_RSP,msg)
end

-- 设置深度链接数据
function MSG_XYX_SET_LINK_REQ(data)
    -- data
    local msg = activity_pb.TMSG_XYX_SET_LINK_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_SET_LINK_REQ, msg)
end

-- 设置深度链接数据
function MSG_XYX_SET_LINK_RSP(msg)
    -- errorcode
    event.Trigger(event_activity_define.TMSG_XYX_SET_LINK_RSP,msg)
end

-- 深度链接数据通知
function MSG_XYX_LINK_DATA_NTF(msg)
    -- data
    event.Trigger(event_activity_define.TMSG_XYX_LINK_DATA_NTF,msg)
end

-- 设置小游戏关卡数据
function MSG_XYX_SET_LEVEL_REQ(data)
    -- type
    -- data
    local msg = activity_pb.TMSG_XYX_SET_LEVEL_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_SET_LEVEL_REQ, msg)
end

-- 设置小游戏关卡数据
function MSG_XYX_SET_LEVEL_RSP(msg)
    -- errorcode
    event.Trigger(event_activity_define.TMSG_XYX_SET_LEVEL_RSP,msg)
end

-- 小游戏关卡数据通知
function MSG_XYX_LEVEL_DATA_NTF(msg)
    -- type
    -- data
    event.Trigger(event_activity_define.TMSG_XYX_LEVEL_DATA_NTF,msg)
end

-- 设置小游戏属性、技能
function MSG_XYX_SET_PROP_REQ(data)
    -- gametype
    -- key
    -- data
    local msg = activity_pb.TMSG_XYX_SET_PROP_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_SET_PROP_REQ, msg)
end

-- 设置小游戏属性、技能
function MSG_XYX_SET_PROP_RSP(msg)
    -- errorcode
    event.Trigger(event_activity_define.TMSG_XYX_SET_PROP_RSP,msg)
end

-- 获取小游戏属性、技能
function MSG_XYX_GET_PROP_REQ(data)
    -- gametype
    -- key
    local msg = activity_pb.TMSG_XYX_GET_PROP_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_GET_PROP_REQ, msg)
end

-- 获取小游戏属性、技能
function MSG_XYX_GET_PROP_RSP(msg)
    -- errorcode
    -- gametype
    -- key
    -- data
    event.Trigger(event_activity_define.TMSG_XYX_GET_PROP_RSP,msg)
end

-- 小游戏属性、技能通知
function MSG_XYX_PROP_NTF(msg)
    -- gametype
    -- key
    -- data
    event.Trigger(event_activity_define.TMSG_XYX_PROP_NTF,msg)
end

-- 道具激活小游戏请求
function MSG_XYX_ITEMACTIVE_REQ(data)
    -- type
    local msg = activity_pb.TMSG_XYX_ITEMACTIVE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_ITEMACTIVE_REQ, msg)
end

-- 道具激活小游戏响应
function MSG_XYX_ITEMACTIVE_RSP(msg)
    -- errorcode
    -- type
    event.Trigger(event_activity_define.TMSG_XYX_ITEMACTIVE_RSP,msg)
end

-- 小游戏广告
function MSG_XYX_ADVRW_REQ(data)
    -- lvID
    -- LevelType
    -- unlockType
    local msg = activity_pb.TMSG_XYX_ADVRW_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_ADVRW_REQ, msg)
end

-- 小游戏广告响应
function MSG_XYX_ADVRW_RSP(msg)
    -- errorcode
    -- lvID
    event.Trigger(event_activity_define.TMSG_XYX_ADVRW_RSP,msg)
end

-- 关卡广告奖励请求
function MSG_XYX_STAGE_REWARD_REQ(data)
    -- chapterid
    -- stageNum
    -- ChaptersNum
    -- nCollectionID
    -- nStageIndex
    local msg = activity_pb.TMSG_XYX_STAGE_REWARD_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_STAGE_REWARD_REQ, msg)
end

-- 关卡广告奖励响应
function MSG_XYX_STAGE_REWARD_RSP(msg)
    -- chapterid
    -- stageNum
    -- errorcode
    event.Trigger(event_activity_define.TMSG_XYX_STAGE_REWARD_RSP,msg)
end

-- 小游戏升级请求
function MSG_XYX_LEVEL_REQ(data)
    local msg = activity_pb.TMSG_XYX_LEVEL_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_LEVEL_REQ, msg)
end

-- 小游戏升级响应
function MSG_XYX_LEVEL_RSP(msg)
    -- errorcode
    -- lv
    event.Trigger(event_activity_define.TMSG_XYX_LEVEL_RSP,msg)
end

-- Facebook 社群引导信息请求（领奖信息）（client——>场景服）
function MSG_FACEBOOK_GUIDE_REQ(data)
    local msg = activity_pb.TMSG_FACEBOOK_GUIDE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_FACEBOOK_GUIDE_REQ, msg)
end

-- Facebook 社群引导信息回复 （场景服——>client）
function MSG_FACEBOOK_GUIDE_RSP(msg)
    -- errorcode
    event.Trigger(event_activity_define.TMSG_FACEBOOK_GUIDE_RSP,msg)
end

-- 劳动节活动请求账本
function MSG_LABOURDAYACTIVITY_ACCOUNTBOOK_REQ(data)
    -- day
    local msg = activity_pb.TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_LABOURDAYACTIVITY_ACCOUNTBOOK_REQ, msg)
end

-- 劳动节活动请求账本回复
function MSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP(msg)
    -- errCode
    -- bookInfo
    -- day
    -- total
   if msg.errCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errCode)
    else
        event.Trigger(event_activity_define.TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP,msg)
    end
end

-- 关卡广告奖励批量领取请求
function MSG_XYX_STAGE_REWARD_MULTI_REQ(data)
    -- reqInfo
    local msg = activity_pb.TMSG_XYX_STAGE_REWARD_MULTI_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_STAGE_REWARD_MULTI_REQ, msg)
end

-- 关卡广告奖励批量领取回复
function MSG_XYX_STAGE_REWARD_MULTI_RSP(msg)
    -- rspInfo
    -- errorcode
    event.Trigger(event_activity_define.TMSG_XYX_STAGE_REWARD_MULTI_RSP,msg)
end

-- 通用活动请求排行榜信息
function MSG_COMM_ACTIVITY_RANK_REQ(data,code,themeId,worldId)
    -- actRankType
    local msg = activity_pb.TMSG_COMM_ACTIVITY_RANK_REQ()
    --eg：activity_pb.ACTTYPE_WORLDBOSS_DAMAGE
    msg.actRankType = data
    if code then
        msg.actid = code
    end
    --同盟对决的主题ID
    if themeId then
        msg.themeId = themeId
    end

    if worldId then
        msg.toWorldId = worldId
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_COMM_ACTIVITY_RANK_REQ, msg)
end

-- 通用活动请求排行榜回复
function MSG_COMM_ACTIVITY_RANK_RSP(msg)
    -- errCode
    -- selfRank
    -- rankInfo
    -- actRankType
    -- pumpkinInfos
    if msg.errCode ~= 0 then
        local error_code_pb = require "error_code_pb"
        if msg.errCode == error_code_pb.enErr_Abnormal then
            local log = require "log"
            log.Error("获取排行榜数据出错，  确认是否对应服务器未开活动微服,错误码=",msg.errCode)
        else
            flow_text.AddErrorCodeRes(msg.errCode)            
        end
    else
       local gw_activity_ranking_data = require "gw_activity_ranking_data"
       gw_activity_ranking_data.SetData(msg)
       event.Trigger(event_activity_define.TMSG_COMM_ACTIVITY_RANK_RSP,msg)
        
    end
end

-- 数据更新 下发给客户端 MSG_XYX_DATA_NTF
function MSG_XYX_DATA_NTF(msg)
    -- data
    event.Trigger(event_activity_define.TMSG_XYX_DATA_NTF,msg)
end

-- 邮箱订阅绑定请求
function MSG_RESERVATION_EMAIL_BIND_REQ(data)
    local msg = activity_pb.TMSG_RESERVATION_EMAIL_BIND_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_RESERVATION_EMAIL_BIND_REQ, msg)
end

-- 邮箱订阅解除绑定请求
function MSG_RESERVATION_EMAIL_UNBIND_REQ(data)
    local msg = activity_pb.TMSG_RESERVATION_EMAIL_UNBIND_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_RESERVATION_EMAIL_UNBIND_REQ, msg)
end

-- 邮箱订阅领奖请求
function MSG_RESERVATION_EMAIL_SENDREWARD_REQ(data)
    local msg = activity_pb.TMSG_RESERVATION_EMAIL_SENDREWARD_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_RESERVATION_EMAIL_SENDREWARD_REQ, msg)
end

-- 邮箱订阅领奖回复
function MSG_RESERVATION_EMAIL_SENDREWARD_RSP(msg)
    -- errorcode
    event.Trigger(event_activity_define.TMSG_RESERVATION_EMAIL_SENDREWARD_RSP,msg)
end

-- 邮箱订阅是否领奖请求
function MSG_RESERVATION_EMAIL_BGETREWARD_REQ(data)
    local msg = activity_pb.TMSG_RESERVATION_EMAIL_BGETREWARD_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_RESERVATION_EMAIL_BGETREWARD_REQ, msg)
end

-- 邮箱订阅是否领奖回复
function MSG_RESERVATION_EMAIL_BGETREWARD_RSP(msg)
    -- bGetRewards
    -- errorcode
    event.Trigger(event_activity_define.TMSG_RESERVATION_EMAIL_BGETREWARD_RSP,msg)
end

-- 领取集结大作战奖励请求
function MSG_GATHERING_AWARD_REQ(nGatherID,termID)
    -- dbid
    -- nGatherID
    local msg = activity_pb.TMSG_GATHERING_AWARD_REQ()
    msg.nGatherID = nGatherID
    msg.termID = termID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_GATHERING_AWARD_REQ, msg)
end

-- 领取集结大作战奖励回复
function MSG_GATHERING_AWARD_RSP(msg)
    -- errorcode
    -- uRewardId
    if msg.uRewardId then
        event.Trigger(event_activity_define.TMSG_GATHERING_AWARD_RSP,msg)
    end
end

-- 请求世界BOSS活动主信息
function MSG_WORDBOSS_INFO_REQ(data)
    local msg = activity_pb.TMSG_WORDBOSS_INFO_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_WORDBOSS_INFO_REQ, msg)
end

-- 世界BOSS活动主信息通知
function MSG_WORDBOSS_INFO_NTF(msg)
    -- actid
    -- boss
    -- attackcnt
    -- remaincnt
    -- achieve
    -- worldDateInfo
    event.Trigger(event_activity_define.TMSG_WORDBOSS_INFO_NTF,msg)
    local festival_activity_cfg = require "festival_activity_cfg"
    event.Trigger(gw_event_activity_define.GW_SCHEDULE_DATA_UPDATE, festival_activity_cfg.ActivityCodeType.WorldBoss)
end

-- 请求世界BOSS上榜阵容信息
function MSG_WORDBOSS_LINE_INFO_REQ(data)
    -- rank
    local msg = activity_pb.TMSG_WORDBOSS_LINE_INFO_REQ()
    msg.rank = data
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_WORDBOSS_LINE_INFO_REQ, msg)
end

-- 世界BOSS上榜阵容信息
function MSG_WORDBOSS_LINE_INFO_RSP(msg)
    -- rank
    -- dbid
    -- power
    -- heros
    event.Trigger(event_activity_define.TMSG_WORDBOSS_LINE_INFO_RSP,msg)   
end

-- 世界BOSS伤害纪录通知
function MSG_WORDBOSS_DMGRECORD_NTF(msg)
    -- bossid
    -- damage
    event.Trigger(event_activity_define.TMSG_WORDBOSS_DMGRECORD_NTF,msg)    
end

-- 请求错误通知
function MSG_WORDBOSS_ERR_NTF(msg)
    -- errorcode
    event.Trigger(event_activity_define.TMSG_WORDBOSS_ERR_NTF,msg)
end

-- 兑换物品请求
function MSG_FULLBATTLE_EXCHANGE_REQ(data)
    -- AtyID
    -- ContentID
    -- ExchangeNum
    local msg = activity_pb.TMSG_FULLBATTLE_EXCHANGE_REQ()
    msg.AtyID = data.AtyID
    msg.ContentID = data.ContentID
    msg.ExchangeNum = data.ExchangeNum
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_FULLBATTLE_EXCHANGE_REQ, msg)
end

-- 兑换物品回复
function MSG_FULLBATTLE_EXCHANGE_RSP(msg)
    -- errorcode
    -- AtyID
    -- ContentID
    -- RewardID
    -- RewardNum
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    else
        --[[local gw_fully_war_mgr = require "gw_fully_war_mgr"
        gw_fully_war_mgr.UpdateExchangeInfo(msg)]]
        if not msg.RewardIDs or not msg.ExchangeNum then
            return
        end
        local rewardData = reward_mgr.GetRewardGoodsMergers(msg.RewardIDs)
        if rewardData and #rewardData > 0 then
            rewardData[1].num = msg.ExchangeNum * rewardData[1].num
            local listData = { title = "", dataList = rewardData }
            local showData = {}
            table.insert(showData, listData)
            local ui_reward_result = require "ui_reward_result_new"
            ui_reward_result.SetInputParam(showData)
            windowMgr:ShowModule("ui_reward_result_new")
        end
    end
    event.Trigger(event_activity_define.TMSG_FULLBATTLE_EXCHANGE_RSP,msg)
    --兑换道具 打点
    local reportMsg =
    {
        item_id = msg.ContentID, --兑换的道具id
    }
    event.EventReport("BattleExchange_RedemptionItem", reportMsg)

    --通用活动兑换打点
    if msg.ContentID then
        local game_scheme = require "game_scheme"
        local cfg = game_scheme:ActivityContent_0(msg.ContentID)
        local util = require "util"
        if cfg then
            local need = util.SplitString(cfg.expenditure, "#", tonumber)
            local item = need[1]
            local itemNum = need[2] or 1
            if msg.AtyID and msg.AtyID ~= 0 then
                local data =
                {
                    reward_ID = msg.RewardIDs, --兑换的道具id
                    activity_id = msg.AtyID, --活动id
                    gain_num = msg.ExchangeNum,--兑换数量
                    cost = itemNum*msg.ExchangeNum--消耗货币数量
                }
                event.EventReport("UndeadTreasure_ExchangeRewards", data)
            end
        end
    end
end

-- 兑换商店信息通知
function MSG_EXCHANGE_SHOP_INFO_NTF(msg)
    -- AtyID
    -- itemID
    -- exchangeInfos
    local gw_fully_war_mgr = require "gw_fully_war_mgr"
    gw_fully_war_mgr.UpdateExchangeInfo(msg)
    event.Trigger(event_activity_define.TMSG_EXCHANGE_SHOP_INFO_NTF,msg)
end

-- 全面备战（进度变更通知）
function MSG_FULLBATTLE_REWARD_NTF(msg)
    -- AtyID
    -- progressNum
    -- goods
    local gw_fully_war_mgr = require "gw_fully_war_mgr"
    --gw_fully_war_mgr.UpdateExchangeInfo(msg)
    gw_fully_war_mgr.UpdateRechargeInfo(msg)
    --gw_fully_war_mgr.UpdateTotalTicketNum(msg.AtyID, msg.progressNum)
    event.Trigger(event_activity_define.TMSG_FULLBATTLE_REWARD_NTF,msg)
end

-- 幸运转盘 抽奖请求
function MSG_LUCKY_DRAW_LOTTERY_REQ(data)
    -- iLotteryID
    -- iLotteryType
    local msg = activity_pb.TMSG_LUCKY_DRAW_LOTTERY_REQ()
    msg.iLotteryID = data.iLotteryID
    msg.iLotteryType = data.iLotteryType
    msg.AtyID = data.AtyID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_LUCKY_DRAW_LOTTERY_REQ, msg)
end

-- 幸运转盘 抽奖回复
function MSG_LUCKY_DRAW_LOTTERY_RSP(msg)
    -- errorcode
    -- iLotteryID
    -- iLotteryType
    -- iRewardIds
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end

    --打点---------------------------------------
    local reportMsg =
    {
        lotteryType = msg.iLotteryType, --抽奖id
        atyID = msg.AtyID, --抽奖类型
    }
    local rewardIds_str = ""
    if msg:HasField("boxDataList") then
        for i, v in ipairs(msg.boxDataList.boxDatas) do
            rewardIds_str = rewardIds_str.."#"..v.miracleboxId
        end
    end
    for i, v in ipairs(msg.iRewardIds) do
        rewardIds_str = rewardIds_str.."#"..v
    end
    reportMsg.rewardIds_str = rewardIds_str
    event.EventReport("Common_LuckyDrawLotteryCall", reportMsg)
    --打点-----------------------------------

    event.Trigger(gw_event_activity_define.GW_LUCKY_SPIN_RESULT, msg)
    event.Trigger(event_activity_define.TMSG_LUCKY_DRAW_LOTTERY_RSP,msg)
end

-- 幸运转盘数据推送
function MSG_LUCKY_DRAW_DAILYGIFT_NTF(msg)
    -- lotteryNum
    -- alreadyNum
    -- AtyID
    local gw_lucky_wheel_mgr = require "gw_lucky_wheel_mgr"
    gw_lucky_wheel_mgr.SetUsedSiphonNum(msg.AtyID, msg.alreadyNum)
    gw_lucky_wheel_mgr.SetSurplusSiphonNum(msg.AtyID, msg.lotteryNum)
    --取数据走新的
    local common_lottery_data = require "common_lottery_data"
    common_lottery_data.SetLotteryData(msg)
    event.Trigger(event_activity_define.TMSG_LUCKY_DRAW_DAILYGIFT_NTF,msg)
end

-- 寻宝大作战活动信息请求
function MSG_GEAR_SUPPLY_GETACTIVITYDATA_REQ(data)
    local msg = activity_pb.TMSG_GEAR_SUPPLY_GETACTIVITYDATA_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_GEAR_SUPPLY_GETACTIVITYDATA_REQ, msg)
end

-- 寻宝大作战活动信息回复
function MSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP(msg)
    local gw_find_treasure_mgr = require "gw_find_treasure_mgr"
    gw_find_treasure_mgr.SetActData(msg)
end

-- 寻宝大作战奖励领取请求
function MSG_GEAR_SUPPLY_RECEIVEAWARD_REQ(data)
    local msg = activity_pb.TMSG_GEAR_SUPPLY_RECEIVEAWARD_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_GEAR_SUPPLY_RECEIVEAWARD_REQ, msg)
end

-- 寻宝大作战奖励领取回复
function MSG_GEAR_SUPPLY_RECEIVEAWARD_RSP(msg)
    local gw_find_treasure_mgr = require "gw_find_treasure_mgr"
    gw_find_treasure_mgr.SetReceiveAwardResult(msg)
end

-- 寻宝大作战抽奖请求
function MSG_GEAR_SUPPLY_DRAWAWARD_REQ(data)
    local msg = activity_pb.TMSG_GEAR_SUPPLY_DRAWAWARD_REQ()
    msg.nGridIdx = data
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_GEAR_SUPPLY_DRAWAWARD_REQ, msg)
end

-- 寻宝大作战抽奖回复
function MSG_GEAR_SUPPLY_DRAWAWARD_RSP(msg)
    local gw_find_treasure_mgr = require "gw_find_treasure_mgr"
    gw_find_treasure_mgr.SetDrawResult(msg)
end

-- 寻宝大作战秘宝大奖选择请求
function MSG_GEAR_SUPPLY_SETREWARD_REQ(bigIndex, isSaveBig)
    local msg = activity_pb.TMSG_GEAR_SUPPLY_SETREWARD_REQ()
    msg.nBigRewardGridID = bigIndex
    msg.nDefaultSelect = isSaveBig and 1 or 0
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_GEAR_SUPPLY_SETREWARD_REQ, msg)
end

-- 寻宝大作战秘宝大奖选择回复
function MSG_GEAR_SUPPLY_SETREWARD_RSP(msg)
    local gw_find_treasure_mgr = require "gw_find_treasure_mgr"
    gw_find_treasure_mgr.SetBigRewardResult(msg)
end

----------- 战令模块开始 -------------
-- 购买充值接口,避免每个人都写一下
function Send_New_Recharge_REQ(rechargeId)
    local game_scheme = require "game_scheme"
    local rechargeCfg = game_scheme:Recharge_0(rechargeId)
    if not rechargeCfg then
        return
    end

    local ui_select_diamond = require "ui_select_diamond"
    if not ui_select_diamond.AccountIsBinded() then
        return
    end
    local net_recharge_module = require "net_recharge_module"
    net_recharge_module.Send_New_Recharge_REQ(rechargeId, 1, rechargeCfg.iPrice)
end

function TMSG_LUA_RUN_FUNCB_NEW_REQ()
    
end


---description 活动数据请求
---param headingCodes 活动headingCode列表
function MSG_ACTIVITY_DATA_REQ(headingCodes)
    local msg = activity_pb.TMSG_ACTIVITY_DATA_REQ()
    for _,v in ipairs(headingCodes) do
        msg.headingCode:append(v)
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ACTIVITY_DATA_REQ, msg)
end

function MSG_ACTIVITY_DATA_RSP(msg)
    event.Trigger(event_activity_define.TMSG_ACTIVITY_DATA_RSP,msg)
end

----------- 战令模块结束 -------------

-------------------------------------------------隐秘宝藏 start----------------------------------------------
---description 查看订单请求
---param atyID 活动id    橡果酒馆 999999
function MSG_COMMONATY_VIEWORDER_REQ(data)
    local msg = activity_pb.TMSG_COMMONATY_VIEWORDER_REQ()
    msg.atyID = data.atyID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_COMMONATY_VIEWORDER_REQ, msg)
end
-- 查看订单回复
function MSG_COMMONATY_VIEWORDER_RSP(msg)
    -- datas 订单数据
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end
    event.Trigger(event_activity_define.TMSG_COMMONATY_VIEWORDER_RSP,msg)
end

---description 上架物品请求
function MSG_COMMONATY_SHELF_GOODS_REQ(data)
    -- atyID
    -- consumeItemId
    -- consumeNum
    -- targetItemId
    -- targetItemNum
    local msg = activity_pb.TMSG_COMMONATY_SHELF_GOODS_REQ()
    msg.atyID = data.atyID
    msg.consumeItemId = data.consumeItemId
    msg.consumeNum = data.consumeNum or 1
    msg.targetItemId = data.targetItemId
    msg.targetItemNum = data.targetItemNum or 1
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_COMMONATY_SHELF_GOODS_REQ, msg)
end
-- 上架物品回复
function MSG_COMMONATY_SHELF_GOODS_RSP(msg)
    -- SwapOrderData order = 2;		// 生成的订单数据	
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end
    event.Trigger(event_activity_define.TMSG_COMMONATY_SHELF_GOODS_RSP,msg)
end

---description 交换物品请求
function MSG_COMMONATY_EXCHANGE_GOODS_REQ(data)
    -- atyID
    -- orderId
    local msg = activity_pb.TMSG_COMMONATY_EXCHANGE_GOODS_REQ()
    msg.atyID = data.atyID
    msg.orderId = data.orderId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_COMMONATY_EXCHANGE_GOODS_REQ, msg)
end
-- 交换物品回复
function MSG_COMMONATY_EXCHANGE_GOODS_RSP(msg)
    -- SwapOrderData order = 2;		// 交换的订单数据	
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end
    event.Trigger(event_activity_define.TMSG_COMMONATY_EXCHANGE_GOODS_RSP,msg)
end

---description 取消交换请求
function MSG_COMMONATY_CANCLE_EXCHANGE_REQ(data)
    -- atyID
    -- orderId
    local msg = activity_pb.TMSG_COMMONATY_CANCLE_EXCHANGE_REQ()
    msg.atyID = data.atyID
    msg.orderId = data.orderId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_COMMONATY_CANCLE_EXCHANGE_REQ, msg)
end
-- 取消交换回复
function MSG_COMMONATY_CANCLE_EXCHANGE_RSP(msg)
    -- SwapOrderData order = 2;		// 交换的订单数据	
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end
    event.Trigger(event_activity_define.TMSG_COMMONATY_CANCLE_EXCHANGE_RSP,msg)
end

---description 交易订单列表请求
function MSG_COMMONATY_EXCHANGE_ORDER_LIST_REQ(data)
    -- atyID
    local msg = activity_pb.TMSG_COMMONATY_EXCHANGE_ORDER_LIST_REQ()
    msg.atyID = data.atyID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_COMMONATY_EXCHANGE_ORDER_LIST_REQ, msg)
end
-- 交易订单列表回复
function MSG_COMMONATY_EXCHANGE_ORDER_LIST_RSP(msg)
    -- repeated SwapOrderData datas = 2;			// 订单信息列表
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end
    event.Trigger(event_activity_define.TMSG_COMMONATY_EXCHANGE_ORDER_LIST_RSP,msg)
end

---description 已完成交易订单记录请求
function MSG_COMMONATY_VIEW_ORDER_RECORD_REQ(data)
    -- atyID
    -- isAlliance 是否联盟记录 0:个人记录  1：联盟记录
    local msg = activity_pb.TMSG_COMMONATY_VIEW_ORDER_RECORD_REQ()
    msg.atyID = data.atyID
    msg.isAlliance = data.isAlliance or 0
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_COMMONATY_VIEW_ORDER_RECORD_REQ, msg)
end
-- 已完成交易订单记录回复
function MSG_COMMONATY_VIEW_ORDER_RECORD_RSP(msg)
    -- repeated SwapOrderData datas = 2;		// 订单数据列表
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end
    event.Trigger(event_activity_define.TMSG_COMMONATY_VIEW_ORDER_RECORD_RSP,msg)
end

---description 隐秘宝藏点赞请求
function MSG_ACORNPUB_TREASURE_PARASE_REQ(data)
    -- orderId
    local msg = activity_pb.TMSG_ACORNPUB_TREASURE_PARASE_REQ()
    msg.orderId = data.orderId
    msg.atyID = data.atyID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_ACORNPUB_TREASURE_PARASE_REQ, msg)
end
-- 已完成交易订单记录回复
function MSG_ACORNPUB_TREASURE_PARASE_RSP(msg)
    -- required int32 SwapOrderData 	= 2;	// 订单id
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end
    event.Trigger(event_activity_define.TMSG_ACORNPUB_TREASURE_PARASE_RSP,msg)
end

-------------------------------------------------隐秘宝藏 end----------------------------------------------



-- 社区好礼奖励状态请求
function MSG_COMMUNITYGIFT_REWARDSTATUS_REQ(data) 
    local msg = activity_pb.TMSG_COMMUNITYGIFT_REWARDSTATUS_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_COMMUNITYGIFT_REWARDSTATUS_REQ, msg)
end

-- 社区好礼奖励状态回复
function MSG_COMMUNITYGIFT_REWARDSTATUS_RSP(msg)  
    -- errorcode
    -- rewardStatusList
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end
    event.Trigger(event_activity_define.TMSG_COMMUNITYGIFT_REWARDSTATUS_RSP,msg)
end

-- 社区好礼跳转请求
function MSG_COMMUNITYGIFT_JUMP_REQ(data)
    -- index
    local msg = activity_pb.TMSG_COMMUNITYGIFT_JUMP_REQ()
    msg.index = data.index
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_COMMUNITYGIFT_JUMP_REQ, msg)
end

-- 社区好礼跳转回复
function MSG_COMMUNITYGIFT_JUMP_RSP(msg)
    -- errorcode
    -- index
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end
    event.Trigger(event_activity_define.TMSG_COMMUNITYGIFT_JUMP_RSP,msg)
end

-- 社区好礼奖励领取请求
function MSG_COMMUNITYGIFT_GETREWARD_REQ(data)
    -- index
    local msg = activity_pb.TMSG_COMMUNITYGIFT_GETREWARD_REQ()
    msg.index = data.index
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_COMMUNITYGIFT_GETREWARD_REQ, msg)
end

-- 社区好礼奖励领取回复
function MSG_COMMUNITYGIFT_GETREWARD_RSP(msg)
    -- errorcode
    -- index
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end
    event.Trigger(event_activity_define.TMSG_COMMUNITYGIFT_GETREWARD_RSP,msg)
end

function MSG_MINIGAME_ACTIVITY_JION_REQ(data)
    local msg = activity_pb.TMSG_MINIGAME_ACTIVITY_JION_REQ()
    msg.nGameCfgType = data.nGameCfgType --游戏类型
    msg.nGameCfgID = data.nGameCfgID --游戏关卡id
    msg.nActivityID = data.nActivityID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_MINIGAME_ACTIVITY_JION_REQ, msg)
end

function MSG_MINIGAME_ACTIVITY_JION_RSP(msg)
    -- errorcode
    -- index
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end
    event.Trigger(event_activity_define.TMSG_MINIGAME_ACTIVITY_JION_RSP,msg)
end

function MSG_BATTLE_PASS_GET_ALL_REWARD_REQ(atyId)
    local msg = activity_pb.TMSG_BATTLE_PASS_GET_ALL_REWARD_REQ()
    msg.nActivityID = atyId or 0
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_BATTLE_PASS_GET_ALL_REWARD_REQ, msg)
end

function MSG_BATTLE_PASS_GET_ALL_REWARD_RSP(msg)
    -- errorcode
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
    end
end
local isPopReward = nil
function SetBindStateReward(_isPopReward)
    isPopReward = _isPopReward
end
function GetBindISPopReward()
    return isPopReward
end
function MSG_GETBINDEMAIL_REWARD_REQ()
    local log = require "log"
    log.Warning("Email--> MSG_GETBINDEMAIL_REWARD_REQ",isPopReward) 
    local msg = activity_pb.TMSG_GETBINDEMAIL_REWARD_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_GETBINDEMAIL_REWARD_REQ, msg)
end

function MSG_GETBINDEMAIL_REWARD_RSP(msg)
    -- errorcode
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
    end
    if isPopReward then
        if msg.rewardIDs then
            local rewardData = {}
            local reward_mgr = require "reward_mgr"
            for i, v in ipairs(msg.rewardIDs) do
                rewardData =  reward_mgr.GetRewardGoodsList(v,rewardData)
            end
            if rewardData and #rewardData > 0 then
                local listData = { title = "", dataList = rewardData }
                local showData = {}
                table.insert(showData, listData)
                local ui_reward_result = require "ui_reward_result_new"
                ui_reward_result.SetInputParam(showData)
                windowMgr:ShowModule("ui_reward_result_new")
            end
            isPopReward = nil
        end
    end

end

--预告活动通知
function MSG_ACTIVITY_NOTICEPERIOD_NTF(msg)
    event.Trigger(event_activity_define.MSG_ACTIVITY_NOTICE_PERIOD_NTF,msg)
end


---@see 评分设置请求
---@param number score
function MSG_GOOGLE_COMMENT_SET_REQ(score)
    local msg = activity_pb.TMSG_GOOGLE_COMMENT_SET_REQ()
    msg.score = score
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_GOOGLE_COMMENT_SET_REQ, msg)
end
---@see 评分设置回复
---@param number score
function MSG_GOOGLE_COMMENT_SET_RSP(msg)
    if msg.err ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
        return
    end
    local grade_data = require "grade_data"
    grade_data.SetGradeScore(msg.score)
end

--region 首冲英雄副本协议

-- 设置首充英雄副本的阵容战力信息请求
function MSG_XYX_ENDLESS_SET_HEROPOWER_REQ(data)
    -- arrHeroIDs
    -- nUnlockType
    -- nStartStageID
    local msg = activity_pb.TMSG_XYX_ENDLESS_SET_HEROPOWER_REQ()
    for i, v in ipairs(data.arrHeroIDs) do
        table.insert(msg.arrHeroIDs, v)
    end
    --msg.arrHeroIDs = data.arrHeroIDs
    msg.nUnlockType = data.nUnlockType
    msg.nStartStageID = data.nStartStageID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_ENDLESS_SET_HEROPOWER_REQ, msg)
end

-- 设置最新失败跳过小游戏关卡id
function MSG_XYX_ENDLESS_SET_HEROPOWER_RSP(msg)
    -- nStartStageID
    -- nUnlockType
    -- errorcode
    event.Trigger(event_activity_define.MSG_XYX_ENDLESS_SET_HERO_POWER_RSP,msg)
end

-- 拉取小游戏数据
function MSG_XYX_GET_DATA_REQ(data)
    local msg = activity_pb.TMSG_XYX_GET_DATA_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_GET_DATA_REQ, msg)
end


-- 领取不同类型的小游戏对应的缓存奖励
function MSG_XYX_GOT_GAME_REWARD_REQ(data)
    local msg = activity_pb.TMSG_XYX_GOT_GAME_REWARD_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_XYX_GOT_GAME_REWARD_REQ, msg)
end

-- 领取不同类型的小游戏对应的缓存奖励回复
function MSG_XYX_GOT_GAME_REWARD_RSP(msg)
    -- errorcode
    if msg and msg.errorcode == 0 then
        -- arrRewardInfo
        event.Trigger(event_activity_define.MSG_XYX_GOT_GAME_REWARD_RSP,msg)
    else
        flow_text.Add(lang.Get(100000 + msg.errorcode))
    end
end
--endregion 
--region 万圣节活动

-- 活动排行榜提交奖励请求
function MSG_ACT_RANK_SUBMIT_REQ(data)
    local msg = activity_pb.TMSG_ACT_RANK_SUBMIT_REQ()
    msg.nActivityID = data.nActivityID
    msg.itemId = data.itemId
    msg.submitCnt = data.nCount
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb_new.MSG_ACT_RANK_SUBMIT_REQ, msg)
end

-- 活动排行榜提交提交奖励请求回复
function MSG_ACT_RANK_SUBMIT_RSP(msg)
    if msg and msg.errorcode == 0 then
        
    else
        flow_text.Add(lang.Get(100000 + msg.errorcode))
    end
end

--endregion

local MessageTable = {
    {xManMsg_pb.MSG_BROKENST_MOPUP_REWARD_NTF, MSG_BROKENST_MOPUP_REWARD_NTF, activity_pb.TMSG_BROKENST_MOPUP_REWARD_NTF},
    {xManMsg_pb.MSG_BROKENST_UPDATA_BADGES_NTF, MSG_BROKENST_UPDATA_BADGES_NTF, activity_pb.TMSG_BROKENST_UPDATA_BADGES_NTF},
    {xManMsg_pb.MSG_ACTIVITY_COMPLETE_NTF, MSG_ACTIVITY_COMPLETE_NTF, activity_pb.TMSG_ACTIVITY_COMPLETE_NTF},
    {xManMsg_pb.MSG_XYX_PASS_LV_RSP, MSG_XYX_PASS_LV_RSP, activity_pb.TMSG_XYX_PASS_LV_RSP},
    {xManMsg_pb.MSG_XYX_SET_LINK_RSP, MSG_XYX_SET_LINK_RSP, activity_pb.TMSG_XYX_SET_LINK_RSP},
    {xManMsg_pb.MSG_XYX_LINK_DATA_NTF, MSG_XYX_LINK_DATA_NTF, activity_pb.TMSG_XYX_LINK_DATA_NTF},
    {xManMsg_pb.MSG_XYX_SET_LEVEL_RSP, MSG_XYX_SET_LEVEL_RSP, activity_pb.TMSG_XYX_SET_LEVEL_RSP},
    {xManMsg_pb.MSG_XYX_LEVEL_DATA_NTF, MSG_XYX_LEVEL_DATA_NTF, activity_pb.TMSG_XYX_LEVEL_DATA_NTF},
    {xManMsg_pb.MSG_XYX_SET_PROP_RSP, MSG_XYX_SET_PROP_RSP, activity_pb.TMSG_XYX_SET_PROP_RSP},
    {xManMsg_pb.MSG_XYX_GET_PROP_RSP, MSG_XYX_GET_PROP_RSP, activity_pb.TMSG_XYX_GET_PROP_RSP},
    {xManMsg_pb.MSG_XYX_PROP_NTF, MSG_XYX_PROP_NTF, activity_pb.TMSG_XYX_PROP_NTF},
    {xManMsg_pb.MSG_XYX_ITEMACTIVE_RSP, MSG_XYX_ITEMACTIVE_RSP, activity_pb.TMSG_XYX_ITEMACTIVE_RSP},
    {xManMsg_pb.MSG_XYX_ADVRW_RSP, MSG_XYX_ADVRW_RSP, activity_pb.TMSG_XYX_ADVRW_RSP},
    {xManMsg_pb.MSG_XYX_STAGE_REWARD_RSP, MSG_XYX_STAGE_REWARD_RSP, activity_pb.TMSG_XYX_STAGE_REWARD_RSP},
    {xManMsg_pb.MSG_XYX_LEVEL_RSP, MSG_XYX_LEVEL_RSP, activity_pb.TMSG_XYX_LEVEL_RSP},
    {xManMsg_pb.MSG_FACEBOOK_GUIDE_RSP, MSG_FACEBOOK_GUIDE_RSP, activity_pb.TMSG_FACEBOOK_GUIDE_RSP},
    {xManMsg_pb.MSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP, MSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP, activity_pb.TMSG_LABOURDAYACTIVITY_ACCOUNTBOOK_RSP},
    {xManMsg_pb.MSG_XYX_STAGE_REWARD_MULTI_RSP, MSG_XYX_STAGE_REWARD_MULTI_RSP, activity_pb.TMSG_XYX_STAGE_REWARD_MULTI_RSP},
    {xManMsg_pb.MSG_COMM_ACTIVITY_RANK_RSP, MSG_COMM_ACTIVITY_RANK_RSP, activity_pb.TMSG_COMM_ACTIVITY_RANK_RSP},
    {xManMsg_pb.MSG_XYX_DATA_NTF, MSG_XYX_DATA_NTF, activity_pb.TMSG_XYX_DATA_NTF},
    {xManMsg_pb.MSG_RESERVATION_EMAIL_SENDREWARD_RSP, MSG_RESERVATION_EMAIL_SENDREWARD_RSP, activity_pb.TMSG_RESERVATION_EMAIL_SENDREWARD_RSP},
    {xManMsg_pb.MSG_RESERVATION_EMAIL_BGETREWARD_RSP, MSG_RESERVATION_EMAIL_BGETREWARD_RSP, activity_pb.TMSG_RESERVATION_EMAIL_BGETREWARD_RSP},
    {xManMsg_pb_new.MSG_GATHERING_AWARD_RSP, MSG_GATHERING_AWARD_RSP, activity_pb.TMSG_GATHERING_AWARD_RSP},
    {xManMsg_pb.MSG_WORDBOSS_INFO_NTF, MSG_WORDBOSS_INFO_NTF, activity_pb.TMSG_WORDBOSS_INFO_NTF},
    {xManMsg_pb.MSG_WORDBOSS_LINE_INFO_RSP, MSG_WORDBOSS_LINE_INFO_RSP, activity_pb.TMSG_WORDBOSS_LINE_INFO_RSP},
    {xManMsg_pb.MSG_WORDBOSS_DMGRECORD_NTF, MSG_WORDBOSS_DMGRECORD_NTF, activity_pb.TMSG_WORDBOSS_DMGRECORD_NTF},
    {xManMsg_pb.MSG_WORDBOSS_ERR_NTF, MSG_WORDBOSS_ERR_NTF, activity_pb.TMSG_WORDBOSS_ERR_NTF},
    {xManMsg_pb_new.MSG_FULLBATTLE_EXCHANGE_RSP, MSG_FULLBATTLE_EXCHANGE_RSP, activity_pb.TMSG_FULLBATTLE_EXCHANGE_RSP},
    {xManMsg_pb_new.MSG_EXCHANGE_SHOP_INFO_NTF, MSG_EXCHANGE_SHOP_INFO_NTF, activity_pb.TMSG_EXCHANGE_SHOP_INFO_NTF},
    {xManMsg_pb_new.MSG_FULLBATTLE_REWARD_NTF, MSG_FULLBATTLE_REWARD_NTF, activity_pb.TMSG_FULLBATTLE_REWARD_NTF},
    {xManMsg_pb_new.MSG_LUCKY_DRAW_LOTTERY_RSP, MSG_LUCKY_DRAW_LOTTERY_RSP, activity_pb.TMSG_LUCKY_DRAW_LOTTERY_RSP},
    {xManMsg_pb_new.MSG_LUCKY_DRAW_DAILYGIFT_NTF, MSG_LUCKY_DRAW_DAILYGIFT_NTF, activity_pb.TMSG_LUCKY_DRAW_DAILYGIFT_NTF},
    {xManMsg_pb.MSG_ACTIVITY_DATA_RSP, MSG_ACTIVITY_DATA_RSP, activity_pb.TMSG_ACTIVITY_DATA_RSP},
    {xManMsg_pb_new.MSG_COMMONATY_VIEWORDER_RSP, MSG_COMMONATY_VIEWORDER_RSP, activity_pb.TMSG_COMMONATY_VIEWORDER_RSP},
    {xManMsg_pb_new.MSG_COMMONATY_SHELF_GOODS_RSP, MSG_COMMONATY_SHELF_GOODS_RSP, activity_pb.TMSG_COMMONATY_SHELF_GOODS_RSP},
    {xManMsg_pb_new.MSG_COMMONATY_EXCHANGE_GOODS_RSP, MSG_COMMONATY_EXCHANGE_GOODS_RSP, activity_pb.TMSG_COMMONATY_EXCHANGE_GOODS_RSP},
    {xManMsg_pb_new.MSG_COMMONATY_CANCLE_EXCHANGE_RSP, MSG_COMMONATY_CANCLE_EXCHANGE_RSP, activity_pb.TMSG_COMMONATY_CANCLE_EXCHANGE_RSP},
    {xManMsg_pb_new.MSG_COMMONATY_EXCHANGE_ORDER_LIST_RSP, MSG_COMMONATY_EXCHANGE_ORDER_LIST_RSP, activity_pb.TMSG_COMMONATY_EXCHANGE_ORDER_LIST_RSP},
    {xManMsg_pb_new.MSG_COMMONATY_VIEW_ORDER_RECORD_RSP, MSG_COMMONATY_VIEW_ORDER_RECORD_RSP, activity_pb.TMSG_COMMONATY_VIEW_ORDER_RECORD_RSP},
    {xManMsg_pb_new.MSG_ACORNPUB_TREASURE_PARASE_RSP, MSG_ACORNPUB_TREASURE_PARASE_RSP, activity_pb.TMSG_ACORNPUB_TREASURE_PARASE_RSP},
    {xManMsg_pb_new.MSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP, MSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP, activity_pb.TMSG_GEAR_SUPPLY_GETACTIVITYDATA_RSP},
    {xManMsg_pb_new.MSG_GEAR_SUPPLY_RECEIVEAWARD_RSP, MSG_GEAR_SUPPLY_RECEIVEAWARD_RSP, activity_pb.TMSG_GEAR_SUPPLY_RECEIVEAWARD_RSP},
    {xManMsg_pb_new.MSG_GEAR_SUPPLY_DRAWAWARD_RSP, MSG_GEAR_SUPPLY_DRAWAWARD_RSP, activity_pb.TMSG_GEAR_SUPPLY_DRAWAWARD_RSP},
    {xManMsg_pb_new.MSG_GEAR_SUPPLY_SETREWARD_RSP, MSG_GEAR_SUPPLY_SETREWARD_RSP, activity_pb.TMSG_GEAR_SUPPLY_SETREWARD_RSP},
    {xManMsg_pb_new.MSG_COMMUNITYGIFT_REWARDSTATUS_RSP, MSG_COMMUNITYGIFT_REWARDSTATUS_RSP, activity_pb.TMSG_COMMUNITYGIFT_REWARDSTATUS_RSP},
    {xManMsg_pb_new.MSG_COMMUNITYGIFT_JUMP_RSP, MSG_COMMUNITYGIFT_JUMP_RSP, activity_pb.TMSG_COMMUNITYGIFT_JUMP_RSP},
    {xManMsg_pb_new.MSG_COMMUNITYGIFT_GETREWARD_RSP, MSG_COMMUNITYGIFT_GETREWARD_RSP, activity_pb.TMSG_COMMUNITYGIFT_GETREWARD_RSP},
    {xManMsg_pb_new.MSG_MINIGAME_ACTIVITY_JION_RSP, MSG_MINIGAME_ACTIVITY_JION_RSP, activity_pb.TMSG_MINIGAME_ACTIVITY_JION_RSP},
    {xManMsg_pb_new.MSG_BATTLE_PASS_GET_ALL_REWARD_RSP, MSG_BATTLE_PASS_GET_ALL_REWARD_RSP, activity_pb.TMSG_BATTLE_PASS_GET_ALL_REWARD_RSP},
    {xManMsg_pb_new.MSG_GETBINDEMAIL_REWARD_RSP, MSG_GETBINDEMAIL_REWARD_RSP, activity_pb.TMSG_GETBINDEMAIL_REWARD_RSP},
    {xManMsg_pb_new.MSG_ACTIVITY_NOTICEPERIOD_NTF, MSG_ACTIVITY_NOTICEPERIOD_NTF, activity_pb.TMSG_ACTIVITY_NOTICEPERIOD_NTF},
    {xManMsg_pb_new.MSG_GOOGLE_COMMENT_SET_RSP, MSG_GOOGLE_COMMENT_SET_RSP, activity_pb.TMSG_GOOGLE_COMMENT_SET_RSP},
    {xManMsg_pb.MSG_XYX_ENDLESS_SET_HEROPOWER_RSP, MSG_XYX_ENDLESS_SET_HEROPOWER_RSP, activity_pb.TMSG_XYX_ENDLESS_SET_HEROPOWER_RSP},
    {xManMsg_pb.MSG_XYX_GOT_GAME_REWARD_RSP, MSG_XYX_GOT_GAME_REWARD_RSP, activity_pb.TMSG_XYX_GOT_GAME_REWARD_RSP},
    {xManMsg_pb_new.MSG_ACT_RANK_SUBMIT_RSP, MSG_ACT_RANK_SUBMIT_RSP, activity_pb.TMSG_ACT_RANK_SUBMIT_RSP},
}
net_route.RegisterMsgHandlers(MessageTable)