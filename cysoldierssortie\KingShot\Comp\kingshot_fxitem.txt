---@class kingshot_fxitem : fusion_gopoolitem
local fxItem = bc_Class("kingshot_fxitem", require("fusion_gopoolitem"))
fxItem.Fx = nil
fxItem.tween = nil

function fxItem:__init(...)
    self:Ctor(...)
    self.Fx = self.gameObject.transform:GetChild(0):GetComponent(KingShot_Define.TypeOf.ParticleSystem)
end

---开始播放特效
---@param reset boolean 是否重置特效
function fxItem:Play(reset, duration, delayCall)
    self.gameObject:SetActive(true)
    if reset then
        self.Fx:Simulate(0, true)
        self.Fx:Play()
    end
    if duration ~= nil and delayCall ~= nil then
        self.tween = KingShot_Define.DOVirtual.DelayedCall(duration, delayCall)
    end
end


function fxItem:KillTween()
    if self.tween ~= nil then
        self.tween:Kill()
        self.tween = nil
    end
end

function fxItem:Recycle()
    self:KillTween()
end

function fxItem:__delete()
    self:KillTween()
    self:Dispose()
end

return fxItem
