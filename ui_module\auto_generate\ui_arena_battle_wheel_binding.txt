local require = require
local typeof = typeof

local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local Button = CS.UnityEngine.UI.Button


module("ui_arena_battle_wheel_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenabattlewheel.prefab"

WidgetTable ={
	txt_title = { path = "main/top/txt_title", type = Text, },
	rtf_rectFace = { path = "main/playerLeft/rtf_rectFace", type = RectTransform, },
	txt_name = { path = "main/playerLeft/txt_name", type = Text, },
	txt_power = { path = "main/playerLeft/txt_power", type = Text, },
	rtf_hpl = { path = "main/playerLeft/rtf_hpl", type = RectTransform, },
	rtf_rectFacer = { path = "main/playerRight/rtf_rectFacer", type = RectTransform, },
	txt_namer = { path = "main/playerRight/txt_namer", type = Text, },
	txt_powerr = { path = "main/playerRight/txt_powerr", type = Text, },
	rtf_hprr = { path = "main/playerRight/rtf_hprr", type = RectTransform, },
	rtf_teaminfoLeft = { path = "main/rtf_teaminfoLeft", type = RectTransform, },
	btn_LeftBtn = { path = "main/btn_LeftBtn", type = Button, event_name = "OnBtnLeftBtnClickedProxy"},
	btn_switch1 = { path = "main/btn_switch1", type = Button, event_name = "OnBtnSwitch1ClickedProxy"},
	btn_switch2 = { path = "main/btn_switch2", type = Button, event_name = "OnBtnSwitch2ClickedProxy"},
	btn_startFight = { path = "main/btn_startFight", type = Button, event_name = "OnBtnStartFightClickedProxy"},
	rtf_teaminfoRight = { path = "main/rtf_teaminfoRight", type = RectTransform, },
	rtf_effect = { path = "main/rtf_effect", type = RectTransform, },

}
