local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_arena_main_common_binding"

--region View Life
module("ui_arena_main_common")
local ui_path = binding.UIPath
local window = nil
local UIView = {}
local subModuleName = nil       --子模块

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
    local isCenterCloseBtn = data and data.isCloseBtn
    if isCenterCloseBtn then
        local main_slg_const = require("main_slg_const")
        local sand_ui_event_define = require "sand_ui_event_define"
        local event = require "event"
        event.Trigger(sand_ui_event_define.GW_EXIT_EXPERIENCE,main_slg_const.MainExperimentType.Arena_Storm)
    end
end
--endregion

--region View Logic

function UIView:LoadArenaUI(data, moduleName)
    data.uiParent = self.rtf_ArenaShow
    local ui_window_mgr = require "ui_window_mgr"
    if not moduleName then
        return
    end
    subModuleName = moduleName
    ui_window_mgr:ShowModule(moduleName, nil, nil, data)
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        if subModuleName then
            local ui_window_mgr = require "ui_window_mgr"
            ui_window_mgr:UnloadModule(subModuleName)
            subModuleName = nil
        end
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
