local require = require
local table = table
local pairs = pairs
local ipairs = ipairs
local string = string
local tostring = tostring
local tonumber = tonumber
local debug = debug
local type = type
local math = math
local print = print
local os = os
local _G = _G
local util = require "util"
local json = require "dkjson"
local class = require "class"

local Application = CS.UnityEngine.Application
local Time = CS.UnityEngine.Time
local System = CS.System
local LogDebug = CS.War.Base.LogDebug

module("hook_count")

_G["hook_counts"] = _G["hook_counts"] or {}

local M = {}
function Init()
    M.ON_REGISTER_CONSOLE_SET = function(evname, name, st)
        if st~=1 then return end

        if not string.startwith(name, "sw-hc-")   then return end
        local mark = string.sub(name, 7)
        -- print("ON_REGISTER_CONSOLE_SET", evname, name, st,mark)

        if mark then
            local console_util = require("console_util")
            console_util.ResetKey(name)
            _G["hook_counts"] = _G["hook_counts"] or {}
            local hook_count = _G["hook_counts"][mark]
            if hook_count then
                hook_count:DebugCsv()
            end
        end
    end

    local event = require "event"
    event.Register("ON_REGISTER_CONSOLE_SET", M.ON_REGISTER_CONSOLE_SET)

    return M
end
Init()

function M:ctor(selftype, mark, ...)
    self.mark = mark or ""
    self.db = {}
    _G["hook_counts"] = _G["hook_counts"] or {}
    _G["hook_counts"][self.mark] = self
    local rMark = "sw-hc-" .. mark
    local console_util = require("console_util")
    console_util.RegisterConsoleOne(rMark, {2, 0})

    -- util.DelayCallOnce(
    --     0,
    --     function()
    --         util.RegisterConsole(
    --             rMark,
    --             0,
    --             function(st)
    --                 if st == 1 then
    --                     self:DebugCsv(enc)
    --                 end
    --             end
    --         )
    --     end
    -- )
    -- print("ctor",selftype,mark,...)
end

function M:StackCount(layer, ...)
    local ifo = debug.traceback("", layer)
    local mark = string.match(ifo, "traceback:\n\t(.-): in")
    self:SecCount(mark, ...)
end

function M:SecMaxFrameCount(...)
    local sec = math.floor(Time.realtimeSinceStartup)
    self:Count(sec, ...)
end

function M:SecCount(...)
    local sec = math.floor(Time.realtimeSinceStartup)
    self:Count(sec, ...)
end

function M:CallCount(...)
    local sec = math.floor(Time.realtimeSinceStartup)
    self:Count(sec, ...)
end

function M:Count(...)
    local keys = {...}
    for i = 1, #keys do
        if type(keys[i]) == "function" then
            keys[i] = ""
        end
        if not keys[i] then
            keys[i] = ""
        end
        keys[i] = tostring(keys[i])
    end
    local tick_key = table.concat(keys, "|")
    -- print(self.mark,self.db)
    local TickRecordDB = self.db
    if not TickRecordDB[tick_key] then
        TickRecordDB[tick_key] = 1
    else
        TickRecordDB[tick_key] = TickRecordDB[tick_key] + 1
    end
end

function M:DeCount(...)
    local keys = {...}
    local tick_key = table.concat(keys, "|")
    local TickRecordDB = self.db
    if not TickRecordDB[tick_key] then
        TickRecordDB[tick_key] = -1
    else
        TickRecordDB[tick_key] = TickRecordDB[tick_key] - 1
    end
end

function M:DebugCsv(enc)
    local d = {}
    d.ind = 1
    local TickRecordDB = self.db
    local n = 0
    for k, v in pairs(TickRecordDB) do
        local ls = string.split(k, "|")
        table.insert(ls, v)
        d[d.ind] = table.concat(ls, ",")

        d.ind = d.ind + 1
        n = n + 1
        if n > 100000 then
            print("break")
            break
        end
    end

    local prettyjson = table.concat(d, "\n")

    local folder = Application.dataPath

    if not Application.isEditor then
        local logDebug = LogDebug.Instance
        local logPath = logDebug:GetLogFileRootPath()
        folder = logPath
    end

    local timeStr = os.date("%Y_%m_%d_%H_%M_%S", os.time())
    local path = folder .. "/Json/" .. self.mark .. "__" .. timeStr .. ".csv"
    print("save_path", path, d.ind, string.len(prettyjson))

    util.WriteFile(path, prettyjson, "wb")
    --self:DebugLuaType()
    return path
end

function M:Debug(enc)
    local prettyjson = enc and enc(self.db) or json.encode(self.db)
    local timeStr = os.date("%Y_%m_%d_%H_%M_%S", os.time())

    local folder = Application.dataPath

    if not Application.isEditor then
        local logDebug = LogDebug.Instance
        local logPath = logDebug:GetLogFileRootPath()
        folder = logPath
    end

    local path = folder .. "/Json/" .. self.mark .. "__" .. timeStr .. ".json"
    print("save_path", path, prettyjson)
    -- util.WriteFile(path, prettyjson, "wb")
end

---@public 打印lua脚本的类型
function M:DebugLuaType()
    local d = {}
    local TickRecordDB = self.db
    for k, v in pairs(TickRecordDB) do
        local ls = string.split(k, "|")
        if ls[1] and (ls[8] == "Static") then
            table.insert(d,{mod = ls[1], order = tonumber(ls[2]),type = ls[8]})
        end        
    end  
    --排序一波
    table.sort( d, function ( a,b )
        if not a or not b then return false end
        return a.order < b.order
    end )
    --防止被截断；每400一次打印
    local count  = #d /400
    count = math.ceil(count)
    for i = 0, count -1 do
        local content = ""
        local start = i * 400 + 1
        local endIndex = (i+1)*400 > #d and #d or (i+1)*400
        for k = start, endIndex do
            --content = content ..d[k].mod .." = true,\n"
            content = content .. "\""..d[k].mod.."\",\n"
        end
        local str = string.format("打印当前require脚本的类型,数量：%d- %d 内容： {\n%s\n}",start,endIndex,content)
        print(str)
    end
end

local object = require "object"
local CM = class(object, nil, M)

return CM
