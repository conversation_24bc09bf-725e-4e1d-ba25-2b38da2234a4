---
--- Created by: y<PERSON><PERSON>.
--- DateTime: 2024/8/30.
--- Desc: 个性化帮助类，主要处理策划统一的一些读表操作，统一数据处理之类的,避免每个脚本都写一遍逻辑，不方便修改
---

local print = print
local require = require
local table = table
local stringFormat = string.format
local tonumber = tonumber
local type = type
local tostring = tostring

local math = math
local log = require "log"
local event = require "event"
local game_scheme = require "game_scheme"
local data_personalInfo = require "data_personalInfo"
local gameScheme = require "game_scheme"
local lang = require "lang"
local util = require "util"
local skep_mgr = require "skep_mgr"
local const = require "const"
local item_data = require "item_data"
local color_palette = require "color_palette"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local VertexGradient = CS.TMPro.VertexGradient
local custom_avatar_mgr = require "custom_avatar_mgr"
local custom_avatar_data = require "custom_avatar_data"
local const_personalInfo = require "const_personalInfo"
local string = string
local os = os
local GWG = GWG
local ReviewingUtil     = require "ReviewingUtil"
local ipairs = ipairs

local helper_personalInfo = {}

--region 获得个性化物品通用数据，对齐策划：代闯 修改日期：2024/8/30 
--- 统一规则的表：RoleFace RoleSchloss RoleFrame RoleTitle
--- 统一属性规则：
--- 1. 每张表的ID：对应Item表ID
--- 2. PieceParam：佩戴属性，读取GWMapEffect
--- 3. PieceParam2：拥有属性，读取GWMapEffect
--- 4. 品质：统一读取Item表的quality
--- 5. 名字：优先读取每张表的NameID，如果没有配置或者配置是0，则读取Item表的nameKey
local function getPersonalisedItemConfig(id, cfg, propId)
    --- 统一多张表的配置数据结构
    local config = {
        cfg = cfg,
        id = id,
        nameId = 0,
        iconId = 0,
        modelId = 0,
        quality = 1,

        propId = propId,
        priority = cfg.Priority,
        automaticEquipment = cfg.AutomaticEquipment,

        showLimit = 0, --对应配置表开服时间（h）
        unlocked = false,
        expireTime = 0,
        useAttributes = {},
        haveAttributes = {}
    }

    local itemCfg = gameScheme:Item_0(id)
    if not itemCfg then
        log.Error(stringFormat("Item表配置 %s 缺失", id or "nil"))
        return
    end

    if cfg.NameID and cfg.NameID ~= 0 then
        config.nameId = cfg.NameID
    else
        config.nameId = itemCfg.nameKey
    end

    if cfg.rivalType and cfg.rivalType ~= 0 then
        config.iconId = cfg.rivalType
    else
        config.iconId = itemCfg.icon
    end
    -- 模型只在配置中设置
    if cfg.ModulID and cfg.ModulID ~= 0 then
        config.modelId = cfg.ModulID
    end
    if cfg.ShowLimit and cfg.ShowLimit > 0 then
        config.showLimit = cfg.ShowLimit
    end
    config.quality = cfg.quality or itemCfg.quality
    -- 在背包中寻找数据
    local entity = skep_mgr.GetGoodsEntity(id)
    if entity then
        config.unlocked = helper_personalInfo.UnlockCheck(id, propId)
        config.expireTime = entity:GetExpireTime()
    end

    config.useAttributes = { }
    if cfg.PieceParam and cfg.PieceParam.data then
        for i = 1, cfg.PieceParam.count do
            config.useAttributes[i] = cfg.PieceParam.data[i - 1]
        end
    end

    config.haveAttributes = { }
    if cfg.PieceParam2 and cfg.PieceParam2.data then
        for i = 1, cfg.PieceParam2.count do
            config.haveAttributes[i] = cfg.PieceParam2.data[i - 1]
        end
    end

    if propId == data_personalInfo.PropEnum.AnimalsID then
        config.unlocked = true
        config.expireTime = 0
        if GWG and GWG.GWHomeMgr and GWG.GWHomeMgr.droneData then
            config.adornID = GWG.GWHomeMgr.droneData.GetDroneId()
        end
    end
    return config
end

--- 检查页签是否被查看过
function helper_personalInfo.CheckPageChecked(tagEnum)
    local key = helper_personalInfo.GetPageCheckKey(tagEnum)
    local checked = PlayerPrefs.GetInt(key, 0) == 1 and true or false
    return checked
end

function helper_personalInfo.SetPageChecked(tagEnum)
    local key = helper_personalInfo.GetPageCheckKey(tagEnum)
    PlayerPrefs.SetInt(key, 1)
    PlayerPrefs.Save()
end

function helper_personalInfo.GetPageCheckKey(tagEnum)
    local roleID = tostring(data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID))
    local key = stringFormat("%s_personalInfo_page_checked_%s", roleID, tagEnum)
    return key
end

function helper_personalInfo.GetItemCheckKey(itemInfo)
    local roleID = tostring(data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID))
    local key = stringFormat("%s_personalInfo_page_checked_%s", roleID, itemInfo.id)
    return key
end

function helper_personalInfo.GetItemCheckKeyByID(id)
    local roleID = tostring(data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID))
    local key = stringFormat("%s_personalInfo_page_checked_%s", roleID, tostring(id))
    return key
end

function helper_personalInfo.SetItemCheckByID(id, b)
    local key = helper_personalInfo.GetItemCheckKeyByID(id)
    PlayerPrefs.SetInt(key, b and 1 or 0)
    PlayerPrefs.Save()
end


function helper_personalInfo.CheckItemChecked(itemInfo)
    if not itemInfo.unlocked then
        return false
    end
    local key = helper_personalInfo.GetItemCheckKey(itemInfo)
    local checked = PlayerPrefs.GetInt(key, 0) == 1 and true or false
    return checked
end

function helper_personalInfo.UnlockCheck(id, tagEnum)
    -- 在背包中寻找数据
    local entity = skep_mgr.GetGoodsEntity(id)
    if tagEnum == data_personalInfo.PropEnum.NamePlateID or tagEnum == const_personalInfo.PersonalisedTag.namePlate then
        if entity then
            local expireTime = entity:GetExpireTime()
            --log.Warning(string.format("helper_personalInfo.UnlockCheck id=%s expireTime=%s", 
            --tostring(id), tostring(expireTime)))
            if expireTime > 0 then
                return true
            else
                --视为不存在（仅存在消耗道具待激活
                --现有的删掉
                helper_personalInfo.SetItemCheckByID(id, false)
                return false
            end
        else
            --现有的删掉
            helper_personalInfo.SetItemCheckByID(id, false)
        end
    else
        if entity then
            return true
        end
    end

    return false
end

function helper_personalInfo.GetUncheckedItems(tagEnum)
    if not tagEnum then
        return {}
    end

    local uncheckedItems = {}
    if tagEnum == data_personalInfo.PropEnum.NamePlateID or tagEnum == const_personalInfo.PersonalisedTag.namePlate then
        local items = helper_personalInfo.GetNamePlateConfigArray()
        for i = 1, #items do
            items[i].unlocked = helper_personalInfo.UnlockCheck(items[i].id, tagEnum)

            if items[i].unlocked and not helper_personalInfo.CheckItemChecked(items[i]) then
                table.insert(uncheckedItems, items[i])
            end
        end
    end
    
    return uncheckedItems
end

function helper_personalInfo.SetItemChecked(itemInfo)
    if itemInfo then
        local key = helper_personalInfo.GetItemCheckKey(itemInfo)
        PlayerPrefs.SetInt(key, 1)
        PlayerPrefs.Save()
    end
end

function helper_personalInfo.SetPageItemsChecked(tagEnum)
    local uncheckedItems = helper_personalInfo.GetUncheckedItems(tagEnum)
    if #uncheckedItems == 0 then
        return
    end
    for i = 1, #uncheckedItems do
        helper_personalInfo.SetItemChecked(uncheckedItems[i])
    end
end

--- 获得个性化头像的显示配置
function helper_personalInfo.GetFaceItemConfig(id, noDefault)
    local cfg = gameScheme:RoleFace_0(tonumber(id))
    if not cfg and not noDefault then
        local defaultFaceCfg = game_scheme:InitBattleProp_0(8127)
        if defaultFaceCfg and defaultFaceCfg.szParam and defaultFaceCfg.szParam.data then
            cfg = gameScheme:RoleFace_0(defaultFaceCfg.szParam.data[0])
        end
    end

    if cfg then
        -- 头像的解锁特殊处理
        local itemTable = getPersonalisedItemConfig(cfg.headID, cfg, data_personalInfo.PropEnum.FaceID)
        local actor_face_data = require "actor_face_data"
        itemTable.unlocked = actor_face_data.isActiveRoleFace(itemTable.id)
        -- 自定义头像的特殊处理
        if itemTable.id < 10 then
            itemTable.nameId = 720106
            itemTable.unlocked = true
            itemTable.isCustomHead = true
            itemTable.customAvatarData = custom_avatar_data.GetMyAvatar()
            itemTable.delEvent = function()
                custom_avatar_mgr.DelCustomAvatar()
            end
            itemTable.clickFunc = function()
                --关闭换头像引导
                local unforced_guide_mgr = require "unforced_guide_mgr"
                if unforced_guide_mgr.GetCurGuide() == 25 and unforced_guide_mgr.GetCurStep() then
                    local unforced_guide_event_define = require "unforced_guide_event_define"
                    event.Trigger(unforced_guide_event_define.click_custom_face_return)
                end
                --检查打开上传图片的先决条件，然后调用sdk的api打开上传图片的接口
                custom_avatar_mgr.CustomizeAvatar()
            end
            --自定义头像的开关
            if not const.ONOFF_CUSTOM_AVATAR then
                return nil
            end
        end

        return itemTable
    end
end

--- 获得个性化城堡的显示配置
function helper_personalInfo.GetSchlossItemConfig(id, noDefault)
    local cfg = gameScheme:RoleSchloss_0(id)
    if not cfg and not noDefault then
        local defaultSchlossCfg = game_scheme:InitBattleProp_0(8226)
        if defaultSchlossCfg and defaultSchlossCfg.szParam and defaultSchlossCfg.szParam.data then
            cfg = gameScheme:RoleSchloss_0(defaultSchlossCfg.szParam.data[0])
        end
    end
    if cfg then
        return getPersonalisedItemConfig(cfg.CastleID, cfg, data_personalInfo.PropEnum.CityID)
    end
end

function helper_personalInfo.GetNamePlateConfig(id)
    local cfg = gameScheme:RolePlate_0(id) 
    if not cfg then
        --log.Warning("[helper_personalInfo.GetNamePlateConfig] no cfg for id: %s", id)
        local defaultNamePlateCfg = game_scheme:InitBattleProp_0(const_personalInfo.DefaultID.NamePlate) 
        if defaultNamePlateCfg and defaultNamePlateCfg.szParam and defaultNamePlateCfg.szParam.data then
            local default_plate_id = defaultNamePlateCfg.szParam.data[0]
            --debug
            --default_plate_id = 9502
            cfg = gameScheme:RolePlate_0(default_plate_id)
        end
    end
    return cfg
end

--- 获得个性化铭牌的显示配置
function helper_personalInfo.GetNamePlateItemConfig(id, noDefault)
    local cfg = gameScheme:RolePlate_0(id) 
    if not cfg and not noDefault then
        local defaultNamePlateCfg = game_scheme:InitBattleProp_0(const_personalInfo.DefaultID.NamePlate) 
        if defaultNamePlateCfg and defaultNamePlateCfg.szParam and defaultNamePlateCfg.szParam.data then
            local default_plate_id = defaultNamePlateCfg.szParam.data[0]
            cfg = gameScheme:RolePlate_0(default_plate_id)
        end
    end
    if cfg then
        return getPersonalisedItemConfig(cfg.plateID, cfg, data_personalInfo.PropEnum.NamePlateID)
    end
end

---获取个性化神兽装扮的显示配置
function helper_personalInfo.GetAnimalItemConfig(id, Default)
    local cfg = gameScheme:AccessoryAdorn_0(id)
    if not cfg and Default then
        local defaultAnimalCfg = game_scheme:InitBattleProp_0(8234)
        if defaultAnimalCfg and defaultAnimalCfg.szParam and defaultAnimalCfg.szParam.data then
            cfg = gameScheme:AccessoryAdorn_0(defaultAnimalCfg.szParam.data[0])
        end
    end
    if cfg then
        local config = getPersonalisedItemConfig(cfg.adornID, cfg, data_personalInfo.PropEnum.AnimalsID)
        config.unlocked = helper_personalInfo.IsActiveAnimalAdorn(cfg.adornID)
        return config   
    end
end

--- 获得个性化城堡特效的显示配置
function helper_personalInfo.GetSchlossEffectItemConfig(id, noDefault)
    local cfg = gameScheme:RoleSchlossEffect_0(id)
    if not cfg and not noDefault then
        local const_personalInfo = require "const_personalInfo"
        local defaultId = const_personalInfo.defaultEffectId
        cfg = gameScheme:RoleSchlossEffect_0(defaultId)
    end
    if cfg then
        return getPersonalisedItemConfig(cfg.CastleID, cfg, data_personalInfo.PropEnum.EffectID)
    end
end

function helper_personalInfo.IsActiveAnimalAdorn(id)
    local defaultAnimalCfg = game_scheme:InitBattleProp_0(8234)
    if defaultAnimalCfg and defaultAnimalCfg.szParam and defaultAnimalCfg.szParam.data then
        local cfg = gameScheme:AccessoryAdorn_0(defaultAnimalCfg.szParam.data[0])
        if cfg and cfg.adornID == id then
            return true
        else
            local entity = skep_mgr.GetGoodsEntity(id)
            if entity then
                return true
            else
                return false
            end
        end
    end
    return false
end

--- 获得个性化头像框的显示配置
function helper_personalInfo.GetFrameItemConfig(id, noDefault)
    local cfg = gameScheme:RoleFrame_0(id)
    if not cfg and not noDefault then
        local defaultFrameCfg = game_scheme:InitBattleProp_0(1228)
        if defaultFrameCfg and defaultFrameCfg.szParam and defaultFrameCfg.szParam.data then
            cfg = gameScheme:RoleFrame_0(defaultFrameCfg.szParam.data[0])
        end
    end
    if cfg then
        return getPersonalisedItemConfig(cfg.frameID, cfg, data_personalInfo.PropEnum.FrameID)
    end
end

--- 获得个性化称号的显示配置
function helper_personalInfo.GetTitleItemConfig(id, noDefault)
    local cfg = gameScheme:RoleTitle_0(id)
    -- todo 默认称号
    if cfg then
        return getPersonalisedItemConfig(id, cfg, data_personalInfo.PropEnum.TitleID)
    end
end

--- 获得个性化信息的显示配置
function helper_personalInfo.GetPersonalisedItemConfig(id)
    local faceCfg = helper_personalInfo.GetFaceItemConfig(id, true)
    if faceCfg then
        return faceCfg
    end
    local schlossCfg = helper_personalInfo.GetSchlossItemConfig(id, true)
    if schlossCfg then
        return schlossCfg
    end
    local frameCfg = helper_personalInfo.GetFrameItemConfig(id, true)
    if frameCfg then
        return frameCfg
    end
    local titleCfg = helper_personalInfo.GetTitleItemConfig(id, true)
    if titleCfg then
        return titleCfg
    end
    local effectCfg = helper_personalInfo.GetSchlossEffectItemConfig(id, true)
    if effectCfg then
        return effectCfg
    end
    --这里增加

    local namePlateCfg = helper_personalInfo.GetNamePlateItemConfig(id, true)
    if namePlateCfg then
        return namePlateCfg
    end
    
    --托底  需要新增的往上增加
    local animalCfg = helper_personalInfo.GetAnimalItemConfig(id, true)
    if animalCfg then
        return animalCfg
    end
    return nil
end
--endregion

--region 获得属性描述和计算的值，对齐策划：代闯 修改日期：2024/8/29
--- 统一规则：所有属性统一在GWMapEffect表
--- 统一属性规则：
--- 名字：统一读取表的name，如果名字为nil 读proToLang的表
--- 值：统一读取表的strParam.data[0],目前的值使用万分比计算(固定)
function helper_personalInfo.GetPersonalisedProperty(id)
    local cfg = gameScheme:GWMapEffect_0(id)
    if cfg then
        local groupId = cfg.nGroupID
        local nameId = cfg.name
        if nameId == 0 then
            local proToLangCfg = game_scheme:ProToLang_0(cfg.nGroupID)
            if proToLangCfg then
                nameId = proToLangCfg.iLangId
            end
        end
        local value = 0
        if cfg.strParam and cfg.strParam.count > 0 then
            value = cfg.strParam[0]
        end
        return groupId, nameId, value
    else
        return 0, 0, 0
    end
end

function helper_personalInfo.GetPersonalisedPropertyArray(ids)
    local array = { }
    for i = 1, #ids do
        local groupId, nameId, value = helper_personalInfo.GetPersonalisedProperty(ids[i])
        if groupId ~= 0 then
            array[groupId] = array[groupId] or {
                nameId = nameId,
                value = 0
            }
            array[groupId].value = array[groupId].value + value
        end
    end
    return array
end

function helper_personalInfo.GetPersonalisedAllRedNum()
    local num = 0
    -- 目前只计算铭牌页签，其他0
    num = num + helper_personalInfo.GetPersonalisedPageRedNum(const_personalInfo.PersonalisedTag.namePlate)
    return num
end

function helper_personalInfo.GetPersonalisedPageRedNum(personalisedTag)
    local num = 0
    -- 目前只判断铭牌页签，其他0
    if personalisedTag == const_personalInfo.PersonalisedTag.namePlate then
        -- 首先判断页签是否查看
        if not helper_personalInfo.CheckPageChecked(personalisedTag) then
            num = num + 1
            return num
        end
        num = num + #helper_personalInfo.GetUncheckedItems(data_personalInfo.PropEnum.NamePlateID)
    end

    return num
end

local function toTenThousandthPercentage(number)
    if number and (type(number) == "number" or number % 1 == 0) then
        local percentage = (number / 10000) * 100
        return tostring(percentage) .. "%"  -- 将百分比取整并添加百分号
    end
end

function helper_personalInfo.GetPersonalisedPropertyText(nameID, value)
    if nameID and value then
        local name = lang.Get(nameID)
        local valueStr = ""
        if value > 0 then
            valueStr = stringFormat("+%s", toTenThousandthPercentage(value))
        elseif value < 0 then
            valueStr = stringFormat("-%s", toTenThousandthPercentage(value))
        end
        return name, valueStr
    end
end
--endregion

--region 获得个性化配置的数组，对齐策划：代闯 修改日期：2024/8/30 
--- 统一规则的表：RoleFace RoleSchloss RoleFrame RoleTitle
--- 统一属性规则：取出对应的表信息,排序规则
--- 1. 已解锁 > 未解锁 unlocked
--- 2. 高优先级 > 低优先级,Priority
--- 3. 高品质 > 低品质,quality
--- 4. 所有获得的数组配置，都新增一个字段 unlocked
local function compareItems(a, b)
    if a.unlocked ~= b.unlocked then
        return a.unlocked and not b.unlocked  -- 已解锁的排在未解锁之前
    elseif a.priority ~= b.priority then
        return a.priority > b.priority  -- 高优先级排在低优先级之前
    else
        return a.quality > b.quality  -- 高品质排在低品质之前
    end
end

--- 获得头像配置数据集
function helper_personalInfo.GetFaceConfigArray()
    local array = { }
    local count = gameScheme:RoleFace_nums()
    local player_mgr = require "player_mgr"
    local openSerTime = player_mgr.GetRoleOpenSvrTime()
    local serverTime = os.server_time()
    local openHour = math.floor((serverTime - openSerTime) / 3600)
    for i = 0, count - 1 do
        local cfg = gameScheme:RoleFace(i)
        -- 头像用OpenTime不用新的?
        if cfg and util.compareServerTime(cfg.OpenTime) and cfg.InfoShow == 1 then
            local itemTable = helper_personalInfo.GetFaceItemConfig(cfg.headID)
            if itemTable then
                --审核处理
                if itemTable.id >= 10 or not ReviewingUtil.IsReviewing() then
                    table.insert(array, itemTable)
                end
            end
        end
    end
    table.sort(array, compareItems)
    return array
end

--- 获得城堡配置数据集
--- 城堡的Icon特殊处理，需要读取Item表的icon
function helper_personalInfo.GetSchlossConfigArray()
    local array = { }
    local count = gameScheme:RoleSchloss_nums()
    local player_mgr = require "player_mgr"
    local openSerTime = player_mgr.GetRoleOpenSvrTime()
    local serverTime = os.server_time()
    local openHour = math.floor((serverTime - openSerTime) / 3600)
    for i = 0, count - 1 do
        local cfg = gameScheme:RoleSchloss(i)
        local itemTable = helper_personalInfo.GetSchlossItemConfig(cfg.CastleID)
        if not itemTable then
            log.Warning(string.format("GetSchlossConfigArray: %s is invalid", cfg.CastleID))
        end
        if itemTable then
            local isServerOpenTimeCheckOK = openHour >= itemTable.showLimit

            if itemTable and (itemTable.unlocked or cfg.IsShow == 1) and isServerOpenTimeCheckOK then
                table.insert(array, itemTable)
            end
        end
        
    end
    table.sort(array, compareItems)
    return array
end

--- 获得铭牌配置数据集
function helper_personalInfo.GetNamePlateConfigArray()
    local array = { }
    local count = gameScheme:RolePlate_nums() 
    local player_mgr = require "player_mgr"
    local openSerTime = player_mgr.GetRoleOpenSvrTime()
    local serverTime = os.server_time()
    local openHour = math.floor((serverTime - openSerTime) / 3600)
    for i = 0, count - 1 do
        local cfg = gameScheme:RolePlate(i) 
        local itemTable = helper_personalInfo.GetNamePlateItemConfig(cfg.plateID)
        if not itemTable then
            log.Warning(string.format("GetNamePlateConfigArray: %s is invalid", cfg.plateID))
        end

        if itemTable then
            local isServerOpenTimeCheckOK = openHour >= itemTable.showLimit

            if (itemTable.unlocked or cfg.IsShow == 1) and isServerOpenTimeCheckOK then
                table.insert(array, itemTable)
            end
        end
        
    end
    table.sort(array, compareItems)
    return array
end

--- 获得个性化头像框的显示配置
function helper_personalInfo.GetFrameConfigArray()
    local array = { }
    local count = gameScheme:RoleFrame_nums()
    local player_mgr = require "player_mgr"
    local openSerTime = player_mgr.GetRoleOpenSvrTime()
    local serverTime = os.server_time()
    local openHour = math.floor((serverTime - openSerTime) / 3600)
    for i = 0, count - 1 do
        local cfg = gameScheme:RoleFrame(i)
        if cfg.isDelete == 0 then
            local itemTable = helper_personalInfo.GetFrameItemConfig(cfg.frameID)
            if not itemTable then
                log.Warning(string.format("GetFrameConfigArray: %s is invalid", cfg.frameID))
            end
            if itemTable then
                local isServerOpenTimeCheckOK = openHour >= itemTable.showLimit

                if itemTable and (itemTable.unlocked or cfg.IsShow == 1) and isServerOpenTimeCheckOK then
                    table.insert(array, itemTable)
                end
            end
            
        end
    end
    table.sort(array, compareItems)
    return array
end

--- 获得个性化称号的显示配置
function helper_personalInfo.GetTitleConfigArray()
    local array = { }
    local count = gameScheme:RoleTitle_nums()
        local player_mgr = require "player_mgr"
    local openSerTime = player_mgr.GetRoleOpenSvrTime()
    local serverTime = os.server_time()
    local openHour = math.floor((serverTime - openSerTime) / 3600)
    for i = 0, count - 1 do
        local cfg = gameScheme:RoleTitle(i)
        if cfg.LimitType ~= 6 then
            local itemTable = helper_personalInfo.GetTitleItemConfig(cfg.TitleID)
            if itemTable then
                local isServerOpenTimeCheckOK = openHour >= itemTable.showLimit
            
                if itemTable and (itemTable.unlocked or cfg.IsShow == 1) and isServerOpenTimeCheckOK then
                    table.insert(array, itemTable)
                end
            end
            
        end
    end
    table.sort(array, compareItems)
    return array
end

function helper_personalInfo.GetAnimalConfigArray()
    local array = { }
    local count = gameScheme:AccessoryAdorn_nums()
    local player_mgr = require "player_mgr"
    local openSerTime = player_mgr.GetRoleOpenSvrTime()
    local serverTime = os.server_time()
    local openHour = math.floor((serverTime - openSerTime) / 3600)
    for i = 0, count - 1 do
        local cfg = gameScheme:AccessoryAdorn(i)
        local itemTable = helper_personalInfo.GetAnimalItemConfig(cfg.adornID)
        local isServerOpenTimeCheckOK = openHour >= itemTable.showLimit
        
        if itemTable and isServerOpenTimeCheckOK then
            table.insert(array, itemTable)
        end
    end
    return array
end
---@public 获得城堡特效配置数据集
function helper_personalInfo.GetEffectConfigArray()
    local array = { }
    local count = gameScheme:RoleSchlossEffect_nums()
    local player_mgr = require "player_mgr"
    local openSerTime = player_mgr.GetRoleOpenSvrTime()
    local serverTime = os.server_time()
    local openHour = math.floor((serverTime - openSerTime) / 3600)
    for i = 0, count - 1 do
        local cfg = gameScheme:RoleSchlossEffect(i)
        local itemTable = helper_personalInfo.GetSchlossEffectItemConfig(cfg.CastleID)
        if not itemTable then
            log.Warning(string.format("GetEffectConfigArray: %s is invalid", cfg.CastleID))
        end
        if itemTable then
            local isServerOpenTimeCheckOK = openHour >= itemTable.showLimit

            if itemTable and isServerOpenTimeCheckOK then
                table.insert(array, itemTable)
            end
        end
        
    end
    return array
end
--endregion

--region 配置属性色，对齐美术：朱晓晶 修改日期：2024/8/30 
function helper_personalInfo.SetQualityTMPColor(tmp, quality, useQualityColor2)
    if tmp and quality then
        local colorTable = useQualityColor2 and item_data.Item_Quality_ColorEnum2_GW[quality] or item_data.Item_Quality_ColorEnum1_GW[quality]
        if colorTable then
            tmp.color = color_palette.HexToColor(colorTable.vertexColor)
            tmp.enableVertexGradient = colorTable.gradient
            if colorTable.gradient then
                local gradient = VertexGradient(color_palette.HexToColor(colorTable.topColor), color_palette.HexToColor(colorTable.topColor), color_palette.HexToColor(colorTable.bottomColor), color_palette.HexToColor(colorTable.bottomColor));
                tmp.colorGradient = gradient;
            end
        end
    end
end

function helper_personalInfo.SetQualityTextGradientColor(text, gradient, quality, useQualityColor2)
    if text and gradient and quality then
        local colorTable = useQualityColor2 and item_data.Item_Quality_ColorEnum2_GW[quality] or item_data.Item_Quality_ColorEnum1_GW[quality]
        if colorTable then
            text.color = color_palette.HexToColor(colorTable.vertexColor)
            gradient.enabled = colorTable.gradient
            if colorTable.gradient then
                gradient.TopColor = color_palette.HexToColor(colorTable.topColor)
                gradient.BottomColor = color_palette.HexToColor(colorTable.bottomColor)
            end
        end
    end
end
--endregion

--region 获得城堡模型的地址，对齐策划：代闯，修改日期：2024/10/21
helper_personalInfo.SchlossPrefabType = {
    -- 沙盘
    sand = 1,
    -- 城建
    building = 2,
    -- 个性化
    personalised = 3,
}

function helper_personalInfo.GetSchlossPrefabPath(prefabType, schlossId)
    local cityId = schlossId or data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.CityID)
    if cityId and cityId > 0 then
        local cfg = game_scheme:RoleSchloss_0(tonumber(cityId))
        if cfg then
            local helper_personalInfo = require "helper_personalInfo"
            if prefabType == helper_personalInfo.SchlossPrefabType.sand then
                return cfg.SandModuleRoute
            elseif prefabType == helper_personalInfo.SchlossPrefabType.building then
                return cfg.buildingModul
            else
                return cfg.ModulRoute
            end
        end
    end
    return nil
end
--endregion

--获取个性化神兽的模型地址
function helper_personalInfo.GetAnimalPrefabPath(animalId)
    local animalId = animalId or data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.AnimalsID) --获取无人机装饰ID
    if animalId > 0 then
        local cfg = game_scheme:AccessoryAdorn_0(tonumber(animalId))
        if cfg then
            --print("GetAnimalPrefabPath",cfg.modelID)
            local modulCfg = game_scheme:Modul_0(cfg.modelID)
            local ModulePath = modulCfg.modelPath
            return ModulePath
        end
    end
    return nil
end

--region 获得城堡特效模型的地址
function helper_personalInfo.GetSchlossEffectPrefabPath(prefabType,schlossId)
    local effectId = schlossId or data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.EffectID)
    local cfg = game_scheme:RoleSchlossEffect_0(tonumber(effectId))
    if cfg then
        local scaleParam = nil
        local helper_personalInfo = require "helper_personalInfo"
        if prefabType == helper_personalInfo.SchlossPrefabType.sand then
            scaleParam = cfg.Scale
        elseif prefabType == helper_personalInfo.SchlossPrefabType.building then
            scaleParam = cfg.buildingScale
        else
            scaleParam = cfg.ModulScale
        end
        local offsetPos = nil
        if scaleParam.count >= 4 then
            offsetPos = { x = scaleParam.data[1], y = scaleParam.data[2], z = scaleParam.data[3] }
        end
        return cfg.Effect, effectId, scaleParam.data[0], offsetPos, cfg.EffectBottom
    end
    return nil
end

-- 程序需求 增加开关 控制显示 当前服的名字和world (个人信息页面worldName 和 worldID 同时显示使用，方便测试时知道是谁的服 仅限主干测试使用 不上分支)
function helper_personalInfo.SetWorldAndServerNameToShow(str,ShowState) 
    if not str or type(str) ~= "string" then 
        return str
    end
    local val = require "val"
    local finalStr = ""
    local setting_server_data = require "setting_server_data"
    if val.IsTrue("sw_show_long_worldID",0) then 
        local data = setting_server_data.GetServerListData()
        local curServerid = setting_server_data.GetLoginWorldID()	
        
        for i,v in ipairs(data) do 
            if curServerid == v.id and v.name then 
                finalStr = finalStr .. v.name .. " "
                break
            end
        end
    end
    return finalStr .. str
end
--endregion

return helper_personalInfo
