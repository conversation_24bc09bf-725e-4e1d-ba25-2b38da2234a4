local require = require
local typeof = typeof

local RectTransform = CS.UnityEngine.RectTransform
local Image = CS.UnityEngine.UI.Image
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local SpriteSwitcher = CS.War.UI.SpriteSwitcher


module("ui_arena_battle_result_wheel_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenabattleresultwheel.prefab"

WidgetTable ={
	rtf_Win = { path = "rtf_Win", type = RectTransform, },
	rtf_victoryEffect = { path = "rtf_Win/rtf_victoryEffect", type = RectTransform, },
	rtf_victoryEffect1 = { path = "rtf_Win/rtf_victoryEffect1", type = RectTransform, },
	rtf_winLeftBG = { path = "rtf_Win/rtf_winLeftBG", type = RectTransform, },
	rtf_winRightBG = { path = "rtf_Win/rtf_winRightBG", type = RectTransform, },
	rtf_winImg = { path = "rtf_Win/rtf_winImg", type = RectTransform, },
	rtf_winImgEn = { path = "rtf_Win/rtf&img_winImgEn", type = RectTransform, },
	img_winImgEn = { path = "rtf_Win/rtf&img_winImgEn", type = Image, },
	rtf_Lose = { path = "rtf_Lose", type = RectTransform, },
	rtf_defeatEffect = { path = "rtf_Lose/rtf_defeatEffect", type = RectTransform, },
	rtf_loseLeftBG = { path = "rtf_Lose/rtf_loseLeftBG", type = RectTransform, },
	rtf_loseRightBG = { path = "rtf_Lose/rtf_loseRightBG", type = RectTransform, },
	rtf_loseImg = { path = "rtf_Lose/rtf_loseImg", type = RectTransform, },
	rtf_loseImgEn = { path = "rtf_Lose/rtf&img_loseImgEn", type = RectTransform, },
	img_loseImgEn = { path = "rtf_Lose/rtf&img_loseImgEn", type = Image, },
	rtf_effect = { path = "rtf_effect", type = RectTransform, },
	rtf_node = { path = "rtf_node", type = RectTransform, },
	rtf_rectFace = { path = "rtf_node/playerLeft/rtf_rectFace", type = RectTransform, },
	txt_name = { path = "rtf_node/playerLeft/txt_name", type = Text, },
	txt_power = { path = "rtf_node/playerLeft/txt_power", type = Text, },
	rtf_hpl = { path = "rtf_node/playerLeft/rtf_hpl", type = RectTransform, },
	rtf_rectFacer = { path = "rtf_node/playerRight/rtf_rectFacer", type = RectTransform, },
	txt_namer = { path = "rtf_node/playerRight/txt_namer", type = Text, },
	txt_powerr = { path = "rtf_node/playerRight/txt_powerr", type = Text, },
	rtf_hprr = { path = "rtf_node/playerRight/rtf_hprr", type = RectTransform, },
	rtf_teaminfoLeft = { path = "rtf_node/rtf_teaminfoLeft", type = RectTransform, },
	rtf_teaminfoRight = { path = "rtf_node/rtf_teaminfoRight", type = RectTransform, },
	rtf_wintimebg_l = { path = "rtf_node/rtf_wintimebg_l", type = RectTransform, },
	txt_winTimes = { path = "rtf_node/rtf_wintimebg_l/txt_winTimes", type = Text, },
	rtf_wintimebg_r = { path = "rtf_node/rtf_wintimebg_r", type = RectTransform, },
	txt_winTimesr = { path = "rtf_node/rtf_wintimebg_r/txt_winTimesr", type = Text, },
	btn_mail = { path = "rtf_node/btn_mail", type = Button, event_name = "OnBtnMailClickedProxy"},
	btn_share = { path = "rtf_node/btn_share", type = Button, event_name = "OnBtnShareClickedProxy"},
	txt_oldRank = { path = "RankChange/txt_oldRank", type = Text, },
	txt_curRank = { path = "RankChange/txt_curRank", type = Text, },
	ss_rankChange = { path = "RankChange/ss_rankChange", type = SpriteSwitcher, },

}
