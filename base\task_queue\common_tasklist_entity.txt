local table = table
local log = require "log"
local xpcall = xpcall
local print = print
local base_object = require "base_object"
local class = require "class"
module("common_tasklist_entity")
local M = {}
local taskGroup = {} --按标签分组，标签有处理顺序优先级，+最优先顺序列表，默认最后执行列表，这是个字典
local tableExistInGroupList = {} --已存在的标签组，已经分好组，在update的时候遍历增加效率
local taskExistGroupCount = {} --已存在的组中，任务的数量记录
local totalTaskGroup = 0
local tagPirority = { 
	last = -1, 	 --可以放到最晚执行的操作序列
	default = 0, --默认的优先级 ，优先级越高，插入到越前面执行
} 
local taskRun = true
function M:StartTask()
	taskRun = true
	print("zlog task start",self.key)
end
function M:PauseTask()
	taskRun = false
	print("zlog task stop",self.key)
end
--插入任务，任务默认执行，分组默认是最后一个分组，执行顺序在所有任务后面
function M:Push(taskFunc,taskParam,tag,canTaskRepeat) 
	canTaskRepeat = canTaskRepeat or false --能否重复添加任务，默认不重复添加
	taskParam = taskParam or "common_Param" 
	tag = tag or tagPirority.default
	--新建优先级分组序列，按照tag的大小对优先级进行排序，tag越大，组优先级越高 
	if taskGroup[tag] == nil then
		taskGroup[tag] = {}
		taskGroup[tag].key = {} --检索的字典
		taskGroup[tag].data = {} --遍历的data数据列表
		taskExistGroupCount[tag] = 0 --记录分组内任务数量 
		local insertPos = #tableExistInGroupList + 1 --初始化默认是最后一个组
		for i=1,#tableExistInGroupList do
			if tableExistInGroupList[i] < tag then
				insertPos = i
				break
			end
		end 
		table.insert(tableExistInGroupList,insertPos,tag) --用于做update的遍历，需要对任务进行排序 
		totalTaskGroup = totalTaskGroup + 1 --记录tag分类数量+1
	end
	local taskInGroup = taskGroup[tag]
	if taskInGroup.key[taskFunc] == nil then
		taskInGroup.key[taskFunc] = {}
	end
	if canTaskRepeat or taskInGroup.key[taskFunc][taskParam] == nil then--同函数，同参数变量，在相同tag内只能存在一个,如果canTaskRepeat为true，则可存在多个相同任务
		local taskTable = { 
			taskFunc = taskFunc,
			taskParam = taskParam
		 }
		 taskInGroup.key[taskFunc][taskParam] = true
		 table.insert(taskInGroup.data,taskTable) --数据插入到列表，做同组执行排序
		taskExistGroupCount[tag] = taskExistGroupCount[tag] + 1
	else 
		if taskExistGroupCount[tag] > 1 then --队列长度大于1 才需要做这个操作
			for i=1,taskExistGroupCount[tag] do
				if taskInGroup.data[i].taskFunc == taskFunc and taskInGroup.data[i].taskParam == taskParam then
					local task = taskInGroup.data[i]
					table.remove(taskInGroup.data,i)
					table.insert(taskInGroup.data,task) --数据插入到列表，做同组执行排序 
					break
				end 
			end
		end
		
	end 
	--TaskUpdate()
end
local executePerFrame = 5 --每帧固定执行的任务数
local freeTimeDef = 0.033 --上一帧如果是执行时间过长，则本帧不处理任务
--定时器轮询更新
function M:TaskUpdate(evt,deltaTime)   
	if not taskRun then
		return 
	end
	local currentExecute = 0
	--按分组执行任务
	while totalTaskGroup >0 do
		local taskInGroupTag = tableExistInGroupList[1] --拿到对应分组的tag，取优先级最高的一个分组
		local taskInGroup = taskGroup[taskInGroupTag] --通过tag从taskGroup字典中拿到group,group长度必定>=1 
		local groupCount = taskExistGroupCount[taskInGroupTag]
		while groupCount > 0 do 
			local task = taskInGroup.data[1]
			self:TryExecuteTask(task,taskInGroupTag,deltaTime) --每次都取队列第一个
			table.remove(taskInGroup.data,1)
			taskInGroup.key[task.taskFunc][task.taskParam] = nil --清除key
			groupCount = groupCount-1
			taskExistGroupCount[taskInGroupTag] = groupCount 
			currentExecute = currentExecute + 1
			if currentExecute >= executePerFrame or deltaTime > freeTimeDef then
				break
			end
		end 
		if groupCount <= 0 then 
			--组内任务被执行完毕后，标签组会被移除
			table.remove(tableExistInGroupList,1)
			totalTaskGroup = totalTaskGroup -1   
			taskGroup[taskInGroupTag] = nil
		end 
		if currentExecute >= executePerFrame or deltaTime > freeTimeDef then
			break
		end
	end  
end
--执行任务，满足执行条件返回true
function M:TryExecuteTask(task,tag,deltaTime)  
	--lua中update执行的任务列表需要走xpcall，避免导致一个任务报错导致整个流程中断
	local isSucces = false
	xpcall(function()  
		task.taskFunc(task.taskParam)  
	end,function(err)
		log.Error("Func Exec Error:",err) 
	end) 
	return isSucces
end

--清除指定分组的任务
function M:ClearTaskGroup(tag) 
	taskExistGroupCount[tag] = 0
	for i=1,#tableExistInGroupList do
		if tableExistInGroupList[i] == tag then
			 table.remove(tableExistInGroupList,i)
			break
		end
	end
	totalTaskGroup = totalTaskGroup-1
end

--清楚所有未执行的任务
function M:ClearAllTask() 
	tableExistInGroupList = {}
	taskExistGroupCount = {}
	totalTaskGroup = 0
end
--该队列是否已经全部执行完毕
function M:IsAllTaskFinish()
	return totalTaskGroup <= 0
end

--从指定的tag中移除某个task
-- function ClearTaskFromTag(task,tag)
-- 	local taskInGroup = taskGroup[tag]
-- 	taskInGroup.key[task.taskFunc] = nil --将dict的寻址标记清除
-- 	for i = 1, taskExistGroupCount[tag] do
-- 		if taskInGroup.data[i] == task then
-- 			table.remove(taskInGroup.data,i)
-- 			taskExistGroupCount[tag] = taskExistGroupCount[tag] -1
-- 			break
-- 		end
-- 	end
-- 	if taskExistGroupCount[tag] <= 0 then --当某个tag的组数量变成0，需要清理tag的组
-- 		taskExistInGroup[tag] = nil --需要清理taskGroup中的tagkey
-- 		taskGroup[tag] = nil
-- 	end
-- end  
return class(base_object, nil, M)