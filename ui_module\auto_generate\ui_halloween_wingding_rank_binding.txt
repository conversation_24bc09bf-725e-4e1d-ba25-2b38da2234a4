local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local RectTransform = CS.UnityEngine.RectTransform
local Toggle = CS.UnityEngine.UI.Toggle
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Text = CS.UnityEngine.UI.Text


module("ui_halloween_wingding_rank_binding")

UIPath = "ui/prefabs/gw/activity/halloweenactivity/uihalloweenwingdingrank.prefab"

WidgetTable ={
	btn_closeBtn = { path = "listBg/closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	rtf_rankTypeToggle = { path = "listBg/rtf_rankTypeToggle", type = RectTransform, },
	tog_personToggle = { path = "listBg/rtf_rankTypeToggle/tog_personToggle", type = Toggle, value_changed_event = "OnTogPersonToggleValueChange"},
	tog_allianceToggle = { path = "listBg/rtf_rankTypeToggle/tog_allianceToggle", type = Toggle, value_changed_event = "OnTogAllianceToggleValueChange"},
	rtf_firstReward = { path = "listBg/banner/rtf_firstReward", type = RectTransform, },
	rtf_rankOrRewardToggle = { path = "listBg/rtf_rankOrRewardToggle", type = RectTransform, },
	tog_rankToggle = { path = "listBg/rtf_rankOrRewardToggle/tog_rankToggle", type = Toggle, value_changed_event = "OnTogRankToggleValueChange"},
	tog_rewardToggle = { path = "listBg/rtf_rankOrRewardToggle/tog_rewardToggle", type = Toggle, value_changed_event = "OnTogRewardToggleValueChange"},
	rtf_rankPage = { path = "listBg/rtf_rankPage", type = RectTransform, },
	rtf_rankList = { path = "listBg/rtf_rankPage/rtf_rankList", type = RectTransform, },
	srt_rankContent = { path = "listBg/rtf_rankPage/rtf_rankList/Viewport/srt_rankContent", type = ScrollRectTable, },
	scrItem_selfRank = { path = "listBg/rtf_rankPage/scrItem_selfRank", type = ScrollRectItem, },
	rtf_rewardPage = { path = "listBg/rtf_rewardPage", type = RectTransform, },
	rtf_rewardList = { path = "listBg/rtf_rewardPage/rtf_rewardList", type = RectTransform, },
	srt_rewardContent = { path = "listBg/rtf_rewardPage/rtf_rewardList/Viewport/srt_rewardContent", type = ScrollRectTable, },
	scrItem_selfReward = { path = "listBg/rtf_rewardPage/scrItem_selfReward", type = ScrollRectItem, },
	txt_tip = { path = "listBg/rtf_rewardPage/txt_tip", type = Text, },

}
