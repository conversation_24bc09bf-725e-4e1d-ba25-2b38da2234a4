--@region FileHead
-- ui_common_mini_game_result.txt ---------------------------------
-- author:  黄伟华
-- date:    1/7/2025 10:37:29 AM
-- ver:     1.0
-- desc:    Description
-------------------------------------------------
--@endregion 

--@region Require
local print     = print
local require   = require
local typeof    = typeof
local string    = string
local table = table
local math = math
local os = os
local pairs = pairs
local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local Image         = CS.UnityEngine.UI.Image
local RectTransform = CS.UnityEngine.RectTransform
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
local SpriteSwitcher =CS.War.UI.SpriteSwitcher
local UIUtil        = CS.Common_Util.UIUtil

local gw_independent_game_data = require "gw_independent_game_data"
local event_mini_game_define = require "event_mini_game_define"
local gw_independent_game_mgr = require "gw_independent_game_mgr"
local util = require "util"
local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local windowMgr             = require "ui_window_mgr"
local lang = require "lang"
local game_scheme = require "game_scheme"
local gw_independent_game_const = require"gw_independent_game_const"
local cfg_util = require "cfg_util"
local reward_mgr = require "reward_mgr"
local item_data = require "item_data"
local goods_item_new = require "goods_item_new"

--@endregion 
local time_util             = require "time_util"
--@region ModuleDeclare
module("ui_common_mini_game_result")
--local interface = require "iui_pazzle_game_defeat"
local window = nil
local UIPazzleGameDefeat = {}
local parentTrans = windowMgr.canvasMeshT

--@endregion

--@region WidgetTable
UIPazzleGameDefeat.widget_table = {
--@region User
    
	titleImg = {path = "newshibaidi/shibaiPic", type = RectTransform},
    closeBtn = { path = "closeBtn", type = "Button", event_name = "closeOnClick"},
	bgTrans = {path = "newshibaidi", type = RectTransform},
    AutoLoseRoot = {path = "AutoLose", type = RectTransform},
    Auto_Retry = {path = "LayoutRoot/BtnList/Auto_Retry", type = "Button",event_name="OnAutoRetryClick"},
    Auto_Skip = {path = "LayoutRoot/BtnList/Auto_Skip", type = "Button",event_name="OnAutoSkipClick"},

    AutoWinRoot = {path = "AutoWin", type = RectTransform},
    Auto_OK = {path = "LayoutRoot/BtnList/Auto_OK", type = "Button",event_name="OnAutoOkClick"},

    taskTxt = {path = "taskTxt", type = "Text"},
    loseLayoutRoot = {path = "LayoutRoot/loseLayoutRoot", type = RectTransform},
    infoTxt1 = {path = "LayoutRoot/loseLayoutRoot/infoTxt1", type = RectTransform},
    infoNum1 = {path = "LayoutRoot/loseLayoutRoot/infoTxt1/infoNum1", type = "Text"},
    infoNum2 = {path = "LayoutRoot/loseLayoutRoot/infoTxt2/infoNum2", type = "Text"},

    winLayoutRoot = {path = "LayoutRoot/winLayoutRoot", type = RectTransform},
    infoTxt3 = {path = "LayoutRoot/winLayoutRoot/infoTxt3", type = "Text"},
    soldierRoot = {path = "LayoutRoot/winLayoutRoot/soldierRoot", type = RectTransform},
    soldierNum = {path = "LayoutRoot/winLayoutRoot/soldierRoot/soldierNum", type = "Text"},
    tf_rewardContent = {path = "LayoutRoot/winLayoutRoot/rewardRoot/scrollRect/tf_rewardContent", type = RectTransform},
    rewardRoot = {path = "LayoutRoot/winLayoutRoot/rewardRoot", type = RectTransform},
--@endregion 
}
--@endregion 

--@region WindowCtor
function UIPazzleGameDefeat:ctor(selfType)
	self.__base:ctor(selfType)
    self.miniGameLevelId = 0
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function UIPazzleGameDefeat:Init()
    self:SubscribeEvents()

end

--@endregion 

--cfgID MiniGameLevelControl:ID
--isWin 输赢
--otherParam 其他参数{endGameTime:关卡用时,deadEnemyCount:杀敌数，}
function UIPazzleGameDefeat:SetInputParam(cfgID,isWin,otherParam)
    self.otherParam = otherParam
    self.isWin=isWin or false
    self.levelCfg=game_scheme:MiniGameLevelControl_0(cfgID,gw_independent_game_const.UnlockType)
end 


--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIPazzleGameDefeat:OnShow()
    --local ui_main_puzzle = require "ui_main_puzzle"
    --ui_main_puzzle.setInShowResultDefeat(false)
    --event.Trigger(event.CHECK_FULL_SCREEN_SHIELD)
    if not self.levelCfg then
        windowMgr:UnloadModule("ui_common_mini_game_result")
        return
    end
    self:UpdateUIPage()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnHide
--[[界面隐藏时调用]]
function UIPazzleGameDefeat:OnHide()
--@region User
--@endregion 
end --///<<< function


--@endregion 



--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UIPazzleGameDefeat:UpdateUIPage()
    if not self:IsValid() then
        return
    end
    if self.goodsItems then
        for i, v in pairs(self.goodsItems) do
            v:Dispose()
        end
        self.goodsItems = nil
    end
    --@region User
    UIUtil.SetActive(self.AutoLoseRoot,not self.isWin)
    UIUtil.SetActive(self.Auto_Retry,not self.isWin)
    if not self.isWin then
        UIUtil.SetActive(self.Auto_Skip, self.levelCfg and self.levelCfg.jumpLock == 1, false)
    else
        UIUtil.SetActive(self.Auto_Skip, false)
    end
    
    UIUtil.SetActive(self.loseLayoutRoot,not self.isWin)
    UIUtil.SetActive(self.winLayoutRoot,self.isWin)
    UIUtil.SetActive(self.AutoWinRoot,self.isWin)
    if not self.isWin then
        UIUtil.SetActive(self.Auto_OK, self.levelCfg and self.levelCfg.jumpLock ~= 1, true)
    else
        UIUtil.SetActive(self.Auto_OK, true)
    end
    local otherParam = self.otherParam

    self.infoNum1.text = otherParam.deadEnemyCount or 0    --杀敌数
    UIUtil.SetActive(self.infoTxt1, otherParam.deadEnemyCount ~= nil)

    self.infoNum2.text = otherParam.endGameTime and time_util.FormatTimeXMan(otherParam.endGameTime) or "00:00"--用时
    local hookLevelCfg = self.otherParam.hookLevelCfg
    --[[if self.otherParam.isHook and hookLevelCfg then
        self.taskTxt.text = string.format2(lang.Get(673041), hookLevelCfg.checkPointID)
    else
        
    end]]
    local str = string.format2(lang.Get( 673014), self.levelCfg.LevelID)
    self.taskTxt.text = string.format2("{%s1} {%s2}", lang.Get(self.levelCfg.szName), str)
    
    local value = self:randomFloatTwoDecimals(self.levelCfg.score.data[0],self.levelCfg.score.data[1])/100
    local valueText = string.format("<size=46><color=#FFDD22>%s</color></size>",math.floor(value * 100 + 0.5) / 100)
    self.infoTxt3.text = string.format2(lang.Get(607402),valueText)
    
    local iRewardId = nil
    if self.otherParam.isHook and hookLevelCfg then
        local hangUpCfg = game_scheme:HangUp_0(hookLevelCfg.money)
        if hangUpCfg then
            iRewardId = cfg_util.ArrayToLuaArray(hangUpCfg.passReward)
        end
    else
        iRewardId = cfg_util.ArrayToLuaArray(self.levelCfg.iRewardId)
    end
    local rewardList = reward_mgr.GetRewardGoodsList2(iRewardId)
    local _soldierNum = 0
    local itemRewardList = {}
    for k,v in pairs(rewardList) do
        if v.nType == item_data.Reward_Type_Enum.maxLvSolider or v.nType == item_data.Reward_Type_Enum.solider then
            _soldierNum = v.num + _soldierNum
        else
            table.insert(itemRewardList,v)
        end
    end
    local isSoliderShow = false
    if self.isWin and (self.otherParam.isPass and self.otherParam.isPass == 0) and _soldierNum > 0 then
        isSoliderShow = true
    end
    UIUtil.SetActive(self.soldierRoot,isSoliderShow)--isPass == 1通关
    self.soldierNum.text = "x".._soldierNum
    
    UIUtil.SetActive(self.rewardRoot,#itemRewardList > 0)
    if #itemRewardList > 0 then
        self.goodsItems = {}
        for k,v in pairs(itemRewardList) do
            self.goodsItems[k] = goods_item_new.CGoodsItem():Init(self.tf_rewardContent,nil, 0.7)
            self.goodsItems[k]:SetGoods(nil, v.id, v.num, function()
                local iui_item_detail = require "iui_item_detail"
                iui_item_detail.Show(v.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, v.num, nil, nil, nil)
            end)
        end
    end
    --@region User
--@endregion 
end --///<<< function

function UIPazzleGameDefeat:randomFloatTwoDecimals(min, max)
    math.randomseed(os.time())
    local num = min + (max - min) * math.random()
    -- 保留两位小数
    return num
end
--@endregion 

--@region WindowClose
function UIPazzleGameDefeat:Close()
    self.RetryIng = nil
    if self:IsValid() then
		self:UnsubscribeEvents()
	end
    if self.goodsItems then
        for i, v in pairs(self.goodsItems) do
            v:Dispose()
        end
        self.goodsItems = nil
    end
    
	self.__base:Close()
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIPazzleGameDefeat:SubscribeEvents()

    self.OnAutoRetryClick = function()
        if self.RetryIng then
            return
        end
        if self.levelCfg and gw_independent_game_const.SoldiersSortieTypeFight[self.levelCfg.LevelType] then
            --如果是士兵突击
            local minigame_mgr = require "minigame_mgr"
            minigame_mgr.EventReport_Retry()
            minigame_mgr.MiniGameClose()
            event.Trigger(event.BATTLE_RESULT_CLOSE)
            event.Trigger(event.MINIGAME_CLOSE, self.isWin)
            if not self.levelCfg then
                self:CloseAllWindow()
                return
            end
            local type = self.levelCfg.LevelType
            local levelID = self.levelCfg.LevelID
            self.RetryIng = true
            util.DelayCall(0.5, function()
                self.RetryIng = false
                windowMgr:UnloadModule("ui_common_mini_game_result")
                gw_independent_game_mgr.OpenMiniGame(type, levelID)
            end)
        else
            windowMgr:UnloadModule("ui_common_mini_game_result")
            gw_independent_game_mgr.MiniGameReset(1)
        end
    end

    self.OnAutoSkipClick = function()
        if self.RetryIng then
            return
        end
        if not self.levelCfg then
            return
        end

        if self.otherParam and self.otherParam.isHook then
            --发送通关逻辑
            gw_independent_game_mgr.NetHookLevelSend()
            return
        end
        if self.levelCfg and gw_independent_game_const.SoldiersSortieTypeFight[self.levelCfg.LevelType] then
            --如果是士兵突击
            local minigame_mgr = require "minigame_mgr"
            minigame_mgr.EventReport_Skip()
        else
            local miniGameName = gw_independent_game_mgr.GetGameResKeyByType(self.levelCfg.LevelType)
            local data = {
                type = self.levelCfg.LevelType or 0,
                LevelID = self.levelCfg.LevelID or 0,
                mini_game_name = miniGameName or "",
            }
            event.Trigger(event.GAME_EVENT_REPORT, "MiniGame_Skip", data)
        end
        local skipMaxId = gw_independent_game_data.GetGameTypeSkipStage(self.levelCfg.LevelType)
        if skipMaxId < self.levelCfg.LevelID then
            local net_independent_game = require "net_independent_game"
            net_independent_game.TMSG_XYX_SET_FAILSTAGE_REQ(self.levelCfg.LevelType, self.levelCfg.LevelID)
        else
            event.Trigger(event_mini_game_define.SET_FAILSTAGE)
            event.Trigger(event.CLICK_PASS_LEVEL)    
        end
        local hookLevelCfg = self.otherParam.hookLevelCfg
        if hookLevelCfg then
            --主线打点
            local reportMsg = {
                Level_id = hookLevelCfg.checkNumber, --关卡ID
                Hook_type = 2, --关卡类型
            }
            event.Trigger(event.GAME_EVENT_REPORT, "Hook_success", reportMsg)    
        end
        self:CloseBtnClick()
    end

    self.OnAutoOkClick = function()
        self:CloseBtnClick()
    end
    self.closeOnClick = function()
        if self.otherParam.isHook then
            return
        end
        self:CloseBtnClick()
    end
    --event.Register(event_mini_game_define.SET_FAILSTAGE,self.OnAutoOkClick)
end --///<<< function

function UIPazzleGameDefeat:CloseBtnClick()
    if self.RetryIng then
        return
    end
    local mainSlgMgr = require "main_slg_mgr"
    mainSlgMgr.EnterBattleState(false)
    self:CloseAllWindow()
    if self.levelCfg and gw_independent_game_const.SoldiersSortieTypeFight[self.levelCfg.LevelType] then
        --如果是士兵突击
        local minigame_mgr = require "minigame_mgr"
        minigame_mgr.MiniGameClose()
        event.Trigger(event.BATTLE_RESULT_CLOSE)
        event.Trigger(event.MINIGAME_CLOSE, self.isWin)
    else
        event.Trigger(event.NEW_XYX_END_LEVEL, self.levelCfg.ID)
    end
    local type = self.levelCfg and self.levelCfg.LevelType or nil
    gw_independent_game_mgr.MiniGameClose(type,self.isWin)    
end

function UIPazzleGameDefeat:CloseAllWindow()
    windowMgr:UnloadModule("ui_common_mini_game_result")
    if windowMgr:IsModuleShown("ui_casual_game_common") then
        windowMgr:UnloadModule("ui_casual_game_common")
    end
    if windowMgr:IsModuleShown("ui_mini_select_hero") then
        windowMgr:UnloadModule("ui_mini_select_hero")
    end
    if windowMgr:IsModuleShown("ui_mini_game_info") then
        windowMgr:UnloadModule("ui_mini_game_info")
    end
    if windowMgr:IsModuleShown("ui_main_puzzle") then
        windowMgr:UnloadModule("ui_main_puzzle")
    end
    if windowMgr:IsModuleShown("ui_main_tower") then
        windowMgr:UnloadModule("ui_main_tower")
    end
end
--@endregion 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIPazzleGameDefeat:UnsubscribeEvents()
    event.Unregister(event_mini_game_define.SET_FAILSTAGE,self.OnAutoOkClick)
end --///<<< function


--@region WindowInherited
local CUIPazzleGameDefeat = class(ui_base, nil, UIPazzleGameDefeat)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIPazzleGameDefeat()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/gw_commonminigame/uicommonminigameresult.prefab", nil, nil, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end


