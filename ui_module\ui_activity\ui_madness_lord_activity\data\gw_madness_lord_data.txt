--- Created by 农勇智.
--- DateTime: 2025/6/26 10:35
--- Des:狂暴领主类

local pairs = pairs
local ipairs = ipairs
local table = table
local require = require
local data_mgr = require "data_mgr"
local event = require "event"
local game_scheme = require "game_scheme"
local os = os
local util = require "util"
local GWConst = require "gw_const"
local gw_ed = require("gw_ed")
local player_mgr = require "player_mgr"
local alliance_data = require "alliance_data"
local tonumber = tonumber
local string = string
local event_violentZombieLeader_define = require("event_violentZombieLeader_define")

---@class GWMadnessLordData
module("gw_madness_lord_data")
local M = {}
local self = M --简化写法，静态类中直接也用Self获取自身
---所有数据存储
local _d = data_mgr:CreateData("gw_madness_lord_data")
---非服务器数据存储
local mc = _d.mde.const

local isInit = false --初始化标记

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@see 初始化
function M.Init()
    if isInit then
        return
    end
    isInit = true
    mc.timeTicker = nil;
    mc.activityData = {};
    mc.playerRank = {};
    mc.allianceRank = {};
    mc.bossRank = {};
end

function M.GetActivityData()
    return mc.activityData
end

function M.SetActivityData(msg)
    mc.activityData = {};
    for i,v in ipairs(msg.data) do
        local temp = {}
        temp.sid = v.sid  --实体的id
        temp.cfgId = v.configId --实体的配置id
        temp.createTime = v.createTime --实体的创建时间
        temp.aliveTime = v.aliveTime --实体的存活时间
        temp.maxHp = v.configHeath --实体的配置血量，就是最大血量
        temp.hp = v.curHeath --实体的当前血量
        temp.weekStatus = v.weekStatus --实体的虚弱状态，0正常1虚弱
        temp.bossLevel = v.bossLevel --等级
        temp.pos = {
            x = v.tPosX,
            y = v.tPosY
        }
        table.insert(mc.activityData,temp)
    end
    event.Trigger(event_violentZombieLeader_define.MSG_VIOLENT_ZOMBIE_DATA_UPDATE)
end

function M.GetPlayerRank()
    return mc.playerRank
end

function M.SetPlayerRank(msg)
    mc.playerRank.selfRank = {}
    mc.playerRank.data = {}
    for i,v in ipairs(msg.rankInfo) do
        local temp = {}
        temp.rank = v.rank;
        temp.dbid = v.dbid;
        temp.score = v.score;
        temp.roleLv = v.roleLv;
        --适配faceID 2025.4.2 将faceID转换为faceStr
        local faceStr = v.faceID
        if v.faceStr and not string.IsNullOrEmpty(v.faceStr) then
            faceStr = v.faceStr
        end
        temp.faceID = faceStr;
        temp.frameID = v.frameID;
        temp.sex = v.sex;
        temp.playerName = v.name;
        temp.leagueName = v.leagueName;
        temp.leagueShortName = v.leagueShortName
        temp.worldId = v.worldId;
        table.insert(mc.playerRank.data,temp)
        if temp.dbid == player_mgr.GetPlayerRoleID() then
            mc.playerRank.selfRank = temp
        end
    end
    table.sort(mc.playerRank.data,function(a, b)
        return a.rank < b.rank
    end)
end

function M.GetAllianceRank()
    return mc.allianceRank
end

function M.SetAllianceRank(msg)
    mc.allianceRank.selfRank = {}
    mc.allianceRank.data = {}
    for i,v in ipairs(msg.rankInfo) do
        local temp = {}
        temp.rank = v.rank;
        temp.score = v.score;
        
        temp.leagueName = v.leagueName;
        temp.leagueShortName = v.leagueShortName
        temp.worldId = v.worldId;
        temp.leagueid = v.leagueid
        temp.leagueFlag = v.leagueFlag
        table.insert(mc.allianceRank.data,temp)
        local userAllianceData = alliance_data.GetUserAllianceData()
        if not userAllianceData or userAllianceData.allianceId == nil or userAllianceData.allianceId == 0  then
            
        else
            if temp.leagueid == userAllianceData.allianceId then
                mc.allianceRank.selfRank = temp
            end
        end
    end
    table.sort(mc.allianceRank.data,function(a, b)
        return a.rank < b.rank
    end)
end

function M.GetAllBossRank()
    return mc.bossRank
end

function M.GetBossRank(bossType)
    return mc.bossRank[bossType] or {}
end

function M.SetPlayerRank(msg)
    if msg.paramInt and msg.paramInt[1] then
        local key = tonumber(msg.paramInt[1])
        if not mc.bossRank[key] then
            mc.bossRank[key] = {}
        end
        mc.bossRank[key].selfRank = {}
        mc.bossRank[key].data = {}
        for i,v in ipairs(msg.rankInfo) do
            local temp = {}
            temp.rank = v.rank;
            temp.dbid = v.dbid;
            temp.score = v.score;
            if v.score > maxScore then
                maxScore = v.score
            end
            temp.roleLv = v.roleLv;
            --适配faceID 2025.4.2 将faceID转换为faceStr
            local faceStr = v.faceID
            if v.faceStr and not string.IsNullOrEmpty(v.faceStr) then
                faceStr = v.faceStr
            end
            temp.faceID = faceStr;
            temp.frameID = v.frameID;
            temp.sex = v.sex;
            temp.playerName = v.name;
            temp.leagueName = v.leagueName;
            temp.leagueShortName = v.leagueShortName
            temp.worldId = v.worldId;
            table.insert(mc.bossRank[key].data,temp)
            if temp.dbid == player_mgr.GetPlayerRoleID() then
                mc.bossRank[key].selfRank = temp
            end
        end
        table.sort(mc.bossRank[key].data,function(a, b)
            return a.rank < b.rank
        end)
    end

end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
--- 数据清理
function M.Dispose()
    isInit = false
    
    if mc.timeTicker then --销毁广播计时器
        util.RemoveDelayCall(mc.timeTicker)
        mc.timeTicker = nil;
    end
    mc.chatData = {}
    mc.activityData = {}
end
return M