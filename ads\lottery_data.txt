local require = require
local ipairs = ipairs
local pairs = pairs
local os = os
local coroutine = coroutine
local string = string
local print     = print
local tostring = tostring

local net = require "net"
local lotterywish_pb = require "lotterywish_pb"
local msg_pb = require "msg_pb"
local net_route = require "net_route"
local flow_text = require "flow_text"
local lang = require "lang"
local event = require "event"
local game_scheme = require "game_scheme"
local util = require "util"
local log = require "log"
local base_object = require "base_object"
local version_mgr = require "version_mgr"
local const = require "const"
local game_config = require "game_config"

local Application   = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform

module("lottery_data")

--message TLotteryWishData
--{
--	required int32 iType = 1;  //许愿池类型
--	required int32 iRefreshTimeStamp = 2; //下一次刷新时间戳(秒)
--	required int32 iLevelCreate = 3; //奖池生成时的等级
--	repeated TLotteryWishRwItem arrRwItems = 4; //奖励品信息
--	required bool bFreeRefresh = 5; //是否可以免费刷新
--	required int32 iDailyCount = 6;	// 当天抽奖次数
--	required int32 iLastLotteryTime = 7;	// 上次抽奖时间
--}

-- iType 抽奖类型，有3种，主城模块，冒险模块、联盟模块
timeout = 2 --广告加载超时
local maxTimes = game_scheme:InitBattleProp_0(616).szParam.data[0] --每个转盘每天最高可以转3次
local cd = game_scheme:InitBattleProp_0(615).szParam.data[0] * 60
local openCfgs = {617, 618, 619}
local openServerCfgs = {642, 643, 644}
local lotteryData = {}
local timeTickers = {}
local redDots = {}

--广告中心不显示tips
local isLotteryNotShowTips = false
--双倍签到不显示tips
local isSignNotShowTips = false

function UpdateLotteryWishData(iType, data)
	lotteryData[iType] = data
	event.Trigger(event.EVENT_UDAPTE_LOTTERY_DATA, iType)
	StartCountdown(iType)
	-- log.Warning("UpdateLotteryWishData:", iType, data.iRefreshTimeStamp, data.iLevelCreate, data.bFreeRefresh, data.iDailyCount, data.iLastLotteryTime)	
end

function GetLotteryData(iType)
	return lotteryData[iType] or {}
end

-- 获取倒计时 每天3次，每次cd为10min
function GetLotteryCountdown(iType)
	local data = lotteryData[iType]
	if not data then
		return
	end
	local nTime = nil
	if data.iDailyCount >= maxTimes then
		--local now_date = os.date("*t")
		--nTime = os.time({year=now_date.year, month=now_date.month, day=now_date.day, hour=24,min=0,sec=0}) --+ util.GetTimeArea()*3600
		nTime = data.iRefreshTimeStamp
	else
		local nextTime = data.iLastLotteryTime + cd
		if nextTime > os.server_time() then
			nTime = nextTime
		end
	end
	return nTime
end

-- 倒计时显示红点
function StartCountdown(iType)
	ShowRedDot(iType, true)
	local remainTime = GetLotteryCountdown(iType)
	if not remainTime then
		return
	end
	if timeTickers[iType] then
		timeTickers[iType]:Dispose()
		timeTickers[iType] = nil
	end
	timeTickers[iType] = base_object()
	remainTime = remainTime - os.server_time()
	timeTickers[iType]:CreateTimeTicker(0, function()
		ShowRedDot(iType, false)
		local last = os.server_time()
		while true do
			local now = os.server_time()
			local pass = now - last
			local t = remainTime - pass	
			if t <= 0 then
				-- 显示红点
				ShowRedDot(iType, true)
				if timeTickers[iType] then
					timeTickers[iType]:Dispose()
					timeTickers[iType] = nil
				end
				break
			end
			coroutine.yield(1)
		end
	end)
end

function ShowRedDot(iType, isShow)
	-- local new_scene_mgr = require "new_scene_mgr"
	-- if iType == lotterywish_pb.emLotteryWishType_Advertisement2 then
	-- 	event.Trigger(event.SCENE_GAMEITEMREDDOT, new_scene_mgr.SceneItemType.Ads2, isShow)
	-- end
	if iType == lotterywish_pb.emLotteryWishType_Advertisement3 then
		event.Trigger(event.UPDATE_SOCIATY_REDTIP, 9, isShow)
	end
	redDots[iType] = isShow
	event.Trigger(event.EVENT_UPDATE_LOTTERY_REDDOT, iType, isShow)
end

function GetRedDot(iType)
	return redDots[iType]
end

-- 判断广告牌位是否开启
function CheckModuleIsOpen(iType)
	local ad_config = require "ad_config"
	if not ad_config.IsEnableAd() then
		return false
	end
	
	-- 关卡和等级限制
	local ui_pop_mgr = require "ui_pop_mgr"
	return ui_pop_mgr.CheckIsOpen(openCfgs[iType-2], false)
end

--[[
1、华为包         看不到任务、建筑和签到         都不可见
2、老包（没开广告的）  都可见    建筑、任务、签到提示下载最新包
3、正常包（开广告）   都可见    建筑、任务、签到都正常看广告

广告相关是否隐藏
iType 是1-4 1、主城 2、冒险 3、联盟 4、签到
]]--

function CheckAdsActive(iType)
	if util.GetChannelTag() == const.package_name_set.com_q1_hero_huawei then
        return false
	end
	-- 区服限制
	if iType and openServerCfgs[iType] and game_config.ENABLE_Q1_DEBUG_MODE  == false then
		local server_data = require "setting_server_data"
		local worldid = server_data.GetLoginWorldID()
		local cfg = game_scheme:InitBattleProp_0(openServerCfgs[iType]).szParam.data
		if cfg then
			if worldid < cfg[0] or worldid > cfg[1] then
				return false
			end
		end
	end

	-- 如果服务器刷关
	local module_on_off = require "module_on_off"
	local lua_pb = require "lua_pb"
	if not module_on_off.GetModuleOnOff(lua_pb.EModule_LotteryAd) then
		return false
	end
	return true
end

-- 检测是否需要提示更新广告版本
function CheckUpdateAdsVersion()
	if not CheckAdsActive() then
		log.Error("CheckUpdateAdsVersion error")
		return true
	end

	if (game_config.ENABLE_ADMOB and not version_mgr.CheckSvnTrunkVersion(42927))
	or (game_config.ENABLE_HUAWEI and not version_mgr.CheckSvnTrunkVersion(53338)) then
		local message_box = require "message_box"
		local url_mgr = require "url_mgr"
		message_box.Open(lang.Get(184008), message_box.STYLE_YES, function()
			local url = Application.platform == RuntimePlatform.IPhonePlayer and url_mgr.APPLE_STORE_URL or url_mgr.GOOGLE_URL
			local q1sdk = require "q1sdk"
			q1sdk.ApplicationOpenURL(url)
		end, 0, lang.KEY_OK)
		return true
	end

	return false
end

-- 检查是否还有观看广告的机会
function CheckCanWatchAd(index)
	if index < 4 and not CheckModuleIsOpen(index + 2) then
		return false
	end

	if index == 4 then
		local ui_pop_mgr = require "ui_pop_mgr"
		if not ui_pop_mgr.CheckIsOpen(237, false) then
			return false
		end
		local signIn_data = require "signIn_data"
		if signIn_data.GetIsOpenDR() ~= 0 then
			return false
		end
	end
	return true
end

function C2SLotteryWishREQ(wishType)
	local msg = lotterywish_pb.TMSG_LOTTERYWISH_REQ()
    msg.iType = wishType
    msg.iPlayType = 1
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_LOTTERYWISH_REQ, msg)
end

function OnMSG_LOTTERYWISH_RSP(msg)
	if msg.iType ~= lotterywish_pb.emLotteryWishType_Advertisement2 and
	msg.iType ~= lotterywish_pb.emLotteryWishType_Advertisement3 
	then
		return
	end
	if msg.enErrCode == 0 then
		local rewardList = msg.arrRewardInfo
		if msg.stData then
			UpdateLotteryWishData(msg.stData.iType, msg.stData)
		end
		-- 播放完动画之后显示奖励弹框
		local index = 0
		local level = lotteryData[msg.iType].iLevelCreate
		local cfg = nil
		for i=1,game_scheme:LotteryWish_nums() do
			local scheme = game_scheme:LotteryWish_0(i)
			if msg.iType == scheme.iType and level >= scheme.iLevel then
				cfg = scheme
			end
		end
		if cfg then
			for i=0, cfg.arrBoxID.count-1 do
				if cfg.arrBoxID.data[i] == msg.iFirstRwBoxID then
					index = i+1
					break
				end
			end
		end
		event.Trigger(event.EVENT_ONMSG_LOTTERYWISH_RSP, index, rewardList)
		local properties = string.format("{\"iType\":%d,\"rewardid\":%d}", msg.iType - 2, rewardList[1].rewardID)
		event.Trigger(event.GAME_EVENT_REPORT, "ads_lottery_success", properties)
	else
		flow_text.Add(lang.Get(msg.enErrCode + 100000))
	end
end

function OnMSG_LOTTERYWISH_SYNC_NTF(msg)
	for i, data in ipairs(msg.stLotteryWishPartData.arrLotteryWishData) do
		UpdateLotteryWishData(data.iType, data)
	end
end

---ShowLotteryJumpAdsTip 广告中心跳过广告提示框
---@param lotteryCallBack 抽奖回调
---@param playAdsCallBack 播放广告回调
function ShowLotteryJumpAdsTip(lotteryCallBack, playAdsCallBack)
	if IsPlayerRechargeMoney() then
		--已付费用户，直接抽奖
		if lotteryCallBack then
			lotteryCallBack()
		end
		return
	end
	if isLotteryNotShowTips then
		--不显示提示框，直接播放广告
		if playAdsCallBack then
			playAdsCallBack()
		end
	else
		ShowJumpAdsTip(true, playAdsCallBack)
	end
end

---ShowLoginGiftJumpAdsTip 登录好礼跳过广告提示框
---@param receiveRewardCallBack 双倍领奖回调
---@param playAdsCallBack     播放广告回调	
function ShowLoginGiftJumpAdsTip(receiveRewardCallBack, playAdsCallBack)
	if IsPlayerRechargeMoney() then
		--已付费用户，直接领取奖励
		if receiveRewardCallBack then
			receiveRewardCallBack()
		end
		return
	end
	if isSignNotShowTips then
		--不显示提示框，直接播放广告
		if playAdsCallBack then
			playAdsCallBack()
		end
	else
		ShowJumpAdsTip(false, playAdsCallBack)
	end
end

---ShowJumpAdsTip 跳过广告提示框
---@param isLottery   是否广告中心 否：双倍签到
---@param contentLangId 内容id
function ShowJumpAdsTip(isLottery, playAdsCallBack)
	local message_box = require "message_box"
	local contentLangId = isLottery and 184012 or 184011

	--确认取消回调
	local clickCallBack = function(callbackData, nRet)
		if message_box.RESULT_YES == nRet then
			--前往付费
			local ui_window_mgr = require("ui_window_mgr")
			ui_window_mgr:ShowModule("ui_frist_rechage")
		elseif playAdsCallBack then
			--观看广告
			playAdsCallBack()
		end
	end

	--勾选框状态
	local toggleChangeCallBack = function(isOn)
		if isLottery then
			--广告中心不显示tips
			isLotteryNotShowTips = isOn
		else
			--双倍签到不显示tips
			isSignNotShowTips = isOn
		end
	end
	print("展示跳过广告提示", lang.Get(7284))
	message_box.Open(lang.Get(contentLangId), message_box.STYLE_YESNO, clickCallBack, 
	0, lang.Get(184015), lang.Get(184014), lang.Get(7284), nil, true, nil, false,nil,nil,
	 nil, nil, lang.Get(184013), toggleChangeCallBack)
end

--玩家是否有充值
function IsPlayerRechargeMoney()
	-- local activity_mgr = require "activity_mgr"
	-- local rechargeMoneyData = activity_mgr.GetTotalRechargeMoneyData()
	local net_vip_module = require "net_vip_module"
	local rechargeMoney = net_vip_module.GetRechargeNum()
	print("玩家充值信息", tostring(rechargeMoney))
    if not rechargeMoney or rechargeMoney <= 0 then
		return false
	end
    
	return true
	-- if (rechargeMoneyData[2] and rechargeMoneyData[2] ~= 0X7FFFFFFF) or 
	-- 	(rechargeMoneyData[3] and rechargeMoneyData[3] ~= 0X7FFFFFFF) then
	-- 	--已充值
	-- 	return 	true	
	-- end
	-- return false
end



MessageTable =
{
    {msg_pb.MSG_LOTTERYWISH_SYNC_NTF, OnMSG_LOTTERYWISH_SYNC_NTF, lotterywish_pb.TMSG_LOTTERYWISH_SYNC_NTF},
    {msg_pb.MSG_LOTTERYWISH_RSP, OnMSG_LOTTERYWISH_RSP, lotterywish_pb.TMSG_LOTTERYWISH_RSP},
}
net_route.RegisterMsgHandlers(MessageTable)

function ClearData()
	lotteryData = {}
	for i, v in pairs(timeTickers) do
		if v then
			v:Dispose()
			v = nil
		end
	end
	timeTickers = {}
	redDots = {}

	isLotteryNotShowTips = false
	isSignNotShowTips = false
end
event.Register(event.SCENE_DESTROY, ClearData)