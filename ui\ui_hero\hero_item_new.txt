-- heroItem_new_new.txt ------------------------------------------
-- author:  黄丹鸿
-- date:    2024.10.22
-- ver:     1.0
-- desc:    新版英雄头像item，可以通用
--------------------------------------------------------------

local require = require
local type = type
local pairs = pairs
local tostring = tostring
local string = string
local typeof = typeof
local tonumber = tonumber

local Vector3 = CS.UnityEngine.Vector3
local RectTransform = CS.UnityEngine.RectTransform
local GrayImageType = typeof(CS.War.UI.ImageGray)
local GameObject = CS.UnityEngine.GameObject
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local ParticleSystemRenderer = CS.UnityEngine.ParticleSystemRenderer

local RawImage = CS.UnityEngine.UI.RawImage
local log = require "log"
local card_sprite_asset = require "card_sprite_asset"
local game_scheme = require "game_scheme"
local CModelViewer = require "modelviewer"
local util = require "util"
local idle = require "idle"
local render_tool = require "render_tool"
local const = require "const"
local Slider = CS.UnityEngine.UI.Slider
local Text = CS.UnityEngine.UI.Text
local gw_hero_data = require "gw_hero_data"
local gw_hero_mgr = require "gw_hero_mgr"

module("hero_item_new")
local entityResPath = "ui/prefabs/heroitem_new.prefab"
local animEntityResPath = "ui/prefabs/heroanimitem.prefab"
local HeroItem_new = {}

local ADDRES_PATH = {
    --effect
    --effect = "ui/prefabs/XXXeffect.prefab",
    selectMask = "ui/prefabs/heroitem_selectmask.prefab",
    hpItem = "ui/prefabs/heroitem_hpitem.prefab"
}

HeroItem_new.widget_table = {
    button = { path = "", type = "Button" },
    -- baseLayer
    baseLayerIcon = { path = "baseLayer/mask/icon", type = "Image" },
    iconGray = { path = "baseLayer/mask/icon", type = GrayImageType },
    baseLayerBg = { path = "baseLayer/bg", type = SpriteSwitcher },
    bgGray = { path = "baseLayer/bg", type = GrayImageType },
    baseLayerFrame = { path = "baseLayer/frame", type = SpriteSwitcher },
    frameGray = { path = "baseLayer/frame", type = GrayImageType },
    frameRtsf = { path = "baseLayer/frame", type = "RectTransform" },
    -- bottomLayer
    bottomRtsf = { path = "bottomLayer", type = "RectTransform" },
    levelText = { path = "bottomLayer/level", type = "Text" },
    heroStars = { path = "bottomLayer/heroStar", type = RectTransform },
    jobIcon = { path = "bottomLayer/job", type = SpriteSwitcher },
    --topLayer
    typeIcon = { path = "topLayer/type", type = SpriteSwitcher },
    typeIconGray = { path = "topLayer/type", type = GrayImageType },

    --mountPoint
    selectedMaskTrans = { path = "mountPoint/selectMask", type = RectTransform },
    hpItemTrans = { path = "mountPoint/hpItem", type = RectTransform },
    liuguangEffect = { path = "mountPoint/effect", type = RectTransform },

}
longHPSliderPos = { x = 0, y = -94 }

function HeroItem_new:ctor(selfType, ownerModuleName)
    self.__base:ctor(selfType, "base_item")
    self.callback = nil
    spriteAsset = spriteAsset or card_sprite_asset.CreateHeroAsset()
    typeIconAsset = typeIconAsset or card_sprite_asset.CreateSpriteAsset()

    self.ownerModuleName = ownerModuleName

    --用于状态标记falg
    self.selectedFlag = util.DirtyValue(false)
    self.grayFlag = util.DirtyValue(false, true)
    self.interactableFlag = util.DirtyValue(true)
    self.typeFlag = util.DirtyValue(true, true)

    self.maxLvFlag2 = util.DirtyValue(false)
    self.showLvFlag = util.DirtyValue(false, true)
    self.isSoulLinkingFlag = util.DirtyValue(false, true)
    self.scale = 1
    self.maskSelectState = false
    self.hpItemState = false
    self.hpValue = -1
    self.mpValue = -1

    --effect
    -- 考虑使用 displayFx 配置，替换掉 liuguangEffect 效果的加载，出于稳定性考虑，先保留 liuguangEffect 加载逻辑
    self.displayFx = {
        select = {
            valid = false,
            effectImageName = "selectEffect",
        },
    }
    self.showEffect = false  -- 是否显示五星流光特效
    self.effectOrder = 0     -- 五星流光层级
    self.maskType = 0        -- 五星流光特效的masking类型
    self.useIncrementOrder = false
    self.assetLoaders = {}
    -- 默认展示
    self.bottomState = true
end
---isSetTip 是否开启标签
function HeroItem_new:Init(parentTrans, callback, scale, itemType, initCompleteCallback, updateState, isSetTip)
    self.scale = scale or 1
    local path = nil
    if itemType then
        path = animEntityResPath
    else
        path = entityResPath
    end
    self.itemType = itemType
    self.isSetTip = isSetTip
    self:LoadResource(
            path,
            "",
            function()
                self:RegistEvents()
                self.transform.sizeDelta = { x = 140, y = 140 }
                if callback then
                    callback(self)
                end
                if scale then
                    self.transform.localScale = { x = scale, y = scale, z = scale }
                else
                    self.transform.localScale = Vector3.one
                end

                self.transform.anchoredPosition3D = { x = 0, y = 0, z = 0 }
                self.transform.anchorMin = { x = 0.5, y = 0.5 }
                self.transform.anchorMax = { x = 0.5, y = 0.5 }
                --if updateState == nil or updateState then
                --self:SetState()
                --end

                local lang_util = require "lang_util"
                lang_util.SetFont(self.transform.gameObject)

                self.loaded = true
                if self.DisplayInfoCallBack then
                    self.DisplayInfoCallBack(self)
                    self.DisplayInfoCallBack = nil
                end
                --所有初始化处理完成后调用，请不要在下面加初始化逻辑
                if initCompleteCallback then
                    initCompleteCallback(self)
                end
                -- 设置显示状态
                self:SetBottomState(self.bottomState)
            end,
            idle.ITEM_IO,
            parentTrans.transform
    )

    return self
end

function HeroItem_new:IsSelected()
    return self.selectedFlag.value
end

--兼容老的实体处理
function HeroItem_new:InitHeroInfo(hero)
    self.hero = hero
end

--初始化英雄信息
function HeroItem_new:InitHeroEntityInfo(heroEntity)
    self.heroEntity = heroEntity

    -- 需要初始化 self.hero 后才能获得资源路径
    -- 更换英雄时关闭所有加载特效
    local k, v
    for k, v in pairs(self.displayFx) do
        self:SetEffectValid(k, false)
    end
end

--英雄不可按
function HeroItem_new:Interactable(flag)
    self.interactableFlag.value = flag
    self:SetState()
end

--蒙黑英雄
function HeroItem_new:MaskHero(flag, interactable)
    self:Interactable(interactable and interactable or not flag)
    self:SetState()
end

--选择英雄
function HeroItem_new:SelectHero(flag)
    self.selectedFlag.value = flag
    self:SetState()
end

function HeroItem_new:DisplayImage(transform, renderTxInfo, enabled)
    local rawimage = transform:GetComponent(typeof(RawImage))
    if rawimage == nil then
        log.Warning("HeroItem_new 使用粒子系统转RT时需要绑定 RawImage组件")
        return false
    end
    -- log.Error("DisplayImage, instance id:",rawimage.gameObject:GetInstanceID())
    return renderTxInfo:SetImage(transform, rawimage, enabled)
end

--英雄头像灰色
function HeroItem_new:GrayHeroIcon(flag)
    self.grayFlag.value = flag
    self:SetState()
end

--显示类型
function HeroItem_new:OnShowType(flag)
    self.typeFlag.value = flag
    --self:SetState()
end

function HeroItem_new:SetState()
    if self.UIRoot ~= nil then
        if self.selectedFlag.dirty then
            self.selectedFlag.dirty = false
            self:SetSelectMaskEnable(self.selectedFlag.value)
        end
        if self.isHide then
            self.gameObject:SetActive(false)
        end

        if self.interactableFlag.dirty then
            self.interactableFlag.dirty = false
            self.button.interactable = self.interactableFlag.value
        end
        if self.grayFlag.dirty then
            self.grayFlag.dirty = false
            local flag = self.grayFlag.value
            self.typeIconGray:SetEnable(flag)
            self.frameGray:SetEnable(flag)
            self.bgGray:SetEnable(flag)
            self.iconGray:SetEnable(flag)
        end
        --设置英雄等级 
        if self.heroData and self.heroData.numProp and self.heroData.numProp.lv then 
            self.showLvFlag.value = self.heroData.numProp.lv
        else
            self.showLvFlag.value = self.heroEntity:GetLevel()
        end
        if self.showLvFlag.dirty then
            self.showLvFlag.dirty = false
            self:ShowHeroLv(self.showLvFlag.value)
        end
        --设置英雄星级
        local star = 0
        if self.heroData and self.heroData.numProp and self.heroData.numProp.starLv then 
            star = self.heroData.numProp.starLv
        else
            star = self.heroEntity:GetStar()
        end
        self:SetHeroStar(star)
        --设置英雄职业
        local job = nil
        if self.heroData and self.heroData.numProp and self.heroData.numProp.heroID then 
            job = self.heroData.numProp.heroID
        else
            job = self.heroEntity:GetHeroID()
        end
        self:SetHeroJob(job)

        --RefreshAddItem
        self:RefreshHP()
        self:RefreshMask()
    end
end

function HeroItem_new:SetHeroJob(heroCfgID)
    local cfg = game_scheme:Hero_0(heroCfgID)
    if cfg then
        self.jobIcon:Switch(cfg.profession - 1)
    end
end

function HeroItem_new:SetHeroStar(star)
    -- local star = star or self.heroEntity:GetStar()
    if not star then 
        log.Error("heroItemNew data error please check (star)")
        return
    end
    local ui_hero_star_item = require "ui_hero_star_item"
    self.heroStarObj = self.heroStarObj or ui_hero_star_item.new()
    self.heroStarObj:Init(self.heroStars, star)
end

function HeroItem_new:RegistEvents()
    self.onClickHero = function(...)
        -- body
        if self.trialLimitFunc then
            self.trialLimitFunc()
        else
            if self.callback then
                self.callback(self)
            end
        end
    end
    self.button.onClick:AddListener(self.onClickHero)

    --event.Register(event.SCREEN_SIZE_CHANGED, self.OnScreenSizeChanged)
end

--通过GetRewardItemData得到，刷新的时候用
--goodsID 实际上是heroId
function HeroItem_new:SetGoods(goodsEntity, goodsID, goodsNum, clickCallback, data, desStr)
    if clickCallback and type(clickCallback) == "boolean" then
        clickCallback = function()
            self:DefultClick()
        end
    end
    self:SetHero({ heroID = goodsID }, clickCallback)
end

function HeroItem_new:DefultClick()
    if self.heroEntity then
        local ui_show_hero_card = require "ui_show_hero_card"
        ui_show_hero_card.ShowWithData(ui_show_hero_card.ParseArrHeroId(self.heroEntity:GetHeroID()), nil, nil, nil, true)
    end
end

--设置数据
function HeroItem_new:SetHero(hero, callback)
    --[[
        heor参数使用方法：
        1、传入heroEntity实体
        2、支持以下结构表：
        {
            heroID = ?,
            skinID = ?,
            numProp = {
                starLv = ?,
                lv = ?,
                Career = ?
                Type = ?
            }
        }
    ]]
    -- self.callback = callback
    if hero ~= nil then
        self:InitHeroInfo(hero)
    end

    -- if self.gameObject ~= nil then
    --     self:DisplayInfo()
    -- end
    local heroEntity = gw_hero_data.GetHeroEntity(hero.heroSid)
    -- 存储一份传入数据 方便后续调用
    if hero then 
        self.heroData = hero
    end
    if heroEntity then
        --self:SetHeroEntity(heroEntity,callback)
    else
        local gw_hero_entity = require "gw_hero_entity"
        heroEntity = gw_hero_entity.new()
        local numProp = hero.numProp or {}
        local serverData = {
            heroLevel = numProp.lv or 1,
            heroStarLv = numProp.starLv or 0
        }
        heroEntity:SetFakeData(hero.heroID, serverData)
    end
    self:SetHeroEntity(heroEntity, callback)
end
function HeroItem_new:SetHeroEntity(heroEntity, callback)
    self.callback = callback
    if heroEntity ~= nil then
        self:InitHeroEntityInfo(heroEntity)
    end
    if self.gameObject ~= nil then
        self:DisplayInfo()
        self.DisplayInfoCallBack = nil
    else
        self.DisplayInfoCallBack = self.DisplayInfo
    end
end
function HeroItem_new:SetActive(isVisible)
    if self.gameObject then
        self.gameObject:SetActive(isVisible)
    else
        self.isHide = not isVisible
    end
end
--刷新显示
function HeroItem_new:DisplayInfo()
    if self.heroEntity == nil or util.IsObjNull(self.gameObject) then
        return
    end

    --如果state不是null，则正常加载英雄，否则特殊处理
    local cfg = nil
    local starsLv = 0
    if self.heroData and self.heroData.numProp and self.heroData.numProp.starLv and self.heroData.heroID then 
        cfg = game_scheme:Hero_0(self.heroData.heroID) 
        starsLv = self.heroData.numProp.starLv
    else
        cfg = self.heroEntity:GetHeroCfg()
        if cfg then
            starsLv = starsLv ~= 1 and starsLv or cfg.starLv
        end
    end

    --临时英雄头像
    local heroType = 0
    local heroCareer = 0
    if cfg == nil then
        -- local nullIcon = nullIconArr[starsLv]
        -- typeIconAsset:GetSprite(
        --         nullIcon,
        --         function(sprite)
        --             if self:IsDisposed() then return end
        --             self.baseLayerIcon.sprite = sprite
        --         end
        -- )
        -- self.baseLayerFrame:Switch(hero_mgr.Star2Frame[starsLv])
        -- self.baseLayerBg:Switch(hero_mgr.Star2Frame[starsLv])

        -- heroType = self.hero.numProp ~= nil and self.hero.numProp.type ~= nil and self.hero.numProp.type or 0
        -- heroCareer = self.hero.numProp and self.hero.numProp.heroCareer and self.hero.numProp.heroCareer or 0
    else
        -- 刷新五星精英特效的显示
        self:UnLoadHeroLiuGuangEffect()
        self:UnLoadHeroEffectSet()
        if cfg.isElite ~= 1 then
            self.showEffect = false
        end
        if self.showEffect then
            self:LoadHeroLiuGuangEffect()
            self:LoadHeroEffectSet()
        end

        heroType = cfg.type ~= nil and cfg.type or 0
        heroCareer = cfg.profession
        local cfgID = 0 
        if self.heroData and self.heroData.heroID then 
            cfgID = self.heroData.heroID 
        else
            cfgID = self.heroEntity:GetHeroID()
        end
        local currentFace = gw_hero_mgr.ChangeHeroIcon(cfgID, starsLv)
        -- if not self.hero.rawIcon then
        --     local skinID = self.hero.skinID
        --     if self.hero.battleProp and self.hero.battleProp.power and ((not skinID) or (skinID == 0)) then
        --         --如果有battleProp字段证明传入的是自己的英雄，外部没传入则自动检测是否穿戴皮肤
        --         local notMaze = true
        --         if self.hero.heroSid then
        --             local player_mgr = require "player_mgr"
        --             if player_mgr.GetMazePalPartDataBySid(self.hero.heroSid) or player_mgr.GetPeakPalPartDataBySid(self.hero.heroSid) then
        --                 notMaze = false
        --             end
        --         end
        --         if notMaze then
        --             local skin_mgr = require "skin_mgr"
        --             skinID = skin_mgr.GetCurSkinID(self.hero.heroID)
        --         end
        --     end
        --     if skinID and skinID ~= 0 then
        --         local faceID = hero_mgr.ChangeHeroSkinIcon(skinID, starsLv)
        --         currentFace = faceID or currentFace
        --     end
        -- end

        local tFun = nil
        self.baseLayerIcon:SetActive(true)
        self.baseLayerFrame:Switch(cfg.rarityType - 2)
        self.baseLayerBg:Switch(cfg.rarityType - 2)
        tFun = function(sprite)
            --长条形头像
            if self:IsDisposed() then
                return
            end
            self.baseLayerIcon.sprite = sprite
        end
        spriteAsset:GetSprite(currentFace, tFun)
        self:SetState()
    end

    -- if self.typeFlag.dirty then
    --     self.typeFlag.dirty = false
    --     self.typeIcon:SetActive(heroType ~= 0 and heroType ~= nil and self.typeFlag.value)
    -- end
    self.typeIcon:Switch((heroType - 1) or 0)
    --设置英雄等级
    if self.heroData and self.heroData.numProp and self.heroData.numProp.lv then 
        self.showLvFlag.value = self.heroData.numProp.lv
    else
        self.showLvFlag.value = self.heroEntity:GetLevel()
    end
    if self.showLvFlag.dirty then
        self.showLvFlag.dirty = false
        self:ShowHeroLv(self.showLvFlag.value)
    end
    --设置英雄星级
    local star = 0
    if self.heroData and self.heroData.numProp and self.heroData.numProp.starLv then 
        star = self.heroData.numProp.starLv
    else
        star = self.heroEntity:GetStar()
    end
    self:SetHeroStar(star)
end

function HeroItem_new:Dispose()
    self:Reset()
    if self.onClickHero and self.button and not util.IsObjNull(self.button) then
        self.button.onClick:RemoveListener(self.onClickHero)
        self.onClickHero = nil
    end

    if self.assetLoaders then
        for i, v in pairs(self.assetLoaders) do
            v:Dispose()
        end
        self.assetLoaders = nil
    end
    self:UnLoadHeroLiuGuangEffect()
    self:ClearDisplayFx()
    --event.Unregister(event.SCREEN_SIZE_CHANGED, self.OnScreenSizeChanged)
    self.__base:Dispose()
end

-----------------------重构------------------------------
-- 重构期望：调用者主动确认自己所需的模块，所有方法都只做其方法所描述的事，即调用者需要主动更新hero_item
--从对象池拿出来的预制体可能有组件未关闭，且新模块中未设置对应的组件，导致某些组件错误的显示
--白名单模式，永远认为HeroItem_new预制体内默认显示的组件会被设置到正确状态，其他组件设置为false
function HeroItem_new:Reset()
    if self.UIRoot ~= nil then
        self.typeIconGray:SetEnable(false)
        self.frameGray:SetEnable(false)
        self.bgGray:SetEnable(false)
        self.iconGray:SetEnable(false)
        self.levelText.color = { r = 1, g = 1, b = 1, a = 1 }
        if self.godEqipItem then
            self.godEqipItem:Dispose()
            self.godEqipItem = nil
        end
        self.godEquipEntity = nil
        self.loadedMask = false;
        self.loadedHpItem = false;
        -- self.heroCfgID = nil
        self.heroData = nil
        self:ReSetAddContent()
    end
end
function HeroItem_new:SetHero_(hero)
    --self.hero = hero
    local heroEntity = gw_hero_data.GetHeroEntity(hero.heroSid)
    if heroEntity then
        self.heroEntity = heroEntity
        --self:SetHeroEntity(heroEntity,callback)
    else
        log.Error("要走新的接口 SetHeroEntity")
    end
end
function HeroItem_new:ShowHeroIcon()
    -- local cfg = self.heroEntity:GetHeroCfg()
    -- local starsLv = self.heroEntity:GetStar()
    local cfg = nil
    local starsLv = 0
    local cfgID = 0
    if self.heroData and self.heroData.heroID then 
        cfgID = self.heroData.heroID
        cfg = game_scheme:Hero_0(self.heroData.heroID)
    else
        cfgID = self.heroEntity:GetHeroID()
        cfg = self.heroEntity:GetHeroCfg()
    end

    if self.heroData and self.heroData.numProp and self.heroData.numProp.starLv then 
        starsLv = self.heroData.numProp.starLv
    else
        starsLv = self.heroEntity:GetStar()
    end

    
    -- if cfg then
    --     starsLv = starsLv ~= 1 and starsLv or cfg.starLv
    -- end
    heroType = cfg.type ~= nil and cfg.type or 0

    if not util.IsObjNull(self.baseLayerIcon) then
        self.baseLayerIcon:SetActive(true)
    end
    if not util.IsObjNull(self.baseLayerFrame) then
        self.baseLayerFrame:Switch(cfg.rarityType - 2)--hero_mgr.Star2Frame[starsLv])
    end
    if not util.IsObjNull( self.baseLayerBg) then
        self.baseLayerBg:Switch(cfg.rarityType - 2)--hero_mgr.Star2Frame[starsLv])
    end
    if not util.IsObjNull(self.typeIcon) then
        self.typeIcon:SetActive(heroType ~= 0 and heroType ~= nil)
        self.typeIcon:Switch((heroType - 1) or 0)
    end
    if not util.IsObjNull(self.jobIcon) then
        self.jobIcon:Switch(cfg.profession - 1)
    end

    local currentFace = gw_hero_mgr.ChangeHeroIcon(cfgID, starsLv)
    -- if not self.hero.rawIcon then
    --     local skinID = self.hero.skinID
    --     if self.hero.battleProp and self.hero.battleProp.power and ((not skinID) or (skinID == 0)) then
    --         --如果有battleProp字段证明传入的是自己的英雄，外部没传入则自动检测是否穿戴皮肤
    --         local notMaze = true
    --         if self.hero.heroSid then
    --             local player_mgr = require "player_mgr"
    --             if player_mgr.GetMazePalPartDataBySid(self.hero.heroSid) or player_mgr.GetPeakPalPartDataBySid(self.hero.heroSid) then
    --                 notMaze = false
    --             end
    --         end
    --         if notMaze then
    --             local skin_mgr = require "skin_mgr"
    --             skinID = skin_mgr.GetCurSkinID(self.hero.heroID)
    --         end
    --     end
    --     if skinID and skinID ~= 0 then
    --         local faceID = hero_mgr.ChangeHeroSkinIcon(skinID, starsLv)
    --         currentFace = faceID or currentFace
    --     end
    -- end
    if currentFace then 
        spriteAsset:GetSprite(currentFace, function(sprite)
            if self:IsDisposed() then
                return
            end
            self.baseLayerIcon.sprite = sprite
        end)
    end
end
function HeroItem_new:ShowIconFrame(flag)
    self.baseLayerFrame:SetActive(flag)
end

function HeroItem_new:SetIconGray(flag)
    if (self.baseLayerIcon ~= nil) then
        self.baseLayerIcon:SetGray(flag)
    end
    self.frameGray:SetEnable(flag)
end

--英雄不可按
function HeroItem_new:Interactable(flag)
    self.interactableFlag.value = flag
    self:SetState()
end

--锁住英雄
function HeroItem_new:LockHero(flag, interactable)
    self:Interactable(interactable and interactable or not flag)
    self:SetState()
end

function HeroItem_new:SetClickCallback(callback)
    self.callback = callback
end

function HeroItem_new:SetSelect(select)
    self:SetSelectMaskEnable(select)
end

function HeroItem_new:SetInteractable(flag)
    self.button.interactable = flag
end

function HeroItem_new:ShowHeroLv(value)
    if not value then 
        self.levelText.gameObject:SetActive(false)
        log.Error("heroItemNew showHeroLv value is nil")
        return
    end
    self.levelText.gameObject:SetActive(true)
    if value then
        self.levelText.text = string.format("Lv.%d   ", value)
    end
end

function HeroItem_new:IsLoaded()
    if self then
        return self.loaded
    end
    return false
end

--Set selectedMask
---@param enabled boolean
function HeroItem_new:SetSelectMaskEnable(enabled)
    if enabled == nil then
        enabled = false
    end
    self.maskSelectState = enabled
end

function HeroItem_new:RefreshMask()
    if not self.loadedMask then
        self:LoadAddRes("selectMask", self.selectedMaskTrans)
        self.loadedMask = true
    end
    if not self.UIRoot then
        return
    end
    if self.maskNew then
        -- already exists
        self:RefreshAddContent("selectMask")
    end
end

--Set selected HP
---@param text - hpText
function HeroItem_new:OnShowHP(enable, value, value2)
    if enable == nil then
        enable = false
    end
    self.hpItemState = enable
    self.hpValue = value
    self.mpValue = value2
end

function HeroItem_new:RefreshHP()
    if not self.loadedHpItem then
        self:LoadAddRes("hpItem", self.hpItemTrans)
        self.loadedHpItem = true
    end
    if not self.UIRoot then
        return
    end
    if self.hpText then
        -- already exists
        self:RefreshAddContent("hpItem")
    end
end

function HeroItem_new:CheckAddState(parentTrans)
    local count = parentTrans.childCount
    return count > 0
end

-- load add Res
function HeroItem_new:LoadAddRes(type, parentTrans)
    if self:CheckAddState(parentTrans) then
        self:InitAddRes(type, parentTrans)
        return
    end
    local path = ADDRES_PATH[type]
    local asset_loader = require "asset_loader"
    local loader = self.assetLoaders[path]
    if (not loader) then
        loader = asset_loader(path, "hero_item_new")
        self.assetLoaders[path] = loader
    end
    loader:load(function(obj)
        if (not self:IsValid()) then
            loader:Dispose()
            return
        end
        GameObject.Instantiate(obj.asset, parentTrans, false)
        self:InitAddRes(type, parentTrans)
        self:RefreshAddContent(type)
    end)
end

function HeroItem_new:RefreshAddContent(type)
    if type == "selectMask" then
        if self.selectedMaskTrans.gameObject.activeSelf ~= self.maskSelectState then
            self.selectedMaskTrans:SetActive(self.maskSelectState)
        end
    elseif type == "hpItem" then
        self.hpItemTrans:SetActive(self.hpItemState)
        self.hpText.text = self.hpValue .. "/" .. self.mpValue
        local result = tonumber(self.hpValue) / tonumber(self.mpValue)
        self.sliderCom.value = result
    end
end

function HeroItem_new:InitAddRes(type, parentTrans)
    local addResTran = parentTrans:GetChild(0);
    if type == "selectMask" then
        self.maskNew = addResTran:Find("maskNew")
        self.selected = addResTran:Find("selected")
    elseif type == "hpItem" then
        self.sliderCom = addResTran:Find("sld_x_green"):GetComponent(typeof(Slider))
        self.hpText = addResTran:Find("sld_x_green/Text_1"):GetComponent(typeof(Text))
    end
end

function HeroItem_new:ReSetAddContent()
    self.selectedMaskTrans:SetActive(false)
    self.hpItemTrans:SetActive(false)
    self.liuguangEffect:SetActive(false)
end

-------------------------------兼容老代码处理，留空实现 -------------------------
--判断是否显示试用次数限制，通过战斗类型
function HeroItem_new:TrialLimitEnable(_type)

end

function HeroItem_new:ShowOrHideTeamIcon(value, index)

end

--原力武器图标
function HeroItem_new:ForceWeaponFlag(stage)

end
-------------------------------兼容老代码处理 - ------------------------

--[[是否显示精英特效
@param bool 是否显示
@param maskType render的显示类型，主要处理滑块光效穿透问题
]]
function HeroItem_new:HeroEffectEnable(bool, order, maskType, useIncrement)
    if self.isHeroanimitem == true then
        return
    end
    self.showEffect = bool
    self.maskType = maskType or 0
    self.effectOrder = order
    self.useIncrementOrder = useIncrement or false
end

--[[设置覆盖流光特效
@param number 流光特效类型
]]
function HeroItem_new:SetOverrideEffect(res_path, scale)
    self.overrideEffectRes = res_path
    self.overrideEffectScale = scale or 1
end

function HeroItem_new:DisplayImage(transform, renderTxInfo, enabled)
    local rawimage = transform:GetComponent(typeof(RawImage))
    if rawimage == nil then
        log.Warning("HeroItem_new 使用粒子系统转RT时需要绑定 RawImage组件")
        return false
    end
    return renderTxInfo:SetImage(transform, rawimage, enabled)
end

-- fxInfo.loadingResSet: 加载中的资源集
-- fxInfo.displayResPath: 当前显示的资源
-- fxInfo.resPath: 当前需要显示的资源
-- return: 是否需要加载 resPath。即当前需要显示的资源既不在加载中，也不在显示
function HeroItem_new:UpdateEffectLoadState(fxInfo)
    if fxInfo.loadingResSet == nil then
        fxInfo.loadingResSet = {}
    end
    local bLoading = fxInfo.loadingResSet[fxInfo.resPath]
    if bLoading then
        return false
    end

    if fxInfo.displayResPath == fxInfo.resPath then
        return false
    end

    return true
end

function HeroItem_new:AddEffectLoadingRes(fxInfo, resPath)
    if fxInfo.loadingResSet == nil then
        fxInfo.loadingResSet = {}
    end
    fxInfo.loadingResSet[resPath] = true
end

function HeroItem_new:RemoveEffectLoadingRes(fxInfo, resPath)
    if fxInfo.loadingResSet == nil then
        return
    end
    fxInfo.loadingResSet[resPath] = nil
end

function HeroItem_new:DisplayEffect(fxInfo)
    fxInfo.renderTxInfo = render_tool.GetRenderTextureByTag(fxInfo.resPath, self.ownerModuleName)
    if fxInfo.renderTxInfo then
        -- 当前显示的资源路径
        fxInfo.displayResPath = fxInfo.resPath
        local effectTrans = self[fxInfo.effectImageName]
        if effectTrans == nil then
            log.Error("需设置用于显示粒子效的图片:" .. tostring(fxInfo.effectImageName))
        else
            self:DisplayImage(effectTrans, fxInfo.renderTxInfo, true)
        end
    else
        if self:IsDisposed() then
            -- 界面已经销毁,不再加载资源
            return
        end

        -- loadingResPath： 加载中资源。displayResPath：当前显示资源。resPath：新显示资源
        -- 防止同一个 item 对同一资源在资源加载过程重复释放和加载，释放时根据资源路径释放，次数会变少
        -- 即加载过程中，先卸载，再加载，若不作处理，调用两次 render_tool.LoadResource，但卸载时，是按 ab name 卸载，会减少卸载次数
        local needLoadRes = self:UpdateEffectLoadState(fxInfo)
        if not needLoadRes then
            return
        end
        self:AddEffectLoadingRes(fxInfo, fxInfo.resPath)

        render_tool.LoadResource(fxInfo.resPath, function(assetbundleName, renderGameObject)
            local effectTrans = self[fxInfo.effectImageName]
            if effectTrans == nil then
                renderGameObject.Dispose()
                return
            end

            if self:IsDisposed() then
                -- 界面已经销毁,不再加载资源
                renderGameObject.Dispose()
                return
            end

            if fxInfo.resPath ~= assetbundleName or effectTrans == nil or util.IsObjNull(effectTrans) then
                --界面不再需要加载完成的资源，或已销毁
                renderGameObject.Dispose()
                return
            end

            local resPath = assetbundleName
            self:RemoveEffectLoadingRes(fxInfo, resPath)

            -- 加载特效资源完成时，可能当前帧已有其它 item 已完成资源加载，此处先从缓存中查询一次
            fxInfo.renderTxInfo = render_tool.GetRenderTextureByTag(resPath, self.ownerModuleName)
            if fxInfo.renderTxInfo then
                fxInfo.displayResPath = resPath
                self:DisplayImage(effectTrans, fxInfo.renderTxInfo, true)
                return
            end

            -- 缓存中不存在，创建新的 RT 显示图片
            renderGameObject.SetMaxParticleSize(100)
            renderGameObject.SetSize(Vector3.one * 0.01)

            local rawimage = effectTrans:GetComponent(typeof(RawImage))
            if rawimage then
                fxInfo.displayResPath = resPath
                fxInfo.renderTxInfo = renderGameObject.RenderGameObject(resPath, 132, 170, render_tool.RenderMode.Single, 0, -0.02, 10, nil, self.ownerModuleName)
                if fxInfo.renderTxInfo then
                    fxInfo.renderTxInfo:SetImage(effectTrans, rawimage, true)
                end
            else
                log.Error(effectTrans .. "使用粒子系统转RT时需要绑定 RawImage 组件")
                renderGameObject.Dispose()
            end
        end)
    end
    fxInfo.displaying = true
    return true
end

function HeroItem_new:DisplayParticleWithRT(resPath)
    self.renderTxInfo = render_tool.GetRenderTextureByTag(resPath, self.ownerModuleName)
    if self.renderTxInfo then
        self.effectModelViewerResPath = resPath
        self:DisplayImage(self.liuguangEffect, self.renderTxInfo, true)
    else
        if self.targetEffectModelViewerResPath == resPath or self.effectModelViewerResPath == resPath then
            -- 防止同一个 item 对同一资源在资源加载过程重复释放和加载，释放时根据资源路径释放，次数会变少            
            return true
        end
        if self:IsDisposed() then
            -- 界面已经销毁,不再加载资源
            return
        end

        self.targetEffectModelViewerResPath = resPath
        render_tool.LoadResource(resPath, function(assetbundleName, renderGameObject)
            if self:IsDisposed() then
                -- 界面已经销毁,不再加载资源
                renderGameObject.Dispose()
                return
            end
            if self.targetEffectModelViewerResPath ~= assetbundleName or self.liuguangEffect == nil or util.IsObjNull(self.liuguangEffect) then
                --界面不再需要加载完成的资源，或已销毁
                renderGameObject.Dispose()
                return
            end
            self.targetEffectModelViewerResPath = nil
            self.renderTxInfo = render_tool.GetRenderTextureByTag(resPath, self.ownerModuleName)
            if self.renderTxInfo then
                self.effectModelViewerResPath = resPath
                self:DisplayImage(self.liuguangEffect, self.renderTxInfo, true)
                return
            end
            renderGameObject.SetMaxParticleSize(100)
            renderGameObject.SetSize(Vector3.one * 0.01)
            local rawimage = self.liuguangEffect:GetComponent(typeof(RawImage))
            if rawimage then
                self.effectModelViewerResPath = resPath

                self.renderTxInfo = renderGameObject.RenderGameObject(resPath, 132, 170, render_tool.RenderMode.Single, 0, -0.02, 10, nil, self.ownerModuleName)
                if self.renderTxInfo then
                    self.renderTxInfo:SetImage(self.liuguangEffect, rawimage, true)
                end
            else
                log.Warning(self.liuguangEffect .. "使用粒子系统转RT时需要绑定 RawImage 组件")
                renderGameObject.Dispose()
            end
        end)
    end
    self.initLiuGuangEffect = true
    return true
end

local effectForSPlus = {
    [gw_hero_mgr.Hero_Star.Purple] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_zi/prefabs/effect_ui_juexinyanjiusuo_shangzhen_zi.prefab",
    [gw_hero_mgr.Hero_Star.PurplePlus] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_zi_02/prefabs/effect_ui_juexinyanjiusuo_shangzhen_zi_02.prefab",
    [gw_hero_mgr.Hero_Star.Yellow] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_huang/prefabs/effect_ui_juexinyanjiusuo_shangzhen_huang.prefab",
    [gw_hero_mgr.Hero_Star.YellowPlus] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_huang_02/prefabs/effect_ui_juexinyanjiusuo_shangzhen_huang_02.prefab",
    [gw_hero_mgr.Hero_Star.Red] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_hong/prefabs/effect_ui_juexinyanjiusuo_shangzhen_hong.prefab",
    [gw_hero_mgr.Hero_Star.RedPlus] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_hong_02/prefabs/effect_ui_juexinyanjiusuo_shangzhen_hong_02.prefab",
    [gw_hero_mgr.Hero_Star.White] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_bai/prefabs/effect_ui_juexinyanjiusuo_shangzhen_bai.prefab",
    [gw_hero_mgr.Hero_Star.White_1] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_bai_02/prefabs/effect_ui_juexinyanjiusuo_shangzhen_bai_02.prefab",
    [gw_hero_mgr.Hero_Star.White_2] = "art/effects/effects/effect_ui_common_herokuang_glowsao03/prefabs/effect_ui_common_herokuang_glowsao03.prefab",
    [gw_hero_mgr.Hero_Star.White_3] = "art/effects/effects/effect_ui_common_herokuang_glowsao02/prefabs/effect_ui_common_herokuang_glowsao02.prefab",
    [gw_hero_mgr.Hero_Star.White_4] = "art/effects/effects/effect_ui_common_herokuang_glowsao01/prefabs/effect_ui_common_herokuang_glowsao01.prefab",
    [gw_hero_mgr.Hero_Star.White_5] = "art/effects/effects/effect_ui_common_herokuang_saoguang/prefabs/effect_ui_common_herokuang_saoguang.prefab"
}

function HeroItem_new:LoadHeroLiuGuangEffect()
    local resPath = ""
    --end
    local cfg = self.heroEntity:GetHeroCfg()
    local handBook_cfg = game_scheme:Hero_0(cfg.HerohandBookID)
    local starsLv = self.heroEntity:GetStar()

    -- 英雄稀有度类型（1B 2A 3S 4S+），S+ 英雄特效与其它英雄不一致
    if handBook_cfg and handBook_cfg.rarityType == 4 then
        --只保留S+的特效，其他都不需要
        resPath = effectForSPlus[starsLv]
    end

    --使用覆盖流光
    if not string.empty(self.overrideEffectRes) then
        resPath = self.overrideEffectRes
    end

    if not resPath or resPath == "" then
        return
    end
    self.liuguangEffect:SetActive(true)
    if const.CanUseParticlSystemRT(self.ownerModuleName) then
        if self.initLiuGuangEffect then
            --所有逻辑中必须先卸载，再加载新特效，否则判定为逻辑问题，不更新特效，防止效果重复加载
            log.Warning("HeroItem_new 重复加载特效，请检查逻辑，先卸载原有特效")
            return
        end
        --使用render_tool的情况下，后续逻辑不执行
        if self:DisplayParticleWithRT(resPath) then
            return
        end
    end
    -- 根据屏幕分辨率适配
    -- local scaleFactor = (screen_util.width / screen_util.height) / (720/1280) * self.scale
    if self.effectModelViewer == nil then
        self.effectModelViewer = CModelViewer()

        self.effectModelViewer:Init(self.liuguangEffect, function(obj)
            self.effectModelViewer:ShowGameObject(resPath, function(goEffect)
                self.goEffect = goEffect
                local scale = Vector3.one
                local childs = goEffect:GetComponentsInChildren(typeof(ParticleSystemRenderer))
                local child = nil
                --覆盖流光特效自定义控制缩放
                if resPath == self.overrideEffectRes then
                    scale = { x = self.overrideEffectScale, y = self.overrideEffectScale, z = self.overrideEffectScale }
                end
                for i = 0, childs.Length - 1 do
                    child = childs[i]
                    child.maskInteraction = self.maskType or 0
                end
                goEffect.transform.localScale = scale
                goEffect.transform.localPosition = { x = 0, y = 0, z = 0 }
            end)
        end)
    end
    --设置光效的渲染顺序
    self.effectModelViewer:SetRenderOrder(self.effectOrder or 100, self.useIncrementOrder)
end

function HeroItem_new:UnLoadHeroLiuGuangEffect()
    if self.effectModelViewer then
        self.effectModelViewer:ResetScale()--遗落之境开始战斗上阵时的英雄特效缩放问题修复
        self.effectModelViewer:Dispose()
        self.effectModelViewer = nil
    end

    -- 增加 self.targetEffectModelViewerResPath 记录当前最新的加载资源，防止资源加载过程中，先释放，再加载相同资源造成的多次调用 render_tool 绘制
    if self.effectModelViewerResPath then
        render_tool.ReleaseRenderGameObject(self.effectModelViewerResPath, self.ownerModuleName, self.renderTxInfo)
        self:DisplayImage(self.liuguangEffect, self.renderTxInfo, false)
        self.effectModelViewerResPath = nil
        self.renderTxInfo = nil
    end

    self.initLiuGuangEffect = false
end

function HeroItem_new:SetSelectEffectValid(bValid)
    local flag = "select"

    local fxInfo = self.displayFx[flag]
    if not fxInfo then
        log.Warning("需要开启的选择效果不支持:" .. flag)
        return
    end

    local resPath = self:GetSelectResPath()
    if fxInfo.resPath == resPath and fxInfo.valid == bValid then
        return
    end

    if fxInfo.resPath ~= resPath then
        fxInfo = self:SetEffectResPath(flag, resPath)
    end

    self:SetEffectValid(flag, bValid)
end

function HeroItem_new:SetEffectValid(flag, bValid)
    local fxInfo = self.displayFx[flag]
    if bValid then
        if fxInfo and fxInfo.valid ~= bValid then
            fxInfo.valid = true
            self:LoadHeroEffect(flag)
        else
            log.Warning("需要开启的效果类型不支持:" .. flag)
            return
        end
    else
        if fxInfo and fxInfo.valid ~= bValid then
            fxInfo.valid = false
            self:UnLoadHeroEffect(flag)
        end
    end
end

function HeroItem_new:LoadHeroEffectSet()
    local k, v
    for k, v in pairs(self.displayFx) do
        if v.valid then
            self:LoadHeroEffect(k)
        end
    end
end

function HeroItem_new:UnLoadHeroEffectSet()
    local k, v
    for k, v in pairs(self.displayFx) do
        self:UnLoadHeroEffect(k)
    end
end

function HeroItem_new:LoadHeroEffect(flag)
    if not const.CanUseParticlSystemRT(self.ownerModuleName) then
        log.Warning("不支持加载真实粒子系统实现:" .. tostring(flag))
        return
    end

    local fxInfo = self.displayFx[flag]
    if not fxInfo then
        log.Error("特效数据未初始化:" .. tostring(flag))
        return
    end

    if fxInfo.displaying then
        --所有逻辑中必须先卸载，再加载新特效，否则判定为逻辑问题，不更新特效，防止效果重复加载
        log.Error("HeroItem_new 重复加载卡牌可选择特效，请检查逻辑，先卸载原有特效")
        return
    end
    if fxInfo.resPath == nil or fxInfo.resPath == "" then
        log.Error("特效数据未设置需要显示的资源 AssetBundleName：" .. tostring(flag))
        return
    end

    self:DisplayEffect(fxInfo)
end

function HeroItem_new:UnLoadHeroEffect(flag)
    local fxInfo = self.displayFx[flag]
    if not fxInfo then
        return
    end

    -- 增加 targetEffectModelViewerResPath 记录当前最新的加载资源，防止资源加载过程中，先释放，再加载相同资源造成的多次调用 render_tool 绘制
    if fxInfo.displayResPath and fxInfo.renderTxInfo then
        render_tool.ReleaseRenderGameObject(fxInfo.displayResPath, self.ownerModuleName, fxInfo.renderTxInfo)

        local effectTrans = self[fxInfo.effectImageName]
        if effectTrans then
            self:DisplayImage(effectTrans, fxInfo.renderTxInfo, false)
        end
        fxInfo.displayResPath = nil
        fxInfo.renderTxInfo = nil
    end

    fxInfo.displaying = false
end

function HeroItem_new:SetEffectResPath(flag, resPath)
    if resPath == nil or resPath == "" then
        log.Warning("SetEffectResPath 不能为空")
        return
    end

    local fxInfo = self.displayFx[flag]
    if fxInfo == nil then
        fxInfo = {}
        self.displayFx[flag] = fxInfo
    end
    fxInfo.resPath = resPath

    return fxInfo
end
function HeroItem_new:BossFlagEnable(isBoss)
end

function HeroItem_new:SetBottomState(state)
    self.bottomState = state
    if self.gameObject then
        self.bottomRtsf.gameObject:SetActive(state)
    end
end

function HeroItem_new:ClearDisplayFx()
    local k, v
    for k, v in pairs(self.displayFx) do
        self:UnLoadHeroEffect(k)

        v.valid = false
        v.displaying = false
        v.loadingResSet = {}
        v.displayResPath = nil
        v.resPath = nil
    end
end

local class = require "class"
local base_game_object = require "base_game_object"
CHeroItem = class(base_game_object, nil, HeroItem_new)