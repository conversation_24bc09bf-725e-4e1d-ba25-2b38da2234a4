local require = require

local Application = CS.UnityEngine.Application

local game_config = require "game_config"
local const = require "const"

module("version_mgr")

--[[   
	使用说明:

我们提供一个主干版本号和一个分支版本号，主干包主干版本号和分支版本号都为打包时的版本，分支包主干版本号为切分支时的主干版本，分支版本号为打包时的版本。
正常只需要判断主干版本号，
下面情况需要判断分支版本号，如svn版本(看svn日志)25380我提交了C#代码到主干，但是25379版本时已经切了一个分支，如果这时候我需要把C#代码提交到分支，则判断主干版本号大于等于25380已经不适用，因为分支的主干版本号为25379，如果我在25381版本将代码提交到了分支，则需要判断分支版本号大于等于25381,即 version_mgr.CheckSvnTrunkVersion(25380) or version_mgr.CheckSvnBranchVersion(25381)。

]]


--判断svn版本  weimian1 判断接口，后续统一返回true, 相关代码直接使用true分支
function CheckSvnTrunkVersion(trunk)
	-- if Application.isEditor then
	-- 	return true
	-- end
	-- local tv, bv = GetSvnVersion()
	-- if tv == nil then
	-- 	return false
	-- end
	-- if tv >= trunk then
	-- 	return true
	-- end
	return true
end

--判断svn版本 weimian1 判断接口，后续统一返回true
function CheckSvnBranchVersion(branch)
	-- if Application.isEditor then
	-- 	return true
	-- end
	-- local tv, bv = GetSvnVersion()
	-- if bv == nil then
	-- 	return false
	-- end
	-- if bv >= branch then
	-- 	return true
	-- end
	return true
end

function GetSvnVersion()
	if game_config.TRUNK_VERSION and game_config.BRANCH_VERSION then
		return game_config.TRUNK_VERSION-const.SVN_VERSION_OFFSET, game_config.BRANCH_VERSION-const.SVN_VERSION_OFFSET
	end
	return nil, nil
end