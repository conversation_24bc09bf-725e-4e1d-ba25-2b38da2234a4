
local require = require
local GWG = GWG
local Collider = CS.UnityEngine.Collider
local SphereCollider = CS.UnityEngine.SphereCollider
local typeof = typeof
local Vector3 = CS.UnityEngine.Vector3
local math = math
-- 添加更多Unity类型引用用于Debug
local GameObject = CS.UnityEngine.GameObject
local Color = CS.UnityEngine.Color
local Material = CS.UnityEngine.Material
local Shader = CS.UnityEngine.Shader
local Renderer = CS.UnityEngine.Renderer
local Object = CS.UnityEngine.Object
local PrimitiveType = CS.UnityEngine.PrimitiveType
local function_open_mgr = require "function_open_mgr"
local event				= require "event"
local util = require "util"

module("gw_home_lord_util")

local M = {}

-- 领主组件引用
M.lordCompId = nil
M.lordComp = nil

-- Debug相关
M.debugMode = false
M.clickedGridDebugObj = nil  -- 点击位置的红色地块Debug对象

-- 创建领主组件
function M.CreateLordComp()
    -- 使用临时模型路径，后续美术出资源后替换
    local modelPath = "art/characters/lingzhu/prefabs/lingzhu_simple.prefab" 
    
    -- 创建组件
    local comp, id = GWG.GWAdmin.PopBSComponent(GWG.GWCompName.gw_home_comp_lord)
    comp.entityType = GWG.GWConst.EHomeEntityType.Lord
    -- 获取领主配置
    local gw_home_config_mgr = require "gw_home_config_mgr"
    local lordConfig = gw_home_config_mgr.GetLordConfig()
    
    -- 加载模型
    comp.entity = GWG.GWAssetMgr:Load(modelPath, function(asset)
        comp:Bind(asset)

        -- 设置模型缩放和位置调整
        if asset and asset.transform then
            asset.transform.localScale = {x=lordConfig.modelScale, y=lordConfig.modelScale, z=lordConfig.modelScale}

            -- 添加碰撞体用于点击检测
            local collider = asset:GetComponent(typeof(Collider))
            if not collider then
                -- 如果没有碰撞体，添加一个球形碰撞体
                collider = asset:AddComponent(typeof(SphereCollider))
                if collider then
                    collider.radius = 0.5  -- 设置碰撞体半径
                    collider.isTrigger = false  -- 不是触发器，用于点击检测
                end
            end
        end

    end, GWG.GWHomeNode.heroNode(), "Home_Lord_" .. id)
    
    return id, comp
end

-- 获取领主节点（用于挂载领主实体）
function M.GetLordParentNode()
    -- 使用实体层级作为父节点
    return GWG.GWHomeNode.heroNode()
end

function M.IsModuleOpen()
    local net_module_open = require "net_module_open"
    local moduleOpenPro_pb = require "moduleOpenPro_pb"
    local isShow = net_module_open.GetModuleIsOn(moduleOpenPro_pb.emModuleID_LordPatrol)
    return isShow
end

-- 初始化领主系统
function M.InitLordSystem(isForce)
    if not M.IsModuleOpen() and not isForce then
        return
    end
    local isOpen = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.HomeLord) -- 是否解锁了
    if not isOpen and not isForce then
        return
    end
    ---防御性编程 节点不能为空
    if GWG.GWHomeNode == nil or GWG.GWHomeNode.heroNode()==nil then
        return
    end
    -- 检查是否已经创建了领主
    if M.lordCompId then
        return M.lordCompId, M.lordComp
    end

    -- 初始化领主专用的网格数据
    local gw_home_astar_pathfinding = require "gw_home_astar_pathfinding"
    gw_home_astar_pathfinding.ForceRefreshWalkableData()

    -- 创建领主组件
    local id, comp = M.CreateLordComp()

    -- 保存引用
    M.lordCompId = id
    M.lordComp = comp

    return id, comp
end

-- 获取当前领主组件
function M.GetLordComp()
    return M.lordComp
end

-- 暂停领主系统
function M.PauseLordSystem()
    if M.lordComp then
        M.lordComp:Pause()
        return true
    end
    return false
end

-- 恢复领主系统
function M.ResumeLordSystem()
    if M.lordComp then
        M.lordComp:Resume()
        return true
    end
    return false
end

-- 检查领主系统是否存在
function M.IsLordSystemExists()
    return M.lordComp ~= nil and M.lordCompId ~= nil
end

-- 处理场景点击事件（集成到输入系统）
function M.OnSceneClick(worldX, worldY)
    if not M.lordComp then
        return false
    end

    -- 转换为网格坐标
    local gw_home_grid_data = require "gw_home_grid_data"
    local gridX, gridY = gw_home_grid_data.GetGridXYdByXZ(worldX, worldY)

    -- 确保网格坐标有效
    if not gridX or not gridY then
        GWG.GWAdmin.SwitchUtility.HomeLog("无效的点击位置:", worldX, worldY)
        return false
    end

    -- 四舍五入到最近的整数网格
    gridX = math.floor(gridX + 0.5)
    gridY = math.floor(gridY + 0.5)
    
    GWG.GWAdmin.SwitchUtility.HomeLog("点击位置转换: 世界坐标(" .. worldX .. "," .. worldY .. ") -> 网格坐标(" .. gridX .. "," .. gridY .. ")")
    
    -- 检查点击位置是否可通行
    local gw_home_astar_pathfinding = require "gw_home_astar_pathfinding"
    if gw_home_astar_pathfinding.IsWalkable(gridX, gridY) then
        -- 显示点击位置的红色地块Debug（如果开启了Debug模式）
        if M.debugMode then
            M.ShowClickedGridDebug(gridX, gridY)
        end
        
        local properties = {LordPos = gridX.."#"..gridY}
        event.Trigger(event.GAME_EVENT_REPORT, "LordOrder", properties)
        
        -- 设置领主的手动目标点
        M.lordComp:SetManualTarget(gridX, gridY)
        return true  -- 表示已处理该点击事件
    else
        GWG.GWAdmin.SwitchUtility.HomeLog("点击位置不可通行:", gridX, gridY)
    end

    return false  -- 未处理该点击事件
end

-- 处理领主点击事件
function M.OnLordClick()
    if M.lordComp then
        M.lordComp:OnClick()
        return true
    end
    return false
end

-- 清理领主系统
function M.DisposeLordSystem()
    -- 关闭Debug模式
    M.SetDebugMode(false)

    -- 清理红色地块Debug
    M.HideClickedGridDebug()

    if M.lordComp then
        M.lordComp:Recycle()
        M.lordComp = nil
    end
    M.lordCompId = nil

    -- 重置所有状态
    M.debugMode = false
    
    GWG.GWAdmin.SwitchUtility.HomeLog("领主系统已完全清理")
end

-- Debug功能：开启/关闭Debug模式
function M.SetDebugMode(enabled)
    M.debugMode = enabled

    local gw_home_astar_pathfinding = require "gw_home_astar_pathfinding"
    gw_home_astar_pathfinding.SetDebugMode(enabled)

    if enabled then
        GWG.GWAdmin.SwitchUtility.HomeLog("领主巡逻系统Debug模式已开启")
    else
        -- 清理点击位置Debug
        M.HideClickedGridDebug()
    end
end

function M.RefreshDebugView()
    if M.lordCompId then
        local gw_home_astar_pathfinding = require "gw_home_astar_pathfinding"
        gw_home_astar_pathfinding.ShowWalkableDebugView()
    end
end


-- 检查点击是否命中领主
function M.IsClickOnLord(worldX, worldY)
    if not M.lordComp or util.IsObjNull(M.lordComp.transform) then
        return false
    end
    
    local lordPos = M.lordComp.transform.position
    local distance = Vector3.Distance(
            Vector3(worldX, lordPos.y, worldY),
        lordPos
    )
    
    -- 检查点击距离是否在领主的交互范围内（1个网格单位）
    return distance <= 1.0
end

-- 显示点击位置的红色地块Debug
function M.ShowClickedGridDebug(gridX, gridY)
    -- 清理之前的红色地块
    M.HideClickedGridDebug()

    -- 获取世界坐标
    local gw_home_grid_data = require "gw_home_grid_data"
    local worldX, worldY, worldZ = gw_home_grid_data.GetPosByGridXY(gridX, gridY, true)

    -- 创建红色地块Debug对象
    local debugObj = GameObject.CreatePrimitive(PrimitiveType.Cube)
    debugObj.name = "ClickedGridDebug_" .. gridX .. "_" .. gridY
    debugObj.transform.position = Vector3(worldX, worldY + 0.05, worldZ)
    debugObj.transform.localScale = Vector3(0.9, 0.05, 0.9)

    -- 设置红色材质
    local renderer = debugObj:GetComponent(typeof(Renderer))
    if renderer then
        local material = Material(Shader.Find("Standard"))
        material.color = Color.red
        material:SetFloat("_Metallic", 0)
        material:SetFloat("_Glossiness", 0.8)
        renderer.material = material
    end

    -- 移除碰撞体
    local collider = debugObj:GetComponent(typeof(Collider))
    if collider then
        Object.DestroyImmediate(collider)
    end

    M.clickedGridDebugObj = debugObj
end

-- 隐藏点击位置的红色地块Debug
function M.HideClickedGridDebug()
    if M.clickedGridDebugObj then
        Object.DestroyImmediate(M.clickedGridDebugObj)
        M.clickedGridDebugObj = nil
    end
end

return M
