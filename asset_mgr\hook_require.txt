-- 通过钩子函数拦截 require 操作，记录模块加载的时间和路径信息，并提供事件跟踪和日志报告功能。 有助于开发者分析模块加载性能和调试代码加载顺序问题

local require = require
local pairs = pairs
local print = print 
local type = type
local table = table
local tostring = tostring
local getmetatable = getmetatable
local string = string
local debug = debug
local _G = _G
local dkjson = require "dkjson"
local Time = CS.UnityEngine.Time
local tonumber = tonumber

module("hook_require")
local config_para = 
{
    --手动控制，当前console_util_config的值还未生效
    enable_check_inner_exe_fun = nil, --开启检测require的脚本是否内部自身函数内部其他函数一层调用     
    enable_check_lua_type = nil, --开启检测加载的lua脚本的类型（主要为了检测是否是class实例函数）
    enable_gather_source_stack = true  --开启lua脚本被require进来的堆栈
}

local require_table = {}
local requireStep = "Step0"
-- 判断是否以指定字符串开头
local function IsStartWith(str, prefix)
    if not str or not prefix then
        return false
    end
    return string.sub(str, 1, #prefix) == prefix
end

-- 判断是否以指定字符串结尾
local function IsEndWith(str, suffix)
    if not str or not suffix then
        return false
    end
    return #str >= #suffix and string.sub(str, -#suffix) == suffix
end
---@public 获取lua模块的类型 比如是单例静态脚本/容许实例的脚本
function GetLuaModuleType(module, moduleName)
    local tType = type(module)
    local ret = tType
    if tType ~= "table" then
        if tType == "boolean" then          
            ret = "loaded"
        end
    else
        ret = IsSingleton(module,moduleName) and  "Static" or "Inst"       
    end
    return ret
end
---@public 检测是否是单例
function IsSingleton(mod,moduleName)
    if not mod or not moduleName then
        return false
    end
    -- 基础类型检查
    if type(mod) ~= "table" then
        return false
    end  
    -- 检查是否存在构造痕迹
    local has_ctor = mod.new or mod.ctor or mod.__call or mod.Close
    if  has_ctor then
        return false
    end   
    if IsStartWith( moduleName,"ui_") and  not (IsEndWith(moduleName, "mgr")  or  IsEndWith(moduleName, "data")) then
        return false
    end
    if  IsEndWith(moduleName, "_controller") then
        return false
    end
    -- 扫描元表 "存在元表操作可能支持实例化"
    local mt = getmetatable(mod)
    if mt and (mt.__index or mt.__call) then
        return false
    end    
    --本想实现 :FunA()的方法列表中是否首个包含self;但必须运行时检测；但麻烦了，且破坏逻辑了，当前只通过一些规则来判断
    return true
end
--region 钩子函数
local config_check_inner_exe_fun_white_list = {
    Init = true,
    init = true,
    Register = true,
    Unregister = true,
    Trigger = true,
    RegisterMsgHandlers = true,
    RegisterResponseLuaFuncNew = true,
    initialize = true
}
local curRequireStack = {}  
local function tempHook(event)
    if event == "call" then
        local info = debug.getinfo(2, "nS") -- 获取上层调用信息
        if info and info.source == "=[C]" then
            return
        end
        local func_name = info.name or "anonymous"
        --添加白名单；要不太卡了
        if not config_check_inner_exe_fun_white_list[func_name] then
            return
        end
        local source = info.source:match("@?(.*)")
        --取栈顶元素
        local curModule = curRequireStack[#curRequireStack]
        --如果来源不是自身，也忽略
        if source ~= curModule then
            --再次获取上一层堆栈是否是模块 ---主要为了检测一层调佣
           local  info2 = debug.getinfo(3, "nS") -- 获取上层调用信息
            local source2 = info2.source:match("@?(.*)")
            if source2 ~= curModule then
                return                
            end
            func_name = source.."_"..func_name 
        end        
        if require_table.hT[curModule]  then
            if not  require_table.hT[curModule].callFuncRecord then
                require_table.hT[curModule].callFuncRecord = {}
                
            end
            if not require_table.hT[curModule].callFuncRecord[func_name] then
                require_table.hT[curModule].callFuncRecord[func_name] = 0
            end
            require_table.hT[curModule].callFuncRecord[func_name] =  require_table.hT[curModule].callFuncRecord[func_name] + 1             
            --print("require期间被调用 module=",curModule,source,func_name,require_table.hT[curModule].callFuncRecord[func_name])
        end
    end
end
--@public 设置函数调用钩子
function SetFunctionCallHook( active)
    if not active then
        debug.sethook()
    else
        debug.sethook(tempHook, "c") -- 仅监控调用事件
    end
end
local active = nil
--@public 检测我是否需要启动钩子
function CheckFunctionCallHook(moduleName, depth)
    if  depth == 1 then
        if active then          
            SetFunctionCallHook(false)
        else         
            SetFunctionCallHook(true)
        end
        active = not active
    end
end
--endregion
function hook_require()
    if require_table.hc then 
        return 
    end
    local oldrequire = _G.require
    local ind = 1
    local hook_count = require "hook_count"
    require_table.hc = hc or hook_count("hook_require")
    local hc = require_table.hc
    --引用层级和计数
    local current_depth = 0
    local hT = {}
    require_table.hT = hT
    local tmpT = hT
    local get_path = function(t)
        local path = ""
        local limite = 500
        while t do
            if limite <= 0 then break end
            limite = limite - 1
            path = (t.modname or "") .. "/" .. path
            t = t.parent
        end
        if config_para.enable_gather_source_stack then
            --预留支持打印堆栈
            path = path.."             "..debug.traceback()
            path = string.gsub(path, "[\n\t]", ";")            
        end
        return path
    end
    local function newrequire(modname)
        if require_table[modname] then return oldrequire(modname) end
        local tind = ind
        ind = ind + 1
        current_depth = current_depth + 1
        local starttime = Time.realtimeSinceStartup
        print("require",modname,tmpT.modname,current_depth)
        hT[modname] =hT[modname] or {parent = tmpT,modname = modname}
        tmpT = hT[modname]
        local selfT = tmpT
        local res
        local innerCountText = nil
        local innerFun = nil
        if config_para.enable_check_inner_exe_fun then
            CheckFunctionCallHook(modname,current_depth)
            --入栈
            table.insert(curRequireStack,modname)
            res = oldrequire(modname)
            --出栈
            table.remove(curRequireStack,#curRequireStack)
            CheckFunctionCallHook(modname,current_depth)
            innerFun = "NULL"
            innerCountText = "0"
            if hT[modname].callFuncRecord then
                innerFun = ""
                innerCountText = ""               
                --只取最多前10个
                local tempIndex = 0
                for i, v in pairs(hT[modname].callFuncRecord) do                    
                    tempIndex = tempIndex + 1
                    if tempIndex > 10 then                        
                        break
                    end
                    innerFun = innerFun..i..";"
                    innerCountText = innerCountText.."_"..tostring(v)                   
                end
            end
        else
            res = oldrequire(modname)
        end
        local endtime = Time.realtimeSinceStartup
        tmpT = selfT.parent or hT
        --print("requireddd", modname, tmpT.modname,current_depth) 
        local typeResult = nil
        if config_para.enable_check_lua_type then
            typeResult = GetLuaModuleType(res,modname) 
        end
        --保留5位小数
        local div = endtime - starttime 
        div = tonumber(string.format("%.5f", div))
        hc:Count(modname,tind,starttime,endtime,div,get_path(selfT),current_depth,requireStep,typeResult,innerFun,innerCountText)
        require_table[modname] = true
        current_depth = current_depth - 1
        return res
    end
    _G.require = newrequire
    
end

function SetRequireStep(step)
    requireStep = step
end

function track_event(ev,param)
    local event = require("event")
    event.Trigger(
        event.GAME_EVENT_REPORT,
        ev,
        param,true
    )
    -- if Application.isEditor then 
    --     return
    -- end
    -- if not Q1SDK.IsEnable() then
    --     return
    -- end
    -- Q1SDK.Instance:TrackEvent(ev,param)
end
 

function set_require_report()
    local log = require("log")
    local oldrequire = _G.require
    local require_table_1 = {}
    local marked = {}
    local trigger_func = function(t)
        -- print("require_report",dkjson.encode(t))

        local param = t.param or {}
        for v,_ in pairs(param) do
            do 
                if marked[v] then
                    break
                end
                marked[v] = true
                track_event("code_litter",
                {
                    ctype = 2,
                    param1 = v,
                })
            end
            -- print("require_report", module_name)
        end
    end

    local function newrequire(modname)
        if require_table_1[modname] then
            return oldrequire(modname)
        end
        
        local res = oldrequire(modname)
        require_table_1[modname] = true

        log._logfilter("require_report",modname,nil,10,trigger_func)
        return res
    end
    _G.require = newrequire

end
 

function print_hook()
    -- local path = require_table.hc:DebugCsv()
    local hT = require_table.hT
    for k,v in pairs(hT) do
        v.parent_name = v.parent and v.parent.modname or ""
        v.parent = nil
    end
    print("print_hook",dkjson.encode(hT))
end

return {
    hook_require = hook_require,
    print_hook = print_hook,
    set_require_report = set_require_report,
    GetLuaModuleType = GetLuaModuleType,
    IsSingleton = IsSingleton,
    SetRequireStep = SetRequireStep,
    
}