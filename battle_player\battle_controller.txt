local ui_window_mgr = require "ui_window_mgr"
local player_mgr = require "player_mgr"
local game_scheme = require "game_scheme"
local battle_manager = require "battle_manager"
local battle_data = require "battle_data"
local time_scale_mgr = require "time_scale_mgr"


module("battle_controller")

local BattleController = {}

function BattleController:ctor(selfType)
end

function BattleController:GetBattleType()
end

function BattleController:Initialize()
end

function BattleController:OnBattleBegin()
end

function BattleController:OnBattleEnd(skiped)
end

function BattleController:OnBattleEvent(event)
end

function BattleController:OnRoundBegin(round)
    if self.battleUI ~= nil then
        self.battleUI.ShowRound(round)
    end
    event.Trigger(event.NEXT_ROUND, round)
end

function BattleController:OnRoundEnd(round)
    -- todo: maxspeed minspeed
    local speed = battle_manager.GetPlaySpeed()
    local isSpeed = ( maxspeed-minspeed>0.1 and speed > 0.5 * (minspeed + maxspeed) ) and 2 or 1
    local properties = string.format("{\"round\":%d,\"is_speed\":%d,\"battle_type\":\"%s\",\"dungeon_id\":%d}",round,isSpeed,stageTypeStr,levelID )
    event.Trigger(event.GAME_EVENT_REPORT, "battle_round", properties)
end

function BattleController:CanSkip()
    return true;
end

function BattleController:OnWeaponProgressChanged(value)
    if self.battleUI ~= nil then
----        print("<color=#51438B>武器能量报告>>>>>>> SID ="..sid.."  Value = "..num.."</color>")

        self.battleUI.ChangeWeaoponProgress(value)
    end
end

function BattleController:OnLoadingBegin()
    local ui = ui_window_mgr:ShowModule("ui_loading")
    ui:SetLoadingProgress(0)
    self.loadingUI = ui
end

function BattleController:OnLoadingCompleted()
    if self.loadingUI then
        ui_window_mgr:UnloadModule("ui_loading")
        self.loadingUI = nil
    end

    self:ShowDefaultUIBattle()

    battle_manager.Play()

    --播放战斗音乐
    local music_contorller = require "music_contorller"
    music_contorller.PlayTempMusic(music_contorller.ID_BATTLE)
end

function BattleController:OnLoadingProgressChanged(progress)
    if self.loadingUI then
        self.loadingUI:SetLoadingProgress(progress)
    end

    -- todo: 翻译
    local battleType = GetBattleType()
    if not isReplay then
        if progress == 0 then
            bloadTime = os.time()
        end
        if progress == 1 then
            local loadtime = os.time() - bloadTime
            battle_manager.ReportBattleLoading(lastStageType, loadtime, lastLevel)
            -- local properties = string.format("{\"loading_time\":%d,\"battle_type\":\"%s\",\"dungeon_id\":%d}",loadtime,stageTypeStr,levelID )
            -- event.Trigger(event.GAME_EVENT_REPORT, "battle_loading", properties)
        end
    end
end

function BattleController:ShowDefaultUIVictory()
    local ui_battle_victory = require "ui_battle_victory_new"

    SetBattleRewards(ui_battle_victory, rewards)
    battle_data.rewards = {}
    SetBattleHeroes(ui_battle_victory)
    ui_battle_victory.SetShowType(battle_message.GetStateType())
    ui_battle_victory.SetBattleRecordData(battleID, opponentName)

    local battlevictory = ui_window_mgr:ShowModule("ui_battle_victory_new")
    battlevictory.onOkEvent = function()
        battlevictory.onOkEvent = nil

        self:CloseDefaultUIBattle()

        if finalizeBattle ~= nil and not battle_data.skipBattle then
            finalizeBattle(victory, true)
            finalizeBattle = nil
            event.Trigger(event.HALL_SCENE_SILDING, true)
        end
        finalizeBattle = nil
        if #finallizeCallBack > 0 then
            for i, v in ipairs(finallizeCallBack) do
                v()
                finallizeCallBack[i] = nil
            end
        end
    end
end

function BattleController:ShowDefaultUIDefeated()
end

function BattleController:ShowDefaultUIBattle()
    local playerProp = player_mgr.GetPlayerProp()
    local vipCfg = game_scheme:VipPrivilege_0(playerProp.vipLevel)
    local playerLv = player_mgr.GetPlayerLV()
    local canSpeedUp = playerLv >= game_scheme:InitBattleProp_0(131).szParam.data[0];
    local canSkip = playerLv >= game_scheme:InitBattleProp_0(132).szParam.data[0];
    local playSpeed2 = game_scheme:InitBattleProp_0(153).szParam.data[0];
    local playSPeed1 = game_scheme:InitBattleProp_0(174).szParam.data[0];

    local IBPropData4 = game_scheme:InitBattleProp_0(1217)
    local IBPropData8 = game_scheme:InitBattleProp_0(1218)

    local playSpeed4 = IBPropData4 and IBPropData4.szParam and IBPropData4.szParam.data and IBPropData4.szParam.data[0] or 360
    local playSpeed8 = IBPropData8 and IBPropData8.szParam and IBPropData8.szParam.data and IBPropData8.szParam.data[0] or 780

    
    if vipCfg ~= nil then
        canSpeedUp = canSpeedUp or vipCfg.iTwiceSpeedFlag ~= 0
        canSkip = canSkip or vipCfg.iSkipBattleFlag ~= 0
    end

    local speed1 = (playSPeed1*0.01)
    local speed2 = canSpeedUp and (playSpeed2*0.01) or speed1
    local speed4 = canSpeedUp and (playSpeed4*0.01) or speed1
    local speed8 = canSpeedUp and (playSpeed8*0.01) or speed1
    
    --clamp [minspeed, maxspeed]
    --time_scale_mgr.SetPlaySpeed(math.max(math.min(battle_manager.GetPlaySpeed(), maxspeed), minspeed))
    time_scale_mgr.SetPlaySpeed(battle_manager.GetPlaySpeed())
    local window = ui_window_mgr:ShowModule("ui_in_battle")
    if canSkipBattle ~= nil then
        canSkip = canSkipBattle() and canSkip
    end

    window:SetCanSkip(canSkip)
    window:Setpeed(speed1,1)
    window:Setpeed(speed2,2)
    window:Setpeed(speed4,3)
    window:Setpeed(speed8,4)
    window:SetStageType(battleType)
    self.battleUI = window
end

function BattleController:CloseDefaultUIBattle()
    ui_window_mgr:UnloadModule("ui_in_battle")
    if ui_window_mgr:IsModuleShown("ui_halo_tips") then
        ui_window_mgr:UnloadModule("ui_halo_tips")
    end
    if ui_window_mgr:IsModuleShown("ui_halo_detail") then
        ui_window_mgr:UnloadModule("ui_halo_detail")
    end
end

local object = require "object"
local CBattleController = class(object, nil, BattleController)

return CBattleController