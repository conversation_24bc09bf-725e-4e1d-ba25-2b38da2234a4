local string = string
local newclass = newclass
local reward_mgr = require "reward_mgr"
local log = require "log"
local slot_machine_ani_helper = require "slot_machine_ani_helper"
local slot_game_progress_item = require "slot_game_progress_item"


--老虎机进度面板
---@class slot_game_progress_panel
local slot_game_progress_panel = {}



---@public Render
---@param rtf RectTransform
---@param parent_order int
---@param data slot_game_progress_panel_data
---@param OnClickClose function
---@param OnClickProgress function
function slot_game_progress_panel.Render(rtf, parent_order,  data, OnClickClose, OnClickProgress)
    local txt_score_progress = rtf:Find("img_bg/score/txt_score_progress"):GetComponent("Text")
    txt_score_progress.text = string.format("%d<color=#ffffff><size=26>/%d</size></color>", data.score, data.total_score)

    local btn_rtclose = rtf:Find("img_bg/btn_rtclose"):GetComponent("Button")
    btn_rtclose.onClick:RemoveAllListeners()
    btn_rtclose.onClick:AddListener(function()
        OnClickClose()
    end)

    local rtf_progress_content = rtf:Find("img_bg/bar/rtf_progress_content"):GetComponent("RectTransform")
    slot_machine_ani_helper.ClearChildren(rtf_progress_content)
    local progress_prefab = rtf:Find("item_progress_prefab")
    for i = 1, #data.progress_data_list do
        local _i = i
        local progress_data = data.progress_data_list[i]
        local item_progress = CS.UnityEngine.GameObject.Instantiate(progress_prefab)
        item_progress.name = "item_progress_" .. i
        item_progress.transform:SetParent(rtf_progress_content, false)

        slot_game_progress_item.Render(item_progress:GetComponent("RectTransform"), parent_order, progress_data, function (progress_data, id, count, reward_type, _goodsItem)
            --log.Log("点击进度条", _data, id)
            OnClickProgress(_i, progress_data, id, count, reward_type, _goodsItem)
        end)
    end

    -- 进度条
    local img_progress = rtf:Find("img_bg/bar/img_progress"):GetComponent("Image")
    local count = #data.progress_data_list
    for i = 1, count do
        local progress_data = data.progress_data_list[i]
        if data.score >= progress_data.progress then
            img_progress.fillAmount = i / count
        else
            if i > 1 and data.score > data.progress_data_list[i - 1].progress then
                local _delta = data.progress_data_list[i].progress - data.progress_data_list[i - 1].progress
                local _progress = (data.score - data.progress_data_list[i - 1].progress) / _delta
                img_progress.fillAmount = img_progress.fillAmount + _progress / count
            else
                --img_progress.fillAmount = 0
            end
        end
    end
end


return slot_game_progress_panel