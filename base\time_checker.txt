local require = require
local object = require 'object'
local class = require 'class'

local DateTime = CS.System.DateTime
local start_time = DateTime.Now.Ticks
module('time_checker')

local M = {}

function M:NowTick()
    return (DateTime.Now.Ticks - start_time)/10000
end
function M:Check(key)
    local nTick = self:NowTick()
    local cache = self.cache or {}
    self.cache = cache
    if cache[key] then
        self:Tick(key)
    else
        cache[key] = nTick
    end
end
function M:Remove(key)
    self.cache = self.cache or {} 
    self.cache[key] = nil
end
function M:RemoveAll()
    self.cache = {}
end
function M:Tick(key)
    self.cache = self.cache or {} 
    local nTick = self:NowTick()

    if self.onTick and key and self.cache[key] then
        self.onTick(key,(nTick - self.cache[key]))
    end
    
end

return class(object, nil, M)