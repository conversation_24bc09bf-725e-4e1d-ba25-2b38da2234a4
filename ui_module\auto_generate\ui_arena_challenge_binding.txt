local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Toggle = CS.UnityEngine.UI.Toggle
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable


module("ui_arena_challenge_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenachallenge.prefab"

WidgetTable ={
	btn_BuyTicket = { path = "bg/changeNumShow/NumBG/btn_BuyTicket", type = Button, event_name = "OnBtnBuyTicketClickedProxy"},
	txt_TicketNum = { path = "bg/changeNumShow/NumBG/txt_TicketNum", type = Text, },
	tog_SkipBattle = { path = "bg/tog_SkipBattle", type = Toggle, value_changed_event = "OnTogSkipBattleValueChange"},
	btn_closeBtn = { path = "bg/btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	btn_refreshListBuy = { path = "bg/btn_refreshListBuy", type = Button, event_name = "OnBtnRefreshListBuyClickedProxy"},
	txt_BuyRefreshNum = { path = "bg/btn_refreshListBuy/txt_BuyRefreshNum", type = Text, },
	btn_refreshListFree = { path = "bg/btn_refreshListFree", type = Button, event_name = "OnBtnRefreshListFreeClickedProxy"},
	txt_freeRefreshNum = { path = "bg/btn_refreshListFree/txt_freeRefreshNum", type = Text, },
	srt_challengeList = { path = "bg/list/Viewport/srt_challengeList", type = ScrollRectTable, },
	txt_skipTips = { path = "bg/txt_skipTips", type = Text, },

}
