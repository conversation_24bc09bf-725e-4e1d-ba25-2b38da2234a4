local require = require
  
local util = require "util"

module("block_cache")
local blockCache = {}
function IfBlock(mark,time) 
    if blockCache[mark] then
        return true
    end
    blockCache[mark] = true

    util.DelayCall(time or 5, function ()
        blockCache[mark] = false
    end) 
    return false
end


function Finish(mark)
    if mark~= nil then
        blockCache[mark] = false
    end
    return mark and blockCache[mark]
end
