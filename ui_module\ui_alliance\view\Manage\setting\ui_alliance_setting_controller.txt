﻿--- ui_alliance_setting_controller.txt
--- Generated by Emmy<PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by .
--- DateTime: 
--- desc:    
---
local require = require
local newclass = newclass
local tonumber = tonumber
local alliance_const = require "alliance_const"
local lang = require "lang"
local util = require "util"
local module_scroll_list = require "scroll_list"
local controller_base = require "controller_base"
local alliance_pb = require "alliance_pb"
local flow_text = require "flow_text"
local event_alliance_define = require "event_alliance_define"
local windowMgr = require "ui_window_mgr"
local net_alliance_module = require "net_alliance_module"
local alliance_data = require "alliance_data"
module("ui_alliance_setting_controller")

local controller = nil
local UIController = newclass("ui_alliance_setting_controller", controller_base)
local powerValue = 0
local headOfficeValue = 0
local applySetValue = 0 --1自动入盟 2 手动入盟
local clearSetValue = alliance_pb.emAllianceClearType_rejectclear
local allianceLang = 0
local allianceFlagId = 0
local nationalFlagID = 217
local userData = {}
--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self:SubscribeEvents()
    self:InitView()
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end
function UIController:InitView()
    userData = alliance_data.GetUserAllianceData()
    if userData and util.get_len(userData) > 0 then
        self:TriggerUIEvent( "RefreshAllianceInfo", userData)
        self:RefreshFlagView(userData.flag)
        clearSetValue = userData.clearSet
        applySetValue = userData.applySet
        powerValue = userData.ceLimit
        headOfficeValue = userData.lvLimit
        nationalFlagID = userData.nationalFlagID
        self:TriggerUIEvent( "RefreshJoinWayText", applySetValue == alliance_pb.emAllianceApplyType_Auto)
    end
    self:TriggerUIEvent( "InitSlider", OnValueChanged)
    self:TriggerUIEvent( "ShowNationalFlag", nationalFlagID)
end
function OnValueChanged(value)
    clearSetValue = value == 1 and alliance_pb.emAllianceClearType_conditioned1 or alliance_pb.emAllianceClearType_rejectclear
end
function UIController:OnBtn_closeBtnClickedProxy()
    if clearSetValue ~= userData.clearSet or applySetValue ~= userData.applySet
            or powerValue ~= userData.ceLimit or headOfficeValue ~= userData.lvLimit then
        --关闭界面的时候设置参数
        net_alliance_module.MSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ(applySetValue, powerValue,
                headOfficeValue, clearSetValue)
    end
    windowMgr:UnloadModule("ui_alliance_setting")
end
function UIController:OnBtn_replaceBtnClickedProxy()
    --打开界面 传入旗帜数据
    local iconData = alliance_data.GetCurAllianceFlagList()
    windowMgr:ShowModule("ui_alliance_icon", nil, nil, iconData)
end
function UIController:OnBtn_NameBtnClickedProxy()
    windowMgr:ShowModule("ui_modify_name")
end
function UIController:OnBtn_AbbBtnClickedProxy()
    windowMgr:ShowModule("ui_modify_abb")
end
function UIController:OnBtn_LangBtnClickedProxy()
    local dataTable = { userData.language };
    windowMgr:ShowModule("ui_alliance_lang", nil, nil, dataTable);
end
function UIController:OnBtn_powerBtnClickedProxy()
    self:TriggerUIEvent( "SetPowerInputActive")
end
function UIController:OnBtn_levelBtnClickedProxy()
    self:TriggerUIEvent( "SetLevelInputActive")
end
function UIController:OnBtn_joinBtnClickedProxy()
    if applySetValue == 0 then
        applySetValue = userData.applySet
    end
    applySetValue = applySetValue == alliance_pb.emAllianceApplyType_Auto and alliance_pb.emAllianceApplyType_Apply or alliance_pb.emAllianceApplyType_Auto
    --刷新View
    self:TriggerUIEvent( "RefreshJoinWayText", applySetValue == alliance_pb.emAllianceApplyType_Auto)
end

function UIController:OnBtn_NationalFlag()
    windowMgr:ShowModule("ui_national_flag_setting",nil,nil,{selectType = 2})
end

function UIController:OnInputField_powerInputFieldEndEdit(value)

    local num = tonumber(value)
    if num then
        powerValue = num
        if powerValue > alliance_const.ALLIANCE_MAX_POWER_LIMIT then
            powerValue = alliance_const.ALLIANCE_MAX_POWER_LIMIT
            self:TriggerUIEvent( "RefreshPowerInput", powerValue)
        end
    end

end

function UIController:OnInputField_levelInputFieldEndEdit(value)
    local num = tonumber(value)
    if num then
        headOfficeValue = num
        if headOfficeValue > alliance_const.ALLIANCE_MAX_LEVEL_LIMIT then
            headOfficeValue = alliance_const.ALLIANCE_MAX_LEVEL_LIMIT
            self:TriggerUIEvent( "RefreshLevelInput", headOfficeValue)
        end
    end
end

function UIController:Close()
    powerValue = 0
    headOfficeValue = 0
    applySetValue = 0
    clearSetValue = alliance_pb.emAllianceClearType_rejectclear
    allianceLang = 0
    allianceFlagId = 0
    userData = {}
    self.__base.Close(self)
    controller = nil
end

function UIController:SubscribeEvents()
    --选着之后发送网络事件
    self.refreshLangId = function(eventName, id)
        if id and userData.language ~= id then
            net_alliance_module.MSG_ALLIANCE_MODIFY_LANGUAGE_REQ(id)
        end
    end
    self:RegisterEvent(event_alliance_define.UPDATE_ALLIANCE_SELECT_LANG, self.refreshLangId)
    --刷新语言
    self.refreshLang = function(eventName, langId)
        flow_text.Add(lang.Get(600395))
        self:RefreshLangView(langId)
    end
    self:RegisterEvent(event_alliance_define.SET_ALLIANCE_LANG_DATA, self.refreshLang)
    --刷新名称
    self.refreshName = function(eventName, data)
        --设置联盟名字或者简称
        if data.modifyType == alliance_pb.emAllianceNameType_Name then
            --设置名称
            self:TriggerUIEvent( "RefreshAllianceName", data.modifyName)
        elseif data then
            --设置简称
            self:TriggerUIEvent( "RefreshAllianceAbb", data.modifyName)
        end
    end
    self:RegisterEvent(event_alliance_define.SET_ALLIANCE_NAME_SHORT_DATA, self.refreshName)
    --选着旗帜
    self.selectFlag = function(eventName, flagId, iconId)
        if flagId and userData.flag ~= flagId then
            net_alliance_module.MSG_ALLIANCE_MODIFY_FLAG_REQ(flagId)
        end
        self:RefreshFlagView(flagId)
    end
    self:RegisterEvent(event_alliance_define.SELECT_ALLIANCE_SELECT_FLAG, self.selectFlag)
    --刷新旗帜
    self.refreshFlag = function(eventName, flagId)
        --选择的旗帜id
        self:RefreshFlagView(flagId)
    end
    self:RegisterEvent(event_alliance_define.SET_ALLIANCE_FLAG_DATA, self.refreshFlag)
    self.exitAlliance = function(eventName, data)
        windowMgr:UnloadModule(self.view_name)
    end
    self:RegisterEvent(event_alliance_define.EXIT_ALLIANCE, self.exitAlliance)
    --刷新国旗
    self.refreshNationalFlag = function(eventName, msg)
        self:TriggerUIEvent("ShowNationalFlag", msg.nationalFlagID)
    end
    self:RegisterEvent(event_alliance_define.SET_ALLIANCE_NATIONAL_FLAG_DATA, self.refreshNationalFlag)
end

--刷新旗帜
function UIController:RefreshFlagView(flagId)
    allianceFlagId = flagId
    self:TriggerUIEvent( "RefreshFlagIcon", flagId)
end
--刷新lang选着
function UIController:RefreshLangView(langId)
    allianceLang = langId
    self:TriggerUIEvent( "RefreshLang", lang.Get(allianceLang))
end

--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end

--endregion
