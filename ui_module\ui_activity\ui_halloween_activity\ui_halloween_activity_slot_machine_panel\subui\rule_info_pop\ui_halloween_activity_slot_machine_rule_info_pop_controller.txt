local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

local halloween_slot_machine_mgr = require "halloween_slot_machine_mgr"
local halloween_activity_slot_machine_data_helper = require "halloween_activity_slot_machine_data_helper"
local halloween_activity_slot_machine_const = require "halloween_activity_slot_machine_const"
local game_scheme = require("game_scheme")

--region Controller Life
module("ui_halloween_activity_slot_machine_rule_info_pop_controller")
local controller = nil
local UIController = newClass("ui_halloween_activity_slot_machine_rule_info_pop_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)    
end

function UIController:OnShow()
    self.__base.OnShow(self)


    self:ShowRewardPage()
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end
function  UIController:OnBtnReward_disClickedProxy()
    self:ShowRewardPage()
end
function  UIController:OnBtnRule_disClickedProxy()
    self:ShowRulePage()
end
function  UIController:OnBtnRuleClickedProxy()
end
function  UIController:OnBtnRewardClickedProxy()
end

function  UIController:GetActivityId()
    return halloween_slot_machine_mgr.GetActivityID()
end
function  UIController:ShowRewardPage()
    local activity_id = halloween_slot_machine_mgr.GetSlotGameActivityId()
    --RenderRewardPage(act_id, same3_data_list, same2_data_list, same0_data_list)
    local same_count_map = halloween_activity_slot_machine_data_helper.GetSepreatedSlotGameCfgIntoSameCount(activity_id)
    self:TriggerUIEvent("RenderRewardPage", activity_id, same_count_map[3], same_count_map[2], same_count_map[0])

    self:TriggerUIEvent("SelectPage", 1)
end

function  UIController:ShowRulePage()
    local activity_id = self:GetActivityId()

    local help_id = halloween_activity_slot_machine_const.help_id
    local help_string_list = halloween_activity_slot_machine_data_helper.GetRuleGroupData(help_id)

    self:TriggerUIEvent("RenderRulePage", help_string_list)
    self:TriggerUIEvent("SelectPage", 2)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
