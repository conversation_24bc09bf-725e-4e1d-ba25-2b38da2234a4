---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by linsh22022102.
--- DateTime: 2024/7/6 14:37
---
---
--用来搜索

--初始化数据
local require = require
local gw_common_util = require "gw_common_util"
local gw_sand_event_define = require "gw_sand_event_define"
local log = require "log"
local lang = require "lang"
local event_define = require "event_define"
local red_const = require "red_const"
local red_system = require "red_system"
local main_slg_common_define = require "main_slg_common_define"
local screen_util = require "screen_util"
local main_slg_const = require "main_slg_const"
local const = require "const"
local main_slg_data = require "main_slg_data"
local card_sprite_asset = require "card_sprite_asset"
local sand_ui_data = require "sand_ui_data"
local gw_ed = require("gw_ed")
local event = require "event"
local sand_ui_event_define = require "sand_ui_event_define"
local net_sandbox_module = require "net_sandbox_module"
local game_scheme = require "game_scheme"
local gw_const = require "gw_const"
local e_handler_mgr = require "e_handler_mgr"
local xpcall = xpcall
local Canvas = CS.UnityEngine.Canvas
local M = {}
local util = require "util"
local ui_window_mgr = require "ui_window_mgr"

local LeanTween = CS.LeanTween

M.uiMainName = "ui_main_slg"
M.uiMainTop = "ui_main_slg_top"
M.uiMainBottom = "ui_main_slg_bottom"
local PopWindowTable = {
    ["ShowSpeedUp"] = "ui_sand_marching_acceleration",
    ['ShowBaseOperate'] = 'ui_sand_advanced_relocation',
    ['ShowPlayerInfo'] = "ui_player_detail_info_ex",
    ['ShowHeroSelectPanel'] = "ui_new_hero_select_panel",
}

local LogWarning = function(...)
    local log = require "log"
    log.Warning("[sandbox_ui_mgr]", ...)
end

local Log = function(...)
    local log = require "log"
    log.LogFormat("[sandbox_ui_mgr]", ...)
end

local Error = function(...)
    local log = require "log"
    log.Error("[sandbox_ui_mgr]", ...)
end

local function GetDataByTopicKey(topicData, topicKey)
    return topicData[topicKey + 1]
end
local function RegisterGWMsg(msg, func)
    gw_ed.mgr:Register(msg, func)
end

local function RegisterEvent(msg, func)
    event.Register(msg, func)
end

local function ShowModule(moduleName, data, onshow, onhide)
    if ui_window_mgr:IsModuleShown(moduleName) then
        if moduleName == "ui_main_slg_top" then
            e_handler_mgr.TriggerHandler(moduleName, "ResetUILayer")
        end
        return
    end
    ui_window_mgr:ShowModule(moduleName, onshow, onhide, data)

end

local function UnloadModule(moduleName, data, bDealHide)

    ui_window_mgr:UnloadModule(moduleName, bDealHide, data)
end






--region 生命周期
local mainSubModule = {
    "ui_main_slg_top",
    "ui_main_slg_bottom",
}

local mainAllModule = {
    [1] = M.uiMainName,
    [2] = M.uiMainTop,
    [3] = M.uiMainBottom,
}

local sandOnlyMainSubModules = {
    "ui_sand_marching_team",
    "ui_sand_back_to_base",
}
-- 在主页面之后进行实例化
local BehindMainSlgModules = {
    "ui_sand_box_monsters_approching",
    "ui_monsters_approching_scroller"
}
--沙漠风暴额外打开的UI
local stormOnlyMainSubModules = {
    --"ui_storm_mini_map",--小地图
}
function M.Init()

    if M.isRegist then
        return
    end

    M.isRegist = true

    if const.USE_MAIN_SLG then
        const.uiMainName = M.uiMainName
        M.RegisterMsgs()
        local cacheUILevel = 1
        for _, tmpModule in ipairs(mainSubModule) do
            M.InitModuleByName(tmpModule, cacheUILevel)
        end
        for _, tmpModule in ipairs(sandOnlyMainSubModules) do
            M.InitModuleByName(tmpModule, cacheUILevel, true)
        end
        M.InitModuleByName(M.uiMainName, cacheUILevel)
        for _,tmpModule in ipairs(BehindMainSlgModules) do 
            M.InitModuleByName(tmpModule, cacheUILevel)
        end
        ui_window_mgr.SetDefaultFullScreenModules("new_hook_scene", true)
        M.RemoveOpenList()
    end
end

function M.RemoveOpenList()
    for uiName, v in pairs(const.oldMainUI) do
        for k1, uiList in pairs(ui_window_mgr.GetOpenList()) do
            table.remove_value(uiList, uiName)
        end
    end
end

function M.InitModuleByName(moduleName, cacheUILevel, excludeHome)
    gw_const.ESEnterCullLua[moduleName] = true
    if not excludeHome then
        gw_const.EHomeEnterCullLua[moduleName] = true
    end
    ui_window_mgr.SetCacheUI(moduleName, cacheUILevel)
    ui_window_mgr.SetDefaultUILayer(moduleName, const.UI_LAYER_SCENE_UI)
end

--自动注册服务器的消息 gw_ed.GW_SAND_NET_EVENT
function M.OnSandNetEvent(msgName, funcName, ...)
    if funcName and M[funcName] then
        M[funcName](...)
    end
end



--监听事件打开各种UI
function M.RegisterMsgs()
    RegisterGWMsg(gw_ed.GW_SAND_NET_EVENT, M.OnSandNetEvent)
    RegisterGWMsg(gw_ed.GW_SAND_EXIT_SCENE, M.GW_SAND_EXIT_SCENE)
    RegisterGWMsg(gw_ed.GW_SWITCH_SCENE_TO_SAND, M.OpenSandSceneSwitch)
    RegisterGWMsg(gw_ed.GW_SWITCH_SCENE_TO_HOME, M.OpenCitySceneSwitch)
    --RegisterGWMsg(gw_ed.GW_SAND_SERVER_STATE_EVENT, M.OnServerStateChange)
    RegisterGWMsg(gw_ed.GW_HOME_NET_EVENT, M.BuildMainUpgrade)

    RegisterEvent(sand_ui_event_define.GW_OPEN_SAND_MAIN, M.GW_OPEN_SAND_MAIN)
    RegisterEvent(sand_ui_event_define.GW_CLOSE_SAND_MAIN_NO_TOP, M.GW_CLOSE_SAND_MAIN_NO_TOP)
    RegisterEvent(sand_ui_event_define.GW_CLOSE_SAND_MAIN, M.GW_CLOSE_SAND_MAIN)
    RegisterEvent(sand_ui_event_define.GW_MAIN_REFRESH_BUTTON, M.GW_MAIN_REFRESH_BUTTON)

    RegisterEvent(sand_ui_event_define.GW_SAND_SCENE_CHANGE, M.GW_SAND_SCENE_CHANGE)
    RegisterEvent(event.UI_MODULE_CLOSE, M.UI_MODULE_CLOSE)
    RegisterEvent(event.UI_MODULE_SHOW, M.UI_MODULE_SHOW)
    RegisterEvent(sand_ui_event_define.GW_EXIT_EXPERIENCE, M.GW_EXIT_EXPERIENCE)
    RegisterEvent(event.LANGUAGE_SETTING_CHANGED, M.OnLanguageSettingChanged)
    RegisterEvent(event.USER_DATA_RESET, M.OnUserDataReset)
    RegisterEvent(event.SCREEN_SIZE_CHANGED, M.SCREEN_SIZE_CHANGED)
    RegisterEvent(sand_ui_event_define.GW_MAIN_CHAT_BUBBLE_REFRESH, M.GW_MAIN_CHAT_BUBBLE_REFRESH)

    RegisterEvent(event.GW_SCENE_CHANGE_SUCCESS, M.GW_SCENE_CHANGE_SUCCESS)
    RegisterEvent(event.NEW_BATTLE_START, M.HiddenScene)
    RegisterEvent(event.NEW_XYX_START_LEVEL, M.HiddenScene)
    RegisterEvent(event.FIRST_LOGIN_CREATE_DATA_FINISH, M.FIRST_LOGIN_CREATE_DATA_FINISH)

    -- 沙盘事件
    RegisterEvent(gw_sand_event_define.GW_SAND_UPDATE_SELF_DATA, M.OnServerStateChange)
    RegisterEvent(gw_sand_event_define.GW_SAND_DISPOSE_SELF_DATA, M.OnServerStateChange)
    RegisterEvent(gw_sand_event_define.GW_SAND_FUNCTION_TRIGGER, M.OnServerStateChange)

    red_system.RegisterRedFunc(red_const.Enum.ExperienceMain_Item, M.ExperienceMain_Item)--主界面底部图标红点
    red_system.RegisterRedFunc(red_const.Enum.ExperienceItem_New, M.ExperienceItem_New)--主界面底部图标红点
    red_system.RegisterRedFunc(red_const.Enum.MiniGame_Main_Icon, M.MiniGame_Main_Icon)--主界面底部小游戏图标红点
    -- RegisterGWMsg(gw_ed.GW_HOME_EVENT_UPDATE,M.GWMainCarriageEnter)
    red_system.RegisterRedFunc(red_const.Enum.Questionnaire, M.GetQuestionnaireRed)
    red_system.RegisterRedFunc(red_const.Enum.DownLoadRed, M.GetDownLoadRed)
    red_system.RegisterRedFunc(red_const.Enum.ChangePackageRed, M.GetChangePackageRed)
end
---@public 设置主界面显示类型 0/1  0是默认刷新所有逻辑  1是纯重新展示ui
function M.SetUIMainSlgShowType(type)
    M.ui_main_slg_show_type =  type
end
function M.GetUIMainSlgShowType()
    return M.ui_main_slg_show_type or 0
end

--主界面底部小游戏图标红点
function M.MiniGame_Main_Icon()
    local gw_ab_test_mgr = require "gw_ab_test_mgr"
    local buildType = gw_ab_test_mgr.GetHookLevelType()
    if buildType == 1 then
        local function_open_mgr = require "function_open_mgr"
        local isOpen = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.MainMiniGame)
        if not isOpen then
            return false
        end
    end
    local gw_independent_game_mgr = require "gw_independent_game_mgr"
    return gw_independent_game_mgr.IsShowRedDot()
end

--主界面底部图标红点
function M.ExperienceMain_Item(id)
    if id == main_slg_const.MainExperimentType.CampTrial then
        local ui_camp_trial_mgr = require "ui_camp_trial_mgr"
        return ui_camp_trial_mgr.CampTrial_ItemRed()
    elseif id == main_slg_const.MainExperimentType.Arena_New then
        local arena_data = require "arena_data"
        return arena_data.GetAreaNewRedPoint()
    elseif id == main_slg_const.MainExperimentType.Arena_Weekend then
        local weekend_arena_mgr = require "weekend_arena_mgr"
        return weekend_arena_mgr.GetWeekendAreaRedPoint()
    elseif id == main_slg_const.MainExperimentType.Arena_3v3 then
        local legend_championships_mgr = require "legend_championships_mgr"
        return legend_championships_mgr.Get3V3AreaRedPoint()
    end
end

--主界面New红点
function M.ExperienceItem_New(id)
    local arena_data = require "arena_data"
    return arena_data.GetArenaNewRed(id)
end

---@public 问卷调查红点
function M.GetQuestionnaireRed()
    --目前是默认显示
    return 1
end

---@public 下载资源显示红点
function M.GetDownLoadRed()
    --目前是默认显示
    return 1
end

---@public 换包好礼红点
function M.GetChangePackageRed()
    local change_package_mgr = require "change_package_mgr"
    local redCount = change_package_mgr.GetRedCount()
    return redCount
end

local isInitScene = false
local isNotNetLogin = false
function M.InitScene()
    M.Init()
    M.originTop = nil
    M.originBottom = nil
    M.isClosing = nil
    isNotNetLogin = true
    main_slg_data.SetCurSceneType(gw_const.ESceneType.Home)
    event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN, gw_const.ESceneType.Home)
    --第一次进入主界面
    event.Trigger(event_define.FIRST_ENTER_MAIN)

    isInitScene = true
    main_slg_data.InitChatBubbleData()
end

function M.OnUserDataReset()
    isInitScene = false
    isNotNetLogin = false
    main_slg_data.Init()
    --初始化聊天气泡

end

local serverStateUpdateEvent = {
    ["EnterSceneSandBox"] = true,
    ["ExitSandBox"] = true,
}

local serverStateUpdateEventByEventName = {
    [gw_sand_event_define.GW_SAND_UPDATE_SELF_DATA] = true,
    [gw_sand_event_define.GW_SAND_DISPOSE_SELF_DATA] = true,
}

function M.OnServerStateChange(eventName, sandEventName)
    local isUpdateEvent = (eventName and serverStateUpdateEventByEventName[eventName])
            or (sandEventName and serverStateUpdateEvent[sandEventName])

    if not isUpdateEvent then
        return
    end

    local gw_sand_data = require "gw_sand_data"
    local isCrossState = gw_sand_data.selfData.IsCrossServiceState()
    if isCrossState then
        ui_window_mgr:ShowModule("ui_sand_box_back_tips")
    else
        if ui_window_mgr:IsModuleShown("ui_sand_box_back_tips") then
            ui_window_mgr:UnloadModule("ui_sand_box_back_tips")
        end
    end
end

function M.GetZoneSandBoxSid()
    local zone_sid = gw_common_util.GetSandZoneSandBoxSid()
    return zone_sid or 0
end

--region 主界面打开关闭
function M.EnterBattleState(state)
    M.battleStart = state
    if state then
        --event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
    end
end



--屏蔽主界面打开，持续0.2秒
function M.BlockOpenMain(callback)
    M.blockOpenMain = true
    if M.cancelBlockOpenMain then
        util.RemoveDelayCall(M.cancelBlockOpenMain)
    end
    M.cancelBlockOpenMain = util.DelayCallOnce(const.blockMainOpenTime, function()
        M.blockOpenMain = false
        M.cancelBlockOpenMain = nil
        if callback then
            callback()
        end
    end)
end

--打开主界面
function M.GW_OPEN_SAND_MAIN(eventName, sceneState)
    --打开主界面
    --local GWSandMgr = require "gw_sand_mgr"
    --if ui_window_mgr:IsModuleShown(M.uiMainName) then
    --    return
    --end
   
    --在登录界面 不显示主界面
    --LogWarning("打开主界面")
    if not isNotNetLogin then
        return
    end
    --if ui_window_mgr:IsModuleShown("ui_main_slg_top") then
    --    e_handler_mgr.TriggerHandler("ui_main_slg_top", "ResetUILayer")
    --end

    if M.battleStart then
        return
    end

    if M.blockOpenMain then
        return
    end
    M.isClosing = false
    
    local sceneType = sceneState or GWMgr.curScene
    if sceneType == gw_const.ESceneType.Loading or sceneType == gw_const.ESceneType.None then
        return
    end

    GWG.GWMgr.ShowCurScene()
    main_slg_data.SetMainState(main_slg_const.MainShowState.OpenAll)

    for _, name in ipairs(mainSubModule) do
        M.TweenToShow(name)
        if name == "ui_main_slg_top" and ui_window_mgr:IsModuleShown("ui_main_slg_top") then
            e_handler_mgr.TriggerHandler(name, "ResetUILayer")
        end
    end
    for _,name in ipairs(BehindMainSlgModules) do
        M.TweenToShow(name)
    end
    M.TweenToShow(M.uiMainName)
    
    if main_slg_data.GetIsSandScene() then
        for _, name in ipairs(sandOnlyMainSubModules) do
            M.TweenToShow(name)
        end
    end
end

---跳过关闭自动打开主界面配置
local _skipCloseAutoShowMainUI = {}
function M.AddSkipCloseAutoShowMainUI(moduleName)
    --_skipCloseAutoShowMainUI[moduleName] = true
end
function M.RemoveSkipCloseAutoShowMainUI(moduleName)
    --_skipCloseAutoShowMainUI[moduleName] = nil
end

---@description 监听关闭UI的事件，自动打开主界面
function M.UI_MODULE_CLOSE(_, moduleName)
    --触发一下等待执行的方法
    event.DelayTrigger(event_define.EVENT_COMMON_TRIGGER_WAIT_FUNC, moduleName)
    -- if main_slg_data.showState == main_slg_const.MainShowState.OpenAll then
    --     return
    -- end
    -- if not isNotNetLogin then
    --     return
    -- end
    -- if not isInitScene then
    --     return
    -- end
    -- --如果是在试炼中，不自动打开主界面
    -- if main_slg_data.IsInExperiment() then
    --     return
    -- end

    -- if _skipCloseAutoShowMainUI[moduleName] then
    --     M.RemoveSkipCloseAutoShowMainUI(moduleName)
    --     return
    -- end

    -- if main_slg_common_define.autoOpenMainUIConfig[moduleName] then
    --     M.GW_OPEN_SAND_MAIN()
    -- end
    -- if main_slg_common_define.autoOpenMainUIConfigWithChecking[moduleName] and main_slg_common_define.autoOpenMainUIConfigWithChecking[moduleName].checkFunc() then
    --     M.GW_OPEN_SAND_MAIN()
    -- end
end

--跳过打开自动关闭主界面配置
local _skipOpenAutoCloseMainUI = {}
function M.AddSkipOpenAutoCloseMainUI(moduleName)
    _skipOpenAutoCloseMainUI[moduleName] = true
end
function M.RemoveSkipOpenAutoCloseMainUI(moduleName)
    _skipOpenAutoCloseMainUI[moduleName] = nil
end

---@description 监听打开UI的事件，自动关闭主界面
function M.UI_MODULE_SHOW(_, moduleName)
    -- if not isInitScene then
    --     return
    -- end
    -- if _skipOpenAutoCloseMainUI[moduleName] then
    --     M.RemoveSkipOpenAutoCloseMainUI(moduleName)
    --     return
    -- end
    -- if main_slg_common_define.autoCloseMainUINoTopConfig[moduleName] then
    --     M.GW_CLOSE_SAND_MAIN_NO_TOP()
    -- end
    -- if main_slg_common_define.autoCloseMainUINoTopSetLayerConfig[moduleName] then
    --     M.GW_CLOSE_SAND_MAIN_WITHOUT_TOP_AND_SET_LAYER(moduleName)
    -- end

    -- if main_slg_common_define.autoCloseMainUIConfig[moduleName] then
    --     M.GW_CLOSE_SAND_MAIN()
    -- end
end

--ui_window_mgr调用打开ui_lobby时映射到该方法
function M.CommonShowModule()
    ShowModule(M.uiMainName)
end

---@description 判断是否是带动画的主界面
function M.IsTweenMainUI(name)
    return name == "ui_main_slg"
            or name == "ui_main_slg_bottom"
            or name == "ui_main_slg_top"
            or name == "ui_sand_marching_team"
            or name == "ui_sand_box_monsters_approching" 
            or name == "ui_monsters_approching_scroller" 
end

function M.TweenToShow(name)
    if M.IsTweenMainUI(name) then
        if not ui_window_mgr:IsModuleShown(name) then
            ShowModule(name)
        else
            e_handler_mgr.TriggerHandler(name, "TweenToShow")
        end
    else
        ShowModule(name)
    end
end

function M.TweenToClose(name)
    if M.IsTweenMainUI(name) then
        if ui_window_mgr:IsModuleShown(name) then
            e_handler_mgr.TriggerHandler(name, "TweenToClose")
        end
    else
        UnloadModule(name)
    end
end



function M.CloseMain(skipTop, setLayer, UIName)
    --LogWarning("关闭主界面")
    if M.IsMainUIOnlyShown(M.uiMainTop) and skipTop then
        if UIName then
            e_handler_mgr.TriggerHandler(M.uiMainTop, "SetUILayerOnTop", UIName)
        end
        return
    end
    if M.IsMainUIOnlyShown(M.uiMainTop) and not skipTop then
        UnloadModule(M.uiMainTop)
        return
    end
    if M.isClosing then
        return
    end
    M.isClosing = true
    
    if ui_window_mgr:IsModuleShown(M.uiMainTop) then
        M.TweenToClose(M.uiMainName)
        for _, name in ipairs(mainSubModule) do
            local skip = skipTop and name == M.uiMainTop or false
            if not skip then
                M.TweenToClose(name)
            else
                if skipTop and name == M.uiMainTop and setLayer then
                    e_handler_mgr.TriggerHandler(name, "SetUILayerOnTop", UIName)
                end
            end
        end
        for _, name in ipairs(BehindMainSlgModules) do
            M.TweenToClose(name)
        end
        for _, name in ipairs(sandOnlyMainSubModules) do
            M.TweenToClose(name)
        end
    end
end

function M.IsMainUIOnlyShown(name)
    local isOnlyShow = ui_window_mgr:IsModuleShown(name)

    if isOnlyShow then
        for i, v in ipairs(mainAllModule) do
            if v and v ~= name and ui_window_mgr:IsModuleShown(v) then
                isOnlyShow = false
                break
            end
        end
    end
    return isOnlyShow
end

--关闭主界面，但保留顶部UI
function M.GW_CLOSE_SAND_MAIN_NO_TOP()
    M.CloseMain(true)
    main_slg_data.SetMainState(main_slg_const.MainShowState.TopRemain)
end
--关闭主界面，但保留顶部UI同时让顶部UI在最前。
function M.GW_CLOSE_SAND_MAIN_WITHOUT_TOP_AND_SET_LAYER(UIName)
    M.CloseMain(true, true, UIName)
    main_slg_data.SetMainState(main_slg_const.MainShowState.TopRemain)
end

--关闭主界面
function M.GW_CLOSE_SAND_MAIN()
    --关闭主界面
    M.CloseMain()
    main_slg_data.SetMainState(main_slg_const.MainShowState.CloseAll)
end

--endregion

--region 沙盘切换逻辑

--在城市场景点击沙盘切换按钮
--@param func function 切换完成回调
function M.OpenSandSceneSwitch(_, func, position, sandboxSid)
    local gw_common_util = require "gw_common_util"
    gw_common_util.SwitchToSand(func, position, sandboxSid)
end

--在沙盘场景点击城市切换按钮
--@param func function 切换完成回调
function M.OpenCitySceneSwitch(_, func)
    local gw_common_util = require "gw_common_util"
    gw_common_util.SwitchToHome(func)
end

--离开沙盘场景
function M.GW_SAND_EXIT_SCENE(msg)
    LogWarning("离开沙盘场景")
    main_slg_data.SetCurSceneType(gw_const.ESceneType.Home)
    local gw_home_mgr = require "gw_home_mgr"
    gw_home_mgr.EnterHomeScene()
end

local arenaList = {
    [1] = main_slg_const.MainExperimentType.Arena_Weekend,
    [2] = main_slg_const.MainExperimentType.Arena_3v3,
    [3] = main_slg_const.MainExperimentType.Arena_Storm
}

--退出试炼时，打开试炼界面
function M.GW_EXIT_EXPERIENCE(_, experimentType)
    local isShow = false
    for i, v in ipairs(arenaList) do
        if experimentType == v then
            isShow = true
            break
        end
    end
    if not isShow then
        isShow = experimentType == main_slg_data.GetCurExperimentRecord()
    end
    --[[if experimentType == arenaList[1] or experimentType == arenaList[2] then
        isShow = main_slg_data.GetCurExperimentRecord() == arenaList[1] or main_slg_data.GetCurExperimentRecord() == arenaList[2]
    else
        isShow = experimentType == main_slg_data.GetCurExperimentRecord()
    end]]
    if isShow then
        ShowModule("ui_experiment")
    end
end

--场景切换时要做的事情
function M.GW_SAND_SCENE_CHANGE()

    --如果是沙盘界面需要打开沙盘独有的界面
    if main_slg_data.GetIsSandScene() then
        for _, name in ipairs(sandOnlyMainSubModules) do
            ShowModule(name)
        end
    end
    local gw_common_util = require "gw_common_util"
    if gw_common_util.CheckInSand_Storm() then
        for _, name in ipairs(stormOnlyMainSubModules) do
            ShowModule(name)
        end
    end
    --更新动态按钮,暂时只更新前五个
    --for i = 1, 5 do
    --    local buttonData = main_slg_data.GetButtonDataByType(i)
    --    main_slg_data.ResetButtonData(buttonData)
    --    event.Trigger(sand_ui_event_define.GW_MAIN_BUTTONS_REFRESH, i)
    --end
    M.GW_REFRESH_ALL_BUTTONS() --刷新所有按钮
end


--刷新所有按钮
function M.GW_REFRESH_ALL_BUTTONS()
    for k, v in pairs(main_slg_const.MainButtonGroupType) do
        local buttonData = main_slg_data.GetButtonDataByType(v)
        main_slg_data.ResetButtonData(buttonData)
        event.Trigger(sand_ui_event_define.GW_MAIN_BUTTONS_REFRESH, v)
    end
end

---进入沙盘返回服务器id跟位置
function M.OnEnterSandWithServerData(msg)
    if msg.errCode ~= 0 and msg.errorCode ~= 0 then
        LogWarning("进入沙盘场景失败", msg.errCode)
        --flow_text.Add(lang.Get(100000 + msg.errCode))
    else
        if sand_ui_data.GetIsSandBoxScene() then
            return
        end
        LogWarning("进入沙盘场景")
        --判断是否有RSP回调
        if M.onEnterSandServerRsp then
            M.onEnterSandServerRsp()
        end
        main_slg_data.SetCurSceneType(gw_const.ESceneType.Sand)
        if not GWG.GWMgr.openSceneFsm then
            event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN, gw_const.ESceneType.Sand)
            event.Trigger(sand_ui_event_define.GW_SAND_SCENE_CHANGE)
        end
    end
end

--endregion

--region 预下载按钮

---@public 获取下载按钮是否显示
function M.GetDownLoadBtnIsShow()
    local isShow = false
    local gray_load_mgr = require "gray_load_mgr"
    local loadSize, total, start_or_pause = gray_load_mgr.GetLoadParam()
    local bFinishLoad = total > 0 and loadSize >= total
    local preloadType = gray_load_mgr.IsCurGrayLoadPreloadType()
    local param = gray_load_mgr.GetGrayLoadParam()
    local grayBtnNotCloseOnFin = param.grayBtnNotCloseOnFin

    local bGrayLoad = total > 0 and loadSize < total
    isShow = bGrayLoad
    --是否是预下载
    if preloadType then
        --如果是预下载类型
        if bFinishLoad then
            local active = grayBtnNotCloseOnFin and true or false
            isShow = active
        end
    end

    return isShow
end

--endregion

M.RefreshDic = {}


--语言切换
function M.OnLanguageSettingChanged()
    --刷新所有动态按钮
    M.GW_REFRESH_ALL_BUTTONS()
end

--合并按钮刷新
function M.GW_MAIN_REFRESH_BUTTON(_, buttonType)
    local buttonData = main_slg_data.GetButtonDataByType(buttonType)
    if not buttonData then
        return
    end

    --初始化组类型刷新方法
    if not M.RefreshDic[buttonData.groupType] then
        local refreshData = {}
        local groupType = buttonData.groupType
        refreshData.refreshFunc = function()
            main_slg_data.ResetButtonData(buttonData)
            event.Trigger(sand_ui_event_define.GW_MAIN_BUTTONS_REFRESH, groupType)
            refreshData.isRefreshing = false
        end
        M.RefreshDic[buttonData.groupType] = refreshData
    end

    local refreshData = M.RefreshDic[buttonData.groupType]
    --判断是否正在刷新，如果正在刷新就不刷新
    if refreshData.isRefreshing then
        return
    end
    --0.1秒后刷新
    refreshData.isRefreshing = true
    util.DelayCallOnce(0.1, refreshData.refreshFunc)

end

function M.GWMainCarriageEnter()
    local buttonData = main_slg_data.GetButtonDataByType(main_slg_const.MainButtonType.vehicle)
    main_slg_data.ResetButtonData(buttonData)
    --event.Trigger(sand_ui_event_define.GW_MAIN_BUTTONS_REFRESH, main_slg_const.MainButtonType.vehicle)
end

---@public GW_MAIN_CHAT_BUBBLE_REFRESH 聊天气泡刷新事件
---@param chatBubbleType string 聊天气泡类型
function M.GW_MAIN_CHAT_BUBBLE_REFRESH(_, chatBubbleData)
    main_slg_data.JudgeChatBubbleIsShow(chatBubbleData)
end

function M.FIRST_LOGIN_CREATE_DATA_FINISH()
   M.GW_REFRESH_ALL_BUTTONS()
end

---@public GW_SCENE_CHANGE_SUCCESS 场景切换成功事件
---@param sceneType string 场景类型
function M.GW_SCENE_CHANGE_SUCCESS(_, sceneType)
    if sceneType == gw_const.ESceneType.Sand then
        event.Trigger(sand_ui_event_define.GW_SAND_SCENE_CHANGE)
    elseif sceneType == gw_const.ESceneType.Home then
        event.Trigger(sand_ui_event_define.GW_SAND_SCENE_CHANGE)
        event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)
    elseif sceneType == gw_const.ESceneType.Truck then
        event.Trigger(sand_ui_event_define.GW_SAND_SCENE_CHANGE)
        event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)
    elseif sceneType == gw_const.ESceneType.Storm then
        event.Trigger(sand_ui_event_define.GW_SAND_SCENE_CHANGE)
    end
end

--主界面读取icon
function M.LoadMainIcon(icon, iconName)
    M.MainSpriteAsset = M.MainSpriteAsset or card_sprite_asset.CreateGWMainAsset()
    M.MainSpriteAsset:GetSpriteSafely(icon, iconName)
end

--主界面读取maskIcon
function M.LoadMainMaskIcon(icon, iconName, maskType)
    if maskType == nil or maskType == main_slg_const.MainMaskIconType.None then
        M.LoadMainIcon(icon, iconName)
        return
    end
    if maskType == main_slg_const.MainMaskIconType.soldier then
        M.MainSoldierSpriteAsset = M.MainSoldierSpriteAsset or card_sprite_asset.CreateSoldierHeadAsset()
        M.MainSoldierSpriteAsset:GetSpriteSafely(icon, iconName)
        return
    end
end

--region 动画

--根据锚点计算实际本地坐标
local function ConvertAnchorToLocalPosition(rectTransform, anchorPosition)

    if UIUtil.ConvertAnchorToLocalPosition then
        return UIUtil.ConvertAnchorToLocalPosition(rectTransform, anchorPosition)
    end


    -- 获取父对象的 RectTransform

    if not rectTransform then
        print("RectTransform is null, unable to convert coordinate.")
        return Vector3.zero
    end

    local parentRectTransform = rectTransform.parent

    if not parentRectTransform then
        print("Parent RectTransform is null, unable to convert coordinate.")
        return Vector3.zero
    end

    -- 计算实际位置
    local parentSize = parentRectTransform.rect.size
    local anchorMin = rectTransform.anchorMin
    local anchorMax = rectTransform.anchorMax

    local pivot = rectTransform.pivot

    -- 计算锚点偏移量
    local anchorOffsetMin = Vector2(parentSize.x * anchorMin.x, parentSize.y * anchorMin.y)
    local anchorOffsetMax = Vector2(parentSize.x * anchorMax.x, parentSize.y * anchorMax.y)
    local anchorCenter = (anchorOffsetMin + anchorOffsetMax) * 0.5

    -- 计算 local position
    local adjustedLocalPosition = anchorPosition - Vector2(pivot.x * parentSize.x, pivot.y * parentSize.y) + anchorCenter

    return adjustedLocalPosition
end

function M.SCREEN_SIZE_CHANGED()
    M.originTop = nil
    M.originBottom = nil
end

function M.MoveTweenXAuto(trans, isLeft, isShow, onfinish)
    if not trans then
        if onfinish then
            onfinish()
        end
        return
    end

    local status = xpcall(function()
        local from = 0
        local to = 0
        local offset = 200

        local symbol = isLeft and -1 or 1
        local origin = ConvertAnchorToLocalPosition(trans, Vector2(360 * symbol * -1, 0)).x
        offset = screen_util.GetCanvasWithMesh().sizeDelta.x * 0.5 --x轴的一半，保证能够移动到屏幕外
        if isShow then
            from = origin + offset * symbol
            to = origin
        else
            from = origin
            to = origin + offset * symbol
        end
        local tween = M.MoveTweenX(trans, from, to, 0.3)
        if onfinish then
            tween:setOnComplete(function()
                onfinish()
            end)
        end
        return tween
    end, function(err)
        Error(err)
        if onfinish then
            onfinish()
        end
    end)


end

function M.MoveTweenYAuto(trans, isTop, isShow, onfinish)
    if not trans then
        if onfinish then
            onfinish()
        end
        return
    end

    local status = xpcall(function()
        local from = 0
        local to = 0
        local offset = 200
        local canvas = trans:GetComponentInParent(typeof(Canvas))
        local canvasParent = canvas and canvas.transform or nil
        if isTop then
            M.originTop = M.originTop or screen_util.GetCanvasWithMesh().sizeDelta.y * 0.5 + ((not util.IsObjNull(canvasParent)) and canvasParent.localPosition.y or 0)
        else
            M.originBottom = M.originBottom or screen_util.GetCanvasWithMesh().sizeDelta.y * 0.5
        end
        local origin = isTop and M.originTop or M.originBottom
        local symbol = isTop and 1 or -1
        offset = math.abs(origin) / 2 --y轴的一半，保证能够移动到屏幕外

        if isShow then

            from = (origin + offset) * symbol
            to = origin * symbol
        else
            from = origin * symbol
            to = (origin + offset) * symbol
        end
        local tween = M.MoveTweenY(trans, from, to, 0.3)
        if onfinish then
            tween:setOnComplete(function()
                onfinish()
            end)
        end
        return tween
    end, function(err)
        Error(err)
        if onfinish then
            onfinish()
        end
    end)


end

function M.MoveTweenX(trans, from, to, duration)

    LeanTween.cancel(trans)
    LeanTween.moveLocalX(trans.gameObject, from, 0)
    return LeanTween.moveLocalX(trans.gameObject, to, duration)
end

function M.MoveTweenY(trans, from, to, duration)
    LeanTween.cancel(trans)
    LeanTween.moveLocalY(trans.gameObject, from, 0)
    return LeanTween.moveLocalY(trans.gameObject, to, duration)
end

--endregion
--region 处理主界面的部分ItemIcon的位置  （1资源栏食物 2资源栏铁矿 3资源栏金币 4背包 5英雄）
local mainResourceItemTransforms = {
}
local mainButtonTransforms = {
}

function M.GetButtonTransforms()
    return mainButtonTransforms
end

---@public 设置主界面对应资源栏item的 transform
function M.SetResourceItemTransform(type, tf)
    if not mainResourceItemTransforms then
        mainResourceItemTransforms = {}
    end
    mainResourceItemTransforms[type] = tf
end
---@public 获取主界面对应资源栏item的 transform
----@param id number 传入对应的资源item的IconId
function M.GetResourceItemTransformByID(id)
    --读取Item图标
    local itemCfg = game_scheme:Item_0(id)
    if not itemCfg then
        Error("GetResourceItemTransformByID 传入的 itemCfg not exist id=", id)
        return
    end
    local targetIconId = itemCfg.icon
    if not mainResourceItemTransforms[targetIconId] then
        Error("找不到对应的顶部资源Item type的缓存transform  id =", id)
    end
    return mainResourceItemTransforms[targetIconId]
end

----对主界面由main_slg_const.MainButtonGroupType+MainButtonType统一管理的按钮进行获取
---@public 设置主界面对按钮的 transform
function M.SetMainButtonTransform(buttonGroupType, mainButtonType, tf)
    if not mainButtonTransforms then
        mainButtonTransforms = {}
    end
    if not mainButtonTransforms[buttonGroupType] then
        mainButtonTransforms[buttonGroupType] = {}
    end
    mainButtonTransforms[buttonGroupType][mainButtonType] = tf
end

function M.ResetMainButtonTransform(buttonGroupType)
    if not mainButtonTransforms then
        mainButtonTransforms = {}
    end
    if not mainButtonTransforms[buttonGroupType] then
        mainButtonTransforms[buttonGroupType] = {}
    end
    mainButtonTransforms[buttonGroupType] = { }
end

---@public 获取主界面动态按钮的 transform
---@param buttonGroupType number 目标按所在的组  main_slg_const.MainButtonGroupType
---@param mainButtonType string 目标按钮的按钮类型 main_slg_const.MainButtonType
---@param ignoreLog boolean 忽略log
function M.GetMainButtonTransformByType(buttonGroupType, mainButtonType, ignoreLog)
    if not mainButtonTransforms[buttonGroupType] then
        if not ignoreLog then
            Error("找不到对应的GetMainButtonTransformByType 缓存的transform  buttonGroupType =", buttonGroupType)
        end
        return
    end
    local tf = mainButtonTransforms[buttonGroupType][mainButtonType]
    if not tf then
        if not ignoreLog then
            Error("找不到对应的GetMainButtonTransformByType 缓存的transform  buttonGroupType =", buttonGroupType, "mainButtonType=", mainButtonType)
        end
        return
    end
    return tf
end

---@public 获取主界面动态活动入口按钮的 transform
---@param entranceId number 活动入口id  对应配置在activityMain.csv中的表entranceId/AtyEntrance2
---@param isQuickEntrance boolean 是否是快捷入口  如果entranceId传的是AtyEntrance2 则必须true
---@param ignoreLog boolean 忽略log
function M.GetMainActivityEntranceButtonTransform(entranceId, isQuickEntrance, ignoreLog)
    local buttonGroupType = main_slg_const.MainButtonGroupType.RightTop
    if not mainButtonTransforms[buttonGroupType] then
        if not ignoreLog then
            Error("找不到对应的GetMainButtonTransformByType 缓存的transform  buttonGroupType =", buttonGroupType)
        end
        return
    end
    local mainButtonType = entranceId
    if isQuickEntrance then
        mainButtonType = entranceId + main_slg_common_define.ActivityQuickEntranceOffset
    end
    local tf = mainButtonTransforms[buttonGroupType][mainButtonType]
    if not tf then
        if not ignoreLog then
            Error("查找活动入口时找不到对应的GetMainButtonTransformByType 缓存的transform  buttonGroupType =", buttonGroupType, "mainButtonType=", mainButtonType)
        end
        return
    end
    return tf
end

--通过活动ID获取活动入口transform
function M.GetMainActivityEntranceButtonTransformByActivityId(activityID)
    local festival_activity_cfg = require "festival_activity_cfg"
    local activityCfg = festival_activity_cfg.GetActivityCfgByActivityID(activityID)
    if activityCfg then
        return M.GetMainActivityEntranceButtonTransform(activityCfg.AtyEntrance)
    end
    return nil
end

--endregion

---@public 大本升级事件
function M.BuildMainUpgrade()
    --触发一次刷新下载按钮
    event.Trigger(event.RefreshMain_Download)
end

---@public 隐藏当前场景
function M.HiddenScene()
    if ui_window_mgr:IsModuleShown("ui_bs_operate") then
        ui_window_mgr:UnloadModule("ui_bs_operate")
    end
    GWG.GWMgr.HideCurScene()
end

function M.NetCloseReturnLogin()
    isNotNetLogin = false
end

function M.Dispose()
    mainResourceItemTransforms = nil
    mainButtonTransforms = nil
end
--由register_init_module初始化
--M.Init()
local closeMainMap = {}
--延迟打开主界面，关闭界面的时候需要打开主界面
function M.DelayOpenMainUI(uiModuleName)
    if uiModuleName then
        closeMainMap[uiModuleName] = nil
        local ui_window_mgr = require "ui_window_mgr"
        local wnd = ui_window_mgr:GetWindowObj(uiModuleName)
        if wnd then
            --自动开主界面
            if wnd.delayOpenMain then
                M.DelaySlgMainUI(true,wnd.delayOpenMain,uiModuleName)
            end
        end
    end
end

--延迟关闭主界面
function M.DelayCloseMainUI(uiModuleName)
    if uiModuleName then
         --打开界面的时候去关闭主界面
        local ui_window_mgr = require "ui_window_mgr"
        local wnd = ui_window_mgr:GetWindowObj(uiModuleName)
        if wnd and wnd.delayCloseMain then
            --自动关主界面
            M.DelaySlgMainUI(false,wnd.delayCloseMain,uiModuleName)
            closeMainMap[uiModuleName] = true
        end
    end
end

local delayMainUITimer = nil
--通用的延迟开关主界面
function M.DelaySlgMainUI(isOpen,time,uiModuleName)
    if delayMainUITimer then
        util.RemoveDelayCall(delayMainUITimer)
        delayMainUITimer = nil
    end
    local eventStr = isOpen and sand_ui_event_define.GW_OPEN_SAND_MAIN or sand_ui_event_define.GW_CLOSE_SAND_MAIN
    if uiModuleName then
        if isOpen then
            --打开主界面
            for k,v in pairs(closeMainMap) do
                if v then
                    --存在界面 要关闭主界面，不要再次打开了
                    return
                end
            end
        else
            --关闭主界面
            if main_slg_common_define.autoCloseMainUINoTopConfig[uiModuleName] then
                eventStr = sand_ui_event_define.GW_CLOSE_SAND_MAIN_NO_TOP
            elseif main_slg_common_define.autoCloseMainUINoTopSetLayerConfig[uiModuleName] then
                M.GW_CLOSE_SAND_MAIN_WITHOUT_TOP_AND_SET_LAYER(uiModuleName)
                return
            end
            
        end
    end
    if time and time>0 then
        delayMainUITimer = util.DelayCallOnce(time,function()
            event.Trigger(eventStr)
        end)
    else
        event.Trigger(eventStr)
    end
end

function M.SetDelayMainTime(uiModuleName,isOpen,time)
    local ui_window_mgr = require "ui_window_mgr"
    local wnd = ui_window_mgr:GetWindowObj(uiModuleName)
    if wnd then
        if isOpen then
            wnd.delayOpenMain = time
        else
            wnd.delayCloseMain = time
        end
    end
end

return M