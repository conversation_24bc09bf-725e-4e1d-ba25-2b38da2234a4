local table = table 
local require = require
local event = require "event"  
local common_tasklist_entity = require "common_tasklist_entity" 
local log = require "log"
--local files_version_mgr = require 'files_version_mgr'
module("common_task") 
--local isOpenTaskPolling = files_version_mgr.GetEnableTaskPolling()  --任务开关，开启任务轮询模式，否则函数堆栈正常执行
local isOpenTaskPolling = true
local taskEntityList = {}
local taskEntityDict = {} 
local currentListCount = 0
local defaultTasklistKey = "defaultTasklistKey"
--启动某个任务
function StartTask(tasklistKey)
	local tasklist = taskEntityDict[tasklistKey]  
	if tasklist then
		tasklist:StartTask()
	else
		log.Warning("start task not exist",tasklistKey)
	end
end
--暂停某个任务
function PauseTask(tasklistKey)
	local tasklist = taskEntityDict[tasklistKey]  
	if tasklist then
		tasklist:PauseTask()
	else
		log.Warning("pause task not exist",tasklistKey)
	end
end
function GetTasklistEntity(tasklistKey) 
	if taskEntityDict[tasklistKey] then
		return taskEntityDict[tasklistKey]
	else 
		local tasklist = CreateTaskEntity(tasklistKey)
		RegisterUpdateTick() --每当有任务创建，都要启动注册update事件
		return tasklist
	end 
end

function CreateTaskEntity(tasklistKey)
	local tasklist = common_tasklist_entity()
	tasklist.key = tasklistKey
	taskEntityDict[tasklistKey] = tasklist
	table.insert(taskEntityList,tasklist)
	currentListCount = currentListCount + 1
	return tasklist
end
--插入任务，任务默认执行，分组默认是最后一个分组，执行顺序在所有任务后面 100为最高优先级类型，直接执行
function Push(taskFunc,taskParam,tag,tasklistKey,canTaskRepeat)
	if not isOpenTaskPolling then
		taskFunc(taskParam)
		return
	end 
	if tag == 100 then
		taskFunc(taskParam)
		return
	end
	tasklistKey = tasklistKey or defaultTasklistKey
	local m_task = GetTasklistEntity(tasklistKey)
	m_task:Push(taskFunc,taskParam,tag,canTaskRepeat)
	return m_task
end 

--定时器轮询更新
function TaskUpdate(evt,deltaTime)     
	if currentListCount == 0 then
		return
	end
	for i=1,currentListCount do
		local tasklist = taskEntityList[i]
		tasklist:TaskUpdate(evt,deltaTime) 
		if CheckRemoveTasklist(tasklist.key) then
			i = i-1
		end
	end 
end
--检查移除任务，将执行完毕的任务清理出去
function CheckRemoveTasklist(tasklistKey)
	local tasklist = taskEntityDict[tasklistKey]
	if tasklist == nil then
		return false
	end
	if tasklist:IsAllTaskFinish() then
		return ClearTasklist(tasklistKey)
	end
	return false
end

--清除指定分组的任务
function ClearTasklist(tasklistKey) 
	if not taskEntityDict[tasklistKey] then
		return false
	end
	local tasklist = taskEntityDict[tasklistKey]
	tasklist:ClearAllTask()
	for i=1,currentListCount do
		if taskEntityList[i].key == tasklistKey then
			table.remove(taskEntityList,i)
			break
		end
	end
	taskEntityDict[tasklistKey] = nil 
	currentListCount = currentListCount - 1

	if currentListCount <= 0 then
		UnregisterUpdateTick()
	end
	return true
end

--清除所有未执行的任务
function ClearAllTasklist() 
	local totalrmlist = {}
	for i=1,currentListCount do
		table.insert(totalrmlist,taskEntityList[i].key)
	end
	for i=1,#totalrmlist do
		ClearTasklist(totalrmlist[i])
	end
end

--有任务的时候，注册update
function RegisterUpdateTick()
	event.Register(event.CSUpdate, TaskUpdate)
end
--任务执行完毕之后，关闭update
function UnregisterUpdateTick()
	event.Unregister(event.CSUpdate, TaskUpdate)
end
log.Warning("qzlogwarning taskPollingState",isOpenTaskPolling)

