local require = require
local print = print

local game = require "game"
local event = require "event"
local game_config = require "game_config"
local ui_window_mgr = require "ui_window_mgr"

local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local Debug = CS.UnityEngine.Debug
local Application = CS.UnityEngine.Application
local Utility = CS.War.Script.Utility

module("controller_cutscene")

local processed = false
local callback = nil

function CheckEnterNewPlayerCutscene()
----    print("CheckEnterNewPlayerCutscene")
    if processed == false then
       StartUIWatcher()
    else
        ui_window_mgr:UnloadModule("ui_menu_cg")
    end

    return false
end

function RegisterCallback(cb)
    callback = cb
end

function IsMenuCG()
    if game_config.ENABLE_CG then 
        return ui_window_mgr:IsModuleShown("ui_menu_cg")
    end
    
end
function PrepareMenuCG()
    if game_config.ENABLE_CG then 
        local userID = PlayerPrefs.GetString("userID","")
        local key = "CutsceneShown" .. userID
        local shown = PlayerPrefs.GetInt(key, 0)
        if shown == 0 then 
            ShowCG(true)
            return 
        end 
    end
end

function NotifyCGPlayed()
    SaveCGPlayedStatus()

    ui_window_mgr:UnloadModule("ui_menu_cg")
    -- Debug.Log("cg unloaded")

    ReturnLoginFlow()
end

function SaveCGPlayedStatus()
    if processed then return end
    local userID = PlayerPrefs.GetString("userID","")
    -- local roleID = game.actors[1].roleID
    local key = "CutsceneShown" .. userID
    PlayerPrefs.SetInt(key, 1)
    processed = true
end

function ResetProcessFlag()
    processed = false
end

function StartUIWatcher()
    if game_config.ENABLE_CG then 
        local userID = PlayerPrefs.GetString("userID","")
        local key = "CutsceneShown" .. userID
        local shown = PlayerPrefs.GetInt(key, 0)
        if shown == 0 then 
            ShowCG(true,true)
            return 
        end
        ShowCG(false)
        return
    end
    -- if (game.actors ~= nil and #game.actors > 0) and (game_config.ENABLE_Q1_DEBUG_MODE or Application.isEditor or Utility.VersionIsHigher(Application.version, '1.0.24')) then
    --     local roleID = game.actors[1].roleID
    --     local level = game.actors[1].level
    --     local key = "CutsceneShown" .. roleID
    --     local shown = PlayerPrefs.GetInt(key, 0)
    --     if shown == 0 and level <= 1 then 
    --         ShowCG(true)
    --         return 
    --     end
    -- end

    -- ShowCG(false)
end


function ShowCG(enable,grant)


    local ui_login_main_mgr = require "ui_login_main_mgr"
    local isLogined = ui_login_main_mgr.GetOnceLogined()
    if isLogined then
        return
    end

    local ui_menu_cg = require "ui_menu_cg"
    ui_menu_cg.NotifyNeedPlayCG(enable,grant)
    if not enable then
        ui_window_mgr:UnloadModule("ui_menu_cg")
    end
end

function ReturnLoginFlow()
    if callback then
        callback()
        callback=nil
    end
end
-- event.Register(event.SCENE_DESTROY_NEW, ResetProcessFlag)
-- event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, ResetProcessFlag)