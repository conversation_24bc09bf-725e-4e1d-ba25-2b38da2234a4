local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local gw_task_const = require "gw_task_const"
local event_task_define = require "event_task_define"
local ui_util = require "ui_util"
local gw_task_mgr = require "gw_task_mgr"
local game_scheme = require "game_scheme"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_arena_daily_box_controller")
local controller = nil
local UIController = newClass("ui_arena_daily_box_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)  
    if not data then
        return
    end
    self.taskIdList = data.taskIdList
    self.activityId = data.activityId
    self:InitTaskShow()
    self:RefreshTaskShow()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    local updateTask = function(eventName, taskData, moduleId, moduleList)
        if moduleList[gw_task_const.TaskModuleType.StormArena] then
            self:RefreshTaskShow()
        end
    end
    self:RegisterEvent(event_task_define.REFRESH_TASK, updateTask)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnCloseClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end
function  UIController:OnBtnBoxItem_1ClickedProxy()
end
function  UIController:OnBtnBoxItem_2ClickedProxy()
end
function  UIController:OnBtnBoxItem_3ClickedProxy()
end

--初始化任务显示
function UIController:InitTaskShow()
    if not self.taskIdList or #self.taskIdList ~= 3 then
        return
    end
    local data = {}
    self.taskCfg = {}
    local progressList = {}
    for i = 1, 3 do
        local taskId = self.taskIdList[i]
        local taskCfg = game_scheme:TaskMain_0(taskId)
        if not taskCfg then
            return
        end
        local progress = {
            progressNum = taskCfg.ConditionValue1
        }
        table.insert(self.taskCfg, taskCfg)
        table.insert(progressList, progress)
    end
    data.taskCfg = self.taskCfg
    data.activityId = self.activityId
    local maxTaskData = gw_task_mgr.GetTaskData(self.taskIdList[3])
    if not maxTaskData then
        return
    end
    data.curChallengeNum = maxTaskData.rate
    data.maxTaskFinishNum = data.taskCfg[3].ConditionValue1
    data.sliderValue = ui_util.GetProgressByProgressList(progressList, data.curChallengeNum)
    self:TriggerUIEvent("InitProgressShow", data)
end

--刷新任务显示
function UIController:RefreshTaskShow()
    if not self.taskIdList or #self.taskIdList ~= 3 then
        return
    end
    for i = 1, 3 do
        local taskId = self.taskIdList[i]
        local taskData = gw_task_mgr.GetTaskData(taskId)
        if not taskData then
            return
        end
        self:TriggerUIEvent("RefreshRewardShow", i, taskData.status and 1 or 0)
    end
end


--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
