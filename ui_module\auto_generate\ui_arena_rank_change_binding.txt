local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local RectTransform = CS.UnityEngine.RectTransform
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Text = CS.UnityEngine.UI.Text
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem


module("ui_arena_rank_change_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenarankchange.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	rtf_changeAni = { path = "rtf_changeAni", type = RectTransform, },
	rtf_RankShow = { path = "rtf_RankShow", type = RectTransform, },
	ss_BG = { path = "rtf_RankShow/ss_BG", type = SpriteSwitcher, },
	ss_rankBg = { path = "rtf_RankShow/ss_rankBg", type = SpriteSwitcher, },
	txt_curRankNumUp = { path = "rtf_RankShow/ss_rankBg/txt_curRankNumUp", type = Text, },
	txt_curRankNumDown = { path = "rtf_RankShow/ss_rankBg/txt_curRankNumDown", type = Text, },
	ss_playerNameBG = { path = "rtf_RankShow/ss_playerNameBG", type = SpriteSwitcher, },
	txt_playerName = { path = "rtf_RankShow/ss_playerNameBG/txt_playerName", type = Text, },
	txt_oldRank = { path = "rtf_RankShow/RankChangeShow/txt_oldRank", type = Text, },
	txt_curRank = { path = "rtf_RankShow/RankChangeShow/txt_curRank", type = Text, },
	ss_changeArrow = { path = "rtf_RankShow/RankChangeShow/ss_changeArrow", type = SpriteSwitcher, },
	txt_changeDes = { path = "rtf_RankShow/txt_changeDes", type = Text, },
	scrItem_selfArenaInfo = { path = "scrItem_selfArenaInfo", type = ScrollRectItem, },

}
