local typeof = typeof
local table = table
local dump = dump
local Debug = CS.UnityEngine.Debug

local UtilityManager = CS.UtilityManager
local PostProcessingBehaviour = CS.UnityEngine.PostProcessing.PostProcessingBehaviour

module("battle_player_assistant")

local bEnableBattlePostPro = true
local FPSLimit = 25
local FPSLimitPercent = 0.6
local fpsData = {}

function StartFPSCounter()
    local utilityManager = UtilityManager.GetInstance()
    fpsData.fpsSampleCount = 10
    fpsData.currentSample = 0
    fpsData.fpsRecord = {}
    Debug.LogWarning("FPSSampeUpdate StartFPSCounter:"..fpsData.currentSample..",fpsSampleCount:"..fpsData.fpsSampleCount)
    if not bEnableBattlePostPro then
        utilityManager:EnableComponentType(typeof(PostProcessingBehaviour), false)
        return
    end
    utilityManager:RegisterFPSListener(FPSSampeUpdate)
end

function StopFPSCounter()
    local utilityManager = UtilityManager.GetInstance()
    if utilityManager then
        utilityManager:UnRegisterFPSListener(FPSSampeUpdate)
    end
end

--完成一次采样
function FPSSampeUpdate()
    local utilityManager = UtilityManager.GetInstance()
    table.insert(fpsData.fpsRecord, utilityManager.CurrentFps)
    fpsData.currentSample = fpsData.currentSample + 1
    Debug.LogWarning("FPSSampeUpdate currentSample:"..fpsData.currentSample..",fpsSampleCount:"..fpsData.fpsSampleCount)
    if fpsData.currentSample >= fpsData.fpsSampleCount then
        StopFPSCounter()
        -- dump(fpsData)
        local lessCount = 0
        for i=1,#fpsData.fpsRecord do
            if fpsData.fpsRecord[i] < FPSLimit then
                lessCount = lessCount + 1
            end
        end
        Debug.LogWarning("FPSSampeUpdate lessCount:"..lessCount..",percent:"..(lessCount / fpsData.fpsSampleCount))
        if lessCount / fpsData.fpsSampleCount > FPSLimitPercent then
            Debug.LogWarning("Close PostProcessingBehaviour")
            utilityManager:EnableComponentType(typeof(PostProcessingBehaviour), false)
            bEnableBattlePostPro = false
        end
    end
end
