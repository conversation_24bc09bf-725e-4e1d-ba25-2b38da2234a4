local getmetatable = getmetatable
local setmetatable = setmetatable
local rawset = rawset
local pcall = pcall
local print = print
local rawget = rawget

module("class")

-- local SetObjProperty = function(t,k,v)
--     t[k] = v
-- end

-- local function Class(base,static,instance)
--     -- 目前base一定要传
--     local mt = getmetatable(base)

--     local class = static or {}
--     setmetatable(class,
--         {
--             __index = base,
--             __call = function(...)
--                 local nInst = {}
--                 --使用子类或者父类调用同个方法，self代表的table是不一样的
--                 local r = mt.__call(...)
--                 r.__self = nInst

--                 local ret = instance or {}

--                 --子类构造方法可以通过__super获取真正父类的实例，不可以在构造方法外使用__super字段（function除外，父类function复用）
--                 ret.__super = r
--                 nInst.__base = r

--                 local ins_ret = setmetatable(
--                     -- {
--                     --     __base = r,
--                     -- },
--                     nInst,
--                     {
--                         --所有后期属性设置都会优先存在子类table内
--                         __index = function(t, k)
--                             local ret_field
--                             ret_field = ret[k]
--                             if nil == ret_field then
--                                 ret_field = r[k]
--                             end

--                             return ret_field
--                         end,

--                         __newindex = function(t,k,v)
--                             --[[ 与后面的 pcall 等价，增加函数变量，方便 lua profiler 统计内存分配，对比数据
--                             local tmpSetProperty = function()
--                                 r[k]=v
--                             end
--                             if not pcall(tmpSetProperty) then
--                                 rawset(t,k,v)
--                             end
--                             ]]
--                             -- if not pcall(function() r[k]=v end) then
--                             --     rawset(t,k,v)
--                             -- end
--                             if not pcall(SetObjProperty, r, k, v) then
--                                 rawset(t,k,v)
--                             end
--                         end,
--                     })

--                 --ctor方法不需要递归调用父类ctor方法，__call方法会调用对于ctor
--                 if ret.ctor then
--                     ret.ctor(ins_ret, ...)
--                 end

--                 return ins_ret
--             end,
--         }
--     )
--     return class
-- end

local function calculateIndexLevel(obj, level)
    if rawget(obj, "__indexLevel") then
        rawset(obj, "__indexLevel", level)
    end
    local __base = rawget(obj, "__base")
    if __base then
        calculateIndexLevel(__base, level + 1)
    end
end

---@param base table 父类
---@param static any nil
---@param instance table 子类
---整个类设计较重度，如何仅是简单键值对，属性等类，建议使用更轻量的类，如 core_base 下的 Class 方法
-- local function Class(base,static,instance, className)
local function Class(base, static, instance)
    -- 目前base一定要传
    local parentMeta = getmetatable(base)

    --- class 对象变量只是个类对象构造器, wrap 类，instance 才是类实现
    --- class 主要只提供 __index 和 __call 方法
    local class = static or {}
    setmetatable(
        class,
        {
            __index = base,
            __call = function(...)
                --- instance 类型的类的实例对象
                local curObj = {}

                -- curObj.className = className or ""
                -- if className then
                --     print("SetObjProperty className", className)
                -- end

                --- 上级父类的实例对象, call 会导致逐级调用 call，为每级父类都创建一个实例对象，通过 __base 来指向父类实例对象
                --- e.g. UIBase = {__base = {}}
                ---@type table
                local parentObj = parentMeta.__call(...)
                --- __self 指向 instance 类型的类对象，即当前类的对象，由于__newindex 的实现，__self 将出现在最底层基类对象中
                --- __self 则会被以 1~n 等差数列次数方式多次重复访问 __newindex，被 ui_base 用于移除窗口类子类对象
                -- parentObj.__self = curObj

                --- 从底层向上逐级传递，会进行 n 次设置（n 为子类继承深度）
                local filedObj = rawget(parentObj, "__filedObj")
                if not filedObj then
                    filedObj = parentObj
                end
                curObj.__filedObj = filedObj
                curObj.__indexLevel = 1
                calculateIndexLevel(parentObj, 2)

                --- 满足 ui_base 移除窗口类子类对象需求
                filedObj.__self = curObj

                --- instance 类型的类，即 curClass 为当前类，curObj|ins_ret 为当前类对象
                --- e.g. UIBase
                local curClass = instance or {}

                --子类构造方法可以通过__super获取真正父类的实例，不可以在构造方法外使用__super字段（function除外，父类function复用）
                --- __super 由类持有，指向父类实例对象
                --- __base 由类对象持有，指向父类实例对象
                curClass.__super = parentObj
                curObj.__base = parentObj

                --- ins_ret == curObj
                local ins_ret =
                    setmetatable(
                    curObj,
                    {
                        --所有后期属性设置都会优先存在子类table内
                        __index = function(t, k)
                            --- 先访问类，如，是否是类函数，若不存在，再访问父类，通过 index 逐级向上访问父类函数等
                            -- 若不存在，则通过 index 传递到最底层基类对象中访问对象属性
                            local ret_field
                            --- 以下过滤性能受 curObj 及 filedObj 中包含属性数量影响
                            --- 这里按设计假设 curObj 属性远少于 filedObj
                            if rawget(curObj, "__indexLevel") == 1 then
                                ret_field = filedObj[k]
                                if ret_field then
                                    return ret_field
                                end
                            end
                            ret_field = curClass[k]
                            if ret_field then
                                return ret_field
                            end
                            ret_field = parentObj[k]
                            return ret_field
                        end,
                        __newindex = function(t, k, v)
                            --- 属性会通过 __newindex 传递到最底层对象中，这里直接通过 filedObj 设置，避免 __newindex 递归调用
                            filedObj[k] = v

                            --- 向父类实例设置属性，通过 __newindex 传递，最终被设置在最底层基类对象中
                            -- if not pcall(SetObjProperty, parentObj, k, v) then
                            --     rawset(t,k,v)
                            -- end
                        end
                    }
                )

                --ctor方法不需要递归调用父类ctor方法，__call方法会调用对于ctor
                if curClass.ctor then
                    curClass.ctor(ins_ret, ...)
                end

                return ins_ret
            end
        }
    )
    return class
end

return Class
