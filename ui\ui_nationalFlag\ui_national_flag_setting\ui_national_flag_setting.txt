local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local typeof = typeof
local RectTransform = CS.UnityEngine.RectTransform
local UIUtil = CS.Common_Util.UIUtil
local Button = CS.UnityEngine.UI.Button
local Image = CS.UnityEngine.UI.Image
local os = os

local event = require "event"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_national_flag_setting_binding"
local game_scheme = require "game_scheme"
local national_flag_mgr = require "national_flag_mgr"
local ui_window_mgr = require "ui_window_mgr"
local time_util = require "time_util"
local event_NationalFlag_define = require "event_NationalFlag_define"

--region View Life
module("ui_national_flag_setting")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

local currSelectCountryID = nil
local needShowNationalFlag = {}

local selectType

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
    self:ShowNationalFlagUI()
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:ShowNationalFlagUI()
    --- 需要显示的国旗table
    -- 整理出需要显示的国旗
    needShowNationalFlag = {}
    for i = 1, game_scheme:Country_nums() do
        local countryData = game_scheme:Country_0(i)
        if countryData and countryData.visible == 1 then
            table.insert(needShowNationalFlag, countryData)
        end
    end
    -- 根据needShowNationalFlag渲染
    -- 将渲染回调绑定到当前 UIView 实例，使回调中可以使用 self
    self.srt_Content.onItemRender = function(scroll_rect_item, index, dataItem)
        self:OnRenderItem(scroll_rect_item, index, dataItem)
    end
    self.srt_Content:SetData(needShowNationalFlag, #needShowNationalFlag)
end

function UIView:OnRenderItem(scroll_rect_item, index, dataItem)
    local compImg = scroll_rect_item:Get("img")
    local compBtn = scroll_rect_item:Get("btn")
    local selected = scroll_rect_item:Get("selected")
    local selected_1 = scroll_rect_item:Get("selected_1")
    if compImg then
        -- 使用 UIView 的方法创建子精灵
        self:CreateSubSprite("CreateNationalFlagAsset", compImg, dataItem.information)
    end

    compBtn.onClick:AddListener(function()
        currSelectCountryID = dataItem.ID
        -- 使用 self 刷新列表
        self.srt_Content.data = needShowNationalFlag
        self.srt_Content:Refresh(0, -1)
    end)

    -- 处理选中
    if currSelectCountryID ~= dataItem.ID then
        selected.gameObject:SetActive(false)
        selected_1.gameObject:SetActive(false)
    else
        selected.gameObject:SetActive(true)
        selected_1.gameObject:SetActive(true)
    end
end

function UIView:SetType(type)
    selectType = type
    self:SetCountDown()
    if selectType == 1 then
        currSelectCountryID = national_flag_mgr.GetSelfNationalFlagInfo()
    elseif selectType == 2 then
        currSelectCountryID = national_flag_mgr.GetAllianceNationalFlagID()
        if currSelectCountryID == nil or currSelectCountryID == 0 then
            currSelectCountryID = national_flag_mgr.GetSelfNationalFlagInfo()
        end
    end
end

function UIView:SetCountDown()
    if selectType == 3 then
        self.txt_CountDown.text = lang.Get(1)
        self.btn_Confirm.interactable = true
        return
    elseif selectType == 2 then
        -- 数据源切换成联盟倒计时
        local nationalFlagTime = require("national_flag_mgr").GetChangeFlagCountDownEndTimeAlliance()

        local timer = self:CreateTimer(1, function()
            --对时间的处理
            local countDown = nationalFlagTime - os.server_time()
            local timeStr = time_util.FormatTime5(countDown)
            if countDown <= 0 then
                -- 确认
                self.txt_CountDown.text = lang.Get(1)
                self.btn_Confirm.interactable = true
            else
                -- 倒计时
                self.txt_CountDown.text = timeStr
                self.btn_Confirm.interactable = false
            end
        end)
    elseif selectType == 1 then
        -- TODO 国旗功能待修改
        --- 上次修改国旗的时间
        local nationalFlagTime = require("national_flag_mgr").GetChangeFlagCountDownEndTime()

        local timer = self:CreateTimer(1, function()
            --对时间的处理
            local countDown = nationalFlagTime - os.server_time()
            local timeStr = time_util.FormatTime5(countDown)
            if countDown <= 0 then
                -- 确认
                self.txt_CountDown.text = lang.Get(1)
                self.btn_Confirm.interactable = true
            else
                -- 倒计时
                self.txt_CountDown.text = timeStr
                self.btn_Confirm.interactable = false
            end
        end)
    end
    -- 处理倒计时
end

function UIView:OnConfirm(data)
    if data == 2 then
        -- 切换联盟国旗
        national_flag_mgr.RequestChangeAllianceNationalFlag(currSelectCountryID)
    elseif data == 3 then
        -- 创建联盟
        require("national_flag_mgr").SetCreateAllianceNationalFlagID(currSelectCountryID)
        event.Trigger(event_NationalFlag_define.ON_NATIONAL_FLAG_REFRESH)
    elseif data == 1 then
        -- 切换个人国旗
        national_flag_mgr.RequestChangeSelfNationalFlag(currSelectCountryID)
        ui_window_mgr:UnloadModule("ui_personalInfo")
    end
    ui_window_mgr:UnloadModule("ui_national_flag_setting")
    needShowNationalFlag = {}
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
            window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
            window:LoadUIResource(ui_path, nil, nil, nil, nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
