local require = require
local typeof = typeof

local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local Button = CS.UnityEngine.UI.Button
local GameObject = CS.UnityEngine.GameObject


module("ui_halloween_jump_center_binding")

UIPath = "ui/prefabs/gw/activity/halloweenactivity/jumpcenter/uihalloweenjumpcenter.prefab"

WidgetTable ={
	txt_ActivityCountDownTime = { path = "up/ChangeTimeBg/txt_ActivityCountDownTime", type = Text, },
	rtf_goal_item = { path = "up/goal/rtf_goal_item", type = RectTransform, },
	txt_tips = { path = "up/txt_tips", type = Text, },
	btn_TipsBtn = { path = "btn_TipsBtn", type = Button, event_name = "OnBtnTipsBtnClickedProxy"},
	txt_tips2 = { path = "verticalContent/txt_tips2", type = Text, },
	item_JumpCenterItem = { path = "verticalContent/Scroll View/Viewport/Content/item_JumpCenterItem", type = GameObject, },

}
