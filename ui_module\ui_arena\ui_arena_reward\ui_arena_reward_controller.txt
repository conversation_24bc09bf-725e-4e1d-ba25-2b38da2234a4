local print = print
local tostring            = tostring
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local game_scheme = require "game_scheme"
local cfg_util = require "cfg_util"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_arena_reward_controller")
local controller = nil
local UIController = newClass("ui_arena_reward_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    self.rewardData = data
    self:OnTog1ValueChange(true)
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.rankCfgList = nil
    self.dailyRewardList = nil
    self.personRewardList = nil
    self.allianceRewardList = nil
    self.selectIndex = nil

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end
function  UIController:OnTog1ValueChange(state)
    if state then
        self.selectIndex = 1
        self:SetPersonRankRewardList()
        self:TriggerUIEvent("ShowPanel",1, self.personRewardList)
    end
end
function  UIController:OnTog2ValueChange(state)
    if state then
        self.selectIndex = 2
        self:SetDailyRewardList()
        self:TriggerUIEvent("ShowPanel",2, self.dailyRewardList)
    end
end
function  UIController:OnTog3ValueChange(state)
    if state then
        self.selectIndex = 3
        self:SetAllianceRankRewardList()
        self:TriggerUIEvent("ShowPanel",3, self.allianceRewardList)
    end
end
function  UIController:OnBtnPop_btn_returnClickedProxy()
    self:CloseView()
end

--设置个人排行榜配置
function UIController:SetPersonRankRewardList()
    if self.personRewardList then
        return
    end
    if not self.rewardData or not self.rewardData.personRewardType then
        return
    end
    self.personRewardList = {}
    local list = self:GetRankRewardList(self.rewardData.personRewardType)
    for i = 1, #list do
        local cfg = list[i]
        local rewardSection = cfg_util.GetLuaArrayByCfgList(cfg.conditionalparameters)
        local str = rewardSection[2] and table.concat(rewardSection,"-") or tostring(rewardSection[1])
        local rewardData ={
            reward = cfg.Reward,
            rankNum = rewardSection[2] and cfg.conditionalparameters.data[0] or nil,
            rankDesc = str
        }
        table.insert(self.personRewardList, rewardData)
    end
end

--设置每日奖励配置
function UIController:SetDailyRewardList()
    if self.dailyRewardList then
        return
    end
    if not self.rewardData or not self.rewardData.dailyTask then
        return
    end
    local gw_task_mgr = require "gw_task_mgr"
    self.dailyRewardList = {}
    for k ,v in ipairs(self.rewardData.dailyTask) do
        local taskCfg = game_scheme:TaskMain_0(v)
        local taskData = gw_task_mgr.GetTaskData(v)
        if taskCfg and taskData then
            local rewardData ={
                reward = taskCfg.TaskReward,
                title = string.format("%s%s",gw_task_mgr.GetTaskDescriptionByCfg(taskCfg),GetNeedCountStr(taskData.rate,taskCfg.ConditionValue1))
            }
            table.insert(self.dailyRewardList,rewardData)
        end
    end
end

function GetNeedCountStr(curCount,needCount)
    curCount = curCount or 0
    needCount = needCount or 0
    local needCountStr = string.format("(%d/%d)",curCount,needCount)
    return needCountStr
end


--设置联盟排行榜配置
function UIController:SetAllianceRankRewardList()
    if self.allianceRewardList then
        return
    end
    if not self.rewardData or not self.rewardData.allianceRewardType then
        return
    end
    self.allianceRewardList = {}
    local list = self:GetRankRewardList(self.rewardData.allianceRewardType)
    for i = 1, #list do
        local cfg = list[i]
        local rewardSection = cfg_util.GetLuaArrayByCfgList(cfg.conditionalparameters)
        local str = rewardSection[1]~= rewardSection[2] and table.concat(rewardSection,"-") or tostring(rewardSection[1])
        local rewardData ={
            reward = cfg.Reward,
            --665026 第{}名奖励
            title = string.format2(lang.Get(665026),str),
            GetTipFunc = function()
                return  string.format2(self.rewardData.allianceTip,str)
            end,

        }
        table.insert(self.allianceRewardList,rewardData)
    end
end

--获取排行奖励配置
function UIController:GetRankRewardList(rewardType)
    if not self.rankCfgList then
        self.rankCfgList = cfg_util.InitConfigByFieldsAndIndex("RankingRewards","rewardtype", "subtype") 
    end
    if rewardType.count < 2 then
        return {}
    end
    local typeList = cfg_util.ArrayToLuaArray(rewardType)
    local list = self.rankCfgList[typeList[1]][typeList[2]]
    if not list then
        return {}
    end
    return list
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
