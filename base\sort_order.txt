-- sort_order.txt ------------------------------------------------
-- author:  郑秀程
-- date:    2018.5.17
-- ver:     1.0
-- desc:    UI排序管理器
-------------------------------------------------------------------

local require = require
local typeof = typeof
local pairs = pairs

local util = require "util"
local event = require "event"
local const       = require "const"

local Canvas = CS.UnityEngine.Canvas
local GameObject = CS.UnityEngine.GameObject
local CanvasType = typeof(CS.UnityEngine.Canvas)
local GraphicRaycasterType = typeof(CS.War.UI.GRaycaster)
local SortingGroupType    = typeof(CS.UnityEngine.Rendering.SortingGroup)
local CanvasGroupType = typeof(CS.UnityEngine.CanvasGroup)
local AdditionalCanvasShaderChannels = CS.UnityEngine.AdditionalCanvasShaderChannels

module("sort_order")

local OrdersList = {
    
}

local GraphicRaycasters = {}
local canvasMesh = nil
--[[注册一个要进行排序处理的根对象
@param rootTrans 要对子对象进行排序的根对象
]]
function RegisterRoot(rootTrans)
    if not rootTrans then
        return
    end
    
    local order = OrdersList[rootTrans]
    if not order then
        OrdersList[rootTrans] = {curIndex=0}
    end

end

--[[注销一个要进行排序处理的根对象
@param rootTrans 要对子对象进行排序的根对象
]]
function UnregisterRoot(rootTrans)
    if not rootTrans then
        --OrdersList[rootTrans] = nil
        return
    end
	if OrdersList[rootTrans] then
		OrdersList[rootTrans] = {curIndex=0}
	end
end

--[[当某个子对象被移除的时候调用，这里会自动删除自动添加的组件
@param childTrans 被添加的自对象
@param rootTrans 根对象
]]
function OnRemoveChild(childTrans, rootTrans)
    --先不实现，看看有问题吗？
end
 
--[[当某个子对象被添加的时候调用，这里会自动更新子对象的 排序 索引
@param childTrans 被添加的子对象
@param rootTrans 根对象
]]
function OnAddChild(childTrans, rootTrans, assetBundleName, enableSortingGroup, uiBase)
    if not childTrans or not rootTrans then
        return 0
    end
    
    local order = OrdersList[rootTrans]
    if not order then
        local log = require "log"
        if not rootTrans or (not const.OPEN_NEW_WINDOW_MGR) or (not enableSortingGroup) or (not uiBase) then
            return 0
        end
        local canvas = childTrans.gameObject:GetComponent(CanvasType)
        if not util.IsObjNull(canvas) then
            return 0
        end

        if uiBase.canvasGroupInfo then
            return 0
        end
        uiBase.canvasGroupInfo = {}
        local canvasGroup = childTrans.gameObject:GetComponent(CanvasGroupType)
        if not util.IsObjNull(canvasGroup) then
            return 0
        end
        -- 只有 prefab 或 gameObject 上没有 SortingGroup 时才添加 SortingGroup。由于 SetActive 并不会真实隐藏节点，由
        -- CanvasGroup.alpha = 0|1 来控制物体是否绘制，避免所有没有 Canvas 的界面在屏幕外时仍然绘制即 drawcall 不会减少
        --[[
        解决当页面以 tab 方式切换时，drawcall 叠加不减少问题。
        RectMask2D 会导致界面不同分辨率下被裁剪问题。 Mask 考虑效率问题
        Canvas 会导致子界面是全屏界面但要求嵌入到父界面时层级错乱。
        SetActive 会导致网格重建，目前是不使用此方法，会导致界面没有 canvas 组件时即使在屏幕外仍会绘制
        先使用 CanvasGroup 来控制是否绘制
        ]]
        canvasGroup = childTrans.gameObject:AddComponent(CanvasGroupType)
        uiBase.canvasGroupInfo.canvasGroup = canvasGroup

        return 0
    end
            
    -- if const.OPEN_NEW_WINDOW_MGR then
    -- else
    -- end

    -- order.curIndex = order.curIndex + 1
    
    -- 下面AddComponent(CanvasType)之后可能导致childTrans引用失效。所以先引用gameObject
    local go = childTrans.gameObject

    local canvas = go:GetComponent(CanvasType)
    if not canvas or canvas:IsNull() then
        canvas = go:AddComponent(CanvasType)

        
        if const.OPEN_NEW_WINDOW_MGR then
            if enableSortingGroup then
                local sGroup = go:AddComponent(SortingGroupType)
            end
            canvas.enabled = false
        else 

        end
    end
    
    -- if canvas then
    --     canvas.overrideSorting = true
        
        -- if const.OPEN_NEW_WINDOW_MGR then

        -- else 
        --     canvas.sortingOrder = order.curIndex
        -- end
    -- end
    
    --加上GraphicRaycaster组件不然不能响应UI事件了
    local graycaster = go:GetComponent(GraphicRaycasterType)
    if not graycaster or graycaster:IsNull() then
        graycaster = go:AddComponent(GraphicRaycasterType)
    end
    if graycaster then
        GraphicRaycasters[assetBundleName] = graycaster  

        if graycaster.AddShaderChannel then
            graycaster:AddShaderChannel(AdditionalCanvasShaderChannels.TexCoord3)
            graycaster:AddShaderChannel(AdditionalCanvasShaderChannels.Tangent)
        end
    end
    -- return order.curIndex
    return 0
end

function GetNextOrder()
   return ApplyIndexs(canvasMesh, 1)
end
--[[申请指定个数的排序索引，这个通常用来处理窗口内的光效
@param rootTrans 根对象
@param nCount 要申请的个数
@return nStart, nEnd 返回起始索引
]]
function ApplyIndexs(rootTrans, nCount)

    if const.OPEN_NEW_WINDOW_MGR then
        
    else 
        rootTrans = rootTrans or canvasMesh
        local order = OrdersList[rootTrans]
        if not order then
            return -1,-1
        end
        
        local nStart = order.curIndex + 1
        local nEnd = nStart + nCount
        order.curIndex = nEnd + 1
        
        return nStart,nEnd
        
    end
end
--[[申请指定个数的排序索引，这个通常用来处理窗口内的光效 废弃方法 参考uibase的curOrder
@param rootTrans 根对象
@param nCount 要申请的个数
@return nStart, nEnd 返回起始索引
]]
function ApplyBaseIndexs(self,rootTrans, nCount)

    if const.OPEN_NEW_WINDOW_MGR then
        
    else 
        return ApplyIndexs(rootTrans, nCount)
    end
    

    if self and self.curOrder then 
        return self.curOrder + 1,nCount and (self.curOrder + nCount)
    end
end

function ResetOrder(childTrans)
    local order = OrdersList[childTrans.parent]
    if order then
        local canvas = childTrans:GetComponent(CanvasType)
        if canvas then
            
            order.curIndex = order.curIndex + 1
            if const.OPEN_NEW_WINDOW_MGR then
        
            else 
                canvas.sortingOrder = order.curIndex
            end
            canvas.overrideSorting = true
        end
    end
end

--[[初始化，这里将要进行排序的根对象注册进来]]
function Init()
    canvasMesh = GameObject.Find("/UIRoot/CanvasWithMesh").transform
    RegisterRoot(canvasMesh)
end

Init()

function OnSceneDestroy()
    UnregisterRoot(canvasMesh)
    
    for k,v in pairs(GraphicRaycasters) do 
        if not util.IsObjNull(v) then
            v.enabled = true
        end
    end
    loadmodule = 0
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)

function OnModuleClose(eventName, moduleName, assetBundleName)
    GraphicRaycasters[assetBundleName] = nil
end
event.Register(event.UI_MODULE_CLOSE, OnModuleClose)

local loadmodule = 0
local ticker = nil
function OnModuleShow(eventname, count, moduleName)
    if ticker then
        util.RemoveDelayCall(ticker)
        ticker = nil
    end
    loadmodule = count
    for k,v in pairs(GraphicRaycasters) do 
        if not util.IsObjNull(v) then
            v.enabled = false
        end
    end
    
    ticker = util.DelayCall(3, function()
        for k,v in pairs(GraphicRaycasters) do 
            if not util.IsObjNull(v) then
                v.enabled = true
            end
        end
        loadmodule = 0
    end)
end
event.Register(event.SHOW_MODULE, OnModuleShow)

function OnModuleLoadComplete()
    if loadmodule > 0 then
        loadmodule = loadmodule - 1
    end
    if loadmodule == 0 then       
        for k,v in pairs(GraphicRaycasters) do 
            if not util.IsObjNull(v) then
                v.enabled = true
            end
        end
    end
end

function GetLoadModule()
	return loadmodule
end
event.Register(event.UI_MODULE_LOAD_COMPLETE, OnModuleLoadComplete)

 
