--  BaseSubset.txt ------------------------------------------
-- author:  吕培玥
-- date:    2020.12.14
-- ver:     1.0
-- desc:   	管理ui_base  子集
--------------------------------------------------------------

local sprite_asset = require "sprite_asset"
local newclass = newclass
local ipairs = ipairs
local pairs = pairs
local log = log
local table = table
local type = type
local typeof = typeof
local util = require "util"
local event = require 'event'
local Component = require "base_comp"
local card_sprite_asset = require "card_sprite_asset"
local UIUtil = CS.Common_Util.UIUtil
local LeanTween = CS.LeanTween
local LeanTweenType = CS.LeanTweenType
local CanvasType = typeof(CS.UnityEngine.Canvas)
local SortingGroupType = typeof(CS.UnityEngine.Rendering.SortingGroup)
local Time = CS.UnityEngine.Time
local math = math
local EventTriggerListener = CS.War.UI.EventTriggerListener
local Input = CS.UnityEngine.Input

module("BaseSubset")

local Subset = newclass("BaseSubset", Component, true)

function Subset:ctor(selfType, root)
    Subset.__base.ctor(self, "BaseSubset")
end

function Subset:_InitSubSet()
    self.TimerList = {}
    self.ObjectList = {}
    self.EventList = {}
    self.TweenList = {}
    self.SpriteList = {}
    self.BtnClickList = {}

    self.LongBtnList = {}
    
end

function Subset:CreateSpriteAssetByPath(cardAssetType)
    if not cardAssetType then
        return
    end
    if self.SpriteList[cardAssetType] then
        return
    end
    self.SpriteList[cardAssetType] = sprite_asset.CSpriteAsset(cardAssetType)
    return self.SpriteList[cardAssetType]
end



--创建图集并赋值
function Subset:CreateSubSprite(funcName, img, pathName,onLoadedFunc)
    if not funcName or not img or not pathName then
        return
    end
    self.SpriteList[funcName] = self.SpriteList[funcName] or card_sprite_asset[funcName .. ""]()
    if not self.SpriteList[funcName] then
        log.Error("没有找到card_sprite_asset： 是否未定义对应的创建函数 name= ", funcName)
        return
    end
    self.SpriteList[funcName]:GetSprite(pathName, function(sp)
        if not util.IsObjNull(sp) and not util.IsObjNull(img) then
            img.sprite = sp
        end
        if type(onLoadedFunc)  == "function" then
            onLoadedFunc()
        end
    end,img)
end

--清理图集
function Subset:ClearSubSprite(funcName)
    if not funcName then
        return
    end
    if self.SpriteList[funcName] then
        self.SpriteList[funcName]:Dispose()
        self.SpriteList[funcName] = nil
    end
end

----------UI特效是控制层级问题
---@public 获取canvas相关的层级参数
---@return table return canvas的 sortingOrder + SortingGroupType的sortingOrder
function Subset:GetCanvasAndSortingGroup()
    local canvas = self.UIRoot:GetComponent(CanvasType)
    local sGroup = self.UIRoot:GetComponent(SortingGroupType)
    if not util.IsObjNull(canvas) and not util.IsObjNull(sGroup) then
        return canvas.sortingOrder, sGroup.sortingOrder, canvas, sGroup
    end
    return 0, 0, canvas, sGroup
end
---@public 设置canvas和sortingGroup的层级
---@param component table 要设置的根组件
---@param canvasSortingOrder number canvas的层级
---@param sortingGroupOrder number sortingGroup的层级
---@param path string 路径
function Subset:SetCanvasLayerAndSortGroup(component, canvasSortingOrder, sortingGroupOrder, path)
    --先添加一个WarGraphicRaycaster
    UIUtil.GetOrAddWarGraphicRaycaster(component, path)
    UIUtil.SetCanvasLayerAndSortGroup(component, canvasSortingOrder, sortingGroupOrder, path)
end
---@public 设置canvas和sortingGroup的激活与否
---@param component table 要设置的根组件
---@param enabled boolean 是否激活
---@param path string 路径
function Subset:SetEnableCanvasAndSortingGroup(component, enabled, path)
    UIUtil.SetEnableCanvasAndSortingGroup(component, canvasSortingOrder, sortingGroupOrder, path)
end


--leantween 动画
function Subset:LeanTween(lean, target, desValue, tim, callback, moveType)
    if not lean or not target or not desValue then
        return
    end
    tim = tim or 1
    local twn = LeanTween[lean .. ""](target, desValue, tim)
    if callback then
        twn:setOnComplete(callback)
    end
    if moveType then
        twn:setEase(LeanTweenType[moveType .. ""])
    end
    table.insert(self.TweenList, twn)
    return twn
end

--清理leantwwn 动画
function Subset:LeanCancel(twn)
    if not twn then
        return
    end
    if twn.uniqueId and self.TweenList[twn] then
        LeanTween.cancel(twn.uniqueId)
        self.TweenList[twn] = nil
    end
end

function Subset:RegisterEvent(name, func, triggerDelay, delayType)
    --[[if not self.EventList[name] then
        self.EventList[name] = {}
    end]]

    if not self.EventList[name] then
        self.EventList[name] = func
    else
        local originListOrFunc = self.EventList[name]
        if type(originListOrFunc) == "function" then
            self.EventList[name] = { originListOrFunc }
            table.insert(self.EventList[name], func)
        else
            table.insert(originListOrFunc, func)
        end
    end
    event.Register(name, func, triggerDelay, delayType)
end

--创建计时器
function Subset:CreateTimer(time, callback)
    local id = util.IntervalCall(time, callback)
    self.TimerList[id] = id
    return id
end

--创建延迟定时器
function Subset:CreateDelayTimer(time, callback)
    local id = util.DelayCallOnce(time, callback)
    self.TimerList[id] = id
    return id
end


--移除计时器
function Subset:RemoveTimer(id)
    util.RemoveDelayCall(id)
    if self.TimerList[id] then
        self.TimerList[id] = nil
    end
end

--创建from-to动画，返回id
function Subset:Tween(from, to, tim, updateFunc, completeFunc, interval)
    local sTime = Time.realtimeSinceStartup
    local tTime = sTime + tim
    local id = util.DelayCallOnce(0, function()
        if tim <= 0 then
            if completeFunc then
                completeFunc()
            end
            return
        end
        local nowTime = Time.realtimeSinceStartup
        if nowTime >= tTime then
            if completeFunc then
                completeFunc()
            end
        else
            local t = math.lerp(sTime, tTime, (nowTime - sTime) / tim)-- (nowTime - sTime) / tim
            if updateFunc then
                return updateFunc(from + (to - from) * t) or interval or 0
            end
            if completeFunc then
                completeFunc()
            end
        end
    end)
    self.TimerList[id] = id
    return id
end


--移除subset中所有计时器
function Subset:RemoveAllTimer(id)
    for i, v in pairs(self.TimerList or {}) do
        if v then
            util.RemoveDelayCall(v)
        end
    end
    self.TimerList = {}
end

--注册子物体
function Subset:RegisterSubset(obj)
    table.insert(self.ObjectList, obj)
end

--注册子物体
function Subset:CreateSubset(obj)
    table.insert(self.ObjectList, obj)
    return obj
end

function Subset:AddBtnOnClick(btn, func)
    if util.IsObjNull(btn) or not func then
        return
    end
    if not self.BtnClickList[btn] then
        btn.onClick:RemoveAllListeners()
        btn.onClick:AddListener(func)
    end
    self.BtnClickList[btn] = true
    return btn
end
function Subset:RemoveBtnOnClick(btn)
    if util.IsObjNull(btn) then
        return
    end
    if self.BtnClickList[btn] then
        btn.onClick:RemoveAllListeners()
    end
    self.BtnClickList[btn] = nil
end

---@private 创建按钮长按
function Subset:CreateBtnLongClick(btn, func, longTime,triggerInterval)
    local timerFrame = 0.02 --计时器间隔
    local timerID = 0 --计时器ID
    local totalTime = 0
    longTime = longTime or 1 --长按时间
    triggerInterval = triggerInterval or 0.2 --触发间隔
    local longCountTime = 0 --累计时间

    local btnDownFun = function()
        timerID = self:CreateTimer(timerFrame, function()

            if not Input.anyKey then
                timerID = 0
                longCountTime = 0
                totalTime = 0
                return true
            end
            
            totalTime = totalTime + timerFrame
            if totalTime >= longTime then
                longCountTime = longCountTime + timerFrame
                if longCountTime >= triggerInterval then
                    longCountTime= longCountTime -triggerInterval

                    if func then
                        func()
                    end
                end
            end
        end)
    end

    local btnUpFun = function()
        self:RemoveTimer(timerID)
        timerID = 0
        longCountTime = 0
        totalTime = 0
    end

    local eventBtn = UIUtil.GetOrAddComponent(btn.transform, typeof(EventTriggerListener))
    
    eventBtn:onDown("+", btnDownFun)
    eventBtn:onUp("+", btnUpFun)

    self.LongBtnList[btn] = {
        eventBtn = eventBtn,
        btnDownFun = btnDownFun,
        btnUpFun = btnUpFun,
        timerID = timerID
    }
    return timerID
end

---@public 停止按钮长按
function Subset:StopBtnLongClick(btn)
    if self.LongBtnList[btn] then
        local btnUpFun = self.LongBtnList[btn].btnUpFun
        if btnUpFun then
            btnUpFun()
        end
    end
end

---@public 添加按钮长按
function Subset:AddBtnLongClick(btn, func, longTime,triggerInterval)
    if util.IsObjNull(btn) or not func then
        return
    end
    if not self.LongBtnList[btn] then
        self:CreateBtnLongClick(btn, func, longTime,triggerInterval)
    else
        self:RemoveBtnLongClick(btn)
        self:CreateBtnLongClick(btn, func, longTime,triggerInterval)
    end
end

---@public 移除按钮长按
function Subset:RemoveBtnLongClick(btn)
    if util.IsObjNull(btn) then
        return
    end
    if self.LongBtnList[btn] then
        local eventBtn = self.LongBtnList[btn].eventBtn
        local btnDownFun = self.LongBtnList[btn].btnDownFun
        local btnUpFun = self.LongBtnList[btn].btnUpFun

        if btnUpFun then
            btnUpFun()
        end
        
        if eventBtn then
            eventBtn:onDown("-", btnDownFun)
            eventBtn:onUp("-", btnUpFun)
        end
    end
    self.LongBtnList[btn] = nil
end


function Subset:DisposeAllSubset()
    for i, func in pairs(self.EventList) do
        if func and type(func) == "table" then
            for _, f in ipairs(func) do
                event.Unregister(i, f)
            end
        else
            event.Unregister(i, func)
        end
    end
    self.EventList = {}

    for i, v in pairs(self.TimerList) do
        if v then
            util.RemoveDelayCall(v)
        end
    end
    for i, v in ipairs(self.ObjectList) do
        if v then
            v:Dispose()
        end
    end
    for k, v in pairs(self.TweenList) do
        if v and v.uniqueId then
            LeanTween.cancel(v.uniqueId)
        end
    end
    self.TweenList = {}

    for k, v in pairs(self.SpriteList) do
        if v then
            v:Dispose()
        end
    end
    self.SpriteList = {}

    for btn, v in pairs(self.BtnClickList) do
        if util.IsObjNull(btn) then
            btn.onClick:RemoveAllListeners()
        end
    end
    self.BtnClickList = {}

    for btn, v in pairs(self.LongBtnList) do
        if util.IsObjNull(btn) then
            self:RemoveBtnLongClick(btn)
        end
    end
    self.LongBtnList = {}
end

function Subset:exportMethods()
    self:exportMethods_({
        "_InitSubSet",
        "DisposeAllSubset",
        "RegisterEvent",
        "CreateDelayTimer",
        "CreateTimer",
        "RemoveTimer",
        "RemoveAllTimer",
        "RegisterSubset",
        "CreateSubset",
        "LeanTween",
        "LeanCancel",
        "CreateSubSprite",
        "ClearSubSprite",
        "SetCanvasLayerAndSortGroup",
        "SetEnableCanvasAndSortingGroup",
        "GetCanvasAndSortingGroup",
        "CreateSpriteAssetByPath",
        "AddBtnOnClick",
        "RemoveBtnOnClick",
        "Tween",
        "AddBtnLongClick",
        "RemoveBtnLongClick",
        "CreateBtnLongClick",
        "StopBtnLongClick",
    })
    return self.target_
end
return Subset
