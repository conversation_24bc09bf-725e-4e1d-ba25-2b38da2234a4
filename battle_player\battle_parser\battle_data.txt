local math     = math
local print    = print
local pairs    = pairs
local tostring = tostring
local require  = require
local dump     = dump
local table = table
local tonumber = tonumber
local type = type
local ipairs = ipairs

local player_mgr = require "player_mgr"
local tbs_pb     = require "tbs_pb"
local const      = require "const"
local common_pb               = require "common_new_pb"
local event = require "event"

local Debug = CS.UnityEngine.Debug
local EpisodeDim = CS.War.Battle.EpisodeDim
local log = require "log"

module ("battle_data")

local heroByPos = {}
local heroById = {}
local weaponById = {}
local summonedByMasterID = {}
local summonedProp = {}
local summonedPropIndex = {}
--modelById = {}
euiptWeapon = {left = 0,right = 0}
PalSideType = 
{
    Unknown = 0,
    Left = 1,
    Right = 2,
}
local roleID
local lastSkillID = {}

leftHaloID = 0
rightHaloID = 0
stageType = 0
stageLv = 0
rewards = {}
lastRewards = {}
victory = false
skipBattle = false
attacker = 0
defender = 0
winner = 0
loser = 0
preLoadWeaponValue = 0
rightPreLoadWeaponValue = 0
roundTimes = 0
scores = 0
leftPals = {}
rightPals = {}
totalDieCount = 0
leftDieCount = 0
rightDieCount = 0
leftHp = 0
rightHp = 0
leftDmg = 0
rightDmg = 0
leftID = 0 --战场左侧的归属ID
rightID = 0 --战场右侧的归属ID
skillEffectScore = 0 --技能特效总分
ectypeID = 0 --装备副本ID
leftSummonedInitData = nil    --战场左侧召唤物初始状态（我方）
rightSummonedInitData = nil   --战场右侧召唤物初始状态（敌方）
isInSeriesBattle = false   --当前是否在连续战斗中

ReturnSelectFunc = nil   --返回设置阵容的回调

ShowMainPanel = false --是否需要展示主页面

local replaceHeroPalID = 0 --变身英雄实体id
local replaceModelPalID = 0 --变身后替身模型id

--战斗中变身英雄id信息
--key=英雄实体id（变身前英雄） value=变身后替換id
local replaceHeroIdData = {}

--变身英雄相关go引用，卸载时还原使用
local replaceHeroGos = {}

--变身英雄是否变身的状态
local replaceHeroState = {}

--变身英雄配置信息
--key=heroId(变身英雄id) --Hero配表
--value=replaceModelId（替身模型id） --module配表
local replaceHeroCfgDatas = nil

--buff移除时，显示卡牌模型的buffid列表
--吞噬技能使用
local showBodyBuffIds = nil

--buff添加移除时，复活模型的buffid列表
--复活技能使用
local reviveBuffIds = nil

-- 战斗元素高亮显示列表
local skillLightTargets = {}

---------------多战斗数据----------------start
--战报缓存，用于在多个同步播放的战斗，多战斗
--战斗结束后移除
battleReports = {}

--多战斗结束时间字典
--key:战斗类型
--value:结束时间（以servertime为基准）
multipleBattleEndTimes = {}
 --切出战斗时已播放时长
multipleBattleCutOutPlayTimes = {}
--切出战斗时间
multipleBattleCutOutTimes = {}

--连续战斗切出数据
multipleBattleSeriesPerTime = {}
multipleBattleSeriesTotalWave = {}
multipleBattleSeriesNextWaveStartTime = {}
multipleBattleSeriesCutOutWave = {}
--可切出的多战斗类型 见battle_switch_manager
-- multipleBattleTypes = {
--     [common_pb.Advance] = true,
--     [common_pb.TheTowerOfIllusion] = true,  --星际通缉
--     [common_pb.FactionType1] = true,        --阵营通缉1
--     [common_pb.FactionType2] = true,        --阵营通缉2
--     [common_pb.FactionType3] = true,        --阵营通缉3
--     [common_pb.FactionType4] = true,        --阵营通缉4
-- }
--设置战斗battle_data初始数据战报Id
ReportType_SetInitBatlleData = 10000
---------------多战斗数据-----------------end

function ClearBattleInfo()
    heroByPos = {}
    summonedProp = {}
    summonedPropIndex = {}
    summonedByMasterID = {}
    heroById = {}
    weaponById = {}
    lastSkillID = {}
    euiptWeapon = {left = 0,right = 0}
    leftHaloID = 0
    rightHaloID = 0
    adornIdList = {} --召唤兽的外观
    preLoadWeaponValue = 0
    rightPreLoadWeaponValue = 0
    SetRoundTimes(0)  
    ClearStatisticData()
    replaceHeroIdData = {}
    replaceHeroGos = {}
    replaceHeroState = {}
    enemyFaceID = 0       --敌人头像
    enemyFameID = 0       --敌人头像框
    isShowPower = false       --是否显示战力
    levelNum = ""             --当前关卡
    stageLevel = 0
end

function ClearReward()
    rewards = {}
    scores = 0  
end

function Clear()
    ClearBattleInfo()
    ClearReward()
    stageType = 0
    stageLv = 0
    victory = false
    ectypeID = 0
    ClearTopPowerData()
    ShowMainPanel =false
    ReturnSelectFunc = nil
end

function ResetSummonedData()
    for k, v in pairs(summonedByMasterID) do
        v.born = nil
        v.register = false
        v.deadRound = nil
    end
    lastSkillID = {}
end

function ClearStatisticData()
    leftPals = {}
    rightPals = {}
    totalDieCount = 0
    leftDieCount = 0
    rightDieCount = 0
    adornIdList = {} --召唤兽的外观
    leftHp = 0
    rightHp = 0
    leftDmg = 0
    rightDmg = 0
    leftID = 0
    rightID = 0
    skillEffectScore = 0
    leftSummonedInitData = nil
    rightSummonedInitData = nil
end

function SetRoundTimes(round)
    roundTimes = round
end

--获取变身英雄PalID
--key=变身英雄实体PalID（变身前英雄） value=变身后替身模型PalID（客户端虚拟id）
function GetReplaceHeroIdData()
    return replaceHeroIdData
end

--获取英雄变身后替身模型PalID
function GetReplaceModleIdBy(heroPalId)
    return replaceHeroIdData and replaceHeroIdData[heroPalId]
end

--设置变身英雄实体PalID，替身模型PalID（客户端虚拟id）
function SetReplaceHeroPalIdData(sourceId, replaceModelId)
    replaceHeroIdData[sourceId] = replaceModelId
    --replaceHeroPalID = sourceId
    --replaceModelPalID = replaceModelId
end

--设置变身英雄相关模型
function SetReplaceHeroGo(palId, go)
    if replaceHeroGos then 
        replaceHeroGos[palId] = go
    end
end

--获得变身英雄相关模型go，卸载时还原使用
function GetReplaceHeroGoBy(palId)
    return replaceHeroGos and replaceHeroGos[palId]
end


function ReplaceHeroChangeBody(palId)

end

function GetReplaceHeroState()
    return 
end

function ClearReplaceHeroGo()
    replaceHeroGos = {}
end

function GetSummoneds()
    local summoneds = {}
    for k,v in pairs(heroById) do
        if k >= 100 then
            table.insert(summoneds, v)
        end
    end
    table.sort(summoneds, function(v1, v2)
        return v1.palID < v2.palID
    end)
    return summoneds
end

function GetSummonedByMasterId(masterID)    
    return summonedByMasterID[masterID]
end

function GetSummonedList()
    return summonedByMasterID
end

function GetSummonedPropByType(palID, _type)
    return summonedProp[palID][_type]
end

function GetHeroByID(palId)
    return heroById[palId]
end

function GetHeroByPos(pos)
    return heroByPos[pos]
end

function GetHeroByCfgId(cfgID)
    local k,v
    for k,v in pairs(heroById) do
        if v.heroID == cfgID then
            return v
        end
    end
end

function GetHeroModuleCfg(palId)
    local hero = heroById[palId]
    if hero ~= nil then
        return hero.moduleCfg
    end
    return nil
end

function UpdateHeroMaxHp(palID, maxhp)
    local heroData = GetHeroByID(palID)
    if heroData then
        heroData.maxhp = maxhp
    end
end

function UpdateHeroHp(palID, hp)
    local heroData = GetHeroByID(palID)
    if heroData then
        heroData.hp = hp
    end
end

function UpdateHeroDefence(palID, defence)
    local heroData = GetHeroByID(palID)
    if heroData then
        heroData.defence = defence
    end
end

-- BuildActor 创建战斗中布局使用的英雄时调用此接口向 Lua 创建英雄管理容器
-- realmaxhp 多个回合扣血后，真正血量上限比原先上限低
function RegisterHero(palID, cfgID, pos, hp, mp, maxhp, maxmp, lv, heroCfg, starLv, moduleCfg, realmaxhp,talent,skinID,defence,isLargeHp,isTrialHero,artifactID)
    -- print("qsy_yxsy:[battle_data]RegisterHero>>>>",pos,isTrialHero)
    if skinID and skinID>0 then
        local game_scheme = require "game_scheme"
        local skin_cfg = game_scheme:Skin_0(skinID)
        if skin_cfg then
            local ModelID = skin_cfg.ModelID
            local modelCfg = game_scheme:Modul_0(ModelID)
            if modelCfg then
                moduleCfg = modelCfg
            end
        end
    end
    local hero = {
        palID = palID,
        heroID = cfgID,
        --在服务器下发的英雄队列中位置，战斗中根据 BattleActorManager 配置的 nodes 位置，以此 pos 为下标查找实际布局的位置
        pos = pos,
        hp = hp,
        mp = mp,
        isLargeHp = isLargeHp,
        maxhp = maxhp,
        realmaxhp= realmaxhp, -- or maxhp,
        maxmp = maxmp,
        lv = lv,
        cfg = heroCfg,
        moduleCfg = moduleCfg,
        numProp = {
            starLv = starLv,
            lv = lv,
            Type = heroCfg and heroCfg.type or 1,
            Career = heroCfg and heroCfg.profession or 1, 
        },
        statistics = {
            damage = 0,     -- 输出
            heal = 0,       -- 治疗
            hp = hp,        -- 剩余血量
            maxhp = maxhp,  -- 最大血量
            isDead = false, -- 死亡
            hurt = 0,--承伤
            sp = 0, -- 护盾
        },
        talentSkills = talent,
        skinID = skinID,
        defence = defence,
        isTrialHero = isTrialHero,
        artifactID = artifactID,
    }
    --print("<color=#00ff00>添加英雄信息</color>pos:", pos)
    --dump(hero)
    heroByPos[pos] = hero
    heroById[palID] = hero
end

function RegisterWeapon(palID, cfgID, pos, hp, mp, maxhp, maxmp, lv, weaponCfg, realmaxhp)
    local weapon = {
        palID = palID,
        heroID = cfgID,
        pos = pos,
        hp = hp or 0,
        mp = mp,
        maxhp = maxhp,
        realmaxhp= realmaxhp or maxhp,
        maxmp = maxmp,
        lv = lv,
        --icon = weaponCfg.icon,
        weaponCfg = weaponCfg,
        numProp = {
            starLv = 0,
            lv = lv,
            Type = 0,
            Career = 0, 
        },
        statistics = {
            damage = 0,     -- 输出
            heal = 0,       -- 治疗
            hp = hp or 0,        -- 剩余血量
            maxhp = maxhp,  -- 最大血量
            isDead = false, -- 死亡
            hurt = 0,--承伤
            sp = 0, --护盾
        }
    }

    heroByPos[pos] = weapon
    heroById[palID] = weapon
    weaponById[palID] = {}

    ------print("注册武器，",palID,pos)
    if pos == 13 then
        euiptWeapon.left = palID
        preLoadWeaponValue = mp--初始能量
    elseif pos == 14 then
        euiptWeapon.right = palID
        rightPreLoadWeaponValue = mp
    end
end

local adornIdList = {}        --召唤兽的外观
function SetWeaponAdorn(palID,adornId)
    adornIdList[palID] = adornId
end

function GetWeaponAdorn(palID)
    return adornIdList[palID]
end

function GetLastSkillID(palID)
    return lastSkillID[palID]
end

function SetLastSkillID(palID, v)
    lastSkillID[palID] = v
end

function ResetLastSkillID()
    lastSkillID = {}
end

function UpdateSummonedProp(palID, _type, value, idx)
    if(palID == nil or summonedPropIndex == nil or not summonedPropIndex[palID])then
        return
    end
    summonedPropIndex[palID][_type] = summonedPropIndex[palID][_type] or 0
    summonedPropIndex[palID][_type] = summonedPropIndex[palID][_type] + 1
    local index = idx or summonedPropIndex[palID][_type]
    summonedProp[palID][_type] = summonedProp[palID][_type] or {}
    summonedProp[palID][_type][index] = value
end

function SetLastSummonedProp(palID, _type, value)
    if summonedProp and summonedProp[palID] and summonedProp[palID][_type] then
        local len = #summonedProp[palID][_type]
        summonedProp[palID][_type][len] = value
    end
end

function RegisterSummoned(palID, masterID, cfgID, hp, mp, maxhp, maxmp, lv, summonedCfg, moduleCfg, icon, realmaxhp, damage, dir, deadRound, isInitData,skin)
    -- print("qsy_yxsy:[battle_data]RegisterSummoned>>>>>")
    local propCfg = {}
    local specialNumber = summonedCfg.SpecialNumber
    local util = require "util"
    local specialNumberArr = util.SplitString(specialNumber, ";")
    for k,v in ipairs(specialNumberArr) do
        local t = util.SplitString(v, "#", tonumber)
        propCfg[t[1]] = t[2]
    end
    local summoned = {
        palID = palID,
        register = true,
        heroID = cfgID,
        pos = palID,
        hp = hp or 0,
        mp = mp,
        maxhp = maxhp,
        realmaxhp= realmaxhp or maxhp,
        maxmp = maxmp,
        lv = lv,
        icon = icon,
        summonedCfg = summonedCfg,
        moduleCfg = moduleCfg,
        masterID = masterID,
        dir = dir, 
        propCfg = propCfg,
        deadRound = deadRound,
        numProp = {
            starLv = 0,
            lv = lv,
            Type = 0,
            Career = 0, 
        },
        statistics = {
            damage = damage or 0,     -- 输出
            heal = 0,       -- 治疗
            hp = hp or 0,        -- 剩余血量
            maxhp = maxhp,  -- 最大血量
            isDead = deadRound and deadRound > 0 or false, -- 死亡 --召唤物死亡特殊处理，召唤物可能在切入时是死亡状态
            hurt = 0,--承伤
            sp = 0, --护盾
        },
        skinID = skin
    }

    if isInitData then 
        --如果召唤物主人已死亡，则不显示召唤物初始状态
        local master = GetHeroByID(masterID)
        if master and master.statistics.isDead == false then
            local summonedInitData = { palID = palID,
                register = true,
                heroID = cfgID,
                pos = palID,
                masterID = masterID,
                dir = dir, 
                deadRound = deadRound,
                icon = icon,
                isInitData = true,
                summonedCfg = summonedCfg,
                statistics = {
                    isDead = deadRound and deadRound > 0 or false, -- 死亡 --召唤物死亡特殊处理，召唤物可能在切入时是死亡状态
                }
                }    --战场左侧召唤物初始状态（我方）

            if dir == 1 then 
                leftSummonedInitData = summonedInitData   --战场左侧召唤物初始状态（我方）
            elseif dir == 2 then
                rightSummonedInitData = summonedInitData    --战场左侧召唤物初始状态（敌方）
            end
        end
    end

    heroByPos[palID] = summoned
    heroById[palID] = summoned
    summonedByMasterID[masterID] = summoned 
    summonedProp[palID] = summonedProp[palID] or {}
    summonedPropIndex[palID] = summonedPropIndex[palID] or {}
end

function IsSummoned(palID)
    if palID >= 100 then
        return true
    end
    return false
end

function IsWeapon(palID)
    local weaponInfo = weaponById[palID]
    return (weaponInfo ~= nil)
end

function GetRoleID()
    if roleID == nil then
        return player_mgr.GetPlayerRoleID()
    else
        return roleID
    end
end

function SetRoleID(id)
    -- 代码中很少有调用此接口设置 id，但 IsVictory 接口会使用 GetRoleID 接口来判断是否胜利,使用其他人战报时,胜利状态判断错误
    -- 所以逻辑上有 bug？
    roleID = id
end

function GetMyRoldID()
    return player_mgr.GetPlayerRoleID()
end

---------------------statistics----------------------
function count_heal(palId, number)
    local hero = heroById[palId]
    if hero then
        hero.statistics.heal = hero.statistics.heal + number
    end
end

-- palId 造成的伤害
function count_damage(palId, number)
    local hero = heroById[palId]
    if hero then
        if hero.masterID then
            hero = heroById[hero.masterID]
        end
        hero.statistics.damage = hero.statistics.damage + number
    end
end

function count_dead(palId, dead)
    local hero = heroById[palId]
    if hero then
        hero.statistics.isDead = hero.statistics.isDead or dead
    end
end

function count_hp(palId, deltaHp)
    if deltaHp ~= 0 then
        local hero = heroById[palId]
        if hero and hero.statistics.hp and hero.statistics.maxhp then
            hero.statistics.hp = math.max(0, math.min(hero.statistics.hp + deltaHp, hero.statistics.maxhp))
            -- Debug.LogError("血量变化："..tostring(deltaHp)..",剩余血量:"..tostring(hero.statistics.hp)..",targets:"..tostring(palId))
        end
    end
end

function count_hurt(palId, number)
    local hero = heroById[palId]
    if hero then
        hero.statistics.hurt =  hero.statistics.hurt + number
    end
end

function count_sp(palId, deltaSp)
    if deltaSp == 0 then
        return
    end
    local hero = heroById[palId]
    if hero then
        hero.statistics.sp = math.max(0, hero.statistics.sp + deltaSp)
    end
end

function GetSp(palId)
    local hero = heroById[palId]
    if hero then
        return hero.statistics.sp
    else
        return 0
    end
end

function clamp_hp(palId, deltaHp)
    if deltaHp ~= 0 then
        local hero = heroById[palId]
        if hero and hero.statistics.hp and deltaHp and (hero.realmaxhp or hero.maxhp) then
            --print("clamp_hp1111111realmaxhp", hero.realmaxhp, "maxhp", hero.maxhp, "deltaHp", deltaHp)
            local d = math.min(hero.statistics.hp+deltaHp,hero.realmaxhp or hero.maxhp)
            if d  == (hero.realmaxhp or hero.maxhp) then
----                print("Clamp_hp",palId,d,hero.realmaxhp,hero.maxhp,d-hero.statistics.hp)
            end 
            deltaHp = d-hero.statistics.hp
        end
    end
    return deltaHp
end

--!!!!!!! isLargeHp 特殊处理，传给C#值固定为false，在lua端，当isLargeHp为true时，所有hp，maxHp，和伤害恢复都应该乘0.001，保证显示正确
function handleLargeHp(palId,deltaHp)
    if deltaHp and deltaHp ~= 0 then
        local hero = heroById[palId]
        if hero and hero.statistics.hp and hero.isLargeHp then
            return deltaHp * 0.001
        end
    end
    return deltaHp
end
------------------------------------------------------

function IsVictory()
    return victory
end

function GetHeros()
    return heroByPos
end

function GetBattleRound()
	return roundTimes
end

function GetPalSide(palID)
    local palData
    if leftPals then
        for i=1,#leftPals do
            palData = leftPals[i]
            if palData.palID == palID then
                return PalSideType.Left
            end
        end
    end
    if rightPals then
        for i=1,#rightPals do
            palData = rightPals[i]
            if palData.palID == palID then
                return PalSideType.Right
            end
        end
    end
    return PalSideType.Unknown
end

function OnPalDie(palId)
    totalDieCount = totalDieCount + 1
    local palSide = GetPalSide(palId)
    if palSide == PalSideType.Left then
        leftDieCount = leftDieCount + 1
    elseif palSide == PalSideType.Right then
        rightDieCount = rightDieCount + 1
    end
end

function OnPalDieReport(palDieReport)
    if palDieReport == nil then
        return
    end
    OnPalDie(palDieReport.target)
end

function GetTotalDieCount()
    return totalDieCount
end

function GetLeftDieCount()
    return leftDieCount
end

function GetRightDieCount()
    return rightDieCount
end

-- palReportContext : tbs.proto -> message PalReportContext
function BuildActorData(palReportContext)
    local hp = palReportContext.props[tbs_pb.EnBuildProp_Hp + 1]
    local palSide = GetPalSide(palReportContext.palID)
    if palSide == PalSideType.Left then
        leftHp = leftHp + hp
    elseif palSide == PalSideType.Right then
        rightHp = rightHp + hp
    end
end

-- numericReport : tbs.proto -> message NumericReport
function OnNumericReport(numericReport)
    if numericReport.numType == tbs_pb.NumericType_HP and numericReport.numbers < 0 then
        -- numericReport.palID 造成的伤害
        local palSide = GetPalSide(numericReport.palID)
        if palSide == PalSideType.Left then
            leftDmg = leftDmg + numericReport.numbers
        elseif palSide == PalSideType.Right then
            rightDmg = rightDmg + numericReport.numbers
        end
    end
end

--失败方伤害
function GetLoserDmg()
    local dmgValue = 0
    if loser == attacker then
        dmgValue = leftDmg
    elseif loser == defender then
        dmgValue = rightDmg
    end
    if dmgValue < 0 then
        dmgValue = dmgValue * -1
    end
    return dmgValue
end

--胜利方伤害
function GetWinnerDmp()
    local dmgValue = 0
    if winner == attacker then
        dmgValue = leftDmg
    elseif winner == defender then
        dmgValue = rightDmg
    end
    if dmgValue < 0 then
        dmgValue = dmgValue * -1
    end
    return dmgValue
end

--失败方血量
function GetLoserHP()
    if loser == attacker then
        return leftHp
    elseif loser == defender then
        return rightHp
    end
    return 0
end

--胜利方血量
function GetWinnerHP()
    if winner == attacker then
        return leftHp
    elseif winner == defender then
        return rightHp
    end
    return 0
end

function AppendSkillEffectScore(changedValue)
    if const.Open_Battle_SkillEffect_Log then
        Debug.LogError("AppendSkillEffectScore:"..changedValue)
    end
    skillEffectScore = skillEffectScore + changedValue
end

--技能特效总分
function GetSkillEffectScore()
    return skillEffectScore
end

function OnGameOverReport(msg)
    if msg:HasField("equipEctype") then
        ectypeID = msg.equipEctype.ectypeID
    end
end

--装备副本，时空穿越 关卡id
function GetEquipEctypeID()
    return ectypeID
end

-- 战斗元素高亮显示处理

function BeginSkill()
    -- Debug.Log("Dim Effect, BeginSkill")
    skillLightTargets = {}
end

function EndSkill()
    -- Debug.Log("Dim Effect, EndSkill")
    skillLightTargets = {}
end

function AddLightTarget( targetId, operation, segmentIndex )
    -- Debug.Log("Dim Effect, operation:"..tostring(operation)..", light id:"..targetId..",segmentIndex:"..segmentIndex)

    if operation == EpisodeDim.Operation.Light then
        skillLightTargets[targetId] = true
    else
        skillLightTargets[targetId] = nil
    end
end

function GetLightTargetList()
    local targetList = {}
    local k,v
    local idx = 0
    for k,v in pairs(skillLightTargets) do
        idx = idx + 1
        targetList[idx] = k
    end

    return targetList
end

--返回变身英雄信息字典
--key=heroId(变身英雄id) --Hero配表
--value=replaceModelId（替身模型id） --module配表
function GetReplaceHeroCfgDatas()
    if not replaceHeroCfgDatas then 
        replaceHeroCfgDatas = {}
        --读取变身英雄配置
        local heroIds = {}
        local replaceModelIds = {}
        local game_scheme = require "game_scheme"
        local cfg = game_scheme:InitBattleProp_0(612)
		if cfg then
            heroIds = cfg.szParam.data
        end
        cfg = game_scheme:InitBattleProp_0(613)
		if cfg then
            replaceModelIds = cfg.szParam.data
        end
        local replaceModelId = 0 
        for key, heroId in pairs(heroIds) do
            replaceModelId = replaceModelIds[key]
            replaceHeroCfgDatas[heroId] = replaceModelId
        end
    end

    return replaceHeroCfgDatas
end

--是否为显示卡牌模型buffid（该buff移除时）
function IsShowBodyBuffId(buffId)
    if not showBodyBuffIds then
        local game_scheme = require "game_scheme"
        local cfg = game_scheme:InitBattleProp_0(614)
        if cfg then
            showBodyBuffIds = cfg.szParam.data
        end
    end

    for index, value in pairs(showBodyBuffIds) do
        if value == buffId then
            return true
        end
    end
    return false
end

--是否为复活的buffid（该buff移除时）
function IsReviveBuffId(buffId)
    if not reviveBuffIds then
        local game_scheme = require "game_scheme"
        local cfg = game_scheme:InitBattleProp_0(969)
        if cfg then
            reviveBuffIds = cfg.szParam.data
        else
            reviveBuffIds = {3415}
        end
        -- log.Error("IsReviveBuffId是否为复活的buffid:", buffId, reviveBuffIds[0])
        -- dump(reviveBuffIds)
    end

    for index, value in pairs(reviveBuffIds) do
        if value == buffId then
            -- log.Error("true是否为复活的buffid:", buffId)
            return true
        end
    end
    return false
end

function OnSceneDestroy()
    --多战斗数据清理
    battleReports = {}
    multipleBattleEndTimes = {}
    multipleBattleCutOutPlayTimes = {}
    multipleBattleCutOutTimes = {}
	multipleBattleSeriesPerTime = {}
	multipleBattleSeriesTotalWave = {}
	multipleBattleSeriesNextWaveStartTime = {}
	multipleBattleSeriesCutOutWave = {}

    local battle_manager = require "battle_manager"
    local player = battle_manager.GetBattlePlayer()
    if player and player.ClearMultipleBattleDatas then
        player:ClearMultipleBattleDatas()
        print("清理C#缓存多战斗数据")
    end
end

--region 上阵站位 行列处理

---description 根据下标，获取行和列,下标从1开始
--index，1 col，0 row:，0
--index，2 col，1 row:，0
--index，3 col，0 row:，1
--index，4 col，1 row:，1
--index，5 col，2 row:，1
function GetRowAndColByIndex(index)
    if not index then
        log.Error("index为空,请排查逻辑!!!!!!!!!")
        return 0,0
    end
    if index == 0 then
        log.Error("下标得从1开始，请排查逻辑!!!!!!!!!")
    end
    if index ==6 then
        log.Error("下标最大为5，请排查逻辑!!!!!!!!!")
    end
    local row = 0
    if index > 2 then
        row = 1
    end
    local col = index - row * 2 - 1
    return row,col
end


---description 根据行和列，返回下标，下标从0开始
function GetIndexByRowAndCol(row,col)
    if not row or not col then
        log.Error("row或col为空，请排查逻辑")
        return 0
    end
    if row ==0 and col ==2 then
        log.Error("col 为2的行,row得是1,请排查逻辑")
    end
    local index = row == 0 and col + 1 or col + 3
    return   index - 1 
end
---description 根据行和列，返回下标，下标从1开始
function GetIndexByRowAndColFromOne(row,col)
    local index = GetIndexByRowAndCol(row,col)
    return index + 1
end


---@description 服务器BattleLineUp 转 我方阵容，注意这里依赖的是我方有的英雄
---@return table hData 值为heroEntity下标从0开始，跟selectedHero的下标一致
function LineUpToHeroData(lineUp)
    local hData = {}
    for i, v in ipairs(lineUp.palList) do
        local heroEntity = player_mgr.GetPalPartDataBySid(v.palId)
        if heroEntity then
            local key = GetIndexByRowAndCol(v.row,v.col)
            hData[key] = heroEntity
        end
    end
    return hData
end


--endregion

event.Register(event.SCENE_DESTROY, OnSceneDestroy)

function GetCopyCache()
    local data = {}
    data.stageType = stageType
    data.stageLv = stageLv
    data.rewards = rewards
    data.victory = victory
    data.scores = scores
    return data
end

local enemyFaceID = 0       --敌人头像
local enemyFameID = 0       --敌人头像框
local isShowPower = false       --是否显示战力
local levelNum = ""             --当前关卡
local enemyPower = 0
local myPower = 0
local enemyFaceIDNotByCfg = 0
--设置顶部战力信息
function SetTopPowerData(isShow, levelNumber, curEnemyFaceID, curEnemyFameID,enemyFaceIDNotCfg)
    isShowPower = isShow or false
    levelNum = levelNumber or ""
    enemyFaceID = curEnemyFaceID or 0
    enemyFameID = curEnemyFameID or 0
    enemyFaceIDNotByCfg = enemyFaceIDNotCfg or 0
end

--设置双方个人信息
local myInfo = {}
local enemyInfo = {}
local isCancelOpenBattleAni = false
function SetEnemyInfo(info)
    enemyInfo = info

    local alliance_mgr = require"alliance_mgr"
    local gw_home_soldier_data = require "gw_home_soldier_data"
    myInfo = {
        maxSoldierLv = gw_home_soldier_data.GetCampusSoldierLevel() or 1,
        playerName = player_mgr.GetRoleName(),
        leagueShortName = alliance_mgr.GetUserAllianceShortName(),	--联盟简称,
    }
end

function SetSoldierNum(mySoldierNum, enemySoldierNum)
    myInfo.soldierNum = mySoldierNum
    enemyInfo.soldierNum = enemyInfo.soldierNum or enemySoldierNum
end

function GetBattlePlayerInfo()
    return myInfo, enemyInfo
end

--获取顶部战力信息
function GetTopPowerData()
    local data = {
        isShowPower = isShowPower,
        levelNum = levelNum,
        enemyFaceID = enemyFaceID,
        enemyFameID = enemyFameID,
        enemyPower = enemyPower,
        myPower = myPower,
        enemyFaceIDNotByCfg = enemyFaceIDNotByCfg,
    }
    return data
end

function ClearTopPowerData()
    isShowPower = false
    levelNum = ""
    enemyFaceID =  0
    enemyFameID =  0
    enemyFaceIDNotByCfg =  0
    enemyPower =  0
    myPower =  0

    myInfo = {}
    enemyInfo = {}
    isCancelOpenBattleAni = false
end

stageLevel = 0
--设置战斗关卡
function SetStageLevel(stageLv)
    stageLevel = stageLv
end 

--获取战斗关卡
function GetStageLevel()
    return stageLevel or 0
end

function SetReturnSelectFunc(callback)
    ReturnSelectFunc = callback
end

--设置敌人战斗力
function SetEnemyPower(power)
    enemyPower = power
end

--设置自己战力
function SetMyPower(power)
    myPower = power
end

--设置是否取消开战动画
function SetIsCancelOpenBattleAni(isCancel)
    isCancelOpenBattleAni = isCancel
end

--获取是否取消开战动画
function GetIsCancelOpenBattleAni()
    return isCancelOpenBattleAni
end


tipsCfg = {
    -- {langID = 文字显示lang值， iconIndex = 图标显示， eventID = 点击事件处理}
    {langID = 32055, BG = 0, iconIndex = 9,jumpTextColor = "#795B27",eventID = 9,paramsName={"limitgiftType"}, index = 10},--英雄碎片礼包
    {langID = 631005, BG = 0, iconIndex = 0, jumpTextColor = "#795B27", eventID = 1, paramsName = {}, index = 0},  -- 获得UR英雄(首冲)
    {langID = 631017, BG = 0, iconIndex = 3, jumpTextColor = "#795B27", eventID = 3, paramsName = {}, index = 3},  -- 招募英雄
    {langID = 631006, BG = 0, iconIndex = 1, jumpTextColor = "#795B27", eventID = 2, paramsName = {"changeLevelHeroSid"}, index = 1},  -- 升级英雄
    {langID = 631007, BG = 1, iconIndex = 2, jumpTextColor = "#2A6384", eventID = 2, paramsName = {"changeStarHeroSid"}, index = 2},  -- 升阶英雄
    {langID = 631008, BG = 1, iconIndex = 4, jumpTextColor = "#2A6384", eventID = 4, paramsName = {"changeSkillHeroSid"}, index = 4},  -- 技能升级
    {langID = 631009, BG = 1, iconIndex = 5, jumpTextColor = "#2A6384", eventID = 2, paramsName = {"changeEquipHeroSid"}, index = 5},  -- 穿戴装备
    {langID = 631010, BG = 1, iconIndex = 6, jumpTextColor = "#2A6384", eventID = 5, paramsName = {"changeEquipLevelHeroSid", "changeEquipEntity"}, index = 6},  -- 装备升级
    {langID = 631011, BG = 1, iconIndex = 7, jumpTextColor = "#2A6384", eventID = 6, paramsName = {}, index = 7},  -- 神兽升级
    {langID = 631012, BG = 0, iconIndex = 0, jumpTextColor = "#795B27", eventID = 7, paramsName = {}, index = 8},  -- 获得UR英雄(新英雄)
    {langID = 631013, BG = 1, iconIndex = 8, jumpTextColor = "#2A6384", eventID = 8, paramsName = {}, index = 9},  -- 调整阵容(主线）
}