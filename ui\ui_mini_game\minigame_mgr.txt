local print = print
local require = require
local type = type
local ipairs = ipairs
local GWG = GWG
local log = require "log"
local net_route = require "net_route"
local dump = dump
local ui_window_mgr = require "ui_window_mgr"
local DateTime = CS.System.DateTime
local util = require "util"
--local File = CS.System.IO.File
local event = require "event"
local net_minigame_module = require "net_minigame_module"
local math = math
local UnityEngine = CS.UnityEngine
local table = table
local pairs = pairs
local asset_loader = require "asset_loader"
local minigame_buff_mgr = require "minigame_buff_mgr"
local coroutine = coroutine
local game_scheme = require "game_scheme"
local IOSystem = require "iosystem_load"
local tonumber = tonumber
local player_mgr = require "player_mgr"
local MainLoop = CS.War.Script.MainLoop
local Time = CS.UnityEngine.Time
module("minigame_mgr")

local StateType = {
    NONE = 0,    --未开始
    LOADING = 1, --加载中
    PLAYING = 2, --正在游戏中
    PAUSE = 3,   --暂停
    FINISH = 4,  --结束
    SUSPEND = 5, --挂起状态，游戏场景不显示，退出小游戏界面，但是核心资源不销毁
}
local cysoldierssortie_MsgCode = {
    STAGE_SUCCESS = 1,  --关卡成功
    STAGE_FAIL = 2,     --关卡失败
    STAGE_RESET = 3,    --重置关卡
    STAGE_PAUSE = 4,    --暂停
    STAGE_CONTINUE = 5, --继续
    STAGE_EXIT = 6,     --退出关卡
}

local hookLevelCfg = nil
local enter_scr = nil
local curMiniGameLevel = 1
local IsOpenMiniGame = false
local level_scr = nil
local miniLevelCfg = nil
local startGameTime = nil
local endGameTime = 0
local moveActionMap = {}
local IsGetResult = false
local inMiniGameTimer = nil
local miniGamePrepState = 0
local isStartGame = false
local isGameFinish = true
local allHeroHurtMap = {}
local allHeroHitHurtMap = {}
local heroPower = 0
local gameResult = false
local IsLoadingShow = false
local oldPlayerId
local CloseResultTimeIntervalCall
local loadResState = 0 --游戏加载资源进度
local rewardLevelTimer
local rewardLevelTimerIndex = 0
local TargetWorldPos
local ScreenTargetPosCache = {}
local rewardId = nil --关卡额外奖励id
local isPlayReward = false
--- 记录当前活动关卡模式
local CampaignFlag = false
--- 活动关卡模式携带的参数
local CampaignParams = nil
--- 活动关卡模式的关卡索引
local CampaignLvIndex = nil

function setCurMiniGame(_curMiniGameLevel)
    enter_scr = require "cysoldierssortie_enter"
    curMiniGameLevel = _curMiniGameLevel
    IsOpenMiniGame = true
    startGameTime = nil
    isStartGame = false
    isGameFinish = false
    rewardLevelTimer = nil
    rewardLevelTimerIndex = 0
    heroPower = 0
    loadResState = 0
    TargetWorldPos = nil
    rewardId = nil
    isPlayReward = false
end

--游戏是否已完成获已结束
function IsGameFinish()
    return isGameFinish
end

function SetLevelScr(_level_scr)
    level_scr = _level_scr
    miniLevelCfg = level_scr.MiniLevelCfg
    minigame_buff_mgr.SetLevelScr(_level_scr)
end

function IsUseSpecialSoldier()
    if level_scr then
        return level_scr:IsUseSpecialSoldier()
    end
    return false
end

---@return boolean 是否开启神兽系统
function IsOpenDrone()
    if level_scr then
        return level_scr:IsOpenDrone()
    end
    return true
end

--游戏开始移动
function SetStartGame()
    if isStartGame then
        return
    end
    -- 活动关卡不需要动态难度
    if not CampaignFlag then
        minigame_buff_mgr.GlobalDBuffAttribute() --初始化降低buff
    end
    allHeroHurtMap = {}
    allHeroHitHurtMap = {}
    -- log.Error("sss")

    --加入伤害统计
    local heroList = level_scr.playerLua:GetCHeroList()
    if #heroList > 0 then
        for i, v in ipairs(heroList) do
            AddHeroHurt(v, 0, v.slotIndex)
            AddHeroHitHurt(v, 0, v.slotIndex)
        end
    end
    if level_scr.playerLua._playerDrone then
        AddHeroHurt(level_scr.playerLua._playerDrone, 0, 6)
        AddHeroHitHurt(level_scr.playerLua._playerDrone, 0, 6)
    end

    level_scr.levelMgr:SetLevelChallengeDebuff(CampaignFlag and 0 or minigame_buff_mgr.GetGlobalDBuffAttribute())
    startGameTime = DateTime.Now
    minigame_buff_mgr.CheckCondition(nil, minigame_buff_mgr.ConditionType.Start)
    minigame_buff_mgr.CheckCondition(nil, minigame_buff_mgr.ConditionType.EnemySizeMax, level_scr:GetCurEnemyCount())
    moveActionMap = {}
    isStartGame = true
end

-- 用于在特定情况下重新执行游戏开始时的buff
function ReSetStartGame()
    minigame_buff_mgr.CheckCondition(nil, minigame_buff_mgr.ConditionType.Start)
    minigame_buff_mgr.CheckCondition(nil, minigame_buff_mgr.ConditionType.EnemySizeMax, level_scr:GetCurEnemyCount())
end

function GetIsStartGame()
    return isStartGame
end

function InputMgrOnBeginDrag(beginDragData)
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        ui_window_mgr:GetWindowObj("ui_mini_game_info"):FingerActionInfo()
    end
    SetStartGame()
end

function StartRewardLevelMode()
    if level_scr then
        rewardLevelTimer = miniLevelCfg.time or 30
        rewardLevelTimerIndex = 0
        level_scr:PlayEnterRewardLevelFX()
        level_scr:OnStartCoroutine(function()
            coroutine.yield(UnityEngine.WaitForSeconds(2))
            if level_scr then
                rewardLevelTimerIndex = 0
                level_scr:StartRewardLevel()
            end
        end)
        if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
            ui_window_mgr:GetWindowObj("ui_mini_game_info"):ShowRewardLevelMode()
        end
    end
end

function GetRewardLevelModeTimer()
    if rewardLevelTimer and rewardLevelTimer > rewardLevelTimerIndex then
        return rewardLevelTimer - rewardLevelTimerIndex
    end
    return 0
end

function GetRewardLevelModeTimerSlider()
    if rewardLevelTimer and rewardLevelTimer > rewardLevelTimerIndex then
        return 1 - rewardLevelTimerIndex / rewardLevelTimer
    end
    return 0
end

function getCurMiniGameLevel()
    return curMiniGameLevel
end

--- 刷新关卡ID，目前用于首充活动的无尽模式
---@param lvID number 关卡ID
function RefreshCurMiniGameLevel(lvID)
    curMiniGameLevel = lvID
end

function IsQuickFightFunc()
    local prop = game_scheme:InitBattleProp_0(8283)
    local openHookLevel = 17
    if prop then
        openHookLevel = game_scheme:InitBattleProp_0(8283).szParam.data[0]
        openHookLevel = tonumber(openHookLevel)
    end

    local curHookLevel = GetHookLevelId()
    if curHookLevel >= openHookLevel then
        return true
    end
    return false
end

-- 外部通过此方法判断是否开启小游戏
function getIsOpenMiniGame()
    return IsOpenMiniGame
end

function OnSceneDestroy()
    MinigameExit()
    curMiniGameLevel = 1
    IsOpenMiniGame = false
end

event.Register(event.SCENE_DESTROY, OnSceneDestroy)
event.Register(event.SCENE_DESTROY_NEW, OnSceneDestroy)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, OnSceneDestroy)

function LoadingTimeOut()
    if IsOpenMiniGame then
        EventReport_Timeout(curMiniGameLevel)
        if hookLevelCfg and miniLevelCfg then
            --根据战力跳过
            local levelPor = miniLevelCfg.LevelPower or 0
            local gw_hero_data = require "gw_hero_data"
            local heroPower2 = gw_hero_data.GetHaveHeroPowerMax(5)
            if levelPor > 0 and heroPower2 >= levelPor then
                SendWinMsg()
                event.Trigger(event.BATTLE_RESULT_CLOSE)
                event.Trigger(event.MINIGAME_CLOSE, GetGameResult())
            end
        end
    end

    MinigameExit()
end

function SetLoadResState(resState)
    loadResState = resState
end

function LoadingShow()
    if not IsOpenMiniGame then
        return
    end
    if IsLoadingShow then
        return
    end
    IsLoadingShow = true

    InitOpenGame(curMiniGameLevel)
end

---断线重连失败时返回登陆界面时调用
function DestoryTinyGame()
    isForceOut = true
    print("主场景销毁事件，小游戏销毁")
end

event.Register(event.SCENE_DESTROY, DestoryTinyGame)

function IsSceneValid()
    return true
end

function SetAllStopAction()
    level_scr:OnStartCoroutine(function()
        coroutine.yield(UnityEngine.WaitForSeconds(0.1))
        if not level_scr then
            return
        end
        local newEnemyList = level_scr:GetAllEnemy()
        if newEnemyList and #newEnemyList > 1 then
            for i, v in ipairs(newEnemyList) do
                local enemyItem = v
                enemyItem:StopMove()
            end
        end
    end)
end

function BroadcastMsg(msgCode)
    if msgCode == cysoldierssortie_MsgCode.STAGE_SUCCESS then
        MiniGameFinish(true)
    elseif msgCode == cysoldierssortie_MsgCode.STAGE_FAIL then
        MiniGameFinish(false)
    end
end

function SetHeroPower(hero_power)
    heroPower = hero_power
end

function GetGameResult()
    return gameResult
end

function MiniGameWinResult()
    EventReport_Success(curMiniGameLevel)
    EventReport_Success_Hook()
    if miniLevelCfg and miniLevelCfg.Type == 3 then
        local rescueData = {
            isWin = true,
            endGameTime = endGameTime,
            curMiniGameLevel = curMiniGameLevel,
        }
        ui_window_mgr:ShowModule("ui_storm_rescue_result", nil, nil, rescueData)
    elseif miniLevelCfg.LevelMode == 1 or miniLevelCfg.LevelMode == 2 then
        local win = ui_window_mgr:ShowModule("ui_mini_game_result")
        win:SetInputParam(curMiniGameLevel, hookLevelCfg, level_scr, true, endGameTime)
        -- 首充副本战斗胜利结算 （正常模式）
    elseif miniLevelCfg.Type == 4 then
        --优化需求 先执行一键收集 在限时奖励弹出动画页面 最后显示原战斗胜利页面
        CollectAllReward()
        local data = {
            rewardList = GetCollectDropDatas(),
            showType = 1, --1:普通模式 2:无尽模式
        }
        local closeCallBack = function()
            local win = ui_window_mgr:ShowModule("ui_mini_game_battle_victory")
            win:SetInputParam(GetHeroHurt(), GetHeroHitHurt(), true)
        end
        ui_window_mgr:ShowModule("ui_hero_first_charge_mini_win_result", nil, closeCallBack, data)
        --首充副本战斗胜利结算（无尽模式）
    elseif miniLevelCfg.Type == 5 then
        CollectAllReward()
        --优化需求 根据玩家是否至少通关一关 显示不同的结算页面
        local data = {
            rewardList = GetCollectDropDatas(),
            showType = 2,
            breakType = GetEndlessBreakType()
        }
        local closeCallBack = function()
            MiniGameClose()
            local result = GetGameResult()
            event.Trigger(event.MINIGAME_CLOSE, result)
            event.Trigger(event.BATTLE_RESULT_CLOSE)
        end
        ui_window_mgr:ShowModule("ui_hero_first_charge_mini_win_result", nil, closeCallBack, data)
    else
        local win = ui_window_mgr:ShowModule("ui_mini_game_battle_victory")
        win:SetInputParam(GetHeroHurt(), GetHeroHitHurt())
    end
end

function MiniGameFinish(iswin)
    ShowWeaponAnim(false)
    gameResult = iswin
    SetAllStopAction()

    if startGameTime then
        endGameTime = math.floor((DateTime.Now - startGameTime).TotalSeconds)
    end
    local gw_popups_trigger_mgr = require "gw_popups_trigger_mgr"
    gw_popups_trigger_mgr.TriggerLevel(iswin)
    isGameFinish = true
    -- 活动关卡不参与动态难度逻辑
    if not CampaignFlag then
        minigame_buff_mgr.GlobalDBuffAttribute(iswin, hookLevelCfg)
    end
    -- 标记是小游戏入口内的小游戏：士兵突击
    local tinyGameFlag = miniLevelCfg and miniLevelCfg.Type == 2
    if iswin then
        SetIsResult(false)
        SendWinMsg()
        if tinyGameFlag then
            EventReport_Success(curMiniGameLevel)
            -- 这是士兵突击的特殊逻辑，不发结算请求
            local gw_independent_game_mgr = require "gw_independent_game_mgr"
            gw_independent_game_mgr.SoldiersSortieMiniGameFinish(curMiniGameLevel, true)
        else
            if level_scr and level_scr.RewardLevelMode then
                if miniLevelCfg then
                    local rewards = miniLevelCfg.Reward.data
                    local reward_Id = rewards[#rewards]
                    ui_window_mgr:ShowModule("ui_mini_game_reward_result", nil, nil, { RewardID = reward_Id })
                end
            else
                MiniGameWinResult()
            end
        end
    else
        SetIsResult(true)
        if tinyGameFlag then
            EventReport_Fail(curMiniGameLevel)
            -- 这是士兵突击的特殊逻辑，不发结算请求
            local gw_independent_game_mgr = require "gw_independent_game_mgr"
            gw_independent_game_mgr.SoldiersSortieMiniGameFinish(curMiniGameLevel, false)
        else
            EventReport_Fail(curMiniGameLevel)
            EventReport_Fail_Hook()

            if miniLevelCfg and miniLevelCfg.Type == 3 then
                --周日活动结算界面
                local rescueData = {
                    isWin = false,
                    endGameTime = endGameTime,
                    curMiniGameLevel = curMiniGameLevel,
                }
                ui_window_mgr:ShowModule("ui_storm_rescue_result", nil, nil, rescueData)
            elseif miniLevelCfg and miniLevelCfg.Type == 5 then
                --首充英雄活动 无尽模式 即英雄全部死亡 触发结算页面
                CollectAllReward()
                --优化需求 根据玩家是否至少通关一关 显示不同的结算页面
                local data = {
                    rewardList = GetCollectDropDatas(),
                    showType = 2,
                }
                local closeCallBack = function()
                    MiniGameClose()
                    local result = GetGameResult()
                    event.Trigger(event.MINIGAME_CLOSE, result)
                    event.Trigger(event.BATTLE_RESULT_CLOSE)
                end
                ui_window_mgr:ShowModule("ui_hero_first_charge_mini_win_result", nil, closeCallBack, data)
            else
                if miniLevelCfg.LevelMode == 1 or miniLevelCfg.LevelMode == 2 then
                    local win = ui_window_mgr:ShowModule("ui_mini_game_result")
                    win:SetInputParam(curMiniGameLevel, hookLevelCfg, level_scr, iswin, endGameTime)
                else
                    local win = ui_window_mgr:ShowModule("ui_mini_game_battle_defeat")
                    win:SetInputParam(GetHeroHurt(), GetHeroHitHurt())
                end
            end
        end
    end
    OnCloseResultTime(iswin)
end

event.Register(event.CLOSE_REWARD_RESULT, MiniGameWinResult)

function SendWinMsg()
    print("==========Send_HOOKLEVELCHALLENGE_RESULT_REQ==========")
    -- 小游戏类型为0时必须与hookLevel表中的miniLevel相匹配，其它类型可不在hookLevel中配置，当hookID发0时 服务器会根据miniLevel中的Type走不同的结算逻辑
    -- FIXME 【新怪物攻城】接入后要修改此处逻辑
    if miniLevelCfg.Type == 1 or hookLevelCfg then
        local checkPointID = hookLevelCfg and hookLevelCfg.checkPointID or 0
        if level_scr and level_scr.RewardLevelMode then
            local rateSliderValue = level_scr.rewardLevelGetCount
            local winSize = level_scr.rewardLevelCount
            local valueNum = 0
            if winSize and winSize > 0 then
                valueNum = tonumber(rateSliderValue / winSize * 100)
            end
            net_minigame_module.Send_HOOKLEVELCHALLENGE_RESULT_REQ(checkPointID, 3, miniLevelCfg.MiniLevelID, valueNum)
        else
            net_minigame_module.Send_HOOKLEVELCHALLENGE_RESULT_REQ(checkPointID, 3, miniLevelCfg.MiniLevelID)
        end
    end
end

function SetIsResult(isT, reward)
    IsGetResult = isT
    rewardId = reward
    -- rewardId=800472
    SendGetResult()

    if IsGetResult and hookLevelCfg then
        if ui_window_mgr:IsModuleShown("ui_mini_game_result") then
            ui_window_mgr:GetWindowObj("ui_mini_game_result"):UpdataWinInfo(true)
        end
    end
end

function SendGetResult()
    if isGameFinish and IsGetResult == false and hookLevelCfg and level_scr then
        level_scr:OnStartCoroutine(function()
            coroutine.yield(UnityEngine.WaitForSeconds(3))
            if isGameFinish and level_scr and IsGetResult == false then
                SendWinMsg()
            end
            SendGetResult()
        end)
    end
end

function GetIsResult()
    return IsGetResult
end

function GetRewardId()
    --if not isPlayReward then
    --     rewardId=nil
    --end
    --return rewardId
    return nil
end

function SetPlayReward(isT)
    isPlayReward = isT
end

function GetHookLevelId()
    if hookLevelCfg then
        return hookLevelCfg.checkNumber
    end
    return curMiniGameLevel
end

function GetShowLevelId()
    if miniLevelCfg.DisplayLevelID and miniLevelCfg.DisplayLevelID > 0 then
        return miniLevelCfg.DisplayLevelID
    end
    if hookLevelCfg then
        return hookLevelCfg.checkNumber
    end
    return curMiniGameLevel
end

function GetHookLevelCfg()
    return hookLevelCfg
end

--跳过当前关卡
function JumpMiniGame()
    CloseResultTimeIntervalCall = nil
    if hookLevelCfg and miniLevelCfg and miniLevelCfg.SkipLevel == 1 then
        EventReport_Skip(curMiniGameLevel, miniLevelCfg.Type)
        EventReport_Success_Hook()

        SendWinMsg()
        local win = ui_window_mgr:ShowModule("ui_mini_game_result") --ui_mini_game_battle_victory
        win:SetInputParam(curMiniGameLevel, hookLevelCfg, level_scr, true, endGameTime)
    else
        MiniGameClose()
    end
end

function OpenMiniGame(gameLevel, levelCfg, battleStageType)
    if IsOpenMiniGame then
        return
    end
    CampaignFlag = false
    CampaignParams = nil
    CampaignLvIndex = nil
    RegisterBackgroundListener(true)
    IsOpenMiniGame = true
    curMiniGameLevel = gameLevel
    local ui_loading = require "ui_loading"
    ui_loading.SetBattleStageType(battleStageType or 1)
    hookLevelCfg = levelCfg
    if not hookLevelCfg then
        hookLevelCfg = GetHookLevelCfgByMiniGameId(gameLevel)
    end
    local miniLvCfg
    if hookLevelCfg then
        miniLvCfg = game_scheme:MiniLevel_0(hookLevelCfg.miniLevel)
    end

    if gameLevel >= 600099 then
        local cameraMoveX, cameraMoveY = 53, 47
        if miniLvCfg == nil then
            miniLvCfg = game_scheme:MiniLevel_0(gameLevel)
            if miniLvCfg then
                cameraMoveX = miniLvCfg.cameraMove.data[0] / 100 or 53
                cameraMoveY = miniLvCfg.cameraMove.data[1] / 100 or 47
            end
        end
        if miniLvCfg and miniLvCfg.selectHeroMode == 2 then
            local data = {}
            data.saveFunc = function(_, teamdata)
                ui_window_mgr:UnloadModule("ui_new_hero_select_panel_minigame")
                ui_window_mgr:ShowModule("ui_loading")
            end
            ui_window_mgr:ShowModule("ui_new_hero_select_panel_minigame", nil, nil, data)
        else
            event.Trigger(event.KINGSHOT_HOME_MODE_START, cameraMoveX, cameraMoveY)

            local minigame_mgr = require "minigame_mgr"
            minigame_mgr.LoadingShow()

            local sand_ui_event_define = require "sand_ui_event_define"
            event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
        end
    else
        ui_window_mgr:ShowModule("ui_loading")
    end


    if not oldPlayerId or oldPlayerId ~= player_mgr.GetPlayerRoleID() then
        --加载本地buff数据
        oldPlayerId = player_mgr.GetPlayerRoleID()
        minigame_buff_mgr.LoadGlobalDBuffAttribute()
    end
end

---@return boolean 是否是活动关卡
function IsCampaign()
    return CampaignFlag
end

--- 活动入口，兼容多关卡模式
---@param lvIndex 活动关卡下标
---@param ... 其他参数：首充活动(关卡ID数组、MiniGameLevelControl.csv的ID数组)
function OpenMiniGame_Campaign(lvIndex, ...)
    if IsOpenMiniGame then
        return
    end
    hookLevelCfg = nil
    curMiniGameLevel = 0
    local lvIDs, ctrlIDs = ...
    if lvIDs and ctrlIDs then
        local curLvID = lvIDs[lvIndex]
        if curLvID then
            curMiniGameLevel = curLvID
        end
    end
    CampaignLvIndex = lvIndex
    CampaignFlag = true
    CampaignParams = { ... }
    RegisterBackgroundListener(true)
    IsOpenMiniGame = true
    local ui_loading = require "ui_loading"
    ui_loading.SetBattleStageType(1)
    ui_window_mgr:ShowModule("ui_loading")
end

function GetHookLevelCfgByMiniGameId(gameLevel)
    --获取每条表数据
    local num = game_scheme:HookLevel_nums()
    for i = 0, num - 1 do
        local cfg = game_scheme:HookLevel(i)
        if cfg.miniLevel and cfg.miniLevel == gameLevel then
            return cfg
        end
    end
    return nil
end

function GetAttackLimit()
    if not hookLevelCfg then
        return -1, -1
    end
    if hookLevelCfg.AttackLimit and hookLevelCfg.FirstAttackLimit then
        return hookLevelCfg.AttackLimit, hookLevelCfg.FirstAttackLimit
    end
    return -1, -1
end

local loaders = {}
local Loadercallbacks = {}
function mgrload(res, callback)
    local clist = Loadercallbacks[res] or {}
    table.insert(clist, callback)
    Loadercallbacks[res] = clist
    local loader = loaders[res] or asset_loader(res, "cysoldierssortie")
    loaders[res] = loader
    local r = res

    loader:load(function(obj, debug)
        local list = Loadercallbacks[r]
        Loadercallbacks[r] = nil
        if not getIsOpenMiniGame() then
            return
        end
        for rr, cb in pairs(list) do
            cb(obj.asset, debug)
        end
        -- print("tiny_mgr mgrload obj.asset:",obj.asset ,"||||cb:_",callback)
        -- callback(obj.asset,debug)
    end, true)
end

local oldUpdateTimer = nil
function UpdateTimer()
    if not oldUpdateTimer then
        oldUpdateTimer = DateTime.Now
    end
    if isStartGame then
        --  IOSystem.TimerUpdateFun()
        SetWeaponProgress()
    end
    if (DateTime.Now - oldUpdateTimer).TotalSeconds >= 1 then
        --log.Error("aaaaaa=="..(DateTime.Now-oldUpdateTimer).TotalSeconds)
        minigame_buff_mgr.UpdateTimer()
        oldUpdateTimer = DateTime.Now
        if isStartGame and rewardLevelTimer then
            rewardLevelTimerIndex = rewardLevelTimerIndex + 1
            if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
                ui_window_mgr:GetWindowObj("ui_mini_game_info"):UpdataRewardLevelTimer()
            end
            if GetRewardLevelModeTimer() == 0 then
                level_scr:GameOver(true)
            end
        end
    end
end

function SetNotCloseResultTime()
    CloseResultTimeIntervalCall = nil
end

function OnCloseResultTime(iswin)
    local timerSize = game_scheme:InitBattleProp_0(8304)
    if not timerSize then
        return
    end
    timerSize = timerSize.szParam.data[0]
    if not timerSize or timerSize == 0 then
        return
    end
    CloseResultTimeIntervalCall = true
    level_scr:OnStartCoroutine(function()
        coroutine.yield(UnityEngine.WaitForSeconds(timerSize))
        if not IsGetResult then
            return
        end
        if not CloseResultTimeIntervalCall or not level_scr then
            return
        end
        CloseResultTimeIntervalCall = nil
        if ui_window_mgr:IsModuleShown("ui_mini_game_result") then
            ui_window_mgr:UnloadModule("ui_mini_game_result")
        end
        if ui_window_mgr:IsModuleShown("ui_mini_game_battle_victory") then
            ui_window_mgr:UnloadModule("ui_mini_game_battle_victory")
        end
        if ui_window_mgr:IsModuleShown("ui_mini_game_battle_defeat") then
            ui_window_mgr:UnloadModule("ui_mini_game_battle_defeat")
        end
        if ui_window_mgr:IsModuleShown("ui_common_mini_game_result") then
            ui_window_mgr:UnloadModule("ui_common_mini_game_result")
        end
        if ui_window_mgr:IsModuleShown("ui_storm_rescue_result") then
            ui_window_mgr:UnloadModule("ui_storm_rescue_result")
        end
        if not iswin then
            if hookLevelCfg and miniLevelCfg and miniLevelCfg.SkipLevel == 1 then
                JumpMiniGame()
                OnCloseResultTime(true)
                return
            end
        end
        event.Trigger(event.BATTLE_RESULT_CLOSE)
        event.Trigger(event.MINIGAME_CLOSE, GetGameResult())
        MiniGameClose()
        local unforced_guide_mgr = require "unforced_guide_mgr"
        if unforced_guide_mgr.GetCurGuide() == 10 then
            unforced_guide_mgr.CloseGuide()
        end
    end)
end

---@param lvID integer 关卡ID
---@return integer 获取游戏类型
function GetMiniLevelTypeByLvID(lvID)
    local type = 0
    local tmpLvConfig = game_scheme:MiniLevel_0(lvID)
    if tmpLvConfig then
        type = tmpLvConfig.Type
    end
    return type
end

--- 进入士兵突击系列游戏
---@param levelid integer 关卡ID
---@param lvType integer 关卡类型
function EventReport_Enter(lvID, lvType)
    if lvType == nil then
        lvType = GetMiniLevelTypeByLvID(lvID)
    end
    local data = {
        type = 1,
        LevelID = lvID or 0,
        mini_game_name = "cysoldierssortie",
        SubType = lvType,
    }
    event.Trigger(event.GAME_EVENT_REPORT, "MiniGame_Enter", data)
end

---@param levelid integer 关卡ID
---@param lvType integer 关卡类型
function EventReport_Timeout(lvID, lvType)
    if lvType == nil then
        lvType = GetMiniLevelTypeByLvID(lvID)
    end
    local data = {
        type = 1,
        LevelID = lvID or 0,
        --0未开始,1开始加载neegameentry,2加载neegameentry完成,3开始加载SpriteAtlas,4加载SpriteAtlas完成,5开始加载level.prefab，6加载level.prefab完成
        LoadResState = loadResState or 0,
        mini_game_name = "cysoldierssortie",
        SubType = lvType,
    }
    event.Trigger(event.GAME_EVENT_REPORT, "MiniGame_TimeOut", data)
end

---@param levelid integer 关卡ID
---@param lvType integer 关卡类型
function EventReport_Success(lvID, lvType)
    if lvType == nil then
        lvType = GetMiniLevelTypeByLvID(lvID)
    end
    local reportData = {
        type = 1,
        StayTime = endGameTime or 0,
        LevelID = lvID or 0,
        mini_game_name = "cysoldierssortie",
        SubType = lvType,
    }
    event.Trigger(event.GAME_EVENT_REPORT, "MiniGame_Success", reportData)
end

---@param levelid integer 关卡ID
---@param lvType integer 关卡类型
function EventReport_Skip(lvID, lvType)
    lvID = lvID or curMiniGameLevel
    if lvType == nil then
        lvType = GetMiniLevelTypeByLvID(lvID)
    end
    local data = {
        type = 1,
        LevelID = lvID or 0,
        mini_game_name = "cysoldierssortie",
        SubType = lvType,
    }
    event.Trigger(event.GAME_EVENT_REPORT, "MiniGame_Skip", data)
end

---@param levelid integer 关卡ID
---@param lvType integer 关卡类型
---@param reasonType integer 失败原因 1=正常失败，2=主动退出
function EventReport_Fail(lvID, lvType, reasonType)
    lvID = lvID or curMiniGameLevel
    if lvType == nil then
        lvType = GetMiniLevelTypeByLvID(lvID)
    end
    local reportData = {
        type = 1,
        StayTime = endGameTime or 0,
        LevelID = lvID or 0,
        mini_game_name = "cysoldierssortie",
        SubType = lvType,
        Fail_reason = reasonType or 1,
    }
    event.Trigger(event.GAME_EVENT_REPORT, "MiniGame_Fail", reportData)
end

---@param levelid integer 关卡ID
---@param lvType integer 关卡类型
---@param retryReason integer 重试原因 1是结算界面 2是关卡内重置按钮（只有华佗小游戏）
function EventReport_Retry(lvID, lvType, retryReason)
    lvID = lvID or curMiniGameLevel
    if lvType == nil then
        lvType = GetMiniLevelTypeByLvID(lvID)
    end
    local data = {
        type = 1,
        LevelID = lvID or 0,
        mini_game_name = "cysoldierssortie",
        SubType = lvType,
        Retry_reason = retryReason or 1,
    }
    event.Trigger(event.GAME_EVENT_REPORT, "MiniGame_Retry", data)
end

---@param reasonType integer 失败原因 1=正常失败，2=主动退出
function EventReport_Fail_Hook(reasonType)
    if not hookLevelCfg then
        return
    end
    local reportMsg = {
        Level_id = GetHookLevelId(), --关卡ID
        Hook_type = 1,               --关卡类型
        Team_power = heroPower,      --队伍总战斗力
        Cost_time = endGameTime,     --耗时
        battle_round = 0,            --回合数(跳过战斗回合数是0)
        Fail_reason = reasonType or 1,
    }
    event.Trigger(event.GAME_EVENT_REPORT, "Hook_fail", reportMsg)
end

function EventReport_Success_Hook()
    if not hookLevelCfg then
        return
    end
    local reportMsg = {
        Level_id = GetHookLevelId(), --关卡ID
        Hook_type = 1,               --关卡类型
        Team_power = heroPower,      --队伍总战斗力
        Cost_time = endGameTime,     --耗时
        battle_round = 0,            --回合数(跳过战斗回合数是0)
    }
    event.Trigger(event.GAME_EVENT_REPORT, "Hook_success", reportMsg)
    local q1sdk = require "q1sdk"
    q1sdk.AdjustAndFirebaseReport("battle_level", { value = reportMsg.Level_id .. "" })
end

--- 进入的是主线关卡，要额外打点
function EventReport_Enter_Hook()
    if not hookLevelCfg then
        return
    end
    local reportMsg = {
        Level_id = GetHookLevelId(), --关卡ID
        Hook_type = 1,               --关卡类型
        -- Team_power = heroPower,      --队伍总战斗力
        -- Cost_time = endGameTime,     --耗时
        battle_round = 0, --回合数(跳过战斗回合数是0)
    }
    event.Trigger(event.GAME_EVENT_REPORT, "Hook_enter", reportMsg)
end

-- 主线小游戏进入
function InitOpenGame(levelid)
    minigame_buff_mgr.ClsAll()
    setCurMiniGame(levelid)
    enter_scr.isSuspend = nil
    print("开启士兵突击小游戏" .. levelid)

    EventReport_Enter(levelid)
    EventReport_Enter_Hook()

    enter_scr:Open(CampaignFlag and CampaignLvIndex or levelid, mgrload, mgrMessager, CampaignParams)
    UpdataGameStateInfo()
    if inMiniGameTimer then
        util.RemoveDelayCall(inMiniGameTimer)
        inMiniGameTimer = nil
    end

    inMiniGameTimer = util.IntervalCall(0, function()
        local State = enter_scr:State()
        -- print("State:",State,"tiny_mgr.state.PLAYING:",tiny_mgr.state.PLAYING,"State == tiny_mgr.state.PLAYING:",tostring(State)==tostring(tiny_mgr.state.PLAYING))
        if miniGamePrepState == State then
            return
        end
        UpdataGameStateInfo()
        miniGamePrepState = State
        --if State == tiny_mgr.state.PLAYING and callback then
        --    callback()
        --end
    end)
    SetIsRunPuzzleGame(true)
end

local isRunPuzzleGame = false
function GetIsRunPuzzleGame()
    return isRunPuzzleGame
end

function SetIsRunPuzzleGame(bool)
    isRunPuzzleGame = bool
end

function UpdataGameStateInfo()
    if not enter_scr then
        return
    end
    local State = enter_scr:State()
    if State == StateType.LOADING then
        -- ui_window_mgr:ShowModule("ui_loading")
    elseif State == StateType.PLAYING then
        event.Trigger(event.NEW_XYX_START_LEVEL, levelid)
        event.Trigger(event.CHECK_FULL_SCREEN_SHIELD)

        if not ui_window_mgr:IsModuleShown("new_hook_scene") then
            ui_window_mgr:ShowModule("ui_casual_game_common")
        end
    elseif State == StateType.FINISH or State == StateType.SUSPEND then
        ui_window_mgr:UnloadModule("ui_casual_game_common")
    end
end

function SetState(_state)
    if not enter_scr then
        return
    end
    if enter_scr.SetState and _state then
        enter_scr.SetState(_state)
    end
end

function GetMainCam()
    if not level_scr then
        return nil
    end
    return level_scr:GetMainCam()
end

function OpenUiMiniGameInfo()
    if not ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        local win = ui_window_mgr:ShowModule("ui_mini_game_info")
        win:SetInputParam(level_scr, miniLevelCfg)
    end
end

function UpdataUiMiniGameInfo()
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        ui_window_mgr:GetWindowObj("ui_mini_game_info"):UpdataInfo()
    end
end

function ShowUiMiniGameInfo()
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        ui_window_mgr:GetWindowObj("ui_mini_game_info"):ShowTaskInfo()
    end
end

--- 获取到奖励箱子的位置，世界坐标
function GetRewardBoxPos()
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        return ui_window_mgr:GetWindowObj("ui_mini_game_info"):GetRewardBoxPos()
    end
    return nil
end

--- 游戏中打开界面时需要触发
function TriggerOpenPanelInGame()
    if level_scr then
        level_scr:TriggerOpenPanelInGame()
    end
end

--- 游戏中关闭界面时需要触发
function TriggerClosePanelInGame()
    if level_scr then
        level_scr:TriggerClosePanelInGame()
    end
end

--- 无尽模式的主动退出结算时调用
function TriggerEndlessOver()
    if level_scr then
        level_scr:TriggerEndlessOver()
    end
end

---@return number 获取当前关卡类型 4=首充活动正常模式，5=首充活动无尽模式
function GetMiniLevelCfgType()
    if miniLevelCfg then
        return miniLevelCfg.Type
    end
    return 0
end

--- 一键拾取
function CollectAllReward()
    if level_scr then
        level_scr:CollectAllDrops()
    end
end

---@return integer 获取无尽模式主动跳出的类型 1=战力不足，2=关卡未解锁
function GetEndlessBreakType()
    if level_scr then
        return level_scr:GetEndlessBreakType()
    end
    return nil
end

---@return cysoldierssortie_dropData[] 获取已获得的掉落物品(已经通关的波次的凋落物)
---@return cysoldierssortie_dropData[] 获取已拾取的掉落物品(当前波次拾取的，但是未结算)
function GetCollectDropDatas()
    if level_scr then
        return level_scr:GetCollectDropDatas()
    end
    return nil, nil
end

---@return integer 当前最大波次
---@return integer 通过波次= 当前最大波次-进入的波次
function GetEndlessWaveData()
    if level_scr then
        return level_scr:GetEndlessWaveData()
    end
    return 0, 0
end

function ShowEndlessRunProcess(show)
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        return ui_window_mgr:GetWindowObj("ui_mini_game_info"):ShowEndlessRunProcess(show)
    end
end

function ShowEndlessWaitProcess(show, str, process)
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        return ui_window_mgr:GetWindowObj("ui_mini_game_info"):ShowEndlessWaitProcess(show, str, process)
    end
end

function ShowEndlessNextAttent(show)
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        return ui_window_mgr:GetWindowObj("ui_mini_game_info"):ShowEndlessNextAttent(show)
    end
end

function ShowEndlessSpeedline(show)
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        return ui_window_mgr:GetWindowObj("ui_mini_game_info"):ShowEndlessSpeedLine(show)
    end
end

function ShowRateSlider(show)
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        return ui_window_mgr:GetWindowObj("ui_mini_game_info"):ShowRateSlider(show)
    end
end

function GetRewardLevelIconPos()
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        return ui_window_mgr:GetWindowObj("ui_mini_game_info"):GetRewardLevelIconPos()
    end
    return nil
end

function GetRewardLevelIconPosWS()
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        return ui_window_mgr:GetWindowObj("ui_mini_game_info"):GetRewardLevelIconPosWS()
    end
    return nil
end

function GetRewardLevelIconWorldPos(cam, camMove)
    if TargetWorldPos then
        return TargetWorldPos
    end
    if camMove then
        local targetPosWS = GetRewardLevelIconPosWS()
        TargetWorldPos = targetPosWS
        return TargetWorldPos
    end
    local screenTargetPos = GetRewardLevelIconPos()
    if not screenTargetPos then
        return nil
    end
    ScreenTargetPosCache.x = screenTargetPos.x
    ScreenTargetPosCache.y = screenTargetPos.y
    ScreenTargetPosCache.z = 10
    local worldPos = cam:ScreenToWorldPoint(ScreenTargetPosCache)
    TargetWorldPos = worldPos
    return TargetWorldPos
end

function ShowWeaponAnim(isT)
    if not isStartGame or isGameFinish then
        return
    end
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        ui_window_mgr:GetWindowObj("ui_mini_game_info"):ShowWeaponAnim(isT)
    end
end

local WeaponProgressMaxTimer
local cWeaponProgressTimer
function SetWeaponProgress(time)
    if not isStartGame then
        return
    end
    local Progress = 1
    if time then
        WeaponProgressMaxTimer = time
        cWeaponProgressTimer = DateTime.Now
        -- WeaponProgressMaxTimer=time*
        -- if (DateTime.Now-oldUpdateTimer).TotalSeconds>=1 then
    else
        if not WeaponProgressMaxTimer then
            return
        end
        Progress = (DateTime.Now - cWeaponProgressTimer).TotalSeconds / WeaponProgressMaxTimer
        Progress = Progress * 100
        if Progress > 100 then
            Progress = 100
        end
    end
    --local Progress=
    if ui_window_mgr:IsModuleShown("ui_mini_game_info") then
        ui_window_mgr:GetWindowObj("ui_mini_game_info"):SetWeaponProgress(Progress)
    end
end

function OnCloseLight()
    if enter_scr then
        enter_scr:CloseLight()
    end
end

function OnOpenLight()
    if enter_scr then
        enter_scr:OpenLight()
    end
end

function MiniGameClose()
    RegisterBackgroundListener(false)
    oldUpdateTimer = nil
    isGameFinish = true
    allHeroHurtMap = {}
    minigame_buff_mgr.ClsAll()
    print("==========MinigameClose==========")
    -- local tiny_mgr = require "tiny_mgr"
    -- tiny_mgr.Close(false)

    if inMiniGameTimer then
        util.RemoveDelayCall(inMiniGameTimer)
        inMiniGameTimer = nil
    end
    if CloseResultTimeIntervalCall then
        CloseResultTimeIntervalCall = nil
    end

    miniGamePrepState = 0
    -- TinyMusicMgr.TinyMusicClose()
    if ui_window_mgr:IsModuleShown("ui_casual_game_common") then
        ui_window_mgr:UnloadModule("ui_casual_game_common")
    end
    if ui_window_mgr:IsModuleShown("ui_mini_select_hero") then
        ui_window_mgr:UnloadModule("ui_mini_select_hero")
    end
    ui_window_mgr:UnloadModule("ui_mini_game_info")
    isStartGame = false
    IsLoadingShow = false
    startGameTime = nil
    curMiniGameLevel = 1
    IsOpenMiniGame = false
    if enter_scr then
        enter_scr:Close()
        enter_scr = nil
    end
    if loaders then
        for i, v in pairs(loaders) do
            v:Dispose()
        end
        loaders = {}
    end
    Loadercallbacks = {}
    hookLevelCfg = nil
    -- local sand_ui_event_define = require "sand_ui_event_define"
    --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
    event.Trigger(event.KINGSHOT_HOME_MODE_END)
end

function MinigameExit()
    --print("==========MinigameExit==========")
    MiniGameClose()
end

event.Register(event.MINIGAME_EXIT, MinigameExit)
--function onshow()
--     --print("==========onshow==========")
--end
--event.Register("MINI_GAME_MESSAGE_onshow",onshow)

function LOADSUCCESS()
    ui_window_mgr:UnloadModule("ui_loading")
    log.Warning("Minigame load success: " .. Time.realtimeSinceStartup)
end

event.Register(event.MINIGAME_SCENE_LOAD_SUCCESS, LOADSUCCESS)

--统计英雄伤害
function AddHeroHurt(character, hurt, index)
    if not character then
        return
    end
    if character._unit_type ~= 4 and character._unit_type ~= 6 then
        return
    end
    --dump(character._heroId,"AddHeroHurt222")
    -- dump(hurt,"AddHeroHurt")
    local heroHurtItem = allHeroHurtMap[character]
    if not heroHurtItem then
        heroHurtItem = {}
        allHeroHurtMap[character] = heroHurtItem
        heroHurtItem.character = character
        -- heroHurtItem.cfg=game_scheme:MiniUnit_0(character._unitID)
        heroHurtItem.heroID = character._heroId
        heroHurtItem.isHero = true
        -- 是否是试用
        heroHurtItem.isTrial = character.heroData and character.heroData.isTrial or false
        heroHurtItem.heroData = character.heroData
        if character._isDrone then
            heroHurtItem.isHero = false
            local droneData = require "gw_home_drone_data"
            heroHurtItem.lv = droneData.OnGetDroneLv() or 0
            local droneSkin = GWG.GWHomeMgr.droneData.GetCurAdroneSkin()
            if not droneSkin or droneSkin == 0 then
                local data_personalInfo = require "data_personalInfo"
                droneSkin = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.AnimalsID)
            end
            heroHurtItem.id = droneSkin
        end
        heroHurtItem.hurt = 0
        heroHurtItem.index = index or 0
    end
    heroHurtItem.hurt = heroHurtItem.hurt + hurt
end

function GetHeroHurt()
    local heroHurtList = {}
    for i, v in pairs(allHeroHurtMap) do
        table.insert(heroHurtList, v)
    end

    return heroHurtList
end

--统计英雄被伤害
function AddHeroHitHurt(character, hurt, index)
    if not character then
        return
    end
    if character._unit_type ~= 4 and character._unit_type ~= 6 then
        return
    end
    -- dump(character._heroId,"AddHeroHitHurt222")
    -- dump(hurt,"AddHeroHitHurt")
    local heroHurtItem = allHeroHitHurtMap[character]
    if not heroHurtItem then
        heroHurtItem = {}
        allHeroHitHurtMap[character] = heroHurtItem
        heroHurtItem.character = character
        --heroHurtItem.cfg=game_scheme:MiniUnit_0(character._unitID)
        -- heroHurtItem.heroID=heroHurtItem.cfg.ModelID
        heroHurtItem.heroID = character._heroId
        heroHurtItem.isHero = true
        -- 是否是试用
        heroHurtItem.isTrial = character.heroData and character.heroData.isTrial or false
        heroHurtItem.heroData = character.heroData
        if character._isDrone then
            heroHurtItem.isHero = false
            local droneData = require "gw_home_drone_data"
            heroHurtItem.lv = droneData.OnGetDroneLv() or 0
            local droneSkin = GWG.GWHomeMgr.droneData.GetCurAdroneSkin()
            if not droneSkin or droneSkin == 0 then
                local data_personalInfo = require "data_personalInfo"
                droneSkin = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.AnimalsID)
            end
            heroHurtItem.id = droneSkin
        end
        heroHurtItem.hurt = 0
        heroHurtItem.index = index or 0
    end
    heroHurtItem.hurt = heroHurtItem.hurt + hurt
end

function GetHeroHitHurt()
    local heroHurtList = {}
    for i, v in pairs(allHeroHitHurtMap) do
        table.insert(heroHurtList, v)
    end

    return heroHurtList
end

function OnApplicationPause(pauseStatus)
    if not pauseStatus then
        log.Warning("pauseStatus=false")
    else
        log.Warning("pauseStatus=true")
    end
end

function RegisterBackgroundListener(bRegister)
    if not MainLoop.Instance.RegisterApplicationPauseHandler then
        log.Error("MainLoop 尚未初始化")
        return
    end
    if bRegister then
        MainLoop.Instance:RegisterApplicationPauseHandler(OnApplicationPause)
    else
        MainLoop.Instance:UnRegisterApplicationPauseHandler(OnApplicationPause)
    end
end

local MessageTable = {
    --{ msg_pb.MSG_XYX_PASS_LV_RSP,    RSP_PuzzleGame_PASS_LV,    activity_pb.TMSG_XYX_PASS_LV_RSP,},--小游戏过关回应

}
net_route.RegisterMsgHandlers(MessageTable)
