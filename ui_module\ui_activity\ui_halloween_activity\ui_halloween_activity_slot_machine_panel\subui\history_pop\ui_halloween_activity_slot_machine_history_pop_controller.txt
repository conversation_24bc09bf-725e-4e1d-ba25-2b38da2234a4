local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

local time_util = require "time_util"
local net_login_module = require "net_login_module"

local halloween_slot_machine_mgr = require "halloween_slot_machine_mgr"

local halloween_activity_slot_machine_history_data = require "halloween_activity_slot_machine_history_data"

--region Controller Life
module("ui_halloween_activity_slot_machine_history_pop_controller")
local controller = nil
local UIController = newClass("ui_halloween_activity_slot_machine_history_pop_controller", controller_base)


---@public 需要传入activityID
function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)    

    local serverTimeZone = net_login_module.GetServerTimeZone()
    local group_data = halloween_activity_slot_machine_history_data:GetDataSplitByServerDate(serverTimeZone)
    self:TriggerUIEvent("RenderList", halloween_slot_machine_mgr.GetSlotGameActivityId(), group_data)
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic

function UIController:OnBtnCloseBtnClickedProxy(data)
    ui_window_mgr:UnloadModule(self.view_name)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
