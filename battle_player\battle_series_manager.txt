-- battle_switch_manager.txt ------------------------------------------
-- author:  zjw
-- date:    2021.07.20
-- ver:     1.0
-- desc:    连续战斗管理
--------------------------------------------------------------
local pairs = pairs
local table = table
local print = print
local require = require
local dump = dump

local util = require "util"
local event = require "event"
local game_scheme = require "game_scheme"
local battle_data = require "battle_data"
local common_new_pb = require "common_new_pb"
local ui_window_mgr = require "ui_window_mgr"
-- local secretplace_mgr = require "secretplace_mgr"
local gw_hero_mgr = require "gw_hero_mgr"
local asset_loader = require "asset_loader"
local GameObject = CS.UnityEngine.GameObject
local tbs_pb = require "tbs_pb"

module("battle_series_manager")

local battleDataForType = {}

local timer = nil

--配置可以连续战斗的类型
local seriesBattleType = {
    [common_new_pb.KillingTower] = true, --杀戮场单人
    [common_new_pb.MultiKillingTower] = true, --杀戮场多人
}

--连续战斗战报数据
--{key(wave)= {battleId = 0, battleBits = nil} }
local seriesBattleReports = {}
--当前战斗类型
local curBattleStageType = 0
--类似battleid，所有波次共用
local battleKillingID = 0
--当前杀戮类型
local curDeckType = 1
--当前杀戮关卡
local curDeckLevel = 1
--当前战斗是第几波次
local currentWave = 0
--当前战报id
local currentBattleId = ""
--连续战斗总波次
local totalWaveCount = 0
--等待播放的战斗类型
local waitPlaybattleStageType = 0
--等待播放的波次
local waitPlayWave = 0

--快速战斗信息
local quickBattleInfos = {}
local quickBattleType = 0
local quickBattleLevel = 0
--快速战斗当前波次
local quickBattleWave = 0
--快速自动战斗波次上限
local quickBattleMaxWave = 0
--快速战斗是否达到褒词上限
local quickBattleIsMaxWave = false
--最后一波快速战斗是否胜利
local lastQuickBattleIsVictory = false

local isShowBattleResult = false
--可以自动挑战的波数
autoQuickBattleWaveCount = 3

local lastWave = 0
local lastTotalWaveCount = 0

--切出战斗数据
local isCutOutBattle = false
local cutOutDelayTimeId = nil
local cutOutBattleType = nil

--判断是否在连续战斗中
function IsInSeriesBattle(stageType)
    if not IsSeriesBattle(stageType) then
        return false
    end

    --通过战斗ui打开判断是否在战斗中
    if not ui_window_mgr:IsModuleShown("ui_in_battle") then
        -- print("没有打开uiinbattle界面！！！！！！")
        return false
    end

    return true
end

--判断是否是连续战斗类型
function IsSeriesBattle(stageType)
    if seriesBattleType[stageType] then
        return true
    end
    return false
end

--判断当前是否为最后一波连续战斗
function IsLastWave()
    return IsLastWaveBy(currentWave)
end

--判断是否为最后一波连续战斗
function IsLastWaveBy(wave)
    return wave >= totalWaveCount
end

--获取当前波次
function GetWave()
    return  currentWave
end

--获取最后一次波次
function GetLastWave()
    return lastWave,lastTotalWaveCount
end

--获取当前战斗id
function GetCurrentBattleId()
    return currentBattleId
end

--获取当前波次战报
function GetCurrentWaveBattle()
    local typeDatas = seriesBattleReports[curBattleStageType]
    if not typeDatas then
        return
    end

    local data = typeDatas[currentWave]
    --print("获取当前波次战报 curBattleStageType:", curBattleStageType, "currentWave:", currentWave)
    return data.reports
end

--获取当前波次战斗信息
function GetCurrentWaveBattleData()
    local typeDatas = seriesBattleReports[curBattleStageType]
    if not typeDatas then
        return
    end

    local data = typeDatas[currentWave]
    --print("获取当前波次战报 curBattleStageType:", curBattleStageType, "currentWave:", currentWave)
    return data
end

--获取总波次
function GetToatlWave()
    return  totalWaveCount
end

--请求进入杀戮场首场单人战斗
function ReqEnterKillingTowerSingleBattle(lineUp, level, buffId,battleStageType,deckType)
    -- secretplace_mgr.ReSetRelicIDs()--清空上一次战斗中所有的遗物ID

    -- curDeckType = deckType or 1
    -- curDeckLevel = level
    -- curBattleStageType = battleStageType or common_new_pb.KillingTower
    -- totalWaveCount = curBattleStageType == common_new_pb.KillingTower and GetToatlWaveBy(1, level) or GetToatlWaveBy(2, level)
    -- lastTotalWaveCount = totalWaveCount
    -- --print("请求进入杀戮场首场单人战斗!!!!!!", level, buffId, " totalWaveCount ", totalWaveCount)
    -- if curBattleStageType == common_new_pb.KillingTower then
    --     secretplace_mgr.Single_Battle_REQ(lineUp, level, buffId)
    -- else
    --     --print("请求进入杀戮场首场多人战斗!!!!!!", level, buffId, " totalWaveCount ", totalWaveCount)
    --     secretplace_mgr.Multi_Battle_REQ(lineUp, level, buffId)
    -- end
end

--请求进入杀戮场后续单人战斗
function ReqKillingTowerSingleBattle(wav, buffId,battleStageType)
    -- if battleKillingID == 0 then
    --     --缺少KillingID，
    --     return
    -- end
    -- if battleStageType == common_new_pb.KillingTower then
    --     --print("请求进入杀戮场后续单人战斗!!!!!!", battleKillingID, wav, buffId)
    --     secretplace_mgr.Single_Result_REQ(battleKillingID, wav, buffId)
    -- else
    --     --print("请求进入杀戮场后续多人战斗!!!!!!", battleKillingID, wav, buffId)
    --     secretplace_mgr.Multi_Result_REQ(battleKillingID, wav, buffId)
    -- end
end

--获得当前连续战斗总波次
function GetToatlWaveBy(deckType, deckLevel)
    local length = game_scheme:ChallengeDeck_nums()
    local itemData
    local maxWaveid = 0
    for i = 1, length do
        itemData = game_scheme:ChallengeDeck(i)
        if itemData and itemData.DeckType == deckType and itemData.DeckLevel == deckLevel
            and maxWaveid <= itemData.Waveid then
                maxWaveid = itemData.Waveid
        end
    end
    return maxWaveid
end

--接收杀戮场单人（首场）战斗回复
function OnReceiveEnterKillingTowerSingleBattle(killingID, battleId)
    battleKillingID = killingID
    currentWave = 1
    waitPlaybattleStageType = curBattleStageType or common_new_pb.KillingTower
    waitPlayWave = 1
    local typeDatas = GetSeriesBattleReportListBy(curBattleStageType)
    typeDatas[currentWave] = {["battleId"] = battleId }
    seriesBattleReports[curBattleStageType] = typeDatas
    --print("接收杀戮场单人/多人（首场）战斗回复!!!!!!", battleId, currentWave,"curBattleStageType",curBattleStageType,"battleKillingID",battleKillingID)
end

--接收杀戮场单人（后续）战斗回复
function OnReceiveKillingTowerSingleBattle(killingID, wave, battleId)
    --print("battleKillingID",battleKillingID,"killingID",killingID)
    if battleKillingID ~= killingID then
        return
    end
    local typeDatas = GetSeriesBattleReportListBy(curBattleStageType)
    typeDatas[wave] = {["battleId"] = battleId }
    seriesBattleReports[curBattleStageType] = typeDatas
    --print("接收杀戮场单人/多人（后续）战斗回复!!!!!!", battleId, wave,"curBattleStageType",curBattleStageType)
end


--设置下发的连续战斗战报数据
function SetSeriesBattleReport(battleStageType, battleId, reports, rewards, scores)
    --检测是否有对应的battleId
    local typeDatas = GetSeriesBattleReportListBy(curBattleStageType)
    local wave = 0
    for key, value in pairs(typeDatas) do
         if value.battleId == battleId then
            value.reports = reports
            value.rewards = rewards
            value.scores = scores
            --print("存储连续战斗数据源：", battleId)
            wave = key
         end
    end
    seriesBattleReports[curBattleStageType] = typeDatas

    --切出战斗处理
    if isCutOutBattle then
        SetCutOutBattleByReport(battleStageType, wave, reports, battleId)
        return
    end

    --当前在快速连续战斗中
    if quickBattleWave > 0 then
        SetQuickBattle(battleStageType, wave, reports, battleId)
        return
    end

    --print("-设置下发的连续战斗 播放连续战斗！！！！！：battleStageType", battleStageType, "wave", wave, "waitPlaybattleStageType", waitPlaybattleStageType, "waitPlayWave", waitPlayWave)
    if waitPlaybattleStageType ~= battleStageType then
        return
    end

    --如果请求到的波次小于等于待播放的波次，则播放当前请求到的波次；可以规避服务器下发波次不一致，导致卡战斗问题
    if wave <= waitPlayWave then
        PlayTargetWaveBattle(battleStageType, wave)
        -- waitPlaybattleStageType = 0
        -- waitPlayWave = 0
        -- currentWave = wave
        -- battle_manager.OnBattleReportReceived(battleStageType, reports, nil, nil, nil, nil, battleId)
    end

    --如果不是最后一场连续战斗，需要请求战斗结果，才可以继续请求下一场战斗
    -- if not IsLastWaveBy(wave) then
    --     local battle_message = require "battle_message"
    --     battle_message.C2SBattleResult(battleId)
    -- end
end

function GetSeriesBattleReportListBy(battleStageType)
    if not seriesBattleReports[battleStageType] then
        seriesBattleReports[battleStageType] = {}
    end
    return seriesBattleReports[battleStageType]
end

--是否有下一波战报
function HasNextWaveBattleReport(battleStageType)
    --获取对应类型的连续战斗数据
    local typeDatas = seriesBattleReports[battleStageType]
    if not typeDatas then
        return
    end

    local nextWave = currentWave + 1
    local nextData = typeDatas[nextWave]
    if nextData and nextData.reports then
        return true
    end

    return false

end

--播放下一波战斗
function PlayNextWaveBattle(battleStageType, isAutoSelectBuff)
    local nextWave = currentWave + 1
    PlayTargetWaveBattle(battleStageType, nextWave, isAutoSelectBuff)
end

--播放目标波次战斗
function PlayTargetWaveBattle(battleStageType, wave, isAutoSelectBuff)
    --获取对应类型的连续战斗数据
    --print("播放目标波次战斗！", battleStageType, wave)
    -- dump(seriesBattleReports)
    local typeDatas = seriesBattleReports[battleStageType]
    if not typeDatas then
        return
    end

    local data = typeDatas[wave]
    if not data or not data.reports then
        waitPlaybattleStageType = battleStageType
        waitPlayWave = wave
        --print("-播放目标波次战斗 播放连续战斗！！！！！：waitPlaybattleStageType", waitPlaybattleStageType, "waitPlayWave", waitPlayWave)
        if NeedSelectRelicBy(wave) then 
            
            if isAutoSelectBuff then 
                --自动随机遗物
                -- local buffId = 0
                -- local relics = secretplace_mgr.GetSelectRelic(curDeckType, curDeckLevel, wave)
                -- if relics and #relics > 0 then
                --     buffId = relics[2]
                -- end
                -- secretplace_mgr.SetRelicIDs(buffId,wave)
                -- ReqKillingTowerSingleBattle(wave, buffId,battleStageType)
            else
                --打开选择遗物界面
                ShowSelectRelicUIBy(wave, function(wave, buffid)
                    ReqKillingTowerSingleBattle(wave, buffid,battleStageType)
                end)
            end
            return
        end
        ReqKillingTowerSingleBattle(wave, 0,battleStageType)
        return
    end
    --print("开始播放连续战斗！", wave, data.battleId)
    waitPlaybattleStageType = 0
    waitPlayWave = 0
    currentWave = wave
    lastWave = wave
    currentBattleId = data.battleId
    local battle_manager = require "battle_manager"
    battle_manager.OnBattleReportReceived(battleStageType, data.reports, nil, nil, nil, nil, data.battleId)
    event.Trigger(event.NEXT_WAVE)
end

--打开下一场战斗选择遗物界面
function ShowSelectRelicUIByNextWave(selectedCallback)
    ShowSelectRelicUIBy(currentWave + 1, selectedCallback)
end


--打开选择遗物界面
function ShowSelectRelicUIBy(wave, selectedCallback)
    event.Trigger(event.SECRETPLACE_SHOW_SELECT_RELIC, curDeckType, curDeckLevel, wave, selectedCallback)
end

--预加载下一波战斗资源
function PreLoadResourceOfNextWaveBattle()

    --加载敌方出现特效
    LoadEnemyActiveEffect()

    if curDeckType == 0 or curDeckLevel == 0 then 
        return
    end

    local nextWave = currentWave + 1
    local nextCfg = GetTegetWaveData(nextWave)
    if not nextCfg then
        return
    end
    --播放战斗时预加载资源，减少卡顿，将下载上限设置为1
    local asset_load_mgr = require "asset_load_mgr"
    asset_load_mgr.SetMaxLoadingCount(1)

    local mosterTeamId = nextCfg.MonsterteamId
    --print("<color=#ffffff>开始预加载下一波连续战斗资源</color> mosterTeamId:", mosterTeamId, "curDeckType", curDeckType, "curDeckLevel", curDeckLevel, "nextWave", nextWave)
    PreLoadResourceByMonsterTeam(mosterTeamId)
end

--指定波次是否需要选遗物
function NeedSelectRelicByNextWave()
    return NeedSelectRelicBy(currentWave + 1)
end

--指定波次是否需要选遗物
function NeedSelectRelicBy(wave)
    local cfg = GetTegetWaveData(wave)
    --print("查表结果：", curDeckType, curDeckLevel, wave, #cfg.arrRelic1.data)
    if not cfg then 
        return
    end
    local count = #cfg.arrRelic1.data
    return count > 0
end

function GetTegetWaveData(wave)
    return game_scheme:ChallengeDeck_1(curDeckType, curDeckLevel, wave)
end

--预加载战斗怪物组资源
function PreLoadResourceByMonsterTeam(id)
    local monsterTeam = game_scheme:monsterTeam_0(id)
    if monsterTeam then
        local enemyDataArr = {}
        for i=0, monsterTeam.MonsterId.count-1 do
            local heroId = monsterTeam.MonsterId.data[i]
            if heroId~=0 then
                enemyDataArr[i] = gw_hero_mgr.CreateVirtualEntity(heroId,nil,nil,0,0,1,1)
            end
        end
        --预加载敌方资源
        local battle_preview_manager = require "battle_preview_manager"
        battle_preview_manager.PreLoadEnemyResource(enemyDataArr)
    end
end


local enemyActiveEffectPath = "art/effects/prefabs/buff/buff_shuaxin_masaike.prefab"
local effectAssetLoader = nil
local effectObjectTemplate = nil
local effectPool = {}

function LoadEnemyActiveEffect()
    if not effectAssetLoader then
        effectAssetLoader = asset_loader(enemyActiveEffectPath,"series_battle")
        effectAssetLoader:load(function (obj)
            if not obj then
                return
            end
            effectObjectTemplate = obj.asset
        end)
    end
end

function ShowEnmeyShowEffect(parentContaine)
    if not effectObjectTemplate or util.IsObjNull(effectObjectTemplate) then 
        return
    end

    local count = #effectPool
    local effectObject = nil
    if count > 0 then
        effectObject = effectPool[count]
        table.remove(effectPool, count)
    end
    if not effectObject or util.IsObjNull(effectObject) then 
        effectObject = GameObject.Instantiate(effectObjectTemplate)
    end
    effectObject.transform:SetParent(parentContaine.transform)
    effectObject.transform.localPosition = {x=0, y=0, z=0}
    effectObject.transform.localScale = {x=1, y=1, z=1}
    effectObject:SetActive(false)
    effectObject:SetActive(true)
    --延迟回收
    util.DelayCallOnce(3, function()
        if not util.IsObjNull(effectObject) then
            effectObject:SetActive(false)
            effectObject.transform:SetParent(nil)
            table.insert(effectPool, effectObject)
        else
            GameObject.Destroy(effectObject)
        end
    end)
end


function GetLastQuickBattleInfosBy()
    return quickBattleInfos, quickBattleIsMaxWave, lastQuickBattleIsVictory
end

--请求开始快速战斗
function StartQuickBattle(battleType, level, maxWave, lineUp,deckType)
    quickBattleType = battleType
    quickBattleLevel = level
    quickBattleWave = 1
    quickBattleMaxWave = maxWave
    --获取缓存阵容
    -- local lineUp = secretplace_mgr.GetHeroFormation(battleType)
    -- local count = util.TableCount(lineUp)
    -- --如果默认没有上阵英雄，则使用战力最高的英雄
    -- if count <= 0 then
			local sort_heros
    --     if sort_heros then 
    --         local firstHero = sort_heros[1] --获取战力最高的英雄
    --         firstHero.heroSid = firstHero.sid
    --         if firstHero then
    --             table.insert(lineUp, firstHero)
    --         end
    --     end
    -- end
    local count = util.TableCount(lineUp)
    if count > 0 then
        ReqEnterKillingTowerSingleBattle(lineUp, level, 0,battleType,deckType)
    else
        print("上阵英雄数量不足，无法发起战斗！！！！")
    end
end

--设置最后最后一波战斗信息
function SetLastBattleData()

    local typeDatas = GetSeriesBattleReportListBy(curBattleStageType)
    local lastWave = #typeDatas
    local lastData = typeDatas[lastWave]
    if not lastData then 
        return 
    end
    local lastReports = lastData.reports
    if not lastReports then 
        return 
    end
    currentWave = lastWave
    -- currentBattleId = battleId
    -- lastWave = wave

    local battle_manager = require "battle_manager"
    local bits = battle_manager.ParseRealBattleReport(lastReports)
    local pbMsg = tbs_pb.TbsReports()
    pbMsg:ParseFromString(bits)

    battle_data.ResetSummonedData()
    battle_data.ClearBattleInfo()

    --解析战报英雄结算数据
    local multiple_battle_report_creator = require "multiple_battle_report_creator"
    multiple_battle_report_creator.CreateNewReportBy(pbMsg, #pbMsg.reports + 1)

end



--设置快速战斗信息
function SetQuickBattle(battleStageType, wave, reports, battleId)
    if battleStageType ~= quickBattleType then 
        return
    end

    currentWave = wave
    currentBattleId = battleId
    lastWave = wave

    local battle_manager = require "battle_manager"
    local bits = battle_manager.ParseRealBattleReport(reports)
    local pbMsg = tbs_pb.TbsReports()
    pbMsg:ParseFromString(bits)

    battle_data.ResetSummonedData()
    battle_data.ClearBattleInfo()
    battle_data.ClearStatisticData()

    --解析战报英雄结算数据
    local multiple_battle_report_creator = require "multiple_battle_report_creator"
    multiple_battle_report_creator.CreateNewReportBy(pbMsg, #pbMsg.reports + 1)

    local quickBattleInfo = {}
    local heros = battle_data.GetHeros()
    local hero = nil
    local leftHeros = {}
    local rightHeros = {}
    for i = 0, 12 do
        hero = heros[i]
        if hero ~= nil then
            if i <= 5 then
                table.insert(leftHeros, hero)
            else
                table.insert(rightHeros, hero)
            end
        end
    end
    quickBattleInfo.leftHeros = leftHeros
    quickBattleInfo.rightHeros = rightHeros
    quickBattleInfo.isVictory = battle_data.IsVictory()
    quickBattleInfo.wave = wave
    quickBattleInfo.battleid = battleId
    lastQuickBattleIsVictory = quickBattleInfo.isVictory
    isShowBattleResult = not quickBattleInfo.isVictory

    quickBattleInfos[wave] = quickBattleInfo
    --是否达到快速自动战斗波次上限-5,达到则需要玩家进入战斗继续台哦站
    quickBattleIsMaxWave = wave >= quickBattleMaxWave or wave >= totalWaveCount
    --print("快速战斗信息！！！！", "wave", wave, "totalWaveCount", totalWaveCount, "isVictory", quickBattleInfo.isVictory, "isMaxWave", isMaxWave, "quickBattleMaxWave", quickBattleMaxWave)
    --dump(quickBattleInfos)
    
    --请求结算信息
    local battle_message = require "battle_message"
    battle_message.C2SBattleResult(GetCurrentBattleId())

    --派发更新信息事件
    event.Trigger(event.QUICK_SERIES_BATTLE_INFO_UPDATE, quickBattleInfos, lastQuickBattleIsVictory, quickBattleIsMaxWave)

    --达到快速战斗波次上限，结束快速战斗
    --挑战失败，结束快速战斗
    if isShowBattleResult or quickBattleIsMaxWave then
        StopQuickBattle()
        return
    end

    --间隔n秒请求
    local delayTime = 1
    --请求下一波战斗
    util.DelayCallOnce(delayTime, function()
        if quickBattleWave <= 0 then 
            return
        end

        quickBattleWave = quickBattleWave + 1
        --获取随机buff
        local buffId = 0
        local relics = secretplace_mgr.GetSelectRelic(curDeckType, curDeckLevel, quickBattleWave)
        -- print("请求随机buff：", curDeckType, curDeckLevel, quickBattleWave)
        -- dump(relics)
        if relics and #relics > 0 then
            buffId = relics[2]
        end
        secretplace_mgr.SetRelicIDs(buffId,quickBattleWave)
        ReqKillingTowerSingleBattle(quickBattleWave, buffId,battleStageType)
    end)
end

--当前是否在快速连续战斗中
function IsQuickSeriesBattle()
    return quickBattleWave > 0 
end

--是否显示结算界面
function IsShowBattleResult()
    --print("是否显示结算界面!!!!", isShowBattleResult)
    return isShowBattleResult
end


--结束快速战斗
function StopQuickBattle()
    quickBattleType = 0
    quickBattleLevel = 0
    quickBattleWave = 0
    quickBattleMaxWave = 0
end

--当前是否切出战斗中
function IsCutOutBattle()
    return isCutOutBattle
end

function GetCutoutBattleType()
    return cutOutBattleType
end

--切出连续战斗
function CutOutBattle()
    cutOutBattleType = curBattleStageType
    isCutOutBattle = true
    local isVictory = battle_data.IsVictory()
    local curWaveEndTime = battle_data.multipleBattleSeriesNextWaveStartTime[curBattleStageType]
    local serverTime = util.GetServerTime()
    local curWaveEndDelayTime = curWaveEndTime - serverTime
    SetCutOutBattle(isVictory, curWaveEndDelayTime,cutOutBattleType)
end

--切入战斗
function CutOverBattle()
    isCutOutBattle = false
    ClearDelayTime()
    --PlayTargetWaveBattle(cutOutBattleType, currentWave)
end

--切出战斗后，设置后台战斗战报
function SetCutOutBattleByReport(battleStageType, wave, report, battleId)
    if cutOutBattleType ~= battleStageType then 
        return
    end

    currentWave = wave
    currentBattleId = battleId
    lastWave = wave

    -- local battle_manager = require "battle_manager"
    -- local bits = battle_manager.ParseRealBattleReport(report)
    -- local pbMsg = tbs_pb.TbsReports()
    -- pbMsg:ParseFromString(bits)

    -- battle_data.ResetSummonedData()
    -- battle_data.ClearBattleInfo()
    -- battle_data.ClearStatisticData()

    -- --解析战报结算数据
    -- local multiple_battle_report_creator = require "multiple_battle_report_creator"
    -- multiple_battle_report_creator.CreateNewReportBy(pbMsg, #pbMsg.reports + 1)
    -- local isVictory = battle_data.IsVictory()
    local isVictory = GetVictoryByReport(report)
    local curWaveEndDelayTime = battle_data.multipleBattleSeriesPerTime[curBattleStageType] --使用预估时间作为本场战斗的时长
    --print("切出战斗，设置战斗信息：isVictory：", isVictory, "curWaveEndDelayTime", curWaveEndDelayTime, "wave:", wave)
    SetCutOutBattle(isVictory, curWaveEndDelayTime,battleStageType)
end

--切出战斗后，设置后台战斗信息
function SetCutOutBattle(isVictory, curWaveEndDelayTime,battleStageType)
    --切出战斗后波次结束处理函数
    local waveEndFunc = function()
        --print("切出连续战斗，战斗结束：isVictory：", isVictory, "nextWave", currentWave + 1, "totalWaveCount", totalWaveCount)
        -- local battle_message = require "battle_message"
        -- --获胜请求下一场战斗
        -- if isVictory then 
        --     if currentWave >= totalWaveCount then
        --         --全部波次战斗结束显示战斗结算界面
        --         isShowBattleResult = true
        --         battle_message.C2SBattleResult(GetCurrentBattleId())
        --         return
        --     end
        --     isShowBattleResult = false
        --     battle_message.C2SBattleResult(GetCurrentBattleId())

        --     local buffId = 0
        --     local relics = secretplace_mgr.GetSelectRelic(curDeckType, curDeckLevel, currentWave)
        --     if relics and #relics > 0 then
        --         buffId = relics[2]
        --     end
        --     -- print("请求buff列表：", curDeckType, curDeckLevel, currentWave, "buffId:", buffId)
        --     -- dump(relics)
        --     secretplace_mgr.SetRelicIDs(buffId,currentWave)
        --     ReqKillingTowerSingleBattle(currentWave + 1, buffId,battleStageType)
        -- else
        --     --失败显示战斗结算界面
        --     isShowBattleResult = true
        --     battle_message.C2SBattleResult(GetCurrentBattleId())
        -- end
    end

    if curWaveEndDelayTime <= 0 then 
        waveEndFunc()
    else
        ClearDelayTime()
        cutOutDelayTimeId = util.DelayCallOnce(curWaveEndDelayTime, waveEndFunc)
        --print("切出连续战斗等待回调！！！curWaveEndDelayTime：", curWaveEndDelayTime, "currentWave", currentWave)
    end
end

--获取战报胜负结果
function GetVictoryByReport(sourceReprot)
    local battle_manager = require "battle_manager"
    local bits = battle_manager.ParseRealBattleReport(sourceReprot)
    local msg = tbs_pb.TbsReports()
    msg:ParseFromString(bits)
    local victory = false
    for i = 1, #msg.reports do
        local report = msg.reports[i]
        if report:HasField("gameOverReport") and report.gameOverReport then
            local id = battle_data.GetRoleID()
            if report.gameOverReport.winner == id then
                victory = true
            end
            -- print("获取战报胜负结果GetVictoryByReport!!!!victory:", victory, "GetRoleID:", id)
            -- dump(report.gameOverReport)
            break
        end
    end
    
    return victory
end

function ClearDelayTime()
    if cutOutDelayTimeId then 
        util.RemoveDelayCall(cutOutDelayTimeId)
        cutOutDelayTimeId = nil
    end
end


function ClearQuickBattleData()
    isShowBattleResult = false
    quickBattleInfos = {}
    StopQuickBattle()
    quickBattleIsMaxWave = false
    lastQuickBattleIsVictory = false
end

function ClearBattleAssets()
    effectObjectTemplate = nil
    if effectAssetLoader then
        effectAssetLoader:Dispose()
        effectAssetLoader = nil
    end
    for key, value in pairs(effectPool) do
        if value then 
            GameObject.Destroy(value)
        end
    end
    effectPool = {}
end


function Clear()
    curDeckType = 0
    curDeckLevel = 0
    currentWave = 0
    totalWaveCount = 0
    seriesBattleReports = {}
    waitPlaybattleStageType = 0
    waitPlayWave = 0
    currentBattleId = ""
    curBattleStageType = 0
    battleKillingID = 0
    isCutOutBattle = false
    ClearDelayTime()
    ClearQuickBattleData()
    ClearBattleAssets()
end
event.Register(event.SCENE_DESTROY_NEW, Clear)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, Clear)