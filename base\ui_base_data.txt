﻿--- ui_base_data.txt -------------------------------
--- author:  fgy
--- date: 2024/4/9 15:13
--- desc:数据存储如界面缓存、子界面缓存
--------------------------------------------------
local require = require
local event = require "event"
local table = table
local idle = require "idle"
local handleFunc     = handleFunc
local const				= require "const"
local OPEN_CACHE_WINDOW_RES = const.OPEN_CACHE_WINDOW_RES
local util = require "util"
local _G = _G
---@module ui_base_data
module("ui_base_data")

local M = {}

--- 缓存加载过的界面ui Assets  {assetBundleName:go}
local uiProtos = {}
local stack = {}
_G["ui_base|stack"] = stack
---打印日志开关
M.LogLoad = false
if M.<PERSON>g<PERSON>oad then
    require "ui_base_log"
end

------------------------------------------------页面缓存Start------------------------------------------------------------

function M:Create()
    event.IsCacheUI = handleFunc(self,self.IsCacheUI)
end

CacheUiList = {
    -- ["ui_time_loading"] = 1 ,
    ["net_reloading"] = 1 ,
    ["ui_hero_impl"] = 1,
    ["ui_package"] = 3  ,
    ["ui_dungeon_pose"] = 5  ,
    ["ui_select_hero"] = 1  ,
    ["ui_lobby"] = 1  ,
    ["ui_create_array"] = 1  ,
    -- ["ui_illusion_tower"] = 5  ,
    -- ["ui_dungeon"] = 5  ,
    ["ui_skill_package"] = 5  ,
    ["ui_hero_decompose"] = 5  ,
    ["ui_task_achievement"] = 1  ,
    -- ["ui_sociaty_main"] = 3  ,
    ["ui_chat_main_new"] = 5  ,
    ["ui_menu_top"] = 1  ,
    ["ui_menu_bot"] = 1  ,
    ["ui_menu_side"] = 1  ,
    ["new_hook_scene"] = 1  ,

    ui_bindprompt_impl  		=5,
    ui_special_gift_bag         =3,
    flow_notice                 =1,
    flow_tips                   =1,
    ui_frist_rechage            =4,
    ui_lost_weapon              =5,
    ui_target_task              =5,
    ui_festival_activity_base   =3,
    ui_online_reward            =3,
    ui_select_diamond           =4,
    ui_gift_new                 =3,
    -- ui_faction_wanted_entrance =4,
    ui_soul_link               =5,
    -- ui_dungeon_levels          =4,
    ui_rank_base               =5,
    ui_market_new              =3,
    ui_matchplace_entrance     =5,
    ui_halo                    =1,
    ui_soul_link_select_hero   =3,
    -- ui_maze_hero   =4,
    ui_activity_broken_time    =5,
}


function M:IsCacheUI(naMe)
    if not naMe then return end
    local value = OPEN_CACHE_WINDOW_RES and CacheUiList[naMe] and CacheUiList[naMe] <= idle.MEM_LEVEL
    return value
end

function M:SetCacheUIValue(naMe , value)
    if not naMe then return end
    CacheUiList[naMe] = value
end

function M:GetCacheUIValue(naMe)

    if not naMe then return end
    return CacheUiList[naMe]
end

--缓存界面prefab
function M:CachePrefab(naMe,UIPrefab)

    if self:IsCacheUI(naMe) then
        -- print("CachePrefab IsCacheUI",self:IsCacheUI(),self._NAME) --and util.IsObjNull(uiProtos[self._NAME])

        if not util.IsObjNull(UIPrefab)   then
            ------ print("CachePrefab",self._NAME)
            uiProtos[naMe] = UIPrefab
        end
    end
end

function M:GetCacheUIObj(naMe)
    if self:IsCacheUI(naMe) and not util.IsObjNull(uiProtos[naMe]) then
        return uiProtos[naMe]
    end
end
------------------------------------------------页面缓存End--------------------------------------------------------------


------------------------------------------------页面返回关联手机按键Start------------------------------------------------------------

---存储页面返回关联手机按键
function M:SetEscapeHandle(uibase)
    if uibase.isShow then
        if uibase.escapeHandle then
            --print("Register",self.__self,self.UIRoot,self.UIModuleName,show)
            table.remove_value(stack, uibase.__self,true)
            table.insert(stack, uibase.__self)
            -- event.Register(event.ANDROID_BACK, self.escapeHandle)
        end
    else
        if uibase.escapeHandle then
            --print("Unregister",self.__self,self.UIRoot,self.UIModuleName,show)
            table.remove_value(stack, uibase.__self,true)
            -- event.Unregister(event.ANDROID_BACK, self.escapeHandle)
        end
    end
end


function OnANDROID_BACK()
    local len = #stack
    for i=len,1,-1 do
        local m = stack[i]
        -- m = m and m.id
        if m and m:IsUIVisible() then
            if m.escapeHandle and m.escapeHandle() then
                event.Trigger(event.ON_ANDROID_BACK_RESULT,true)
                break
            end
        end
    end

end
event.Register(event.ANDROID_BACK, OnANDROID_BACK)
------------------------------------------------页面返回关联手机按键END------------------------------------------------------------


--子模块缓存对应的父物体
local childCacheModuleParent = {}
------------------------------------------------子页面缓存Start------------------------------------------------------------
--设置子模块父物体缓存
function M:AddParentCache(uiModuleNaMe, parentCache)
    if not uiModuleNaMe then
        return
    end

    childCacheModuleParent = childCacheModuleParent or {}
    if not childCacheModuleParent[uiModuleNaMe] then
        childCacheModuleParent[uiModuleNaMe] = {}
    end

    childCacheModuleParent[uiModuleNaMe].parent = parentCache
end

--清理子模块父物体缓存
function M:ClearParentCache(uiModuleNaMe)
    if not uiModuleNaMe or not childCacheModuleParent then
        return
    end

    if childCacheModuleParent[uiModuleNaMe] then
        childCacheModuleParent[uiModuleNaMe] = nil
    end
end

--获取子模块父物体缓存
function M:GetParentCache(uiModuleNaMe)
    if not uiModuleNaMe then
        return
    end

    return childCacheModuleParent[uiModuleNaMe]
end
------------------------------------------------子页面缓存End--------------------------------------------------------------


M:Create()
return M