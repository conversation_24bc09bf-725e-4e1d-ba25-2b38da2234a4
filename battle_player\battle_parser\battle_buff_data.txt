--@region FileHead
-- battle_buff_data.txt ---------------------------------
-- author:  梁骐显
-- date:    2/4/2022 12:00:00 AM
-- ver:     1.0
-- desc:    Buff 拓展功能
-------------------------------------------------

local pairs    = pairs
local require  = require
local bit 			= require "bit"
local game_scheme = require "game_scheme"
local log = require "log"
local event = require "event"
local dump = dump
local util = require "util"

module("battle_buff_data")

local buffConfig={
    ClineCreate = 0,--客户端创建
    bleed = 1,--流血
    burn = 2,--燃烧
    poisoning = 3,--中毒
    silence = 4,--沉默
    dizz = 5,--眩晕
    frozen = 6,--冰冻
    stone = 7,--石化
    lord = 8,--领主
    order = 9,--秩序印记
    recover = 10,--恢复
    trap = 11,--陷阱
    bounty = 12,--悬赏令
    desire = 13,--摄魂
    sword = 14,--剑影
    undead = 15,--亡灵
    disarm = 16,--缴械
    fall = 17,--陷落
    collapse = 18,--坍缩
    belief = 19,--信仰
    buff = 20,--增益
    debuff = 21,--减益
    reborn = 22,--复活
    split = 23,--割裂
    lover = 24,--恋人
    twine = 25,--缠绕
    shield = 26,--护盾
    imprisoned = 27,--定身
    dragon = 28,--魔龙形态
    cyberDamageIncrease = 29,--赛博剑心增伤
    permanentlyFrozen = 30,--永久冰冻
}

local BuffSchemeCache = {}

local ActiveBuffCache = {}

local WaitingBuffList = {}--buff队列，如果上一个buff没完成，新的将会排队

local charecterMaterialAssets = nil

local isDispose = false

--激活buff通知
function ActiveBuffEffect(RoleID,BuffID,buffLevel)
    GetBuffFlag(BuffID,buffLevel,function (layerFlag,index)
        local buffInfo = {
            role = RoleID,
            buff = BuffID,
            level = buffLevel,
            flag = layerFlag,
            index = index,
        }
        --加载队列
        if CacheBuffEffect(buffInfo) then
            return
        end
        --log.Error("<color=#aaffaa>加载buff</color> "..BuffID.." role:"..RoleID.." >> "..index)
        OnHitFlag(buffInfo)
    end)
end
--event.Register(event.BATTLE_BUFF_ACTIVE,ActiveBuffEffect)

function OnHitFlag(buffInfo)
    if buffInfo.index == buffConfig.frozen  then
        FrozenEffect(buffInfo)
    elseif buffInfo.index == buffConfig.stone then
        StoneEffect(buffInfo)
    elseif buffInfo.index == buffConfig.dizz then
        DizzEffect(buffInfo)
    end
end

--获取buff的位标记
function GetBuffFlag(BuffID,buffLevel,onHitCallback)
    if not BuffSchemeCache[BuffID] then
        BuffSchemeCache[BuffID] = {}
    end

    if not BuffSchemeCache[BuffID][buffLevel] then
        BuffSchemeCache[BuffID][buffLevel] = game_scheme:Buff_0(BuffID, buffLevel)
    end

    local bFlagArr = BuffSchemeCache[BuffID][buffLevel].unFlag

    -- --log.Error("获取buff的位标记 >>>>>>",bFlagArr.count)
    -- dump(bFlagArr.data)
    for i = 0, bFlagArr.count-1 do
        local bFlag = bFlagArr.data[i]
        for _, index in pairs(buffConfig) do
            if index ~= 0 then
                local layerFlag = bit.lshift(1 , index)
                -- --log.Error("匹配 >>>>>>",bFlag,"index>>",index,"layerFlag>>",layerFlag,"resualt =",bit.band(layerFlag, bFlag))
                if bit.band(layerFlag, bFlag) ~= 0 then
                --匹配成功时候回调
                    onHitCallback(layerFlag,index)
                end
            end
        end 
    end
end

--增加等待buff到队列尾部
function InsertWaitingBuff(info)
    if WaitingBuffList[info.role] == nil then
        WaitingBuffList[info.role] = {}
        WaitingBuffList[info.role]["frist_index"] = 1
        WaitingBuffList[info.role]["last_index"] = 0
        WaitingBuffList[info.role]["data"] = {}
    end

    local roleWaiting = WaitingBuffList[info.role]
    roleWaiting.last_index = roleWaiting.last_index + 1
    roleWaiting.data[roleWaiting.last_index] = info
end

--获取头部等待buff
function GetWaitingBuff(roleID)
    local roleWaiting = WaitingBuffList[roleID]
    if roleWaiting and roleWaiting.data[roleWaiting.frist_index] then
        local info = roleWaiting.data[roleWaiting.frist_index]
        roleWaiting.frist_index = roleWaiting.frist_index + 1
        return info
    end
end

--删除一个flag，重新排序
function DeleteWaitingBuff(roleID,flag)
    local roleWaiting = WaitingBuffList[roleID]

    local isFound = false
    local count = 0
    local newData = {}
    if roleWaiting then
        for _, info in pairs(roleWaiting.data) do
            if info.flag ~= flag or isFound then
                count = count + 1
                newData[count] = info
            end
            isFound = isFound or (not isFound and info.flag == flag)
        end
    
        WaitingBuffList[roleID] = {
            frist_index = 1,
            last_index = count,
            data = newData
        }
        
    else
        log.Error("Delete waiting buff Error! Can't find waiting buff, roleID = "..roleID,"flag="..flag)
    end

end

--处理buff叠加情况
function CacheBuffEffect(info)
    local buffInfo = ActiveBuffCache[info.role]
    if buffInfo then
        InsertWaitingBuff(info)
        --log.Error("<color=#ffffaa>角色有buff["..buffInfo.info.index.."]，放入队列</color> "..info.buff.." role:"..info.role.." >> "..info.index)
        -- dump(WaitingBuffList)
        return true
    else
        return false
    end
end




--移除buff通知
function RemoveBuffEffect(RoleID,BuffID,buffLevel)
    --TODO:先解析buff
    GetBuffFlag(BuffID,buffLevel,function (layerFlag,idnex)
        --TODO:先检查当前buff状态，如果有，则移除当前状态
        local currBuffInfo = ActiveBuffCache[RoleID]
        --log.Error("<color=#ffaaaa>卸载buff</color>\t"..BuffID.." Role:"..RoleID.." >> "..idnex)
        if currBuffInfo and currBuffInfo.info and currBuffInfo.remove and currBuffInfo.info.flag == layerFlag then
            ActiveBuffCache[RoleID].remove(currBuffInfo)
            ActiveBuffCache[RoleID] = nil
        else
            --TODO：如果没有，则移除等待列表中的buff
            local roleWaiting = WaitingBuffList[roleID]
            if roleWaiting then
                DeleteWaitingBuff(RoleID,layerFlag)
            end
            --log.Error("<color=#ffffaa>移除缓存中的</color>RoleID="..RoleID.." >>",idnex)
        end

        --TODO：加载等待中的buff
        local nextBuff = GetWaitingBuff(RoleID)
        if nextBuff then
            --log.Error("<color=#aaaaff>加载缓存：</color>RoleID="..nextBuff.role.." >>",nextBuff.index)
            ActiveBuffEffect(nextBuff.role,nextBuff.buff,nextBuff.level)
            DeleteWaitingBuff(nextBuff.role,nextBuff.flag)
            --dump(WaitingBuffList)
            nextBuff = nil
        end
    end)
end
--event.Register(event.BATTLE_BUFF_REMOVE,RemoveBuffEffect)

--结束战斗或者退出登录时候考虑清理
function ClearActiveBuffCache()
    WaitingBuffList = {}
    for roleID, data in pairs(ActiveBuffCache) do
        RemoveBuffEffect(data.info.role,data.info.buff,data.info.level)
    end
    ActiveBuffCache = {}
end
event.Register(event.BATTLE_END,ClearActiveBuffCache)

--注册管理
function CreateInfo(info,removeCallback)
    local battle_manager = require "battle_manager"
    local player = battle_manager.GetBattlePlayer()
    if not player then
        log.Error("[buff]BattlePlayer Not found!")
        return
    end
    local role = player:GetRole(info.role)
    local roldCard = nil
    if role~= nil and role.node~= nil then
        roldCard = role.node.card
    else
        log.Error("Role NOT FOUND! StoneEffect Can't be loaded >>> RoleID=",info.role)
    end
    if roldCard ~= nil then
        ActiveBuffCache[info.role] = {}
        ActiveBuffCache[info.role]["info"] = info
        ActiveBuffCache[info.role]["Card"] = roldCard
        ActiveBuffCache[info.role]["remove"] = removeCallback
    else
        log.Error("Can't be loaded >>> RoleID=",info.role)
    end
    return ActiveBuffCache[info.role]
end

--================================================================================
--=================================Buff功能区域 START==============================
--================================================================================
--冻结
function FrozenEffect(i_info)
    --TODO:FrozenFunc
    local info = CreateInfo(i_info,function (info)
        if info and not util.IsObjNull(info.Card)  and not util.IsObjNull(info.Card.animator) then
            info.Card.animator.speed = info.AnimationSpeed
        end

    end)
    if info and not util.IsObjNull(info.Card)  and not util.IsObjNull(info.Card.animator) then
        info["AnimationSpeed"] = info.Card.animator.speed
        info.Card.animator.speed = 0
    end
end

--眩晕
function DizzEffect(i_info)
    local info = CreateInfo(i_info,function (info)
        if info and not util.IsObjNull(info.Card)  and not util.IsObjNull(info.Card.animator) then
            info.Card.animator:SetBool("Stone",false)
        else
            log.Error("Object lost",util.IsObjNull(info.Card))
        end
    end)
    if info and not util.IsObjNull(info.Card)  and not util.IsObjNull(info.Card.animator) then
        info.Card.animator:SetTrigger("StoneTrigger")
        info.Card.animator:SetBool("Stone",true)
    end
end

--石化
function StoneEffect(i_info)
    local info = CreateInfo(i_info,function (info)
        if info and not util.IsObjNull(info.Card) then
            if not util.IsObjNull(info.Card.animator) then
                info.Card.animator.speed = info.AnimationSpeed
            end
            info.Card:RestoreOriginMaterial()
        else
            log.Error("Object lost",util.IsObjNull(info.Card))
        end
    end)

    if info then
        if charecterMaterialAssets == nil then
            local material_asset = require "material_asset"
            charecterMaterialAssets = material_asset.CMatetialAsset()
        end
    
        if charecterMaterialAssets == nil then
            log.Error("Create charecterMaterialAssets Fialed!")
            return
        end

        if not util.IsObjNull(info.Card)  and not util.IsObjNull(info.Card.animator) then
            info["AnimationSpeed"] = info.Card.animator.speed
            info.Card.animator.speed = 0
        end

        --TODO:Change Char Mat
        charecterMaterialAssets:GetMatetial("f_wenli_mgx_shihua",function(mat)
            if info then
                info.Card:SetupTempMaterial(mat)
                
            end
        end)
    end
end

--================================================================================
--=================================Buff功能区域 END================================
--================================================================================
