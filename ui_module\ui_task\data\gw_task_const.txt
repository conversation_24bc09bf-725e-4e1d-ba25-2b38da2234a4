﻿---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/11/14 10:24
--- Desc : 任务常量

---@module gw_task_const
local require = require
module("gw_task_const")

--根据taskMain表的moduleId 对应
TaskModuleType = {
    Daily = 1, -- 每日
    Chapter = 2, -- 章节
    Mainline = 3, -- 主线
    SideQuest = 4, -- 支线
    FactionTrial = 5, -- 阵营试炼   
    ArmsRace = 6, -- 军备竞赛
    BPDailyTask = 7, -- BP每日任务
    BPAchievementTask = 8, -- BP成就任务
    RoadLight = 9, -- 光明之路
    ProsperityFund = 10, -- 繁荣基金

    -- 世界boss
    HeroTrial = 11, --英雄试炼
    BestLeader1 = 12, --最强指挥官
    WorldBoss = 13, --世界boss

    -- 先锋目标
    pioneer_target = 16,
    AllianceDuel = 18, --同盟对决

    FullyWar = 19, --全面备战
    MatchSingle = 17, --新手竞技场

    CitySiege = 20,--城市竞赛
    
    LuckyWheel = 21, --幸运转盘
    BestLeader2 = 22, --最强指挥官
    TotalRecharge = 23, --累计充值
    RankTask = 24, -- 排行榜任务
    DailyGift = 25, --每日必买
    LoginGift = 26, --登录好礼
    RadarBattlePass = 27, --雷达战令
    TrainBattlePas = 28, --特训战令
    WarZoneDuel = 29, --战区对决
    ShiningMarket = 30, --闪金集市
    MiracleBox = 31, --神迹宝箱
    DevelopFund = 32, -- 发展基金
    CardDrawActivity = 33,--召唤活动
    FindTreasure = 34,  -- 寻宝大作战
    MonstersApproaching = 35,--怪物来袭
    Doomsday = 36,--末日降临
    FirstRecharge = 37, --首充
    ZombieTreasure = 38, --丧尸宝藏
    StormRescue = 39, --风暴营救
    FaceTransplant = 41, --个性换新
    warRally = 14,      --集结大作战
    MythicalBeastPass = 40, --神兽结晶战令
    MythicalBeastBingo = 43, --神兽bingo
    StrongRoad = 44,    --强者之路
    MythicalBeastSignIn = 42, --神兽签到
    HalloweenSignIn = 46, --万圣节签到
    StormArena = 47, --风暴竞技场
}
---任务界面 拥有的任务模块
MainUITaskModuleType = {
    Daily = 1, -- 每日
    Chapter = 2, -- 章节
    Mainline = 3, -- 主线
    SideQuest = 4, -- 支线
}
---初始化模块任务数据
InitModuleDataType = {
    TaskModuleType.Daily,
    TaskModuleType.Chapter,
    TaskModuleType.Mainline,
    TaskModuleType.SideQuest,
    TaskModuleType.WorldBoss,
    TaskModuleType.FullyWar,
    TaskModuleType.Doomsday,
    TaskModuleType.ZombieTreasure,
    TaskModuleType.MythicalBeastSignIn,
    TaskModuleType.HalloweenSignIn,
}

CommonRewardType = {
    [TaskModuleType.Daily] = true,
    [TaskModuleType.Chapter] = true,
    [TaskModuleType.Mainline] = true, -- 主线任务
    [TaskModuleType.SideQuest] = true, -- 支线任务
    [TaskModuleType.ArmsRace] = true, -- 军备竞赛
    [TaskModuleType.BPDailyTask] = true, -- BP每日任务
    [TaskModuleType.BPAchievementTask] = true, -- BP成就任务
    [TaskModuleType.HeroTrial] = true, -- 英雄试炼
    [TaskModuleType.RoadLight] = true, -- 光明之路
    [TaskModuleType.ProsperityFund] = true, -- 繁荣基金
    [TaskModuleType.WorldBoss] = true, -- 世界boss
    [TaskModuleType.pioneer_target] = true, -- 先锋目标
    [TaskModuleType.AllianceDuel] = true, -- 同盟对决
    [TaskModuleType.FullyWar] = true, -- 全面备战
    [TaskModuleType.LuckyWheel] = true, -- 幸运转盘
    [TaskModuleType.TotalRecharge] = true, -- 累计充值
    [TaskModuleType.BestLeader1] = true, -- 最强指挥官
    [TaskModuleType.BestLeader2] = true, -- 最强指挥官
    [TaskModuleType.DailyGift] = true, -- 每日必买
    [TaskModuleType.LoginGift] = true, --登录好礼
    [TaskModuleType.RankTask] = true, -- 排行榜任务
    [TaskModuleType.RadarBattlePass] = true, -- 雷达战令
    [TaskModuleType.TrainBattlePas] = true, -- 特训战令
    [TaskModuleType.WarZoneDuel] = true, --战区对决
    [TaskModuleType.ShiningMarket] = true, --闪金集市
    [TaskModuleType.MiracleBox] = true, --神迹宝箱
    [TaskModuleType.DevelopFund] = true, --发展基金
    [TaskModuleType.CardDrawActivity] = true,--召唤活动
    [TaskModuleType.FactionTrial] = true,--阵营试炼
    [TaskModuleType.MonstersApproaching] = true,--怪物来袭
    [TaskModuleType.MatchSingle] = true,--新手竞技场
    [TaskModuleType.FindTreasure] = true, -- 寻宝大作战
    [TaskModuleType.Doomsday] = true, -- 末日降临
    [TaskModuleType.CitySiege] = true, -- 城市竞赛
    [TaskModuleType.FirstRecharge] =true, --首充
    [TaskModuleType.ZombieTreasure] =true, --丧尸宝藏
    [TaskModuleType.warRally] =true, --集结大作战
    [TaskModuleType.StormRescue] =true, --风暴营救
    [TaskModuleType.FaceTransplant] =true, --个性换新
    [TaskModuleType.StrongRoad] =true, --强者之路
    [TaskModuleType.MythicalBeastBingo] = true, -- 神兽bingo
    [TaskModuleType.MythicalBeastSignIn] = true, -- 神兽签到
    [TaskModuleType.MythicalBeastPass] = true, --神兽结晶战令
    [TaskModuleType.HalloweenSignIn] = true, --万圣节签到
}

--领取奖励界面关闭回调
GetRewardCloseCallBackFun = {
    [TaskModuleType.BestLeader2] = function(msg)
        local gw_bestleader_mgr = require "gw_bestleader_mgr"
        gw_bestleader_mgr.ShowGetKey(msg)
    end,
}

--请求领取奖励成功回调
GetRewardSuccessCallBackFun = {
    [TaskModuleType.BestLeader2] = function(msg, showFunc)
        local gw_bestleader_mgr = require "gw_bestleader_mgr"
        local gw_bestleader_define = require "gw_bestleader_define"
        local event = require "event"
        if not gw_bestleader_mgr.GetNeedShowBigRewardEffect(msg) then
            --不播放特效，直接弹奖励获得
            showFunc()
            return
        end
        event.Trigger(gw_bestleader_define.GW_BESTLEADER_PLAY_EFFECT, showFunc)
    end,
}

ChapterTaskOpenId = 1401        -- 章节任务
MainlineOrSideTaskOpenId = 1402 -- 主线/支线任务
DailyTaskOpenId = 1403          -- 每日任务
HeroSummon_Hero = 1100 --英雄招募
HeroSummon_Survivor = 1200 --幸存者招募
EquipUpgrade_Ex = 1302 --装备晋升
TaskBoxItemId = 26-- 任务宝箱 通过道具id 索引判断是否是任务宝箱数据
DayLastBoxInitId = 8148 -- 每日任务宝箱 最后显示图标
DayTaskGoodsId = 26 ---每日任务道具id