local table = table
local require = require
local string = string
local ipairs = ipairs
local tonumber = tonumber
local math = math
local festival_activity_cfg = require "festival_activity_cfg"
local gw_task_data = require "gw_task_data"
local reward_mgr = require "reward_mgr"
local game_scheme = require "game_scheme"
local os = require "os"
local time_util = require "time_util"
local festival_activity_mgr = require "festival_activity_mgr"
local data_mgr = require "data_mgr"
local log = require "log"
local util = require "util"
module("halloween_wingding_data")

---所有数据存储
local _d = data_mgr:CreateData("halloween_wingding_data")
---非服务器数据存储
local mc = _d.mde.const

--region 活动相关

---@public function 设置主活动ID
function SetActivityId(activityID)
    mc.activityID = activityID
end

---@public function 获取主活动ID
function GetActivityID()
    return mc.activityID
end

---@public function 设置活动任务ID
function SetActivityTaskID(activityTaskID)
    mc.activityTaskID = activityTaskID
end

---@public function 获取活动任务ID
function GetActivityTaskID()
    return mc.activityTaskID
end

---@public function 设置活动排行ID
function SetRankActivityID(activityRankID)
    mc.activityRankID = activityRankID
end

---@public function 获取活动排行ID
function GetRankActivityID()
    return mc.activityRankID
end

---@public function 获取活动数据
function GetActivityData()
    if not mc.activityData then
        mc.activityData = festival_activity_mgr.GetActivityDataByActivityID(mc.activityID)
    end
    return mc.activityData
end
--endregion

--region 任务数据

---@public function 获取宴会任务ID数组
function GetWingdingTaskIDArr()
    if not mc.wingdingTaskIDArr then
        local activityCfg = festival_activity_cfg.GetActivityCfgByActivityID(mc.activityTaskID)
        if activityCfg then
            mc.wingdingTaskIDArr = {}
            for i = 0, activityCfg.ctnID1.count - 1 do
                table.insert(mc.wingdingTaskIDArr, activityCfg.ctnID1.data[i])
            end
        end
    end
    return mc.wingdingTaskIDArr
end

---@public function 获取宴会提交任务ID
function GetWingdingSubmitNum()
    if not mc.submitTaskID then
        local activityCfg = festival_activity_cfg.GetActivityCfgByActivityID(mc.activityTaskID)
        if activityCfg then
            mc.submitTaskID = activityCfg.ctnID3.data[0]
        end
    end
    return mc.submitTaskID
end

---@public function 获取宴会提交任务数据
function GetSubmitData()
    if not mc.submitItemId or mc.submitMinCount or mc.submitReward then
        local submitTaskID = GetWingdingSubmitNum()
        if submitTaskID then
            local taskCfg = game_scheme:TaskMain_0(submitTaskID)
            if taskCfg then
                mc.submitItemId = taskCfg.ConditionValue2.data[0]
                mc.submitMinCount = taskCfg.ConditionValue1
                mc.submitReward = taskCfg.TaskReward
            end
        end
    end
    return mc.submitItemId, mc.submitMinCount, mc.submitReward
end

--endregion

function Clear()
    _d.mde:clear()
    mc = _d.mde.const
end