local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local face_item = require "face_item_new"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_arena_challenge_binding"

--region View Life
module("ui_arena_challenge")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.onChallengeListRenderFunc = function(...)
        self:OnChallengeListRenderFunc(...)
    end
    self.srt_challengeList.onItemRender = self.onChallengeListRenderFunc
    self.srt_challengeList.onItemDispose = function(scroll_rect_item,index)
        if scroll_rect_item.data and scroll_rect_item.data["faceItem"] then
            scroll_rect_item.data["faceItem"]:Dispose()
            scroll_rect_item.data["faceItem"] = nil
        end
    end

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:InitBaseShow(data)
    if not data then
        return
    end
    self.txt_BuyRefreshNum.text = data.buyRefreshExpend
    self:SetActive(self.txt_skipTips, data.needChallengeNum and data.needChallengeNum > 0)
    if data.needChallengeNum and data.needChallengeNum > 0 then
        -- 1009119 完成{%s1}次挑战可解锁快速战斗
        self.txt_skipTips.text = string.format2(lang.Get(1009119),data.needChallengeNum) 
    end
end

function UIView:UpdateChallengeNumShow(data)
    if not data then
        return
    end
    self.txt_TicketNum.text = data.ticketNum
    self:SetActive(self.tog_SkipBattle, data.canShowSkip)
    self.tog_SkipBattle.isOn = data.canSkip
end


function UIView:UpdateRefreshBtn(data)
    if not data then
        return
    end
    self:SetActive(self.btn_refreshListBuy, not data.canFreeRefresh)
    self:SetActive(self.btn_refreshListFree, data.canFreeRefresh)
    self.txt_freeRefreshNum.text = string.format("%d/%d", data.curFreeRefreshTime, data.maxFreeRefreshTime)
end

function UIView:UpdateChallengeList(data)
    if not data then
        return
    end
    self.srt_challengeList:SetData(data, #data)
    self.srt_challengeList:Refresh(-1, -1)
    self.srt_challengeList.renderPerFrames = 8
end

function UIView:OnChallengeListRenderFunc(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    scroll_rect_item.data[3] = scroll_rect_item.data[3] or {}

    local rank = scroll_rect_item:Get("rank")
    local faceTra = scroll_rect_item:Get("faceTra")
    local name = scroll_rect_item:Get("name")
    local power = scroll_rect_item:Get("power")
    local soldierImg = scroll_rect_item:Get("soldierImg")
    local soldierLv = scroll_rect_item:Get("soldierLv")
    local fightBtnGray = scroll_rect_item:Get("fightBtnGray")
    local fightText = scroll_rect_item:Get("fightText")

    rank.text = dataItem.rank
    name.text = string.format("%s %s", dataItem.worldStr, util.SplicingUnionShortName(dataItem.unionShortName, dataItem.name, true))
    power.text = util.NumberWithUnit2(dataItem.power)
    self:CreateSubSprite("CreateSoldierHeadAsset", soldierImg, dataItem.soldierIcon)
    soldierLv.text = string.format("Lv.%d", dataItem.soldierLv)
    --657014 挑战
    --657018 已挑战
    fightText.text = dataItem.canChallenge and lang.Get(657014) or lang.Get(657018)
    fightBtnGray:SetEnable(not dataItem.canChallenge)
    local item = scroll_rect_item.data["faceItem"] or face_item.CFaceItem():Init(faceTra.transform, nil, 1.2)
    item:SetFaceInfo(dataItem.faceStr, function()
        local mgr_personalInfo = require "mgr_personalInfo"
        mgr_personalInfo.ShowRoleInfoView(dataItem.roleID)
    end)
    item:SetNewBg(true)
    item:SetFrameID(dataItem.frameID, true)
    item:FrameEffectEnable(true, self.curOrder+1)
    scroll_rect_item.data["faceItem"] = item

    scroll_rect_item.InvokeFunc = scroll_rect_item.InvokeFunc or function(funcname,obj)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index,dataItem)
        end
    end
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
