local require = require
local print = print
local pairs = pairs
local ipairs = ipairs
local table = table
local typeof = typeof
local tonumber = tonumber
local math = math
local string = string
local tostring = tostring
local Canvas = CS.UnityEngine.Canvas
local Outline = CS.UnityEngine.UI.Outline
local Animator = CS.UnityEngine.Animator
local Screen = CS.UnityEngine.Screen
local gw_home_novice_util = require "gw_home_novice_util"
local screen_util = require "screen_util"
local Image         = CS.UnityEngine.UI.Image
local Card = CS.War.Battle.Card
local TextMeshProUGUI         = CS.TMPro.TextMeshProUGUI

local event = require "event"
local class = require "class"
local ui_base = require "ui_base"
local windowMgr = require "ui_window_mgr"
local hero_item = require "hero_item_new"
local hero_item_chuzhan = require "hero_item_chuzhan"
local lang = require "lang"
local player_mgr = require "player_mgr"
local util = require "util"
local game_scheme = require "game_scheme"
local calpro_mgr = require "calpro_mgr"
local halo_item = require "halo_item"
--local ui_window_mgr = require "ui_window_mgr"
local hero_mgr = require "hero_mgr"
local halo_mgr = require "halo_mgr"
local button_tips_trigger = require "button_tips_trigger"
local force_guide_system=require"force_guide_system"
local force_guide_event=require"force_guide_event"
local sort_order = require "sort_order"
local gw_hero_mgr = require "gw_hero_mgr"
local Vector2 = CS.UnityEngine.Vector2

-- local maze_mgr = require "maze_mgr"
local net_weapon_module = require "net_weapon_module"
local laymain_data = require "laymain_data"
local log = require "log"
local flow_text = require "flow_text"
local archive_data = require "archive_data"
local ui_select_model_node = require "ui_select_model_node"
local ui_select_hero_model = require "ui_select_hero_model"
local ui_drag_object = require "ui_drag_object"
local story_mgr = require "story_mgr"
local ui_growup_number      = require "ui_growup_number"
local common_new_pb 		= require "common_new_pb"
local battle_team_ui_mgr    = require "battle_team_ui_mgr"
local goods_item            = require "goods_item_new"

local battle_data = require "battle_data"
local hero_trial_mgr = require"hero_trial_mgr"
local oversea_res = require "oversea_res"
local gw_power_mgr = require "gw_power_mgr"
local camp_trial_data = require "camp_trial_data"
local gw_hero_data = require "gw_hero_data"
local Common_Util = CS.Common_Util.UIUtil

local Toggle 		= typeof(CS.UnityEngine.UI.Toggle)
local ScrollRectTable    = CS.UI.UGUIExtend.ScrollRectTable
local GameObject = CS.UnityEngine.GameObject
local RectTransform = CS.UnityEngine.RectTransform
local Transform = CS.UnityEngine.Transform
local Time = CS.UnityEngine.Time
local Vector3 = CS.UnityEngine.Vector3
local LeanTouch = CS.Lean.Touch.LeanTouch
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
local ParticalOrder = CS.War.UI.ParticalOrder
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local SpriteMask		= CS.UnityEngine.SpriteMask
local ImageGray = CS.War.UI.ImageGray
local Animation = CS.UnityEngine.Animation
local Text = CS.UnityEngine.UI.Text
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local ContentSizeFitter 	= CS.UnityEngine.UI.ContentSizeFitter
local RawImage = CS.UnityEngine.UI.RawImage
local LayoutRebuilder = CS.UnityEngine.UI.LayoutRebuilder
local NotchHelper = CS.com.notch.NotchHelper
local GWG   = GWG
local RectTransformUtility = CS.UnityEngine.RectTransformUtility
local Vector2 = CS.UnityEngine.Vector2
module("ui_select_hero")

HeroLineUpEnum ={
    LineUp1 = 1,
    LineUp2 = 2,
    LineUp3 = 3,
    LineUp4 = 4,
    LineUp5 = 5,
}

local RivalInfo = nil

local window = nil
local SelectHeroWindow = {}

--可选英雄集
local canSelectHeros = nil
local banSelectTips = nil

--传入的敌方英雄数据 (单队)
local enemyShowHp = nil
local enemyPower = nil

local isMulti = nil

local HeroData = {}
local haveTips = false
local prefs = 0

local maxSelectNumber = 5
local filterHeroSidList = {}
local curItem = nil

local showtips = false
local battleType = nil
local showWeaponGuide = nil

local haloID = nil
local forMaze = false
local isPeak = false
local factionWantedType = nil
local dontUpDateByServer = false
local dontUseWeapon = false
local tempPower = {}

local heroPositionTips = {} --光效引导中  特定英雄
local bLnstanceEnemy = nil
local tipsStr = nil
preSlot = nil
local showEnemyHeroRarity = false
local grindThreshold = nil   --碾压阈值
local showGrind = false      --当前打开是否展示碾压

local forSpaceGap = false
local forPlot = false
local forSecretPlace = false
local forMultiSecretPlace = false
local defaultHeros = nil -- 默认英雄

local spaceGapHeros = nil -- 时空裂缝系统英雄
local plotHeros = nil -- 剧情副本系统英雄
local lastSelectHeros = nil
local isShowTip = false
--不能使用的英雄提示id
local UnUseHeroTipId = 73212
local up_profile = {}
local enemyHeroProfiles = {}
local enemyHeroModelProfiles = {}
local enemyHeroModelFunc = {}

local helpNpcList = {}

local curEnemyIndex = HeroLineUpEnum.LineUp1
local TabData = {}
local selectedWeapon = {}
local timerDic={}
local useNew = false
local FleetID = nil--舰队远征挑战关卡ID

local fightBtnTxt = nil
local quickFightBtnTxt = nil
local isArenaDefence = false --是否为竞技场防御阵容
--战斗防守阵容使用（原联盟火车改通用
local isBattleDefenseLineup = nil  
--是否需要保存攻击阵容
local isNeedSaveAtkLine = nil  
--防守阵容需要重新保存编队下标与上配合使用（原联盟火车改通用
local defenseSetIndexLineup = {}
local isShowResetBtn = false
local isShowAdjustBtn = false
local isMainStoryChallenge = false
-- 防守还是进攻
ATTACK = 1
DEFENCE = 2
local curType = ATTACK--竞技场需要区分阵容是防守还是进攻
local isLimitHeroStar = false
local lowStarLvLimit = nil
local teamNum = nil
local leftRoleInfo = nil
local rightRoleInfo = nil
local getHeroFunc = nil
local hideTeamInfo = nil
local showFormatSetting = nil

local CONST_BATTLE_ARRAY_SWITCH_KEY = 29
local currentLevelData = nil
local needCloseWeekGuide = nil

local isShowTeamArtifact = false
local teamArtifactType = nil
--需要排除选择得武器
local excludeWeaponIds = nil 
local CanChangeTeam = nil

local IgnoreSoldier = false --忽略士兵战力

local heroCfgCache = {}
local optimizeHeroCache = {}
local isCanUseTrial = false -- 是否可以使用试用英雄

local closeLineUp = false

local enemyFaceID = 0       --敌人头像
local enemyFameID = 0       --敌人头像框
local enemyFaceIDNotByCfg = 0   --设置入口变更
local isShowPower = false       --是否显示战力
local levelNum = ""             --当前关卡
local curEnemyPower = 0
local mySoldierNum = 0
local enemySoldierNum = 0
local setLineupCallBack = nil -- 当前阵容保存回调 
local teamLangIndex =
{
    [1] = 601008,
    [2] = 601226,
    [3] = 601227,
    [4] = 601228,
}

SelectHeroWindow.widget_table =
{
    rawBg = {path = "bg", type = RawImage},
    bgTrans = {path = "bg", type = RectTransform},
    closeBtn = {path = "closeButton", type = "Button"},
    levelName = {path = "level/txtLevel", type = "Text"},
    fightBtn = {path = "grid_btn/fightButton", type = "Button"},
    nextButton = {path = "grid_btn/nextButton", type = "Button"},
    nextButtonWarn = {path = "grid_btn/nextButton/WarnIcon", type = RectTransform},
    spaceGapFightButton = {path = "grid_btn/spaceGapFightButton", type = "Button"},
    fightSwitcher = {path = "grid_btn/fightButton", type = SpriteSwitcher},
    fightTextColor = {path = "grid_btn/fightButton/Text", type = Text},
    grindBtn = {path = "grid_btn/grindButton", type = "Button"},
    grindImg = {path = "grid_btn/grindButton", type = "Image"},
    grindTipsBtn = {path = "grid_btn/grindButton/tips", type = "Button"},
    teamRecommendButton = {path = "teamRecommendButton", type = "Button"},
    TipsTra = {path = "Tips", type = RectTransform},
    
    heroListSkin = {path = "heroSelect/Scroll View", type = "ScrollRect"},
    heroSelect = {path = "heroSelect", type = RectTransform},
    scroll_table = {path = "heroSelect/Scroll View/Viewport/Content", type = ScrollRectTable},
    Rect_scroll_table = {path = "heroSelect/Scroll View/Viewport/Content", type = RectTransform},
    
    power = {path = "mainWindow/heros/Top/powerBg/MyPowerText", type = "Text"},
    
    enemyPowerBg = {path = "mainWindow/heros/enemypowerbg", type = RectTransform},
    epower = {path = "mainWindow/heros/Top/powerBg/EnemyPowerText", type = "Text"},
    
    MultiToggle = {path = "BottomContent/MultiToggle", type = RectTransform},
    MultiToggleText = {path = "BottomContent/MultiToggle/Icon/Text",type = "Text"},
    OneBtn = {path = "BottomContent/MultiToggle/OneBtn", type = "Button"},
    TwoBtn = {path = "BottomContent/MultiToggle/TwoBtn", type = "Button"},
    ThreeBtn = {path = "BottomContent/MultiToggle/ThreeBtn", type = "Button"},
    Btn4 = {path = "BottomContent/MultiToggle/FourBtn", type = "Button"},
    Btn5 = {path = "BottomContent/MultiToggle/FiveBtn", type = "Button"},
    
    
    OneBtnTxt = {path = "BottomContent/MultiToggle/OneBtn/win", type = "Text"},
    TwoBtnTxt = {path = "BottomContent/MultiToggle/TwoBtn/win", type = "Text"},
    ThreeBtnTxt = {path = "BottomContent/MultiToggle/ThreeBtn/win", type = "Text"},
    BtnTxt4 = {path = "BottomContent/MultiToggle/FourBtn/win", type = "Text"},
    BtnTxt5 = {path = "BottomContent/MultiToggle/FiveBtn/win", type = "Text"},
    
    OneBtnSel = {path = "BottomContent/MultiToggle/OneBtn/JSnewqieyielan/JSnewqieyiehuang", type = "Image"},
    TwoBtnSel = {path = "BottomContent/MultiToggle/TwoBtn/JSnewqieyielan/JSnewqieyiehuang", type = "Image"},
    ThreeBtnSel = {path = "BottomContent/MultiToggle/ThreeBtn/JSnewqieyielan/JSnewqieyiehuang", type = "Image"},
    BtnSel4 = {path = "BottomContent/MultiToggle/FourBtn/JSnewqieyielan/JSnewqieyiehuang", type = "Image"},
    BtnSel5 = {path = "BottomContent/MultiToggle/FiveBtn/JSnewqieyielan/JSnewqieyiehuang", type = "Image"},
    
    
    OneBtnSelTxt = {path = "BottomContent/MultiToggle/OneBtn/JSnewqieyielan/JSnewqieyiehuang/Text", type = "Text"},
    TwoBtnSelTxt = {path = "BottomContent/MultiToggle/TwoBtn/JSnewqieyielan/JSnewqieyiehuang/Text", type = "Text"},
    ThreeBtnSelTxt = {path = "BottomContent/MultiToggle/ThreeBtn/JSnewqieyielan/JSnewqieyiehuang/Text", type = "Text"},
    BtnSelTxt4 = {path = "BottomContent/MultiToggle/FourBtn/JSnewqieyielan/JSnewqieyiehuang/Text", type = "Text"},
    BtnSelTxt5 = {path = "BottomContent/MultiToggle/FiveBtn/JSnewqieyielan/JSnewqieyiehuang/Text", type = "Text"},
    
    OneBtnLock = {path = "BottomContent/MultiToggle/OneBtn/lockIcon", type = "Image"},
    TwoBtnLock = {path = "BottomContent/MultiToggle/TwoBtn/lockIcon", type = "Image"},
    ThreeBtnLock = {path = "BottomContent/MultiToggle/ThreeBtn/lockIcon", type = "Image"},
    BtnLock4 = {path = "BottomContent/MultiToggle/FourBtn/lockIcon", type = "Image"},
    
    mainWindow = {path = "mainWindow", type = RectTransform},
    
    tnode0 = {path = "tipPoint/0", type = RectTransform},
    tnode1 = {path = "tipPoint/1", type = RectTransform},
    tnode2 = {path = "tipPoint/2", type = RectTransform},
    tnode3 = {path = "tipPoint/3", type = RectTransform},
    tnode4 = {path = "tipPoint/4", type = RectTransform},
    tnode5 = {path = "tipPoint/5", type = RectTransform},
    
    openPanel = {path ="openPanel", type = RectTransform},
    menuOpenBtn = {path = "TypeTogList/menuOpenBtn", type = "Button"},
    MaskBtn = {path = "Mask", type = "Button"},
    campTopRoot = {path = "campTopList", type = RectTransform},
    menuCloseBtn = {path = "campTopList/menuCloseBtn", type = "Button"},
    campTog_1 = {path = "campTopList/All", type = Toggle},
    campTog_2 = {path = "campTopList/zhanshiCZ", type = Toggle},
    campTog_3 = {path = "campTopList/fashiCZ", type = Toggle},
    campTog_4 = {path = "campTopList/youxiaCZ", type = Toggle},
    campTog_5 = {path = "campTopList/cikeCZ", type = Toggle},
    campTog_6 = {path = "campTopList/fuzhuCZ", type = Toggle},
    
    typeTogRoot = {path = "TypeTogList", type = "Image"},
    typeTog_1 = {path = "TypeTogList/Tog1", type = Toggle},
    typeTog_1Img = {path = "TypeTogList/Tog1", type = "Image"},
    typeTog_2 = {path = "TypeTogList/Tog2", type = Toggle},
    typeTog_3 = {path = "TypeTogList/Tog3", type = Toggle},
    typeTog_4 = {path = "TypeTogList/Tog4", type = Toggle},
    typeTog_5 = {path = "TypeTogList/Tog5", type = Toggle},
    typeTog_6 = {path = "TypeTogList/Tog6", type = Toggle},
    typeTog_7 = {path = "TypeTogList/Tog7", type = Toggle},
    
    typeTogGray_1 = {path = "togMask/Tog1", type = RectTransform},
    typeTogGray_2 = {path = "togMask/Tog2", type = RectTransform},
    typeTogGray_3 = {path = "togMask/Tog3", type = RectTransform},
    typeTogGray_4 = {path = "togMask/Tog4", type = RectTransform},
    typeTogGray_5 = {path = "togMask/Tog5", type = RectTransform},
    typeTogGray_6 = {path = "togMask/Tog6", type = RectTransform},
    typeTogGray_7 = {path = "togMask/Tog7", type = RectTransform},
    
    haloTra = {path = "BottomContent/HaloAndWeapon/halo",type = Transform},
    haloRoot = {path = "BottomContent/HaloAndWeapon/halo/iconRoot/mask", type = Transform},
    haloEffect = {path = "BottomContent/HaloAndWeapon/halo/haloEffect", type = ParticalOrder},
    heroTip = {path = "GuideWin", type = RectTransform},
    
    haloEnemyTra = {path = "mainWindow/heros/Top/other/halo_enemy",type = RectTransform},
    
    haloEnemyRoot = {path = "mainWindow/heros/Top/other/halo_enemy/iconRoot/mask", type = Transform},
    haloEnemyEffect = {path = "mainWindow/heros/Top/other/halo_enemy/haloEffect", type = ParticalOrder},
    
    weaponButton = {path = "BottomContent/HaloAndWeapon/weaponButton", type = "Button"},
    weaponEffect = {path = "BottomContent/HaloAndWeapon/weaponButton/UI_lianyiguangxiao", type = SortingGroup},
    weaponIcon = {path = "BottomContent/HaloAndWeapon/weaponButton/mask/Icon", type = "Image"},
    weaponMask = {path = "BottomContent/HaloAndWeapon/weaponButton/mask", type = "Image"},
    weaponLevel = {path = "BottomContent/HaloAndWeapon/weaponButton/Level",type = "Text"},
    
    spriteMask = {path = "heroSelect/GameObject", type = typeof(SpriteMask)},
    
    spaceGapTipButton = {path = "grid_btn/spaceGapTipButton", type = "Button"},
    spaceGapTipButtonReddot = {path = "grid_btn/spaceGapTipButton/reddot", type = "Image"},
    spaceGapTips = {path = "spaceGapTips", type = "RectTransform"},
    tipsTxt = {path = "spaceGapTips/tips", type = TextMeshProUGUI},
    
    multiSecretTipTxt = {path = "multiSecretTip", type = "Text"},
    
    multiSecretRoot = {path = "multiSecretRoot", type = RectTransform},
    campLimitImg = {path = "multiSecretRoot/Gameobject/campLimit/Icon", type = SpriteSwitcher},
    replceCampLimitImg = {path = "multiSecretRoot/Gameobject/replceCampLimit/Icon", type = SpriteSwitcher},
    campLimitTxt = {path = "multiSecretRoot/Gameobject/campLimit/Text", type = "Text"},
    replceCampLimitTxt = {path = "multiSecretRoot/Gameobject/replceCampLimit/Text", type = "Text"},
    multiSecretObj = {path = "multiSecretRoot/Gameobject", type = Transform},
    
    resetraintBtn = {path = "resetraintTipsBtn", type = "Button"},
    
    fightBtnEff = {path = "grid_btn/fightButton/UI_chuzheng_anniu", type = (SortingGroup)},
    grindBtnEff = {path = "grid_btn/grindButton/UI_chuzheng_anniu", type = (SortingGroup)},
    
    grindOutline = { path = "grid_btn/grindButton/Text", type = Outline, },
    grindText = { path = "grid_btn/grindButton/Text", type = "Text", },
    grindTipsCanvas = {path = "grid_btn/grindButton/tips", type = Canvas},
    
    powerFlowRoot = {path = "mainWindow/heros", type = "RectTransform"},
    
    mypowerbg = {path = "mainWindow/heros/Top/powerBg/mypowerbg", type = RectTransform},
    
    powerUp = {path = "mainWindow/heros/Top/powerBg/mypowerbg/powerUp", type = Animation},
    powerUpText = {path = "mainWindow/heros/Top/powerBg/mypowerbg/powerUp/Text", type = "Text"},
    
    powerDown = {path = "mainWindow/heros/Top/powerBg/mypowerbg/powerDown", type = Animation},
    powerDownText = {path = "mainWindow/heros/Top/powerBg/mypowerbg/powerDown/Text", type = "Text"},
    
    --属性加成动画
    heroProUp = {path = "mainWindow/heros/heroProUp", type = Animator},
    
    --舰队远征星星条件
    starConditionRoot = { path = "starCondition", type = RectTransform, event_name = "" },--星星条件节点
    starConditionItem = { path = "starCondition/ListItem", type = RectTransform, event_name = "" },--星星条件Item
    contentSize = { path = "starCondition", type = ContentSizeFitter, event_name = "" },--星星条件节点
    --舰队远征一键上阵
    quickFightBtn   = {path = "grid_btn/quickFightBtn", type = "Button"},
    quickFightBtnTxt   = {path = "grid_btn/quickFightBtn/Text", type = "Text"},

    resetBtn = {path = "resetBtn", type = "Button"},--竞技场时空擂台重置队伍按钮
    
    --竞技场需要显示敌方武器
    Btn_weapon_enemy = {path = "enmyWeaponButton", type = "Button"},
    Icon_weapon_enemy = {path = "enmyWeaponButton/Icon", type = "Image"},
    Mask_weapon_enemy = {path = "enmyWeaponButton/blackmask", type = "RectTransform"},
    
    adjustBtn = {path = "adjustBtn", type = "Button"},
    btnArtifact = {path = "btnArtifact", type = "Button"},
    gridBtnName = {path = "grid_btn/spaceGapTipButton/Text", type = "Text"},
    
    
    ---阵营试炼buff
    rect_trialBuff = {path = "BottomContent/rect_trialBuff", type = RectTransform},
    txt_buff_desc = {path = "BottomContent/rect_trialBuff/rect_buffTips/txt_buff_desc", type = "Text"},
    txt_buff_value = {path = "BottomContent/rect_trialBuff/rect_buffTips/txt_buff_value", type = "Text"},
    img_buff_normal = {path = "BottomContent/rect_trialBuff/btn_buff_normal/mask/img_buff_normal", type = "Image"},

    --敌我头像
    MyFaceItem = {path = "mainWindow/heros/Top/powerBg/MyFaceItem", type = RectTransform},
    EnemyFaceItem = {path = "mainWindow/heros/Top/powerBg/EnemyFaceItem", type = RectTransform},
    
    --对战提示
    powerBg = {path = "mainWindow/heros/Top/powerBg", type = RectTransform},
    LevelBg = {path = "mainWindow/heros/Top/powerBg/LevelBg", type = RectTransform},
    levelText = {path = "mainWindow/heros/Top/powerBg/LevelBg/levelText", type = "Text"},
    VSIcon = {path = "mainWindow/heros/Top/powerBg/VSIcon", type = RectTransform},

    BottomContent = {path = "BottomContent", type = RectTransform},

    arenaPowerObj = {path = "mainWindow/heros/Top/PowerBg",type = RectTransform},
    arenaPowerText = {path = "mainWindow/heros/Top/PowerBg/Power", type = "Text"},

    heroUpgradeBtn_1 = {path = "bg/BtnGroup_1/PlayerUpgradeBtn_1",type = "Button"},
    heroUpgradeBtn_2 = {path = "bg/BtnGroup_1/PlayerUpgradeBtn_2",type = "Button"},
    heroUpgradeBtn_3 = {path = "bg/BtnGroup_1/PlayerUpgradeBtn_3",type = "Button"},
    heroUpgradeBtn_4 = {path = "bg/BtnGroup_1/PlayerUpgradeBtn_4",type = "Button"},
    heroUpgradeBtn_5 = {path = "bg/BtnGroup_1/PlayerUpgradeBtn_5",type = "Button"},

    heroUpgradeBtnTrans_1 = {path = "bg/BtnGroup_1/PlayerUpgradeBtn_1",type = "RectTransform"},
    heroUpgradeBtnTrans_2 = {path = "bg/BtnGroup_1/PlayerUpgradeBtn_2",type = "RectTransform"},
    heroUpgradeBtnTrans_3 = {path = "bg/BtnGroup_1/PlayerUpgradeBtn_3",type = "RectTransform"},
    heroUpgradeBtnTrans_4 = {path = "bg/BtnGroup_1/PlayerUpgradeBtn_4",type = "RectTransform"},
    heroUpgradeBtnTrans_5 = {path = "bg/BtnGroup_1/PlayerUpgradeBtn_5",type = "RectTransform"},
}

function SelectHeroWindow:UpdateStarConsition(data)
    local p = self.starConditionRoot.transform
    for i = p.childCount - 1, 2, -1 do
        GameObject.Destroy(p:GetChild(i).gameObject)
    end
    if data then
        for i, v in pairs(data) do
            if v then
                self:InstantiateStarItem(v, i)
            end
        end
    end
    self.contentSize.horizontalFit = 0
    self.contentSize.horizontalFit = 2
    util.DelayCall(0.1,function ()
        if window and window:IsValid() then
            self.contentSize.horizontalFit = 0
            self.contentSize.horizontalFit = 2
        end
    end)
end

local notFitColor = {r=189/255,g=240/255,b=254/255,a=1}
local FitColor = {r=0/255,g=255/255,b=0/255,a=1}
function SelectHeroWindow:InstantiateStarItem(data, i)
    if not self.starCondition then self.starCondition = {} end
    local item = self.starCondition[i] or {}
    local go = item and item.obj or GameObject.Instantiate(self.starConditionItem.gameObject,self.starConditionRoot.transform)
    if go then
        go.transform.localScale = Vector3.one
        go:SetActive(data ~= nil)
        item.obj = go
        item.Txt = item.Txt or go.transform:Find("Text"):GetComponent(typeof(Text))
        item.Img = item.Img or go.transform:Find("Image"):GetComponent(typeof(ImageGray))
        local Txt = item.Txt
        local Img = item.Img
        self.starCondition[i] = item
        if data ~= nil then
            local t = ""
            for index=1,#data do
                if t~="" then
                    t = string.format("%s,%s",t,data[index])
                else
                    t = string.format("%s",data[index])
                end
            end
            Txt.text = t
            if i==2 then
                -- local fleet_expedition_data = require "fleet_expedition_data"
                -- local isFit = fleet_expedition_data.IsAllHeroFitStar2(FleetID,GetSelectedHero())
                -- Img:SetEnable(not isFit)
                -- item.Txt.color = not isFit and notFitColor or FitColor
            else
                Img:SetEnable(true)
            end
        end
    end
end

--[[设置舰队远征挑战关卡ID]]
function SetFleetExpedition(checkpointID)
    FleetID = checkpointID
end

--初始化舰队远征星星条件
function SelectHeroWindow:InitStarConditions()
    -- self.starConditionRoot.gameObject:SetActive(FleetID)
    -- if FleetID then
    --     local fleet_expedition_data = require "fleet_expedition_data"
    --     local cfg = fleet_expedition_data.GetOdysseyDataByCheckPointID(FleetID)
    --     local starConditions = {}
    --     if cfg then
    --         if cfg.stratRequire1 then
    --             table.insert(starConditions,fleet_expedition_data.GetAllStarCondition(cfg.stratRequire1))
    --         end
    --         if cfg.stratRequire2 then
    --             table.insert(starConditions,fleet_expedition_data.GetAllStarCondition(cfg.stratRequire2))
    --         end
    --         if cfg.stratRequire3 then
    --             table.insert(starConditions,fleet_expedition_data.GetAllStarCondition(cfg.stratRequire3))
    --         end
    --     end
    --     if starConditions then
    --         self:UpdateStarConsition(starConditions)
    --     end
    -- end
    -- self.quickFightBtn.gameObject:SetActive(FleetID)
end

function SelectHeroWindow:InitHeroAddPro(isShow,hpPro,attackPro,heroPos,tFlag)
    if not util.IsObjNull(self["addProImg_"..heroPos]) then
        self["addProImg_"..heroPos].gameObject:SetActive(isShow)
    else
        log.Error("not find addProImg_ in heroPos:"..heroPos)
    end

    if not util.IsObjNull(self["addProTxt_"..heroPos]) then
        self["addProTxt_"..heroPos].text = tFlag == 1 and string.format("<color=#FFCD20>%s</color>",hpPro) or string.format("<color=#F888FF>%s</color>",hpPro)
    else
        log.Error("not find addProTxt_ in heroPos:"..heroPos)
    end
end

--获取英雄T级标识的属性值
function SelectHeroWindow:GetHeroProData(heroID,starLv)
    if not heroID or not starLv then return end
    local cfg = archive_data.GetDatasByHeroID(heroID)
    local hpPro=nil
    local attackPro=nil
    local tFlag = nil
    if cfg then
        -- --print("heroID",heroID,"starLv",starLv,"cfg.tFlag",cfg.tFlag)
        -- tFlag = cfg.tFlag
        -- local secretplace_mgr = require "secretplace_mgr"
        -- local csvDatas = secretplace_mgr.GetChallengeDeckProCSV()
        -- if csvDatas and cfg.tFlag and csvDatas[starLv] then
        --     local csv = csvDatas[starLv][cfg.tFlag]
        --     if csv then
        --         local hpPro = string.format("%s%%",((csv.propID1.data[1]+10000)/10000)*100)
        --         local attackPro = string.format("%s%%",((csv.propID2.data[1]+10000)/10000)*100)
        --         return hpPro,attackPro,tFlag
        --     end
        -- end
    end
    return hpPro,attackPro,tFlag
end



--播放属性加成动画
function UpdateProAni(isShow,hpPro,attackPro,heroPos,tFlag)
    if window then
        if not isShow or not hpPro or not attackPro then
            if window["addProRoot_"..heroPos] then
                -- window["addProRoot_"..heroPos].gameObject:SetActive(false)
                window["addProImg_"..heroPos].gameObject:SetActive(false)
            end
            return
        end
        --播放动画效果
        if window["addProRoot_"..heroPos] then
            window["addProRoot_"..heroPos].gameObject:SetActive(isShow)
            window["addProImg_"..heroPos].gameObject:SetActive(false)
        end
        window.heroProUpList = window.heroProUpList or {}
        local listItem = window.heroProUpList[heroPos] or {}
        window.heroProUpList[heroPos] = listItem
        local go = listItem.obj or GameObject.Instantiate(window.heroProUp.gameObject,window["addProRoot_"..heroPos])
        listItem.obj = go
        listItem.sPlusHeroProUpText = listItem.sPlusHeroProUpText or go.transform:GetChild(1):GetComponent(typeof(Text))
        listItem.sHeroProUpText = listItem.sHeroProUpText or go.transform:GetChild(2):GetComponent(typeof(Text))
        listItem.ani = listItem.ani or go.transform:GetComponent(typeof(Animator))
        go.transform.localScale = Vector3.one
        go:SetActive(true)
        if listItem.ani.enabled then
            listItem.ani.enabled = false
        end
        listItem.ani.enabled = true
        listItem.sPlusHeroProUpText.text = tFlag == 1 and string.format("%s\n%s",string.format("%s",hpPro),string.format("%s",attackPro)) or ""
        listItem.sHeroProUpText.text = tFlag == 2 and string.format("%s\n%s",string.format("%s",hpPro),string.format("%s",attackPro)) or ""
        util.DelayCall(2.6,function()
            if window and window:IsValid() then
                window:InitHeroAddPro(isShow,hpPro,attackPro,heroPos,tFlag)
            end
        end)
    end
end

function SelectHeroWindow:ctor(selfType)
    local card_sprite_asset = require "card_sprite_asset"
    self.spriteAsset = card_sprite_asset.CreateSpriteAsset()

    self.matchHomeIndicator = true

    self.onPlayAShowAni = nil --播放完打开动画回调
    --self.onCloseEvent = nil   --关闭回调
    --self.onFightEvent = nil   --点击战斗回调
    self.onShowEvent = nil    --ui初始化回调

    --用于屏蔽哪些框框是不可点的（新手引导需求）
    self.arrDown={0,1,2,3,4,5}
    self.arrUp={0,1,2,3,4,5}

    self.botHeroSlot = {}
    self.isUpdatingPower=false
end

function SetIsShowTip(isShow)
    isShowTip = isShow
end

function SetUnUseHeroTipId(id)
    UnUseHeroTipId = id
end

function GetTabDataByLine(teamIdx)
    if TabData[teamIdx] == nil then
        TabData[teamIdx] = {}
    end
    return TabData[teamIdx]
end

function GetCurData()
    if TabData[curEnemyIndex] == nil then
        TabData[curEnemyIndex] = {}
    end
    return TabData[curEnemyIndex]
end

--下标为0
function GetSelectedHero()
    local data = GetCurData()
    if not data.selectedHero then
        data.selectedHero = {}
    end
    return data.selectedHero
end

function GetCacheData()
    return GetCurData().cacheData
end

--设置是否显示重置按钮
function SetResetBtnVisible(bool)
    isShowResetBtn = bool
    -- if window and window:IsValid() then
    --     window.resetBtn.gameObject:SetActive(bool)
    -- end
end

--设置挑战按钮文本
function SetFightBtnTxt(txt)
    fightBtnTxt = txt
    -- if window and window:IsValid() then
    --     window.fightTextColor.text = txt
    -- end
end

function SetArenaDefenceState(value)
    isArenaDefence = value
    if isShowPower and isArenaDefence then
        isArenaDefence = false
    end
end

---@see 设置一件上阵按钮文本(同时会保存阵容）
---@param txt string 文本
---@param setLineup function 按钮事件
---@param isBattleDefense boolean 是否是防守阵容打开
---@param _isNeedSaveAtkLine boolean 是否需要保存攻击阵容
function SetQuickFightBtnTxt(txt, setLineup, isBattleDefense, _isNeedSaveAtkLine)
    setLineupCallBack = setLineup
    quickFightBtnTxt = txt
    isBattleDefenseLineup = isBattleDefense
    isNeedSaveAtkLine = _isNeedSaveAtkLine 
    -- if window and window:IsValid() then
    --     window.fightTextColor.text = txt
    -- end
end

---@see 编队进攻阵容保存
function SetAtkLineupSave()
    if((battleType == common_new_pb.AllianceTrain or isNeedSaveAtkLine) and not isBattleDefenseLineup)then
        if(setLineupCallBack)then
            setLineupCallBack()
        end
    end
end

function GetCurEnemyIndex()
    return curEnemyIndex
end

function SetCurEnemyIndex(state)
    curEnemyIndex = state
end

--1.设置是否显示队伍调整按钮2.设置是否是主线
function SetAdjustBtnVisible(bool,bool1)
    isShowAdjustBtn = bool
    isMainStoryChallenge = bool1
end

--设置阵容类型
function SetBattleType(_type)
    curType = _type
    isCanUseTrial = curType == ATTACK
end

--设置英雄列表星级要求
function SetLimitHeroStar(isLimit,_starLvLimit)
    isLimitHeroStar = isLimit
    lowStarLvLimit = _starLvLimit
end

--设置竞技场左右边玩家信息
function SetRoleInfos(_leftRoleInfo, _rightRoleInfo, _getHeroFunc, _hideTeamInfo)
    leftRoleInfo = _leftRoleInfo
    rightRoleInfo = _rightRoleInfo
    getHeroFunc = _getHeroFunc
    hideTeamInfo = _hideTeamInfo
end

--设置队伍数量
function SetTeamCount(count)
    teamNum = count
end

--得到队伍数量
function GetLineCount()
    if teamNum and teamNum>0 then
        return teamNum 
    end
	local common_new_pb = require "common_new_pb"
    if battleType == common_new_pb.WeekendArena then--时空擂台
        local myTeamCount = GetTeamCount()--队伍数量
        return myTeamCount
    end
    if IsThreeLine() then
        return 3
    end
    if IsMulti() then
        return 2
    end
    return 1
end

--设置默认英雄
function SetDefaultHeros(heros)
    defaultHeros = heros
end

--得到上锁槽位信息
function GetSlotState()
    return GetCurData().slotState
end

--设置上锁槽位
function SetSlotState(slots,line)
    line = line or 1
    TabData[line] = TabData[line] or {}
    TabData[line].slotState = slots
end

function GetPowerFlowRoot()
    return GetCurData().powerFlowRoot
end

function SelectHeroWindow:InitHeroSelect()
    
    self.heroObjs = {}
    self.heroModels = {}
    self.heroModelsFunc = {}
    self.heroMySNodes = ui_select_model_node.GetMyQualityNodes()
    self.heroEnemySNodes = ui_select_model_node.GetEnemyQualityNodes()
    self.heroNode = ui_select_model_node.GetHeroNode()
    self.heroSelectCamera = ui_select_model_node.GetHeroSelectCamera()
    self.heroShadowNode = ui_select_model_node.GetHeroSelectModel().transform:Find("Shadow")
    for i=0, 4 do
        self.heroMySNodes[i]:Switch(0)
        Common_Util.SetActive(self.heroMySNodes[i],true)
        --self.heroMySNodes[i].gameObject:SetActive(true)
        Common_Util.SetActive(ui_select_model_node.GetMyTextNodes()[i],true)
        --ui_select_model_node.GetMyTextNodes()[i].gameObject:SetActive(true)
        
        self.heroEnemySNodes[i]:Switch(0)
        --Common_Util.SetActive(self.heroEnemySNodes[i],true)
        if isBattleDefenseLineup then
            Common_Util.SetActive(self.heroEnemySNodes[i],false)
            Common_Util.SetActive(ui_select_model_node.GetEnemyTextNodes()[i],false)
        end
    end
    -- if screen_util.height / screen_util.width < 1280 / 720 then
    --     -- local y = ( screen_util.height / screen_util.width * 720 / 1280) % 2
    --     -- self.heroSelectCamera.lensShift = {x = 0, y = 0.1}
    --     self.heroSelectCamera.lensShift = {x = 0, y = 0.03}
    -- elseif screen_util.width >= 720 and screen_util.height > 1280 then
    --     -- local y = ( screen_util.width / screen_util.height * 1280 / 720) % 2
    --     -- if y ~= 0 then
    --     --     y = y - 1
    --     --     if y < 0 then
    --     --         y = -y
    --     --     end
    --     --     if y > 0.35 then
    --     --         y = 0.3
    --     --     end
    --     --     if y ~= 0 and y < 0.24 then
    --     --         y = y + 0.11
    --     --     end
    --     -- end
    --     -- self.heroSelectCamera.lensShift = {x = 0, y = y}
    --     self.heroSelectCamera.lensShift = {x = 0, y = 0.1}
    -- else
    --     self.heroSelectCamera.lensShift = {x = 0, y = 0}
    -- end
    -- for i=6, 11 do
    --     self.heroEnemySNodes[i - 6] = self.heroShadowNode.transform:GetChild(i)
    --     self.heroEnemySNodes[i - 6].gameObject:SetActive(true)
    -- end
    if screen_util.height / screen_util.width <= 1.5 then
        local v =  (720 / 1280) / (screen_util.width / screen_util.height)
        self.heroSelectCamera.sensorSize = {x = self.heroSelectCamera.sensorSize.x / v, y = self.heroSelectCamera.sensorSize.y}
        
        ui_select_model_node.GetSelectHeroBg().transform.localScale = {x = ui_select_model_node.GetSelectHeroBg().transform.localScale.x / v, y = ui_select_model_node.GetSelectHeroBg().transform.localScale.y / v, z = ui_select_model_node.GetSelectHeroBg().transform.localScale.z / v }
    end

    if screen_util.height / screen_util.width >= 2 then
        local v =  (screen_util.width / screen_util.height) / (720 / 1280)
        --self.heroSelect.sizeDelta = {x = self.heroSelect.sizeDelta.x, y = self.heroSelect.sizeDelta.y * v}
        self.heroSelect.offsetMax = {x = self.heroSelect.offsetMax.x, y = self.heroSelect.offsetMax.y / v}
        self.BottomContent.offsetMax = {x = self.BottomContent.offsetMax.x, y = self.BottomContent.offsetMax.y / v}
    end
    
    ui_select_model_node.GetHeroSelectModel().gameObject:SetActive(true)

    self.epos0 = ui_select_model_node.GetEnemyHerolayout():Find("hero_0"):GetComponent(typeof(RectTransform))
    self.epos1 = ui_select_model_node.GetEnemyHerolayout():Find("hero_1"):GetComponent(typeof(RectTransform))
    self.epos2 = ui_select_model_node.GetEnemyHerolayout():Find("hero_2"):GetComponent(typeof(RectTransform))
    self.epos3 = ui_select_model_node.GetEnemyHerolayout():Find("hero_3"):GetComponent(typeof(RectTransform))
    self.epos4 = ui_select_model_node.GetEnemyHerolayout():Find("hero_4"):GetComponent(typeof(RectTransform))
    --self.epos5 = ui_select_model_node.GetEnemyHerolayout():Find("hero_5"):GetComponent(typeof(RectTransform))

    self.pos0 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_0"):GetComponent(typeof(RectTransform))
    self.pos1 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_1"):GetComponent(typeof(RectTransform))
    self.pos2 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_2"):GetComponent(typeof(RectTransform))
    self.pos3 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_3"):GetComponent(typeof(RectTransform))
    self.pos4 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_4"):GetComponent(typeof(RectTransform))
    --self.pos5 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_5"):GetComponent(typeof(RectTransform))

    self.artifactSlot={}
    self.artifactSlot[0] = self.pos0
    self.artifactSlot[1] = self.pos1
    self.artifactSlot[2] = self.pos2
    self.artifactSlot[3] = self.pos3
    self.artifactSlot[4] = self.pos4
	

    --特殊Buff加成（挑战秘境）
    self.addProRoot_0 =self.pos0:GetComponent(typeof(RectTransform))
    self.addProImg_0 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_0/Image"):GetComponent(typeof(Image))
    self.addProTxt_0 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_0/Image/Text"):GetComponent(typeof(Text))
    self.addProOutLine_0 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_0"):GetComponent(typeof(Outline))

    self.addProRoot_1 =self.pos1:GetComponent(typeof(RectTransform))
    self.addProImg_1 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_1/Image"):GetComponent(typeof(Image))
    self.addProTxt_1 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_1/Image/Text"):GetComponent(typeof(Text))
    self.addProOutLine_1 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_1"):GetComponent(typeof(Outline))

    self.addProRoot_2 =self.pos2:GetComponent(typeof(RectTransform))
    self.addProImg_2 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_2/Image"):GetComponent(typeof(Image))
    self.addProTxt_2 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_2/Image/Text"):GetComponent(typeof(Text))
    self.addProOutLine_2 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_2"):GetComponent(typeof(Outline))

    self.addProRoot_3 =self.pos3:GetComponent(typeof(RectTransform))
    self.addProImg_3 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_3/Image"):GetComponent(typeof(Image))
    self.addProTxt_3 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_3/Image/Text"):GetComponent(typeof(Text))
    self.addProOutLine_3 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_3"):GetComponent(typeof(Outline))

    self.addProRoot_4 =self.pos4:GetComponent(typeof(RectTransform))
    self.addProImg_4 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_4/Image"):GetComponent(typeof(Image))
    self.addProTxt_4 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_4/Image/Text"):GetComponent(typeof(Text))
    self.addProOutLine_4 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_4"):GetComponent(typeof(Outline))

    --self.addProRoot_5 =self.pos5:GetComponent(typeof(RectTransform))
    --self.addProImg_5 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_5/Image"):GetComponent(typeof(Image))
    --self.addProTxt_5 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_5/Image/Text"):GetComponent(typeof(Text))
    --self.addProOutLine_5 = ui_select_model_node.GetMyHerolayout():Find("dynamic/hero_5"):GetComponent(typeof(Outline))

    self.heroProUp = ui_select_model_node.GetMyHerolayout():Find("heroProUp"):GetComponent(typeof(Animator))

    --for i = 0, maxSelectNumber - 1 do
    --    if self.heroMySNodes then
    --        self.heroMySNodes[i].gameObject:SetActive(not (GetCurData() and GetCurData().slotState and GetSlotState()[i] == 0))
    --        if ui_select_model_node.GetMyTextNodes() and ui_select_model_node.GetMyTextNodes()[i] then
    --            ui_select_model_node.GetMyTextNodes()[i].gameObject:SetActive(not (GetCurData() and GetCurData().slotState and GetSlotState()[i] == 0))
    --        end
    --    end
    --end
end

function SelectHeroWindow:Init()
    --local myTransform = self.UIRoot:GetComponent(typeof(RectTransform))
    --local moveOffset = (1 - myTransform.anchorMax.y) * Screen.height
    --self.rawBg.transform.anchoredPosition = {x=0,y=moveOffset}
    if screen_util.IsFoldableScreen() then --兼容折叠屏
        self.rawBg.transform.anchoredPosition = {x=0,y=0}
    end
    
    local safeArea = NotchHelper.GetSafeArea()
    local offset = Screen.height - safeArea.height -- 获取刘海高度
    --self.enemyPowerBg.anchoredPosition = {x=-74.6, y = -37.4 - offset}
    --self.haloEnemyTra.anchoredPosition = {x=14.39893, y = -53 - offset}
    self.Btn_weapon_enemy.transform.anchoredPosition = {x = 48,y = -141 - offset}
    
    --if battleType == common_new_pb.Ashdungeon then
    --    self.scroll_table.tileSize = {x=0, y = 150, z=0}
    --else
    --    self.scroll_table.tileSize = {x=0, y = 135, z=0}
    --end

    ui_drag_object.InitDrag(false)
    self:RefreshToggleView()     
    self:InitWindowData()
    ui_select_model_node.GetBackgroundSprite(false,nil,function ()
        ui_select_model_node.InitBattleSelect(function (_rt)
            self:InitHeroSelect()

            self:InitHeroWin()
            self:SetPowerShow()

            self.rawBg.texture = _rt
            --Common_Util.SetSize(self.bgTrans,_rt.width,_rt.height)
            self.rawBg.gameObject:SetActive(true)
            local common_new_pb = require "common_new_pb"

            self.resetBtn.gameObject:SetActive(isShowResetBtn)
            self.adjustBtn:SetActive(isShowAdjustBtn == true and GetLineCount() > 1)
            --self.btnArtifact.gameObject:SetActive(isShowTeamArtifact)
            -- 单人秘境属性加成动画
            if forMultiSecretPlace then
                local _selectHero = GetSelectedHero()
                for i,v in pairs(_selectHero) do
                    local hpPro,attackPro,tFlag = window:GetHeroProData(v.heroID,v.numProp and v.numProp.starLv or v.heroStar)
                    UpdateProAni(true,hpPro,attackPro,i,tFlag)
                end
            end
        end)
    end)
end

function SelectHeroWindow:InitWindowData()
    tempPower = {}

    self.initDragSlotX = -1
    self.initDragSlotY = -1
    self.point = {}
    self.point[0] = self.tnode0
    self.point[1] = self.tnode1
    self.point[2] = self.tnode2
    self.point[3] = self.tnode3
    self.point[4] = self.tnode4
    --self.point[5] = self.tnode5
    self.oldDragSlot = -1
    self.enableScroll = true --用于屏蔽底部列表拖拽时滑动
    self.heroListSkin.enabled = self.enableScroll


    --英雄赛选
    self.quality = nil
    self.toggleGroup = {
        self.typeTog_1,
        self.typeTog_2,
        self.typeTog_3,
        self.typeTog_4,
        self.typeTog_5,
        self.typeTog_6,
        self.typeTog_7,
    }
    --阵营筛选
    self.campIndex = nil
    self.campToggleGroup = {
        self.campTog_1,
        self.campTog_2,
        self.campTog_3,
        self.campTog_4,
        self.campTog_5,
        self.campTog_6,
    }
end

function SelectHeroWindow:InitHeroWin()
    self.YieldFrame = function()
        if not self or not self:IsValid() then
            return
        end
        
        --英雄列表
        self.scroll_table.onItemRender=onItemRenderBottom
        self.scroll_table.onItemDispose=function(scroll_rect_item,index)
            if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data[3] then
                scroll_rect_item.data[3]:Dispose()
                scroll_rect_item.data[3] = nil
            end
        end

        -- 敌方战力动态显示
        local laymain_data = require "laymain_data"
        local ceRate = laymain_data.GetStageCERate()
        self:UpdateEnemyPower(ceRate)
        self:UpdateUi()
       
    end
    util.YieldFrame(self.YieldFrame)
   
    event.Trigger(event.UI_SELECT_HERO_SHOW)
    GetAssignHeroCfg()
    local canvas = GameObject.Find("UIRoot/CanvasWithMesh")
    self.curOrder = sort_order.ApplyBaseIndexs(self,canvas.transform, 1)
    --self.haloEffect:SetOrder(self.curOrder+1)
    event.Trigger(event.ON_GUIDE_BUTTON_TIPS, true)
    self:RefreshContentList()

    --如果处于新手引导状态 屏蔽拖拽
    -- --print("force_guide_system.GetState()>>>>>>>>>>",force_guide_system.GetState())
    self.camera =  canvas:GetComponent(typeof(Canvas)).worldCamera

    if force_guide_system.GetState() == 2 then
        TouchDisable(true)
    end
    
    --if dontUpDateByServer then
        self:SetWeaponIcon()
    --else
    --    local usedWeapon = GetCurData().usedWeapon
    --    if usedWeapon then
    --        net_weapon_module.TMSG_WEAPON_GET_CHOOSE_INFO_REQ(usedWeapon.stage,usedWeapon.arenaType1)
    --    end
    --end

    self:SubscribeEvents()
    self:InitGrindState()   

    --先注册toggle事件，再进行toggle触发
    if factionWantedType then
        for i=1, 7 do
            if i ~= factionWantedType +1 then
                self["typeTogGray_"..i]:SetActive(true)
            end
        end
        if self["typeTog_"..factionWantedType+1] then
            self["typeTog_"..factionWantedType+1].isOn = true
        end
    end


    self.TipsTra.gameObject:SetActive(isShowTip)
    self:InitStarConditions()
    self:InitHeroSelectName()
end


function SelectHeroWindow:SetPos()
    local width, height = util.GetUIScreenSize()
    local ori = 720.0/1280.0
    local now = screen_util.width /screen_util.height
    return now/ori
end

function SelectHeroWindow:InitHeroSelectName()
    currentLevelData = nil
    story_mgr.GetCacheLevel()
    if not ui_select_model_node.GetHookLevelGrade() or ui_select_model_node.GetBattleType() ~= 1 then
        self.levelName.transform.parent.gameObject:SetActive(false)
        return
    end
    --self.levelName.transform.parent.gameObject:SetActive(true)
    local data = game_scheme:HookLevel_0(ui_select_model_node.GetHookLevelGrade())
    if data then
        local levelName = data.checkNumber
        self.levelName.text = levelName
        local mapId = data.mapID
        if mapId >= CONST_BATTLE_ARRAY_SWITCH_KEY and isShowAdjustBtn and isMainStoryChallenge then
            local npcTeam_cfg = data.ranksID
            local npcTeamData = npcTeam_cfg[1]
            if npcTeamData then
				currentLevelData = data
				local teamCount = table.count(npcTeamData)
				SetTeamCount(teamCount)
                needCloseWeekGuide = nil
				self:InitSwitchBattleArray()
				util.DelayCallOnce(0.2,function()
                    if window ~= nil and window:IsValid() then
                        --local isShow = self.isShow
                        --log.Log("call window guid state:"..tostring(isShow))
                        local unforced_guide_mgr = require "unforced_guide_mgr"
                        unforced_guide_mgr.Unlock(73)
                        local guide = unforced_guide_mgr.GetCurGuide()
                        if guide == 75 then

                            --弱引导 设计需要注意, 用户异常操作 情况， 显然目前引导系统存在缺陷,
                            --Q1  加载了引导   加载过程中取消
                            --Q2  用了引导,  使用引导的对象  消失 或者异常。
                            event.Trigger(event.ENTER_HERO_SWITCH_BATTLE_ARRAY)
                            --需要 check  引导还在加载，但是用户又取消了引导
                            util.DelayCallOnce(0.3,function()
                                if window == nil or window.isShow==false  or self.isShow == false then
                                    needCloseWeekGuide = nil
                                    local unforced_guide_mgr = require "unforced_guide_mgr"
                                    local guide = unforced_guide_mgr.GetCurGuide()
                                    if guide == 75 then
                                        unforced_guide_mgr.CloseGuide()
                                    end
                                end
                            end)
                            needCloseWeekGuide = true
                        end
                    end
				end)
            end
        end
    end
end

function SelectHeroWindow:InitSwitchBattleArray()
    SetResetBtnVisible(true)
    -- SetAdjustBtnVisible(true)
    if not self.resetBtn.gameObject.activeSelf then
        self.resetBtn.gameObject:SetActive(isShowResetBtn)
        self.adjustBtn:SetActive(isShowAdjustBtn == true and GetLineCount() > 1)
    end
end

function SelectHeroWindow:GetSelectedHero()
    if not TabData[HeroLineUpEnum.LineUp1] then
        return nil
    end
    return TabData[HeroLineUpEnum.LineUp1].selectedHero
end

function SelectHeroWindow:GetSelectedHero2()
    if not TabData[HeroLineUpEnum.LineUp2] then
        return nil
    end
    return TabData[HeroLineUpEnum.LineUp2].selectedHero
end

function SelectHeroWindow:GetSelectedHero3()
    if not TabData[HeroLineUpEnum.LineUp3] then
        return nil
    end
    return TabData[HeroLineUpEnum.LineUp3].selectedHero
end

function SelectHeroWindow:GetSelectedHero4()
    if not TabData[HeroLineUpEnum.LineUp4] then
        return nil
    end
    return TabData[HeroLineUpEnum.LineUp4].selectedHero
end

function SelectHeroWindow:GetSelectedHero5()
    if not TabData[HeroLineUpEnum.LineUp5] then
        return nil
    end
    return TabData[HeroLineUpEnum.LineUp5].selectedHero
end

--刷新控件
function SelectHeroWindow:RefreshContentList()
    self.slot = {}
    self.slot[0] = self.pos0
    self.slot[1] = self.pos1
    self.slot[2] = self.pos2
    self.slot[3] = self.pos3
    self.slot[4] = self.pos4
    --self.slot[5] = self.pos5
end

--获取时空擂台队伍数量
function GetTeamCount()
    if teamNum and teamNum>0 then
        return teamNum 
    end
    local weekend_arena_mgr = require "weekend_arena_mgr"
    local common_new_pb = require "common_new_pb"
    if battleType == common_new_pb.WeekendArena then--时空擂台
        --local myTeamCount = weekend_arena_mgr.GetCanSetTeamCount()--队伍数量
        --return myTeamCount
        --巅峰赛改成1个队伍
        return 1
    end
    return IsEnemyCfgLine()
end

--是否有单队
function IsOneLine()
    return GetTeamCount() > 0
end

--是否是多队
function IsMulti()
    return GetTeamCount() > 1
end

--是否是三队
function IsThreeLine()
    return GetTeamCount() > 2
end

function IsEnemyCfgLine()
    local line = 0
    for i, v in ipairs(TabData) do
        if v.enemyHeroData then
            line = line + 1
        end
    end
    return line
end

--得到第一个切页数据
function GetLine1()
    return TabData[HeroLineUpEnum.LineUp1]
end

--得到第二切页数据
function GetLine2()
    return TabData[HeroLineUpEnum.LineUp2]
end

--得到第三切页数据
function GetLine3()
    return TabData[HeroLineUpEnum.LineUp3]
end

function SetCurEnemyIndex(index)
    curEnemyIndex = index or HeroLineUpEnum.LineUp1
end

--刷新阵容数据
function SelectHeroWindow:RefreshToggleView()
    if not self:IsValid() then
        return
    end
    --刷新切页按钮
    --self:SwitchPage(curEnemyIndex)
    oversea_res.ChangeLangResNew("allhero", "ui_select_hero", self.typeTog_1Img, true)
    --self.btnArtifact.gameObject:SetActive(isShowTeamArtifact)
    self.MultiToggle.gameObject:SetActive(IsMulti())
    self.nextButton.gameObject:SetActive(curEnemyIndex<GetLineCount() and not isBattleDefenseLineup)
    self.fightBtn.gameObject:SetActive(not (curEnemyIndex<GetLineCount() and not isBattleDefenseLineup))
    Common_Util.SetActive(self.haloEnemyTra,isArenaDefence ~= true)
    Common_Util.SetActive(self.arenaPowerObj,isArenaDefence == true)
    self.nextButtonWarn.gameObject:SetActive(not IsHasSelectHero())
    self.ThreeBtn.gameObject:SetActive(IsThreeLine())
    self.OneBtnTxt.text = forMaze and isMulti and GetLine1() == nil and lang.Get(73356) or ""
    self.TwoBtnTxt.text = forMaze and isMulti and GetLine2() == nil and lang.Get(73356) or ""
    self.ThreeBtnTxt.text = forMaze and isMulti and GetLine3() == nil and lang.Get(73356) or ""
    self.OneBtnSel.color = {r=self.OneBtnSel.color.r,g=self.OneBtnSel.color.g,b=self.OneBtnSel.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp1 and 1 or 0}
    self.TwoBtnSel.color = {r=self.TwoBtnSel.color.r,g=self.TwoBtnSel.color.g,b=self.TwoBtnSel.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp2 and 1 or 0}
    self.ThreeBtnSel.color = {r=self.ThreeBtnSel.color.r,g=self.ThreeBtnSel.color.g,b=self.ThreeBtnSel.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp3 and 1 or 0}
    self.OneBtnSelTxt.color = {r=self.OneBtnSelTxt.color.r,g=self.OneBtnSelTxt.color.g,b=self.OneBtnSelTxt.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp1 and 1 or 0}
    self.TwoBtnSelTxt.color = {r=self.TwoBtnSelTxt.color.r,g=self.TwoBtnSelTxt.color.g,b=self.TwoBtnSelTxt.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp2 and 1 or 0}
    self.ThreeBtnSelTxt.color = {r=self.ThreeBtnSelTxt.color.r,g=self.ThreeBtnSelTxt.color.g,b=self.ThreeBtnSelTxt.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp3 and 1 or 0}
    self.Btn4.gameObject:SetActive(GetTeamCount()>=4)
    self.Btn5.gameObject:SetActive(GetTeamCount()>=5)
    self.BtnSel4.color = {r=self.BtnSel4.color.r,g=self.BtnSel4.color.g,b=self.BtnSel4.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp4 and 1 or 0}
    self.BtnSel5.color = {r=self.BtnSel5.color.r,g=self.BtnSel5.color.g,b=self.BtnSel5.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp5 and 1 or 0}
    self.BtnSelTxt4.color = {r=self.BtnSelTxt4.color.r,g=self.BtnSelTxt4.color.g,b=self.BtnSelTxt4.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp4 and 1 or 0}
    self.BtnSelTxt5.color = {r=self.BtnSelTxt5.color.r,g=self.BtnSelTxt5.color.g,b=self.BtnSelTxt5.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp5 and 1 or 0}
    
    if curEnemyIndex == HeroLineUpEnum.LineUp1 then
        self.MultiToggleText.text = lang.Get(601516)
    elseif curEnemyIndex == HeroLineUpEnum.LineUp2 then
        self.MultiToggleText.text = lang.Get(601517)
    elseif curEnemyIndex == HeroLineUpEnum.LineUp3 then
        self.MultiToggleText.text = lang.Get(601518)
    elseif curEnemyIndex == HeroLineUpEnum.LineUp4 then
        self.MultiToggleText.text = lang.Get(601519)
    end
end

function SelectHeroWindow:SwitchPage(state)
    if state == curEnemyIndex then
        return 
    end
	local common_new_pb = require "common_new_pb"
    if battleType ~= common_new_pb.WeekendArena and battleType ~= common_new_pb.Legend and battleType ~= common_new_pb.Advance and battleType ~= common_new_pb.WMTopRace
        and battleType ~= common_new_pb.AllianceTrain  then

        local tabDataItem = TabData[state]
        if  not tabDataItem  or tabDataItem.enemyHeroData == nil then
            --TODO：针对星际迷航多队临时增加日志测试
            -- if forMaze and isMulti then
            --     log.Error("state",state,"curEnemyIndex",curEnemyIndex,"TabData[state]",TabData[state],"TabData[state].enemyHeroData",TabData[state] and TabData[state].enemyHeroData)
            -- end
            flow_text.Add(lang.Get(73372))
            -- log.Error("lang.Get(73372)",lang.Get(73372))
            return
        end
    end

    self:RefreshEnemyState(state)
    self:UpdateUi()
    --切换下一队 (火车系统防守阵容)
    self.nextButton.gameObject:SetActive(curEnemyIndex<GetLineCount() and not isBattleDefenseLineup)
    self.fightBtn.gameObject:SetActive(not (curEnemyIndex<GetLineCount() and not isBattleDefenseLineup))
    self.nextButtonWarn.gameObject:SetActive(not IsHasSelectHero())
    --武器切换
    self:SetWeaponIcon()
    local idle = require "idle"
    idle.ClearNormalPool(0)
end

function SelectHeroWindow:RefreshEnemyState(state)
    curEnemyIndex = state
    self:HideEffect()
    self.OneBtnSel.color = {r=self.OneBtnSel.color.r,g=self.OneBtnSel.color.g,b=self.OneBtnSel.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp1 and 1 or 0}
    self.TwoBtnSel.color = {r=self.TwoBtnSel.color.r,g=self.TwoBtnSel.color.g,b=self.TwoBtnSel.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp2 and 1 or 0}
    self.ThreeBtnSel.color = {r=self.ThreeBtnSel.color.r,g=self.ThreeBtnSel.color.g,b=self.ThreeBtnSel.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp3 and 1 or 0}
    self.OneBtnSelTxt.color = {r=self.OneBtnSelTxt.color.r,g=self.OneBtnSelTxt.color.g,b=self.OneBtnSelTxt.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp1 and 1 or 0}
    self.TwoBtnSelTxt.color = {r=self.TwoBtnSelTxt.color.r,g=self.TwoBtnSelTxt.color.g,b=self.TwoBtnSelTxt.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp2 and 1 or 0}
    self.ThreeBtnSelTxt.color = {r=self.ThreeBtnSelTxt.color.r,g=self.ThreeBtnSelTxt.color.g,b=self.ThreeBtnSelTxt.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp3 and 1 or 0}
    self.BtnSel4.color = {r=self.BtnSel4.color.r,g=self.BtnSel4.color.g,b=self.BtnSel4.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp4 and 1 or 0}
    self.BtnSel5.color = {r=self.BtnSel5.color.r,g=self.BtnSel5.color.g,b=self.BtnSel5.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp5 and 1 or 0}
    self.BtnSelTxt4.color = {r=self.BtnSelTxt4.color.r,g=self.BtnSelTxt4.color.g,b=self.BtnSelTxt4.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp4 and 1 or 0}
    self.BtnSelTxt5.color = {r=self.BtnSelTxt5.color.r,g=self.BtnSelTxt5.color.g,b=self.BtnSelTxt5.color.b,a=curEnemyIndex == HeroLineUpEnum.LineUp5 and 1 or 0}
    if curEnemyIndex == HeroLineUpEnum.LineUp1 then
        self.MultiToggleText.text = lang.Get(601516)
    elseif curEnemyIndex == HeroLineUpEnum.LineUp2 then
        self.MultiToggleText.text = lang.Get(601517)
    elseif curEnemyIndex == HeroLineUpEnum.LineUp3 then
        self.MultiToggleText.text = lang.Get(601518)
    elseif curEnemyIndex == HeroLineUpEnum.LineUp4 then
        self.MultiToggleText.text = lang.Get(601519)
    end
end




--隐藏特效
function SelectHeroWindow:HideEffect()
    if self.powerUpList then
        for i,v in ipairs(self.powerUpList) do
            v:SetActive(false)
        end
    end
    if self.powerDownList then
        for i,v in ipairs(self.powerDownList) do
            v:SetActive(false)
        end
    end
end

function SelectHeroWindow:SubscribeEvents()
    self.onClose = function()
        local gw_zombie_treasure_data = require "gw_zombie_treasure_data"
        local res = gw_zombie_treasure_data.GetStarsShow()
        local common_new_pb = require "common_new_pb"
        event.Trigger(event.BATTLEFINAL,common_new_pb.GameGoal,res)
        local battle_manager = require "battle_manager"
        battle_manager.SetBattleType(nil) --关闭界面之前要先把战斗类型设置为空啊！（优美的中国话）
        if self.onCloseEvent ~= nil then
            SetAtkLineupSave()
            self.onCloseEvent(self)
            enemyFaceID = 0       --敌人头像
            enemyFameID = 0       --敌人头像框
            isShowPower = false       --是否显示战力
            levelNum = ""             --当前关卡
            curEnemyPower = 0
            enemyFaceIDNotByCfg = 0
        end
        event.Trigger(event.NEW_BATTLE_BACK)
    end
    self.closeBtn.onClick:AddListener(self.onClose)

    self.onClickResetrain = function()
        -- windowMgr:ShowModule("ui_restraint")
        local wind = windowMgr:ShowModule("ui_restraint", function()
            TouchDisable(true)
        end, function()
            if not closeLineUp then --只有在允许上阵时才能恢复
                TouchDisable(false)
            end
        end)
        wind:SetTipsStr(tipsStr)
    end
    self.resetraintBtn.onClick:AddListener(self.onClickResetrain)

    self.OnClickGrindTips = function()
        local ui_common_tips = require "ui_common_tips"
        local param = {pos = {x = 70,y = -500},langID = 2153,textWidth = 460,textColor = "ffffff",fontSize = 22}
        ui_common_tips.SetInputParam(param)
        windowMgr:ShowModule("ui_common_tips")
    end
    self.grindTipsBtn.onClick:AddListener(self.OnClickGrindTips)

    self.onFight = function()
        if self.onFightEvent ~= nil and self.hasInit then
            local unforced_guide_mgr = require "unforced_guide_mgr"
            if (unforced_guide_mgr.GetCurGuide() == 19 or unforced_guide_mgr.GetCurGuide() == 22) and unforced_guide_mgr.GetCurStep() then
                local unforced_guide_event_define = require "unforced_guide_event_define"
                event.Trigger(unforced_guide_event_define.click_arena_hero_fight)
            end
            local power = calpro_mgr.CalculateHeroListPowerWithHalo(GetSelectedHero(),nil,getHeroFunc)
            tempPower = {mine = power,enemy = enemyPower}
            --TODO：如果是多队阵容且有一队没有上阵英雄提示 联盟火车防守阵容不需要这个判断
            if(battleType ~= common_new_pb.AllianceTrain)then
                if IsThreeLine() then
                    if util.get_len(self:GetSelectedHero()) == 0 and (self:GetSelectedHero2()~=nil and util.get_len(self:GetSelectedHero2()) == 0) and (self:GetSelectedHero3()~=nil and util.get_len(self:GetSelectedHero3()) == 0) then
                        flow_text.Add(lang.Get(73364))
                        return
                    elseif util.get_len(self:GetSelectedHero()) == 0 then
                        flow_text.Add(lang.Get(73361))
                        self:SwitchPage(HeroLineUpEnum.LineUp1)
                        return
                    elseif (self:GetSelectedHero2()~=nil and util.get_len(self:GetSelectedHero2()) == 0) then
                        flow_text.Add(lang.Get(73362))
                        self:SwitchPage(HeroLineUpEnum.LineUp2)
                        return
                    elseif (self:GetSelectedHero3()~=nil and util.get_len(self:GetSelectedHero3()) == 0) then
                        flow_text.Add(lang.Get(73363))
                        self:SwitchPage(HeroLineUpEnum.LineUp3)
                        return
                    elseif (self:GetSelectedHero4()~=nil and util.get_len(self:GetSelectedHero4()) == 0) then
                        flow_text.Add(lang.Get(73364))
                        self:SwitchPage(HeroLineUpEnum.LineUp4)
                        return
                    elseif (self:GetSelectedHero5()~=nil and util.get_len(self:GetSelectedHero5()) == 0) then
                        flow_text.Add(lang.Get(73364))
                        self:SwitchPage(HeroLineUpEnum.LineUp5)
                        return
                    end
                elseif IsMulti() then
                    if util.get_len(self:GetSelectedHero()) == 0 and (self:GetSelectedHero2()~=nil and util.get_len(self:GetSelectedHero2()) == 0) then
                        flow_text.Add(lang.Get(73364))
                        return
                    elseif util.get_len(self:GetSelectedHero()) == 0 and IsOneLine() then
                        --星级迷航多队死亡队伍不会设置enemyHeroData，位面战纪不管是否死亡都会设置enemyHeroData
                        local line1 = GetLine1()
                        if line1 and line1.enemyHeroData ~= nil then
                            flow_text.Add(lang.Get(73361))
                            self:SwitchPage(HeroLineUpEnum.LineUp1)
                            return
                        end
                    elseif (self:GetSelectedHero2()~=nil and util.get_len(self:GetSelectedHero2()) == 0) and GetLine2() then
                        flow_text.Add(lang.Get(73362))
                        self:SwitchPage(HeroLineUpEnum.LineUp2)
                        return
                    end
                else
                    if util.get_len(GetSelectedHero()) == 0 then
                        local lang = require "lang"
                        flow_text.Add(lang.Get(7110))
                        return
                    end
                    --TODO:挑战秘境需要阵营英雄限制
                    if forMultiSecretPlace then
                        -- --print("window.curfactionNum",window.curfactionNum,"window.factionNum",window.factionNum,"window.curReplaceNum",window.curReplaceNum)
                        if window.curfactionNum and window.curReplaceNum and window.factionNum and window.curfactionNum+window.curReplaceNum<window.factionNum then
                            --local content = window.tipID and string.format(lang.Get(window.tipID),window.factionNum,window.curfactionNum,window.factionNum) or  ""
                            local content = string.format(lang.Get(93083),window.factionNum,lang.Get(hero_mgr.Hero_Type_Name[window.factionID]))
                            flow_text.Add(content)
                            return
                        end
                    end
                end
            end
            local tempHeroData = self:GetFilteredHero(self.quality,self.campIndex)
            if util.get_len(tempHeroData) ~= 0 then
                self.onFightEvent(self,false)
                battle_data.SetTopPowerData(isShowPower, levelNum, enemyFaceID, enemyFameID,enemyFaceIDNotByCfg)
                battle_data.SetSoldierNum(mySoldierNum, enemySoldierNum)
                battle_data.SetEnemyPower(curEnemyPower)
                battle_data.SetMyPower(self.myOldPower)
                enemyFaceID = 0       --敌人头像
                enemyFameID = 0       --敌人头像框
                isShowPower = false       --是否显示战力
                levelNum = ""             --当前关卡
                curEnemyPower = 0
                enemyFaceIDNotByCfg = 0
                mySoldierNum = 0
                enemySoldierNum = 0
            end
            --新手强制引导事件
            force_guide_system.TriComEvent(force_guide_event.cEventClickFight)
            force_guide_system.TriComEvent(force_guide_event.cEventMonsterAttack)

--[[            if unforced_guide_mgr.GetCurStep() then
                local cfg = unforced_guide_mgr.GetCurStepConfig()
                local curGuide = unforced_guide_mgr.GetCurGuide()
                if cfg then
                    local unforcedGuideBattleType = unforced_guide_mgr.GetBattleType()
                    local stepID = cfg.stepId
                    if unforcedGuideBattleType == unforced_guide_mgr.BATTLE_TYPE_NUM.ILLUSION_TOWER and (curGuide == 2 or curGuide == 31) and (stepID == 5 or stepID == 4) then
                        event.Trigger(event.CLICK_SELECT_HERO_BATTLE)

                        if windowMgr:IsModuleShown("ui_pointing_target") then
                            windowMgr:UnloadModuleImmediate("ui_pointing_target")
                        end
                    elseif unforcedGuideBattleType == unforced_guide_mgr.BATTLE_TYPE_NUM.AREAN_SINGLE and curGuide == 4 and stepID == 73 then
                        event.Trigger(event.SELECT_HERO_COMPLETE)
                    elseif unforcedGuideBattleType == unforced_guide_mgr.BATTLE_TYPE_NUM.LOST_LAND and curGuide == 52 then
                        if stepID == 411 then
                            --event.Trigger(event.ENTER_LOST_LAND_BASE)
                        elseif stepID == 414 then
                            event.Trigger(event.CLICK_SELECT_HERO_BATTLE)
                        end
                    end
                end
            end]]
        end
    end

    self.onSpaceGapFight = function()
        if self.onFightEvent ~= nil then
            local power = calpro_mgr.CalculateHeroListPowerWithHalo(GetSelectedHero(),nil,getHeroFunc)
            tempPower = {mine = power,enemy = enemyPower}
            if util.get_len(GetSelectedHero()) == 0 then
                local lang = require "lang"
                flow_text.Add(lang.Get(7110))
                return
            end

            local tempHeroData = self:GetFilteredHero(self.quality,self.campIndex)
            local selectHero = GetSelectedHero()
            if util.get_len(selectHero) > 0 then
                self.onFightEvent(self,false)
            end
            --新手强制引导事件
            force_guide_system.TriComEvent(force_guide_event.cEventClickFight)
            local unforced_guide_mgr = require "unforced_guide_mgr"
            if unforced_guide_mgr.GetCurStep() then
                local cfg = unforced_guide_mgr.GetCurStepConfig()
                if unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.ILLUSION_TOWER and (unforced_guide_mgr.GetCurGuide() == 2 or unforced_guide_mgr.GetCurGuide() == 31) and cfg and (cfg.stepId == 5 or cfg.stepId == 4) then
                    event.Trigger(event.CLICK_SELECT_HERO_BATTLE)

                    if windowMgr:IsModuleShown("ui_pointing_target") then
                        windowMgr:UnloadModuleImmediate("ui_pointing_target")
                    end
                elseif unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.AREAN_SINGLE and unforced_guide_mgr.GetCurGuide() == 4 and cfg and cfg.stepId == 73 then
                    event.Trigger(event.SELECT_HERO_COMPLETE)
                end
            end
        end
    end

    self.OnGrind = function()
        if self.onFightEvent ~= nil then
            local power = calpro_mgr.CalculateHeroListPowerWithHalo(GetSelectedHero(),nil,getHeroFunc)
            tempPower = {mine = power,enemy = enemyPower}
            if util.get_len(GetSelectedHero()) == 0 then
                local lang = require "lang"
                flow_text.Add(lang.Get(7110))
                return
            end
            if power/ GetCurData().monsterPower < grindThreshold then
                local lang = require "lang"
                flow_text.Add(lang.Get(103211))
                return
            end

            local tempHeroData = self:GetFilteredHero(self.quality,self.campIndex)
            if util.get_len(tempHeroData) ~= 0 then
                self.onFightEvent(self,true)
            end
        end
    end


    self.OnSelectWeaponCallBack = function(weaponId)
        local currentData = GetCurData()
        local weaponData = currentData.usedWeapon

        if self:IsValid() and weaponData then
            weaponData["weaponId"] = weaponId
            local weapon_data = require("weapon_data")
            weapon_data.SetlocalizedWeaponData(weaponData.stage,weaponId,curEnemyIndex-1)
            self:SetWeaponIcon()
            UpdatePower()
        end
    end

    self.OnWeaponButton = function()
        windowMgr:ShowModule("ui_new_magic_weapon_info_tips")
        --local ui_weapon_select = require("ui_weapon_select")
        --selectedWeapon = excludeWeaponIds or {}
        --for i,v in pairs(TabData) do
        --    if v.usedWeapon and v.usedWeapon.weaponId then
        --        selectedWeapon[i-1] = v.usedWeapon["weaponId"]
        --    else
        --        v.usedWeapon = {}
        --    end
        --end
        --ui_weapon_select.AddSelectedWeapon(selectedWeapon)
        --
        --local currentData = GetCurData()
        --local weaponData = currentData.usedWeapon
        --ui_weapon_select.BattleDataInit(weaponData,
        --function(weaponId)
        --    if  self.OnSelectWeaponCallBack then
        --        self.OnSelectWeaponCallBack(weaponId)
        --    end
        --end,dontUpDateByServer)
        --windowMgr:ShowModule("ui_weapon_select", function()
        --    TouchDisable(true)
        --end, function()
        --    TouchDisable(false)
        --end)
        --
        --local unforced_guide_mgr = require "unforced_guide_mgr"
		--if unforced_guide_mgr.GetCurGuide() == 34 and unforced_guide_mgr.GetCurStep() then
		--	local cfg = unforced_guide_mgr.GetCurStepConfig()
		--	if cfg then
		--		if cfg.stepId == 222 then
		--			event.Trigger(event.CLICK_WEAPON_BTN)
		--		end
		--	end
		--end
    end

    self.OnTeamRecommendButton = function( )
        local open_test_mgr = require "open_test_mgr"
        if open_test_mgr.GetLineUpCount() <= 0 then
            flow_text.Add(lang.Get(309))
        else
            local ui_my_team_recommend = require "ui_my_team_recommend"
            windowMgr:ShowModule("ui_my_team_recommend",function()
                TouchDisable(true)
            end, function()
                if not closeLineUp then --只有在允许上阵时才能恢复
                    TouchDisable(false)
                end
            end)
        end
    end
    self.OnNextButton = function( )
        self:SwitchPage((curEnemyIndex+1))
        SetAtkLineupSave()
    end
    
    self.fightBtn.onClick:AddIntervalListener(self.onFight, 1)
    self.spaceGapFightButton.onClick:AddIntervalListener(self.onSpaceGapFight, 1)
    self.grindBtn.onClick:AddIntervalListener(self.OnGrind, 1)
    self.weaponButton.onClick:AddListener(self.OnWeaponButton)
    self.nextButton.onClick:AddListener(self.OnNextButton)
    self.teamRecommendButton.onClick:AddListener(self.OnTeamRecommendButton)

    self.OnUpdateHaloData = function ()
       -- 计算光环位置
        UpdateHalo()
    end
    event.Register(event.HALO_UPGRADE_UPDATE_DATA, self.OnUpdateHaloData)
    local gw_hero_define = require "gw_hero_define"
    self.OnUpdateHero = function()
        self:UpdateUpgradeBtn()
    end
    event.Register(gw_hero_define.UPGRADE_HERO_NEW,self.OnUpdateHero)
    event.Register(gw_hero_define.HERO_AWAKEN_NEW,self.OnUpdateHero)

    for i, toggle in ipairs(self.toggleGroup) do
        self["toggleValueChanged"..i] = function(isOn)
            if not isOn then
                return
            end 
            if factionWantedType and i ~= factionWantedType+1 then
                if self["typeTog_"..i].isOn then
                    -- 只能上阵指定阵营的英雄
                    flow_text.Add(lang.Get(16372))
                end
                if self["typeTog_"..factionWantedType+1] then
                    self["typeTog_"..factionWantedType+1].isOn = true
                end
                return
            end
            if window and window:IsValid() then
                window:SetFilterHero(i-1 ,i ~= 0,self.campIndex)
            end
        end
        toggle.onValueChanged:AddListener(self["toggleValueChanged"..i])
    end
    for i,toggle in ipairs(self.campToggleGroup) do
        self["campToggleValueChanged"..i] = function(isOn)
            if not isOn then
                return
            end 
            if window and window:IsValid() then
                window:SetFilterHero(nil ,nil,i-1)
            end
        end
        toggle.onValueChanged:AddListener(self["campToggleValueChanged"..i])
    end

    self.OnOpenMenu = function()
        self.openPanel.gameObject:SetActive(true)  
        self.campTopRoot.gameObject:SetActive(true)
        self.MaskBtn.gameObject:SetActive(true)
        self.typeTogRoot.enabled = false
        self.menuOpenBtn.gameObject:SetActive(false)
    end
    -- self.widget_table["menuOpenBtn"].event_name = "OnOpenMenu"
    self.menuOpenBtn.onClick:AddListener(self.OnOpenMenu)

    self.OnCloseMenu = function()
        self.openPanel.gameObject:SetActive(false)  
        self.campTopRoot.gameObject:SetActive(false)
        self.MaskBtn.gameObject:SetActive(false)
        self.typeTogRoot.enabled = true
        self.menuOpenBtn.gameObject:SetActive(true)
    end
    --问题：第一次打开界面时点击按钮没有响应
    -- self.widget_table["MaskBtn"].event_name = "OnCloseMenu"
    -- self.widget_table["menuCloseBtn"].event_name = "OnCloseMenu"
    self.MaskBtn.onClick:AddListener(self.OnCloseMenu)
    self.menuCloseBtn.onClick:AddListener(self.OnCloseMenu)

    --接收武器装备信息
    self.onGetChooseWeapon = function (evenname,msg)
        local weapon_data = require("weapon_data")
        local usedWeapon = GetCurData().usedWeapon
        if not usedWeapon then return end
        local beenSet = {}
        for i = 1, #TabData do
            local key = "weaponId"..i
            local wid = 0
            if msg[key] and msg:HasField(key) and not beenSet[msg[key]] then --直接判断HasField一个不能存在的key会抛异常，导致后面代码不执行，ui没更新
                weapon_data.SetlocalizedWeaponData(usedWeapon.stage,msg[key],i-1,usedWeapon.arenaType1)
                wid = msg[key]
                beenSet[wid] = true
            else
                weapon_data.SetlocalizedWeaponData(usedWeapon.stage,0,i-1,usedWeapon.arenaType1)
            end
            if TabData[i] and TabData[i].usedWeapon then
                TabData[i].usedWeapon.weaponId = wid
            end
        end

	-- if not usedWeapon then return end
    --     if msg:HasField("weaponId1") and msg.weaponId1 then
    --         weapon_data.SetlocalizedWeaponData(usedWeapon.stage,msg.weaponId1,0,usedWeapon.arenaType1)
    --     end
    --     if IsMulti() and msg:HasField("weaponId2") then
    --        weapon_data.SetlocalizedWeaponData(usedWeapon.stage,msg.weaponId2,1,usedWeapon.arenaType1)
    --     end
    --     if IsMulti() and msg:HasField("weaponId3") then
    --        weapon_data.SetlocalizedWeaponData(usedWeapon.stage,msg.weaponId3,2,usedWeapon.arenaType1)
    --     end
    --     if IsMulti() and msg:HasField("weaponId4") then
    --         weapon_data.SetlocalizedWeaponData(usedWeapon.stage,msg.weaponId4,3,usedWeapon.arenaType1)
    --      end
    --      if IsMulti() and msg:HasField("weaponId5") then
    --         weapon_data.SetlocalizedWeaponData(usedWeapon.stage,msg.weaponId5,4,usedWeapon.arenaType1)
    --      end

        -- --print("接收武器装备信息stage",usedWeapon.stage,"weaponId",usedWeapon.weaponId,"IsMulti()",IsMulti())

        if usedWeapon then
            -- local msgWeaponID  = usedWeapon.weaponId
            -- usedWeapon["weaponId"] = msgWeaponID
            self:SetWeaponIcon()
            UpdatePower()
        end
        --if usedWeapon2 then
        --    local msgWeaponID  = msg.weaponId2
        --    usedWeapon2["weaponId"] = msgWeaponID
        --end

    end
    event.Register(event.WEAPON_GET_CHOOSE_RSP,self.onGetChooseWeapon)

    self.updateEnemyCERateEvent = function(eventName, ceRate)
        self:UpdateEnemyPower(ceRate)
    end
    event.Register(event.UPDATE_ENEMY_CERATE, self.updateEnemyCERateEvent)

    self.OnBUTTON_TIPS_TRIGGER_CLOSE = function(eventName, id)
        if id == 7 or id == 17 then
            showtips = false
        end
    end
    event.Register(event.BUTTON_TIPS_TRIGGER_CLOSE , self.OnBUTTON_TIPS_TRIGGER_CLOSE)

    -- for i=0,7 do
    --     local btn = self["lockedpos"..i]
    --     if btn then
    --         btn.onClick:AddListener(function ()
    --             flow_text.Add(lang.Get(183009))
    --         end)
    --     end
    -- end

    self.spaceGapTipEvent = function ()
        -- 时空裂缝提示弹窗
        -- TouchDisable(true)
        -- local ui_hero_tip_pop = require "ui_hero_tip_pop"
        -- ui_hero_tip_pop.SetScaleCenterPos(self.spaceGapTipButton.gameObject.transform.position)
        -- if forSpaceGap then
        --     local space_gap_mgr = require "space_gap_mgr"
        --     local stageID = space_gap_mgr.GetBattleID()
        --     local cfg = game_scheme:SpaceGap_0(stageID)
        --     ui_hero_tip_pop.SetEnterType(ui_hero_tip_pop.enterType.SpaceGap)
        --     ui_hero_tip_pop.SetHeroList(spaceGapHeros,lang.Get(cfg.Strategy),lang.Get(cfg.Name),ui_hero_tip_pop.enterType.SpaceGap)
        --     -- ui_hero_tip_pop.SetScaleCenterPos(self.spaceGapTipButton.gameObject.anchoredPosition)
            
        --     local curID = space_gap_mgr.GetBattleID()
        --     local enterLv = space_gap_mgr.GetFirstEnterTipLv()
        --     if curID ~= enterLv then
        --         space_gap_mgr.SetFirstEnterTipLv(curID)
        --     end
        --     self.spaceGapTipButtonReddot.gameObject:SetActive(false)
            
        --     local unforced_guide_mgr = require "unforced_guide_mgr"
        --     if unforced_guide_mgr.GetCurGuide() == 37 and unforced_guide_mgr.GetCurStep() then
        --         local cfg = unforced_guide_mgr.GetCurStepConfig()
        --         if cfg and cfg.stepId then
        --             if cfg.stepId == 233 then
        --                 event.Trigger(event.CLICK_TIPBTN)
        --             end
        --         end
        --     end
        --     local battleID = space_gap_mgr.GetBattleID()
        --     local json = require "dkjson"
        --     local json_str = json.encode({
        --         SpaceGap_id = battleID,
        --     })
        --     event.Trigger(event.GAME_EVENT_REPORT, "SpaceGap_tip", json_str)
        -- elseif forPlot and not useNew then
        --     local hero_exclusive_mgr = require "hero_exclusive_mgr"
        --     local stageID = hero_exclusive_mgr.GetBattleID()
        --     local cfg = game_scheme:ExclusiveFight_0(stageID)
        --     ui_hero_tip_pop.SetEnterType(ui_hero_tip_pop.enterType.Plot)
        --     ui_hero_tip_pop.SetHeroList(plotHeros,lang.Get(cfg.Strategy),lang.Get(cfg.Name),ui_hero_tip_pop.enterType.Plot)
        -- elseif forPlot and useNew then
        --     local hero_exclusive_mgr = require "hero_exclusive_mgr"
        --     local stageID = hero_exclusive_mgr.GetBattleID()
        --     local cfg = game_scheme:NewExclusiveFight_0(stageID)
        --     ui_hero_tip_pop.SetEnterType(ui_hero_tip_pop.enterType.RookiePlot)
        --     ui_hero_tip_pop.SetHeroList(plotHeros,lang.Get(cfg.Strategy),lang.Get(cfg.Name),ui_hero_tip_pop.enterType.RookiePlot)
        -- end
        -- windowMgr:ShowModule("ui_hero_tip_pop",nil,function ()
        --     if not closeLineUp then --只有在允许上阵时才能恢复
        --         TouchDisable(false)
        --     end
        -- end)
    end
    self.spaceGapTipButton.onClick:AddIntervalListener(self.spaceGapTipEvent,0.5)

    self.EnemyViewOneStateEvent = function()
        if not IsOneLine() then
            flow_text.Add(lang.Get(73369))
            return
        end
        self:SwitchPage(HeroLineUpEnum.LineUp1)
    end
    self.OneBtn.onClick:AddListener(self.EnemyViewOneStateEvent)

    self.EnemyViewTwoStateEvent = function()
        if not IsMulti() then
            flow_text.Add(lang.Get(73369))
            return
        end
        self:SwitchPage(HeroLineUpEnum.LineUp2)
    end
    self.TwoBtn.onClick:AddListener(self.EnemyViewTwoStateEvent)

    self.EnemyViewThreeStateEvent = function()
        if not IsThreeLine() then
            flow_text.Add(lang.Get(73369))
            return
        end
        self:SwitchPage(HeroLineUpEnum.LineUp3)
    end
    self.ThreeBtn.onClick:AddListener(self.EnemyViewThreeStateEvent)

    self.Btn4OnClickEvent = function()
        if (GetTeamCount()<4) then
            flow_text.Add(lang.Get(73369))
            return
        end
        self:SwitchPage(HeroLineUpEnum.LineUp4)
    end
    self.Btn4.onClick:AddListener(self.Btn4OnClickEvent)


    self.Btn5OnClickEvent = function()
        if (GetTeamCount()<5) then
            flow_text.Add(lang.Get(73369))
            return
        end
        self:SwitchPage(HeroLineUpEnum.LineUp5)
    end
    self.Btn5.onClick:AddListener(self.Btn5OnClickEvent)
    
	self.onResetBtnClick = function()
        --主线推关  下阵
        -- 清空当前队伍
        if currentLevelData ~= nil then
            local cur_data = GetCurData()
            if cur_data ~= nil  then
                local oldData = cur_data.usedWeapon
                if oldData ~= nil then
                    if oldData.weaponId ~= 0 then  --TODO
                        oldData.weaponId = 0
                        local weapon_data = require("weapon_data")
                        weapon_data.SetlocalizedWeaponData(oldData.stage,oldData.weaponId,oldData.lineupIdx,oldData.arenaType1)
                        self:SetWeaponIcon()
                    end
                end
            end
        end

        local selectedHero = GetSelectedHero()
        if selectedHero == nil or table.count(selectedHero) <= 0 then
            return
        end

        local cacheData = GetCacheData()
        for i = 0,4 do
            selectedHero[i] = nil
            self:PerformDeselect(i)
        end
        selectedHero = {}
        cacheData = {}
        HeroData = self:GetUnselectHerodata()
        local temp = {}
        if GetHelpHero() then
            for i,v in ipairs(helpNpcList) do
                if v.heroData.heroSid == 0 then
                    table.insert(temp,v.heroData)
                end

            end
        end
        for i,v in ipairs(HeroData) do
            table.insert(temp,v)
        end

        self.scroll_table.data= temp
        self.scroll_table:Refresh(-1,-1)
        CheckFinger()
        if window and window.UIRoot then
            window:UpdateUi()
        end
	end
    self.resetBtn.onClick:AddListener(self.onResetBtnClick)

    --一键上阵
    self.OnBtn_quickFighClickedProxy = function()
        local unforced_guide_mgr = require "unforced_guide_mgr"
        if (unforced_guide_mgr.GetCurGuide() == 19 or unforced_guide_mgr.GetCurGuide() == 22) and unforced_guide_mgr.GetCurStep() then
            local unforced_guide_event_define = require "unforced_guide_event_define"
            event.Trigger(unforced_guide_event_define.click_arena_hero_quick_fight)
            event.Trigger(unforced_guide_event_define.start_arena_hero_fight)
        end
        --下阵当前队伍所有英雄, 只有设置了isBattleDefenseLineup的时候才会下阵
        if isBattleDefenseLineup then
            if(setLineupCallBack)then
                for k,v in pairs(defenseSetIndexLineup)do
                    if(v and k ~= curEnemyIndex)then
                        setLineupCallBack(k)
                    end
                end
                --先处理其他队伍的保存最后走当前队伍的保存
                setLineupCallBack(curEnemyIndex)
            end
            return
        end
        local selectedHero = GetSelectedHero()
        if selectedHero ~= nil or table.count(selectedHero) > 0 then
            local cacheData = GetCacheData()
            for i = 0,4 do
                selectedHero[i] = nil
                self:PerformDeselect(i)
            end
            selectedHero = {}
            cacheData = {}
        end
        local unSelectHeroList = self:GetUnselectHeroList()
        --[[local sortByPower = function(t1, t2)
            
            local power1 = gw_power_mgr.GetHeroPowerByCfgId(t1.heroID)
            local power2 = gw_power_mgr.GetHeroPowerByCfgId(t2.heroID)
            if power1 ~= power2 then
                return power1 > power2
            else
                return t1.heroID < t2.heroID
            end
        end
        table.sort(unSelectHeroList, sortByPower)]]
        for i = 1, 5 do
            local row, col = battle_data.GetRowAndColByIndex(i)
            --row，0是前排，1是后排
            local professionIndexList = {}
            if row == 0 then
                --前排
                professionIndexList = {1}  --1肉盾 
            elseif row == 1 then
                --后排
                professionIndexList = {4, 5}    --4输出 5辅助
            end
            for k, v in ipairs(unSelectHeroList) do
                local heroCfg = game_scheme:Hero_0(v.heroID)
                if heroCfg then
                    for _,j in ipairs(professionIndexList) do
                        if heroCfg.profession == j then
                            local canUp = camp_trial_data.GetCanUpBySid(ui_select_model_node.GetBattleType(), v.heroSid)
                            if canUp then
                                self:PerformSelect(v,i - 1) --找到了一个可以加入队伍的英雄
                                selectedHero[i] = v
                                table.remove(unSelectHeroList,k)
                                break 
                            end
                        end
                    end
                end
                if selectedHero[i] then
                    break
                end
            end
        end

        for i = 1, 5 do
            local v = selectedHero[i]
            if not v then
                --有空位
                local hero = unSelectHeroList[1]
                if hero then
                    local canUp = camp_trial_data.GetCanUpBySid(ui_select_model_node.GetBattleType(), hero.heroSid)
                    if canUp then
                        self:PerformSelect(hero,i - 1) --找到了一个可以加入队伍的英雄
                        v = hero
                        selectedHero[i] = v
                        table.remove(unSelectHeroList,1)
                    end
                end
            end
            if v then
                SelectHero(v.hero)
            end
            
        end

        HeroData = self:GetUnselectHerodata()
        self.scroll_table.data= HeroData
        self.scroll_table:Refresh(-1,-1)
        UpdatePower()
        CheckFinger()
        event.Trigger(event.SAVE_SQUAD);

    end
    self.quickFightBtn.onClick:AddListener(self.OnBtn_quickFighClickedProxy)

	self.onEnemyWeaponClick = function()
        --TODO
		local tips = lang.Get(1002)--未装备召唤兽
		local legend_championships_mgr = require "legend_championships_mgr"
		if hideTeamInfo and legend_championships_mgr.IsHideTeamIndex(curEnemyIndex) then
			tips = lang.Get(8064)--该队伍的信息已隐藏
		end
		self.weaponTip.gameObject:SetActive(true)
		local usedWeapon = GetCurData().enemyWeapon
		self.weaponText.text = tips
		if usedWeapon and usedWeapon.weaponid ~= 0 then
			local weaponCfg = game_scheme:MagicWeapon_0(usedWeapon.weaponid,1,usedWeapon.weaponLv,0)
			self.weaponText.text = lang.Get(weaponCfg.weaponNameID)..":"..lang.Get(weaponCfg.skillDecID)
		end
	end
	self.Btn_weapon_enemy.onClick:AddListener(self.onEnemyWeaponClick)

	self.adjustBtnEvent = function()
		if currentLevelData ~= nil then
			if not leftRoleInfo then
				leftRoleInfo = {}
				leftRoleInfo.team = leftRoleInfo.team or {}
			end
		else
            if  battleType==common_new_pb.Maze  then
                if not leftRoleInfo then
                    leftRoleInfo = {}
                    leftRoleInfo.team = leftRoleInfo.team or {}
                end
            end
        end

        local tempRightRoleInfo = nil
		if leftRoleInfo and leftRoleInfo.team then
            for j,tabDataItem in pairs(TabData) do
                local tempSelectedHerd = tabDataItem.selectedHero
                if tempSelectedHerd then
                    local data = {}
                    for i,v in pairs(tempSelectedHerd) do
                        if v then
                            data[i+1] = v
                        end
                    end
                    leftRoleInfo.team[j] = data
                end

                if currentLevelData ~= nil then
                    tempRightRoleInfo = tempRightRoleInfo or {}
                    tempRightRoleInfo.team = tempRightRoleInfo.team or {}
                    local enemyData = tabDataItem.enemyHeroData
                    if enemyData then
                        local tempData = {}
                        for i,v in pairs(enemyData) do
                            if v then
                                tempData[i+1] =  v
                            end
                        end
                        tempRightRoleInfo.team[j] = tempData
                    end
		        else
                    if  battleType==common_new_pb.Maze  then
                        tempRightRoleInfo = tempRightRoleInfo or {}
                        tempRightRoleInfo.team = tempRightRoleInfo.team or {}
                        local enemyData = tabDataItem.enemyHeroData
                        -- 星际迷航这里某队打完了可能为空
                        if tabDataItem.isFinish then
                            leftRoleInfo.teamState = leftRoleInfo.teamState or {}
                            leftRoleInfo.teamState[j] = true -- 标记战斗已结束
                            tempRightRoleInfo.team[j]=tabDataItem.finishHeroData or {}
                        else
                            if enemyData then
                                local tempData = {}
                                for i,v in pairs(enemyData) do
                                    if v then
                                        tempData[i+1] =  v
                                    end
                                end
                                tempRightRoleInfo.team[j]=tempData
                            end
                        end
                    end
                end
		    end
        end

        local lineCount = GetLineCount()
        local temp_battleType
        local showCount
        if currentLevelData ~= nil then
            TouchDisable(true)
            temp_battleType = 1
            if battleType == common_new_pb.WMTopRace then
                -- 巅峰赛
                temp_battleType = 0
            end
            rightRoleInfo  = tempRightRoleInfo
            local oncloseFunc = function()
                if not closeLineUp then --只有在允许上阵时才能恢复
                    TouchDisable(false)
                end
            end
            local win = windowMgr:ShowModule("ui_legend_championships_formation_setting",oncloseFunc)
            win:SetWindowStyle(curType == ATTACK and 2 or 1)
            win:SetInputParam(lineCount, leftRoleInfo, rightRoleInfo, curType == ATTACK,temp_battleType,{showTeamCount=showCount})
            --引导结束
            local unforced_guide_mgr = require "unforced_guide_mgr"
            local guide = unforced_guide_mgr.GetCurGuide()
            if guide == 75 then
                local cfg = unforced_guide_mgr.GetCurStepConfig()
                if cfg and cfg.stepId then
                    if cfg.stepId == 630 then
                        event.Trigger(event.CLICK_HERO_ADJUST_BTN)
                    end
                end
            end
        else
            local showAllRightHero = nil
            if battleType == common_new_pb.WMTopRace then
                -- 巅峰赛
                temp_battleType = 0
            elseif battleType==common_new_pb.Maze then
                temp_battleType = 2
                rightRoleInfo  = tempRightRoleInfo
                showCount=tempRightRoleInfo and tempRightRoleInfo.team and #(tempRightRoleInfo.team)
            elseif battleType == common_new_pb.Legend then
                showAllRightHero = true
            end

            local win = windowMgr:ShowModule("ui_legend_championships_formation_setting", function()
                TouchDisable(true)
            end, function()
                if not closeLineUp then --只有在允许上阵时才能恢复
                    TouchDisable(false)
                end
            end)
            win:SetWindowStyle(curType == ATTACK and 2 or 1)
            win:SetInputParam(lineCount, leftRoleInfo, rightRoleInfo, curType == ATTACK,temp_battleType,{showTeamCount=showCount,showAllRightHero = showAllRightHero})
        end
	end
	self.adjustBtn.onClick:AddListener(self.adjustBtnEvent)
  --self.OnBtn_btnArtifactClickedProxy = function()
  --      local win = windowMgr:ShowModule("ui_battle_team_artifact")
  --      if win then
  --          local leftTeamData = {}
  --          local curIndex=1 --打开神器界面时的初始页数
  --          local curCount=1 --队伍数
  --          for teamIndex,tabData in pairs(TabData) do
  --              -- 星际迷航某一队伍可能已经结束了，linecount会等于1
  --              if not tabData.isFinish then
  --                  if tabData.selectedHero then
  --                      if teamIndex==curEnemyIndex then
  --                          curIndex=curCount                            
  --                      end
  --                      leftTeamData[curCount] = leftTeamData[curCount] or {}
  --                      for heroIndex,v in pairs(tabData.selectedHero) do
  --                          if v ~= nil then                       
  --                              leftTeamData[curCount][heroIndex] = v.heroSid
  --                          end
  --                      end
  --                      curCount=curCount+1
  --                  end
  --              end
  --          end
  --          win:SetInputParam(leftTeamData, curIndex, GetLineCount(), teamArtifactType)
  --      end
  --  end
  --  self.btnArtifact.onClick:AddListener(self.OnBtn_btnArtifactClickedProxy)
    --交换队伍成功事件
	self.OnUPDATE_LEGEND_CHAMPIONSHIPS_TEAM_SORT = function(eventName, sort)		--todo
        local weapon_data = require("weapon_data")
        -- local reqWeaponUpdate=function(_stage,_weaponId,_bUse,_lineupIdx,_arenaType1)
        --     local msg = {
        --         stage = _stage,
        --         weaponId = _weaponId,
        --         bUse = _bUse,
        --         lineupIdx = _lineupIdx,
        --         arenaType1 = _arenaType1,
        --     }
        --     net_weapon_module.TMSG_WEAPON_CHOOSE_REQ(msg)
        -- end
        if currentLevelData == nil then
            local common_new_pb = require "common_new_pb"
            if not battleType then return end
            if battleType==common_new_pb.Legend then
                local newData = {}
                for k,v in pairs(sort) do
                    local t =  GetTabDataByLine(v)
                    newData[k] = t
                end

                local test = {}
                for index, h in pairs(newData) do
                    if TabData[index] then
                        if not test[index] then
                            test[index] = {}
                        end
                        test[index].selectedHero = h.selectedHero
                        test[index].saveSelectedHero = h.saveSelectedHero
                        test[index].usedWeapon = h.usedWeapon
                    end
                end

                for index, h in pairs(test) do
                    if test[index] then
                        if not TabData[index] then
                            TabData[index] = {}
                        end
                        TabData[index].selectedHero = h.selectedHero
                        TabData[index].saveSelectedHero = h.saveSelectedHero
                        TabData[index].usedWeapon = h.usedWeapon
                    end
                end

                if leftRoleInfo and leftRoleInfo.team then
                    local newTeam = {}
                    for k,v in pairs(sort) do
                        newTeam[k] = leftRoleInfo.team[v]
                    end
                    leftRoleInfo.team = newTeam
                end
                if window and window.UIRoot then
                    window:UpdateUi()
                end
            elseif battleType==common_new_pb.Maze then
                local newTabData = {}
                for key, tabDataItem in pairs(TabData) do
                    newTabData[key] =
                    {
                        enemyHeroData = tabDataItem.enemyHeroData,
                        enemyShowHp = tabDataItem.enemyShowHp,
                        monsterPower = tabDataItem.monsterPower,
                        originEnemyPower =  tabDataItem.originEnemyPower,
                    }
                end


                for key, newIdex in ipairs(sort) do
                    local tabDataItem = TabData[newIdex]
                    local newTabDataItem = newTabData[key]
                    newTabDataItem.saveSelectedHero = tabDataItem.saveSelectedHero
                    newTabDataItem.selectedHero = tabDataItem.selectedHero
                    newTabDataItem.showHp = tabDataItem.showHp
                    newTabDataItem.cacheData = tabDataItem.cacheData
                    local usedWeapon = tabDataItem.usedWeapon
                    if usedWeapon then
                        newTabDataItem.usedWeapon = usedWeapon
                        local newLineUpIdx = key-1
                        usedWeapon.lineupIdx = newLineUpIdx
                        weapon_data.SetlocalizedWeaponData(usedWeapon.stage,usedWeapon.weaponId,usedWeapon.lineupIdx,usedWeapon.arenaType1)
                    end
                    -- if not dontUpDateByServer then
                    --     local isActive=usedWeapon.weaponId~=0
                    --     if isActive then
                    --         reqWeaponUpdate(usedWeapon.stage,usedWeapon.weaponId,true,usedWeapon.lineupIdx,usedWeapon.arenaType1)
                    --     else
                    --         local unUsedWeapon=newTabDataItem.usedWeapon
                    --         reqWeaponUpdate(unUsedWeapon.stage,unUsedWeapon.weaponId,false,unUsedWeapon.lineupIdx,unUsedWeapon.arenaType1)
                    --     end
                    -- end
                end
                TabData = newTabData
                if window and window.UIRoot then
                    window:UpdateUi()
                    self:SetWeaponIcon()
                end
            end

            if leftRoleInfo and leftRoleInfo.team then
                local newTeam = {}
                for k,v in pairs(sort) do
                    newTeam[k] = leftRoleInfo.team[v]
                end
                leftRoleInfo.team = newTeam
            end
            if window and window.UIRoot then
                window:UpdateUi()
                self:SetWeaponIcon()
            end
        else
            local newTabData = {}
            for key, tabDataItem in pairs(TabData) do
                newTabData[key] =
                {
                    enemyHeroData = tabDataItem.enemyHeroData,
                    enemyShowHp = tabDataItem.enemyShowHp,
                    monsterPower = tabDataItem.monsterPower,
                    originEnemyPower =  tabDataItem.originEnemyPower,
                }
            end
            for key, newIdex in ipairs(sort) do
                local tabDataItem = TabData[newIdex]
                local newTabDataItem = newTabData[key]
                newTabDataItem.saveSelectedHero = tabDataItem.saveSelectedHero
                newTabDataItem.selectedHero = tabDataItem.selectedHero
                local usedWeapon = tabDataItem.usedWeapon
                newTabDataItem.usedWeapon = usedWeapon
                local newLineUpIdx = key-1
                usedWeapon.lineupIdx = newLineUpIdx
                --SetSaveWeaponData(usedWeapon,nil,false,false,key)
                weapon_data.SetlocalizedWeaponData(usedWeapon.stage,usedWeapon.weaponId,usedWeapon.lineupIdx,usedWeapon.arenaType1)
                -- if not dontUpDateByServer then
                --     local isActive=usedWeapon.weaponId~=0
                --     if isActive then
                --         reqWeaponUpdate(usedWeapon.stage,usedWeapon.weaponId,true,usedWeapon.lineupIdx,usedWeapon.arenaType1)
                --     else
                --         local unUsedWeapon=newTabDataItem.usedWeapon
                --         reqWeaponUpdate(unUsedWeapon.stage,unUsedWeapon.weaponId,false,unUsedWeapon.lineupIdx,unUsedWeapon.arenaType1)
                --     end
                -- end
            end
            TabData = newTabData
            if window and window.UIRoot then
                window:UpdateUi()
                self:SetWeaponIcon()
            end
        end
	end

	event.Register(event.UPDATE_LEGEND_CHAMPIONSHIPS_TEAM_SORT, self.OnUPDATE_LEGEND_CHAMPIONSHIPS_TEAM_SORT)

	self.OnFORMATION_SETTING_START_BATTLE = function()
		if self.onFight then
			self.onFight()
		end
	end
    self.OnUpdatePower=function(heroSid)
        if not self.isUpdatingPower then
            self.isUpdatingPowe=true
            util.DelayCallOnce(1,function()
                UpdatePower()
				if self and self:IsValid() then
                	self.isUpdatingPower=false
				end
                event.Trigger(event.SAVE_SQUAD);
            end)
        end
    end
	event.Register(event.FORMATION_SETTING_START_BATTLE, self.OnFORMATION_SETTING_START_BATTLE)
	--event.Register(event.BATTLE_TEAM_SHOW_ARTIFACT, self.RefreshTeamArtifact)
	event.Register(event.ON_HERO_UPDATE_GEMSTONE_RSP, self.RefreshHeroArtifact)
	event.Register(event.UPDATE_HERO_EQUIEPMENT, self.RefreshHeroArtifact)
    event.Register(event.UPDATE_HERO_BATTLE_PROP,self.OnUpdatePower)
    --- 屏幕尺寸变化回调
    self.OnScreenSizeChanged = function (eventName, newSize, oldSize)
        ui_select_model_node.ResetRatio(function (_rt)
            if self.rawBg and not util.IsObjNull(self.rawBg) then
                self.rawBg.texture = _rt
            end
        end)
    end
    event.Register(event.SCREEN_SIZE_CHANGED, self.OnScreenSizeChanged)
    -- 试用英雄回收
    self.checkTrialHero = function(name, sid)
        self:CheckTrialHero(name, sid[1])
    end
    event.Register(event.DESTROY_TRIALHERO, self.checkTrialHero)
end

function SelectHeroWindow:UnsubscribeEvents()
    self.closeBtn.onClick:RemoveAllListeners()
    if self.onFight then
        self.fightBtn.onClick:RemoveIntervalListener(self.onFight)
    end
    if self.onSpaceGapFight then
        self.spaceGapFightButton.onClick:RemoveIntervalListener(self.onSpaceGapFight)
    end
    if self.OnGrind then
        self.grindBtn.onClick:RemoveIntervalListener(self.OnGrind, 1)
    end
    self.weaponButton.onClick:RemoveListener(self.OnWeaponButton)
    self.nextButton.onClick:RemoveListener(self.OnNextButton)
    self.teamRecommendButton.onClick:RemoveListener(self.OnTeamRecommendButton)
    self.quickFightBtn.onClick:RemoveListener(self.OnBtn_quickFighClickedProxy)
    self.resetraintBtn.onClick:RemoveListener(self.onClickResetrain)
    self.grindTipsBtn.onClick:RemoveListener(self.OnClickGrindTips)
    self.spaceGapTipButton.onClick:RemoveIntervalListener(self.spaceGapTipEvent)
    event.Unregister(event.HALO_UPGRADE_UPDATE_DATA, self.OnUpdateHaloData)
    event.Unregister(event.WEAPON_GET_CHOOSE_RSP,self.onGetChooseWeapon)
    self.haloEffect.gameObject:SetActive(false)
    event.Unregister(event.UPDATE_ENEMY_CERATE, self.updateEnemyCERateEvent)
    event.Unregister(event.BUTTON_TIPS_TRIGGER_CLOSE , self.OnBUTTON_TIPS_TRIGGER_CLOSE)
	event.Unregister(event.UPDATE_LEGEND_CHAMPIONSHIPS_TEAM_SORT, self.OnUPDATE_LEGEND_CHAMPIONSHIPS_TEAM_SORT)
	event.Unregister(event.FORMATION_SETTING_START_BATTLE, self.OnFORMATION_SETTING_START_BATTLE)
    event.Unregister(event.SCREEN_SIZE_CHANGED, self.OnScreenSizeChanged)
    --event.Unregister(event.BATTLE_TEAM_SHOW_ARTIFACT, self.RefreshTeamArtifact)
	event.Unregister(event.ON_HERO_UPDATE_GEMSTONE_RSP, self.RefreshHeroArtifact)
	event.Unregister(event.UPDATE_HERO_EQUIEPMENT, self.RefreshHeroArtifact)
    event.Unregister(event.UPDATE_HERO_BATTLE_PROP,self.OnUpdatePower)
    event.Unregister(event.DESTROY_TRIALHERO, self.checkTrialHero)
    local gw_hero_define = require "gw_hero_define"
    event.Unregister(gw_hero_define.UPGRADE_HERO_NEW,self.OnUpdateHero)
    event.Unregister(gw_hero_define.HERO_AWAKEN_NEW,self.OnUpdateHero)
    
    for i, toggle in ipairs(self.toggleGroup) do
        toggle.onValueChanged:RemoveListener(self["toggleValueChanged"..i])
    end
    for i, toggle in ipairs(self.campToggleGroup) do
        toggle.onValueChanged:RemoveListener(self["campToggleValueChanged"..i])
    end
    -- for i=0,7 do
    --     local btn = self["lockedpos"..i]
    --     if btn thenAdjustResultProcessFunc
    --     end
    -- end
    self.OneBtn.onClick:RemoveListener(self.EnemyViewOneStateEvent)
    self.TwoBtn.onClick:RemoveListener(self.EnemyViewTwoStateEvent)
    self.ThreeBtn.onClick:RemoveListener(self.EnemyViewThreeStateEvent)
    self.Btn4.onClick:RemoveListener(self.Btn4OnClickEvent)
    self.Btn5.onClick:RemoveListener(self.Btn5OnClickEvent)
    self.menuOpenBtn.onClick:RemoveListener(self.OnOpenMenu)
    self.MaskBtn.onClick:RemoveListener(self.OnCloseMenu)
    self.menuCloseBtn.onClick:RemoveListener(self.OnCloseMenu)
    self.resetBtn.onClick:RemoveListener(self.onResetBtnClick)
	self.Btn_weapon_enemy.onClick:RemoveListener(self.onEnemyWeaponClick)
	self.adjustBtn.onClick:RemoveListener(self.adjustBtnEvent)
    self.btnArtifact.onClick:RemoveAllListeners()
end

function SelectHeroWindow:SetWeaponIcon()
    --self.weaponButton.gameObject:SetActive(usedWeapon~=nil and not dontUseWeapon)
    local droneId = GWG.GWHomeMgr.droneData.GetDroneId()
    if not droneId then
        Common_Util.SetActive(self.weaponButton,false)
        return
    end
    Common_Util.SetActive(self.weaponButton,true)
    self.weaponLevel.text = "Lv."..GWG.GWHomeMgr.droneData.OnGetDroneLv()
    local droneSkin = GWG.GWHomeMgr.droneData.GetCurAdroneSkin()
    local cfg = game_scheme:AccessoryAdorn_0(droneSkin)
    local icon = cfg and cfg.icon or game_scheme:InitBattleProp_0(8131).szParam.data[1]
    self.spriteAsset:GetSprite(tonumber(icon),function(sprite)
        self.weaponIcon.sprite = sprite
    end)
    --TODO 对手武器
    
    --下面是原本的逻辑
    --self.weaponButton.gameObject:SetActive(usedWeapon~=nil and not dontUseWeapon)

    --local weapon_data = require("weapon_data")
    --local ActiveWeapon = weapon_data.GetActivedWeapon()
    --local usedWeapon = GetCurData().usedWeapon
    --if usedWeapon and usedWeapon.weaponId == 0 and util.get_len(ActiveWeapon) > 0 and not excludeWeaponIds then
    --    if forSpaceGap or forPlot then
    --        return 
    --    end
    --    local unforced_guide_mgr = require "unforced_guide_mgr"
    --    unforced_guide_mgr.Unlock(32)
    --    if unforced_guide_mgr.GetCurGuide() == 34 and unforced_guide_mgr.GetCurStep() then
    --        local cfg = unforced_guide_mgr.GetCurStepConfig()
    --        if cfg then
    --            if cfg.stepId == 222 then
    --                event.Trigger(event.ENTER_INSTANLL_WEAPON)
    --            end
    --        end
    --    else
    --        if not forSpaceGap and not forPlot then
    --            showWeaponGuide = true
    --            button_tips_trigger.Show("ui_select_hero",27,self.weaponButton.transform,nil,nil,true,nil,true,nil,nil,nil,self.curOrder+3)
    --        end
    --    end
    --end
    ---- self.weaponEffect.gameObject:SetActive(usedWeapon and usedWeapon.weaponId == 0 and #ActiveWeapon > 0)
    --local isActive = usedWeapon and usedWeapon.weaponId ~= 0 and weapon_data.GetWeaponByID(usedWeapon.weaponId).bActive
    --self.weaponMask.gameObject:SetActive(isActive)
    --self.spriteAsset:GetSprite(4601, function(sprite)
    --    self.weaponIcon.sprite = sprite
    --end)
    --if usedWeapon and usedWeapon.weaponId and usedWeapon.weaponId ~= 0 then
    --    local cfg = game_scheme:Hero_0(usedWeapon.weaponId + 79999)
    --    if cfg then
    --        local faceid = cfg.faceID.data[0]
    --        self.spriteAsset:GetSprite(faceid, function(sprite)
    --            self.weaponIcon.sprite = sprite
    --        end)
    --    end
    --    if button_tips_trigger.GetCurNameId() == 27 then
    --        -- 战舰引导
    --        showWeaponGuide = nil
    --        button_tips_trigger.Close()
    --    end
    --end
    ---- self.weaponEffect.sortingOrder = self.curOrder + 1
    --
    ---- 对手武器
	--local enmyUsedWeapon = GetCurData().enemyWeapon
	--self.Mask_weapon_enemy.gameObject:SetActive(not enmyUsedWeapon or enmyUsedWeapon.weaponId == 0)
    --self.spriteAsset:GetSprite(4601, function(sprite)
    --    self.Icon_weapon_enemy.sprite = sprite
    --end)
	--if enmyUsedWeapon and enmyUsedWeapon.weaponId ~= 0 then
    --    local cfg = game_scheme:Hero_0(enmyUsedWeapon.weaponId + 79999)
    --    if cfg then
    --        local faceid = cfg.faceID.data[0]
    --        self.spriteAsset:GetSprite(faceid, function(sprite)
    --            self.Icon_weapon_enemy.sprite = sprite
    --        end)
    --    end
    --end
end


local redColor = "<color=#FF0000>%d</color>"
local greenColor = "<color=#00FF00>%d</color>"
function UpdateMultiSecretTipData(tipID,factionID,factionNum,hasfactionNum,replaceFactionID,replceFactionNum,hasReFactionNum)
    window.tipID = tipID
    window.factionNum = window.factionNum or factionNum
    window.factionID = window.factionID or factionID
    window.replaceFactionID = window.replaceFactionID or replaceFactionID
    window.replceFactionNum = window.replceFactionNum or replceFactionNum
    window.curfactionNum = hasfactionNum
    window.curReplaceNum = hasReFactionNum
    -- --print("tip1:",string.format("%s阵营≥%s名", lang.Get(hero_mgr.Hero_Type_Name[window.factionID]),window.factionNum), "tip2:",string.format("可用%s名%s英雄替代",window.replceFactionNum, lang.Get(hero_mgr.Hero_Type_Name[window.replaceFactionID])))
    RefreshMultiSecretUI()
    if window and window:IsValid() then
        if forMultiSecretPlace then
            --TODO:播放动画
            -- local _selectHero = GetSelectedHero()
            -- for i,v in pairs(_selectHero) do
            --     local hpPro,attackPro,tFlag = window:GetHeroProData(v.heroID,v.numProp.starLv)
            --     UpdateProAni(true,hpPro,attackPro,i,tFlag)
            -- end
        end
    end
end


function SetCurFactionNum()
    if forMultiSecretPlace then
        local num = 0
        local replaceNum = 0
        local selected = GetSelectedHero()
        for _,v in pairs(selected or {}) do
            if v ~= nil then
                local cfg_select = game_scheme:Hero_0(v.heroID)
                if cfg_select then
                    if cfg_select.type == window.factionID then
                        num= num+1
                    elseif cfg_select.type == window.replaceFactionID and replaceNum<window.replceFactionNum then
                        replaceNum = replaceNum+1
                    end
                end
            end
        end
        window.curfactionNum = num
        window.curReplaceNum = replaceNum
    end
end

function RefreshMultiSecretUI(isShow,hero,index)
    if window and window:IsValid() then
        if forMultiSecretPlace then
            --[[
            window.multiSecretTipTxt.gameObject:SetActive(true)
            -- --print("window.curfactionNum",window.curfactionNum,"window.factionNum",window.factionNum)
            if window.curfactionNum and window.factionNum then
                local curNum = (window.curfactionNum<window.factionNum) and string.format(redColor,window.curfactionNum) or string.format(greenColor,window.curfactionNum)
                window.multiSecretTipTxt.text = window.tipID and string.format(lang.Get(window.tipID),window.factionNum,curNum,window.factionNum) or  ""
            end
            ]]--

            window.multiSecretRoot.gameObject:SetActive(true)
            if window.factionID then
                window.campLimitImg:Switch(window.factionID-1)
            end
            if window.replaceFactionID then
                window.replceCampLimitImg:Switch(window.replaceFactionID-1)
            end
            local langID = 93069
            if window.curfactionNum and window.curReplaceNum and window.factionNum and window.curfactionNum+window.curReplaceNum>=window.factionNum then
                langID = 93070
            end
            local str = string.format(lang.Get(langID), lang.Get(hero_mgr.Hero_Type_Name[window.factionID]),window.factionNum)
            window.campLimitTxt.text = str
            window.replceCampLimitTxt.text = string.format(lang.Get(93071),window.replceFactionNum, lang.Get(hero_mgr.Hero_Type_Name[window.replaceFactionID]))
            LayoutRebuilder.ForceRebuildLayoutImmediate(window.multiSecretObj)
            if isShow and hero then
                local hpPro,attackPro,tFlag = window:GetHeroProData(hero.heroID,hero.numProp and hero.numProp.starLv or hero.heroStar)
                UpdateProAni(isShow,hpPro,attackPro,index,tFlag)
            elseif not isShow and index then
                UpdateProAni(isShow,nil,nil,index)
            end
        end
    end
end


--设置单人秘境相关的配置
function UpdateSecretPlaceUI(tipID)
    if window and window:IsValid() then
        window.teamRecommendButton.gameObject:SetActive(false)
        window.weaponButton.gameObject:SetActive(false)
        window.spaceGapTips.gameObject:SetActive(true)
        --TODO：多人秘境需要刷新提示
        window.tipsTxt.text = tipID and lang.Get(tipID) or lang.Get(183005)
        window.spaceGapTipButton.gameObject:SetActive(false)
        window.spaceGapFightButton.gameObject:SetActive(false)
        window.fightBtn.gameObject:SetActive(true)
        --if window.lockedBtns then
            for i = 0, maxSelectNumber - 1 do
                --if window.heroMySNodes then
                --    window.heroMySNodes[i].gameObject:SetActive(GetSlotState()[i] ~= 0)
                --end
                local h = GetSelectedHero()[i]
                if h == nil then
                    if GetSlotState() then
                        -- 1:未上锁
                        -- 0：上锁
                        --window.lockedBtns[i].gameObject:SetActive(GetSlotState()[i] == 0)
                    else
                    end
                end
            end
        --end
    end
end

-- 设置成时空裂缝相关的配置
function UpdateSpaceGapUI(tipID)
    if window and window:IsValid() then
        window.teamRecommendButton.gameObject:SetActive(false)
        window.weaponButton.gameObject:SetActive(false)
        window.spaceGapTips.gameObject:SetActive(true)
        window.tipsTxt.text = tipID and lang.Get(tipID) or lang.Get(183005)
        window.spaceGapTipButton.gameObject:SetActive(true)
        window.spaceGapFightButton.gameObject:SetActive(true)
        window.fightBtn.gameObject:SetActive(false)
        --if window.lockedBtns then
            for i = 0, maxSelectNumber - 1 do
                --if window.heroMySNodes then
                --    window.heroMySNodes[i].gameObject:SetActive(GetSlotState()[i] ~= 0)
                --end
                if ui_select_model_node.GetMyTextNodes() and ui_select_model_node.GetMyTextNodes()[i] then
                    Common_Util.SetActive(ui_select_model_node.GetMyTextNodes()[i].gameObject,GetSlotState()[i] ~= 0)
                    --ui_select_model_node.GetMyTextNodes()[i].gameObject:SetActive(GetSlotState()[i] ~= 0)
                end
                if h == nil then
                    if GetSlotState() then
                        -- 1:未上锁
                        -- 0：上锁
                        --window.lockedBtns[i].gameObject:SetActive(GetSlotState()[i] == 0)
                    else
                    end
                end
            end
        --end
    end
end

function SetShowFormatSetting(bo)
    showFormatSetting =bo
end
-- 设置英雄剧情副本配置
function UpdatePlotUI(tipID)
    if window and window:IsValid() then
        window.teamRecommendButton.gameObject:SetActive(false)
        window.weaponButton.gameObject:SetActive(false)
        window.spaceGapTips.gameObject:SetActive(true)
        window.tipsTxt.text = tipID and lang.Get(tipID) or lang.Get(183005)
        window.spaceGapTipButton.gameObject:SetActive(true)
        window.spaceGapTipButtonReddot.gameObject:SetActive(false)
        window.spaceGapFightButton.gameObject:SetActive(true)
        window.fightBtn.gameObject:SetActive(false)
        --if window.lockedBtns then
            for i = 0, maxSelectNumber - 1 do
                --if window.heroMySNodes then
                --    window.heroMySNodes[i].gameObject:SetActive(GetSlotState()[i] ~= 0)
                    if ui_select_model_node.GetMyTextNodes() and ui_select_model_node.GetMyTextNodes()[i] then
                        ui_select_model_node.GetMyTextNodes()[i].gameObject:SetActive(GetSlotState()[i] ~= 0)
                    end
                --end
                local h = GetSelectedHero()[i]
                if h == nil then
                    if GetSlotState() then
                        -- 1:未上锁
                        -- 0：上锁
                        --window.lockedBtns[i].gameObject:SetActive(GetSlotState()[i] == 0)
                    else
                    end
                end
            end
        --end
    end
end
local dragSlot = -1
function SelectHeroWindow:OnShow()
    self.__base:OnShow()
    force_guide_system.TriEnterEvent(force_guide_event.tEventMonsterAttack)
    self:OnPlayAShowAni(self.onPlayAShowAni, nil)
    if self.powerGrowUp == nil then
        --if isArenaDefence then
        --    self.powerGrowUp = ui_growup_number.Init(self.arenaPowerText, false, "ui_select_hero", true)
        --else
        --    self.powerGrowUp = ui_growup_number.Init(self.power, false, "ui_select_hero", true)
        --end
    end
    self.myOldPower = 0
    self:SetCampTrialBuff()
    --self.bgTrans.sizeDelta = {x = screen_util.width,y=screen_util.height}
    local net_festival_activity_module = require "net_festival_activity_module"
    net_festival_activity_module.ClearBatlleVictoryReward()
    --CheckIsExistAssignHero()
    local OnMouseButtonDownFunc = function(slot)
        dragSlot = slot
        self.onButtonDownTime = Time.realtimeSinceStartup
        self:OnDownSceneHeroLayout(slot)
    end
    local OnDragGameObjectFunc = function(screenPos,worldPos, dragGameObject)
        local x = worldPos.x - ui_select_model_node.GetPos().x
        local fnode = dragGameObject:Find("Main") or dragGameObject:Find("main") or dragGameObject.transform
        local y = fnode.position.y- ui_select_model_node.GetPos().y
        local temp = 0
        if dragSlot > 1 then
            temp = 1.4
        end
        if y < -3.4 + temp then
            --下阵
            self.initDragSlotX = -1
            self.initDragSlotY = -1
        else
            if y < -1.3 + temp then
                self.initDragSlotY = 2 --第二排
            else
                self.initDragSlotY = 0  --第一排
            end

            if self.initDragSlotY == 0 then
                if x < -0.4 then
                    -- 0,3
                    self.initDragSlotX = 1  --左列
                    --log.Error("左上"..x)
                else
                    -- 2,5
                    self.initDragSlotX = 2      --右列
                    --log.Error("右上"..x)
                end
            elseif self.initDragSlotY == 2 then
                if x < -0.6 then
                    -- 1,4
                    self.initDragSlotX = 1 --左列
                    --log.Error("左下"..x)
                elseif x < 0.7 then
                    -- 0,3
                    self.initDragSlotX = 2  --中列
                    --log.Error("中下"..x)
                else
                    -- 2,5
                    self.initDragSlotX = 3      --右列
                    --log.Error("右下"..x)
                end
            end
        end
        self.initDragSlot = self.initDragSlotX + self.initDragSlotY - 1
        
        local mTime = Time.realtimeSinceStartup - self.onButtonDownTime
        if mTime < 0.2 and force_guide_system.GetCurStep() ~= 314 and force_guide_system.GetCurStep() ~= 315
                and force_guide_system.GetCurStep() ~= 747 and force_guide_system.GetCurStep() ~= 748 then
            self.initDragSlot = -100
        end
        if force_guide_system.GetCurStep() == 314 or force_guide_system.GetCurStep() == 315
                or force_guide_system.GetCurStep() == 747 or force_guide_system.GetCurStep() == 748 then
            self.initDragSlot = 0
        end
        if mTime < 0.2 then
            for i = 1,5 do
                if RectTransformUtility.RectangleContainsScreenPoint(self["heroUpgradeBtnTrans_" .. i], Vector2(screenPos.x, screenPos.y), self.camera) then
                    self.initDragSlot = -200
                    break
                end
            end
        end

        self:OnDragSceneHeroLayout(self.initDragSlot)
    end
    local OnMouseButtonUpFunc = function(dragObj)
        if dragObj then
            local slot = self.initDragSlot--self.initDragSlotX + self.initDragSlotY - 1
            if slot ~= -200 then
                if force_guide_system.GetCurStep() == 314 or force_guide_system.GetCurStep() == 315
                        or force_guide_system.GetCurStep() == 747 or force_guide_system.GetCurStep() == 748 then
                    slot = 0
                end
                local mTime = Time.realtimeSinceStartup - self.onButtonDownTime
                if mTime < 0.2 and force_guide_system.GetCurStep() ~= 314 and force_guide_system.GetCurStep() ~= 315
                        and force_guide_system.GetCurStep() ~= 747 and force_guide_system.GetCurStep() ~= 748 then
                    slot = -100
                end
                self:OnUpSceneHeroLayout(self.initDragSlot, dragObj)
            end
        end
    end
    self.rtRoot = self.UIRoot:GetComponent(typeof(RectTransform))
    ui_drag_object.InitDrag(true, self.rtRoot, self.rawBg)
    ui_drag_object.SetDragMode(GWG.GWConst.enDragType._5v5)
    ui_drag_object.OnMouseButtonDownFunc = OnMouseButtonDownFunc
    ui_drag_object.OnDragGameObjectFunc = OnDragGameObjectFunc
    ui_drag_object.OnMouseButtonUpFunc = OnMouseButtonUpFunc
    
    if force_guide_system.GetCurStep() then
        -- log.Error("ExeStep>>>>>>>>>>>>>"..force_guide_system.GetCurStep())
        if force_guide_system.GetCurStep() >= 301 and force_guide_system.GetCurStep() < 357 then
            ui_drag_object.PauseTimeTicker()
        end
    end
    if not battle_data.skipBattle then
        --缓存战斗背景
        local battle_preview_manager = require "battle_preview_manager"
        battle_preview_manager.DelayPreLoadBattleBackground()
    end
    
    local weapon_data = require("weapon_data")
    local ActiveWeapon = weapon_data.GetActivedWeapon()
    self.grindTipsCanvas.sortingOrder = self.curOrder + 2

    local menu_bot_data = require "menu_bot_data"
    menu_bot_data.CloseAllPage()

    local unforced_guide_mgr = require "unforced_guide_mgr"
    if unforced_guide_mgr.GetCurGuide() == 19 or unforced_guide_mgr.GetCurGuide() == 22 then
        local unforced_guide_event_define = require "unforced_guide_event_define"
        event.Trigger(unforced_guide_event_define.start_arena_hero_quick_fight)
    end
    --[[    if unforced_guide_mgr.GetCurStep() then
        local cfg = unforced_guide_mgr.GetCurStepConfig()
        local curGuide = unforced_guide_mgr.GetCurGuide()
        if cfg then
            local unforcedGuideBattleType = unforced_guide_mgr.GetBattleType()
            local stepID = cfg.stepId
            if unforcedGuideBattleType == unforced_guide_mgr.BATTLE_TYPE_NUM.LOST_LAND and curGuide == 52 then
                if stepID == 410 or stepID == 414 then
                    event.Trigger(event.ENTER_SELECT_HERO)
                end
            end
        end
    end]]

    if self.delayCall then
        util.RemoveDelayCall(self.delayCall)
        self.delayCall = nil
    end
    self.delayCall = util.DelayCallOnce(0.3, function()
        self.hasInit = true
        if showFormatSetting and self and self.adjustBtnEvent and isShowAdjustBtn and GetLineCount() > 1 then
            self.adjustBtnEvent()
        end
    end)
    self.gridBtnName.text = lang.Get(20397) --新英雄活动使用
end

--阵营试炼设置Buff显示
function SelectHeroWindow:SetCampTrialBuff()
    local battleType = ui_select_model_node.GetBattleType()
    if not camp_trial_data.GetIsTrialBattle(battleType) then
        self.rect_trialBuff.gameObject:SetActive(false)
        return
    end
    local trialData = camp_trial_data.GetTrialData()
    local trialCfg = game_scheme:CampTrial_0(trialData.trialType)
    local gwMapEffectId = trialCfg.TrialBuff
    local buffCfg = game_scheme:GWMapEffect_0(gwMapEffectId)
    if buffCfg then
        local shrinkTxt = require "shrinkTxt"
        self.txt_buff_desc.text =lang.Get(buffCfg.name)
        shrinkTxt.auto_wrap_truncate(self.txt_buff_desc,28,20)
        local buffValue = buffCfg.strParam[0] or 0
        self.txt_buff_value.text = "+" .. tostring(buffValue / 100) .. "%"
        self.spriteAsset:GetSpriteSafely(self.img_buff_normal,trialCfg.Icon)
        self.rect_trialBuff.gameObject:SetActive(true)
    end
end

function GetTempPower()
    return tempPower
end

function SelectHeroWindow:Close()
    ui_drag_object.Close()
    for _, v in pairs(timerDic) do
        util.RemoveDelayCall(v)
    end
    timerDic= {}
    if not util.IsObjNull(ui_select_model_node.GetHeroSelectModel()) then
        ui_select_model_node.GetHeroSelectModel().gameObject:SetActive(false)
    end
    if self.powerGrowUp then
        self.powerGrowUp:Destroy()
        self.powerGrowUp = nil
    end
    for i= 0, 4 do
        if self.heroModels and self.heroModels[i] then
            self.heroObjs[i] = nil
            self.heroModels[i]:Dispose()
            self.heroModels[i] = nil
        end
        
        if self["addProImg_"..i] then
            self["addProImg_"..i].gameObject:SetActive(false)
        end

        if self["heroUpgradeBtn_"..(i + 1)] and not util.IsObjNull(self["heroUpgradeBtn_"..(i + 1)]) then
            self["heroUpgradeBtn_"..(i + 1)].onClick:RemoveAllListeners()
        end
        -- if self.heroModelsFunc[i] then
        --     self.heroModelsFunc[i] = nil
        -- end
    end 
    for i= 0, 4 do
        if enemyHeroProfiles and enemyHeroProfiles[i] then
            enemyHeroProfiles[i]:Dispose()
        end
        enemyHeroProfiles[i] = nil
    end
    for i= 0, 4 do
        if enemyHeroModelProfiles and enemyHeroModelProfiles[i] then
            enemyHeroModelProfiles[i]:Dispose()
        end
        enemyHeroModelProfiles[i] = nil
        enemyHeroModelFunc[i] = nil
    end
    --self:DisposeHeroArtifact()
    enemyHeroProfiles = {}
    enemyHeroModelProfiles = {}
    enemyHeroModelFunc = {}
    self.heroNode = nil
    self.heroObjs = nil
    self.heroModels = nil
    self.heroModelsFunc = nil
    self.heroMySNodes = nil
    self.heroEnemySNodes = nil
    self.heroShadowNode = nil
    showFormatSetting =nil
    self.oldDragSlot = -1
    event.Trigger(event.UI_SELECT_HERO_CLOSE)
    if self.UIRoot and self:IsValid() then
        self:UnsubscribeEvents()
        if showGrind then
            self.onCloseEvent = nil
        end
        FleetID = nil
        if self.delayCall then
            util.RemoveDelayCall(self.delayCall)
            self.delayCall = nil
        end
    end
    prefs = 0
    local unforced_guide_mgr = require "unforced_guide_mgr"
    if unforced_guide_mgr.GetCurGuide() == 19 or unforced_guide_mgr.GetCurGuide() == 22 then
        unforced_guide_mgr.CloseGuide()
    end
--[[    local curGuide = unforced_guide_mgr.GetCurGuide()
    if curGuide == 2 or curGuide == 31 or curGuide == 34 or curGuide == 37 or curGuide == 52 then
        local cfg = unforced_guide_mgr.GetCurStepConfig()
        if cfg and cfg.stepId == 4 or cfg.stepId == 5 or cfg.stepId == 222 or cfg.stepId == 410 or cfg.stepId == 414 then
            if cfg.stepId == 222 or cfg.stepId == 410 or cfg.stepId == 414 then
                if windowMgr:IsModuleShown("ui_guide_assistant_new") then
                    windowMgr:UnloadModuleImmediate("ui_guide_assistant_new")
                end
            end
            if windowMgr:IsModuleShown("ui_pointing_target") then
                windowMgr:UnloadModuleImmediate("ui_pointing_target")
            end
        elseif cfg.stepId == 232 or cfg.stepId == 233 or cfg.stepId == 234 then
            event.Trigger(event.END_SELECT_HERO)
            event.Trigger(event.CLICK_TIPBTN)
            event.Trigger(event.END_INTRODUCE_TIPS)
        end
    end

    local unforced_guide_mgr = require "unforced_guide_mgr"
    if unforced_guide_mgr.GetCurStep() then
        local cfg = unforced_guide_mgr.GetCurStepConfig()
        if unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.SOCIATY_WAR_DEFEND and unforced_guide_mgr.GetCurGuide() == 35 and cfg and cfg.stepId == 225 then
            event.Trigger(event.SELECT_SOCIATY_WAR_DEFEND_HERO)
        elseif unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.SOCIATY_WAR_COMBAT and unforced_guide_mgr.GetCurGuide() == 36 and cfg and cfg.stepId == 229 then
            event.Trigger(event.SELECT_SOCIATY_WAR_COMBAT_HERO)
        end
    end]]

    button_tips_trigger.Close()
    preSlot = nil
    defenseSetIndexLineup = {}
    self.onPlayAShowAni = nil

    if self.bot_hero_drag_profile then
        self.bot_hero_drag_profile:Dispose()
        self.bot_hero_drag_profile = nil
    end

    if self.spriteAsset then
        self.spriteAsset:Dispose()
        self.spriteAsset = nil
    end

    if self.powerTicker then
        util.RemoveDelayCall(self.powerTicker)
        self.powerTicker = nil
    end

    if up_profile then
        for k, v in pairs(up_profile) do
            if v then
                v:Dispose()
            end
            up_profile[k] = nil
        end
    end

    if self.drag_pos then
        self.drag_pos = nil
    end

    if self.OverLayItem then
        self.OverLayItem:Close()
        self.OverLayItem = nil
    end

    if self.bot_drag_pos then
        self.bot_drag_pos = nil
    end

    if self.iconUI then
        self.iconUI:Dispose()
    end

    if self.iconEnemyUI then
        self.iconEnemyUI:Dispose()
    end

    if self.powerUpList then
        for k,v in pairs(self.powerUpList) do
            GameObject.Destroy(v)
            self.powerUpList[k] = nil
        end
        self.powerUpList = nil
    end

    if self.powerDownList then
        for k,v in pairs(self.powerDownList) do
            GameObject.Destroy(v)
            self.powerDownList[k] = nil
        end
        self.powerDownList = nil
    end

    if self.heroProUpList then
        for k,v in pairs(self.heroProUpList) do
            GameObject.Destroy(v.obj)
            self.heroProUpList[k].obj = nil
        end
        self.heroProUpList = nil
    end

    curEnemyIndex = HeroLineUpEnum.LineUp1
    dontUseWeapon = false
    banSelectTips = nil
    bLnstanceEnemy = nil
    event.Trigger(event.ON_GUIDE_BUTTON_TIPS, false)
    factionWantedType = nil
    filterHeroSidList = {}

    canSelectHeros = nil
    self.slot = {}
    --self.lockedBtns = {}
    self.botHeroSlot = {}
    haloID = nil
    
    self.profileEnemy = nil
    self.profile = nil
    
    self.camera = nil
    if self.scroll_table and not util.IsObjNull(self.scroll_table) then
        self.scroll_table:ItemsDispose()
    end
    self:SepCloseFunc()
    self.__base:Close()
    window = nil
    
    heroCfgCache = {}
    optimizeHeroCache = {}
    
end

--变量太多，LuaProfiler监测会有问题，所以进行拆分
function SelectHeroWindow:SepCloseFunc()

    IgnoreSoldier = false
    curItem = nil
    UnUseHeroTipId = 73212
    selectedWeapon = nil
    battleType = nil
    isMulti = nil
    enemyShowHp = nil
    enemyPower = nil
    dontUpDateByServer = false
    defaultHeros = nil
    --slotState = nil
    --slotState2 = nil
    --slotState3 = nil
    self.myHalo = nil
    self.enemyHalo = nil
    self.myPos = nil
    self.enemyPos = nil
    forSpaceGap = false
    forPlot = false
    forSecretPlace = false
    forMultiSecretPlace = false
    isShowTip = false
    showEnemyHeroRarity = false
    tipsStr = nil
    fightBtnTxt = nil
    quickFightBtnTxt = nil
    isBattleDefenseLineup = nil
    isNeedSaveAtkLine = nil
    isArenaDefence = false
    isShowResetBtn = false
    isShowAdjustBtn = false
    isMainStoryChallenge = false
    isLimitHeroStar = false
    lowStarLvLimit = nil
    teamNum = nil
    leftRoleInfo = nil
    rightRoleInfo = nil
    getHeroFunc = nil
    hideTeamInfo = nil
    TabData = {}
    currentLevelData = nil
    excludeWeaponIds = nil
    isCanUseTrial = false
    OnCloseTrialProcess()
    local idle = require "idle"
    idle.ClearNormalPool(0.4)
    self:ResetValue()
end

-- 超过60个外部局部变量
function SelectHeroWindow:ResetValue()
    curType = ATTACK
end

--按下开始拖拽
function SelectHeroWindow:OnDownSceneHeroLayout(slot)
    self.curDragSlot = slot
    if self.oldDragSlot == -1 then
        self.oldDragSlot = slot
    end
    if up_profile[self.curDragSlot] and not util.IsObjNull(up_profile[self.curDragSlot].gameObject) then
        --up_profile[self.curDragSlot].gameObject:SetActive(false)
    end
    self:ArtifactStateSet(self.curDragSlot , false)
end

--上阵英雄拖拽3D(目标位置)
function SelectHeroWindow:OnDragSceneHeroLayout(slot)
    -- local slotstate = GetSlotState()
    -- if slotstate and slotstate[slot] == 0 then
    --     return
    -- end
    if slot == -200 then
        return
    end
    if slot < 0 or self.oldDragSlot ~= slot then
        self:SetParentAndReset(self.heroObjs[self.oldDragSlot],self.heroNode[self.oldDragSlot])
        if up_profile[self.oldDragSlot] and not util.IsObjNull(up_profile[self.oldDragSlot].gameObject) then
            --up_profile[self.oldDragSlot].gameObject:SetActive(true)
        end
        self:ArtifactStateSet(self.oldDragSlot , true)
    end
    local state = GetCurData() and GetCurData().slotState and GetSlotState()[slot] == 0
    if state then
        self:SetParentAndReset(self.heroObjs[self.oldDragSlot],self.heroNode[self.oldDragSlot])
        if up_profile[self.oldDragSlot] and not util.IsObjNull(up_profile[self.oldDragSlot].gameObject)  then
            --up_profile[self.oldDragSlot].gameObject:SetActive(true)
        end
        self:ArtifactStateSet(self.oldDragSlot , true)
        return
    end
    self.oldDragSlot = slot
    if self.heroObjs[self.curDragSlot] then
        self:SetParentAndReset(self.heroObjs[self.curDragSlot],self.heroNode[slot])
        if up_profile[self.curDragSlot] and not util.IsObjNull(up_profile[self.curDragSlot].gameObject) then
            --up_profile[self.curDragSlot].gameObject:SetActive(false)
        end
        self:ArtifactStateSet(self.curDragSlot , false)
    end
    if self.heroObjs[slot] then
        self:SetParentAndReset(self.heroObjs[slot],self.heroNode[self.curDragSlot])
        if up_profile[slot] and not util.IsObjNull(up_profile[slot].gameObject) then
            --up_profile[slot].gameObject:SetActive(false)
        end
        self:ArtifactStateSet(slot , false)
    end
end

--拖拽后松开(目标位置，当前拖拽的obj)
function SelectHeroWindow:OnUpSceneHeroLayout(slot, dragObj)
    local hero = GetSelectedHero()[self.curDragSlot]
    if hero and hero.isHelpNpc then --帮助的npc不可以拖拽
        self:SetParentAndReset(self.heroObjs[self.oldDragSlot],self.heroNode[self.oldDragSlot])
        self:ArtifactStateSet(self.oldDragSlot , true)
        return
    end
    if slot ~= -100 and self.oldDragSlot == self.curDragSlot then
        self:SetParentAndReset(self.heroObjs[self.oldDragSlot],self.heroNode[self.oldDragSlot])
        if up_profile[self.oldDragSlot] and not util.IsObjNull(up_profile[self.oldDragSlot].gameObject) then
            --up_profile[self.oldDragSlot].gameObject:SetActive(true)
        end
        self:ArtifactStateSet(self.oldDragSlot , true)
        return
    end
    local state = GetCurData() and GetCurData().slotState and GetSlotState()[slot] == 0
    if state then
        self:SetParentAndReset(self.heroObjs[self.curDragSlot],self.heroNode[self.curDragSlot])
    if up_profile[self.curDragSlot] and not util.IsObjNull(up_profile[self.curDragSlot].gameObject)  then
        --up_profile[self.curDragSlot].gameObject:SetActive(true)
    end
        self:ArtifactStateSet(self.curDragSlot , true)
        return
    end

    local selectedHero = GetSelectedHero()
    if slot < 0 then
        --local hero = GetSelectedHero()[self.curDragSlot]
        if hero and defaultHeros and defaultHeros[hero.heroID] then
            self:SetParentAndReset(self.heroObjs[self.curDragSlot],self.heroNode[self.curDragSlot])
                if up_profile[self.curDragSlot] and not util.IsObjNull(up_profile[self.curDragSlot].gameObject)  then
                    --up_profile[self.curDragSlot].gameObject:SetActive(true)
                end
            self:ArtifactStateSet(self.curDragSlot, true)
            return

        end
        selectedHero[self.curDragSlot] = nil
        self:PerformDeselect(self.curDragSlot, GetSelectedHero()[slot])

        HeroData = self:GetUnselectHerodata()
        local temp = {}
        if GetHelpHero() then
            for i,v in ipairs(helpNpcList) do
                if v.heroData.heroSid == 0 then
                    table.insert(temp,v.heroData)
                end

            end
        end
        for i,v in ipairs(HeroData) do
            table.insert(temp,v)
        end
        self.scroll_table.data= temp
        self.scroll_table:Refresh(-1,-1)
        CheckFinger()
    else
        if self.heroObjs[slot] then
            self:PerformExchange(self.curDragSlot, slot)
            --新手引导
            force_guide_system.TriComEvent(force_guide_event.cComChangeFormation)
        else
            local hero = selectedHero[self.curDragSlot]
            selectedHero[self.curDragSlot] = nil
            selectedHero[self.oldDragSlot] = hero
            self:PerformDeselect(self.curDragSlot)
            self:PerformSelect(hero,self.oldDragSlot)
        end
    end
    self.oldDragSlot = -1
    self.curDragSlot = -1
    UpdatePower()
    event.Trigger(event.SAVE_SQUAD);
end

function SelectHeroWindow:SetParentAndReset(hero, parentTr)
    if hero and parentTr then
        hero.transform:SetParent(parentTr)
        hero.transform.localPosition = Vector3.zero
    end
end


function SelectHeroWindow:ChangeHero(iSlot, slot, lastSlot)
    if iSlot then
        local s = self.heroObjs[slot]
        local t = self.heroObjs[lastSlot]
        self.heroObjs[slot] = t
        self.heroObjs[lastSlot] = s
        if self.heroObjs[slot] then
            self.heroObjs[slot].transform:SetParent(self.heroNode[slot])
            self.heroObjs[slot].transform.localPosition = Vector3.zero
        end
        if self.heroObjs[lastSlot] then
            self.heroObjs[lastSlot].transform:SetParent(self.heroNode[lastSlot])
            self.heroObjs[lastSlot].transform.localPosition = Vector3.zero
        end
    else
        local s = self.heroObjs[slot]
        local t = self.heroObjs[lastSlot]
        self.heroObjs[slot] = t
        self.heroObjs[lastSlot] = s
        if self.heroObjs[slot] then
            self.heroObjs[slot].transform:SetParent(self.heroNode[slot])
            self.heroObjs[slot].transform.localPosition = Vector3.zero
        end
    end
end

--注意  此处敌我双方的heroData内部参数是不一致的！！！！！！！！
function SelectHeroWindow:LoadHeroModelInfo(slot, isEnemy, heroData, parentTrs, callback)
    if not self.heroModels then
        return
    end
    local heroModel = self.heroModels[slot]
    if isEnemy then
        heroModel = enemyHeroModelProfiles[slot]
    else
        --刷新一次自己的英雄数据
        if heroData then
            local entity = gw_hero_data.GetHeroEntityByCfgId(heroData.heroID)
            if entity and heroData.numProp then
                if heroData.numProp then
                    heroData.numProp.lv = entity:GetLevel()
                    heroData.numProp.starLv = entity:GetStar()
                else
                    heroData.heroLevel = entity:GetLevel()
                    heroData.heroStar = entity:GetStar()
                end

            end
        end
    end
    local id = heroData and heroData.heroID or 1
    local starLv = heroData and heroData.numProp and heroData.numProp.starLv or 5
    local cfg = hero_mgr.GetCfgByModelIdAndStarLv(id, starLv,heroData.skinID)
    if not cfg then return end
    local res = cfg.modelPath
    local model = heroModel and heroModel:Init(res, parentTrs, cfg.showChildModel) or ui_select_hero_model.CHeroModel():Init(res, parentTrs, cfg.showChildModel)
    local func = function()
        model:SetInfo(res, function(go)
            if callback and window and window:IsValid() then
                if not isEnemy then
                    window:InitHeroItemChuZhan(slot, heroData)
                    --window:PerformSelectArtifact(heroData,slot)
                else
                    window:InitEnemyItemChuZhan(slot, heroData)
                end
                local card = Common_Util.GetComponent(go.transform, typeof(Card))
                card:SetDissolve(0)
                callback(go)
            end
        end)
    end
    return model,func
end

function SelectHeroWindow:ResetHeroPos()
    for i=0, 4 do
        if self.heroObjs[i] then
            self.heroObjs[i].transform.localPosition = Vector3.zero
        end
    end
end


--刷新界面
function SelectHeroWindow:UpdateUi()
    if GetHelpHero() then --存在助战英雄，此时自动补全
        HeroData = self:GetUnselectHerodata()
        local selectHero = {}
        local selectedHero = GetSelectedHero()
        for i = 0,maxSelectNumber - 1 do
            if not GetSelectedHero()[i] then
                for j,k in ipairs(HeroData) do
                    local heroCfg = game_scheme:Hero_0(k.heroID)
                    if IsHeroIDSelected(k) == 0 and not selectHero[heroCfg.HerohandBookID] then
                        self:PerformSelect(k,i) --找到了一个可以加入队伍的英雄
                        selectedHero[i] = k
                        table.remove(HeroData,i)
                        selectHero[heroCfg.HerohandBookID] = true
                        break;
                    end
                end
            end
        end
    end
    --屏蔽英雄
    HeroData = self:GetUnselectHerodata()
    local temp = {}
    if GetHelpHero() then
        for i,v in ipairs(helpNpcList) do
            if v.heroData.heroSid == 0 then
                table.insert(temp,v.heroData)
            end
        end
    end
    for i,v in ipairs(HeroData) do
        table.insert(temp,v)
    end
    
    self.scroll_table.data= temp
    self.scroll_table:Refresh(-1,-1)
    CheckFinger()

    --引导相关
    local unforced_guide_mgr = require "unforced_guide_mgr"
    if unforced_guide_mgr.GetCurStep() then
        local cfg = unforced_guide_mgr.GetCurStepConfig()
        if prefs ~= 1 then
            if unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.ILLUSION_TOWER and (unforced_guide_mgr.GetCurGuide() == 2 or unforced_guide_mgr.GetCurGuide() == 31) and cfg and (cfg.stepId == 5 or cfg.stepId == 4) then
                event.Trigger(cfg.triggerEevent)
            elseif unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.AREAN_SINGLE and unforced_guide_mgr.GetCurGuide() == 4 and cfg and cfg.stepId == 73 then
                event.Trigger(cfg.triggerEevent)
            elseif unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.SOCIATY_WAR_DEFEND and unforced_guide_mgr.GetCurGuide() == 35 and cfg and cfg.stepId == 225 then
                event.Trigger(event.CHOOSE_DEFEND_HERO)
            elseif unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.SOCIATY_WAR_COMBAT and unforced_guide_mgr.GetCurGuide() == 36 and cfg and cfg.stepId == 229 then
                event.Trigger(event.CHOOSE_COMBAT_HERO)
            elseif unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.SPACE_GAP and unforced_guide_mgr.GetCurGuide() == 37 then
                if cfg then
                    if cfg.stepId == 233 then
                        event.Trigger(event.INTRODUCE_TIPBTN_POSITION)
                    elseif cfg.stepId == 232 then
                        event.Trigger(event.GUIDE_SELECT_HERO)
                    elseif cfg.stepId == 231 then
                        event.Trigger(event.SELECT_SPACEGAP_LV)
                        event.Trigger(event.GUIDE_SELECT_HERO)
                    end
                end
            end
            prefs = 1
        end
    end
    if #HeroData == 0 then
        TriGuideEvent(1)
    end

    --self:InitHeroArtifact()
    local heroLine = GetSelectedHero()
    for i = 0, maxSelectNumber do
        if heroLine and heroLine[i] then
            self:PerformSelect(heroLine[i], i , true)
        else
            self:PerformDeselect(i , true)
        end
    end
    
    UpdatePower()
    self:UpdateEnemyHeroLayout()

    event.Trigger(event.SAVE_SQUAD);
    self.spriteMask.isCustomRangeActive = true
    self.spriteMask.frontSortingOrder = self.curOrder + 2
    self.spriteMask.backSortingOrder = self.curOrder -1

    self.fightBtnEff.sortingOrder = self.curOrder + 1
    self.grindBtnEff.sortingOrder = self.curOrder + 1
end

--刷新敌人信息
function SelectHeroWindow:UpdateEnemyHeroLayout()
    if self.heroEnemySNodes == nil then
        return
    end
    if isBattleDefenseLineup then
        return
    end


    local enemyTextNodes = ui_select_model_node.GetEnemyTextNodes()    
    if GetCurData().enemyHeroData == nil and enemyTextNodes then        
        for i = 0,4 do
            local curEnemyTextNode = enemyTextNodes[i]
            if curEnemyTextNode and not util.IsObjNull(curEnemyTextNode) then
                curEnemyTextNode.gameObject:SetActive(true)
            end
            local enemySNode = self.heroEnemySNodes and self.heroEnemySNodes[i]
            if enemySNode and not util.IsObjNull(enemySNode) then
                enemySNode.gameObject:SetActive(true)
                enemySNode:Switch(7)
            end
        end
        self.epower.text = "0"
    end
    
   
    local tempData = GetCurData().enemyHeroData
    for i = 0,4 do
        local enti = tempData and tempData[i]
        if enti and (not enemyShowHp or ((enti.cHp or 0) >0)) then
            local tmpIndex = curEnemyIndex
            local minfo, initModelFunc = self:LoadHeroModelInfo(i, true, enti, ui_select_model_node.GetEnemyNode()[i].transform, function (go)
                --local v3 = ui_select_model_node.GetHeroSelectCamera():WorldToScreenPoint(go:GetComponent(typeof(Card)):GetAnchorPoint())
                --local v3 = go:GetComponent(typeof(Card)):GetAnchorPoint()
                --v3.y = v3.y + ui_select_model_node.GetHeadOffset()
                --self["epos"..i].transform.position = v3
                --self["epos"..i].transform.localPosition = Vector3(self["epos"..i].transform.localPosition.x,self["epos"..i].transform.localPosition.y+50,self["epos"..i].transform.localPosition.z)
            end)
            enemyHeroModelProfiles[i] = minfo
            enemyHeroModelFunc[i] = initModelFunc
            self.heroEnemySNodes[i].gameObject:SetActive(true)
            ui_select_model_node.GetEnemyTextNodes()[i].gameObject:SetActive(false)
            local heroCfg = enti.heroCfg or game_scheme:Hero_0(enti.heroID)
            if heroCfg then
                self.heroEnemySNodes[i]:Switch(heroCfg.rarityType)
            else
                self.heroEnemySNodes[i]:Switch(0)
            end
            
            local delayTime = 0.04 * i
            if(timerDic["InitEnemyItemChuZhanTimer"..i])then
                util.RemoveDelayCall(timerDic["InitEnemyItemChuZhanTimer"..i])
                timerDic["InitEnemyItemChuZhanTimer"..i]=nil;
            end
            timerDic["InitEnemyItemChuZhanTimer"..i]=util.DelayCallOnce(delayTime,function()
                if(self and self:IsValid())then
                    if tmpIndex ~= curEnemyIndex then
                        minfo:Dispose()
                        return
                    end
                    if enemyHeroModelFunc[i] then
                        enemyHeroModelFunc[i]()
                        enemyHeroModelFunc[i] = nil
                    end
                end
            end)
        else
            self.heroEnemySNodes[i].gameObject:SetActive(false)
            ui_select_model_node.GetEnemyTextNodes()[i].gameObject:SetActive(false)
            if enemyHeroProfiles[i] then
                enemyHeroProfiles[i]:Dispose()
                enemyHeroProfiles[i] = nil
            end
            if enemyHeroModelProfiles[i] then
                enemyHeroModelProfiles[i]:Dispose()
                enemyHeroModelProfiles[i] = nil
            end
            if enemyHeroModelFunc[i] then
                enemyHeroModelFunc[i] = nil
            end
        end
    end
    curEnemyPower = GetEnemyPower()
    self.epower.text = util.NumberWithUnit2(curEnemyPower)
end

function GetEnemyPower()
    local ceRate = laymain_data.GetStageCERate()
   return math.floor((GetCurData().originEnemyPower or 0) * ceRate)
end

function SelectHeroWindow:InitEnemyItemChuZhan(i,enti)
    if not enemyHeroProfiles[i] then
        enemyHeroProfiles[i] = hero_item_chuzhan.CHeroItem():Init(self["epos"..i],1, function (obj)
            self["enemyHaloEffect_" .. i] = obj.transform:Find("info/effect")
            obj.transform:SetAsFirstSibling()
            --obj:DisplayHeroImage(nil,enti.heroID)
        end)
        enemyHeroProfiles[i]:HideJob(true)
        enemyHeroProfiles[i]:HideStar(true)
        enemyHeroProfiles[i]:ShowHeroLevel(true)
        enemyHeroProfiles[i]:ShowRarity(showEnemyHeroRarity)
        enemyHeroProfiles[i]:ShowHpMp(GetCurData().enemyShowHp, (enti.cHp or 100)/(enti.maxHp or 100), (enti.cMp or 0)/(enti.maxMp or 100),true)
    else
        enemyHeroProfiles[i]:DisplayHeroImage(nil,enti.heroID) 
    end
    enemyHeroProfiles[i]:SetHero(enti,function()
        local ui_hero_info = require "ui_hero_info"
        local isShow = false
        local clickItem = self["epos"..i]:GetChild(0)
        ui_hero_info.ParseBossData({heroSid = nil,heroID = enti.heroID, starLv = enti.numProp and enti.numProp.starLv or enti.heroStar, level = enti.numProp and enti.numProp.lv or enti.heroLevel}, isShow,clickItem,i,bLnstanceEnemy and 3 or 2)
        windowMgr:ShowModule("ui_hero_info")
    end)

end

function SelectHeroWindow:SetModelBg(quality)
    if not quality or quality < 0 then
        return 7
    end
    if quality <= 1 then
        return 0
    elseif quality <= 3 then
        return 1
    elseif quality <= 5 then
        return 2
    elseif quality <= 7 then
        return 3
    elseif quality <= 9 then
        return 4
    elseif quality <= 11 then
        return 5
    else
        return 6
    end
end

--[[更新敌方战力，会根据服务器消息提升或降低，目前只有主线战役有此功能]]
function SelectHeroWindow:UpdateEnemyPower(rate)
	-- 只显示降低的战力
	if rate >= 1 then
		return
	end
	local common_new_pb = require "common_new_pb"
    if battleType == common_new_pb.GameGoal then
        enemyPower = math.floor(GetCurData().originEnemyPower * rate)
		self.epower.text = util.NumberWithUnit2(enemyPower or 0)
	end	
end

function SelectHeroWindow:SetFilterHero(quality,active,campIndex)
    if quality  then
        self.quality = active and  quality or nil
    end
    if campIndex then
        self.campIndex = campIndex
    end
    HeroData = self:GetUnselectHerodata()
    if (HeroData and #HeroData == 0) or banSelectTips then    
        local curNameId = button_tips_trigger.GetCurNameId()
        if curNameId == 7 or curNameId == 17 then
            button_tips_trigger.Close()
            showtips = false
        end
    end
    local temp = {}
    if GetHelpHero() then
        for i,v in ipairs(helpNpcList) do
            if v.heroData.heroSid == 0 then
                table.insert(temp,v.heroData)
            end

        end
    end
    for i,v in ipairs(HeroData) do
        table.insert(temp,v)
    end
    self.scroll_table.data= temp
    self.scroll_table:Refresh(-1,-1)
    CheckFinger()
end

--星际迷航多队英雄，在其他队已上阵英雄可显示在当前队
function SelectHeroWindow:GetMazeHerodata(hero)
    local heroListData = {}
    if forMaze and isMulti then
        --TODO:针对已有队伍索引的英雄才生效
        if GetCacheData() and GetCacheData()[hero.heroSid] then
            local teamIndex = GetCacheData() and GetCacheData()[hero.heroSid] and GetCacheData()[hero.heroSid].teamIndex or 0
            --  --print("curEnemyIndex",curEnemyIndex,"hero.heroSid",hero.heroSid,"sid",sid,"teamIndex",teamIndex)
            if (teamIndex and teamIndex ~= curEnemyIndex+1) then
                table.insert(heroListData, hero)
                
            end
        end 
        if curEnemyIndex == HeroLineUpEnum.LineUp1 then--目前在1队,2队/3队有该英雄
            if GetHeroSlot2(hero) or GetHeroSlot3(hero) then
                table.insert(heroListData, hero)
            end
        elseif curEnemyIndex == HeroLineUpEnum.LineUp2 then--目前在2队,1队/3队有该英雄
            if GetHeroSlot1(hero) or GetHeroSlot3(hero) then
                table.insert(heroListData, hero)
            end
        elseif curEnemyIndex == HeroLineUpEnum.LineUp3 then--目前在3队,1队/2队有该英雄
            if GetHeroSlot1(hero) or GetHeroSlot2(hero) then
                table.insert(heroListData, hero)
            end
        end
    end
    return heroListData
end
function SelectHeroWindow:GettHerodata()
    return self:GetFilteredHero(self.quality,self.campIndex)
end

--获取未被选中的英雄
function SelectHeroWindow:GetUnselectHeroList()
    local totalHeroList = self:GetUnselectHerodata()
    local useHeroList = {}
    for k,v in ipairs(totalHeroList) do
        if v then
            if not v.teamIndex or v.teamIndex == 0 then
                table.insert(useHeroList, v)
            end
        end
    end
    return useHeroList
end

--[[英雄列表里英雄的数据去掉被选中的英雄不显示]]
function SelectHeroWindow:GetUnselectHerodata()
    local tempData = self:GetFilteredHero(self.quality,self.campIndex)
    local heroListData = {}
    banSelectTips = true
    local allLineUpHero = GetAllLineUpHeroDic()
    if tempData ~= nil then
        -- print(396,"英雄列表里英雄的数据去掉被选中的英雄不显示 >>>>>>>",util.get_len(tempData))
        for sid, hero in pairs(tempData) do
            if banSelectTips and hero.sameUp == 0 then
                banSelectTips = false
            end
            if allLineUpHero[hero.heroSid] then
                hero.teamIndex = allLineUpHero[hero.heroSid]
            end
            table.insert(heroListData, hero)
            --[[
            local isGet = allLineUpHero[hero.heroSid]-- GetAllHeroSlot(hero)
            if isGet ~= nil then
                if CanChangeTeam then
                    table.insert(heroListData, hero)
                end
                --TODO：如果该英雄是在其他队伍中的可以显示出来
                if forMaze and isMulti then
                    if GetCacheData() and GetCacheData()[hero.heroSid] then
                        local teamIndex = GetCacheData() and GetCacheData()[hero.heroSid] and GetCacheData()[hero.heroSid].teamIndex or 0
                        -- --print("curEnemyIndex",curEnemyIndex,"hero.heroSid",hero.heroSid,"sid",sid,"teamIndex",teamIndex)
                        if (teamIndex > 0 and teamIndex ~= curEnemyIndex+1) then
                            -- --print("curEnemyIndex",curEnemyIndex,"hero.heroSid",hero.heroSid,"sid",sid,"teamIndex",teamIndex)
                            table.insert(heroListData, hero)
                        end
                    end 
                    if curEnemyIndex == HeroLineUpEnum.LineUp1 then--目前在1队,2队/3队有该英雄
                        if GetHeroSlot2(hero) or GetHeroSlot3(hero) then
                            -- --print("目前在1队,2队/3队有该英雄curEnemyIndex",curEnemyIndex,"hero.heroSid",hero.heroSid,"sid",sid)
                            table.insert(heroListData, hero)
                        end
                    elseif curEnemyIndex == HeroLineUpEnum.LineUp2 then--目前在2队,1队/3队有该英雄
                        if GetHeroSlot1(hero) or GetHeroSlot3(hero) then
                            -- --print("目前在2队,1队/3队有该英雄curEnemyIndex",curEnemyIndex,"hero.heroSid",hero.heroSid,"sid",sid)
                            table.insert(heroListData, hero)
                        end
                    elseif curEnemyIndex == HeroLineUpEnum.LineUp3 then--目前在3队,1队/2队有该英雄
                        if GetHeroSlot1(hero) or GetHeroSlot2(hero) then
                            -- --print("目前在3队,1队/2队有该英雄curEnemyIndex",curEnemyIndex,"hero.heroSid",hero.heroSid,"sid",sid)
                            table.insert(heroListData, hero)
                        end
                    end
                end
            else
                -- --print("isGet",isGet,"curEnemyIndex",curEnemyIndex,"hero.heroSid",hero.heroSid,"sid",sid)
                table.insert(heroListData, hero)
            end
            ]]
            --table.insert(heroListData, hero)
        end
    end
    return heroListData
end

function SelectHeroWindow:FilterHero(heroPart, quality, teamIndex, hasTeamIndex, activeHero, deadHero,campIndex)
    local sid,hero
    for sid, hero in pairs(heroPart) do
        local up = IsSelected(hero)
        --local isTrial = hero_trial_mgr.isTrialHeroBySid(sid)
        --local isTrialBattle = hero_trial_mgr.IsCanTrialHeroByBattleType(battleType)
        --local showTrialCondition = isTrialBattle and isTrial or (not isTrial)
        local heroCfg = GetHeroInCache(hero.heroID) --game_scheme:Hero_0(hero.heroID)
        local typeV = heroCfg and heroCfg.type
        local profession = heroCfg and heroCfg.profession
        local typeLimit = ( not quality) or (quality == 0 or typeV == quality)
        local campLimit = ((not campIndex) or (campIndex==0 or profession == campIndex))
        if (up or (typeLimit and campLimit)) and (not IsInFilterHeroList(sid)) then
            --if showTrialCondition then--试用英雄可以试用的战斗类型
                hero.isUp = up and 1 or 0
                hero.isTrial = 0--时空裂缝，英雄剧情副本不能使用试用英雄，默认设置为0
                hero.sameUp = IsHeroIDSelected(hero)
                hero.type = heroCfg.type
                hero.teamIndex = teamIndex
                hero.hasTeamIndex = hasTeamIndex
                local cacheData = GetCacheData()
                local hp = cacheData and cacheData[hero.heroSid] and cacheData[hero.heroSid].numProp.hp
                if hp and hp == 0 then
                    table.insert(deadHero, hero)
                else
                    table.insert(activeHero, hero)
                end
            --end
        end
    end
end

function SelectHeroWindow:BuildOptimizeHero( hero , skipValueSet )
    local result = optimizeHeroCache[hero.heroSid]
    if result then
        if not skipValueSet then
            result.hero = hero
            result.isUp = hero.isUp
            result.sameUp = hero.sameUp
            --result.numProp.lv = hero.numProp.lv
            --result.numProp.starLv = hero.numProp.starLv
            result.hasTeamIndex = hero.hasTeamIndex
            result.teamIndex = hero.teamIndex
            --result.rarityType = hero.rarityType
            --result.battleProp.power = hero.battleProp.power
            result.type = hero.type
            --result.heroID = hero.heroID
            --result.heroSid = hero.heroSid
            result.isTrial = hero.isTrial
            result.hasFinish = hero.hasFinish
        end
    else
        -- 构建新 table 以避开 hero 空表自带的 metatable 设置，以提高排序性能
        result = 
        {
            hero = hero,
            isUp = hero.isUp,
            sameUp = hero.sameUp,
            numProp = {
                lv = hero.numProp.lv,
                starLv = hero.numProp.starLv
            },
            hasTeamIndex = hero.hasTeamIndex,
            teamIndex = hero.teamIndex,
            rarityType = hero.rarityType,
            battleProp =
            {
                power = hero.battleProp.power
            },
            type = hero.type,
            heroID = hero.heroID,
            heroSid = hero.heroSid,
            isTrial = hero.isTrial,
            hasFinish = hero.hasFinish
        }
        optimizeHeroCache[hero.heroSid] = result 
    end
    return result
end

function SelectHeroWindow:GetSelectedHeroSid()
    local selectedHeroSidSet = {}
    local selectedHeros = self:GetSelectedHero()
    if selectedHeros then
        local k,v
        for k,v in pairs(selectedHeros) do
            if v and v.heroSid then
                selectedHeroSidSet[v.heroSid] = true
            end
        end
    end
    return selectedHeroSidSet
end

function SelectHeroWindow:GetSelectedHeroHandBookID()
    local selectedHeroHandBookID = {}
    local j,k
    for j,k in pairs(TabData) do
        if k and k.selectedHero then
            for _,v in pairs(k.selectedHero) do
                if v then
                    local cfg_select = game_scheme:Hero_0(v.heroID)
                    if cfg_select then
                        selectedHeroHandBookID[v.heroID] = cfg_select.HerohandBookID
                        selectedHeroHandBookID[cfg_select.HerohandBookID] = cfg_select.HerohandBookID--保证同个英雄不同HerohandBookID都设置成已选择状态
                    end
                end
            end
        end
    end
    return selectedHeroHandBookID
end

--获取已选择的英雄中是否含有试用英雄
function SelectHeroWindow:isHasTrialSelectedHero()
    local curSelectedHeros = GetCurData().selectedHero
    if curSelectedHeros then
        for k,v in pairs(curSelectedHeros) do
            if v.isTrial == 1 then
                return true
            end
        end
    end
    return false
end
-- function SelectHeroWindow:isHasTrialSelectedHero(index)
--     --获取当前上阵队伍中是否有试用英雄
--     if TabData[index] and TabData[index].selectedHero then
--         for p, h in pairs(TabData[index].selectedHero) do
--             if h ~= nil and h.isTrial == 1 then
--                 return p
--             end
--         end
--     end
--     return nil
--     -- GetTarHeroSlot()
-- end

--[[英雄数据获取函数
    quality 代表英雄种类 
]]
function SelectHeroWindow:GetFilteredHero(quality,campIndex)
    --log.Error("GetFilteredHero")
    -- local startTime = util.UTC0Seconds()

    -- --print("GetFilteredHero quality:"..tostring(quality),"campIndex",campIndex)
    local t = {}
    local dead = {}
    -- 增加 validHero 表，以避开带 metatable 的包含 hero_entity 的表格进行排序，以提高性能
    local validHero = {}
    local deadHero = {}

    local heroPart = {}
    if canSelectHeros then
        heroPart = canSelectHeros
    else
        local ownHeroList = gw_hero_data.GetOwnedHeroIDList()
        for i,v in ipairs(ownHeroList) do
            local heroData = gw_hero_data.GetOldHeroEntityByHeroId(v)
            if heroData then
                heroPart[v] = heroData
            end
        end
    end --canSelectHeros or player_mgr.GetPalPartData() or {}

    -- print(396,"可以选择英雄 >>>> ",util.get_len(canSelectHeros),"sumHero >>>> "..util.get_len(player_mgr.GetPalPartData()),"quality=",quality,"forMaze=",forMaze,"isPeak=",isPeak)
    if forSpaceGap or forPlot then
        -- 时空裂缝
        -- 英雄剧情副本
        if forSpaceGap then
            heroPart = spaceGapHeros
        elseif forPlot then
            heroPart = plotHeros
        end
        self:FilterHero(heroPart, quality, nil, false, t, dead,campIndex)
        return t
    end

    local selectedHeroSid = self:GetSelectedHeroSid()
    local selectedHeroHandBookID = self:GetSelectedHeroHandBookID()
    local cacheData = GetCacheData()
    for sid, hero in pairs(heroPart) do
        if getHeroFunc then
            hero = getHeroFunc(hero.heroSid)
        end
        local heroID = hero.heroID
        local up = selectedHeroSid[sid] --IsSelected(hero)
        local heroCfg = GetHeroInCache(heroID)
        if heroCfg then
            local typeV = heroCfg.type
            local profession = heroCfg.profession
            local typeLimit = ( not quality) or (quality == 0 or typeV == quality)
            local campLimit = ((not campIndex) or (campIndex==0 or profession == campIndex))
            local condition = (isLimitHeroStar and lowStarLvLimit) and hero.numProp and hero.numProp.starLv and hero.numProp.starLv>=lowStarLvLimit or (not isLimitHeroStar)
            local isTrial = hero_trial_mgr.isTrialHeroBySid(sid)
            local isTrialBattle = isCanUseTrial and hero_trial_mgr.IsCanTrialHeroByBattleType(battleType)
            local showTrialCondition = isTrialBattle and isTrial or (not isTrial)
            -- print("qsy_yxsy:[ui_select_hero]GetFilteredHero>>>showTrialCondition = ",showTrialCondition,isTrialBattle,isTrial,sid,battleType)
            if condition and ((typeLimit and campLimit)) and (not IsInFilterHeroList(sid)) then
                if showTrialCondition then--试用英雄可以试用的战斗类型
                    local cachedHero = cacheData and cacheData[sid]
                    local hp = cachedHero and cachedHero.numProp.hp
                    if hp and hp == 0 then
                        --性能优化项，如果每次都给hero设置新key，消耗量较大
                        hero.isUp = up and 1 or 0
                        hero.isTrial = isTrial and 1 or 0
                        hero.sameUp = selectedHeroHandBookID[heroCfg.HerohandBookID] and 1 or 0 -- IsHeroIDSelected(hero)
                        hero.type = heroCfg.type
                        hero.teamIndex = nil
                        hero.hasTeamIndex = false
                        hero.heroCfg = heroCfg
                        table.insert(dead, hero)
                    else
                        if forMaze and not isPeak then
                            -- local mazeHeroData = maze_mgr.GetHeroDataBySid(sid)
                            -- if sid and mazeHeroData then
                            --     local teamIndex = mazeHeroData.teamIndex
                            --     local otmHero = self:BuildOptimizeHero(hero)
                            --     --性能优化项，如果每次都给hero设置新key，消耗量较大
                            --     otmHero.isUp = up and 1 or 0
                            --     otmHero.isTrial = isTrial and 1 or 0
                            --     otmHero.sameUp = selectedHeroHandBookID[heroCfg.HerohandBookID] and 1 or 0 -- IsHeroIDSelected(hero)
                            --     otmHero.type = heroCfg.type
                            --     otmHero.teamIndex = nil
                            --     otmHero.hasTeamIndex = false
                            --     otmHero.teamIndex = teamIndex
                            --     otmHero.hasTeamIndex = otmHero.teamIndex and otmHero.teamIndex > 0
                            --     table.insert(validHero, otmHero)
                            -- end
                        else
                            local otmHero = self:BuildOptimizeHero(hero)
                            if CanChangeTeam then
                                local _teamIndex =GetSameUpTeamIndex(hero) or 0
                                otmHero.teamIndex = _teamIndex
                                otmHero.hasTeamIndex = _teamIndex and _teamIndex > 0
                            end
                            --性能优化项，如果每次都给hero设置新key，消耗量较大
                            otmHero.isUp = up and 1 or 0
                            otmHero.isTrial = isTrial and 1 or 0
                            otmHero.sameUp = selectedHeroHandBookID[heroCfg.HerohandBookID] and 1 or 0 -- IsHeroIDSelected(hero)
                            otmHero.type = heroCfg.type
                            otmHero.teamIndex = nil
                            otmHero.hasTeamIndex = false
                            otmHero.heroCfg = heroCfg
                            table.insert(validHero, otmHero )
                        end
                    end
                end
            end
        end
    end
    -- log.Error("ui select hero cost time insert:", util.UTC0Seconds() - startTime, util.get_len(validHero))
    -- local startTime = util.UTC0Seconds()

    if forMaze then
        heroPart = GetPalPartData() or {}
        for sid, hero in pairs(heroPart) do
            local up = selectedHeroSid[hero.heroSid] --IsSelected(hero)
            local isTrial = hero_trial_mgr.isTrialHeroBySid(hero.heroSid)
            local isTrialBattle = hero_trial_mgr.IsCanTrialHeroByBattleType(battleType)
            local showTrialCondition = isTrialBattle and isTrial or (not isTrial)
            local heroCfg = GetHeroInCache(hero.heroID)-- game_scheme:Hero_0(hero.heroID)
            if heroCfg then
                local typeV = heroCfg.type
                hero.isUp = up and 1 or 0
                hero.isTrial = isTrial and 1 or 0
                hero.sameUp = IsHeroIDSelected(hero)
                hero.type = heroCfg.type
                hero.teamIndex = nil
                hero.hasTeamIndex = false
                hero.rarityType = heroCfg.rarityType
                hero.heroCfg = heroCfg
                local profession = heroCfg and heroCfg.profession
                local typeLimit = ( not quality) or (quality == 0 or typeV == quality)
                local campLimit = ((not campIndex) or (campIndex==0 or profession == campIndex))
                if (up or (typeLimit and campLimit)) and (not IsInFilterHeroList(sid)) then
                    if showTrialCondition then--试用英雄可以试用的战斗类型
                        local hp = GetCacheData() and GetCacheData()[hero.heroSid] and GetCacheData()[hero.heroSid].numProp.hp
                        if hp and hp == 0 then
                            table.insert(dead, hero)
                        else
                            --if GetCacheData() and GetCacheData()[hero.heroSid] then
                            local teamIndex = GetCacheData() and GetCacheData()[hero.heroSid] and GetCacheData()[hero.heroSid].teamIndex or 0
                            -- --print("curEnemyIndex",curEnemyIndex,"hero.heroSid",hero.heroSid,"teamIndex",teamIndex)
                            --if not teamIndex or (teamIndex == 0 or teamIndex == curEnemyIndex+1) then
                            -- print(396,"雇佣兵 >>>>>>> ",lang.Get(heroCfg.nameID),"heroSid",hero.heroSid,"mazeSId=",maze_mgr.GetHeroDataBySid(hero.heroSid))
                            if hero.heroSid and isPeak then
                                hero.teamIndex = teamIndex
                                hero.hasTeamIndex = teamIndex and teamIndex > 0
                                -- table.insert(t, hero)
                                table.insert(validHero, self:BuildOptimizeHero(hero))
                            end
                            --end
                            --end
                        end
                    end
                end
            end
        end
    end

    -- log.Error("ui select hero cost time insert formaze:", util.UTC0Seconds() - startTime)
    -- local startTime = util.UTC0Seconds()

    --[[英雄排序星级优先,同星级等级优先]]
    --[[
    local SortRule = function (t1, t2)
        local upG = t1.isUp - t2.isUp
        local lvG = t1.numProp["lv"] - t2.numProp["lv"]
        local starLvG = t1.numProp["starLv"] - t2.numProp["starLv"]
        local heroID = t1.heroID - t2.heroID

        if (upG ~= 0) then
            return upG > 0
        else
            if (starLvG ~= 0) then
                return (starLvG>0)
            else
                if (lvG ~= 0) then
                    return (lvG>0)
                else
                    if (heroID ~= 0) then
                        return (heroID>0)
                    end
                end
            end
        end
        return false
    end
    ]]--

    --4-1关卡前排序规则调整为 星级（品阶）→ 等级，4-1后还按 等级 → 星级（品阶）规则
    local passLevel = laymain_data.GetPassLevel()
    local firstSortType = "lv"
    local secondSortType = "starLv"

    local cfg = game_scheme:InitBattleProp_0(399) --读取调整排序关卡
	local changeSortLevel = 106
	if cfg then 
		changeSortLevel = cfg.szParam.data[1]
	end
    if passLevel < changeSortLevel then --106关卡id为4-1
        firstSortType = "starLv"
        secondSortType = "lv"
    end

    -- log.Error("ui select hero cost time 1:", util.UTC0Seconds() - startTime)

    -- local statistic = 0
    --2020.4.17 赖嘉明 新的排序规则为：锁定 → 等级 → 星级（品阶）→ 战力 → 阵营→ID→SID
    local SortRule = function (t1, t2)
        -- statistic = statistic + 1
        
        local numProp1,numProp2 = t1.numProp,t2.numProp
       --[[ local hasTeamIndex1 = (t1.teamIndex and t1.teamIndex>0) and 1 or 0
        local hasTeamIndex2 = (t2.teamIndex and t2.teamIndex>0) and 1 or 0]]

        --[[local isUp1 = GetHeroSlotById(t1.heroID) ~= nil
        local isUp2 = GetHeroSlotById(t2.heroID) ~= nil

        --阵营试炼判断是否已上阵到别的阵营

        if isUp1 ~= isUp2 then
            return  isUp1
        end]]

        local canUp1 = camp_trial_data.GetCanUpBySid(ui_select_model_node.GetBattleType(), t1.heroSid)
        local canUp2 = camp_trial_data.GetCanUpBySid(ui_select_model_node.GetBattleType(), t2.heroSid)
        if canUp1 ~= canUp2 then
            return canUp1
        end

        local hero1 = game_scheme:Hero_0(t1.heroID)
        local hero2 = game_scheme:Hero_0(t2.heroID)
        if hero1.rarityType ~= hero2.rarityType then
            return hero1.rarityType > hero2.rarityType
        end
        
        if t1.isTrial ~= t2.isTrial then
            return t1.isTrial > t2.isTrial
        end
        --[[if t1.sameUp ~= t2.sameUp then
            return t1.sameUp < t2.sameUp
        end]]
       --[[ if hasTeamIndex1 ~= hasTeamIndex2 then
            return hasTeamIndex1 < hasTeamIndex2
        end]]
        if numProp1[firstSortType] ~= numProp2[firstSortType] then
            return numProp1[firstSortType] > numProp2[firstSortType]
        end
        if numProp1[secondSortType] ~= numProp2[secondSortType] then
            return numProp1[secondSortType] > numProp2[secondSortType]
        end
        if t1.rarityType and t2.rarityType and t1.rarityType ~= t2.rarityType then
            return t1.rarityType > t2.rarityType
        end
        local battleProp1,battleProp2 = t1.battleProp,t2.battleProp
        if battleProp1["power"] ~= battleProp2["power"] then
            return battleProp1["power"] > battleProp2["power"]
        end
        local type1 = GetHeroTypeSortOrder(t1.type)
        local type2 = GetHeroTypeSortOrder(t2.type)
        if type1 ~= type2 then
            return type1 > type2
        end
        if t1.heroID ~= t2.heroID then
            return t1.heroID < t2.heroID
        end
        return t1.heroSid < t2.heroSid
    end

    local function SortRuleNew(a, b)
        local canUp1 = camp_trial_data.GetCanUpBySid(ui_select_model_node.GetBattleType(), a.heroSid)
        local canUp2 = camp_trial_data.GetCanUpBySid(ui_select_model_node.GetBattleType(), b.heroSid)
        if canUp1 ~= canUp2 then
            return canUp1
        end
        if a.rarityType ~= b.rarityType then
            return a.rarityType > b.rarityType
        end

        local power1 = gw_power_mgr.GetHeroPowerByCfgId(a.heroID)
        local power2 = gw_power_mgr.GetHeroPowerByCfgId(b.heroID)
        if power1 ~= power2 then
            return power1 > power2
        end

        local debrisNum1 = player_mgr.GetPlayerOwnNum(a.heroCfg.CompositeID)
        local debrisNum2 = player_mgr.GetPlayerOwnNum(b.heroCfg.CompositeID)
        if debrisNum1 ~= debrisNum2 then
            return debrisNum1 > debrisNum2
        end
        return a.heroID < b.heroID
    end

    table.sort(validHero, SortRuleNew)
    table.sort(dead, SortRuleNew)
    t = {}
    local i,v
    for i,v in ipairs(validHero) do
        table.insert(t, v)
    end

    for k,v in ipairs(dead) do
        table.insert(t, v)
    end
    return t
end

function baseSortRule(t1, t2)
    -- statistic = statistic + 1

    local numProp1,numProp2 = t1.numProp,t2.numProp
    local hasTeamIndex1 = (t1.teamIndex and t1.teamIndex>0) and 1 or 0
    local hasTeamIndex2 = (t2.teamIndex and t2.teamIndex>0) and 1 or 0

   --[[ local isUp1 = GetHeroSlotById(t1.heroID) ~= nil
    local isUp2 = GetHeroSlotById(t2.heroID) ~= nil

    --阵营试炼判断是否已上阵到别的阵营

    if isUp1 ~= isUp2 then
        return not isUp1
    end]]

    local canUp1 = camp_trial_data.GetCanUpBySid(ui_select_model_node.GetBattleType(), t1.heroSid)
    local canUp2 = camp_trial_data.GetCanUpBySid(ui_select_model_node.GetBattleType(), t2.heroSid)
    if canUp1 ~= canUp2 then
        return canUp1
    end

    local hero1 = game_scheme:Hero_0(t1.heroID)
    local hero2 = game_scheme:Hero_0(t2.heroID)
    if hero1.rarityType ~= hero2.rarityType then
        return hero1.rarityType > hero2.rarityType
    end

    if t1.isTrial ~= t2.isTrial then
        return t1.isTrial > t2.isTrial
    end
    if t1.sameUp ~= t2.sameUp then
        return t1.sameUp < t2.sameUp
    end
    if hasTeamIndex1 ~= hasTeamIndex2 then
        return hasTeamIndex1 < hasTeamIndex2
    end
    if numProp1[firstSortType] ~= numProp2[firstSortType] then
        return numProp1[firstSortType] > numProp2[firstSortType]
    end
    if numProp1[secondSortType] ~= numProp2[secondSortType] then
        return numProp1[secondSortType] > numProp2[secondSortType]
    end
    if t1.rarityType and t2.rarityType and t1.rarityType ~= t2.rarityType then
        return t1.rarityType > t2.rarityType
    end
    local battleProp1,battleProp2 = t1.battleProp,t2.battleProp
    if battleProp1["power"] ~= battleProp2["power"] then
        return battleProp1["power"] > battleProp2["power"]
    end
    local type1 = GetHeroTypeSortOrder(t1.type)
    local type2 = GetHeroTypeSortOrder(t2.type)
    if type1 ~= type2 then
        return type1 > type2
    end
    if t1.heroID ~= t2.heroID then
        return t1.heroID < t2.heroID
    end
    return t1.heroSid < t2.heroSid
end

local heroTypeSortOrder =
{
    [6] = 6,
    [5] = 5,
    [4] = 1,
    [3] = 2,
    [2] = 3,
    [1] = 4,
}

function GetHeroTypeSortOrder(v)
    return heroTypeSortOrder[v]
end

--刷新上阵英雄，
--stopUpdatePower：避免初始化时反复计算
function SelectHeroWindow:PerformSelect(hero, slot , skipUpdatePower)
    --log.Error("PerformSelect----------->", slot , "    " , hero.heroID)

    if not slot then
        slot = -1
    end
    if not hero or slot < 0 then
        return
    end
    if self.slot == nil then
        return
    end
    local cacheData = GetCacheData()
    local heroData = cacheData and cacheData[hero.heroSid]
    local hp = heroData and heroData.numProp.hp
    -- cacheData 和 hp 可能为空，但不会退出，继续往下执行
    if hp and hp <= 0 then
        --flow_text.Add(lang.Get(73212))
        return
    end
    if force_guide_system.GetCurStep() then
        -- log.Error("ExeStep   PerformSelect>>>>>>>>>>>>>"..force_guide_system.GetCurStep())
        if force_guide_system.GetCurStep() == 313 then
            ui_drag_object.InitDrag(true, self.rtRoot, self.rawBg)
        end
    end

    self:PerformDeselect(slot,hero , skipUpdatePower)
    -- local node = self["node" .. slot]
    -- if node and node.transform and node.transform.childCount >0 then
    -- end
    local delayTime = 0.04 * slot
    if not skipUpdatePower then
        UpdatePower()
    end
   
    --生成图标框
    if not self.slot[slot] or not hero then
        return
    end

    local tmpTeam = curEnemyIndex
    local minfo, initModelFunc = self:LoadHeroModelInfo(slot, false, hero, self.heroNode[slot].transform, function (go)
        if tmpTeam ~= curEnemyIndex then
            if minfo then
                minfo:Dispose()
            end
            return
        end
        if self:IsValid() then
            --if self.heroMySNodes and self.heroMySNodes[slot] and not util.IsObjNull(self.heroMySNodes[slot]) then
            go.gameObject.name = tostring(slot)
            local heroCfg = game_scheme:Hero_0(hero.heroID)
            if heroCfg then
                self.heroMySNodes[slot]:Switch(heroCfg.rarityType)
            end
            --    local starLv = hero and hero.numProp and hero.numProp.starLv or 5
            --    self.heroMySNodes[slot]:Switch(self:SetModelBg(starLv))
            --    self.heroMySNodes[slot].gameObject:SetActive(true)
            ui_select_model_node.GetMyTextNodes()[slot].gameObject:SetActive(false)
            self.heroObjs[slot] = go
            --    --local v3 = ui_new_select_model_node.GetHeroSelectCamera():WorldToScreenPoint(go:GetComponent(typeof(Card)):GetAnchorPoint())
            --    local v3 = go:GetComponent(typeof(Card)):GetAnchorPoint()
            --    v3.y = v3.y + ui_new_select_model_node.GetHeadOffset()
            --    self.slot[slot].transform.position = v3
            --    self.slot[slot].transform.localPosition = Vector3(self.slot[slot].transform.localPosition.x,self.slot[slot].transform.localPosition.y+40,self.slot[slot].transform.localPosition.z)
            --end
        end
        self.heroCount=self.heroCount-1
        if self.heroCount==0 then
    		ui_drag_object.InitDrag(true, self.rtRoot, self.rawBg)
        end
    end)
    self.heroModels[slot]  = minfo
    self.heroModelsFunc[slot]  = initModelFunc
    if delayTime==nil then
        if self.heroModelsFunc[slot] then
            self.heroModelsFunc[slot]()
            self.heroModelsFunc[slot] = nil
        end
    else
        if timerDic["InitHeroItemChuZhanTimer"..slot] then
            util.RemoveDelayCall(timerDic["InitHeroItemChuZhanTimer"..slot])
            timerDic["InitHeroItemChuZhanTimer"..slot]=nil;
        end
        
        timerDic["InitHeroItemChuZhanTimer"..slot]= util.DelayCallOnce(delayTime,function()
            if self and self:IsValid() then
                if tmpTeam ~= curEnemyIndex then
                    return                    
                end
                if self.heroModelsFunc[slot] then
                    self.heroModelsFunc[slot]()
                    self.heroModelsFunc[slot] = nil
                end
            end
        end)
    end
    if self.heroCount==nil then
        self.heroCount=0
    end
    self.heroCount=self.heroCount+1
    
    JudgeSelectHeroStand(hero,slot)
    JudgeAssignHeroStand(hero,slot)
    self:DelayPreLoadBattleLayoutResource()
    if curEnemyIndex == 1 and not battle_data.skipBattle then
        --缓存上阵英雄
        local battle_preview_manager = require "battle_preview_manager"
        battle_preview_manager.DelayPreLoadSelectHeroResource(GetSelectedHero(), 0.1)
    end
    self.nextButtonWarn.gameObject:SetActive(not IsHasSelectHero())

    SetCurFactionNum()
    RefreshMultiSecretUI(true,hero,slot)
    -- if FleetID then
    --     local fleet_expedition_data = require "fleet_expedition_data"
    --     local isFit = fleet_expedition_data.IsAllHeroFitStar2(FleetID,GetSelectedHero())
    --     self.starCondition[2].Img:SetEnable(not isFit)
    --     self.starCondition[2].Txt.color = not isFit and notFitColor or FitColor
    -- end
end

function SelectHeroWindow:InitHeroItemChuZhan(slot, hero)
    if self.slot == nil then
        return
    end
    local cacheData = GetCacheData()
    local heroData = cacheData and cacheData[hero.heroSid]
    local hp = heroData and heroData.numProp.hp
    local hpMax = heroData and heroData.numProp.hpMax
    local mp = heroData and heroData.numProp.mp
    local mpMax = heroData and heroData.numProp.mpMax
    local screenSize = screen_util.width / screen_util.height;
    local baseOffset = 0.4615 - screenSize
    local offset = 0
    local flag = util.Flag_ExternScreen()
    if flag then
        offset = -15
    end
    local profile = up_profile[slot]
    if not profile then
        profile = hero_item_chuzhan.CHeroItem():Init(self.slot[slot],1, function (obj)
            self["myHaloEffect_" .. slot] = obj.transform:Find("info/effect")
            --up_profile[slot]:ShowHpMp(GetCurData().showHp,(hp or 100) / (hpMax or 100),(mp or 0) / (mpMax or 100),false)
            --up_profile[slot].transform:SetAsFirstSibling()
            obj.transform:SetAsFirstSibling()
            local worldPos = obj.transform.position
            local viewportPos = ui_select_model_node.GetHeroSelectCamera():WorldToViewportPoint(Vector3(worldPos.x,worldPos.y,worldPos.z))
            local rawImageUV = Vector2(viewportPos.x,viewportPos.y)
            Common_Util.SetLocalPos(self["heroUpgradeBtn_"..(slot + 1)],self.rawBg.rectTransform.rect.width * (rawImageUV.x - 0.5) + 75 + baseOffset * 50 ,self.rawBg.rectTransform.rect.height * (rawImageUV.y-0.5) - 202 - baseOffset * 240 - offset)-- - offset)
        end,nil)
        profile:HideJob(true)
        profile:HideStar(true)
        profile:ShowHpMp(GetCurData().showHp,(hp or 100) / (hpMax or 100),(mp or 0) / (mpMax or 100),false)
        --profile.transform:SetAsFirstSibling()
    end
    profile:SetHero(hero,self.OnClickHero,true)
    up_profile[slot] = profile
    local result = gw_hero_mgr.GetHeroStarRed(hero.heroSid)
    local result_1 = gw_hero_mgr.GetHeroLevelBtnRed(hero.heroSid)
    Common_Util.SetActive(self["heroUpgradeBtn_"..(slot + 1)],result + result_1 > 0)
    self["heroUpgradeBtn_"..(slot + 1)].onClick:RemoveAllListeners()
    local gw_const = require "gw_const"
    if result_1 > 0 then
        self["heroUpgradeBtn_"..(slot + 1)].onClick:AddListener(function()
            windowMgr:ShowModule("ui_gwhero_base", nil, nil, {
                heroSid = hero.heroSid,
                index = gw_const.HeroUIType.Level,
            })
        end)
    elseif result > 0 then
        self["heroUpgradeBtn_"..(slot + 1)].onClick:AddListener(function()
            windowMgr:ShowModule("ui_gwhero_base", nil, nil, {
                heroSid = hero.heroSid,
                index = gw_const.HeroUIType.Star,
            })
        end)
    end
end

function SelectHeroWindow:UpdateUpgradeBtn()
    local heroLine = GetSelectedHero()
    for i=0,4 do
        if heroLine and heroLine[i] then
            local hero = heroLine[i]
            local result = gw_hero_mgr.GetHeroStarRed(hero.heroSid)
            local result_1 = gw_hero_mgr.GetHeroLevelBtnRed(hero.heroSid)
            local profile = up_profile[i]
            if profile then
                local gw_hero_data = require "gw_hero_data"
                profile:DisplayAll(gw_hero_data.GetHeroCfgId2Entity(hero.heroID),hero.heroID)
            end
            Common_Util.SetActive(self["heroUpgradeBtn_"..(i + 1)],result + result_1 > 0)
            self["heroUpgradeBtn_"..(i + 1)].onClick:RemoveAllListeners()
            local gw_const = require "gw_const"
            if result_1 > 0 then
                self["heroUpgradeBtn_"..(i + 1)].onClick:AddListener(function()
                    windowMgr:ShowModule("ui_gwhero_base", nil, nil, {
                        heroSid = hero.heroSid,
                        index = gw_const.HeroUIType.Level,
                    })
                end)
            elseif result > 0 then
                self["heroUpgradeBtn_"..(i + 1)].onClick:AddListener(function()
                    windowMgr:ShowModule("ui_gwhero_base", nil, nil, {
                        heroSid = hero.heroSid,
                        index = gw_const.HeroUIType.Star,
                    })
                end)
            end
        else
            Common_Util.SetActive(self["heroUpgradeBtn_"..(i + 1)],false)
        end
    end
    UpdatePower()
end

function IsHasSelectHero()
    local isHas = false
    if GetSelectedHero() then
        for i,v in pairs(GetSelectedHero()) do
            isHas = true
        end
    end
    return isHas
end

function SelectHeroWindow:PreLoadBattleLayoutResource()
    --缓存战斗布局资源
    local battle_player = require "battle_player"
    local player = battle_player.GetBattlePlayer()
    if not player or not player.layoutDirectory.GetValidBattleLayout then
        return
    end
    local layout = {}
    local i
    --布阵缓存，新版战斗是把3号主角和12号敌人挖掉了，因此暂时如此处理。
    for i=1,6 do
        if i <= 2 then
            layout[i] = not not GetSelectedHero()[i - 1]
        elseif i == 3 then
            layout[i] = false
        else
            layout[i] = not not GetSelectedHero()[i]
        end
    end
    if GetCurData() and GetCurData().enemyHeroData then
        for i=1,5 do
            layout[i + 6] = not not GetCurData().enemyHeroData[i - 1]
        end
        layout[12] = false
    else
        for i=1,6 do
            layout[i + 6] = false
        end
    end

    local layoutAssetPath = {}
    local layoutPathList = player.layoutDirectory:GetValidBattleLayout(layout, player.random)
    if layoutPathList and layoutPathList.Count ~= 0 then
        local directoryPath = player.layoutDirectory.directory.."/"
        local assetPath
        for i=0,layoutPathList.Count - 1 do
            assetPath = directoryPath..layoutPathList[i]..".prefab"
            layoutAssetPath[assetPath] = 1
        end
    end
    if layoutPathList.Count > 0 and not battle_data.skipBattle then
        local battle_preview_manager = require "battle_preview_manager"
        --ui_select_hero已实现延时操作，不再使用 battle_preview_manager.DelayPreLoadBattleLayoutResource 接口，直接加载
        battle_preview_manager.PreLoadBattleLayoutResource(layoutAssetPath)
    end
end

function SelectHeroWindow:DelayPreLoadBattleLayoutResource()
    util.DelayOneCall("DelayPreLoadBattleLayoutResource", function ()
        if util.get_len(GetCurData().enemyHeroData) <= 0 or util.get_len(GetSelectedHero()) <= 0 then
            return
        end
        if not window or not window:IsValid() then
            return
        end
        window:PreLoadBattleLayoutResource()
    end, 0.1)
end

function SelectHeroWindow:PerformDeselect(slot, hero,skipUpdatePower)
    -- log.Error("PerformDeselect--->".. slot)
    --拖走后删除原框内item
    CheckIsExistAssignHero(hero)
    if not self.heroModels or not self.heroModels[slot] then
        return
    end
    if self.heroMySNodes[slot] then
        self.heroMySNodes[slot]:Switch(0)
    end
    if self.heroModels[slot] then
        self.heroModels[slot]:Dispose();
        self.heroModels[slot] = nil
        self.heroObjs[slot] = nil
    end
    if self.heroModelsFunc[slot] then
        self.heroModelsFunc[slot] = nil
    end
    self["myHaloEffect_" .. slot] = nil
    -- log.Error("PerformDeselect return false--->".. slot)
    if up_profile[slot] then
        up_profile[slot]:Dispose()
        up_profile[slot] = nil
    end
    Common_Util.SetActive(self["heroUpgradeBtn_"..(slot + 1)],false)
    self:PerformDeselectArtifact(slot)
    -- if GetSlotState() and GetSlotState()[slot] then
    --     if self.lockedBtns[slot] then
    --         self.lockedBtns[slot].gameObject:SetActive(false)
    --     end
    -- else
    --     self.lockedBtns[slot].gameObject:SetActive(false)
    -- end

    --self:UnloadNode(slot)
    if not skipUpdatePower then
        UpdatePower()
    end
    --self.heroMySNodes[slot].gameObject:SetActive(true)

    self:DelayPreLoadBattleLayoutResource()
    --self.heroMySNodes[slot]:Switch(self:SetModelBg(-1))
    ui_select_model_node.GetMyTextNodes()[slot].gameObject:SetActive(true)
    self.nextButtonWarn.gameObject:SetActive(not IsHasSelectHero())
    SetCurFactionNum()
    RefreshMultiSecretUI(false,nil,slot)
    -- if FleetID then
    --     local fleet_expedition_data = require "fleet_expedition_data"
    --     local isFit = fleet_expedition_data.IsAllHeroFitStar2(FleetID,GetSelectedHero())
    --     self.starCondition[2].Img:SetEnable(not isFit)
    --     self.starCondition[2].Txt.color = not isFit and notFitColor or FitColor
    -- end
    event.Trigger(event.SAVE_SQUAD);
end

function SelectHeroWindow:PerformExchange(slot,tSlot)
    --交换框内item
    local s = GetSelectedHero()[slot]
    local t = GetSelectedHero()[tSlot]

    local SelectedHero =  GetSelectedHero()
    SelectedHero[slot] = t
    SelectedHero[tSlot] = s

    self:PerformDeselect(slot)
    self:PerformDeselect(tSlot)
    self:PerformSelect(t, slot)
    self:PerformSelect(s, tSlot)
end

--神器相关的方法全部注释掉内容，主要是担心别的模块也有调用，因此不删除方法
--是否需要显示神器
function SelectHeroWindow:IsShowTeamArtifactItem()
    return false
    --if not self.artifactData then return false end
    --return battle_team_ui_mgr.IsShowTeamArtifactItem(teamArtifactType)
end
---- 刷新神器显示状态
function SelectHeroWindow:RefreshTeamArtifact()
    --if not isShowTeamArtifact then
    --    return
    --end
	--if window and window:IsValid() then 
	--	local showState = battle_team_ui_mgr.IsShowTeamArtifactItem(teamArtifactType)
	--	for _, artifactItem in pairs(window.artifactProfiles or {}) do
	--		if artifactItem and not util.IsObjNull(artifactItem.gameObject) and artifactItem.gameObject.activeSelf ~= showState then
	--			artifactItem:SetActive(showState)
	--		end
	--	end
	--end
end

-- 刷新上阵英雄穿戴的神器
function SelectHeroWindow:RefreshHeroArtifact()
    --if not isShowTeamArtifact then
    --    return
    --end
    --if window and window:IsValid() then 
    --    window.artifactData={
    --        another={}, 
    --        data={}, 
    --        oneOfData={}, 
    --        isArtifactGray={},
    --     }
    --    local curHeroData = GetCurData().selectedHero
    --    if curHeroData then
    --        for i = 0, 4 do
    --            if curHeroData[i] then
    --                local tempData = GetHeroEquipEntityByPos(curHeroData[i], 5)
    --                --更新同名神器的状态
    --                if tempData then
    --                    window:PerformSelectArtifact(curHeroData[i],i)
    --                else
    --                    window:PerformDeselectArtifact(i)
    --                end
    --            else
    --                window:PerformDeselectArtifact(i)
    --            end
    --        end
    --    end
    --end
end

function SelectHeroWindow:SetCloseEvent(func)
    self.onCloseEvent = func;
end

function SelectHeroWindow:SetFightEvent(func)
    self.onFightEvent = func;
end

function SelectHeroWindow:InitHeroArtifact()
    --self.artifactProfiles =self.artifactProfiles or {}
    --self.artifactData={
    --    another={}, --当前位置的神器的同名神器所在位置
    --    data={}, --当前位置的神器数据
    --    oneOfData={}, --保存某个ID的神器的数据
    --    isArtifactGray={},--是否置灰
    -- }
end

function SelectHeroWindow:DisposeHeroArtifact()
    --if self.artifactProfiles then
    --    for k, v in pairs(self.artifactProfiles) do
    --        if v then
    --            v:Dispose()
    --        end
    --    end
    --    self.artifactProfiles = nil
    --end
    --self.artifactData={
    --    another={}, --当前位置的神器的同名神器所在位置
    --    data={}, --当前位置的神器数据
    --    oneOfData={}, --保存某个ID的神器的数据
    --    isArtifactGray={},--是否置灰
    -- }
end

function SelectHeroWindow:PerformSelectArtifact(hero,slot)
    --if not isShowTeamArtifact then
    --    return
    --end
    --local isShow=window:IsShowTeamArtifactItem()
    --local tempData = GetHeroEquipEntityByPos(hero, 5)
	--if tempData then
    --    local equips=window.artifactData
    --    local equipID=tempData:GetGoodsID()
    --    if equips.oneOfData and equips.oneOfData[equipID] then
    --        local otherPos=equips.oneOfData[equipID]
    --        local otherData=equips.data[otherPos]
    --        --同个位置反复上
    --        if otherPos == slot then print("test multiup pos=",slot) return end
    --        --如果已经存在同名神器,判断是否需要置灰 isGreater==nil表示两者不是同名神器
    --        local isGreater= god_equipment_mgr.IsGreaterGodEquip(tempData,otherData,slot,otherPos)
    --        if isGreater~=nil then --置灰遍历过的那个神器
    --            --设置另一个已创建神器的状态
    --            window:SetStatus(otherPos,isGreater==true)
    --            --创建当前神器
    --            window:CreateArtifact(tempData,slot,isGreater==false)
    --            --同名神器冲突无法必须显示，无论玩家是否设置默认显示
    --            window:ShowArtifactStatus(otherPos,true)
    --            window:ShowArtifactStatus(slot,true)
    --            equips.oneOfData[equipID]=nil
    --            equips.data[slot]=tempData
    --            equips.another[otherPos]=slot
    --            equips.another[slot]=otherPos
    --        end
	--	else
    --        equips.another[slot]=nil
    --        equips.data[slot]=tempData
    --        equips.oneOfData[equipID]=slot
    --        window:CreateArtifact(tempData,slot,false)
    --        window:ShowArtifactStatus(slot,isShow)
    --    end
    --
	--end
end

function SelectHeroWindow:CreateArtifact(equip,i,status)
    --local equips=window.artifactData
    --local artifactItem = self.artifactProfiles[i]
    --if not artifactItem then
    --    self.artifactProfiles[i] = goods_item.CGoodsItem()
    --    artifactItem = self.artifactProfiles[i]
    --    artifactItem:Init(self.artifactSlot[i],nil,0.3,nil,function()
    --        local af = artifactItem
    --        af.transform.anchoredPosition = {x = -83 , y = 0 }
    --        af.transform:SetAsLastSibling()
    --    end)
    --end
    ----点击弹出提示函数
    --artifactItem:EnableGodEquipFrame(true)
    --artifactItem:SetGoods(equip,nil,nil,function()
    --    --print("================test==============")
    --    -- ShowGodEquipTips(equip,i)
    --end)
    --if artifactItem then
    --    artifactItem:SetEnableState(not status,true)
    --    artifactItem:SetRedDotEnable(status)
    --end
    --equips.isArtifactGray[i]=status
end

function SelectHeroWindow:SetStatus(i,status)
    --local artifactItem = self.artifactProfiles[i]
    --if artifactItem then
    --    if artifactItem then
    --        artifactItem:SetEnableState(not status,true)
    --        artifactItem:SetRedDotEnable(status)
    --    end
    --end
    --window.artifactData.isArtifactGray[i]=status
end

---ShowArtifactStatus 设置某位置上的神器的状态
---@param i number 神器的位置
---@param status boolean 是否显示
function SelectHeroWindow:ShowArtifactStatus(i,status)
    --local artifactItem = self.artifactProfiles[i]
    --if artifactItem then
    --    artifactItem:SetActive(status)
    --end
end

function SelectHeroWindow:PerformDeselectArtifact(slot)
    --if not isShowTeamArtifact then
    --    return
    --end
    -- if self.artifactProfiles[slot] then
    --    self.artifactProfiles[slot]:Dispose()
    --    self.artifactProfiles[slot] = nil
    --end
    --local isShow=window:IsShowTeamArtifactItem()
    --local equips=window.artifactData
    --if equips.data[slot] then
    --    local anotherPos=equips.another[slot]
    --    local equipID= equips.data[slot]:GetGoodsID()
    --    if anotherPos then
    --        window:SetStatus(anotherPos,false)
    --        --同名神器其中一件下阵后，判断是否显示该神器
    --        window:ShowArtifactStatus(anotherPos,isShow)
    --        equips.another[anotherPos]=nil
    --        equips.oneOfData[equipID]=anotherPos
    --    else
    --        equips.oneOfData[equipID]=nil
    --    end
    --    equips.data[slot]=nil
    --    equips.another[slot]=nil
    --    equips.isArtifactGray[slot]=false
    --    -- print("test deselectartifact data=nil i=",i)
    --end
end
function SelectHeroWindow:ArtifactStateSet(index , state)
	--if self.artifactProfiles[index] and not util.IsObjNull(self.artifactProfiles[index].gameObject) then
	--	self.artifactProfiles[index].gameObject:SetActive(state)
	--end
end
--[[获取 英雄身上某个位置的装备]]
function GetHeroEquipEntityByPos(heroEntity, pos)
    local equipment_mgr = require"equipment_mgr"
	local tempData = equipment_mgr.GetHeroEquipByPos(heroEntity,pos)
	return tempData
end
function SetChangeTeam(bo)
    CanChangeTeam  = bo
 end

 
function DeleteOtherTeamHero(hero)
    local teamIndex=hero.teamIndex or 0
    if isBattleDefenseLineup then
        if(not defenseSetIndexLineup[teamIndex])then
            defenseSetIndexLineup[teamIndex] = true
        end
    end

    if TabData and TabData[teamIndex] and  TabData[teamIndex].selectedHero then
        for i, v in pairs(TabData[teamIndex].selectedHero) do
            if v.heroSid == hero.heroSid or v.heroID == hero.heroID then
                TabData[teamIndex].selectedHero[i] = nil
            end
        end		
    end
end
function onItemRenderBottom(scroll_rect_item,index,dataItem)
    if not window then return end
    local _self = window
    
    --local battleType = unforced_guide_mgr.GetBattleType()
    
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    window.botHeroSlot[index] = scroll_rect_item.gameObject:GetComponent(typeof(RectTransform))
    local headTrans = scroll_rect_item:Get("headTrans")
    local helpTips = scroll_rect_item:Get("HelpTips")
    Common_Util.SetActive(helpTips,dataItem.isHelpNpc == true)
    
    local profile = scroll_rect_item.data[3] or hero_item.CHeroItem(_NAME):Init(headTrans,function (p)
        if not p then return end
        p:DisplayInfo()
    end ,0.85)
    scroll_rect_item.data[3] = profile
    local onClickHero= function (item)
        if closeLineUp then
            return
        end
        if window:isHasTrialSelectedHero() and dataItem.isTrial == 1 then
            flow_text.Add(lang.Get(393022))--"XXXXXX该队伍已有试用英雄"
            return
        end
        --点击英雄，荣耀擂台可更换上阵英雄队伍
        if CanChangeTeam then
            
            --local isUp=(dataItem.isUp and dataItem.isUp==1 ) --点击的英雄是当前队伍的同Sid英雄
            
            --是否在其他队伍上阵
            local isInTeam=( dataItem.teamIndex and dataItem.teamIndex ~= curEnemyIndex)
            --是否已经选择
            local isSelected = dataItem.sameUp == 1

            
            --没有选中或者已上阵其他队伍的话，需要判断有没有空位置
            if not isSelected or isInTeam then
                local emptySlotIndex=GetEmptySlot()--获取可以放置的英雄位置索引
                if(emptySlotIndex==nil)then --如果没有可以放置英雄的位置，提示当前上阵英雄已满
                    flow_text.Add(lang.Get(280002))
                    return
                end
            end
            
            
            --点击的英雄的同ID英雄在非当前队伍的某一队上阵了
            --local isInTeam=(dataItem.hasTeamIndex and dataItem.teamIndex and dataItem.teamIndex ~= curEnemyIndex) 
            if isInTeam then
                local teamTips = string.format(lang.Get(242029), dataItem.teamIndex or 0)
                -- 21016 提示
                -- 242029 该英雄已在%s队上阵，是否将改英雄上阵至当前队伍？
                local ui_window_mgr = require "ui_window_mgr"
                local win = ui_window_mgr:ShowModule("ui_message_box_multiple")
                win:SetInputParam(lang.Get(21016), teamTips, {}, function (state)
                    if not state or not window or not window:IsValid() or not dataItem then
                        -- 准备下阵
                        return
                    end
                    if dataItem.teamIndex~=0 then
                        DeleteOtherTeamHero(dataItem)
                    end
                    OnClickBotHero(profile,item,index)
                end, lang.Get(7604), lang.Get(9013))
            else
                
                --[[if isSelected  then
                    flow_text.Add(lang.Get(2147))
                    return
                end]]
               
   --[[             if not isUp and (dataItem.hasTeamIndex and dataItem.teamIndex and dataItem.teamIndex == curEnemyIndex)  then
                    flow_text.Add(lang.Get(2147))
                    return
                end]]
                OnClickBotHero(profile,item,index)
                local h = scroll_rect_item.data[2]
                if not showWeaponGuide and ((HeroData and #HeroData == 1) or (button_tips_trigger.GetCurNameId() == 17 and util.get_len(GetSelectedHero()) >= 5))  then
                    button_tips_trigger.Close()
                    showtips = false
                end
                if not showWeaponGuide and ((HeroData and #HeroData == 1) or (button_tips_trigger.GetCurNameId() == 7 and (h and h.numProp and h.numProp.starLv >= hero_mgr.Hero_Star.Purple) or not GetEmptySlot()))  then
                    button_tips_trigger.Close()
                    showtips = false
                end
            end
            return
        end

        --阵营试炼判断是否已上阵其他队伍
        if not camp_trial_data.GetCanUpBySid(ui_select_model_node.GetBattleType() ,dataItem.heroSid) then
            ---657036 该英雄已上阵
            flow_text.Add(lang.Get(657036))
            return
        end
        
        local isSelected = dataItem.sameUp
        if isSelected == 1 then
            if dataItem.teamIndex and dataItem.teamIndex > 0 then
                if dataItem.teamIndex ~= curEnemyIndex then
                    flow_text.Add(lang.Get(73370))
                else
                    local hero = profile.hero
                    local slot = GetHeroSlot(hero)
                    window:OnDownSceneHeroLayout(slot)
                    window:OnDragSceneHeroLayout(slot)
                    window:OnUpSceneHeroLayout(-100, nil)
                end
            else
                flow_text.Add(lang.Get(2147))
            end
            return
        end
        local emptySlotIndex=GetEmptySlot()--获取可以放置的英雄位置索引
        if(emptySlotIndex==nil)then --如果没有可以放置英雄的位置，提示当前上阵英雄已满
            flow_text.Add(lang.Get(280002))
        end
        local hasTeamIndex = dataItem.teamIndex and dataItem.teamIndex>0
        if (forMaze and hasTeamIndex and dataItem.teamIndex and dataItem.teamIndex ~= curEnemyIndex) then
            flow_text.Add(lang.Get(73370))
            return
        end
        OnClickBotHero(profile,item,index)
        local h = scroll_rect_item.data[2]
        if not showWeaponGuide and ((HeroData and #HeroData == 1) or (button_tips_trigger.GetCurNameId() == 17 and util.get_len(GetSelectedHero()) >= 5))  then
            button_tips_trigger.Close()
            showtips = false
        end
        if not showWeaponGuide and ((HeroData and #HeroData == 1) or (button_tips_trigger.GetCurNameId() == 7 and (h and h.numProp and h.numProp.starLv >= hero_mgr.Hero_Star.Purple) or not GetEmptySlot()))  then
            button_tips_trigger.Close()
            showtips = false
        end
    end
    -- new icon modified
    --profile:TrialLimitEnable(battleType)
    --profile:HeroEffectEnable(true, window.curOrder + 1, 1)
    profile:SetHero(dataItem,onClickHero,true)

    local hp = GetCacheData() and GetCacheData()[dataItem.heroSid] and GetCacheData()[dataItem.heroSid].numProp.hp
    local hpMax = GetCacheData() and GetCacheData()[dataItem.heroSid] and GetCacheData()[dataItem.heroSid].numProp.hpMax
    local mp = GetCacheData() and GetCacheData()[dataItem.heroSid] and GetCacheData()[dataItem.heroSid].numProp.mp
    local mpMax = GetCacheData() and GetCacheData()[dataItem.heroSid] and GetCacheData()[dataItem.heroSid].numProp.mpMax
    profile:OnShowHP(GetCurData().showHp,(hp or 100) / (hpMax or 100),(mp or 0) / (mpMax or 100))
    profile:GrayHeroIcon(hp == 0)
    
    --阵营试炼判断是否已上阵其他队伍,设置置灰以及禁止
    local isTrialOther = not camp_trial_data.GetCanUpBySid(ui_select_model_node.GetBattleType(),dataItem.heroSid)

    if IsSelected(dataItem) then
        profile:SelectHero(true)
    else
        profile:SelectHero(false)
    end
    local hasTeamIndex = IsMulti() and dataItem.teamIndex ~= nil and dataItem.teamIndex > 0
    local teamIcon = scroll_rect_item:Get("QueueIcon")
    local teamIndex = scroll_rect_item:Get("Text")
    Common_Util.SetActive(teamIcon,hasTeamIndex)
    if hasTeamIndex then
        teamIndex.text = dataItem.teamIndex
    end
    if dataItem.sameUp == 1 and not IsSelected(dataItem) then
        profile:GrayHeroIcon(true)
        if not dataItem.teamIndex or dataItem.teamIndex <= 0 then
            profile:LockHero(true,true)
        end
    elseif window:isHasTrialSelectedHero() and dataItem.isTrial == 1 then
        --队伍已有试用英雄，其它试用英雄禁止上阵
        profile:GrayHeroIcon(true)
        profile:LockHero(true,true)
    else
        if isTrialOther or forMaze and hasTeamIndex and dataItem.teamIndex and dataItem.teamIndex ~= curEnemyIndex then
            profile:GrayHeroIcon(true)
            profile:LockHero(true,true)
        else
            profile:LockHero(false,false)
        end
    end

    -- 宝石标识
    --local force_weapon_mgr = require "force_weapon_mgr"
    --local weaponStage = force_weapon_mgr.GetSkillUnlockStage( dataItem.hero )
    -- new icon modified
    --scroll_rect_item.data[3]:ForceWeaponFlag(weaponStage)

    scroll_rect_item.InvokeFunc = ClickEvent
    scroll_rect_item.gameObject:SetActive(true)

    TriGuideEvent(index)
end

function SelectHeroWindow:ShowFinger()
    --local unSelectHeroList = self:GetUnselectHeroList()
    for i,v in ipairs(self.scroll_table.data) do
        if not v.teamIndex or v.teamIndex == 0 then
            local item = self.scroll_table:GetItem(i-1)
            if item then
                local pos = item.transform
                Common_Util.SetParent(self.heroTip,pos);
                Common_Util.SetLocalPos(self.heroTip.transform,0,0,0)
                Common_Util.SetActive(self.heroTip,true);
                break;
            end

        end
    end
    --return false
end

function TriGuideEvent(index )
    if window ==nil or index~=1 then
        return
    end
    if not window.firstDrawed then
        window.firstDrawed = true
        force_guide_system.TriEnterEvent(force_guide_event.tEventEnterHeroSelect)
    else
        if window.clickHeroFlag then
            window.clickHeroFlag = false
            force_guide_system.TriComEvent(force_guide_event.cEventSelect)
        end
    end
    if banSelectTips then
        force_guide_system.TriComEvent(force_guide_event.cEventSelect)
    end
end

function OnClickBotHero(profile,item,index)
    if closeLineUp then
        return
    end
    local unforced_guide_mgr = require "unforced_guide_mgr"
    if unforced_guide_mgr.GetCurStep() then
        local cfg = unforced_guide_mgr.GetCurStepConfig()
        if unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.ILLUSION_TOWER and (unforced_guide_mgr.GetCurGuide() == 2 or unforced_guide_mgr.GetCurGuide() == 31) and cfg and (cfg.stepId == 5 or cfg.stepId == 4) then
            event.Trigger(event.CLICK_SELECT_HERO)
        elseif unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.AREAN_SINGLE and unforced_guide_mgr.GetCurGuide() == 4 and cfg and (cfg.stepId == 72 or cfg.stepId == 73) then
            event.Trigger(event.CLICK_SELECT_HERO)
        elseif unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.SOCIATY_WAR_DEFEND and unforced_guide_mgr.GetCurGuide() == 35 and cfg and cfg.stepId == 225 then
            event.Trigger(event.SELECT_SOCIATY_WAR_DEFEND_HERO)
        elseif unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.SOCIATY_WAR_COMBAT and unforced_guide_mgr.GetCurGuide() == 36 and cfg and cfg.stepId == 229 then
            event.Trigger(event.SELECT_SOCIATY_WAR_COMBAT_HERO)
        elseif unforced_guide_mgr.GetBattleType() == unforced_guide_mgr.BATTLE_TYPE_NUM.SPACE_GAP and unforced_guide_mgr.GetCurGuide() == 37 and cfg and cfg.stepId == 233 then
            event.Trigger(event.SELECT_SPACEGAP_HERO)
            return
        end
    end

    local hero = profile.hero
    local slot = GetHeroSlot(hero)
    --点击下方英雄列表,若是未选中英雄,则在英雄出战框生成英雄模型item,若是选中英雄,删除对应出战框内英雄模型item
    local hp = GetCacheData() and GetCacheData()[hero.heroSid] and GetCacheData()[hero.heroSid].numProp.hp
    if hp and hp <= 0 then
        --flow_text.Add(lang.Get(73212))
        return
    end
    if slot ~= nil then
        window:PerformDeselect(slot)
        local SelectedHero = GetSelectedHero()
        SelectedHero[slot] = nil
        profile:SelectHero(false)
        profile:DisplayInfo()
        local curPosition = window.scroll_table.moveContainer.localPosition
        HeroData = window:GetUnselectHerodata()
        window.scroll_table.data= HeroData
        --window.scroll_table:Refresh(-1,-1)
        window.scroll_table:DoRefresh(0,#HeroData)
        CheckFinger()
        util.DelayOneCall(0.0001,function()
            if window and window:IsValid() then
                if #HeroData > 6 then
                    window.scroll_table:ScrollTo(index)
                    window.scroll_table.moveContainer.localPosition = curPosition
                end
                window.clickHeroFlag = true
            end
        end)
    else
        slot = SelectHero(hero)
        if slot ~= nil then
            window:PerformSelect(profile.hero, slot)
            window.changeBegin = true
            profile:SelectHero(true)
            profile:DisplayInfo()
            local curPosition = window.scroll_table.moveContainer.localPosition
            local t = (#HeroData-7) * -124.45
            if curPosition.x < t then
                curPosition.x = t
            end
            HeroData = window:GetUnselectHerodata()
            if banSelectTips then
                local curNameId = button_tips_trigger.GetCurNameId()
                if curNameId == 7 or curNameId == 17 then
                    button_tips_trigger.Close()
                    showtips = false
                end
            end
            window.scroll_table.data= HeroData
            --window.scroll_table:Refresh(-1,-1)
            window.scroll_table:DoRefresh(0,#HeroData)
            CheckFinger()
            util.DelayOneCall(0.0001,function()
                if window and window:IsValid() then
                    if #HeroData > 6 then
                        window.scroll_table:ScrollTo(index)
                        window.scroll_table.moveContainer.localPosition = curPosition
                    end
                    window.clickHeroFlag = true
                end
            end)
            if #HeroData == 0 then
                TriGuideEvent(1)
                local curNameId = button_tips_trigger.GetCurNameId()
                if curNameId == 7 or curNameId == 17 then
                    button_tips_trigger.Close()
                    showtips = false
                end
            end

            local music_contorller = require "music_contorller"
            music_contorller.PlayFxAudio(300083)

            event.Trigger(event.SELECT_HERO_COMPLETE)
        end
    end
    --UpdatePower()
end

-- 刷新试用英雄上阵状态
function SelectHeroWindow:CheckTrialHero(name, heroSid)
    if not self or not self:IsValid() then return end
    local hData = {}
    local len = #TabData
    for i = 1, len do
        local now = TabData[i].saveSelectedHero
        hData = {}
        for key, value in pairs(now) do
            if value then
                -- print("value", value.heroSid, "value.heroSid ~= heroSid", value.heroSid ~= heroSid)
                if (value.heroSid and value.heroSid ~= heroSid) or value.isTrial ~= 1 then
                    hData[key] = value
                end
            end
        end
        SetSaveHeroData(hData, TabData[i].showHp, TabData[i].cacheData, false, i)
    end
    self:UpdateUi()
end

function ClickEvent(eName,srItem)
----    print(srItem and srItem.data.index)
end

function SelectHero(hero)
    local slot = GetHeroSlot(hero)
    if slot ~= nil then
        return nil
    else
        local slot = preSlot or GetEmptySlot(hero)
        if slot ~= nil then
            local heroLine = GetSelectedHero()
            if heroLine then
                heroLine[slot] = hero
                preSlot = nil
            end
        end
        return slot
    end
end

--更新战斗力显示
function UpdatePower(unAnima)
    if (not window) or (not window:IsValid()) or util.IsObjNull(window.UIRoot) then
        return
    end
    --播放光环效果
    UpdateHalo()
    local _power =0
    local weaponPower = 0
    local heroList = GetCurData().selectedHero
    local gw_home_soldier_data = require "gw_home_soldier_data"
    local maxSoldierLevel = gw_home_soldier_data.GetCampusSoldierLevel()
    local maxLevelSoldierCfg = game_scheme:Soldier_1(maxSoldierLevel)
    local soldierNum = 0
    
    for i, v in pairs(heroList) do
        --log.Error(v.heroData.heroSid)
        if v.heroSid ~= 0 then
            _power = _power +  gw_power_mgr.GetHeroPowerByCfgId(v.heroID)
            local entity = gw_hero_data.GetHeroEntityByCfgId(v.heroID)
            local carrySoldierNum = entity and entity:GetHeroSoldierNum() or 0
            soldierNum = soldierNum + carrySoldierNum
            if not IgnoreSoldier then
                --log.Error("计算士兵战力")
                if maxLevelSoldierCfg then
                    _power = _power + gw_power_mgr.GetSoliderForceInQueue(carrySoldierNum,maxLevelSoldierCfg.soldierID)
                end
            end
        else
            local cfg = game_scheme:Hero_0(v.heroID)
            if cfg then
                _power = _power + cfg.HeroInitialPower
            end
        end
        
    end
    mySoldierNum = soldierNum
    weaponPower = GWG.GWHomeMgr.droneData.GetDroneLocalPower()
    --[[
     local homeland_mgr = require "homeland_mgr"    
     local _power = calpro_mgr.CalculateHeroListPowerWithHalo(ui_bs_arena_hero_select_panel_controller.GetCurData().selectedHero,nil,getHeroFunc)
        window:UpdateGrindState(_power)
       local weaponPower = 0
        if not forSpaceGap and not forPlot then
            weaponPower = ui_bs_arena_hero_select_panel_controller.GetCurData().usedWeapon and homeland_mgr.GetWeaponPowerByID(ui_bs_arena_hero_select_panel_controller.GetCurData().usedWeapon["weaponId"]) or 0
        end]]
    _power = _power + weaponPower

    --ui_bs_arena_hero_select_panel_controller.SetPower(_power)
    if window.powerGrowUp then
        if battleType == common_new_pb.Arena then
            window.powerGrowUp:SetNumber(_power, true)

        else
            window.powerGrowUp:SetNumber(_power, false)
        end
    else
        local string_util = require "string_util"
        if isArenaDefence then
            window.arenaPowerText.text = string_util.ToScientificNotation(_power)
            --    self.powerGrowUp = ui_growup_number.Init(self.arenaPowerText, false, "ui_select_hero", true)
        else
            window.power.text = string_util.ToScientificNotation(_power)
            --    self.powerGrowUp = ui_growup_number.Init(self.power, false, "ui_select_hero", true)
        end
    end
    if (not window.myOldPower) or  window.myOldPower == 0 then
        window.myOldPower = _power
    else
        local p = _power - window.myOldPower
        if p > 0 then
            window.powerUpCount = window.powerUpCount or 0
            window.powerUpList = window.powerUpList or {}
            window.powerUpCount = (window.powerUpCount + 1) % 4
            if window.powerUpCount == 0 then
                window.powerUpCount = 1
            end
            local go = window.powerUpList[window.powerUpCount]
            if not go then
                go = GameObject.Instantiate(window.powerUp.gameObject)
                window.powerUpList[window.powerUpCount] = go
            end
            go.transform:SetParent(window.mypowerbg)
            go.transform.localScale = {x=1,y=1,z=1}
            go:SetActive(true)
            go:GetComponent(typeof(Animation)):Play()
            go.transform:GetChild(0):GetComponent(typeof(Text)).text = math.floor(p)
        elseif  p < 0 then
            window.powerDownCount = window.powerDownCount or 0
            window.powerDownList = window.powerDownList or {}
            window.powerDownCount = (window.powerDownCount + 1) % 4
            if window.powerDownCount == 0 then
                window.powerDownCount = 1
            end
            local go = window.powerDownList[window.powerDownCount]
            if not go then
                go = GameObject.Instantiate(window.powerDown.gameObject)
                window.powerDownList[window.powerDownCount] = go
            end
            go.transform:SetParent(window.mypowerbg)
            go.transform.localScale = {x=1,y=1,z=1}
            go:SetActive(true)
            go:GetComponent(typeof(Animation)):Play()
            go.transform:GetChild(0):GetComponent(typeof(Text)).text = math.floor(-1*p)
        end
        window.myOldPower = _power
    end
end

function GetCurSelectedHeroPower()
    local homeland_mgr = require "homeland_mgr"
    local weaponPower = GetCurData().usedWeapon and homeland_mgr.GetWeaponPowerByID(GetCurData().usedWeapon["weaponId"]) or 0
    local power = calpro_mgr.CalculateHeroListPowerWithHalo(GetSelectedHero(),nil,getHeroFunc) + weaponPower
    return power
end

function SelectHeroWindow:InitGrindState()
    --do
    --    showGrind = true
    --    self.grindBtn.gameObject:SetActive(true)
    --    return
    --end
    local power = calpro_mgr.CalculateHeroListPowerWithHalo(GetSelectedHero(),nil,getHeroFunc)
    showGrind = false
    if grindThreshold then
        if power/ GetCurData().monsterPower >= grindThreshold then
            showGrind = true
        end
    end

    if showGrind then
        --self.grindBtn.gameObject:SetActive(true)
        --self.fightSwitcher:Switch(1)
        --self.fightTextColor.color =  {r=42/255, g=99/255, b=132/255, a=1}
        -- self.fightTextOutline.effectColor = {}
        -- self.fightTextOutline.effectColor = {r = 15/255,g = 70/255,b = 115/255,a = 167/255}
    else
        self.grindBtn.gameObject:SetActive(false)
        --self.fightSwitcher:Switch(0)
        --self.fightTextColor.color =  {r=121/255, g=91/255, b=39/255, a=1}
        -- self.fightTextOutline.effectColor = {r = 175/255,g = 103/255,b = 0,a = 167/255}
    end
	local common_new_pb = require "common_new_pb"
    if fightBtnTxt ~= nil then--时空擂台
        self.fightTextColor.text = fightBtnTxt --lang.Get(1008)
    else
        self.fightTextColor.text = lang.Get(657022)
    end
    if quickFightBtnTxt ~= nil then--时空擂台
        self.quickFightBtnTxt.text = quickFightBtnTxt --lang.Get(1008)
    else
        self.quickFightBtnTxt.text = lang.Get(254128)
    end
end

function SelectHeroWindow:UpdateGrindState(power)
    if not power or not GetCurData().monsterPower or not grindThreshold then
        return
    end
    if showGrind then
        if power/ GetCurData().monsterPower >= grindThreshold then
            self.grindImg:SetGray(false)
            self.grindBtnEff.gameObject:SetActive(true)
            self.fightBtnEff.gameObject:SetActive(false)
            -- self.grindText.color =  {r=1, g=1, b=1, a=1}
            -- self.grindOutline.effectColor =  {r=0.69, g=0.4, b=0, a=0.65}
        else
            self.grindImg:SetGray(true)
            self.grindBtnEff.gameObject:SetActive(false)
            self.fightBtnEff.gameObject:SetActive(true)
            -- self.grindText.color =  {r=0.24, g=0.24, b=0.24, a=0.65}
            -- self.grindOutline.effectColor =  {r=0.82, g=0.82, b=0.82, a=0.65}
        end
    end
end

--刷新阵容光环显示
function UpdateHalo(otherWorld)
    --对手光环
    local potentialActiveHalo = halo_mgr.CalPotentialActivateHalo(GetCurData().enemyHeroData)
    local function_open = require "function_open"
    local functionOpenCfg = game_scheme:FunctionOpen_0(1524)
    local functionOpen = function_open.CheckFunctionIsOpen(1524,false,false)
    local showEnemyHalo = functionOpen
    if functionOpenCfg and otherWorld then
        --TODO 这里进行跨服光环处理，对showEnemyHalo进行赋值
        
    else
        if functionOpen then
            window.enemyHalo = halo_mgr.CalActivateHalo(potentialActiveHalo)
        else
            window.enemyHalo = nil
        end
    end
    

    --window.haloEnemyEffectShow = window.enemyHalo ~= nil

    window.iconEnemyUI = window.iconEnemyUI or halo_item.CHaloItem():Init(window.haloEnemyRoot)
    window.iconEnemyUI:SetGrayState(false)
    
    local enemyHeroType = {}
    if GetCurData().enemyHeroData then
        for i, v in pairs(GetCurData().enemyHeroData) do
            local _type = game_scheme:Hero_0(v.heroID).type
            table.insert(enemyHeroType,_type)
        end
    end
   

    local sendData =
    {
        unlock = showEnemyHalo,
        heroType = enemyHeroType,
        --myHalo = window.enemyHalo,
        isEnemy = true
    }
    
    window.iconEnemyUI:SetHalo(window.enemyHalo, nil, function()
        local ui_halo_tips = require "ui_halo_tips"
        ui_halo_tips.SetInputParam(sendData)--window.enemyHalo)
        local win = windowMgr:ShowModule("ui_halo_tips", function()
            TouchDisable(true)
        end,function()
            if not closeLineUp then --只有在允许上阵时才能恢复
                TouchDisable(false)
            end
        end)
    end)

    --敌方呼吸特效
    --if window.enemyHalo ~= nil then
    --    local cfg = game_scheme:HaloUnLock_0(window.enemyHalo)
    --    if cfg ~= nil then
    --        window.haloEnemyEffect.gameObject:SetActive(true)
    --    else
    --        window.haloEnemyEffect.gameObject:SetActive(false)
    --    end
    --else
        window.haloEnemyEffect.gameObject:SetActive(false)
    --end


    --玩家光环
    potentialActiveHalo = halo_mgr.CalPotentialActivateHalo(GetSelectedHero())
    if functionOpen then
        window.myHalo = halo_mgr.CalActivateHalo(potentialActiveHalo)
    else
        window.myHalo = nil
    end
    
    --window.haloEffectShow = window.myHalo ~= nil
    
    local heroType = {}
    for i, v in pairs(GetSelectedHero()) do
        local type = game_scheme:Hero_0(v.heroID).type
        table.insert(heroType,type)
    end

    local mySendData =
    {
        heroType = heroType,
        --myHalo = window.myHalo,
        isEnemy = false
    }
    
    window.iconUI = window.iconUI or halo_item.CHaloItem():Init(window.haloRoot)
    window.iconUI:SetGrayState(false)

    window.iconUI:SetHalo(window.myHalo, nil, function()
        local ui_halo_tips = require "ui_halo_tips"
        ui_halo_tips.SetInputParam(mySendData)--window.myHalo)
        local win = windowMgr:ShowModule("ui_halo_tips", function()
            TouchDisable(true)
        end,function()
            if not closeLineUp then --只有在允许上阵时才能恢复
                TouchDisable(false)
            end
        end)
    end)

    --我方呼吸特效
    --if window.myHalo ~= nil then
    --    local cfg = game_scheme:HaloUnLock_0(window.myHalo)
    --    if cfg ~= nil then
    --        window.haloEffect.gameObject:SetActive(true)
    --    else
    --        window.haloEffect.gameObject:SetActive(false)
    --    end
    --else
        window.haloEffect.gameObject:SetActive(false)
    --end
    DealyShowHaloEffect()
end

function DealyShowHaloEffect()
    --计算敌方光环位置
    window.enemyPos = halo_mgr.CalHaloPos(window.enemyHalo, GetCurData().enemyHeroData)
    for i = 0,4 do
        if not util.IsObjNull(window["enemyHaloEffect_"..i]) then 
            window["enemyHaloEffect_"..i].gameObject:SetActive(false)
        end
    end
    if window.enemyPos ~= nil then
        for k,v in pairs(window.enemyPos) do
            if not util.IsObjNull(window["enemyHaloEffect_"..v]) then 
                window["enemyHaloEffect_"..v].gameObject:SetActive(true)
            end
        end
    end
    
    --计算我方光环位置
    window.myPos = halo_mgr.CalHaloPos(window.myHalo,GetSelectedHero())
    for i = 0,4 do
        if not util.IsObjNull(window["myHaloEffect_"..i]) then 
            window["myHaloEffect_"..i].gameObject:SetActive(false)
        end
    end
    if window.myPos ~= nil then
        for k,v in pairs(window.myPos) do
            if not util.IsObjNull(window["myHaloEffect_"..v]) then 
                window["myHaloEffect_"..v].gameObject:SetActive(true)
            end
        end
    end
end

function DeselectHero(hero)
    local slot = GetHeroSlot(hero)
    if slot ~= nil then
        local SelectedHero = GetSelectedHero()
        SelectedHero[slot] = nil
        return true
    else
        return false
    end
end

function IsSelected(hero)
    return GetHeroSlot(hero) ~= nil
end

function GetEmptySlot(hero)
    local heroLine = GetSelectedHero()
    local slot = nil
    if hero then
        local heroCfg = game_scheme:Hero_0(hero.heroID)
        if heroCfg then
            for i = 0, maxSelectNumber - 1 do
                local condition = not heroLine or not heroLine[i]
                if  condition then
                    local row, col = battle_data.GetRowAndColByIndex(i + 1)
                    if row == 0 and heroCfg.profession == 1 then
                        slot = i
                        break
                    elseif row == 1 and (heroCfg.profession == 4 or heroCfg.profession == 5) then
                        slot = i
                        break
                    end
                end
            end
        end
    end
    if not slot then
        for i = 0, maxSelectNumber - 1 do
            local condition = not heroLine or not heroLine[i]
            if condition then
                slot =  i
                break
            end
        end
    end
    return slot
end

function GetHeroSlot(hero)
    local heroLine = GetSelectedHero()
    if heroLine then
        for p, h in pairs(heroLine) do
            if h ~= nil and h.heroSid == hero.heroSid then
                return p
            end
        end
    end
    return nil
end

function GetHeroSlotById(heroId)
    local heroLine = GetSelectedHero()
    if heroLine then
        for p, h in pairs(heroLine) do
            if h ~= nil and h.heroID == heroId then
                --log.Error(heroId)
                return p
            end
        end
    end
    return nil
end

function GetHeroSlot1(hero)
    if TabData[HeroLineUpEnum.LineUp1] and TabData[HeroLineUpEnum.LineUp1].selectedHero then
        for p, h in pairs(TabData[HeroLineUpEnum.LineUp1].selectedHero) do
            if h ~= nil and h.heroSid == hero.heroSid then
                return p
            end
        end
    end
    return nil
end

function GetHeroSlot2(hero)
    if TabData[HeroLineUpEnum.LineUp2] and TabData[HeroLineUpEnum.LineUp2].selectedHero then
        for p, h in pairs(TabData[HeroLineUpEnum.LineUp2].selectedHero) do
            if h ~= nil and h.heroSid == hero.heroSid then
                return p
            end
        end
    end
    return nil
end

function GetHeroSlot3(hero)
    if TabData[HeroLineUpEnum.LineUp3] and TabData[HeroLineUpEnum.LineUp3].selectedHero then
        for p, h in pairs(TabData[HeroLineUpEnum.LineUp3].selectedHero) do
            if h ~= nil and h.heroSid == hero.heroSid then
                return p
            end
        end
    end
    return nil
end

--获取对应阵容的所有选中英雄
function GetAllLineUpHeroDic()
    local result = {}
    for k,v in pairs(HeroLineUpEnum) do
        local lineUpTabData = TabData[v]
        if lineUpTabData and lineUpTabData.selectedHero then
            for p, h in pairs(lineUpTabData.selectedHero) do
                if h ~= nil then
                    result[h.heroSid] = v
                end
            end
        end
    end
    return result
end

function GetAllHeroSlot(hero)
    for k,v in pairs(HeroLineUpEnum) do
        if TabData[v] and TabData[v].selectedHero then
            for p, h in pairs(TabData[v].selectedHero) do
                if h ~= nil and h.heroSid == hero.heroSid then
                    return p
                end
            end
        end
    end

    return nil
end

--判断队伍是否有相同英雄（星际迷航多队有队伍索引的，不支持上阵同heroID英雄）
--function IsSameTeamHero()
--
--end

--判断队伍是否有英雄
function IsHeroIDSelected(hero)
    local cfg_hero = GetHeroInCache(hero.heroID) -- game_scheme:Hero_0(hero.heroID)
    if cfg_hero then
        for j,k in pairs(TabData) do
            if k.selectedHero then
                for _,v in pairs(k.selectedHero) do
                    if v ~= nil then
                        local cfg_select = GetHeroInCache(v.heroID) -- game_scheme:Hero_0(v.heroID)
                        if cfg_select and cfg_hero.HerohandBookID == cfg_select.HerohandBookID then
                            return 1
                        end
                    end
                end
            end
        end
    end
    return 0
end

--初始化光效引导中的英雄配置
function GetAssignHeroCfg()
    local tip_cfg = game_scheme:BtnTips_0(22)
    if tip_cfg then
        local heros = tip_cfg.heroPosition
        if heros then
            local arrData = util.SplitString(heros, "#")
            for index = 0,#arrData do
                local source = arrData[index]
                if source then 
                    local data = util.SplitString(source, ";")
                    local heroId = tonumber(data[1])
                    local position = tonumber(data[2])
                    if not heroPositionTips[heroId] then
                        heroPositionTips[heroId] = position
                    end
                end
            end
        end
    end
end

--[[
    hero 英雄id 
    p 英雄当前坐标 （如果不传入p值 则视为下阵英雄 判断传入英雄是否为目标英雄）
]]
function JudgeAssignHeroStand(hero,p)
    if window and window.point and window.point[p] and not util.IsObjNull(window.point[p]) then
        --对上阵英雄站位是否正确进行判断并提示
        local exist = false
        local target = false
        local heroEntity = player_mgr.GetPalPartDataBySid(hero.heroSid)
        if not heroEntity then
            return
        end
        local heroID = heroEntity:GetHeroID()
        --local cfg = game_scheme:Hero_0(heroID)
        local heroPosition = heroPositionTips[heroID]
        if heroPosition then
            target = true
            --属于引导英雄
            if p and heroPosition ~= p then
                --不在对应位置
                exist = true
                local isLeft = GetDirection(p)
                local pos_string = heroPosition + 1 --显示的位置 和 逻辑里 相差1
                local str = string.format(lang.Get(9480), tostring(pos_string))
                if not forSpaceGap and not forPlot then
                    button_tips_trigger.Show("ui_select_hero",22, window.point[p], str, nil, true, false, isLeft)
                end
            else
                local ui_button_tips_kuang = require "ui_button_tips_kuang"
                ui_button_tips_kuang.Close()
            end
        end
        return exist,target
    end
end

--判断上阵英雄是否存在指定引导英雄 [英雄下阵时传入英雄id 规避迭代]
function CheckIsExistAssignHero(hero)
    local exist = false
    local target = false
    if hero then
        exist,target = JudgeAssignHeroStand(hero)
        --下阵英雄必须是目标英雄 才隐藏引导
        if not target then
            return 
        end
    else
        local selected = GetSelectedHero()
        for _,v in pairs(selected or {}) do
            if v ~= nil then
                local cfg_select = game_scheme:Hero_0(v.heroID)
                if cfg_select then
                    local heroPosition = heroPositionTips[cfg_select.heroMapID]     
                    if heroPosition then
                        --存在指定引导英雄
                        exist = JudgeAssignHeroStand(v,_)
                        break
                    end
                end
            end
        end
    end

    if not exist then
        if windowMgr:IsModuleShown("ui_button_tips_kuang") then
            local ui_button_tips_kuang = require "ui_button_tips_kuang"
            ui_button_tips_kuang.Close()
        end
    end
end

function GetDirection(position)
    local isLeft = false
    if position == 2 or position == 5 then
        isLeft = true
    end
    return isLeft
end

function JudgeSelectHeroStand(hero,p)
    --对上阵英雄站位是否正确进行判断并提示
    if haveTips == true then
        return
    end
    local heroEntity = player_mgr.GetPalPartDataBySid(hero.heroSid)
    if not heroEntity then
        return
    end
    local heroID = heroEntity:GetHeroID()
    local cfg = game_scheme:Hero_0(heroID)
    local campID = cfg and cfg.profession
    if p <= 2 and campID and campID >=2 then
        haveTips = true
        local str = lang.Get(9360)
        if p == 5 or p == 2 then
            SelectHeroStandTip(str,p,true)
        else
            SelectHeroStandTip(str,p)
        end
    elseif p >= 3 and campID and campID == 1 then
        haveTips = true
        local str = lang.Get(9361)
        if p == 5 or p == 2 then
            SelectHeroStandTip(str,p,true)
        else
            SelectHeroStandTip(str,p)
        end
    end
end

function SetLineUpState(value)
    closeLineUp = value or false
    if closeLineUp then
        TouchDisable(true)
    end
end

function GetHelpHero()
    if helpNpcList then
        return #helpNpcList > 0
    end
    return false
end

function SetNpcHero(npcList)
    helpNpcList = {}
    local selectedHero = GetSelectedHero()
    for i,v in ipairs(npcList) do
        --if selectedHero[v.pos] then
        --    window:PerformDeselect(v.pos) --已经有人占据了这个位置，则把他下掉
        --end
        local heroEntity = gw_hero_data.GetHeroCfgId2Entity(v.heroId)
        local heroData = gw_hero_data.GetAllHeroDataByCfgID(v.heroId)
        local heroCfg = game_scheme:Hero_0(v.heroId)
        local temp =
        {
            pos = v.pos - 1, --站位从0开始
            heroData = 
            {
                battleProp =
                {
                    power = heroCfg.HeroInitialPower
                },
                hasTeamIndex = false,
                hero = nil,
                heroID = v.heroId,
                heroSid = heroData ~= nil and heroData.isUnLock and heroEntity:GetHeroSid() or 0,
                isTrial = 0,
                isHelpNpc = heroData ~= nil and not heroData.isUnLock or true,
                isUp = 0,
                numProp =
                {
                    lv = heroCfg.monsterLevel,
                    starLv = heroCfg.starLv,
                },
                rarityType = heroCfg.rarityType,
                sameUp = 0,
                type = heroCfg.type
            }
        }
        
        table.insert(helpNpcList,temp)
        --window:PerformSelect(heroData,v.pos,true)
    end
    for i,v in ipairs(helpNpcList) do
        TabData[1].selectedHero[v.pos] = v.heroData
    end
end

function CheckFinger()
    if window then
        Common_Util.SetActive(window.heroTip,false);
        local value = false
        if not closeLineUp then
            if not forSpaceGap and not forPlot and not forSecretPlace then
                if gw_home_novice_util.CheckHeroSelectGuide() then
                    if util.get_len(GetSelectedHero()) < 5 and (HeroData and #HeroData > 0) then
                        window:ShowFinger()
                    elseif GetEmptySlot() and (HeroData and #HeroData > 0) then
                        window:ShowFinger()
                    end
                    --if not value then
                    --    Common_Util.SetParent(window.heroTip,window.closeBtn.transform);
                    --    Common_Util.SetLocalPos(window.heroTip.transform,0,0,0)
                    --    Common_Util.SetActive(window.heroTip,true);
                    --end
                end
            end
        else
            --showtips = button_tips_trigger.Show("ui_select_hero",7, window.fightBtn.transform, nil)
        end
    end
    

end

--显示上阵英雄提示
function SelectHeroStandTip(str,p,downBubble)
    if forSpaceGap or forPlot then
        return
    end
    if window and window.point and window.point[p] and not util.IsObjNull(window.point[p]) then
        button_tips_trigger.Show("ui_select_hero",6, window.point[p], str, function(isClickSelf)
            haveTips = false
            button_tips_trigger.Close()
        end,downBubble)
    end
end

function GetChangedHeroModel(hero)
    local modulId = hero_mgr.ChangeHeroModel(hero.heroID,hero.numProp and hero.numProp.starLv or hero.heroStar)
    local modulCfg = game_scheme:Modul_0(modulId)
    return modulCfg.modelPath
end

function IsInFilterHeroList(sid)
    for k,v in pairs(filterHeroSidList) do
        if sid == v then
            return true
        end
    end
    return false
end

local CSelectHeroWindow = class(ui_base, nil, SelectHeroWindow)

function Show()
    --预先进入引导状态
    local common_new_pb = require "common_new_pb"
    if battleType == common_new_pb.GameGoal then
        --force_guide_system.TriEnterEvent(force_guide_event.tPreEnterSelectHero)
        --force_guide_system.TriComEvent(force_guide_event.cPreEnterSelectHero)
    end

    if window == nil then
        window = CSelectHeroWindow()
        window.delayOpenMain = 0.2
        window.delayCloseMain = 0
        window._NAME = _NAME;window:LoadUIResource("ui/prefabs/gw/buildsystem/uiarenaheroselectpanel.prefab", nil, nil,nil,true,true)--LoadUIResource("ui/prefabs/uiheroselect.prefab", nil, nil,nil,true)
    else
        window:Show()
    end
    local Lean = GameObject.Find("UIRoot/Lean"):GetComponent(typeof(LeanTouch))
    Lean.enabled = true
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end


function Close()
    if needCloseWeekGuide then
        needCloseWeekGuide = nil
        local unforced_guide_mgr = require "unforced_guide_mgr"
        unforced_guide_mgr.CloseGuide()
    end
    if setLineupCallBack then
        setLineupCallBack = nil
    end
    
    if window ~= nil then
        
        window:Close()
        window = nil
        forMaze = false
        showEnemyHeroRarity = false
        showtips = false
        isPeak = false
        timerDic= {}
    end
    isShowTeamArtifact = false
    teamArtifactType = nil
    grindThreshold = nil
    showGrind = false
    CanChangeTeam = nil
    closeLineUp = false --关闭界面后，禁止上阵功能复原。
end

function OnSceneDestroy()
    Close()
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)

function GetshowHp()
    return GetCurData().showHp
end

--=================对外接口=================


---@description 设置我方英雄阵容
---@param heroData table 英雄数据,下标从0开始
---@param hpEnable boolean 是否显示血条
---@param cData table 上阵英雄数据
---@param isUpdateUI boolean 是否更新UI
---@param line number 第几个队伍，默认为1
function SetSaveHeroData(heroData,hpEnable,cData,isUpdateUI,line)
    
    helpNpcList = {}
    
    line = line or 1
    TabData[line] = TabData[line] or {}
    TabData[line].showHp = hpEnable
    TabData[line].cacheData = cData

    if heroData then
        TabData[line].saveSelectedHero = {}
        for p, h in pairs(heroData) do
            if p >= 0 then
                local entity = gw_hero_data.GetHeroEntityByCfgId(h.heroID)
                TabData[line].saveSelectedHero[p] = h
            end
        end
    end
    TabData[line].selectedHero = {}
    if heroData then
        for p, h in pairs(heroData) do
            if p >= 0 then
                local dead = false
                local isTrial = h.isTrial and h.isTrial == 1 or hero_trial_mgr.isTrialHeroBySid(h.heroSid)
                if not IsTrialMode() and cData and cData[h.heroSid] and cData[h.heroSid].numProp.hp == 0 then
                    dead = true
                end
                -- print("qsy_yxsy:[ui_select_hero]SetSaveHeroData>>>>selectedHero",h.heroSid,isTrial,battleType)
                if isTrial and (not dead) then--试用英雄GetTrialPrefabPath
                    if battleType then
                        local canTrial = hero_trial_mgr.GetTrialHeroBattleTimes_Can(battleType,h.heroSid)
                        if not canTrial or canTrial == 0 then
                            dead = true
                        end
                    else
                        log.ErrorReport(log.ENUM_LOGTYPE.ERRINFO,"battleType is nil，试用英雄已自动下战，请检查是否传入战斗类型")
                        dead = true
                    end
                end
                h.isTrial = isTrial and 1 or 0
                -- log.Error("qsy_yxsy:[ui_select_hero]SetSaveHeroData>>>>selectedHero",h.heroSid,
                -- (not dead),IsHeroIDSelected(h) == 0,forMaze)
                if (not dead) and ( IsHeroIDSelected(h) == 0 or forMaze) then
                    local entity = gw_hero_data.GetHeroEntityByCfgId(h.heroID)
                    TabData[line].selectedHero[p] = h
                end
            end
        end
    end

    lastSelectHeros = TabData[line].selectedHero

    if window and window.UIRoot and isUpdateUI then
        window:UpdateUi()
    end
    isCanUseTrial = curType ~= DEFENCE
end

--设置底部可选英雄集，不设置默认可选所有
function SetCanSeletHeros(heroData,isUpdateUI)
    canSelectHeros = {}
    for _,v in pairs(heroData) do
        if v.heroSid then
            canSelectHeros[v.heroSid] = v
        end
    end
    if window and window.UIRoot and isUpdateUI then
        window:UpdateUi()
    end
end

function GetSameUpTeamIndex(hero)
    local heroCfg = game_scheme:Hero_0(hero.heroID)
    if heroCfg and TabData then
        for teamid, _heros in pairs(TabData) do
            if _heros and _heros.selectedHero then
                for i, _hero in pairs(_heros.selectedHero) do
                    if _hero and _hero.heroSid and _hero.heroSid == hero.heroSid then
                        return teamid
                    else
                        -- 不同星级的同一英雄英雄ID一样，改为读图鉴ID HerohandBookID
                        local cfg = game_scheme:Hero_0(_hero.heroID)
                        if cfg and cfg.HerohandBookID and cfg.HerohandBookID == heroCfg.HerohandBookID then
                            return teamid
                        end
                    end
                end
            end
        end
    end
end
function GetselectHero()
    local unselectheroData = window:GetUnselectHerodata()
    local _selectHero = GetSelectedHero()
    local hero = {}
    
    for i,v in pairs(_selectHero) do
        if not HasSame(hero,v.heroID) then
            table.insert(hero,v)
        end
    end

    for i,v in ipairs(unselectheroData) do
        if not HasSame(hero,v.heroID) then
            table.insert(hero,v)
        end
    end
    -- for i,v in ipairs(hero) do
    ------      --print("v.heroID:",v.heroID)
    -- end
    return hero
end

function HasSame( list , id )
    if #list < 0 then return false end
    for i,v in ipairs(list) do
        if v.heroID == id then
            return true
        end
    end
    return false
end

function SelectHeroWindow:CheckTeamSameHero()
    local heroList = {}
    for i, v in pairs(GetSelectedHero()) do
        if not HasSame(heroList,v.heroID) then
            table.insert(heroList,v)
        else
            return true
        end
    end
    return false
end


--设置敌人数据
--[[
    @entityArr 实体列表: key是0~5(对应显示的位置)  value是hero_mgr.CreateVirtualEntity创建的实体
    @totalPower 总战力
    @hpEnable 是否显示血条
    weaponData 敌方武器id和武器等级
]]
---@description 设置敌人数据
---@param entityArr table 敌人阵容列表，下标从0开始
---@param totalPower number 总战力
---@param hpEnable boolean 是否显示血条
---@param isUpdateUI boolean 是否更新UI
---@param line number 第几个队伍，默认为1
---@param weaponData table 敌方武器id和武器等级
function SetEnemyData(entityArr,totalPower,hpEnable,isUpdateUI ,line,weaponData)
    line = line or 1
    TabData[line] = TabData[line] or {}
    TabData[line].enemyHeroData = entityArr
    TabData[line].originEnemyPower = totalPower
    TabData[line].enemyShowHp = hpEnable
    if weaponData and weaponData.weaponid and weaponData.weaponid>0  then
        if not TabData[line].enemyWeapon then TabData[line].enemyWeapon = {} end
        TabData[line].enemyWeapon["weaponId"] = weaponData.weaponid
        TabData[line].enemyWeapon["weaponLv"] = weaponData.weaponLv
    end

    local ui_loading = require "ui_loading"
    ui_loading.SetBattleBossInfo(entityArr)

    if curEnemyIndex == HeroLineUpEnum.LineUp1 and not battle_data.skipBattle then
        local battle_preview_manager = require "battle_preview_manager"
        battle_preview_manager.PreLoadEnemyResource(entityArr)
    end

    if window and window.UIRoot and isUpdateUI then
        window:UpdateUi()
    end

    
    local soldierNum = 0
    for k, v in pairs(entityArr) do
        if v then
            local heroLevelCfg = game_scheme:HeroLevelUp_0(v.heroID, v.numProp and v.numProp.lv or v.heroLevel)
            if heroLevelCfg then
                soldierNum = soldierNum + heroLevelCfg.baseSoldier
            else
                --默认是新手竞技场机器人
                local heroProCfg = game_scheme:HeroPro_0(v.heroID, v.numProp and v.numProp.starLv or v.heroStar)
                soldierNum = soldierNum + heroProCfg.SoldierNum
            end
        end
    end
    enemySoldierNum = soldierNum
end

---@see 设置已胜利队伍
function SetFinishEnemyList(finishEnemyList)
    if not finishEnemyList then return end
    for teamIndex, finishData in pairs(finishEnemyList) do
        TabData[teamIndex] = TabData[teamIndex] or {}
        TabData[teamIndex].isFinish = util.get_len(finishData)>=6
        TabData[teamIndex].finishHeroData = finishData 
    end
end
--设置是否多队阵容
function SetIsMulti(bool)
    isMulti = bool
end

--[[
    @monsterTeam monsterTeam表的id
    @hpMpData hp mp数据列表：key是0~5(对应显示的位置)，value是{hp=0~1，mp=0~1}
    设置第一队敌人
]]
function SetEnemyByMonsterTeam(id,hpMpData,blnstanceEnemy,line)
    line = line or HeroLineUpEnum.LineUp1
    local monsterTotalPower = 0
    local monsterTeam = game_scheme:monsterTeam_0(id)
    if monsterTeam then
        local enemyDataArr = {}
        for i=0, monsterTeam.MonsterId.count-1 do
            local heroId = monsterTeam.MonsterId.data[i]
            if heroId~=0 then
                local hp = hpMpData and hpMpData[i] and hpMpData[i].hp or 1
                local mp = hpMpData and hpMpData[i] and hpMpData[i].mp or 0
                enemyDataArr[i] = gw_hero_mgr.CreateVirtualEntity(heroId,nil,nil,hp,mp,1,1)
            end
            local monster = game_scheme:Hero_0(heroId)
            if monster and monster.HeroInitialPower then
                monsterTotalPower = monsterTotalPower + monster.HeroInitialPower
            end
        end
        SetEnemyData(enemyDataArr,monsterTotalPower,hpMpData~=nil,nil,line)
        bLnstanceEnemy = blnstanceEnemy or bLnstanceEnemy
    end
end

--怪物战斗力
function SetGrindThreshold(v,p,line)
    line = line or 1
    TabData[line] = TabData[line] or {}
    grindThreshold = v
    TabData[line].monsterPower = p
end

--判断武器有没有重复
function CheckIsHasWeapon(wid)
    for i,v in pairs(TabData) do
        if v.usedWeapon then
            if v.usedWeapon.weaponId == wid.weaponId then
                wid.weaponId = 0
            end
        end
    end
end

--现在没有神器的说法了，神器换成召唤兽而且只有一个了。
function SetSaveWeaponData(weaponData,selectedWeaponData,dontUpdate,_dontUseWeapon,line)
    --CheckIsHasWeapon(weaponData)
    --line = line or 1
    --TabData[line] = TabData[line] or {}
    --TabData[line].usedWeapon = weaponData
    --selectedWeapon = selectedWeaponData
    --dontUpDateByServer = dontUpdate or dontUpDateByServer
    --dontUseWeapon = _dontUseWeapon or dontUseWeapon
    --local ui_loading = require "ui_loading"
    --ui_loading.SetBattleStageType(weaponData.stage)
end

--保存战斗类型，用于新手引导判断是否是从狩猎界面打开
function SetSaveBattleType(type)
    ------ --print("SetSaveBattleType>>>>>")
    battleType = type
end

function SetIgnoreSoldier(value)
    IgnoreSoldier = value
end

function SetSortRule(func)
    
end

--勇者试炼用到
function SetFilterHeroList(heroSidList)
    filterHeroSidList = heroSidList
end

-- 时空裂缝使用系统默认英雄
function SetSpaceGapHeroList(heroList)
    forSpaceGap = true
    spaceGapHeros = heroList
end

-- 时空裂缝使用系统默认英雄
function SetExclusiveHeroList(heroList,_useNew)
    useNew = _useNew
    forPlot = true
    plotHeros = heroList
end

-- 单人秘境
function SetSecretplace(isMulti)
    forSecretPlace = true--秘境
    forMultiSecretPlace = isMulti--多人秘境
end

--用于新手引导屏蔽点击
function TouchDisable(isTouch)
    ui_drag_object.SetTouchDisable(isTouch)
end

--用于新手引导限定可点击的框框
--设置槽位是否可用的状态
function SetCheckFram(_arrDown,_arrUp )
    if window then
        window.arrDown=_arrDown
        window.arrUp=_arrUp
    end
end

function SetForMaze(var, GetMazePalPartData, _isPeak)
    forMaze = var
    isPeak = _isPeak
    GetPalPartData = GetMazePalPartData or player_mgr.GetMazePalPartData
end

function SetForFactionWanted(type)
    factionWantedType = type
end

function SetRivalInfo(rivalInfo)
    RivalInfo = rivalInfo
end

function SetShowEnemyHeroRarity(data)
    showEnemyHeroRarity = data
end

function SetTipsStr(str)
    tipsStr = str
end

function SelectHeroWindow:UpdateEnemy(lineUps)
    if lineUps and lineUps[1] then
        local _enemyPower = lineUps[1].personalCE or 0
        local enemyDatas = {}
        local heroArr = lineUps[1].pals
        if heroArr ~= nil then
            for i, hero in ipairs(heroArr) do
                local key = battle_data.GetIndexByRowAndCol( hero.rowNo,hero.colNo)
                --local hero_mgr = require "hero_mgr"
                enemyDatas[key] = gw_hero_mgr.CreateVirtualEntity(hero.palID,hero.level, hero.starLevel,(hero.llCurHp or 0) / (hero.llMaxHp or 100),nil,1,nil,hero.exclusiveLv,hero.skinID)
                --非0表示该英雄是灵魂链接英雄
                if hero.inSoulSlot and hero.inSoulSlot~=0 then
                    enemyDatas[key].isSoulSlot = hero.inSoulSlot
                end
            end
        end
        SetEnemyData(enemyDatas,_enemyPower,false,true)
    end
end

function PlayerDetailFunc(_1, lineUps, guildName, _2, captainID)
    if window and window:IsValid() then
        window:UpdateEnemy(lineUps)
    end
end
event.Register(event.ARENA_PLAYER_DETAILINFO, PlayerDetailFunc)


---@param index number 第几个队伍
---@param heroList table 本地保存的英雄队伍,下标从1开始
---@description 获取本地保存英雄阵容
function GetHeroFormation(index)
    local heroListString = PlayerPrefs.GetString(player_mgr.GetPlayerRoleID().."BattleTwoFormationsSid"..index)
    local heroIDString = PlayerPrefs.GetString(player_mgr.GetPlayerRoleID().."BattleTwoFormationsID"..index)
    local heroRowString = PlayerPrefs.GetString(player_mgr.GetPlayerRoleID().."BattleTwoFormationsRow"..index)
    local heroColString = PlayerPrefs.GetString(player_mgr.GetPlayerRoleID().."BattleTwoFormationsCol"..index)
    local heroList = {}
    if heroListString ~= nil and heroListString ~= "" then
        local arrPalId = util.SplitString(heroListString, "#")
        local arrheroID = util.SplitString(heroIDString, "#")
        local arrRow = util.SplitString(heroRowString, "#")
        local arrCol = util.SplitString(heroColString, "#")
        for i, id in ipairs(arrPalId) do
            local sid = tonumber(id)
            if sid ~= 0 then
                local entity = player_mgr.GetPalPartDataBySid(sid)
                if entity and entity.heroID == tonumber(arrheroID[i]) then
                    local row = tonumber(arrRow[i])
                    local col = tonumber(arrCol[i])
                    local indexKey = battle_data.GetIndexByRowAndCol(row,col)
                    heroList[indexKey] = entity
                end
            end
        end
    end
    return heroList
end

--保存英雄阵容到本地
function SetHeroFormation(hData,index)
    if GetHelpHero() then --存在助战英雄，不能保存
        log.Log("存在助战英雄，因此不保存队列")
        return
    end
    local heroListString = ""
    local heroIDString = ""
    local heroRowString = ""
    local heroColString = ""
    for pos, hero in pairs(hData) do
        heroListString = heroListString..hero.heroSid
        heroIDString = heroIDString..hero.heroID
        local row,col = battle_data.GetRowAndColByIndex(pos + 1)
        --heroRowString = heroRowString..(pos<3 and 0 or 1)
        --heroColString = heroColString..(pos<3 and pos or pos-3)
        heroRowString = heroRowString..(row)
        heroColString = heroColString..(col)

        heroListString= heroListString.."#"
        heroIDString = heroIDString.."#"
        heroRowString= heroRowString.."#"
        heroColString= heroColString.."#"
    end
    PlayerPrefs.SetString(player_mgr.GetPlayerRoleID().."BattleTwoFormationsSid"..index, heroListString)
    PlayerPrefs.SetString(player_mgr.GetPlayerRoleID().."BattleTwoFormationsID"..index, heroIDString)
    PlayerPrefs.SetString(player_mgr.GetPlayerRoleID().."BattleTwoFormationsRow"..index, heroRowString)
    PlayerPrefs.SetString(player_mgr.GetPlayerRoleID().."BattleTwoFormationsCol"..index, heroColString)

end

function SelectHeroWindow:GetSelectedWeapon()
    return GetCurData().usedWeapon and GetCurData().usedWeapon.weaponId
end

--得到上一次选择的英雄阵容
function GetLastSelectHeros()
    return lastSelectHeros
end

function SetExclusiveWeaponIds(data)
    excludeWeaponIds = data or {}
end

---@see 神器显示通用条件
function CheckIsOpenArtifactShow()
    local battle_team_ui_mgr = require "battle_team_ui_mgr"
    isShowTeamArtifact = battle_team_ui_mgr.CheckIsOpenArtifactShow()
    return battle_team_ui_mgr
end

---@see 主线是否显示神器按钮
function CheckIsShowArtifactInMainStory()
    local battle_team_ui_mgr = CheckIsOpenArtifactShow()
    teamArtifactType = battle_team_ui_mgr.eUIType.MainStory

    local power = calpro_mgr.CalculateHeroListPowerWithHalo(GetSelectedHero(),nil)
    local showGrind = false
    if grindThreshold then
        if power/ GetCurData().monsterPower >= grindThreshold then
            showGrind = true
        end
    end
    isShowTeamArtifact = isShowTeamArtifact and not showGrind
end

---@see 星际迷航是否显示神器按钮
function CheckIsShowArtifactInMaze()
    local battle_team_ui_mgr = CheckIsOpenArtifactShow()
    teamArtifactType = battle_team_ui_mgr.eUIType.Maze
end

---@see 日常争霸赛是否显示神器按钮
function CheckIsShowArtifactInSingleChallenge()
    local battle_team_ui_mgr = CheckIsOpenArtifactShow()
    teamArtifactType = battle_team_ui_mgr.eUIType.SingleChallenge
end

---@see 虚空锦标赛是否显示神器按钮
function CheckIsShowArtifactInDomination()
    local battle_team_ui_mgr = CheckIsOpenArtifactShow()
    teamArtifactType = battle_team_ui_mgr.eUIType.Domination
end

---@see 联盟战役是否显示神器按钮
function CheckIsShowArtifactInSociatyWar()
    local battle_team_ui_mgr = CheckIsOpenArtifactShow()
    teamArtifactType = battle_team_ui_mgr.eUIType.SociatyWar
end

---@see 量子入侵是否显示神器按钮
function CheckIsShowArtifactInSociatyBossWar()
    local battle_team_ui_mgr = CheckIsOpenArtifactShow()
    teamArtifactType = battle_team_ui_mgr.eUIType.SociatyBossWar
end
---@see 荣耀擂台是否显示神器按钮
function CheckIsShowArtifactInWeekendArea()
    local battle_team_ui_mgr = CheckIsOpenArtifactShow()
    teamArtifactType = battle_team_ui_mgr.eUIType.WeekendArena
end

---@see 通用是否显示神器按钮
function CheckIsShowArtifactCommon(showType)
    CheckIsOpenArtifactShow()
    teamArtifactType = showType
end

-- ========================================= 勇者试炼相关 Start ==========================================

local isTrailMode = false

function GetTrialPrefabPath()
    return "ui/prefabs/uiheroselectfortriallevel.prefab"
end

-- 设置为勇者试炼状态
function SetTrialMode()
    isTrailMode = true
end

function IsTrialMode()
    return isTrailMode
end

function OnCloseTrialProcess()
    isTrailMode = false
end

-- ========================================== 勇者试炼相关 End ==========================================






-- ========================================== 优化相关 Start ==========================================

function GetHeroInCache(heroID)
    local heroCfg = nil
    if  heroID then
        heroCfg = heroCfgCache[heroID]
        if not heroCfg then
            heroCfg = game_scheme:Hero_0(heroID)
            if heroCfg then
                heroCfgCache[heroID] = heroCfg
            end
        end
    end 
    return heroCfg
end

--是否显示顶部战力信息
function SetTopPowerData(isShow, levelNumber, curEnemyFaceID, curEnemyFameID,enemyFaceIDNotCfg )
    isShowPower = isShow
    if isShow then
        isArenaDefence = false
    end
    levelNum = levelNumber or ""
    enemyFaceID = curEnemyFaceID or 0
    enemyFameID = curEnemyFameID or 0
    enemyFaceIDNotByCfg = enemyFaceIDNotCfg or 0
end

--设置战力显示
function SelectHeroWindow:SetPowerShow()
    --测试使用
    Common_Util.SetActive(self.powerBg, isShowPower)
    Common_Util.SetActive(self.VSIcon, string.empty(levelNum))
    if not isShowPower then
        return
    end
    self.levelText.text = levelNum
    self:SetFaceShow()
end

--设置头像
function SelectHeroWindow:SetFaceShow()
    local face_item = require "face_item_new"
    self.profileEnemy = self.profileEnemy or face_item.CFaceItem()
    self.profileEnemy:Init(self.EnemyFaceItem.transform, nil, 1.1)
    if enemyFaceID and enemyFaceID ~= 0 then
        --怪物头像
        self.profileEnemy:SetMonsterFaceInfo(enemyFaceID, nil)
    else
        --玩家头像
        self.profileEnemy:SetFaceInfo(enemyFaceIDNotByCfg)
    end
    self.profileEnemy:SetActorLvText(false)
    self.profileEnemy:SetFrameID(enemyFameID, true)

    self.profile = self.profile or face_item.CFaceItem()
    self.profile:Init(self.MyFaceItem.transform, nil, 1.1)
    --适配faceID 2025.4.2 将faceID 转换为 faceStr
    local custom_avatar_data = require "custom_avatar_data"
    local customHeadData = custom_avatar_data.GetMyAvatar()
    local faceStr = player_mgr.GetRoleFaceID()
    if customHeadData then 
        faceStr = customHeadData.remoteUrl
    end
    self.profile:SetFaceInfo(faceStr, nil)
    self.profile:SetActorLvText(false)
    self.profile:SetFrameID(player_mgr.GetPlayerFrameID(), true)
end
-- ========================================== 优化相关 End ==========================================