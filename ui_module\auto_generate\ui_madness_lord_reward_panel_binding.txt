local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable


module("ui_madness_lord_reward_panel_binding")

UIPath = "ui/prefabs/gw/activity/madnesslord/uimadnesslordrewardpanel.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	srt_joinPage = { path = "panel/mainPage/ScrollView/Scroll View/Viewport/srt_joinPage", type = ScrollRectTable, },
	btn_LeftBtn = { path = "panel/btn_LeftBtn", type = Button, event_name = "OnBtnLeftBtnClickedProxy"},

}
