---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/Emmy<PERSON>ua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/7/3 16:38
---

local require = require
local util = util
local log = require "log"

local val = require "val"
local try = try
local table_util = require "table_util"
local require_optimize_mgr = require("require_optimize_mgr")
module("register_init_module")

local Warning = log.Warning

InitMoudle = {}
---未登录前就必须require且Init的模块
---机制：该模块登录前一定会被requier且被初始化，启动对应功能
---警告：仅只有逻辑执行时才会执行的模块且不需要依赖网络/监听消息的请逻辑自己维护，不要加入初始化管理
InitMoudle.moduleNameBeforeLogin = {
    --stage 1
    -- "gw_globle",
    --主界面管理
    "gw_g",
    
    -- connan
    "mgr_personalInfo",
    "gw_reconnect_client_mgr",
    
    --"gw_const",
    --"gw_ed",
    --"gw_comp_name",
    --"gw_comp_factory",
    --"gw_camera_const",
    "gw_admin",
    "gw_mgr",
    "gw_home_mgr",
    
    --调整区域
    "gw_home_chapter_data",
    "gw_home_novice_chapter_data",
    
    -- shapan
    "gw_id_mgr",
    "gw_lua_pool_mgr",
    "gw_asset_mgr",
    "gw_input_mgr",
    "gw_sand_mgr",
    "gw_hero_mgr",
    "gw_storm_mgr",
    "game_state_interface",
    "gw_zombie_storm_mgr",
}
---角色+城建数据等基础数据后<时机Time<登录创建数据完成
---常见模块：非（支撑玩家基础数据+城建数据的模块）；  比如在处理其他各种登录数据部件会引用到的模块
---实现目标：1，准备好必要数据后，就尽快进入游戏 关闭登录loading  ----以前是等所有的登录数据，玩家部件等都初始化好后再进入游戏
---当前该模块初始化机制： 
---A,优先处理角色数据和城建数据等基础数据后，执行B,(同时会立马触发加载城建资源逻辑,等待城建场景加载完成时关闭loading界面进入游戏）
---B,该模块列表会自动require且被Init,因为登录的其他数据也在同步处理，可能会早于分帧require（但会支持模块首次访问自动require并Init，并不会影响逻辑）
---C,在等待资源加载过程中可以合理利用其初始化模块，且这时候就关闭登录界面，尽早进入游戏 --注意，这里会保证该列表中的所有模块被require且Init后才会发FIRST_LOGIN_CREATE_DATA_FINISH事件
---警告：仅只有逻辑执行时才会执行的模块且不需要依赖网络/监听消息的请逻辑自己维护，不要加入初始化管理
InitMoudle.modulesBTWHomeAndCreateDataFinish =
{
    --
    "alliance_globle",
    "alliance_data",
    "alliance_function_data",
    "alliance_gift_data",
    "alliance_user_data",
    "alliance_mgr_extend",
    "gw_arm_competition_activity_data",
    --新手引导 弱引导管理
    "weak_guide_follow_mgr",
    "gw_task_mgr",
    "gw_task_red_mgr",
    --通用礼包
    "gw_recharge_mgr",
    
    ---region 任务模块
    --region 新版英雄
   
    --endregion

    "item_module_mgr",
    "gw_home_red_mgr",
    "gw_popups_mgr",
    "mgr_battle_pass",
    "mgr_prosperity_fund",
    "mgr_road_light" ,
    "mgr_nc_pops",
    "mgr_strong_road",
    --独立小游戏
    "gw_independent_game_mgr",
}
---@description 登录创建数据完成,正式进入游戏   FIRST_LOGIN_CREATE_DATA_FINISH事件 <时机
---正式进入游戏后，登录数据都已完成才需要初始化的模块
---常见模块：eg,活动模块  ---等等不需要监听登录事件和被登录调用的
---当前该模块初始化机制： 
---A,正式登录成功后，会分帧依次将下列的模块都require一次并且Init一次
---B,这里如果分帧的过程中有使用场合中的场合1/2：目前该模块会支持使用到时自动Init，会被抢先require并被初始化
---D,这里如果分帧的过程中有使用场合中的场合3时：目前网络模块有未注册的消息等注册时会被补发，能解决
---使用场合：1，登录无需启动，但登录后该模块会--前往主动打开触发 
---        2，登录无需启动，但登录后该模块会--被其他模块使用到被触发，
---        3，登录无需启动，但登录后该模块会--被收到网络消息触发
---警告：仅只有逻辑执行时才会执行的模块且不需要依赖网络/监听消息的请逻辑维护，不要加入初始化管理
InitMoudle.moduleNameInLobby = {   
    "gw_sand_node",
    "gw_sand_hud_node", 
    "gw_sand_lod_const",
    "gw_sand_timer_mgr",
    "gw_sand_vision_mgr",
    "gw_sand_entity_lod_mgr",
    "gw_sand_mark_mgr",
    "unforced_guide_trigger_mgr",--引导触发器
    "main_slg_mgr",
    "main_slg_tips_mgr",    
    --装备
    "gw_equip_mgr",
    "attr_mgr",
    ---endregion

   

    ---region 活动模块
    "gw_activity_mgr", 
    "gw_activity_red_mgr",    
    "gw_activity_data_mgr",
    "gw_world_activity_data",
    "gw_zombie_treasure_data",
    ---endregion


    
    "technology_data",
    "sourceSupply_mgr",
    "cityProtect_mgr",
    "sandBuff_module_mgr",






    --region 沙盘雷达
    "radar_mgr",
    --endregion

    "ui_camp_trial_mgr", --阵营试炼
    "ui_package_gw_mgr",
    --游戏属性
    --"gw_attribute_bonus"
    "gw_hero_summon_mgr",
    "sandbox_treasure_mgr", --沙盘通用宝箱

    --邮件
    "mail_gw_mgr",
    "mail_detail_mgr",

    --城市竞赛
    "city_siege_activity_data",
    
    --国会管理
    "congress_data",
    
    --将军试炼
    "ui_general_trials_mgr",
    --集结大作战
    "ui_war_rally_mgr",
    --
    "gw_home_chapter_data",
    "gw_home_novice_chapter_data",
    --解救人质
    "gw_bomberman_mgr",
    --功能开放mgr
    "function_open_mgr",
    --联盟对决管理
    "alliance_duel_mgr",
    --登录弹窗管理
    --"gw_popup_mgr",
    --商店帮助类
    "gw_shop_util",
    --VIP
    "gw_vip_data",
    --幸运转盘
    "gw_lucky_wheel_mgr",
    --先锋目标
    "pioneer_target_data",
    --全面备战
    "gw_fully_war_mgr",
    --最强指挥官
    "gw_bestleader_mgr",
    --周卡
    "weekend_card_data",
    --联盟徽章
    "alliance_medal_data",
    --登录好礼
    "login_gift_data",
    "gw_alliance_boss_activity_data",
    --聊天管理
    "ui_chat_mgr_gw",
    --排行榜
    "gw_all_rank_mgr",
    --钻石商店
    "diamond_gift_data",
    --限时礼包
    "limit_gift_data",
    --闪金集市
    "shining_market_data",
    --寻宝大作战
    "gw_find_treasure_mgr",
	
	--橡果酒馆任务
    "ui_tavern_mgr",
    --首充
    "gw_firstrecharge_mgr",
    --挂机奖励
    "gw_hangup_mgr",


    --自定义头像
    "custom_avatar_mgr",
    --召唤活动
    "card_draw_data",
    --怪物攻城
    "monsters_approaching_mgr",
    --怪物攻城新版
    "monsters_approaching_group_mgr",
    
    --待办日程
    "todo_schedule_mgr",
    --战区对决
    "war_zone_duel_mgr",
    --消息推送
    "push_message_mgr",
    
    --暴风营救
    "storm_rescue_data",
    --预告管理
    "activity_preview_mgr",
    --改头换脸
    "face_transplant_data",
    --队伍数据管理
    "sand_team_mgr",      
    --超级月好礼 （月月超值）
    "super_month_gift_mgr", 
    --英雄首充副本管理
    "hero_first_charge_mgr",
    --流浪狗
    "gw_straydog_mgr",
    --神兽bingo
    "mythical_beast_mgr",
    --神兽签到
    "mythical_beast_sign_in_mgr",
    --万圣节
    "halloween_activity_mgr",
    --领地复兴
    "land_revival_mgr",
    --国旗
    "national_flag_mgr",
    --狂暴领主
    "gw_madness_lord_mgr",
    --竞技场
    "arena_common_mgr"
}

function InitMoudle:InitRequire()
    if self.is_init_require then
        return
    end
    local event = require("event")
    self.is_login_require_optimize = val.IsTrue("sw_login_require_optimize",0)
    self.is_init_require = true  
    self.onExit = function(mark)
        if mark == "moduleNameBeforeLogin" then
            if self.is_login_require_optimize then
                require_optimize_mgr.DelayerRequireModule({},30,"moduleNameInLobby",true,self.onExit)
            else
                --合并一下
                local array =  table_util.ConcatArray(self.modulesBTWHomeAndCreateDataFinish, self.moduleNameInLobby)
                require_optimize_mgr.DelayerRequireModule(array,30,"moduleNameInLobby",true,self.onExit)
            end
        end       
        event.Trigger(event.SET_FLOW_STATE_CACHE, "register_init_module", "1",mark)
    end  
    if self.is_login_require_optimize then       
        require_optimize_mgr.DelayerRequireModule(self.moduleNameBeforeLogin,20,"moduleNameBeforeLogin",true,self.onExit)
    else       
        require_optimize_mgr.DelayerRequireModule(self.moduleNameBeforeLogin,20,"moduleNameBeforeLogin",true,self.onExit)
    end
end
return InitMoudle
