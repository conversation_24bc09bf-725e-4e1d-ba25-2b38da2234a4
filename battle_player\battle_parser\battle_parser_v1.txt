local require  = require
local table    = table
local pairs    = pairs
local ipairs   = ipairs
local math     = math
local tostring = tostring
local dump     = dump
local print    = print
local string    = string
local tonumber = tonumber

local game_scheme = require "game_scheme"
local event       = require "event"
local tbs_pb      = require "tbs_pb"
local prop_pb     = require "prop_pb"
local player_mgr  = require "player_mgr"

local battle_data             = require "battle_data"
local battle_config           = require "battle_config"
local battle_parser_utility   = require "battle_parser_utility"
local lang                    = require "lang"
local battle_parser_v0        = require "battle_parser_v0"
local const                   = require "const"
local common_pb               = require "common_new_pb"
local json                    = require "dkjson"
local util = require "util"
local log = require "log"
local version_mgr = require "version_mgr"
local battle_manager = require "battle_manager"
local color_palette = require "color_palette"

local Debug = CS.UnityEngine.Debug
local EpisodeDim = CS.War.Battle.EpisodeDim
local EpisodeAction = CS.War.Battle.EpisodeAction

local battleTypeName = 
{
    [common_pb.GameGoal]           = "GameGoal";              --主线
    [common_pb.TheTowerOfIllusion] = "TheTowerOfIllusion";    --幻境之塔，星际通缉
    [common_pb.BraveManTried]      = "BraveManTried";         --勇者试炼，深空试炼，深空探索
    [common_pb.Arena]              = "Arena";                 --竞技场
    [common_pb.Ashdungeon]         = "Ashdungeon";            --阿斯布地牢，遗落之境
    [common_pb.BrokenSpaceTime]    = "BrokenSpaceTime";       --破碎时空，虚空锦标赛
    [common_pb.LeaActivityBoss]    = "LeaActivityBoss";       --活跃度Boss
    [common_pb.LeagueComp]         = "LeagueComp";            --联盟争霸
    [common_pb.LeagueBoss]         = "LeagueBoss";            --联盟Boss
    [common_pb.LeagueTreasureBoss] = "LeagueTreasureBoss";    --联盟探宝
    [common_pb.Compete]            = "Compete";               --切磋, 服务器代码在联盟部件
    [common_pb.MateMonster]        = "MateMonster";           --羁绊打怪
    [common_pb.Replay]             = "Replay";                --战斗回放，客户端用
    [common_pb.Cutscene]           = "Cutscene";              --剧情表演，客户端用
    [common_pb.Maze]               = "Maze";                  --异界迷宫，星际迷航
    [common_pb.Friend]             = "Friend";                --好友切磋
    [common_pb.FactionType1]       = "FactionType1";          --阵营通缉type=1
    [common_pb.FactionType2]       = "FactionType2";          --阵营通缉type=2
    [common_pb.FactionType3]       = "FactionType3";          --阵营通缉type=3
    [common_pb.FactionType4]       = "FactionType4";          --阵营通缉type=4
    [common_pb.PeakOfTime]         = "PeakOfTime";            --时光之巅
    [common_pb.EquipEctype]        = "EquipEctype";           --装备副本，时空穿越
    [common_pb.EquipEctypeZhuanShu]        = "EquipEctypeZhuanShu";           --专属副本
}

module("battle_parser_v1")

local logSwitch = false
local logMark = "[Fight] "
function logSwitchWarning(...)
    if logSwitch then
        log.Warning(logMark,...)
    end
end

function logSwitchError(...)
    if logSwitch then
        log.Error(logMark,...)
    end
end


local SkillContext = nil

--抵挡次数没有属性id，客户端自定义1000
local ResistCntId = 1000

local reportIndex = 0

function ParseReport(player, msg, playByTargetReport)
    local reports = msg.reports
    local reportIndexOffset = 0
    -- local startReportIndex = 1
    if playByTargetReport and playByTargetReport > 0 then
        -- startReportIndex = playByTargetReport
        local multiple_battle_report_creator = require "multiple_battle_report_creator"
        local battleProgressDatas --战斗已攻击信息，
        reports,reportIndexOffset = multiple_battle_report_creator.CreateNewReportBy(msg, playByTargetReport)
        logSwitchWarning("<color=#ffffff>中途切入战斗:！！！！！！！！！！！！！</color> playByTargetReport", playByTargetReport)
        --reports
    else
        --完整战报保存战斗时长信息
        if player.SetNeedSaveTimeDataDic then
            player:SetNeedSaveTimeDataDic(true)
        end
    end
    
    battle_data.ResetSummonedData()
    battle_manager.SetLastRound(1)
    battle_data.ClearBattleInfo()
    battle_data.ClearStatisticData()
    SkillContext = nil
    WinningStreakContext = {}

    --dump(ReportTable)
    for i = 1, #reports do
        local report = reports[i]
        local rt = report.reportType
        local handler = ReportTable[rt]
        logSwitchWarning("<color=#ffffff>播放戰鬥！！！！！！！！！！！！！</color> reportType:", rt, " index:", i, "handler:", handler)
        reportIndex = i
        if reportIndexOffset ~= 0 then
            reportIndex = reportIndex + reportIndexOffset
            --print("<color=#ffffff>新战报偏移差值计算！！！</color> sourceIndex:", reportIndex, " newIndex:", i)
        end
        if handler ~= nil then
            -- if not battle_data.skipBattle 
            -- or rt == tbs_pb.ReportType_GameOverReport 
            -- or rt == tbs_pb.ReportType_BuildReport 
            -- or rt == tbs_pb.ReportType_NumericReport
            -- then
                handler(report, player, reportIndex)
            -- end
        else
            --Debug.LogWarning("Error report type " .. rt)
        end

        for key, value in pairs(battle_data.GetHeros()) do
            --print("<color=#55ffff>阵容属性：</color>", " index:", i,"pos:", key, " info:", json.encode(value, { indent = true }))
        end
    end
end

function OnBuildReport(msg, player)
    battle_parser_v0.OnBuildReport(msg, player)
end

function OnPalDieReport(msg, player)
    -- battle_parser_v0.OnPalDieReport(msg, player)

    -- 死亡6人只收到5次OnPalDieReport
    -- Debug.LogError("OnPalDieReport")
    -- local report = msg.palDieReport
    -- battle_data.OnPalDieReport(report)
end

local roundMinDuring = 0
function OnRoundReport(msg, player, reportIndex)
    if SkillContext ~= nil then
        FlushSkillContext(player)
    end

    local report = msg.roundReport
    logSwitchWarning("report.round",report.round)
    battle_data.SetRoundTimes(report.round)
    if report:HasField("bEnd") and report.bEnd then
        player:RoundEnd(reportIndex)
        battle_data.ResetLastSkillID()
    else
        if version_mgr.CheckSvnBranchVersion(120612) then
            if roundMinDuring == 0 then
                local cfg = game_scheme:InitBattleProp_0(1229)
                roundMinDuring = cfg and cfg.szParam.data[0] or 1000
                roundMinDuring = roundMinDuring/1000
            end
            player:RoundStart(report.round, reportIndex,roundMinDuring)
        else
            player:RoundStart(report.round, reportIndex)
        end
        battle_config.CheckIsExcuteGuide(player, report.round)
    end
end

-- 这里其实是开始一个技能
function OnSkillContainerReport(msg, player, reportIndex)
    local report = msg.skillContainerReport

    if SkillContext ~= nil then
        FlushSkillContext(player)
    end

    SkillContext = {}
    SkillContext.casterID = report.palID
    SkillContext.skillID = report.skillID
    SkillContext.reportIndex = reportIndex
    SkillContext.segment = {}
end

-- 这里其实是技能的一段
function OnUseSkillReport(msg, player)
    local report = msg.useSkillReport

    if SkillContext ~= nil then
        table.insert(SkillContext.segment, {targets = report.targets, descriptor = {}})
    end
end

function OnAddBuffReport(msg, player)
    local report = msg.addBuffReport

    if SkillContext == nil then
        -- 回合开始时挂buff
        battle_parser_utility.AppendAddBuffPackage(player, report.target, report.buffID, report.buffLevel,nil,report.palID,0)
    else
        -- 技能击中时挂buff
        local descript = AcquireDescriptor(report.target)
        if descript.addbuff == nil then
            descript.addbuff = {}
        end
        table.insert(descript.addbuff, {report.buffID, report.buffLevel, report.palID})
    end
end

function OnRemoveBuffReport(msg, player)
    local report = msg.removeBuffReport

    if SkillContext == nil then
        -- 回合开始时删buff
        battle_parser_utility.AppendRemoveBuffPackage(player, report.target, report.buffID, report.buffLevel)
    else
        -- 技能击中时删buff
        local descript = AcquireDescriptor(report.target)
        if descript.removeBuff == nil then
            descript.removeBuff = {}
        end
        table.insert(descript.removeBuff, {report.buffID, report.buffLevel})
    end
end

function AddRolePropChange(player, palID, segment, propId, propValue1, propValue2, propValue3)

    if SkillContext == nil then
        if player.currentAction == nil then
            local action = EpisodeAction()
            action.caster = palID
            action.player = player
            player:AppendEpisode(action)
            player:SetCurrentAction(action)
        end
        AppendRolePropChange(player, palID, segment, propId, propValue1, propValue2, propValue3)
    else
        local descript = AcquireDescriptor(palID)
        if descript.modifyProps == nil then
            descript.modifyProps = {}
        end
        table.insert(descript.modifyProps, {palID = palID, propID = propId, propValue = propValue1, propValue2 = propValue2, propValue3 = propValue3})
    end
end

function AppendRolePropChange(player, palID, segment, propId, propValue1, propValue2, propValue3)

    if player.AppendRolePropChange then 
        player:AppendRolePropChange(palID, segment, propId, propValue1, propValue2, propValue3)
    end
end


local newPalProp2OldMap = {
    [prop_pb.BATTLE_NEW_TOTAL_HEALTH_BASE]=prop_pb.PAL_PROP_HP,
    [prop_pb.BATTLE_NEW_CURR_HEALTH_BASE]=prop_pb.PAL_PROP_REMAIN_HP,
    [prop_pb.BATTLE_NEW_TOTAL_DEFENSE_BASE]=prop_pb.PAL_PROP_DEFENCE,
}

function RemapPalProp2Old(propId)
    return newPalProp2OldMap[propId] or -1
end

function OnUpdatePropReport( msg, player )
    local report = msg.updatePropReport
    local originPropID = report.propID
    report.propID = RemapPalProp2Old(originPropID)
    if report.propID ~= prop_pb.PAL_PROP_HP and 
        report.propID ~= prop_pb.PAL_PROP_REMAIN_HP and 
        report.propID ~= prop_pb.PAL_PROP_DEFENCE then
        return
    end

    -- if report.palID == 8 then
    --     local log = require "log"
    --     log.Error("修改",report.palID,"最大血量", report.propValue)
    -- end
    if SkillContext == nil then
        -- 回合开始时修改属性
        if report.propID == prop_pb.PAL_PROP_HP then
            battle_parser_utility.AppendModifyMaxHp(player, report.palID, report.propValue, 1)
        elseif report.propID == prop_pb.PAL_PROP_REMAIN_HP then
            battle_parser_utility.AppendModifyHp(player, report.palID, report.propValue, 1)            
        end
    end

    if report.propID == prop_pb.PAL_PROP_HP then
        logSwitchWarning("战报最大血量上限!!!!!!" .. report.palID.. "-".. prop_pb.PAL_PROP_DEFENCE .. "-".. report.propValue)
        AddRolePropChange(player, report.palID, 0, prop_pb.PAL_PROP_HP, report.propValue, 0)
    elseif report.propID == prop_pb.PAL_PROP_REMAIN_HP then
        AddRolePropChange(player, report.palID, 0, prop_pb.PAL_PROP_REMAIN_HP, report.propValue, 0)
    elseif report.propID == prop_pb.PAL_PROP_DEFENCE then
        logSwitchWarning("战报防御值!!!!!!" .. report.palID.. "-".. prop_pb.PAL_PROP_DEFENCE .. "-".. report.propValue)
        AddRolePropChange(player, report.palID, 0, prop_pb.PAL_PROP_DEFENCE, report.propValue, 0)
    end
end

function OnShieldReport( msg, player )
    -- 暂未使用
end

--召唤物报告
function OnSummonedReport(msg, player)
    local report = msg.summonedReport
    local battle_data = require "battle_data"
    local summoned = battle_data.GetHeroByID(report.palID)
    if summoned and summoned.register then
        if report:HasField("data") then
            if report.data.val ~= 0 then
                battle_data.UpdateSummonedProp(report.palID, report.data.numtype, report.data.val)
            end
        end
        return
    end

    local summonedCfg = game_scheme:Summoned_0(report.heroid)
    local heroCfg = game_scheme:Hero_0(report.heroid)

    local summonOwner =  battle_data.GetHeroByID(report.masterSid) --找到召唤物的拥有者
    local modelId = nil
    local skinID = nil
    if summonOwner then 
        skinID = summonOwner.skinID
        local skinCfg = game_scheme:Skin_0(skinID) 
        if skinCfg and skinCfg.SummonedID then --不同等级召唤物模型可能不同，策划配置了列表做对应，-jk
            local summonedModelID = skinCfg.SummonedModelID.data
            local summonedID = skinCfg.SummonedID.data
            if summonedModelID then
                for i=0,#summonedID  do
                    if summonedID[i] == report.heroid then
                        if summonedModelID[i] then
                            modelId = summonedModelID[i]
                        else
                            log.Error("没有与召唤物匹配的模型id",skinID)
                        end
                        break
                    end
                end
            else 
                log.Error("召唤物未配置模型ID",skinID)
            end
          
        end
    end
    modelId = modelId or summonedCfg.leftModelID
    local moduleCfg = game_scheme:Modul_0(modelId) 
    --连续战斗时，不初始化左侧召唤物
    if not battle_data.isInSeriesBattle or report.dir ~= 1 then
        if not battle_data.skipBattle and player.RegisterSummoned then
            player:RegisterSummoned(report.palID,report.masterSid,moduleCfg.modelPath)
        end
    end 
    battle_data.RegisterSummoned(report.palID, report.masterSid, report.heroid, 0, 0, 0, 0, 0, summonedCfg, moduleCfg, heroCfg and heroCfg.faceID.data[0], 0, 0, report.dir, report.deadRound, report.isInitData,skinID)

    if report:HasField("data") then
        if report.data.val ~= 0 then
            battle_data.UpdateSummonedProp(report.palID, report.data.numtype, report.data.val)
        end
    end
end

--召唤物属性改变报告
function OnSummonedNumberReport(msg, player)
    local report = msg.summonedNumReport
    if report.data:HasField("skilleffectId") then
        local last = battle_data.GetLastSkillID(report.palID)
        local now = report.data.skilleffectId
        if last ~= now then
            battle_data.UpdateSummonedProp(report.palID, report.data.numtype, report.data.val)
        else
            battle_data.SetLastSummonedProp(report.palID, report.data.numtype, report.data.val)
        end
        battle_data.SetLastSkillID(report.palID, now)
    else
        battle_data.UpdateSummonedProp(report.palID, report.data.numtype, report.data.val)
    end
end

-- local totalHP = 0
function OnNumericReport(msg, player)
--    log.Error("parse skill, OnNumericReport")
    local report = msg.numericReport

    if SkillContext == nil then
        SkillContext = {}
        SkillContext.segment = {}
        table.insert(SkillContext.segment, {descriptor = {}})
    end
    local descript = AcquireDescriptor(report.targets)
    descript.dead = report:HasField("Dead") and report.Dead or descript.dead
    descript.critical = report:HasField("Critical") and report.Critical or descript.critical
    descript.needHit = report.actionID == SkillContext.skillID or descript.needHit
    descript.actionID = report.actionID
    descript.removeShield = report:HasField("removeShield") and report.removeShield or false
    descript.casterID = report.palID
    descript.breakTrend = report:HasField("breakTrend") and report.breakTrend or descript.breakTrend  --原力压制
    logSwitchWarning("动作者id(casterID):",descript.casterID,",目标id(targets):",report.targets,",原力压制(breakTrend):",descript.breakTrend,"伤害变化:",report.numbers)
    if battle_data.IsSummoned(report.palID) then
        local summoned = battle_data.GetHeroByID(report.palID)
        if summoned then
            descript.casterID = summoned.masterID
            end
    end
    descript.fakeDead = report:HasField("fakeDead") and report.fakeDead or descript.fakeDead

    if report.actionID == SkillContext.skillID then
        descript.needHit = descript and descript.needHit or true
    else
        if descript.buffHit == nil then
            descript.buffHit = {}
        end
        table.insert(descript.buffHit, report.actionID)
    end

    if report.numType == tbs_pb.NumericType_HP then
        -- Debug.LogError("血量变化:"..tostring(report.numbers)..",palID:"..tostring(report.palID)..",targets:"..tostring(report.targets))
        -- if report.targets == 7 then
        --     totalHP = totalHP + report.numbers
        --     Debug.LogError("当前扣血："..totalHP)
        -- end

        if report.numbers > 0 then
            descript.healHp = descript.healHp + report.numbers
            battle_data.count_heal(report.palID, report.numbers)
        else
            descript.damageHp = descript.damageHp + report.numbers
            descript.hurtSource[report.palID] = descript.hurtSource[report.palID] or 0 
            descript.hurtSource[report.palID] = descript.hurtSource[report.palID] + report.numbers
            -- report.palID 造成的伤害
            battle_data.count_damage(report.palID, math.abs(report.numbers))
            -- report.targets 承受的伤害
            battle_data.count_hurt(report.targets, math.abs(report.numbers))
        end
    elseif report.numType == tbs_pb.NumericType_MP then
        if report.numbers > 0 then
            descript.generateMp = descript.generateMp + report.numbers
        else
            descript.consumeMp = descript.consumeMp + report.numbers
        end
    elseif report.numType == tbs_pb.NumericType_SP then
        if report.numbers > 0 then
            descript.generateSp = descript.generateSp + report.numbers
        else
            descript.useSp = descript.useSp + report.numbers
        end
        battle_data.count_sp(report.targets, report.numbers)
    end

    battle_data.OnNumericReport(report)

    -- 服务器字段修改为 NumericType_SP
    -- if report:HasField("shieldChanged") then
    --     descript.shieldChanged = report.shieldChanged
    -- end
end

function OnGameOverReport(msg, player)
    local report = msg.gameOverReport

    if SkillContext ~= nil then
        FlushSkillContext(player)
    end
    --print("<color=#ffffff>OnGameOverReport</color>", json.encode(util.cp_pb_data(report), { indent = true }))
    battle_parser_utility.SetRewards(report.winner, report.loser, report.winnerRewards, report.loserRewards)
    player:BattleEnd()

    
    if not battle_manager.GetIsReplay() then
        TriggerBattleReport()
    end
end

function GetBattleScore()
    --ScoreType:评分类型(1回合数评分 2伤害评分 3阵亡人数评分 4技能特效评分)
    --ConditionValue:条件值

    --战斗回合系数
    local roundTimes = battle_data.GetBattleRound()
    local roundFactorCfg = game_scheme:BattleScore_0(1, roundTimes)
    local roundFactor = 1
    if roundFactorCfg == nil then
        local nums = 15 --固定设置最大回合数为15
        roundFactorCfg = game_scheme:BattleScore(nums)
    end
    if roundFactorCfg then
        roundFactor = roundFactorCfg.Score
    end

    --伤害评分系数
    local loserDmg = battle_data.GetLoserDmg()
    local winnerHp = battle_data.GetWinnerHP()
    local expFactor = game_scheme:BattleScore_0(2, 2).Score
    local scaleFactor = game_scheme:BattleScore_0(2, 1).Score
    local dmgScore = math.pow((loserDmg / (roundTimes * winnerHp)), expFactor) * scaleFactor

    --阵亡评分
    local dieCount = battle_data.GetTotalDieCount()
    if dieCount > 12 then
        dieCount = 12
    end
    local dieScore = 0
    local dieCfg = game_scheme:BattleScore_0(3, dieCount)
    if dieCfg then
        dieScore = dieCfg.Score
    end

    --技能得分
    local effectCfg = game_scheme:BattleScore_0(4, 1)
    local maxScore = effectCfg.Score
    local effectScore = battle_data.GetSkillEffectScore()
    if effectScore > maxScore then
        effectScore = maxScore
    end

    local finalScore = math.round(roundFactor * (dmgScore + dieScore + effectScore))
    return math.max(0, finalScore)
end

--必须返回同一种数据类型，如整型或字符串，否则数数科技只能接收显示最先上传的数据类型值 -_-
--返回 string
--[[
玩法                pk人数      上报的战斗id
位面关卡            6v6         关卡id
竞技场              6v6         无
星际迷航            6v6         第几层，第几行，怪物所属的事件id（简单难度普通怪、史诗怪等等）
星际通缉            6v6         层数
时空穿越            6v6         关卡id
联盟boss            6v1         boss id
深空探索            6v6         地图id
虚空锦标赛          6v1         战斗力分段、boss id
遗落之境            6v6         关卡地图id
联盟战              6v6         无
]]
function GetLevelId()
    local log = require "log"
    local stageType = battle_data.stageType
    if stageType == common_pb.GameGoal then
        return tostring(battle_data.stageLv)
    elseif stageType == common_pb.Arena then --竞技场
        return "nil"
    -- elseif stageType == common_pb.Maze then --星际迷航
    --     local maze_mgr = require "maze_mgr"
    --     local layer = maze_mgr.GetCurLevel()
    --     local mazePosData = maze_mgr.GetMazePos()
    --     local row = mazePosData.row
    --     local col = mazePosData.col
    --     return tostring(layer).."#"..tostring(row).."#"..tostring(col)
    -- elseif stageType == common_pb.TheTowerOfIllusion then --星际通缉
    --     local ui_illusion_tower = require "ui_illusion_tower"
    --     return tostring(ui_illusion_tower.GetPassLevel())
    -- elseif stageType == common_pb.FactionType1
    --     or stageType == common_pb.FactionType2
    --     or stageType == common_pb.FactionType3
    --     or stageType == common_pb.FactionType4 then
    --     local factionType = stageType - common_pb.FactionType1 + 1
    --     local faction_wanted_mgr = require "faction_wanted_mgr"
    --     local levelId = faction_wanted_mgr.GetWantedDataLevel(factionType)
    --     return tostring(levelId)
    elseif stageType == common_pb.EquipEctype then --时空穿越
        return tostring(battle_data.GetEquipEctypeID())
    -- elseif stageType == common_pb.LeagueBoss then --联盟boss
    --     local boss_data = require "boss_data"
    --     local bossID = boss_data.GetCurBossID()
    --     return tostring(bossID)
    -- elseif stageType == common_pb.BraveManTried then --勇者试炼，深空试炼，深空探索
    --     local trial_mgr = require "trial_mgr"
    --     local levelId = trial_mgr.GetCurLevelID()
    --     return tostring(levelId)
    -- elseif stageType == common_pb.BrokenSpaceTime then --虚空锦标赛
    --     local domination_mgr = require "domination_mgr"
    --     local powerID = domination_mgr.GetPowerID()
    --     local monsterTeamID = domination_mgr.GetMonsterTeamID()
    --     local bossID = 0
    --     if monsterTeamID then
    --         local monsterTeam = game_scheme:monsterTeam_0(monsterTeamID)
    --         if monsterTeam then
    --             bossID = monsterTeam.MonsterId.data[0]
    --         end
    --     end
    --     return tostring(powerID).."#"..tostring(bossID)
    -- elseif stageType == common_pb.LeaActivityBoss then --活跃度boss
    --     local sociaty_active_boss_mgr = require "sociaty_active_boss_mgr"
    --     local powerID = sociaty_active_boss_mgr.GetPowerID()
    --     local monsterTeamID = sociaty_active_boss_mgr.GetMonsterTeamID()
    --     local bossID = 0
    --     if monsterTeamID then
    --         local monsterTeam = game_scheme:monsterTeam_0(monsterTeamID)
    --         if monsterTeam then
    --             bossID = monsterTeam.MonsterId.data[0]
    --         end
    --     end
    --     return tostring(powerID).."#"..tostring(bossID)
    -- elseif stageType == common_pb.Ashdungeon then --遗落之境
    --     local dungeon_mgr = require "dungeon_mgr"
    --     local stageInfo = dungeon_mgr.GetStageInfo()
    --     if stageInfo then
    --         local mapType = stageInfo.mapType
    --         local stageLevel = stageInfo.stageLevel
    --         return tostring(mapType).."#"..tostring(stageLevel)
    --     else
    --         return "nil"
    --     end
    -- elseif stageType ==  then --联盟战
    end
    return "nil"
end

function BuildBattleReportData()
    local prop = {}

    local roleName = tostring(player_mgr.GetRoleName()) or ""
    prop.role_name = roleName
    prop.battle_score = GetBattleScore()
    prop.battle_type = battle_data.stageType
    prop.battle_type_name = battleTypeName[battle_data.stageType]
    prop.battle_level_str_id = tostring(GetLevelId())
    prop.battle_round_num = battle_data.roundTimes
    prop.battle_death_num = battle_data.GetTotalDieCount()
    prop.battle_Loser_dmg = battle_data.GetLoserDmg()
    prop.battle_winner_dmg = battle_data.GetWinnerDmp()
    prop.battle_Loser_hp = battle_data.GetLoserHP()
    prop.battle_winner_hp = battle_data.GetWinnerHP()
    prop.battle_skillEffect_num = battle_data.GetSkillEffectScore()

    return prop
end

function TriggerBattleReport()
    local prop = BuildBattleReportData()
    if prop then
        local json_str = json.encode(prop)
        event.Trigger(event.GAME_EVENT_REPORT, "Battle_Score", json_str)
        -- Debug.Log("battle_score:"..json_str)
    end
end

function OnStartActionReport(msg, player)
    local report  = msg.startActionReport
end

function OnDestroyReport(msg, player)
    local report = msg.destroyReport
    player:BattleEnd()
end

function OnLogReport(msg, player)
end

function OnWeaponEnergyReport(msg, player)
    local report = msg.wpEnergyReport
    if player.WeaponPowerChangeBySegment then
        -- Debug.LogError("OnWeaponEnergyReport,caster:"..tostring(report.palID)..",SkillContext:"..tostring(SkillContext == nil)..",currentAction:"..tostring(player.currentAction == nil))
        -- if SkillContext == nil and player.currentAction == nil then
        --     -- 如果还没有技能，则为武器能量变化主动创建一个客户端技能队列来展现武器能量
        --     local action = EpisodeAction()
        --     action.caster = report.palID
        --     action.player = player
        --     player:AppendEpisode(action)
        --     player:SetCurrentAction(action)
        -- end
        -- Debug.LogError("WeaponPowerChangeBySegment,caster:"..tostring(report.palID))
        player:WeaponPowerChangeBySegment(report.palID, report.masterId, report.value, 0)
    else
        player:WeaponPowerChange(report.palID, report.masterId, report.value)
    end
end

function OnCutsceneReport(msg, player)
    player:SetMaskEnable(true)
    local report = msg.cutsceneReport

    if SkillContext ~= nil then
        FlushSkillContext(player)
    end

    local cutsceneResrouce = "art/maps/battle/cutscene/scene1shot"..report.cutsceneID ..".prefab"
    if report.cutsceneID == 0 or report.cutsceneID == 9 then
        player:SetupPreludeCutscene(cutsceneResrouce)
    elseif report.cutsceneID == 8 or report.cutsceneID == 8 then
        player:SetupPostludeCutscene(cutsceneResrouce)
	else
		player:SetupCutscene(cutsceneResrouce)
    end
end

function OnSuspendReport(msg, player)
    local report = msg.suspendReport

    if SkillContext ~= nil then
        FlushSkillContext(player)
    end

    player:SetupSuspend(report.palID, battle_config.defaultSuspend)
end


function OnMiscReport(msg, player)
    local report = msg.miscReport
end

function OnMissReport(msg, player)
    local report = msg.missReport

    local style, text = battle_parser_utility.GetMissTextStyle()

    if style then
        if SkillContext ~= nil then
            -- 技能击中时挂buff
            local descript = AcquireDescriptor(report.palID)
            if descript.miss == nil then
                descript.miss = {}
            end
            table.insert(descript.miss, {missPal = report.palID})
        end
    end
end

function OnSdDamageReport(msg, player)
    local report = msg.sddmgincrReport
    if report then
        local common_new_pb = require "common_new_pb"
		if report.stageType == common_new_pb.LeaActivityBoss then
			-- local sociaty_active_boss_mgr = require "sociaty_active_boss_mgr"
			-- sociaty_active_boss_mgr.UpdateBattleOverData(report)
		else
			-- 虚空主宰数据
			-- local domination_mgr = require "domination_mgr"
			-- domination_mgr.UpdateBattleOverData(report)
    	end
	end
end

--抵挡次数
function OnResistCntReport(msg, player)
    local report = msg.resistCntReport
    if report then
        
        -- log.Error("战报抵挡次数!!!!!!" .. report.palID.. "-".. report.curResistCnt.. "-".. report.maxResistCnt)
        AddRolePropChange(player, report.palID, 0, ResistCntId, report.curResistCnt, report.maxResistCnt)
    end
end

--设置初始battledata战斗状态数值
function OnSetInitBatlleData(msg, player)
    local report = msg.setInitBatlleDataReport
    if not report then
        return
    end
    --设置当前回合数
    battle_manager.SetLastRound(report.round)

    --设置英雄数据
    local pals = report.pals
    local heroInfo
    for key, plaData in pairs(pals) do
        heroInfo = battle_data.GetHeroByID(plaData.palID)
        if not heroInfo then
            --如果没有英雄信息代表已经死亡
            battle_data.RegisterHero(plaData.palID, plaData.heroID, plaData.pos, 0,0,0,0, plaData.lv,
                                    nil, plaData.starLv, nil,0,0,0,nil)
            heroInfo = battle_data.GetHeroByID(plaData.palID)
            heroInfo.statistics.isDead = true
            --设置战斗布局的空位占位信息(切入战斗时，已死亡的英雄需要占位)
            if player.SetLayoutOccupiedPosition then
                player:SetLayoutOccupiedPosition(plaData.pos)
            end
        end
        heroInfo.statistics.damage = plaData.damage
        heroInfo.statistics.heal = plaData.heal
        heroInfo.statistics.hurt = plaData.hurt
        heroInfo.statistics.sp = plaData.sp
    end
    -- print("<color=#55ff88>设置战斗信息战报：</color>", json.encode(report, { indent = true }))
end

ReportTable = {
    [tbs_pb.ReportType_BuildReport] = OnBuildReport,
    [tbs_pb.ReportType_PalDieReport] = OnPalDieReport,
    [tbs_pb.ReportType_RoundReport] = OnRoundReport,
    [tbs_pb.ReportType_UseSkillReport] = OnUseSkillReport,
    [tbs_pb.ReportType_AddBuffReport] = OnAddBuffReport,
    [tbs_pb.ReportType_RemoveBuffReport] = OnRemoveBuffReport,
    [tbs_pb.ReportType_NumericReport] = OnNumericReport,
    [tbs_pb.ReportType_GameOverReport] = OnGameOverReport,
    [tbs_pb.ReportType_StartActionReport] = OnStartActionReport,
    [tbs_pb.ReportType_DestroyReport] = OnDestroyReport,
    [tbs_pb.ReportType_LogReport] = OnLogReport,
    [tbs_pb.ReportType_WeaponEnergyReport] = OnWeaponEnergyReport,
    [tbs_pb.ReportType_CutsceneReport] = OnCutsceneReport,
    [tbs_pb.ReportType_SuspendReport] = OnSuspendReport,
    [tbs_pb.ReportType_SkillContainerReport] = OnSkillContainerReport,
    [tbs_pb.ReportType_MiscReport] = OnMiscReport,
    [tbs_pb.ReportType_UpdatePropReport] = OnUpdatePropReport,
    [tbs_pb.ReportType_ShieldReport] = OnShieldReport,
    [tbs_pb.ReportType_SummonedReport] = OnSummonedReport,
    [tbs_pb.ReportType_summonedNumberReport] = OnSummonedNumberReport,
    [tbs_pb.ReportType_MissReport] = OnMissReport,
    [tbs_pb.ReportType_SdDmgIncrReport] = OnSdDamageReport,
    [tbs_pb.ReportType_ResistCntReport] = OnResistCntReport,
    [battle_data.ReportType_SetInitBatlleData] = OnSetInitBatlleData, --设置初始battledata战斗状态数值，
}


function AcquireDescriptor(target)
    local segment = SkillContext.segment[#SkillContext.segment]
    local descript = segment.descriptor[target]
    if descript == nil then
        descript = {}
        descript.damageHp = 0
        descript.healHp = 0
        descript.consumeMp = 0
        descript.generateMp = 0
        descript.useSp = 0
        descript.generateSp = 0
        descript.shieldChanged = 0
        descript.removeShield = false
        descript.hurtSource = {}
        --数值的变化并不一定由当前技能的释放者引起，此处保存谁改变了数值，例如谁释放的技能
        --tbs_pb -> NumericReport -> palID --谁改变了数值，例如谁释放的技能
        --tbs_pb -> NumericReport -> targets --谁的数值被改变了
        descript.casterID = nil

        segment.descriptor[target] = descript
    end
    return descript
end

-- 统计每个技能对每个目标造成的多段伤害
-- skill表中Multistage标识是客户端表现的多段伤害
--          nSkillRepeatCnt标识为服务器实现的多段伤害，但此伤害可能随机段数，随机目标，针对某一随机到的目标未必是多段伤害
-- 分析计算每个目标所受到的服务器多段伤害（真多段伤害）值
function CalculateSkillMultiSegmentHurt()
    if not const.Open_Battle_Damage_MultiSegment_Statistics then
        return
    end

    local segmentIndex = 1
    local segmentCount = #SkillContext.segment

    -- 遍历技能的每一段伤害
    for segmentIndex = 1,segmentCount do
        local segmentData = SkillContext.segment[segmentIndex]
        -- 遍历每一个受攻击目标
        for palID, descript in pairs(segmentData.descriptor) do
            if palID ~= SkillContext.casterID and descript.damageHp ~= 0 then
                local multiSegmentHurt = SkillContext.multiSegmentHurt
                if multiSegmentHurt == nil then
                    multiSegmentHurt = {}
                    SkillContext.multiSegmentHurt = multiSegmentHurt
                end
                local targetMultiSegmentHurt = multiSegmentHurt[palID]
                if targetMultiSegmentHurt == nil then
                    targetMultiSegmentHurt = {
                        minSegment = {segmentIndex = segmentIndex, damageHp = descript.damageHp},
                        maxSegment = {segmentIndex = segmentIndex, damageHp = descript.damageHp},
                        damageHp = {[segmentIndex] = descript.damageHp},
                        multiSegmentFloatText = nil,
                        isRepeatSkill = false,
                    }
                    multiSegmentHurt[palID] = targetMultiSegmentHurt
                else
                    targetMultiSegmentHurt.isRepeatSkill = true
                    local maxSegment = targetMultiSegmentHurt.maxSegment
                    if maxSegment.segmentIndex < segmentIndex then
                        maxSegment.segmentIndex = segmentIndex
                        maxSegment.damageHp = maxSegment.damageHp + descript.damageHp
                        targetMultiSegmentHurt.damageHp[segmentIndex] = maxSegment.damageHp
                    end
                end
            end
        end
    end
end

function IsEnableSkillDimEffect( skillCfg )
    -- 判断技能是否遮罩（仅为2的时候有效，其他时候无效）
    if skillCfg and skillCfg.nSkillMask == 2 then
        return true
    end

    return false
end

function IsReviveSkill( skillCfg )
    if skillCfg and skillCfg.specialSkill == 1 then
        -- 特殊技能标识(不配视为正常技能，1复活）
        return true
    end

    return false
end

function GetSkillNameBySkillId(heroID, skinID, skillID)
    local heroCfg = game_scheme:Hero_0(heroID)
    if not heroCfg then
        log.Error("can't find Config(Hero) by id = ", heroID)
        return
    end
    for i = 0, heroCfg.heroSkillId.count - 1 do
        local v = heroCfg.heroSkillId.data[i]
        local cfg_skill = game_scheme:HeroSkill_0(v, 1)
        local cfg_skinheroskill = skinID and skinID > 0 and game_scheme:SkinHeroSkill_0(skinID, v, 1)

        local nameID = GetSkillSkill(cfg_skill,skillID)
        if nameID then
            return cfg_skinheroskill and cfg_skinheroskill.nameID or nameID
        end
        cfg_skill = game_scheme:HeroSkill_0(v, 2)
        cfg_skinheroskill = skinID and skinID > 0 and game_scheme:SkinHeroSkill_0(skinID, v, 2)
        if cfg_skill then
            local nameID2 = GetSkillSkill(cfg_skill,skillID)
            if nameID2 then
                return cfg_skinheroskill and cfg_skinheroskill.nameID or nameID2
            end
            cfg_skill = game_scheme:HeroSkill_0(v, 3)
            cfg_skinheroskill = skinID and skinID > 0 and game_scheme:SkinHeroSkill_0(skinID, v, 3)
            if cfg_skill then
                local nameID3 = GetSkillSkill(cfg_skill,skillID)
                if nameID3 then
                    return cfg_skinheroskill and cfg_skinheroskill.nameID or nameID3
                end
            end
        end
    end
end

function GetSkillSkill(cfg_skill, skillID)
    local skillParam = util.SplitString(cfg_skill.paramGroup,";")
    for k,v in pairs(skillParam) do
        local skillParam2 = util.SplitString(skillParam[k],"#")
        if tonumber(skillParam2[1]) == skillID then
            return cfg_skill.nameID
        end
    end
    return nil
end

function FlushSkillContext(player)
    local mainSkill = SkillContext.skillID ~= nil
    local segment = 0
    local repeatSkill = false

    local multiSegmentFloatText
    local multiStyle

    local skillCfg
    if mainSkill then
        skillCfg = game_scheme:Skill_0(SkillContext.skillID)
        local data
        local weaponData
        if SkillContext.casterID < 13 then
            data = battle_data.GetHeroByID(SkillContext.casterID)
            if not data then
                log.Error("data is null " ,SkillContext.casterID)
            end
        else
            weaponData = battle_data.GetWeaponAdorn(SkillContext.casterID)
        end

        local skillRes = battle_parser_utility.GetSkillRes(skillCfg, battle_config.defaultAtk,data and data.skinID or 0)
        if weaponData then
            local weaponSkin = game_scheme:AccessoryAdorn_0(weaponData)
            if weaponSkin then
                skillRes = weaponSkin.strPath
            end
        end
        segment = battle_config.GetConfigField(skillCfg, "Multistage", 0)
        if data and data.skinID and data.skinID>0 then
            local skinCfg = game_scheme:Skin_0(data.skinID)
            if skinCfg then
                local SkillNum = battle_config.GetConfigField(skinCfg, "SkillNum")
                if SkillNum then
                    local skinSkillCfg = game_scheme:SkinSkill_0(SkillContext.skillID)
                    segment = battle_config.GetConfigField(skinSkillCfg, "Multistage"..SkillNum, 0)
                end
            end
        end
        repeatSkill = battle_config.GetConfigField(skillCfg, "nSkillRepeatCnt", 1) > 1
        -- log.Error("parse skill, casterID:", SkillContext.casterID, ",skillRes:", skillRes, SkillContext.skillID, SkillContext.specialSkill, ",segment count:", #SkillContext.segment)
        local suspend = battle_config.GetSuspendResBeforeSkill(SkillContext.casterID, skillRes)
        if suspend then
            player:SetupSuspend(SkillContext.casterID, suspend)--------技能播放前插入暂停
        end

        local outsideskillRes = battle_config.GetConfigField(skillCfg, "OutsideSceneSkill", "")
        -- --如果有场外大招，在这里插入到队列
        if outsideskillRes ~= "" then
            local skillInfo = battle_config.GetBubbleSillDetail(SkillContext.skillID)
            local skillname = lang.Get(skillCfg.strName)
            local skilldesc = lang.Get(skillCfg.strDesc)
            -- player:InsertSkillDetail(SkillContext.casterID,skillInfo.name,skillname,skilldesc,skillInfo.image)
            -- player:InsertOutsideSkill(SkillContext.casterID, outsideskillRes)
        end
        -- who cast what skill
        player:BeginSkill(SkillContext.casterID, skillRes, SkillContext.reportIndex) -------创建技能episode容器

        battle_data.BeginSkill() -------重置skillLightTargets容器

        if IsEnableSkillDimEffect(skillCfg) then
            local casterID = SkillContext.casterID
            battle_parser_utility.AppendDimEffect(player, casterID, {[1] = casterID}, EpisodeDim.Operation.Light, 1) -------添加战斗变暗特效
        end
        
        --2022-4-7加入技能名字
        if data then
            local cfg_skillName = battle_config.GetBattleSkillName(data)
            if cfg_skillName then
                --2022-9-27 新增代码player.InsertNormalSkillDetail
                if player.InsertNormalSkillDetail then
                    local nameID = skillCfg.strName
                    if nameID == 0 then
                        nameID = GetSkillNameBySkillId(data.heroID,data.skinID,SkillContext.skillID)
                    end
                    if not nameID or nameID == 0 then
                        local heroCfg = game_scheme:Hero_0(data.heroID)
                        local cfg_skill = heroCfg and game_scheme:HeroSkill_0(heroCfg.heroSkillId.data[0], 1)
                        nameID = cfg_skill and cfg_skill.nameID or lang.KEY_ACTOR_ROLE_NAMENILL
                    end
                    local keyColors = {}
                    if cfg_skillName.colorOutline ~= nil and cfg_skillName.colorUp ~= nil or cfg_skillName.colorDown ~= nil then
                        keyColors[1] = color_palette.HexToColor(cfg_skillName.colorOutline)
                        keyColors[2] = color_palette.HexToColor(cfg_skillName.colorUp)
                        keyColors[3] = color_palette.HexToColor(cfg_skillName.colorDown)
                        local imageName = cfg_skillName.imageName or ""
                        player:InsertNormalSkillDetail(SkillContext.casterID, skillRes, lang.Get(nameID),keyColors,imageName)
                    else
                        log.Error("[buff]SkillName load color data Error, please check BattleSkillName.csv")
                    end
                else
                    local heroCfg = game_scheme:Hero_0(data.heroID)
                    local cfg_skill = heroCfg and game_scheme:HeroSkill_0(heroCfg.heroSkillId.data[0], 1)
                    local skillName = lang.Get(cfg_skill and cfg_skill.nameID or lang.KEY_ACTOR_ROLE_NAMENILL)
                    local keyColors = {}
        
                    if cfg_skillName.colorOutline ~= nil and cfg_skillName.colorUp ~= nil or cfg_skillName.colorDown ~= nil then
                        keyColors[1] = color_palette.HexToColor(cfg_skillName.colorOutline)
                        keyColors[2] = color_palette.HexToColor(cfg_skillName.colorUp)
                        keyColors[3] = color_palette.HexToColor(cfg_skillName.colorDown)
                        local imageName = cfg_skillName.imageName or ""
                        player:InsertSkillDetail(SkillContext.casterID,skillName,keyColors,imageName)
        
                    else
                        log.Error("[buff]SkillName load color data Error, please check BattleSkillName.csv")
                    end
                end
            end
        end
        -- skill bubble
        battle_parser_utility.AppendBubbleSkillPackage(player, SkillContext.casterID, SkillContext.skillID)-------添加战斗对话
    
        -- if outsideskillRes ~= "" then
        --     player:InsertHightLight(SkillContext.casterID,false)
        -- end
    end

    local segmentIndex = 1
    local segmentCount = #SkillContext.segment
    -- 如果是服务器真实多段伤害，需先统计每个目标是否受到多段攻击
    if repeatSkill and segment == 0 and const.Open_Battle_Damage_MultiSegment_Statistics then
        CalculateSkillMultiSegmentHurt() ------计算每个目标所受到的服务器多段伤害
    end

    local deadCount = 0
    local defaultDieRes = battle_config.GetDefaultDieBundleName()
    --假死timeline
    local defaultFakeDeadRes = battle_config.GetDefaultFakeDeadBundleName()
    --当前技能中每个英雄的击杀数量
    local palInfoSet = {}
    local winningstreakDelayTime = 1
    
    local bubbleDead = false
    local hasDescriptor = false
    local tempTargets = {}
    for segmentIndex = 1,segmentCount do
        local segmentData = SkillContext.segment[segmentIndex]
        if segmentData and segmentData.targets and #segmentData.targets > 0 then
            for i = 0, #segmentData.targets do
                table.insert(tempTargets, segmentData.targets[i])
            end
        end
    end

    --多段伤害部分
    for segmentIndex = 1,segmentCount do
        local segmentData = SkillContext.segment[segmentIndex]

        -- local strTargets = ""
        -- if(segmentData.targets ~= nil) then
        --     strTargets = table.concat(segmentData.targets, ",")
        -- end
        -- Debug.LogError("SkillContext.casterID:"..tostring(SkillContext.casterID)..'segment targets:'..strTargets..",segmentIndex:"..segmentIndex)

        if player.AppendSegmentTargetNew then
            player:AppendSegmentTargetNew(segmentData.targets, tempTargets)
        else
            player:AppendSegmentTarget(segmentData.targets)
        end
        
        if IsEnableSkillDimEffect(skillCfg) then
            battle_parser_utility.AppendDimEffect(player, SkillContext.casterID, segmentData.targets, EpisodeDim.Operation.Light, segmentIndex)-----添加多段伤害变暗特效
        end
        if IsReviveSkill(skillCfg) then
            -- log.Error("IsReviveSkill:", skillCfg)
            battle_parser_utility.AppendReviveEpisode(player, SkillContext.casterID, segmentData.targets, segmentIndex)-----添加多段伤害变暗特效
        end

        -- print(tostring(SkillContext.skillID)..",segment idx:"..segmentIndex..",descriptor count:"..util.TableCount(segmentData.descriptor))

        local buffSet = {}
        local buffHitSet = {}

        -- Debug.LogError("descriptor count:"..util.TableCount(segmentData.descriptor))
        --黑武士多段攻击特效在免疫的情况下未正确显示，特殊处理
        if table.empty(segmentData.descriptor) then
            if SkillContext.casterID == 13 then
                local modulCfg = battle_data.GetHeroModuleCfg(SkillContext.casterID)
                local hitRes = battle_parser_utility.GetHitRes(modulCfg, battle_config.defaultHit, skillCfg)
                player:AppendAction(SkillContext.casterID, hitRes, segmentIndex - 1) -----多段伤害为0时，添加新的Episode
            end
        end
        for palID, descript in pairs(segmentData.descriptor) do
                hasDescriptor = true

            local modulCfg = battle_data.GetHeroModuleCfg(palID)

            local casterID = (descript.casterID == nil or descript.casterID == 0) and SkillContext.casterID or descript.casterID
            local hudCasterID = casterID
            if descript.dead then
                deadCount = deadCount + 1
                battle_parser_utility.AddPalKill(palInfoSet, casterID)-----击杀数累计

                if deadCount > 1 then
                    defaultDieRes = battle_config.GetDefaultDieBundleName(true)
                end
                -- Debug.LogWarning("play dead audio:"..defaultDieRes)

                battle_data.OnPalDie(palID)-----击杀数累计
            end

            -- Debug.LogError("parse skill, palID:"..tostring(palID)..",casterID:"..tostring(casterID))

            local hitRes = battle_parser_utility.GetHitRes(modulCfg, battle_config.defaultHit, skillCfg)
            local dieRes = battle_config.GetConfigField(modulCfg, "die", defaultDieRes)

            if palID ~= SkillContext.casterID and (palID >= 100 or descript.damageHp ~= 0) then
                
                if palID >= 100 then
                    local summonObject = battle_data.GetHeroByID(palID)
                    if(summonObject)then
                        dieRes = battle_config.GetConfigField(summonObject.moduleCfg, "die", defaultDieRes)
                    end
                 end
                if descript.dead then
                    local roleWinningStreak = nil
                    if casterID then
                        roleWinningStreak = WinningStreakContext[casterID]
                    end
                    local tmpWinningStreak = roleWinningStreak
                    if tmpWinningStreak == nil then
                        tmpWinningStreak = 0
                    end
                    if palID <= 12 then
                        tmpWinningStreak = tmpWinningStreak + 1
                    end
                    if segment ~= 0 then
                        -- Debug.LogWarning(palID .. " is dead with:" .. defaultDieRes)
                        battle_parser_utility.AppendHitAndDieForSegments(player, palID, segment, modulCfg, defaultDieRes, skillCfg) -----多段伤害死亡
                        bubbleDead = battle_parser_utility.AppendBubbleDiePackage(player, palID, bubbleDead) -----死亡对话信息

                        -- 没有1连胜飘字
                        if tmpWinningStreak > 1 then
                            local palKillCount = battle_parser_utility.GetPalKill(palInfoSet, casterID)
                            battle_parser_utility.AppendWinningStreak(player, casterID, palID,
                             tmpWinningStreak, segment, (palKillCount - 1) * winningstreakDelayTime) -----连胜飘字信息
                        end
                    else
                        -- Debug.LogWarning(palID .. " is dead with:" .. dieRes)
                        battle_parser_utility.AppendDieForSegment(player, palID, segmentIndex, dieRes)-----多段死亡
                        bubbleDead = battle_parser_utility.AppendBubbleDiePackage(player, palID, bubbleDead) -----死亡对话信息

                        -- 没有1连胜飘字
                        if tmpWinningStreak > 1 then
                            local palKillCount = battle_parser_utility.GetPalKill(palInfoSet, casterID)
                            battle_parser_utility.AppendWinningStreak(player, casterID, palID,
                             tmpWinningStreak, segmentIndex, (palKillCount - 1) * winningstreakDelayTime)-----连胜飘字信息
                        end
                    end
                    if tmpWinningStreak ~= roleWinningStreak and casterID ~= nil then
                        WinningStreakContext[casterID] = tmpWinningStreak
                    end
                    -- dump(segmentData)
                elseif SkillContext.casterID ~= nil and descript.needHit then
                    if descript.fakeDead then
                        --log.Error("假死处理：fakeDead", palID)
                        if segment ~= 0 then
                            battle_parser_utility.AppendHitAndFakeDeadForSegments(player, palID, segment, modulCfg, defaultFakeDeadRes, skillCfg) -----添加假死多段伤害
                        else
                            battle_parser_utility.AppendFakeDeadForSegments(player, palID, segmentIndex, defaultFakeDeadRes)  -----假死：多段伤害为0时，添加新的Episode
                        end
                    else
                    	if segment ~= 0 then
                        	battle_parser_utility.AppendHitForSegments(player, palID, segment, modulCfg, skillCfg) -----添加多段伤害
	                    else
	                        player:AppendAction(palID, hitRes, segmentIndex - 1) -----多段伤害为0时，添加新的Episode
	                    end
                    end
                end
            elseif palID == SkillContext.casterID and descript.damageHp ~= 0  then
                if descript.dead then 
                    -- 自己发的技能，触发了别人的buff，把自己反弹死了，要播死亡效果
                    battle_parser_utility.AppendDieForSegment(player, palID, segmentIndex, dieRes) ----------自己死亡处理
                elseif descript.fakeDead then
                    battle_parser_utility.AppendFakeDeadForSegments(player, palID, segmentIndex, defaultFakeDeadRes)  -----添加假死
                end
            end

            local deltaHp = descript.damageHp + descript.healHp
            local deltaMp = descript.consumeMp + descript.generateMp
            local deltaSp = descript.useSp + descript.generateSp
            -- if descript.removeShield then
            --     -- 直接移除所有护盾值
            --     local tmpSp = battle_data.GetSp(palID)
            --     if tmpSp > 0 then
            --         deltaSp = -tmpSp
            --     end
            -- end
            --!!!!!!! isLargeHp 特殊处理，传给C#值固定为false，在lua端，当isLargeHp为true时，所有hp，maxHp，和伤害恢复都应该乘0.001，保证显示正确
            deltaHp = battle_data.handleLargeHp(palID, deltaHp)
            deltaHp = battle_data.clamp_hp(palID, deltaHp)

            --伤害大于血量+护盾导致死亡时，服务器并未下发护盾减少的数据，因此需要特殊处理
            if descript.dead then
                deltaSp = deltaSp - battle_data.GetSp(palID)
            end
            battle_data.count_dead(palID, descript.dead)
            battle_data.count_hp(palID, deltaHp)
            --local hero = battle_data.GetHeroByID(palID)
            --if hero and deltaHp ~= 0 then
                --print("<color=#55ff00>血量属性变化：</color>", "index", reportIndex, "palID:", palID, " deltaHp:", deltaHp, " hp:", hero.statistics.hp)
                --print("<color=#55ffff>阵容属性：</color>", "index", reportIndex, "palID:", palID, " info:", json.encode(hero, { indent = true }))
            --end
            --local shieldValue = descript.shieldChanged

            -- if deltaHp ~= 0 or descript.damageHp ~= 0 or descript.healHp ~= 0 then
            --     Debug.LogError("segment:"..segment..",deltaHp:"..tostring(deltaHp)..",damageHp:"..tostring(descript.damageHp)..",healHp:"..tostring(descript.healHp))
            -- end

            if segment ~= 0 then
                battle_parser_utility.AppendHudAverage(player, palID, segment, deltaHp, deltaMp, deltaSp, hudCasterID or 0, descript.damageHp)--血量变化
                battle_parser_utility.AppendCountDamage(player, palID, descript.hurtSource)--血量变化
                battle_parser_utility.ApppendFloatTextAverage(player, palID, segment, 
                    battle_parser_utility.GetFloatTextStyle(casterID, palID, descript.critical,descript.breakTrend),
                    descript.damageHp, battle_parser_utility.damageHpOffset.y)--飘字
                battle_parser_utility.ApppendFloatTextAverageWithPrefix(player, palID, segment, "heal", descript.healHp, "+")--飘字

                if deltaSp ~= 0 and not descript.removeShield then
                    local prefix = battle_parser_utility.GetFloatTextSign(deltaSp)
                    if deltaSp < 0 then
                        battle_parser_utility.ApppendFloatTextAverageWithPrefix(player, palID, segment, "shield", 
                            deltaSp, prefix)
                    else
                        player:AppendFloatText(palID, "shield", prefix..deltaSp, 0, 0, 30, segmentIndex - 1)
                    end
                end

                -- 技能通过效果实现。暂不能区分技能和效果额外变化，先屏蔽
                -- if deltaMp ~= 0 then
                --     local prefix = battle_parser_utility.GetFloatTextSign(deltaMp)
                --     battle_parser_utility.ApppendFloatTextAverageWithPrefix(player, palID, segment, "mp", 
                --         deltaMp, prefix, 50)
                -- end
            else
                if deltaHp ~= 0 or deltaMp ~= 0 or deltaSp ~= 0 then
                    -- if(deltaHp ~= 0) then
                        -- Debug.LogError("parse skill, palID:"..tostring(palID)..",segmentIndex:"..tostring(segmentIndex)..",deltaHp:"..tostring(deltaHp)..",from:"..tostring(nil))
                    -- end

                    if version_mgr.CheckSvnTrunkVersion(25380) then
                        player:AppendHud(palID, deltaHp, deltaMp, deltaSp, segmentIndex - 1, hudCasterID or 0, descript.damageHp or 0)
                        battle_parser_utility.AppendCountDamage(player, palID, descript.hurtSource)
                    else
                        player:AppendHud(palID, deltaHp, deltaMp, deltaSp, segmentIndex - 1)
                        battle_parser_utility.AppendCountDamage(player, palID, descript.hurtSource)
                    end
                end

                if descript.damageHp ~= 0 then
                    local style = battle_parser_utility.GetFloatTextStyle(casterID, palID, descript.critical,descript.breakTrend)
                    player:AppendFloatText(palID, style, tostring(descript.damageHp), 0, 0, battle_parser_utility.damageHpOffset.y, segmentIndex - 1)

                    -- 服务器多段伤害，且不是自己的技能对自身的伤害
                    if repeatSkill and palID ~= SkillContext.casterID and const.Open_Battle_Damage_MultiSegment_Statistics then
                        local targetMultiSegmentHurt
                        local multiSegmentHurt = SkillContext.multiSegmentHurt
                        if multiSegmentHurt then
                            targetMultiSegmentHurt = multiSegmentHurt[palID]
                        end
                        if targetMultiSegmentHurt == nil then
                            Debug.LogError("can not find multiSegmentHurt info,skillID"..tostring(SkillContext.skillID)..",caster:"..palID..",skill caster:"..tostring(SkillContext.casterID))
                        end
                        -- 当前目标受到多段攻击
                        if targetMultiSegmentHurt and targetMultiSegmentHurt.isRepeatSkill then
                            if targetMultiSegmentHurt.minSegment.segmentIndex == segmentIndex then
                                targetMultiSegmentHurt.multiSegmentFloatText = battle_parser_utility.AppendMultiSegmentFloatText(player)
                                multiStyle = "multistart"
                            elseif targetMultiSegmentHurt.maxSegment.segmentIndex > segmentIndex then
                                multiStyle = "multikeep"
                            else
                                multiStyle = "multiend"
                            end
                            battle_parser_utility.AppendMultiSegmentFloatTextInfo(player, palID, segmentIndex, style, 
                                tostring(targetMultiSegmentHurt.damageHp[segmentIndex]), battle_parser_utility.multiDamageHpOffset.y, multiStyle, 
                                targetMultiSegmentHurt.multiSegmentFloatText)
                        end
                    end
                end

                if descript.healHp ~= 0 then
                    player:AppendFloatText(palID, "heal", "+"..tostring(descript.healHp), 0, 0, 30, segmentIndex - 1)
                end

                if deltaSp ~= 0 and not descript.removeShield then
                    local prefix = battle_parser_utility.GetFloatTextSign(deltaSp)
                    player:AppendFloatText(palID, "shield", prefix..deltaSp, 0, 0, 30, segmentIndex - 1)
                end

                -- 技能通过效果实现。暂不能区分技能和效果额外变化，先屏蔽
                -- if deltaMp ~= 0 then
                --     local prefix = battle_parser_utility.GetFloatTextSign(deltaMp)
                --     player:AppendFloatText(palID, "mp", prefix..deltaMp, 0, 0, 50, segmentIndex - 1)
                -- end
            end

            if descript.addbuff ~= nil then
                local statisticBuff = true
                for _, t in pairs(descript.addbuff) do
                    local buffId = t[1]
                    if mainSkill == false or buffSet[buffId] then
                        statisticBuff = false
                    else
                        buffSet[buffId] = true
                        statisticBuff = true
                    end
                    -- if const.Open_Battle_SkillEffect_Log then
                    --     Debug.LogError("idx:".._..",segmentIndex:"..segmentIndex..
                    --         ",skillID:"..tostring(SkillContext.skillID).."caster:"..SkillContext.casterID..",target:"..palID..",buffId:"..
                    --         buffId..",statistic:"..tostring(statisticBuff))
                    -- end
                    battle_parser_utility.AppendAddBuffPackage(player, palID, buffId, t[2], statisticBuff,t[3],segmentIndex - 1)
                end
            end
    
            --闪避飘字
            if descript.miss ~= nil then
                local style, text = battle_parser_utility.GetMissTextStyle()
                for _, t in pairs(descript.miss) do
                    player:AppendFloatText(t.missPal, style, text, 0, battle_parser_utility.damageHpOffset.x, battle_parser_utility.damageHpOffset.y)
                end
            end

            if descript.removeBuff ~= nil then
                for _, t in pairs(descript.removeBuff) do
                    battle_parser_utility.AppendRemoveBuffPackage(player, palID, t[1], t[2],segmentIndex - 1)
                end
            end

            if descript.modifyProps ~= nil then
                local propsLen = #descript.modifyProps
                -- 必须先设置最大血量，再设置血量，依赖服务器消息顺序
                for i=1,propsLen do
                    local t = descript.modifyProps[i]
                    if not descript.fakeDead and t.propID == prop_pb.PAL_PROP_HP then
                        battle_parser_utility.AppendModifyMaxHp(player, t.palID, t.propValue, segmentIndex)
                    elseif t.propID == prop_pb.PAL_PROP_REMAIN_HP then
                        if not descript.fakeDead then
                            battle_parser_utility.AppendModifyHp(player, t.palID, t.propValue, segmentIndex)
                        end
                            --防御值改变
                    elseif t.propID == prop_pb.PAL_PROP_DEFENCE or t.propID == ResistCntId then
                        --log.Error("<color=#ffffff>防御值修改</color>",t.propID,"防御值!!!!!!", t.propValue)
                        AppendRolePropChange(player, t.palID, 0, t.propID, t.propValue, t.propValue2, t.propValue3)
                    end
                end
            end
    
            if descript.buffHit ~= nil then
                local statisticBuff = true
                for _, buffId in pairs(descript.buffHit) do
                    if mainSkill == false or buffHitSet[buffId] then
                        statisticBuff = false
                    else
                        buffHitSet[buffId] = true
                        statisticBuff = true
                    end
                    -- if const.Open_Battle_SkillEffect_Log then
                    --     Debug.LogError("idx:".._..",buffHit segmentIndex:"..segmentIndex..
                    --         ",skillID:"..tostring(SkillContext.skillID).."caster:"..SkillContext.casterID..",target:"..palID..",buffId:"..
                    --         buffId..",statistic:"..tostring(statisticBuff))
                    -- end
                    battle_parser_utility.AppendBuffHitPackage(player, palID, buffId, 1, statisticBuff, SkillContext.casterID or descript.casterID, segmentIndex - 1)
                end
            end
            
            -- local winninstreakSegment = segment
            -- if segment == 0 then
            --     winninstreakSegment = segmentIndex
            -- end
            -- if descript.addWinningStreak ~= nil then
            --     for _,winningstreak in ipairs(descript.addWinningStreakList) do
            --         battle_parser_utility.AppendWinningStreak(player, SkillContext.casterID, winningstreak[1], 
            --             winningstreak[2], winninstreakSegment)
            --     end
            -- end
        end
    end

    -- e.g. 浪客少女追击技能被免伤时，变成了 0 段伤害，这里需要处理
    if segmentCount > 0 and hasDescriptor == false then
        -- 给自己加一个无表现的血条表现
        player:AppendHud(SkillContext.casterID, 0, 0, 0, 0, 0, 0)
    end

    if mainSkill then
        -- 技能播放完成时自动变暗，此处不通过代码设置，屏蔽
        -- local casterID = SkillContext.casterID
        -- battle_parser_utility.AppendDimEffect(player, casterID, {[1] = casterID}, EpisodeDim.Operation.Dim, segmentCount)

        -- local lightTargets = battle_data.GetLightTargetList()
        -- battle_parser_utility.AppendDimEffect(player, casterID, lightTargets, EpisodeDim.Operation.Dim, segmentCount)

        -- log.Error("parse skill, EndSkill")
        player:EndSkill()
        battle_data.EndSkill()
    end
    SkillContext = nil
end