local require = require
local string = string
local typeof = typeof

local GameObject = CS.UnityEngine.GameObject
local Text = CS.UnityEngine.UI.Text

module("casualgame_progress")

local http_inst = require "http_inst"
local dkjson = require "dkjson"	
local util = require "util"
local ui_login_progress = require "ui_login_progress"
local log = require "log"
local cs_coroutine = require "cs_coroutine"
local loadingText
local percent = 0
local _finishSize = 0
local _totalSize = 0


function InitUI()
    local update_config_mgr = require "update_config_mgr"
    local init_flow = update_config_mgr.IsUseInitFlow()
    if not init_flow then
        loadingText = GameObject.Find("StartupCanvas/Tips")
        if not util.IsObjNull(loadingText) then
            loadingText = loadingText:GetComponent(typeof(Text))
        end
        if not util.IsObjNull(loadingText) then
            loadingText.enabled = true
            loadingText.text = "Loading..."
        end
    end
end
function ShowProgress(finishSize,totalSize,finishCount,totalCount)
    local update_config_mgr = require "update_config_mgr"
    local init_flow = update_config_mgr.IsUseInitFlow()
    if init_flow then
        ShowFlowProgress(finishSize,totalSize,finishCount,totalCount)
    else
        if not ui_login_progress:GetTinyUp() then
            ui_login_progress:SetTinyUp(true)
        end

        _finishSize = ((finishSize/1024)/1024)
        _totalSize = ((totalSize/1024)/1024)
        local text = string.format("Loading %.2fMB/%.2fMB", _finishSize, _totalSize)
        ui_login_progress:SetPercentTips(text)

        percent = finishCount/totalCount
        -- 占据10%
        percent = ui_login_progress:GetExcUpPercent() + percent * 0.1
        ui_login_progress:SetPercent(percent)
    end
end

function ShowFlowProgress(finishSize,totalSize,finishCount,totalCount)
    if totalCount <= 0 or totalSize <= 0 then return end
    percent = finishCount/totalCount
    _finishSize = ((finishSize/1024)/1024)
    _totalSize = ((totalSize/1024)/1024)
    local loadingStr = string.format(" %.2fMB/%.2fMB",_finishSize,_totalSize)
    local init_progress_ui = require "init_progress_ui"
    init_progress_ui:updateDetailPercent(init_progress_ui.init_progress_detail.init_minigame_preload, percent, loadingStr)
end