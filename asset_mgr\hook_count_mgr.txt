local require = require

module("hook_count_mgr")

local hook_count = require("hook_count")
local hook_count_mgr = {}

function GetCounter(mark)
    local counter = hook_count_mgr[mark] or hook_count(mark)
    return counter
end

function Registe()
    local event = require("event")
    event.Register(
        "HOOK_COUNT_EVENT",
        function(evname, mark, methodname, ...)
            local counter = GetCounter(mark)
            if methodname == "Save" then
                return counter:DebugCsv(...)
            end

            local mthd = counter[methodname]
            if mthd then
                return mthd(counter, ...)
            end
        end
    )
end

function InitRequire()
    Registe()
end

InitRequire()
