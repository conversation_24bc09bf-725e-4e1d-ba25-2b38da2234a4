local os = require "os"


local halloween_activity_slot_machine_history_test_data = {}

local single_history_data = {
    time_stamp = 0,
    result_id = 0,
    count = 1,
    pattern_group_randomed = {},
}

for i = 1, 15 do

    local data = {}
    data.time_stamp = os.time() + i * 60 * 60
    data.result_id = i%13 + 1
    data.count = i%5
    data.pattern_group_randomed = {1,2,3}

    halloween_activity_slot_machine_history_test_data[i] = data
end



return halloween_activity_slot_machine_history_test_data