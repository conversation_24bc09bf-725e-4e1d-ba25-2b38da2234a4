local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local gw_event_activity_define = require "gw_event_activity_define"
local event = require "event"
local arena_common_const = require "arena_common_const"
local event_personalInfo = require "event_personalInfo"
local cfg_util = require "cfg_util"
local game_scheme = require "game_scheme"
local event_activity_define = require "event_activity_define"
local log = require "log"
local activity_pb = require "activity_pb"
local net_activity_module = require "net_activity_module"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local arena_common_mgr = require "arena_common_mgr"

--region Controller Life
module("ui_arena_main_not_start_controller")
local controller = nil
local UIController = newClass("ui_arena_main_not_start_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    --请求个人战力排行
    net_activity_module.MSG_COMM_ACTIVITY_RANK_REQ(activity_pb.ACTTYPE_PERSONAL_SELF_POWER)
    self.isGetRank = false
    self.arenaType = data.arenaType
    self.activityId = data.activityId
    self.rankData = {}
    self.rankList = {}
    self:InitBaseData()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.rankList = {}
    self.likeList = {}
    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    self.onPowerRankRSP = function(_,msg)
        if not msg then
            log.Error("can not get power rank rsp")
            return
        end
        if msg.actRankType ~= activity_pb.ACTTYPE_PERSONAL_SELF_POWER then
            return
        end
        self.isGetRank = true
        self.rankData.selfRank = msg.selfRank and msg.selfRank.rank or 0
        self.rankData.rankInfo = msg.rankInfo
        self:InitBaseData()
        self:InitRankData()
    end
    self:RegisterEvent(event_activity_define.TMSG_COMM_ACTIVITY_RANK_RSP,self.onPowerRankRSP)
    
    --点赞
    --刷新点赞数量
    self.UpdateLiking = function(_,msg)
        self:RefreshLikeNum(msg)
    end
    self:RegisterEvent(event_personalInfo.REFRESH_ARENA_LIKE,self.UpdateLiking)
    
    --刷新点赞红点
    self.UpdateTopicData = function()
        self:RefreshLikeRedDot()
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,self.activityId) --刷新活动红点
    end
    self:RegisterEvent(event_personalInfo.REFRESH_ARENA_TOPIC_LIKENUM,self.UpdateTopicData)

    --点赞回复
    self.OnClickLikeCallBack = function(eventName,data)
        self:RefreshLikeNum(data.nLikeRoleNums, self.curPlayID) --刷新点赞数据
        self:ShowFlowTxt() --显示点赞成功飘字
        self:ShowAni() --播放获得奖励动画
    end
    self:RegisterEvent(event_personalInfo.ROLE_PRAISE_UPDATE_MSG,self.OnClickLikeCallBack)

end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic

function  UIController:OnBtnTipBtnClickedProxy()
    arena_common_mgr.OpenArenaHelpUI(self.arenaType)
end

--打开每日奖励
function  UIController:OnBtnShowDailyRewardClickedProxy()
    local data = {
        activityId = self.activityId,
        taskIdList = arena_common_mgr.GetDailyTaskList(self.activityId, self.arenaType),
    }
    self:TriggerUIEvent("ShowDailyReward", data)
end

--打开记录
function  UIController:OnBtnShowRecordListClickedProxy()
    arena_common_mgr.OpenBattleRecordUI(self.arenaType)
end

--打开高级竞技场奖励
function  UIController:OnBtnShowAdvancedRewardClickedProxy()
    --1009115	获得风暴竞技场第{%s1}名的玩家所在的联盟所有成员，都会获得一份对应的联盟奖励
    --默认读取第一个高级竞技场
    arena_common_mgr.OpenArenaRewardUI(self.activityId, self.arenaType, 1009115, 2)
end

--打开中级竞技场奖励
function  UIController:OnBtnShowMiddleRewardClickedProxy()
    --1009115	获得风暴竞技场第{%s1}名的玩家所在的联盟所有成员，都会获得一份对应的联盟奖励
    --默认读取第一个中级竞技场
    arena_common_mgr.OpenArenaRewardUI(self.activityId, self.arenaType, 1009115, 1)
end

--打开英雄界面
function  UIController:OnBtnJumpHeroListClickedProxy()
    local festival_activity_mgr = require "festival_activity_mgr"
    festival_activity_mgr.CloseActivityCenterUI()
    ui_window_mgr:ShowModule("ui_gw_hero_list")
end

--点赞无通用接口
--region 点赞
--点赞成功飘字
function UIController:ShowFlowTxt()
    local click_liking_mgr = require "click_liking_mgr"
    local likeConfigType = arena_common_const.LikeConfigType[self.arenaType]
    local itemInfo = click_liking_mgr.GetRewardInfo(likeConfigType)
    if itemInfo then
        local str = string.format("%s%s",itemInfo.num,lang.Get(itemInfo.nameKey))
        local flow_text = require "flow_text"
        flow_text.Add(string.format2(lang.Get(1007308),str))
    end
end

--刷新点赞数据
function UIController:RefreshLikeNum(data, roleID)
    if not self.rankList then
        return
    end
    if not self.likeList then
        self.likeList = {}
        for k, v in ipairs(self.rankList) do
            local info = v
            info.rank = k
            info.likeNum = 0
            self.likeList[info.roleID] = info
        end
    end
    if data and type(data) == "table" then
        for i,v in ipairs(data) do
            if self.likeList[v.dbid] then
                self.likeList[v.dbid].likeNum = v.likeNum
                self:TriggerUIEvent("RefreshSingleLikingNum", self.likeList[v.dbid].rank,self.likeList[v.dbid]) 
            end
        end
    elseif data and type(data) == "number" and roleID then
        if self.likeList[roleID] then
            self.likeList[roleID].likeNum = data
            self:TriggerUIEvent("RefreshSingleLikingNum", self.likeList[roleID].rank,self.likeList[roleID]) 
        end
    end
end

--刷新点赞红点
function UIController:RefreshLikeRedDot()
    local data_personalInfo = require "data_personalInfo"
    local click_liking_mgr = require "click_liking_mgr"
    local likeConfigType = arena_common_const.LikeConfigType[self.arenaType]
    local likeClientType = arena_common_const.LikeClientType[self.arenaType]
    local curPlayCanLikeNum = data_personalInfo.GetPersonalInfoValue(likeClientType)
    local LikeMaxCount = click_liking_mgr.GetArenaMaxLikeCount(likeConfigType)
    for i = 1,3 do
        self:TriggerUIEvent("RefreshSingleLikingRedDot", i, curPlayCanLikeNum and curPlayCanLikeNum < LikeMaxCount and i == 1)
    end
end

--播放获得奖励动画
function UIController:ShowAni()
    if not self.likeList or not self.likeList[self.curPlayID] then
        return
    end
    local index = self.likeList[self.curPlayID].rank
    self:TriggerUIEvent("ShowLikingAnimation", index)
end

--endregion

--设置基础显示
function UIController:InitBaseData()
    if not self.isGetRank then
        return
    end
    local cfgNum = game_scheme:ArenaCommonInfo_nums()
    local curArenaCfgList = {}
    local arenaId = arena_common_mgr.GetCurArenaID(self.arenaType)
    if not arenaId then
        arena_common_mgr.Warning0("can not get arena id arenaType:", self.arenaType)
        return
    end
    for i = 1, cfgNum do
        local cfg = arena_common_mgr.GetArenaCfg(i)
        if cfg and cfg.ArenaType == self.arenaType then
            table.insert(curArenaCfgList, cfg)
        end
    end
    local middleRankList = curArenaCfgList[1] and cfg_util.ArrayToLuaArray(curArenaCfgList[1].HeroPowerRank) or nil
    local advancedRankList = curArenaCfgList[2] and cfg_util.ArrayToLuaArray(curArenaCfgList[2].HeroPowerRank) or nil
    local data = {
        advancedRankStr = advancedRankList and string.format("%d-%d", advancedRankList[1], advancedRankList[2]) or "",
        middleRankStr = middleRankList and string.format("%d-%d", middleRankList[1], middleRankList[2]) or "",
        serverIdsStr = arena_common_mgr.GetArenaMatchServerStr(arenaId),
    }
    local lowerRank = 0 
    if middleRankList then
        lowerRank = middleRankList[2]
    elseif advancedRankList then
        lowerRank = advancedRankList[2]
    end
    data.curPowerRank = (lowerRank > self.rankData.selfRank and self.rankData.selfRank ~= 0) and self.rankData.selfRank or lang.Get(1009109)      --未上榜
    data.startTime = arena_common_mgr.GetArenaOpenTime(arenaId)
    self:TriggerUIEvent("InitBaseShow",data)
end

--设置前三玩家显示
function UIController:InitRankData()
    if not self.isGetRank then
        return
    end
    self.rankList = arena_common_mgr.GetTopRankData(self.arenaType, self.rankData.rankInfo)
    local likingClick = function(playerData)
        self.curPlayID = playerData.roleId
        arena_common_mgr.RequestLike(self.curPlayID, self.arenaType)
    end
    self:TriggerUIEvent("SetTopPlayerInfo", self.rankList, likingClick)
    arena_common_mgr.RequestLikeData(self.rankList, self.arenaType)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
