local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Toggle = CS.UnityEngine.UI.Toggle
local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local GameObject = CS.UnityEngine.GameObject


module("ui_arena_reward_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenareward.prefab"

WidgetTable ={
	btn_closeBtn = { path = "panelBg/closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	tog_1 = { path = "panelBg/ToogleGroup/tog_1", type = Toggle, value_changed_event = "OnTog1ValueChange"},
	txt_Title = { path = "panelBg/ToogleGroup/tog_1/Btn_pop_tabwxz/txt_Title", type = Text, },
	txt_Title = { path = "panelBg/ToogleGroup/tog_1/Btn_pop_tabxz/txt_Title", type = Text, },
	tog_2 = { path = "panelBg/ToogleGroup/tog_2", type = Toggle, value_changed_event = "OnTog2ValueChange"},
	txt_Title = { path = "panelBg/ToogleGroup/tog_2/Btn_pop_tabwxz/txt_Title", type = Text, },
	txt_Title = { path = "panelBg/ToogleGroup/tog_2/Btn_pop_tabxz/txt_Title", type = Text, },
	tog_3 = { path = "panelBg/ToogleGroup/tog_3", type = Toggle, value_changed_event = "OnTog3ValueChange"},
	txt_Title = { path = "panelBg/ToogleGroup/tog_3/Btn_pop_tabwxz/txt_Title", type = Text, },
	txt_Title = { path = "panelBg/ToogleGroup/tog_3/Btn_pop_tabxz/txt_Title", type = Text, },
	btn_pop_btn_return = { path = "panelBg/btn_pop_btn_return", type = Button, event_name = "OnBtnPop_btn_returnClickedProxy"},
	rtf_content = { path = "panelBg/rtf_content", type = RectTransform, },
	txt_title = { path = "panelBg/rtf_content/titleBg/txt_title", type = Text, },
	rtf_Viewport3 = { path = "panelBg/rtf_content/rtf_Viewport3", type = RectTransform, },
	srt_content_alliance = { path = "panelBg/rtf_content/rtf_Viewport3/srt_content_alliance", type = ScrollRectTable, },
	item_rank_reward_template1 = { path = "panelBg/rtf_content/rtf_Viewport3/srt_content_alliance/item_rank_reward_template1", type = GameObject, },
	rtf_Viewport2 = { path = "panelBg/rtf_content/rtf_Viewport2", type = RectTransform, },
	srt_content_daily = { path = "panelBg/rtf_content/rtf_Viewport2/srt_content_daily", type = ScrollRectTable, },
	item_reward_template1 = { path = "panelBg/rtf_content/rtf_Viewport2/srt_content_daily/item_reward_template1", type = GameObject, },
	rtf_Viewport1 = { path = "panelBg/rtf_content/rtf_Viewport1", type = RectTransform, },
	srt_content_person = { path = "panelBg/rtf_content/rtf_Viewport1/srt_content_person", type = ScrollRectTable, },

}
