---
--- Generated by EmmyLua(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/9/13 12:06
--- Desc: HUD工具类
local require = require
local GWConst = require "gw_const"
local GWHomeHudConst = require "gw_home_hud_const"
local data_mgr = require "data_mgr"
local pairs = pairs

local GWG = GWG
local MoveHudTypes = GWHomeHudConst.MoveHud
module("gw_home_hud_util")
local gw_ed = require "gw_ed"
local GWHomeHudUtil = {}

---所有数据存储
local _d = data_mgr:CreateData("gw_home_hud_util")
---非服务器数据存储
local mc = _d.mde.const
---服务器数据存储
local mn = _d.mde.net

function GWHomeHudUtil.Init()
    --摄像机上次记录距离
    mc.lastCameraDxf = 0
    --摄像机上次记录中心
    mc.lastCameraCenter = {}
    mc.defaultDfx = GWConst.HomeCameraZoom
    mc.lastViewLevel = 0
end

---@public 初始化移动Hud组件
function GWHomeHudUtil.InitMoveHudComponent(comp_name, bindParent)
    local id = GWG.GWIdMgr:AllocComponentId()
    local comp = GWG.GWAdmin.PopBSComponent(comp_name, id)
    if comp then
        local data = MoveHudTypes[comp_name]
        if not data then
            GWG.GWAdmin.SwitchUtility.Error("无法创建HUD组件 comp_name=", comp_name)
            return
        end
        comp:InitData(bindParent, data)
        return id, comp
    else
        GWG.GWAdmin.SwitchUtility.Error("无法创建HUD组件 comp_name=", comp_name)
        return
    end
end

---@private 更新移动Hud组件
local function OnUpdateList(listComps, scaleFactor, needScale)
    for i, comp in pairs(listComps) do
        if comp:IsValid() then
            if needScale then
                comp:SetHudScale(scaleFactor)
            end
            comp:OnLateUpdate()
        end
    end
end

---@public 更新Hud组件
function GWHomeHudUtil.UpdateHudComponent()
    if GWG.GWMgr.curScene ~= GWG.GWConst.ESceneType.Home then
        return
    end
    if not GWG.GWMgr.comp then
        return
    end
    local cameraDxf = GWG.GWMgr.comp:GetCurrentCameraDxf()
    if cameraDxf == nil then
        return
    end
    local needScale = cameraDxf ~= mc.lastCameraDxf
    local scaleFactor = mc.defaultDfx / cameraDxf
    for k, v in pairs(MoveHudTypes) do
        local listComps = GWG.GWAdmin.GetBSComponentsByType(k)
        if listComps then
            OnUpdateList(listComps, scaleFactor, needScale)
        end
    end
end

local function OnHideList(listComps)
    for i, comp in pairs(listComps) do
        if comp:IsValid() then
            comp:OnHide()
        end
    end
end

function GWHomeHudUtil.HideAllHud()
    for k, v in pairs(MoveHudTypes) do
        local listComps = GWG.GWAdmin.GetBSComponentsByType(k)
        if listComps then
            OnHideList(listComps)
        end
    end
end


local function OnShowList(listComps)
    for i, comp in pairs(listComps) do
        if comp:IsValid() then
            comp:OnShow()
        end
    end
end

function GWHomeHudUtil.ShowAllHud()
    for k, v in pairs(MoveHudTypes) do
        local listComps = GWG.GWAdmin.GetBSComponentsByType(k)
        if listComps then
            OnShowList(listComps)
        end
    end
end

---@see 隐藏hud
---@param Id number 实体唯一id
function GWHomeHudUtil.HideHud(Id)
    local comp = GWG.GWAdmin.GetComponentByID(Id)
    if comp then
        comp:OnHide()
    end
end

---@see 显示hud
---@param Id number 实体唯一id
function GWHomeHudUtil.ShowHud(Id)
    local comp = GWG.GWAdmin.GetComponentByID(Id)
    if comp then
        comp:OnShow()
    end
end

function GWHomeHudUtil.OnCameraViewChange()
    GWHomeHudUtil.UpdateHudComponent()
end
GWHomeHudUtil.Init()
gw_ed.mgr:Register(gw_ed.GW_HOME_VIEW_CHANGE, GWHomeHudUtil.OnCameraViewChange)
return GWHomeHudUtil