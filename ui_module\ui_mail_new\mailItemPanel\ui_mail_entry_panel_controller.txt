local print = print
local require = require
local pairs = pairs
local table = table
local newClass = newclass
local type = type
local tostring = tostring
local message_box = require "message_box"
local log = require "log"

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local event = require "event"
local table_util = require "table_util"

local ui_mail_gw_data = require "ui_mail_gw_data"
local mail_define_gw = require "mail_define_gw"
local mail_data_gw = require "mail_data_gw"
local mail_detail_mgr = require "mail_detail_mgr"
local net_mail_module = require "net_mail_module"
local ui_mail_gw_define = require "ui_mail_gw_define"
local os = os


--region Controller Life
module("ui_mail_entry_panel_controller")
local controller = nil
local UIController = newClass("ui_mail_entry_panel_controller", controller_base)

local triggerCount = 0 -- 记录触发次数
local lastTriggerTime = 0 -- 记录上次触发时间
local isCanReq = true -- 标记位

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    if not data then
        ui_window_mgr:UnloadModule("ui_mail_entry_panel")
    end
    triggerCount = 0 -- 记录触发次数
    lastTriggerTime = 0 -- 记录上次触发时间
    isCanReq = true -- 标记位
    self.CData = {}
    self.mailEntryData = {
        data = {},
        mailType = data.mailType,
        cacheSCount = 0, --缓存打开时的服务端邮件数量
        cCount = nil,
        func_onHide = data.func_onHide,
    }
    self:RefreshMailEntryPanelUI(data)
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil
    self.mailEntryData = nil

    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()
    --删除邮件事件监听
    function delMailEntryHandler(funcName)
        self:RefreshMailEntryPanelUI({ mailType = self.mailEntryData.mailType })
    end
    self:RegisterEvent(event.DEL_SPECIAl_MAIL_RES, delMailEntryHandler)

    --下拉item，自动请求分页数据的事件监听
    function mailUpdateHandler(funcName, result, mailType)
        if result == 0 and mailType == self.mailEntryData.mailType then
            self:RefreshMailEntryPanelUI({ mailType = mailType })
        end
    end
    self:RegisterEvent(event.MAIL_UPDATE, mailUpdateHandler)

    --获取详情(未读变已读)事件监听
    self:RegisterEvent(event.UPDATE_MAIL_READ_STATE, delMailEntryHandler)

    function closeView()
        self:CloseView()
    end
    self:RegisterEvent(ui_mail_gw_define.Evt_CloseMailPanel, closeView)
end

function UIController:AutoUnsubscribeEvents()

end

--endregion

--region Controller Logic

function UIController:RefreshMailEntryPanelUI(data)
    if not data or not data.mailType then
        log.Error("Invalid data provided to RefreshMailEntryPanelUI")
        return
    end
    self.mailEntryData.mailType = data.mailType
    self.mailEntryData.data = data.entryMailData or mail_data_gw.GetMailEntryDataByType(data.mailType)
    self.mailEntryData.cacheSCount = mail_data_gw.GetMailCountByType_Server(data.mailType)
    self.mailEntryData.cCount = #self.mailEntryData.data

    local tmpData = {
        mailType = data.mailType,
        uiData = {},
        length = self.mailEntryData.cCount,
    }
    function getItem(t, k)
        return self:GetMailEntryData(t, k)
    end

    table_util.SetDynamicGetItem(tmpData.uiData, getItem)
    --table_util.SetDynamicDataLen(tmpData.uiData, self.mailEntryData.cCount)
    self:TriggerUIEvent("RefreshUI", tmpData)
    --进入邮件子列列表的上报
    local reportMsg = {
        MailType = data.mailType, --邮件类型ID
    }
    event.EventReport("Mail_ViewDetail", reportMsg)
end

---根据传入的类型获取条目信息
function UIController:GetMailEntryData(t, k)
    if not self.mailEntryData.data then
        log.Error("ff:mailEntryData is nil: " .. tostring(k))
        self:OnBtnCloseBtnClickedProxy()
        return nil
    end

    local oneMailData = self.mailEntryData.data[k]
    if not oneMailData then
        return nil
    end

    local onMailEntryClick = function(data)
        self:OnMailEntryBtnClick(data)
    end

    -- 获取uiDefine，如果为nil则提供默认值
    local uiDefine = ui_mail_gw_define.E_SubMailTypeDataMap[oneMailData.mailSubtype]
    if not uiDefine then
        log.Error("ff:uiDefine not found for mailSubtype: " .. tostring(oneMailData.mailSubtype))
        uiDefine=ui_mail_gw_define.E_SubMailTypeDataMap[mail_define_gw.E_SubMailType.Normal]
    end

    local tmpData = {
        mailID = oneMailData.u64MailID,
        uiDefine = uiDefine,
        data = {
            bgType = ui_mail_gw_data.GetMailTemplateData_Params(oneMailData.mailTempletID, "bgType"), -- pvp时设置
            txtName = ui_mail_gw_data.GetMailEntryName(oneMailData),
            txtDes = ui_mail_gw_data.GetMailEntryDes(oneMailData),
            txtTime = ui_mail_gw_data.GetMailEntryTime(oneMailData),
            reward = ui_mail_gw_data.GetMailEntryReward(oneMailData),
            collectGoodsId = ui_mail_gw_data.GetMailEntryCollectGoodsId(oneMailData),
            isRead = oneMailData.nStatus == 1,
            isReceived = oneMailData.nAttachFlag == 1,
        },
        onMailEntryClick = oneMailData.mailSubtype ~= mail_define_gw.E_SubMailType.Collect and onMailEntryClick or nil, -- 采集类型不允许点击
        mailSubtype = oneMailData.mailSubtype,
    }

    return tmpData
end
--totalNum = 0

--判断有没有到当前页最后一个元素，如果有则请求下一页数据
function UIController:OnMailEntryItemRender(index, dataItem)

    --if totalNum ==1 then
    --    return
    --end
    --totalNum = totalNum + 1
    --net_mail_module.GetPageMailByType_REQ(self.mailEntryData.mailType)

    if index < self.mailEntryData.cCount then
        return
    end
    if self.mailEntryData.cCount >= self.mailEntryData.cacheSCount then
        return
    end
    
    if isCanReq then
        net_mail_module.GetPageMailByType_REQ(self.mailEntryData.mailType)
        self:RefreshReqFrequency()
    end
end

function UIController:RefreshReqFrequency()
    local currentTime = os.time() -- 获取当前时间

    -- 如果距离上次触发时间超过2秒，重置计数器和时间
    if currentTime - lastTriggerTime > 2 then
        triggerCount = 0
        lastTriggerTime = currentTime
    end

    -- 增加触发次数
    triggerCount = triggerCount + 1

    -- 如果2秒内触发3次，设置标记位
    if triggerCount >= 3 then
        isCanReq = false
    end
end

---一键删除
function UIController:OnBtnDeleteAllClickedProxy()
    --删除时弹出提示
    local function messageBoxCallback(callbackData, nRet)
        if message_box.RESULT_YES == nRet then
            net_mail_module.DelMail_REQ(self.mailEntryData.mailType)
        end
    end

    local tipStr = lang.Get(5806)--删除已领取的全部邮件
    message_box.Open(tipStr, message_box.STYLE_YESNO, messageBoxCallback, 0, lang.KEY_OK, lang.KEY_CANCEL, lang.KEY_SYSTEM_TIPS)

end

---一键领取
function UIController:OnBtnReadAllClickedProxy()
    net_mail_module.ReceiveMailGoods_REQ(self.mailEntryData.mailType)
    print("OnBtnReadAllClickedProxy")
end

---邮件条目点击跳转详情界面
function UIController:OnMailEntryBtnClick(data)
    mail_detail_mgr.ShowMailDetail(data.mailID)
end

function UIController:OnBtnCloseBtnClickedProxy()
    if self.mailEntryData.func_onHide then
        self.mailEntryData.func_onHide()
    end
    ui_window_mgr:UnloadModule(self.view_name)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
