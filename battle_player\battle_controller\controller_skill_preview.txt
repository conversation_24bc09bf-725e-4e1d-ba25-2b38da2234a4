local require = require
local string = string
local event = require "event"
local battle_manager = require "battle_manager"
local util = require "util"
local ui_window_mgr = require "ui_window_mgr"

local log = require "log"

module("controller_skill_preview")
local ticker = nil

function ShowMedusa()
    ShowPreviewVideo("art/maps/battle/records/skillpreview/medusa.bytes", "ui_seven_signIn")
end

function ShowBlazeCentaur()
    ShowPreviewVideo("art/maps/battle/records/skillpreview/blazecentaur.bytes", "ui_seven_signIn")
end

function ShowFaith()
    ShowPreviewVideo("art/maps/battle/records/skillpreview/faith.bytes", "ui_frist_rechage")
end

function ShowMotherOfSwarms(callBack)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/motherofswarms.bytes", "ui_first_recharge",callBack)
end

function ShowMotherOfSwarms_Japan(callBack)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/yinghuo04.bytes", "ui_frist_rechage",callBack)
end

function ShowOrigin()
    ShowPreviewVideo("art/maps/battle/records/skillpreview/origin.bytes", "ui_seven_signIn")
end

function ShowTyrannosaurusRex(onReopenCallback,moduleName)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/jijiabawanglong.bytes", moduleName or "ui_welfare_gift",onReopenCallback)
end

function ShowDemonHunter(onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/shengtu.bytes", "ui_welfare_gift",onReopenCallback)
end

function ShowHeartofAngrySea(onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/nuhaizhixin.bytes", "ui_welfare_gift",onReopenCallback)
end

function ShowZhenDangDianHu(onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/zhendangdianhu.bytes", "ui_welfare_gift",onReopenCallback)
end


function ShowChongMu(onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/motherofswarms.bytes", "ui_welfare_gift",onReopenCallback)
end

function ShowLieying(onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/xinyingxiong.bytes", "ui_welfare_gift",onReopenCallback)--暴风猎鹰
end

--从Skin表读取录像路径
function ShowSkinVedio(skinCfg,moudleName,onReopenCallback)
    if skinCfg and not string.empty(skinCfg.recordPath) then
        ShowPreviewVideo(skinCfg.recordPath,moudleName,onReopenCallback)
    else
        log.Error("Skin haven't configure [recordPath],ID =",skinCfg.ID)
    end
end
local isNewPlayerHero
local SetNewWeekHeroType = function ()
    if isNewPlayerHero then
        -- local new_week_activity_data = require "new_week_activity_data"
        -- new_week_activity_data.SetIsNewHero(true)
    end
end

function ShowNewHero(isNew,recordPath)
    isNewPlayerHero = isNew
    ShowPreviewVideo(recordPath, "ui_new_week_activity",SetNewWeekHeroType,true)--暴风猎鹰
end

function ShowNewHero1(isNew)
    isNewPlayerHero = isNew
    ShowPreviewVideo("art/maps/battle/records/skillpreview/xinyingxiong.bytes", "ui_new_week_activity",SetNewWeekHeroType)--暴风猎鹰
end

function ShowNewHero2(isNew)
    isNewPlayerHero = isNew
    ShowPreviewVideo("art/maps/battle/records/skillpreview/shanv.bytes", "ui_new_week_activity",SetNewWeekHeroType)--沙女
end

function ShowNewHero3(isNew)
    isNewPlayerHero = isNew
    ShowPreviewVideo("art/maps/battle/records/skillpreview/jijianv.bytes", "ui_new_week_activity",SetNewWeekHeroType)--机甲女
end

function ShowNewHero4(isNew)
    isNewPlayerHero = isNew
    --TODO：填入实际的英雄战斗录像地址
    ShowPreviewVideo("art/maps/battle/records/skillpreview/jianmo.bytes", "ui_new_week_activity",SetNewWeekHeroType)--剑魔
end

function ShowChongMuSkin()
    ShowPreviewVideo("art/maps/battle/records/skillpreview/chongmu.bytes","ui_skin_gift")--虫母皮肤
end

function ShowChongmuSkin_SpecialGift(onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/chongmu.bytes",nil,onReopenCallback)--虫母皮肤
end

function ShowNewHero5(isNew)
    isNewPlayerHero = isNew
    ShowPreviewVideo("art/maps/battle/records/skillpreview/longqishi.bytes", "ui_new_week_activity",SetNewWeekHeroType)--龙骑士
end

function ShowNewHero6(isNew)
    isNewPlayerHero = isNew
    ShowPreviewVideo("art/maps/battle/records/skillpreview/longqishi.bytes", "ui_new_week_activity",SetNewWeekHeroType)--圣骑
end

function ShowRenMaSkin(fromeModule,onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/huoyanrenma.bytes", fromeModule,onReopenCallback)--龙骑士
end

function ShowBaWangLongSkin(fromeModule,onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/megalosaurus.bytes", fromeModule,onReopenCallback)--龙骑士
end

function ShowChongMu_totalRecharge(onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/motherofswarms.bytes",nil,onReopenCallback)
end

--电魂龙裔限定皮肤，黯灭龙裔
function ShowAnMieLongYi(fromeModule, onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/anmielongyi.bytes", fromeModule,onReopenCallback)--龙骑士
end

--剑魔皮肤
function ShowJianMoPiFu(fromeModule, onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/jianmopifu.bytes", fromeModule,onReopenCallback)
end

--轮回之神限定皮肤，轮回帝姬
function ShowLunHuiDiJi(fromeModule, onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/lunhuidiji.bytes", fromeModule,onReopenCallback)--龙骑士
end

--紫焰妖凰皮肤礼包
function ShowZiYanYaoHuang(fromeModule, onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/ziyanyaohuang.bytes", fromeModule,onReopenCallback)--紫焰妖凰
end

--娜塔莉皮肤礼包
function ShowNaTaLi(fromeModule, onReopenCallback)
    ShowPreviewVideo("art/maps/battle/records/skillpreview/natali_ywlh.bytes", fromeModule,onReopenCallback)--娜塔莉
end

--通用活动皮肤礼包
function ShowCommonFestivalPiFu(skinPreviewPath, fromeModule, onReopenCallback)
    if skinPreviewPath == nil or skinPreviewPath == "" then
        log.Error("[ShowCommonFestivalPiFu] skinPreviewPath is empty")
        return false
    end
    ShowPreviewVideo(skinPreviewPath, fromeModule, onReopenCallback)
end

--播放录像
--previewAssetPath 录像的资源路径
--fromeModule 触发录像的来源模块
function ShowPreviewVideo(previewAssetPath, fromeModule,onReopenCallback,_canSkip)
    -- ui_window_mgr:UnloadModule("ui_lobby")
    -- local menu_bot_data = require "menu_bot_data"
    -- menu_bot_data.CloseAllPage()
    if fromeModule then
        ui_window_mgr:HideModule(fromeModule)
    end
    CloseSlgUI()
    battle_manager.SetupNewPlayerCutscene(previewAssetPath, function()
        OpenSlgUI()
        ticker = util.DelayCall(0.2,function()
            if fromeModule then
                ui_window_mgr:ShowModule(fromeModule,nil,function()
                    event.Trigger(event.SHOW_POPUP_MODULE)
                end)
            end

            if onReopenCallback then
                onReopenCallback()
            end
            isNewPlayerHero = nil
        end)
    end,_canSkip)
end
function OpenSlgUI()
    local sand_ui_event_define = require "sand_ui_event_define"
    event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
end
function CloseSlgUI()
    local sand_ui_event_define = require "sand_ui_event_define"
    event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
end

function ClearTimeTicker()
	if ticker then
		util.RemoveDelayCall(ticker)
		ticker = nil
	end
end
event.Register(event.SCENE_DESTROY, ClearTimeTicker)