local require = require
local typeof = typeof

local RectTransform = CS.UnityEngine.RectTransform
local Text = CS.UnityEngine.UI.Text
local GameObject = CS.UnityEngine.GameObject
local Image = CS.UnityEngine.UI.Image
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Button = CS.UnityEngine.UI.Button


module("ui_madness_lord_base_tips_binding")

UIPath = "ui/prefabs/sandbox/uimadnesslordbasetips.prefab"

WidgetTable ={
	rtf_entity = { path = "rtf_entity", type = RectTransform, },
	txt_debuffName = { path = "rtf_entity/layout/debuff/txt_debuffName", type = Text, },
	txt_debuffValue = { path = "rtf_entity/layout/debuff/txt_debuffValue", type = Text, },
	txt_hpText = { path = "rtf_entity/layout/tips_2/tips_2/txt_hpText", type = Text, },
	obj_brokenHeard = { path = "rtf_entity/layout/tips_2/tips_2/txt_hpText/obj_brokenHeard", type = GameObject, },
	rtf_hpSlider = { path = "rtf_entity/layout/hp/hpSlider/rtf_hpSlider", type = RectTransform, },
	txt_hpTxt = { path = "rtf_entity/layout/hp/txt_hpTxt", type = Text, },
	obj_rankList = { path = "rtf_entity/layout/rankObj/obj_rankList", type = GameObject, },
	img_rank1Img = { path = "rtf_entity/layout/rankObj/obj_rankList/rank1/bg/img_rank1Img", type = Image, },
	txt_rank1Damage = { path = "rtf_entity/layout/rankObj/obj_rankList/rank1/bg/txt_rank1Damage", type = Text, },
	txt_rank1Name = { path = "rtf_entity/layout/rankObj/obj_rankList/rank1/txt_rank1Name", type = Text, },
	img_rank2Img = { path = "rtf_entity/layout/rankObj/obj_rankList/rank1_1/bg/img_rank2Img", type = Image, },
	txt_rank2Damage = { path = "rtf_entity/layout/rankObj/obj_rankList/rank1_1/bg/txt_rank2Damage", type = Text, },
	txt_rank2Name = { path = "rtf_entity/layout/rankObj/obj_rankList/rank1_1/txt_rank2Name", type = Text, },
	img_rank3Img = { path = "rtf_entity/layout/rankObj/obj_rankList/rank1_2/bg/img_rank3Img", type = Image, },
	txt_rank3Damage = { path = "rtf_entity/layout/rankObj/obj_rankList/rank1_2/bg/txt_rank3Damage", type = Text, },
	txt_rank3Name = { path = "rtf_entity/layout/rankObj/obj_rankList/rank1_2/txt_rank3Name", type = Text, },
	obj_noRank = { path = "rtf_entity/layout/rankObj/obj_noRank", type = GameObject, },
	txt_timeLeft = { path = "rtf_entity/layout/timerBg/timerBg/txt_timeLeft", type = Text, },
	ss_bossImg = { path = "rtf_entity/titleImg/ss_bossImg", type = SpriteSwitcher, },
	obj_base = { path = "rtf_entity/obj_base", type = GameObject, },
	btn_share = { path = "rtf_entity/obj_base/btns/btn_share", type = Button, event_name = "OnBtnShareClickedProxy"},
	btn_mark = { path = "rtf_entity/obj_base/btns/btn_mark", type = Button, event_name = "OnBtnMarkClickedProxy"},
	txt_bossName = { path = "rtf_entity/txt_bossName", type = Text, },

}
