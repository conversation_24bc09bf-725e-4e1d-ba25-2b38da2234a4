local require = require
local CS = CS
local print = print
local debug = debug
local string = string
local val = require "val"

module("hook_debug")

function init(cache)
    cfg = cache.cfg
    cfg["sw_lua_print_junk"] = {2, 0}
    cfg["sw_lua_profiler_unity"] = {2, 0}
    local sample_stack = {}
    local tsample_stack = {}
    local ind = 1

    local sethook = function()
        -- sethook
        -- open lua profiler in unity
        local val = require "val"
        local sw_print_lua_junk = val.IsTrue("sw_lua_print_junk", 0)
        local sw_lua_profiler_unity = val.IsTrue("sw_lua_profiler_unity", 0)
        if sw_lua_profiler_unity or sw_print_lua_junk then
            local tableProfilerStr = {}
            local uBeginSample = CS.War.Script.Utility.BeginSample
            local uEndSample = CS.War.Script.Utility.EndSample
            local BeginSample = CS.UnityEngine.Profiling.Profiler.BeginSample
            local EndSample = CS.UnityEngine.Profiling.Profiler.EndSample


            function BeginSampleEx(str)
                -- local profiler_idx = tableProfilerStr[str]
                -- if profiler_idx == nil then
                --     profiler_idx = CS.War.Script.Utility.AppendProfilerString(str)
                --     tableProfilerStr[str] = profiler_idx
                -- end
                -- uBeginSample(profiler_idx)
                sample_stack[ind] = str
                ind = ind + 1

                BeginSample(str)
            end
            function EndSampleEx(str)
                -- uEndSample()
                -- EndSample()

                local end_ind = 1
                for i=ind-1,1,-1 do
                    if sample_stack[i] and (sample_stack[i] == str) then
                        end_ind = i
                        -- tsample_stack[str] = 1
                        break
                    end
                end
                for i=end_ind,ind-1 do
                    sample_stack[i] = nil
                    EndSample()
                end
                ind = end_ind
            end
            -- local event = require "event"
            -- function OnEndOfFrame()
            --     EndSampleEx("notag")
            --     -- for k,v in pairs(sample_stack) do
            --     -- end
            --     sample_stack = {}
            --     ind = 1
            -- end
            -- event.Register(event.CSEndOfFrame, OnEndOfFrame)


            local hooking = false
            -- local hook_count = require "hook_count"
            -- local ticker = hook_count("set_hook")
            local val = require "val"

            local Time = CS.UnityEngine.Time
            local oldtime = Time.realtimeSinceStartup
            local cache = {}


            local function hook(event, line)
                if hooking == true then
                    return
                end
                hooking = true

                local t = debug.getinfo(2, "nS")
                local msg = string.format("%s (%s:%s)", t.name, t.source, t.linedefined)

                -- ticker:Count(msg,event, Time.realtimeSinceStartup-oldtime)

                if t.linedefined == -1 then
                    hooking = false
                    return
                end

                local name = t.name

                if msg then
                    if event == "call" then

                        if sw_print_lua_junk then
                            cache[msg] = Time.realtimeSinceStartup
                        end
                        if sw_lua_profiler_unity then

                            BeginSampleEx(msg)
                        end
                    elseif event == "return" then
                        if sw_print_lua_junk then
                            local lastTime = cache[msg]
                            if lastTime then
                                local duration = Time.realtimeSinceStartup - lastTime
                                if duration > 0.2 then
                                    -- if ticker then
                                    --     ticker:Count(name, duration, debug.traceback())
                                    -- end
                                    -- local msg = format("[%s:%s] %s (%s:%s)", t.what, t.namewhat, t.name, t.source, t.linedefined)

                                    print("call_timeout", msg, duration)
                                end
                                cache[msg] = nil
                            end
                        end

                        if sw_lua_profiler_unity then
                            EndSampleEx(msg)
                        end
                    end
                end

                hooking = false
            end

            debug.sethook(hook, "cr")
        end
    end

    cache.console_func["sw_lua_print_junk"] = function(st)
        -- local json = require "dkjson"
        -- print(json.encode(sample_stack))
        -- print(json.encode(tsample_stack))

        -- for i =#sample_stack, 1,-1 do
        --     sample_stack[i] = nil
        -- end
        -- ind = 1
        -- print(json.encode(sample_stack))

        if st == 1 then
            local util = require "util"
            util.DelayOneCall("sethook",sethook,0)
        end
    end

    cache.console_func["sw_lua_profiler_unity"] = function(st)
        if st == 1 then
            local util = require "util"
            util.DelayOneCall("sethook", sethook,0)
        end
    end
end

return {
    init = init
}
