-- -----------------------------------------------
-- description:  活动数据模块
-- author:       陈皮皮
-- -----------------------------------------------

local require = require
local type = type
local tostring = tostring
local pairs = pairs
local ipairs = ipairs
local table = table
local math = math

local bit = require "bit"
local event = require "event"
local game_scheme = require "game_scheme"
local topic_pb = require "topic_pb"
local log = require("log")
local festival_activity_cfg = require "festival_activity_cfg"
local festival_activity_mgr = require "festival_activity_mgr"

---@module festival_activity_data 活动数据模块
module("festival_activity_data")

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 无效数值
local INVALID_INT_VALUE = 0X7FFFFFFF

--- 活动类型枚举
local ActivityCodeType = festival_activity_cfg.ActivityCodeType

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 活动数据预处理器列表
--- 处理函数只会执行一次
---@type (fun(topicID: number, bitData: table, activityData: table): void)[]
local ActivityDataPreprocessorList = {
    -- 解析基础数据
    (function(topicID, bitData, activityData)
        activityData.topicID = topicID
        for k, v in pairs(bitData) do
            if type(k) == "number" then
                if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_HEADING + 1 then
                    -- 获取活动Id
                    activityData.headingCode = -1
                    activityData.versionNumber = -1 --当前versionNumber都为-1 废弃原有的 待慢慢去不删除
                    if v ~= INVALID_INT_VALUE then
                        activityData.activityID = bit.rshift(v, 16)                          -- 活动ID
                        activityData.activityCompleteTimes1 = bit.band(v, 0xFFFF)           -- 活动内容完成次数
                    else
                        activityData.activityCompleteTimes1 = 0
                    end
                    festival_activity_mgr.logger.Warning(4,"[festival_activity_mgr]>>topicID", topicID, " headingCode", activityData.headingCode, "versionNumber", activityData.versionNumber)
                elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_ENDTIME + 1 then
                    -- 活动结束时间
                    activityData.endTimeStamp = v
                    festival_activity_mgr.logger.Warning(4,"[festival_activity_mgr]>>topicID", topicID, " endTime", v)
                elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_STARTTIME + 1 then
                    -- 活动开始时间
                    activityData.startTimeStamp = v
                    festival_activity_mgr.logger.Warning(4,"[festival_activity_mgr]>>topicID", topicID, " startTime", v)
                elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_Complete + 1 then
                    -- 活动任务完成次数
                    if v ~= INVALID_INT_VALUE then
                        --前16位取次数
                        activityData.activityCompleteTimes2 = bit.rshift(v, 16)
                        --后16取活动玩家等级
                        activityData.openLv = bit.band(v, 0xFFFF)
                    else
                        activityData.activityCompleteTimes2 = 0
                        activityData.openLv = nil
                    end
                elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_SIGNDATA + 1 then
                    -- 签到数据
                    if v ~= INVALID_INT_VALUE then
                        activityData.activitySignDays = bit.rshift(v, 24)       -- 签到天数
                        activityData.activitySignData = {}
                        for i = 0, 23 do
                            local _bit = bit.rshift(v, i)
                            local result = bit.band(_bit, 1)                    -- 0标识未领取 1标识领取
                            table.insert(activityData.activitySignData, result) -- 每日签到数据
                        end
                    else
                        activityData.activitySignDays = 0
                        activityData.activitySignData = {}
                    end
                    --没必要处理；直接讲bitData存起来 且因为是pb数据，不容许修改的
                --elseif k >= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 and k <= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D5 +1 then
                --    --同步缓存content1-5 对应的下标3 4 5 6 7
                --    if v ~= INVALID_INT_VALUE then
                --        if not activityData["Content"] then
                --            activityData["Content"] = {}
                --        end
                --        activityData["Content"][k+1] = v  
                --    end
                end
            end
        end
    end),
    -- 读取本地配置
    (function(topicID, bitData, activityData)
        local activityID = activityData.activityID
        if (not activityID) then
            festival_activity_mgr.logger.Warning(2,"PP* 活动数据异常！| topicID: " .. tostring(topicID) .. ", activityID: " .. tostring(activityID))
            return
        end
        -- 活动 ID      
        -- 活动类型码
        activityData.activityCodeType = nil
        --直接获取活动表
        local activityCfg = festival_activity_cfg.GetActivityCfgByActivityID(activityID)
        -- festivalActivity表没有的话去NewActivity表找找 现已不处理
        if activityCfg then
            activityData.headingCode = activityCfg.headingCode
            activityData.activityCodeType = activityCfg.headingCode  --注意 当前直接定义headingCode就是等于activityCodeType
            activityData.atyEntrance = activityCfg.AtyEntrance
            activityData.atyEntrance2 = activityCfg.AtyEntrance2
            --预留获取活动的大类  比如 运营活动/春节活动   但目前没有使用 todo by lzx
            activityData.atyType = activityCfg.atyType
        else
            ----todo  by bxz 如果在表中找不到，是否需要去新手表中找？
            --log.Error("当前活动在festivalActivity表中找不到配置 topicID: ", topicID, ", activityID: ", activityID)
        end
        --直接将bitData也存起来
        for k,v in pairs(bitData or {}) do
            if not activityData.bitData then
                activityData.bitData = {}
            end
            activityData.bitData[k] = v
        end
    end),
}

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 活动数据处理器集合（nil => Default）
--- 处理函数可能会执行多次
---@type table<number, fun(k: number, v: number, activityData: table): boolean>
local ActivityDataLoopProcessorCollection = {
    -- 0 缺省值、普通周活动
    [ActivityCodeType.Default] = (function(k, v, activityData)
        if k >= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 and k <= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D2 + 1 then
            if INVALID_INT_VALUE ~= v then
                activityData.activityContentCompleteTimes1 = activityData.activityContentCompleteTimes1 or {}
                for i = (k - 4) * 8 + 1, (k - 4) * 8 + 8 do
                    local mod8 = math.fmod(i, 8)
                    local _bit = bit.rshift(v, (8 - mod8) * 4)
                    local result = bit.band(_bit, 0xF)
                    activityData.activityContentCompleteTimes1[i] = result
                end
            end
        elseif k >= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D1 + 1 and k <= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D2 + 1 then
            if INVALID_INT_VALUE ~= v then
                activityData.activityContentCompleteTimes2 = activityData.activityContentCompleteTimes2 or {}
                for i = (k - 4) * 8 + 1, (k - 4) * 8 + 8 do
                    local mod8 = math.fmod(i, 8)
                    local _bit = bit.rshift(v, (8 - mod8) * 4)
                    local result = bit.band(_bit, 0xF)
                    activityData.activityContentCompleteTimes2[i - 16] = result
                end
            end
        end
    end),
    -- 1 付费签到
    [ActivityCodeType.Sign] = (nil),
    -- 2 任务
    [ActivityCodeType.Task] = (function(k, v, activityData)
        if k > topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_HEADING + 1 and k < topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_ENDTIME + 1 then
            local isNewbieActivity = (activityData.headingCode == 54)
            local activityCfg = festival_activity_mgr.GetActivityCfgByAtyID(activityData.activityID, isNewbieActivity)
            local index1 = (k - 2) * 2
            local index2 = (k - 2) * 2 + 1
            if (not activityCfg) or (index2 <= activityCfg.ctnID1.count) then
                if v == INVALID_INT_VALUE then
                    v = 0
                end
                local completeTime1, roundTime1, completeTime2, roundTime2 = festival_activity_mgr.SetFestivalCtn1CompleteTimes(v)
                activityData.activityContentCompleteTimes1 = activityData.activityContentCompleteTimes1 or {}
                activityData.activityContentCompleteTimes1[index1] = completeTime1
                activityData.activityContentCompleteTimes1[index2] = completeTime2
                activityData.activityContentRoundTimes = activityData.activityContentRoundTimes or {}
                activityData.activityContentRoundTimes[index1] = roundTime1
                activityData.activityContentRoundTimes[index2] = roundTime2
            end
        end
    end),
    -- 3 礼包
    [ActivityCodeType.Gift] = (function(k, v, activityData)
        if k >= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 and k <= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D2 + 1 then
            if INVALID_INT_VALUE ~= v then
                activityData.activityContentCompleteTimes1 = activityData.activityContentCompleteTimes1 or {}
                for i = (k - 4) * 8 + 1, (k - 4) * 8 + 8 do
                    local mod8 = math.fmod(i, 8)
                    local _bit = bit.rshift(v, (8 - mod8) * 4)
                    local result = bit.band(_bit, 0xF)
                    activityData.activityContentCompleteTimes1[i] = result
                end
            end
        elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D2 + 1 then
            if INVALID_INT_VALUE ~= v then
                activityData["cardReceiveNum_2"] = v
            end
        elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D5 + 1 then
            if INVALID_INT_VALUE ~= v then
                activityData["cardReceiveNum_1"] = v
            end
        end
    end),
    -- 4 周卡
    [ActivityCodeType.WeekCard] = (function(k, v, activityData)
        local headingCode = activityData.headingCode
        -- headingCode 为 37、53、65 是旧版的周卡
        if (headingCode == 37 or headingCode == 53 or headingCode == 65) then
            if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 then
                if v ~= INVALID_INT_VALUE then
                    activityData.buyTimes = bit.rshift(v, 28)
                end
            end
        else
            -- 其他都是新版周卡（签到形式）
            return GetActivityDataLoopProcessor(ActivityCodeType.Default)(k, v, activityData)
        end
    end),
    -- 5 Boss试炼
    [ActivityCodeType.BossChallenge] = (function(k, v, activityData)
        if INVALID_INT_VALUE ~= v then
            if k > topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_HEADING + 1 and k < topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_ENDTIME + 1 then
                local index1 = ((k - 1) * 2) - 1
                activityData.activityContentCompleteTimes1 = activityData.activityContentCompleteTimes1 or {}
                local compliteTime = bit.rshift(v, 24)--完成次数
                local compliteRound = bit.band(bit.rshift(v, 16), 0xFF)--轮次
                activityData.activityContentCompleteTimes1[index1] = { compliteTime = compliteTime, compliteRound = compliteRound }
                local index2 = (k - 1) * 2
                local compliteTime_2 = bit.band(bit.rshift(v, 8), 0xFF)--完成次数2
                local compliteRound_2 = bit.band(v, 0xFF)--轮次2
                activityData.activityContentCompleteTimes1[index2] = { compliteTime = compliteTime_2, compliteRound = compliteRound_2 }
            end
        end
    end),
    -- 6 升星礼包
    [ActivityCodeType.StarRisingGift] = (function(k, v, activityData)
        local AwakenRoadEnum = {
            complite_1 = 4,
            complite_2 = 5,
            Index_HeroID = 6,
            Index_Confirm = 7,
            Index_Star = 8,
        }
        if INVALID_INT_VALUE ~= v then
            activityData.activityContentCompleteTimes1 = activityData.activityContentCompleteTimes1 or {}
            if k == AwakenRoadEnum.Index_HeroID then
                activityData.activityContentCompleteTimes1["heroID"] = v
            elseif k == AwakenRoadEnum.Index_Confirm then
                activityData.activityContentCompleteTimes1["confirmHero"] = v
            elseif k == AwakenRoadEnum.Index_Star then
                activityData.activityContentCompleteTimes1["maxStar"] = v
            elseif k == AwakenRoadEnum.complite_1 then
                activityData["buyData"] = activityData["buyData"] or {}
                for i = 1, 8 do
                    local movePos = 32 - 4 * (i)
                    local buyData = bit.band(bit.rshift(v, movePos), 0xF)--轮次
                    activityData["buyData"][i] = buyData
                end
            elseif k == AwakenRoadEnum.complite_2 then
                activityData["buyData"] = activityData["buyData"] or {}
                for i = 1, 8 do
                    local movePos = 32 - 4 * (i)
                    local buyData = bit.band(bit.rshift(v, movePos), 0xF)--轮次
                    activityData["buyData"][i + 8] = buyData
                end
            end
        end
    end),
    -- 7 心愿抽
    [ActivityCodeType.WishListLottery] = (function(k, v, activityData)
        if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_Complete + 1 then
            -- 召唤次数更新
            if v == INVALID_INT_VALUE then
                activityData.activityCompleteTimes2 = 0
            else
                activityData.activityCompleteTimes2 = v
            end
            event.Trigger(event.WISH_LIST_LOTTERY_TIMES_UPDATE)
        elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 then
            -- 选择奖励
            if v == INVALID_INT_VALUE or v == 0 then
                activityData.selectedRewardID = nil
            else
                activityData.selectedRewardID = v
            end
            event.Trigger(event.BLESSING_ACTIVITY_LOTTERY_SELECT_REWARD)
        end
    end),
    -- 8 圣物抽卡（异度之轮）
    [ActivityCodeType.DecorationLottery] = (ActivityCodeType.WishListLottery),
    -- 9 战甲宝典
    [ActivityCodeType.ArmorTreasureBook] = (nil),
    -- 10 神匣遗迹
    [ActivityCodeType.Relics] = (nil),
    -- 11 位面宝箱
    [ActivityCodeType.TreasureBox] = (nil),
    -- 12 免费签到
    [ActivityCodeType.FreeSign] = (function(k, v, activityData)
        local headingCode = activityData.headingCode
        if (headingCode == 59) then
            -- headingCode 59 是新手活动的签到
            return GetActivityDataLoopProcessor(ActivityCodeType.Default)(k, v, activityData)
        else
            if k >= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 and k <= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D2 then
                if INVALID_INT_VALUE ~= v then
                    activityData.activityContentCompleteTimes1 = activityData.activityContentCompleteTimes1 or {}
                    for i = (k - 4) * 8 + 1, (k - 4) * 8 + 8 do
                        local mod8 = math.fmod(i, 8)
                        local _bit = bit.rshift(v, (8 - mod8) * 4)
                        local result = bit.band(_bit, 0xF)
                        activityData.activityContentCompleteTimes1[i] = result
                    end
                end
            elseif k >= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D1 and k <= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D2 then
                if INVALID_INT_VALUE ~= v then
                    activityData.activityContentCompleteTimes2 = activityData.activityContentCompleteTimes2 or {}
                    for i = (k - 6) * 8 + 1, (k - 6) * 8 + 8 do
                        local _bit = bit.rshift(v, (i - 1 - ((k - 6) * 8)) * 4)
                        local result = bit.band(_bit, 0xF)
                        activityData.activityContentCompleteTimes2[i] = result
                    end
                end
            end
        end
    end),
    -- 13 符文抽取活动（占星屋）
    [ActivityCodeType.SigilLottery] = (function(k, v, activityData)
        -- if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_HEADING + 1 then
        --     local headingCode = bit.rshift(v, 24)                   -- 活动识别码
        --     local versionNumber = bit.band(bit.rshift(v, 16), 0xFF) -- 活动版本号
        --     
        --     --new_week_activity_data.SetFestivalData('topicID', activityData.topicID)
        --     --new_week_activity_data.SetFestivalData('headingCode', headingCode)
        --     --new_week_activity_data.SetFestivalData('versionNumber', versionNumber)
        --     -- 是否为新手玩家
        --     local isNewbieActivity = (activityData.headingCode == 56)
        --     --new_week_activity_data.SetFestivalData('isNewbee', isNewbieActivity)
        -- elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 then
        --     
        --     --new_week_activity_data.SetFestivalData('jackpot1', v)
        -- elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D2 + 1 then
        --     
        --     --new_week_activity_data.SetFestivalData('jackpot2', v)
        -- elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D1 + 1 then
        --     
        --     --new_week_activity_data.SetFestivalData('jackpot3', v)
        -- end
    end),
    -- 14 新英雄召唤
    [ActivityCodeType.HeroSummon] = (nil),
    -- 15 新英雄宝典
    [ActivityCodeType.HeroTreasureBook] = (nil),
    -- 16 试炼副本
    [ActivityCodeType.HeroExclusive] = (nil),
    -- 17 Bingo
    [ActivityCodeType.Bingo] = (nil),
    -- 18 踏青
    -- [ActivityCodeType.Hiking] = (function(k, v, activityData)
        --if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_HEADING + 1 then
        --    if INVALID_INT_VALUE ~= v then
        --        local compliteRound = bit.band(v, 0xFFFF)--轮次
        --        local festival_hiking_mgr = require "festival_hiking_mgr"
        --        festival_hiking_mgr.SetCompliteRound(compliteRound)
        --    end
        --elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_Complete + 1 then
        --    if INVALID_INT_VALUE ~= v then
        --        local festival_hiking_mgr = require "festival_hiking_mgr"
        --        festival_hiking_mgr.SetCurSteps(v)
        --    end
        --end
        -- 节日小游戏（踏青、赛龙舟、漫步）
    --     if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_HEADING + 1 then
    --         if v ~= INVALID_INT_VALUE then
    --             local rounds = bit.band(v, 0xFFFF)
    --             local festival_game_mgr = require "festival_game_mgr"
    --             festival_game_mgr.SetCurRounds(rounds)
    --         end
    --     elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_Complete + 1 then
    --         if v ~= INVALID_INT_VALUE then
    --             local festival_game_mgr = require "festival_game_mgr"
    --             festival_game_mgr.SetCurSteps(v)
    --         end
    --     end
    -- end),
    -- 19 新版徽章活动-道具累积获得
    [ActivityCodeType.BadgeCumulativeProps] = (nil),
    -- 20 新版徽章活动-徽章累积付费
    [ActivityCodeType.BadgeCumulativePayment] = (nil),
    -- 21 新版徽章活动-商店兑换
    [ActivityCodeType.BadgeExchangeShop] = (function(k, v, activityData)
        -- 神器三选一兑换的索引
        if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_SIGNDATA + 1 then
            if activityData.topicID == 708 then
                if v ~= INVALID_INT_VALUE then
                    activityData.artifactIndex1 = v
                else
                    activityData.artifactIndex1 = nil
                end
            end
        elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D5 + 1 then
            --表示终极神器兑换后的下标
            if activityData.topicID == 708 then
                if v ~= INVALID_INT_VALUE then
                    activityData.artifactIndex2 = v
                else
                    activityData.artifactIndex2 = nil
                end
            end
        end
        return GetActivityDataLoopProcessor(ActivityCodeType.Default)(k, v, activityData)
    end),
    -- 22 英雄重生
    [ActivityCodeType.HeroRespawn] = (nil),
    -- 23 Boss试炼2（中秋圆月之战）
    [ActivityCodeType.BossChallengeV2] = (ActivityCodeType.BossChallenge),
    -- 24 圣诞树
    [ActivityCodeType.ChristmasTree] = (function(k, v, activityData)
        local headingCode = activityData.headingCode
        if (headingCode == 3) then
            -- 圣诞树
            if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 then
                if v ~= INVALID_INT_VALUE then
                    if v and v > 0 then
                        activityData.christmasTreeID = v
                    else
                        local activityID = activityData.activityID
                        if activityID then
                            for index = 0, game_scheme:ChristmasTree_nums() - 1 do
                                local treeCfg = game_scheme:ChristmasTree(index)
                                if treeCfg and treeCfg.festivalActivityID == activityID then
                                    activityData.christmasTreeID = treeCfg.ID
                                    break
                                end
                            end
                        end
                    end
                end
            elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D2 + 1 then
                if v ~= INVALID_INT_VALUE then
                    activityData.christmasTreeLevel = v
                end
            elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D1 + 1 then
                if v ~= INVALID_INT_VALUE then
                    activityData.christmasTreeExp = v
                end
            elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D5 + 1 then
                -- 圣诞卡
                if v ~= INVALID_INT_VALUE then
                    festival_activity_mgr.SetLastSendChristmasCardTime(v)
                end
            end
        else
            -- 新版圣诞树
            -- headingCode：14（春节挂灯笼）、15（春节年夜饭）、27（春节贴春联）、147（春节燃爆竹）
            if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D2 + 1 then
                if v ~= INVALID_INT_VALUE then
                    activityData.christmasTreeLevel = v
                end
            elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D1 + 1 then
                if v ~= INVALID_INT_VALUE then
                    activityData.christmasTreeExp = v
                end
            end
            -- 2022/01/05 服务器：吕国锋 treeID改为客户端自行遍历读取 客户端：罗华冠
            local activityID = activityData.activityID
            if activityID then
                for index = 0, game_scheme:ChristmasTree_nums() do
                    local treeCfg = game_scheme:ChristmasTree(index)
                    if treeCfg and treeCfg.festivalActivityID == activityID then
                        activityData.christmasTreeID = treeCfg.ID
                        break
                    end
                end
            end
        end
    end),
    -- 25 非活动页面
    -- [ActivityCodeType.NonActivity] = (function(k, v, activityData)
    --     local headingCode = activityData.headingCode
    --     -- 年兽小游戏，这个值是代表最终的春节大奖是否领取了，其实和小游戏没关系
    --     local Chinese_new_year_mgr = require "Chinese_new_year_mgr"
    --     if (headingCode == Chinese_new_year_mgr.NewYearActivityCodeEnum.MiniGame) then
    --         if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 then
    --             if v ~= INVALID_INT_VALUE then
    --                 activityData.progressRewardState = v
    --             end
    --         end
    --     end
    -- end),
    -- 26 周年庆创角好礼
    [ActivityCodeType.AnniversaryCreationGift] = (function(k, v, activityData)
        if k >= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 and k <= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D2 + 1 then
            if INVALID_INT_VALUE ~= v then
                activityData.activityContentCompleteTimes1 = activityData.activityContentCompleteTimes1 or {}
                for i = (k - 4) * 8 + 1, (k - 4) * 8 + 8 do
                    local mod8 = math.fmod(i, 8)
                    local _bit = bit.rshift(v, (8 - mod8) * 4)
                    local result = bit.band(_bit, 0xF)
                    activityData.activityContentCompleteTimes1[i] = result
                end
            end
        end
    end),
    -- 27 双旦活动黑金卡
    [ActivityCodeType.BlackGoldCard] = (function(k, v, activityData)
        if k >= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 and k <= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D2 + 1 then
            if INVALID_INT_VALUE ~= v then
                activityData.activityContentCompleteTimes1 = activityData.activityContentCompleteTimes1 or {}
                for i = (k - 4) * 8 + 1, (k - 4) * 8 + 8 do
                    local mod8 = math.fmod(i, 8)
                    local _bit = bit.rshift(v, (8 - mod8) * 4)
                    local result = bit.band(_bit, 0xF)
                    activityData.activityContentCompleteTimes1[i] = result
                end
            end
        elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D1 + 1 then
            if INVALID_INT_VALUE ~= v then
                activityData.activityContentCompleteTimes2 = activityData.activityContentCompleteTimes2 or {}
                for i = (k - 6) * 8 + 1, (k - 6) * 8 + 8 do
                    local _bit = bit.rshift(v, (i - 1 - ((k - 6) * 8)) * 4)
                    local result = bit.band(_bit, 0xF)
                    activityData.activityContentCompleteTimes2[i] = result
                end
            end
        elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D2 + 1 then
            if INVALID_INT_VALUE ~= v then
                activityData["cardReceiveNum_2"] = v
            end
        elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D5 + 1 then
            if INVALID_INT_VALUE ~= v then
                activityData["cardReceiveNum_1"] = v
            end
        end
    end),
    -- 28 情人节-送花
    [ActivityCodeType.ValentineDayFlower] = (function(k, v, activityData)
        if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_HEADING + 1 or k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_ENDTIME + 1 then
            --新号ENDTIME有可能分开下发，所以另外做判断
            if activityData.activityID and festival_activity_mgr.IsActivityOpen(activityData.topicID) then
                local net_festival_activity_module = require "net_festival_activity_module"
                net_festival_activity_module.Request_ValentinesIntimacyInfo(activityData.activityID)
            end
        end
    end),
    -- 29 情人节-射箭
    [ActivityCodeType.ValentineDayArchery] = (function(k, v, activityData)
        if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_HEADING + 1 or k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_ENDTIME + 1 then
            --新号ENDTIME有可能分开下发，所以另外做判断
            if activityData.activityID and festival_activity_mgr.IsActivityOpen(activityData.topicID) then
                local net_festival_activity_module = require "net_festival_activity_module"
                net_festival_activity_module.Request_ValentinesArcheryInfo(activityData.activityID)
            end
        end
    end),
    -- 30 情人节-七日特惠礼包
    [ActivityCodeType.ValentineDaySevenDayGift] = (function(k, v, activityData)
        if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 then
            if INVALID_INT_VALUE ~= v then
                local dayBuy = {}
                local counter = 0
                --int32存储七天的购买情况，每四位为一天 总共使用28位
                for i = 0, 6 do
                    local times = bit.band(bit.rshift(v, 32 - 4 * (i + 1)), 0xF)
                    dayBuy[i + 1] = times >= 1
                    if times >= 1 then
                        counter = counter + 1
                    end
                end
                festival_activity_mgr.ValentinePreferentialReport(activityData.topicID, activityData.valentineDayBuy, dayBuy)
                activityData.valentineDayBuy = dayBuy

                festival_activity_mgr.ValentinePreferentialDayRefresh(activityData.topicID)
                --购买次数（天数）大于七则大奖可以领取
                if counter >= 7 then
                    festival_activity_mgr.SetValentinePreferentialRedPointOn(8, activityData.topicID)
                    event.Trigger(event.UPDATE_CHRISTMAS_FESTIVAL_RED)
                end

                event.Trigger(event.SHOW_VALENTINE_BUY_UPDATE)
            end
        elseif k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D1 + 1 then
            if INVALID_INT_VALUE ~= v then
                --大奖购买情况，前四位储存
                local times = bit.band(bit.rshift(v, 28), 0xF)
                activityData.valentineDayBuy[8] = times >= 1

                festival_activity_mgr.ValentinePreferentialDayRefresh(activityData.topicID)
                event.Trigger(event.SHOW_VALENTINE_BUY_UPDATE)
            end
        end
    end),
    -- 31 创世宝库-星矿探索
    [ActivityCodeType.GenesisTreasureLottery] = (function(k, v, activityData)
        if v ~= INVALID_INT_VALUE then
            local genesis_treasure_data = require "genesis_treasure_data"
            genesis_treasure_data.SetLotteryData(k, v)
        end
    end),
    -- 32 创世宝库-兑换商店
    [ActivityCodeType.GenesisTreasureShop] = (function(k, v, activityData)
        if v ~= INVALID_INT_VALUE then
            local genesis_treasure_data = require "genesis_treasure_data"
            genesis_treasure_data.Init(activityData.topicID, activityData.headingCode, activityData.versionNumber)
            genesis_treasure_data.SetShopData(k, v)
        end
    end),
    -- 42 端午节签到活动
    [ActivityCodeType.DragonBoatGift] = (function(k, v, activityData)
        if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_SIGNDATA + 1 then
            -- 签到数据
            if v ~= INVALID_INT_VALUE then
                activityData.activitySignDays = bit.rshift(v, 24)       -- 签到天数
                activityData.activitySignData = {}
                for i = 0, 23 do
                    local _bit = bit.rshift(v, i)
                    local result = bit.band(_bit, 1)                    -- 0标识未领取 1标识领取
                    table.insert(activityData.activitySignData, result) -- 每日签到数据
                end
            else
                activityData.activitySignDays = 0
                activityData.activitySignData = {}
            end
        end
        if k >= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 and k <= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D2 + 1 then

            if INVALID_INT_VALUE ~= v then
                activityData.activityContentCompleteTimes1 = activityData.activityContentCompleteTimes1 or {}
                for i = (k - 4) * 8 + 1, (k - 4) * 8 + 8 do
                    local mod8 = math.fmod(i, 8)
                    local _bit = bit.rshift(v, (8 - mod8) * 4)
                    local result = bit.band(_bit, 0xF)
                    activityData.activityContentCompleteTimes1[i] = result
                end
            end
        elseif k >= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D1 + 1 and k <= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D2 + 1 then

            if INVALID_INT_VALUE ~= v then
                activityData.activityContentCompleteTimes2 = activityData.activityContentCompleteTimes2 or {}
                for i = (k - 4) * 8 + 1, (k - 4) * 8 + 8 do
                    local mod8 = math.fmod(i, 8)
                    local _bit = bit.rshift(v, (8 - mod8) * 4)
                    local result = bit.band(_bit, 0xF)
                    activityData.activityContentCompleteTimes2[i - 16] = result
                end
            end
        end
    end),
    -- 43 年中大促
    -- [ActivityCodeType.MakeFoods] = (function(k, v, activityData)
    --     if v ~= INVALID_INT_VALUE then
    --         local festival_make_foods_game_mgr = require "festival_make_foods_game_mgr"
    --         festival_make_foods_game_mgr.Init(activityData.topicID)
    --         festival_make_foods_game_mgr.SetTopicData(k, v)
    --     end
    -- end),
    -- 45 英雄试用
    [ActivityCodeType.HeroTrial] = (function(k, v, activityData)
        if v ~= INVALID_INT_VALUE then
            local hero_trial_mgr = require "hero_trial_mgr"
            --redenvelopes_celebrate.SetInitData(activityData.topicID, activityData.headingCode, activityData.versionNumber)
            hero_trial_mgr.SetActivityData(activityData, k, v)
        end
    end),
    -- 46 小游戏抢红包
    [ActivityCodeType.SnatchRedEnvelopGame] = (function(k, v, activityData)
        -- if v ~= INVALID_INT_VALUE then
        --     local redenvelopes_celebrate = require "ui_anniversary_snatching_redenvelopes"
        --     --redenvelopes_celebrate.SetInitData(activityData.topicID, activityData.headingCode, activityData.versionNumber)
        --     redenvelopes_celebrate.SetInitData(activityData, k, v)
        -- end
    end),

    -- 47 全服红包
    [ActivityCodeType.WorldSnatchRedEnvelop] = (function(k, v, activityData)
        -- if v ~= INVALID_INT_VALUE then
        --     local redenvelopes_celebrate = require "ui_anniversary_snatching_redenvelopes_celebrate"
        --     --redenvelopes_celebrate.SetInitData(activityData.topicID, activityData.headingCode, activityData.versionNumber)
        --     redenvelopes_celebrate.SetInitData(activityData, k, v)
        -- end
    end),
    -- 50 双旦救援活动
    [ActivityCodeType.PickTheRoute] = (function(k, v, activityData)
        if v ~= INVALID_INT_VALUE then
            local pick_the_route_mgr = require "pick_the_route_mgr"
            pick_the_route_mgr.SetActivityData(activityData, k, v)
        end
    end),
    -- [ActivityCodeType.ChristmasNewyear] = (function(k, v, activityData)
    --     if v ~= INVALID_INT_VALUE then
    --         local christmas_newyear_mgr = require "christmas_newyear_mgr"
    --         christmas_newyear_mgr.SetChristmasTeamTopicData(activityData, k, v)
    --         if k >= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 and k <= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D2 + 1 then
    --             activityData.activityContentCompleteTimes1 = activityData.activityContentCompleteTimes1 or {}
    --             for i = (k - 4) * 8 + 1, (k - 4) * 8 + 8 do
    --                 local mod8 = math.fmod(i, 8)
    --                 local _bit = bit.rshift(v, (8 - mod8) * 4)
    --                 local result = bit.band(_bit, 0xF)
    --                 activityData.activityContentCompleteTimes1[i] = result
    --             end
    --         elseif k >= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D1 + 1 and k <= topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D2 + 1 then
    --             activityData.activityContentCompleteTimes2 = activityData.activityContentCompleteTimes2 or {}
    --             for i = (k - 4) * 8 + 1, (k - 4) * 8 + 8 do
    --                 local mod8 = math.fmod(i, 8)
    --                 local _bit = bit.rshift(v, (8 - mod8) * 4)
    --                 local result = bit.band(_bit, 0xF)
    --                 activityData.activityContentCompleteTimes2[i - 16] = result
    --             end
    --         end
    --     end
    -- end),
    -- 52 画糖人
    -- [ActivityCodeType.DrawSugarman] = (function(k, v, activityData)
    --     if v ~= INVALID_INT_VALUE then
    --         festival_activity_mgr.SetDrawSugarmanData(activityData, k, v)
    --     end
    -- end),
    -- -- 53 存钱罐
    -- [ActivityCodeType.PiggyBank] = (function(k, v, activityData)
    --     if v ~= INVALID_INT_VALUE then
    --         local piggy_bank_data = require "piggy_bank_data"
    --         piggy_bank_data.SetPiggyBankTopicData(activityData, k, v)
    --     end
    -- end),
    -- -- 54 新春祝福
    -- [ActivityCodeType.NewYearBlessing] = (function(k, v, activityData)
    --     local new_year_blessing_mgr = require "new_year_blessing_mgr"
    --     new_year_blessing_mgr.SetNewYearBlessingData(k, v, activityData)
    -- end),
    -- 56 迎春好礼
    -- [ActivityCodeType.ChineseNewYearReward] = (function(k, v, activityData)
    --     local headingCode = activityData.headingCode
    --     -- 原本是年兽小游戏，现在改成迎春好礼
    --     local Chinese_new_year_mgr = require "Chinese_new_year_mgr"
    --     if (headingCode == Chinese_new_year_mgr.NewYearActivityCodeEnum.MiniGame) then
    --         if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D1 + 1 then
    --             if v ~= INVALID_INT_VALUE then
    --                 activityData.progressRewardState = v
    --                 event.Trigger(event.CHINESE_YEAR_ACTIVITY_GET_REWARD)
    --             end
    --         end
    --     end
    -- end),

    --通用活动类型（英雄试炼） 101 --> ACTIVITYID --> Data
    [ActivityCodeType.Universal] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            if activityID == 2009 then
                --英雄试炼
            elseif activityID == 105 then
                local mgr_road_light = require "mgr_road_light"
                mgr_road_light.SetRoadLightData(activityData.activityID, activityData.endTimeStamp, v)
            elseif activityID == 113 then
                local card_draw_data = require "card_draw_data"
                card_draw_data.SetActivityID(activityData.activityID)
            elseif activityID == 219 then
                --风暴营救
                local storm_rescue_data = require "storm_rescue_data"
                storm_rescue_data.SetRewardActivityId(activityData.activityID)
            elseif activityID == 125 then
                --个性换新
                local face_transplant_data = require "face_transplant_data"
                face_transplant_data.SetActivityID(activityData.activityID)
            elseif activityID == 128 or activityID == 129 then
                --强者之路
                local mgr_strong_road = require "mgr_strong_road"
                mgr_strong_road.SetStrongRoadData(activityData.activityID, activityData.endTimeStamp, v)
            elseif activityID == 226 then
                --英雄首充副本
                local hero_first_charge_data = require "hero_first_charge_data"
                hero_first_charge_data.SetRewardActivityId(activityData.activityID)
            elseif activityID == 227 or activityID == 50005 then
                local mythical_beast_mgr = require "mythical_beast_mgr"
                mythical_beast_mgr.SetActivityID(activityData.activityID)
            elseif activityID == 228 or activityID == 50004 then
                local mythical_sing_in_mgr = require "mythical_beast_sign_in_mgr"
                mythical_sing_in_mgr.SetActivityID(activityData.activityID)
            elseif activityID == 231 or activityID == 50003 then
                --风暴营救
                local storm_rescue_data = require "storm_rescue_data"
                storm_rescue_data.SetRewardActivityId(activityData.activityID)
            elseif activityID == 3001 then
                local halloween_sign_in_mgr = require "halloween_sign_in_mgr"
                halloween_sign_in_mgr.SetActivityID(activityData.activityID)
            elseif activityID == 3004 then
                local halloween_slot_machine_mgr = require "halloween_slot_machine_mgr"
                halloween_slot_machine_mgr.SetProgressTaskGameActivityID(activityData.activityID)
            elseif activityID == 3010 then
                local halloween_wingding_data = require "halloween_wingding_data"
                halloween_wingding_data.SetActivityTaskID(activityData.activityID)
            end
        end
    end),
    [ActivityCodeType.HalloweenSlotMachineMain] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            if activityID == 3002 then
                --万圣节老虎机主活动
                local halloween_slot_machine_mgr = require "halloween_slot_machine_mgr"
                halloween_slot_machine_mgr.SetActivityID(activityData.activityID)
            end
        end
    end),
    [ActivityCodeType.SlotMachine] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            if activityID == 3003 then
                --万圣节老虎机
                local halloween_slot_machine_mgr = require "halloween_slot_machine_mgr"
                halloween_slot_machine_mgr.SetSlotGameActivityID(activityData.activityID)
            end
        end
    end),
    [ActivityCodeType.DailyGift1] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            if activityID == 223 then
                --英雄首充每日免费礼包
                local hero_first_charge_data = require "hero_first_charge_data"
                hero_first_charge_data.SetFreeBoxActivityData(activityData.activityID)
            end
            if activityID == 3005 then
                --万圣节老虎机礼包
                local halloween_slot_machine_mgr = require "halloween_slot_machine_mgr"
                halloween_slot_machine_mgr.SetDailyGiftActivityID(activityData.activityID)
            end
        end
    end),
    --城市竞赛
    [ActivityCodeType.CitySiege] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local city_siege_activity_data = require "city_siege_activity_data"
            city_siege_activity_data.SetActivityTime(activityData.activityID,activityData.startTimeStamp,activityData.endTimeStamp)
        end
    end),
    -- 繁荣基金
    [ActivityCodeType.UniversalRecharge] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            if activityID == 1018 or activityID == 1022 then
                --繁荣基金
                local mgr_prosperity_fund = require "mgr_prosperity_fund"
                mgr_prosperity_fund.SetProsperityFundData(activityData.activityID, activityData.endTimeStamp, k, v)
            end
        end
    end),
    -- 战令
    [ActivityCodeType.BattlePass2] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local mgr_battle_pass = require "mgr_battle_pass"
            mgr_battle_pass.SetMainBattlePassData(activityData.activityID, activityData.endTimeStamp, k, v)
        end
    end),
    [ActivityCodeType.BattlePass3] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local mgr_battle_pass = require "mgr_battle_pass"
            mgr_battle_pass.SetMainBattlePassData(activityData.activityID, activityData.endTimeStamp, k, v)
        end
    end),
    [ActivityCodeType.BattlePass2UnTask] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local mgr_battle_pass = require "mgr_battle_pass"
            mgr_battle_pass.SetMainBattlePassData(activityData.activityID, activityData.endTimeStamp, k, v)
        end
    end),
    [ActivityCodeType.AllianceDuel] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT2D1 +1 then
                activityData.countDown = v
            end
        end
    end),
    -- 全面备战
    [ActivityCodeType.FullyWar] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local gw_fully_war_mgr = require "gw_fully_war_mgr"
            gw_fully_war_mgr.SetMainAtyIDList(activityData.activityID)
        end
    end),
    -- 先锋目标
    [ActivityCodeType.PioneerActivity] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local pioneer_target_data = require "pioneer_target_data"
            --设置任务配置数据
            pioneer_target_data.BuildCfgData(activityData.activityID)
            pioneer_target_data.SetCurAtyID(activityData.activityID)
            pioneer_target_data.BuildScoreRewardCfgData(activityData.activityID)
        end
    end),
    -- 每日特惠
    [ActivityCodeType.DailyGift] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local ui_daily_gift_activity_mgr = require "ui_daily_gift_activity_mgr"
            if k == topic_pb.ETOPICNAMEFESTIVAL_ACTIVITY_CONTENT1D2 +1 then
                ui_daily_gift_activity_mgr.SetSelectID(v)
            end
        end
    end),
    -- 周卡
    [ActivityCodeType.WeekendCard] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
           local weekend_card_data = require "weekend_card_data"
           weekend_card_data.SetActivityId(activityID)
        end
    end),
    [ActivityCodeType.MonthCard] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
           local month_card_data = require "month_card_data"
           month_card_data.SetActivityId(activityID)
        end
    end),   
    -- 登录好礼
    [ActivityCodeType.LoginGift] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local login_gift_data = require "login_gift_data"
            login_gift_data.SetActivityId(activityID)
        end
    end),
    --钻石商店
    [ActivityCodeType.DiamondGift]= (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local diamond_gift_data = require "diamond_gift_data"
            diamond_gift_data.SetActivityId(activityID)
        end
    end),
    --限时礼包
    [ActivityCodeType.LimitGift]= (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local limit_gift_data = require "limit_gift_data"
            limit_gift_data.SetMainActivityId(activityID)
        end
    end),
    --特殊限时礼包 （英雄礼包）
    [ActivityCodeType.LimitGiftSpecial]= (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local limit_gift_data = require "limit_gift_data"
            limit_gift_data.SetSpecialActivityId(activityID)
        end
    end),
    --丧尸来袭
    [ActivityCodeType.ZombiesAttack]= (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local zombies_attacking_data = require "zombies_attacking_data"
            zombies_attacking_data.SetActivityId(activityID)
        end
    end),
    --闪金集市
    [ActivityCodeType.ShiningShop]=(function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local shining_market_data = require "shining_market_data"
            shining_market_data.SetActivityId(activityID)
        end
    end),
    --神迹宝箱
    [ActivityCodeType.MiracleBox]=(function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local miracle_box_data = require "miracle_box_data"
            miracle_box_data.SetActivityId(activityID)
        end
    end),
    [ActivityCodeType.DesertStorm] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then

        end
    end),
    --末日降临
    [ActivityCodeType.Doomsday]=(function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local doomsday_data = require "doomsday_data"
            doomsday_data.SetActivityId(activityID)
        end
    end),
    --自选周卡
    [ActivityCodeType.OptionalWeekCard]=(function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local optional_weekcard_data = require "optional_weekcard_data"
            optional_weekcard_data.SetActID(activityID)
        end
    end),
    --风暴营救
    [ActivityCodeType.StormRescue]=(function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local storm_rescue_data = require "storm_rescue_data"
            storm_rescue_data.SetActivityId(activityID)
        end
    end),
    [ActivityCodeType.StormRescue2]=(function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local storm_rescue_data = require "storm_rescue_data"
            storm_rescue_data.SetActivityId(activityID)
        end
    end),
    [ActivityCodeType.StormRescue3]=(function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local storm_rescue_data = require "storm_rescue_data"
            storm_rescue_data.SetActivityId(activityID)
        end
    end),
    [ActivityCodeType.MiniGame]=(function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID == 218 or activityID == 50002 then
            local storm_rescue_data = require "storm_rescue_data"
            storm_rescue_data.SetMiniGameActivityId(activityID)
        elseif activityID == 224 then
            local hero_first_charge_data = require "hero_first_charge_data"
            hero_first_charge_data.SetMiniGameActivityId(activityID)
        elseif activityID == 230 then
            local storm_rescue_data = require "storm_rescue_data"
            storm_rescue_data.SetMiniGameActivityId(activityID)
        end
    end),
    -- 月月超值
    [ActivityCodeType.SuperMonthGift]=(function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local super_month_gift_data = require "super_month_gift_data"
            super_month_gift_data.SetActID(activityID)
        end
    end),
    [ActivityCodeType.HeroFirstRecharge] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local hero_first_charge_data = require "hero_first_charge_data"
            hero_first_charge_data.SetActivityId(activityID)
        end
    end),
    [ActivityCodeType.LandRevivalBattlePass] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            --local land_revival_data = require "land_revival_data"
            --land_revival_data.SetActivityId(activityID)
        end
    end),
    [ActivityCodeType.LandRevivalMain] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local land_revival_data = require "land_revival_data"
            land_revival_data.SetActivityId(activityID)
        end
    end),
    [ActivityCodeType.LandRevivalTask] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local land_revival_data = require "land_revival_data"
            land_revival_data.SetActivityTaskID(activityID)
        end
    end),
    [ActivityCodeType.HalloweenWingding] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local halloween_wingding_data = require "halloween_wingding_data"
            halloween_wingding_data.SetActivityId(activityID)
        end
    end),
    [ActivityCodeType.HalloweenWingdingRank] = (function(k, v, activityData)
        local activityID = activityData.activityID
        if activityID then
            local halloween_wingding_data = require "halloween_wingding_data"
            halloween_wingding_data.SetRankActivityID(activityID)
        end
    end),
}

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 活动数据后处理器集合
--- 处理函数只会执行一次
---@type table<number, fun(activityData: table): void>
local ActivityDataPostprocessorCollection = {
    -- 10 神匣遗迹
    -- [ActivityCodeType.Relics] = (function(activityData)
    --     local activity_flop_data = require "activity_flop_data"
    --     activity_flop_data.ActivityVersionSet(activityData.versionNumber)
    -- end),
    -- 16 英雄试炼（试炼副本）
    [ActivityCodeType.HeroExclusive] = (function(activityData)
        
        --new_week_activity_data.SetHeroExclusiveVersion(activityData.versionNumber)
    end),
    -- 17 Bingo
    -- [ActivityCodeType.Bingo] = (function(activityData)
    --     local activity_bingo_data = require "activity_bingo_data"
    --     activity_bingo_data.ActivityVersionSet(activityData.versionNumber)
    -- end),
    -- 19 新版徽章活动-道具累积获得
    [ActivityCodeType.BadgeCumulativeProps] = (function(activityData)
        festival_activity_mgr.BuildProgressData(activityData.topicID)
        festival_activity_mgr.PickGameEventReport(activityData, activityData.topicID)
    end),
    -- 22 英雄重生
    [ActivityCodeType.HeroRespawn] = (function(activityData)
        festival_activity_mgr.PickDecomposeShopData(activityData)
    end),
    -- 24 圣诞树
    [ActivityCodeType.ChristmasTree] = (function(activityData)
        if activityData.headingCode == 3 then
            festival_activity_mgr.ChristmasActivityEventReport(activityData.topicID)
        end
    end),
    -- 50 画糖人-- 服务器说不需要了
    --[ActivityCodeType.DrawSugarman] = (function(activityData)
    --    if activityData.activityID and festival_activity_mgr.IsActivityOpen(activityData.topicID) then
    --        local net_festival_activity_module = require "net_festival_activity_module"
    --        net_festival_activity_module.Request_DrawTheSugarManInfo(activityData.activityID)
    --    end
    --end),
}

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 获取活动数据预处理器列表
---@type (fun(topicID: number, bitData: table, activityData: table): void)[]
function GetActivityDataPreprocessors()
    return ActivityDataPreprocessorList
end

--- 添加活动数据预处理器（插入到列表尾部）
---@param processor (fun(topicID: number, bitData: table, activityData: table): void)
function AddActivityDataPreprocessor(processor)
    table.insert(ActivityDataPreprocessorList, processor)
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 获取活动数据处理器
---@param activityCodeType number 活动类型码
---@return fun(k: number, v: number, activityData: table): boolean
function GetActivityDataLoopProcessor(activityCodeType)
    local processor = ActivityDataLoopProcessorCollection[activityCodeType]
    if (not processor) then
        return ActivityDataLoopProcessorCollection[ActivityCodeType.Default]
    end
    local processorType = type(processor)
    if (processorType == "function") then
        return processor
    elseif (processorType == "number") then
        return GetActivityDataLoopProcessor(processor)
    else
        log.Error("PP* 无效的活动数据处理器！| codeType: " .. tostring(activityCodeType))
        return nil
    end
end

--- 注册活动数据处理器
---@param activityCodeType number 活动类型码
---@param processor fun(k: number, v: number, activityData: table): boolean 数据处理函数
function RegisterActivityDataLoopProcessor(activityCodeType, processor)
    if ActivityDataLoopProcessorCollection[activityCodeType] then
        log.Warning("PP* 重复注册活动数据处理器！| codeType: " .. tostring(activityCodeType))
    end
    ActivityDataLoopProcessorCollection[activityCodeType] = processor
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 获取活动数据后处理器
---@param activityCodeType number 活动类型码
---@return fun(activityData: table): void
function GetActivityDataPostprocessor(activityCodeType)
    local processor = ActivityDataPostprocessorCollection[activityCodeType]
    if (not processor) then
        return nil
    end
    local processorType = type(processor)
    if (processorType == "function") then
        return processor
    elseif (processorType == "number") then
        return GetActivityDataPostprocessor(processor)
    else
        log.Error("PP* 无效的活动数据后处理器！| codeType: " .. tostring(activityCodeType))
        return nil
    end
end

--- 注册活动数据后处理器
---@param activityCodeType number 活动类型码
---@param processor fun(activityData: table): void 数据处理函数
function RegisterActivityDataPostprocessor(activityCodeType, processor)
    if ActivityDataPostprocessorCollection[activityCodeType] then
        log.Warning("PP* 重复注册活动数据后处理器！| codeType: " .. tostring(activityCodeType))
    end
    ActivityDataPostprocessorCollection[activityCodeType] = processor
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 处理活动数据 - 预处理 Pass
---@param topicID number
---@param bitData table
---@param activityData table
local function ProcessActivityData_PreprocessPass(topicID, bitData, activityData)
    local processors = GetActivityDataPreprocessors()
    if (not processors) then
        return
    end
    for _, v in ipairs(processors) do
        v(topicID, bitData, activityData)
    end
end

--- 处理活动数据 - 循环 Pass（处理函数可能会执行多次，会遍历 bitData 中的 key）
---@param topicID number
---@param bitData table
---@param activityData table
local function ProcessActivityData_LoopProcessPass(topicID, bitData, activityData)
    local dataProcessor = GetActivityDataLoopProcessor(activityData.activityCodeType)
    if (not dataProcessor) then
        return
    end
    for k, v in pairs(bitData) do
        if type(k) == "number" then
            local canBreakLoop = dataProcessor(k, v, activityData)
            -- 当数据处理器返回 true 时可以提前跳出循环
            if (canBreakLoop == true) then
                break
            end
        end
    end
end

--- 处理活动数据 - 后处理 Pass（处理函数只会执行一次）
---@param topicID number
---@param bitData table
---@param activityData table
local function ProcessActivityData_PostprocessPass(topicID, bitData, activityData)
    local processor = GetActivityDataPostprocessor(activityData.activityCodeType)
    if (not processor) then
        return
    end
    processor(activityData)
end

--- 处理活动数据
---@param topicID number
---@param bitData table
---@param activityData table
function ProcessActivityData(topicID, bitData, activityData)
    -- 预处理 Pass（处理函数只会执行一次）
    ProcessActivityData_PreprocessPass(topicID, bitData, activityData)

    -- 循环处理 Pass（处理函数可能会执行多次，会遍历 bitData 中的 key）
    ProcessActivityData_LoopProcessPass(topicID, bitData, activityData)

    -- 后处理 Pass（处理函数只会执行一次）
    ProcessActivityData_PostprocessPass(topicID, bitData, activityData)
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
