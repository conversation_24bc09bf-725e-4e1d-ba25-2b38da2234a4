-- gw_comp_entity.txt ------------------------------------------
-- author:  fgy
-- changed: connan.
-- desc:    沙盘数据实体
--------------------------------------------------------------
local require = require
local ipairs = ipairs
local table = table
local tonumber = tonumber
local pairs = pairs
local string = string
local os = os
local math = math

local gw_common_util = require "gw_common_util"
local game_scheme = require "game_scheme"

local gw_const = require "gw_const"
local EntityType = gw_const.ESEntityType
local gw_proto_prop = require "gw_proto_prop"
local gw_general_prop = require "gw_general_prop"
local gw_sand_entity_gather = require "gw_sand_entity_gather"
local sandbox_pb = require "sandbox_pb"
local util = require "util"
local log = require "log"
local bit = require "bit"
local band = bit.band

local propName2ID = {
    -- Existing property mappings
    baseLv = sandbox_pb.enSXEntityProp_Base_Lv,
    baseKillNum = sandbox_pb.enSXEntityProp_Base_KillNum,
    basePower = sandbox_pb.enSXEntityProp_Base_Power,
    baseSkinID = sandbox_pb.enSXEntityProp_Base_SkinID,
    nationalFlagID = sandbox_pb.enSXEntityProp_NationalFlag_ID,
    monsterCountDown = sandbox_pb.enSXEntityProp_Fixed_Countdown,
    monsterFirstLevel = sandbox_pb.enSXEntityProp_Fixed_MaxKIllLv,
    monsterDayMReward = sandbox_pb.enSXEntityProp_Fixed_DayMRewardCnt,
    monsterDaySReward = sandbox_pb.enSXEntityProp_Fixed_DaySRewardCnt,
    resNum = sandbox_pb.enSXEntityProp_Resource_Num,
    resBeginTime = sandbox_pb.enSXEntityProp_Resource_BeginTime,
    resEndTime = sandbox_pb.enSXEntityProp_Resource_EndTime,
    resCollectSpeed = sandbox_pb.enSXEntityProp_Resource_Speed,
    cfgId = sandbox_pb.enSXEntityProp_Cfg_Id,
    faceId = sandbox_pb.enSXEntityProp_FaceID,
    frameId = sandbox_pb.enSXEntityProp_FrameID,
    namePlateId = sandbox_pb.enSXEntityProp_PlateID,
    playerLevel = sandbox_pb.enSXEntityProp_PlayerLevel,
    roleId = sandbox_pb.enSXEntityProp_RoleID,
    unionID = sandbox_pb.enSXEntityProp_AllianceID,
    serverId = sandbox_pb.enSXEntityProp_ServerID,
    wanderHp = sandbox_pb.enSXEntityProp_Wonder_HP,
    detectTimeStamp = sandbox_pb.enSXEntityProp_Detect_Time1,
    detectButtonCDTimeStamp = sandbox_pb.enSXEntityProp_Detect_Time2,
    detectGold = sandbox_pb.enSXEntityProp_Detect_Gold,
    detectIron = sandbox_pb.enSXEntityProp_Detect_Iron,
    detectFood = sandbox_pb.enSXEntityProp_Detect_Food,
    targetSid = sandbox_pb.enSXEntityProp_March_TargetId,
    marchWonderId = sandbox_pb.enSXEntityProp_March_WonderID,
    followLineSid = sandbox_pb.enSXEntityProp_March_FollowLineSid,
    baseSafety = sandbox_pb.enSXEntityProp_Base_Safety,
    baseCurDefend = sandbox_pb.enSXEntityProp_Base_CUR_DEFEND,
    baseWallBeginTime = sandbox_pb.enSXEntityProp_Base_WALL_BEGINTIME,
    baseWallEndTime = sandbox_pb.enSXEntityProp_Base_WALL_FIRE_ENDTIME,
    baseCityWallLevel = sandbox_pb.enSXEntityProp_Base_CITY_WALL_LEVEL,
    battleState = sandbox_pb.enSXEntityProp_March_BattleState,
    targetDbid = sandbox_pb.enSXEntityProp_March_TargetDBId,
    lastTargetId = sandbox_pb.enSXEntityProp_March_LastTargetId,

    isVisibleToSelf = sandbox_pb.enSXEntityProp_Is_Only_Visible_To_Self,
    isRadarCreate = sandbox_pb.enSXEntityProp_IsRadarCreate,
    radarTaskSID = sandbox_pb.enSXEntityProp_RadarMissionId,
    createRoleID = sandbox_pb.enSXEntityProp_CreatorRoleID,
    rewardNumber = sandbox_pb.enSXEntityProp_RewardNumber,
    worldBossAchieve = sandbox_pb.enSXEntityProp_WORLDBOSS_ACHIEVE,
    worldBossChallenges = sandbox_pb.enSXEntityProp_WORLDBOSS_CHALLENGES,
    worldBossBornStamp = sandbox_pb.enSXEntityProp_WORLDBOSS_BORNTM,
    worldBossDeathStamp = sandbox_pb.enSXEntityProp_WORLDBOSS_DEATHTM,
    allianceBossStateTime = sandbox_pb.enSXEntityProp_AllianceBoss_StateTime,
    allianceBossTotalDamage = sandbox_pb.enSXEntityProp_AllianceBoss_TotalDamage,
    progressSum = sandbox_pb.enSXEntityProp_Treasure_Progress_Sum,
    progressNow = sandbox_pb.enSXEntityProp_Treasure_Progress_Now,
    radarTaskID = sandbox_pb.enSXEntityProp_RadarMission_TaskId,
    carriageQuality = sandbox_pb.enSXEntityProp_Carriage_Quality,
    carriageId = sandbox_pb.enSXEntityProp_March_CarriageID,
    carriageTradeId = sandbox_pb.enSXEntityProp_Carriage_TradeID,
    carriageTradeTm = sandbox_pb.enSXEntityProp_Carriage_TradeTm,
    carriageCompleteness = sandbox_pb.enSXEntityProp_Carriage_Interitry,
    carriageNextTm = sandbox_pb.enSXEntityProp_Carriage_NxtSiteTm,
    carriageVisible = sandbox_pb.enSXEntityProp_Carriage_Visiable,
    carriageLineId = sandbox_pb.enSXEntityProp_Carriage_LineID,
    generalTrialBelongId = sandbox_pb.enSXEntityProp_GeneralTrial_BelongId,
    massAssort = sandbox_pb.enSXEntityProp_Base_Misc_Flag,
    ncDefendersNum = sandbox_pb.enSXEntityProp_Neutral_SoldierNum,
    ncDefenseValue = sandbox_pb.enSXEntityProp_Neutral_Durability,
    ncFirstOccupy = sandbox_pb.enSXEntityProp_Neutral_IsFirst,
    ncAllianceIdx = sandbox_pb.enSXEntityProp_Neutral_AllianceIdx,
    ncProtectCD = sandbox_pb.enSXEntityProp_NC_ProtectCD,
    ncAllianceIconID = sandbox_pb.enSXEntityProp_NC_AllianceIconID,
    ncOfflineAttackCD = sandbox_pb.enSXEntityProp_NC_OfflineAttackCD,
    ncAbandonCD = sandbox_pb.enSXEntityProp_NC_AbandonCD,
    ncDWEndTime = sandbox_pb.enSXEntityProp_NC_DW_EndTime,
    tavernTaskID = sandbox_pb.enSXEntityProp_AcornPub_TaskID,
    tavernDoneTime = sandbox_pb.enSXEntityProp_AcornPub_DoneTime,
    tavernTaskSID = sandbox_pb.enSXEntityProp_AcornPub_TaskSID,
    worldID = sandbox_pb.enSXEntityProp_Base_WorldId,
    congressOfficial = sandbox_pb.enSXEntityProp_Base_CongressOfficial,
    lineEntityId = sandbox_pb.enSXEntityProp_LineEntityId,

    ---联盟火车数值属性
    allianceTrainType = sandbox_pb.enSXEntityProp_AllianceTrain_TrainType,
    allianceTrainId = sandbox_pb.enSXEntityProp_AllianceTrain_TrainID,
    allianceTrainLootCount = sandbox_pb.enSXEntityProp_AllianceTrain_LootCount,
    allianceTrainArriveTime = sandbox_pb.enSXEntityProp_AllianceTrain_ArrivalTime,
    allianceTrainLineId = sandbox_pb.enSXEntityProp_AllianceTrain_LineID,
    allianceTrainFlag = sandbox_pb.enSXEntityProp_AllianceTrain_AllianceFlag,

    --丧尸来袭
    zombieOwner = sandbox_pb.enSXEntityProp_ZombieOwner,

    --沙漠风暴
    --沙漠风暴建筑解锁时间
    storm_UnlockTime = sandbox_pb.enSXEntityProp_DesertStorm_UnlockTime,
    --沙漠风暴归属联盟id
    storm_AllianceId = sandbox_pb.enSXEntityProp_DesertStorm_BelongAllianceId,
    --沙漠风暴归属玩家id
    storm_BelongRoleId = sandbox_pb.enSXEntityProp_DesertStorm_BelongRoleId,
    --沙漠风暴归属玩家头像框id
    storm_BelongRoleFrameId = sandbox_pb.enSXEntityProp_DesertStorm_BelongRoleFrameId,
    --沙漠风暴是否为第一次占领
    storm_IsFirstOccupy = sandbox_pb.enSXEntityProp_DesertStorm_IsFirstOccupy,
    -- 沙漠风暴第一个占领者血量
    storm_FirstOccupierHp = sandbox_pb.enSXEntityProp_DesertStorm_FirstOccupierHp,
    -- 沙漠风暴积分宝箱积分值
    storm_ScoreBoxScoreNum = sandbox_pb.enSXEntityProp_DesertStorm_ScoreBoxScoreNum,
    -- 沙漠风暴油井更新时间
    storm_OilWellChangeTime = sandbox_pb.enSXEntityProp_DesertStorm_OilWellChangeTime,
    -- 沙漠风暴油井更新时积分值
    storm_OilWellChangeNum = sandbox_pb.enSXEntityProp_DesertStorm_OilWellChangeNum,
    -- 沙漠风暴联盟旗帜
    storm_AllianceFlag = sandbox_pb.enSXEntityProp_DesertStorm_AllianceFlag,
    -- 沙漠风暴迁城cD时间
    storm_MoveCityCD = sandbox_pb.enSXEntityProp_DesertStorm_MoveCityCD,

    -- 被集结个数
    massCount = sandbox_pb.enSXEntityProp_BeMassCount,

    --基地特效Id
    baseEffectID = sandbox_pb.enSXEntityProp_Base_EffectID,

    --战区对决占领状态
    occupy_duel_worldID = sandbox_pb.enSXEntityProp_ZoneBattleDuel_Giant_Occupy_World,
    --战区对决占领积分
    occupy_world_score = sandbox_pb.enSXEntityProp_ZoneBattleDuel_Occupy_World_Score,
    --战区对决攻方占领积分
    occupy_world_score_atk = sandbox_pb.enSXEntityProp_ZoneBattleDuel_AtkWorld_Score,
    --战区对决守方占领积分
    occupy_world_score_def = sandbox_pb.enSXEntityProp_ZoneBattleDuel_DefWorld_Score,
    --战区对决占领时间
    occupy_world_overTime = sandbox_pb.enSXEntityProp_ZoneBattleDuel_Occupy_World_OverTime,
    
    
    --战区对决-征服者玩家头像ID
    zoneDuel_FaceID = sandbox_pb.enSXEntityProp_ZoneBattleDuel_FaceID,
    --通用字段-征服者玩家头像框ID
    zoneDuel_FrameID = sandbox_pb.enSXEntityProp_ZoneBattleDuel_FrameID,

    --通用字段-自定义字段
    common_entityCfgID = sandbox_pb.enSXEntityProp_General_Association_CfgID,
    --攻城营寨-已增援队伍数量
    camp_reinforceCount = sandbox_pb.enSXEntityProp_SiegeCamp_Reinforcements_Count,
    --攻城营寨-营寨耐久度
    camp_durability = sandbox_pb.enSXEntityProp_SiegeCamp_Durability,

    --丧尸灾变难度等级
    zombieStorm_difficultyLevel = sandbox_pb.enSXEntityProp_ZombieApocalypse_DifficultyLevel,
    --丧尸灾变据点当前状态
    zombieStorm_state = sandbox_pb.enSXEntityProp_ZombieApocalypse_State,
    --丧尸灾变波数
    zombieStorm_wave = sandbox_pb.enSXEntityProp_ZombieApocalypse_Wave,
    --丧尸灾变下一节点时间
    zombieStorm_time = sandbox_pb.enSXEntityProp_ZombieApocalypse_Time,
    --丧尸灾变防御波数
    zombieStorm_defensiveWave = sandbox_pb.enSXEntityProp_ZombieApocalypse_DefensiveWave,

    --丧尸灾变是否可见
    zombieStorm_visiable = sandbox_pb.enSXEntityProp_ZombieApocalypse_Visiable,
    --丧尸所属玩家
    zombieStorm_belongRoleId = sandbox_pb.enSXEntityProp_ZombieApocalypse_BelongRoleId,
    --丧尸行军id
    zombieStorm_lineId = sandbox_pb.enSXEntityProp_ZombieApocalypse_LineID,
    --定时器id
    zombiestorm_triggerId = sandbox_pb.enSXEntityProp_ZombieApocalypse_TriggerID,
    --丧尸灾变怪物
    zombieStorm_monsterId = sandbox_pb.enSXEntityProp_ZombieApocalypse_MonsterID,
    --通用字段，基地bool位
    base_bool_bit = sandbox_pb.enSXEntityProp_Base_BoolBit,
}

local propNameDefaultValue = {
    [propName2ID.ncDefendersNum] = -1,
    [propName2ID.ncDefenseValue] = -1,
}

local propStrName2ID = {
    -- New string properties
    roleName = sandbox_pb.enSXEntityPropStr_RoleName,
    unionName = sandbox_pb.enSXEntityPropStr_AllianceName,
    unionShortName = sandbox_pb.enSXEntityPropStr_AllianceShortName,
    targetName = sandbox_pb.enSXEntityProp_March_TargetName,
    ncAllianceList = sandbox_pb.enSXEntityProp_NC_AllianceList,
    ncCongressInfo = sandbox_pb.enSXEntityProp_NC_CongressInfo,
    tavernStealPlayers = sandbox_pb.enSXEntityPropStr_AcornPub_RobPlayer,
    faceStr = sandbox_pb.enSXEntityPropStr_FaceStr,
    zoneDuel_FaceStr = sandbox_pb.enSXEntityPropStr_ZoneBattleDuel_FaceStr,
    camp_cityPos = sandbox_pb.enSXEntityProp_SiegeCamp_CityPos,
    unionShortName_zoneDuel=sandbox_pb.enSXEntityProp_ZoneBattleDuel_Occupy_AllianceShort,
    
}

--资源数据，客户端解析配置数据
local resProps = {
    resId = 0,
    visionType = 0,
    colorType = {},
    ModelRes = "",
    resCfg = {},
    ownerType = 0,
    simpleLevel = 0,
}

local resPropsName2Index = {}
local index = 0
for k, v in pairs(resProps) do
    resPropsName2Index[k] = index
    index = index + 1
end

local propID2Name = {}
for k, v in pairs(propName2ID) do
    propID2Name[v] = k
end

local propStrID2Name = {}
for k, v in pairs(propStrName2ID) do
    propStrID2Name[v] = k
end

local comp = {}

local remap_mt = {
    -- Only handle property access, not method calls
    __index = function(self, name)
        local method = comp[name]
        if method then
            return method
        end
    end
}

function comp:CreateInstance()
    return setmetatable({}, remap_mt)
end

function comp.PreViewParse(pb)
    local prePB = { type = pb.type }
    local propType = pb.propType
    local propValue = pb.propValue

    if not propType then
        return
    end

    for k, v in ipairs(propType) do
        if v >= 20 then
            break
        end
        if propID2Name[v] then
            prePB[propID2Name[v]] = propValue[k]
        end
    end
    return prePB
end

comp.propName2ID = propName2ID
comp.propID2Name = propID2Name

comp.propStrName2ID = propStrName2ID
comp.propStrID2Name = propStrID2Name
function comp:Create(data)
    self:UpdateData(data)
    self:UpdateModuleResProp()
    self:AddPropListener()
end

local newPropAddListenerKes = { "baseLv", "baseSkinID", "unionID", "roleId", "storm_AllianceId" }
local newStrPropAddListenerKes = "unionShortName"
function comp:AddPropListener()
    gw_proto_prop.AddListener(self.props, newPropAddListenerKes, comp.UpdateModuleResProp, self)
    gw_proto_prop.AddListener(self.strProps, newStrPropAddListenerKes, comp.UpdateModuleResProp, self)
end

function comp:UpdateData(data)
    self.type = data.type                   -- 类型
    self.sid = data.sid                     -- 服务器下发唯一id

    self.collect_addLoad = data.collect_addLoad or self.collect_addLoad  --士兵负重提升, 百分值,  下标(1~4) = 队伍
    self.rewards = data.rewards or self.rewards  --奖励信息
    local pos = data.pos
    if pos then
        self.pos = self.pos or {}
        self.pos.x = pos.x                     -- 位置
        self.pos.y = pos.y                     -- 位置
        self.worldPos = { x = self.pos.x, y = 0, z = self.pos.y }
    end

    local propType = data.propType or {}
    local propValue = data.propValue or {}
    if not self.props then
        local defaultValue = self.type == gw_const.ESEntityType.NeutralCity and propNameDefaultValue or nil
        self.props = gw_proto_prop.New(propType, propValue, propName2ID, true, defaultValue)
    else
        gw_proto_prop.UpdateData(self.props, propType, propValue)
    end

    local strPropsType = data.strPropsType or {}
    local strPropsValue = data.strPropsValue or {}
    if not self.strProps then
        self.strProps = gw_proto_prop.New(strPropsType, strPropsValue, propStrName2ID, false)
    else
        gw_proto_prop.UpdateData(self.strProps, strPropsType, strPropsValue)
    end

    local gw_sand_mgr = require "gw_sand_mgr"
    if gw_sand_mgr.GetNewModelResourceSwitch() then
        local gw_sand_entity_define = require "gw_sand_entity_define"
        local entityDefine = gw_sand_entity_define.GetEntityDefine(self.type)
        if entityDefine then
            self.cfg = entityDefine.getCfg(self.props, self, self.type)
        end
    else
        local cfgId = self.type == EntityType.Base and self.props.baseSkinID or self.props.cfgId
        local entityType = gw_sand_entity_gather[data.type]
        if entityType then
            self.cfg = entityType.GetCfg(cfgId)
        end
    end

    if not self.nextDispatchChangeOff then
        gw_proto_prop.DispatchChange(self.props)
        gw_proto_prop.DispatchChange(self.strProps)
    else
        self.nextDispatchChangeOff = false
    end

    self.nationalFlagID = self.props.name2ID.nationalFlagID
end

----重置prop数据
function comp:ClearPropData()

    self.props = nil
    self.strProps = nil
    self.nextDispatchChangeOff = true
end

function comp:UpdateModuleResProp()
    local gw_sand_mgr = require "gw_sand_mgr"
    local resTab = gw_sand_mgr.GetSandMapModelResourceKey(self)
    if not resTab then
        return
    end
    if not self.resProps then
        self.resProps = gw_general_prop.New(resTab, resProps, resPropsName2Index)
    else
        local changedMask = gw_general_prop.UpdateData(self.resProps, resTab)
        gw_general_prop.DispatchChange(self.resProps, changedMask)
    end
end

comp.UpdateModuleResPropNoDelay = comp.UpdateModuleResProp

-- 更新详细数据
function comp:UpdateDetailData(data)
    --采集信息专用
    self:UpdateCollectSpeed(data)

    self.collect_addLoad = data.collect_addLoad or self.collect_addLoad  --士兵负重提升, 百分值,  下标(1~4) = 队伍

    --挖掘宝藏专用
    self.roleInfoList = data.roleInfoList or self.roleInfoList

    --城市竞赛积分信息，需要排序
    self:UpdateRankInfoList(data)

    -- 国会专用,总统名字
    self.presName = data.presName

    self.isWonder = data.type == sandbox_pb.enSandboxEntity_WonderMonster
    local detailProp = {}
    if data.props then
        local name
        for key, propData in ipairs(data.props) do
            name = propID2Name[propData.propID]
            if name then
                detailProp[name] = propData.value
            else
                log.Warning("propID2Name not found propID " .. propData.propID)
            end
        end
    end
    gw_proto_prop.SetDetailProp(self.props, detailProp)

    local detailPropStr = {}
    if data.strProps then
        local name
        for key, propData in ipairs(data.strProps) do
            name = propStrID2Name[propData.propID]
            if name then
                detailPropStr[name] = propData.strValue
            else
                log.Warning("propID2Name not found propID " .. propData.propID)
            end
        end
    end
    gw_proto_prop.SetDetailProp(self.strProps, detailPropStr)
end
-------------------endif---------------------

---更新采集速度
function comp:UpdateCollectSpeed(data)
    if data.collect_speed then
        --2024/11/29 服务器要求 采集速度  改为 采集速度/10000 --采集速度, 下标(1~4) = 队伍
        self.collect_speed = data.collect_speed
        for i, v in ipairs(data.collect_speed) do
            self.collect_speed[i] = v / 10000
        end
    end
end

-- 更新排行榜数据
function comp:UpdateRankInfoList(data)
    if data.rankInfoList then
        table.sort(data.rankInfoList, function(a, b)
            if a and b then
                if a.nScore > b.nScore then
                    return true
                elseif a.nScore == b.nScore and a.ScoreTime and b.ScoreTime then
                    return a.ScoreTime < b.ScoreTime
                end
            end
            return false
        end)
        self.rankInfoList = data.rankInfoList
    else
        self.rankInfoList = nil
    end
end

function comp:Dispose()
    self.type = nil                   -- 类型
    self.sid = nil                    -- 服务器下发唯一id	
    self.pos = nil                     -- 位置
    self.props = nil         -- 数值属性
    self.strProps = nil    -- 字符属性
    self.worldPos = nil
    self.cfg = nil
    self.resProps = nil
    --采集信息专用
    self.collect_speed = nil      --采集速度, 下标(1~4) = 队伍
    self.collect_addLoad = nil  --士兵负重提升, 百分值,  下标(1~4) = 队伍

    self.roleInfoList = nil
    self.cfgId = nil
    self.nationalFlagID = nil
end

function comp:GetModuleRes()
    return self.resProps and self.resProps.ModelRes
end

function comp:GetLevelHudOffsetY()
    return self.resProps and self.resProps.resCfg.levelHudOffsetY
end

function comp:GetLevelIconOffsetY()
    return self.resProps and self.resProps.resCfg.levelIconOffsetY
end

--- 获取玩家名称通用
function comp:GetBaseName()
    return self.strProps and self.strProps.roleName
end

--- 获取联盟简称（通用联盟简称）
function comp:GetUnionShortAndRoleName()
    if string.IsNullOrEmpty(self:GetUnionShortName()) then
        return self:GetBaseName()
    else
        return util.SplicingUnionShortName(self:GetUnionShortName(), self:GetBaseName())
    end
end

--- 获取世界id+联盟简称+玩家名字
function comp:GetWorldUnionShortAndRoleName()
    local str = ""
    local sandBoxSid = gw_common_util.GetSandSandBoxSid()
    if (sandBoxSid) then
        str = "#" .. sandBoxSid
    end

    if string.IsNullOrEmpty(self:GetUnionShortName()) then
        return str .. self:GetBaseName()
    else
        return str .. util.SplicingUnionShortName(self:GetUnionShortName(), self:GetBaseName())
    end
end

function comp:GetLevelTxt()
    local level = self.props and self.props.playerLevel
    return level and "Lv." .. level
end

--- 是否是基地
function comp:GetIfBase()
    return self.type and self.type == EntityType.Base
end

--- 获取roleId
function comp:GetRoleId()
    return self.props and self.props.roleId
end

--将军试炼归属ID
function comp:GetGeneralTrialBelongId()
    return self.props and self.props.generalTrialBelongId
end

---获取行军状态
function comp:GetState()
    return self.state
end

--- 是否是自己
function comp:GetIfSelf()
    local player_mgr = require "player_mgr"
    local roleId = player_mgr.GetPlayerRoleID()
    local curRoleId = self:GetRoleId()
    if self.type and self.type == EntityType.Storm then
        curRoleId = self:GetStormRoleId()
    end
    return curRoleId == roleId
end

--- 获取玩家等级
function comp:GetPlayerLevel()
    return self.props and self.props.playerLevel
end

--判定是联盟boss建筑且是自己的
function comp:GetIfSelfAllianceBoss()
    local finalKey2 = self:GetFinalKey2()
    if not finalKey2 then
        return false
    end
    if finalKey2 ~= GWConst.ESEntityResViewType.AllianceBoss then
        return false
    end
    local alliance_mgr = require "alliance_mgr"
    local selfName = alliance_mgr.GetUserAllianceShortName()
    if not selfName or selfName ~= self:GetUnionShortName() then
        return false
    end
    return true
end

function comp:GetShowResTip()
    if self.type == EntityType.Resource then
        return (self.props and self.props.roleId) > 0
    end
    return false
end

--- 获取基地等级
function comp:GetBaseLv()
    return self.props and self.props.baseLv
end

--- 获取基地击杀数
function comp:GetBaseKillNum()
    return self.props and self.props.baseKillNum
end

--- 获取基地战力
function comp:GetBasePower()
    return self.props and self.props.basePower
end

--- 获取基地城防值, back1 为最大值, back2 为当前值
local gw_home_mgr
function comp:GetBaseDefend()
    local maxHp, curHp = 1, 0
    if gw_common_util.CheckInSand_Storm() then
        curHp = self.props.baseCurDefend
        local gw_storm_mgr = require "gw_storm_mgr"
        local rewardCfg = gw_storm_mgr.GetStormCfg().GetDesertStormConfig()
        if rewardCfg then
            maxHp = rewardCfg.BaseHP
        end
    else
        gw_home_mgr = gw_home_mgr or require "gw_home_mgr"
        maxHp, curHp = gw_home_mgr.buildingData.GetWallHpByLevel(self.props.baseCurDefend, self.props.baseWallEndTime, self.props.baseWallBeginTime, self.props.baseCityWallLevel)
    end
    return maxHp, curHp
end

-- 获取基地城防值(服务器的固定属性,真是的血量请使用上面接口)
function comp:GetBasePropDefend()
    return self.props and self.props.baseCurDefend or 0
end

--- 获取基地联盟ID
function comp:GetBaseUnionID()
    return self.props and self.props.unionID
end

--- 获取(战区对决)中立城市占领的世界ID(争夺结束阶段，正式占领)
function comp:GetOccupyWorldID()
    --争夺阶段结束
    local war_zone_duel_helper = require "war_zone_duel_helper"
    local isZoneDuel_CongressBattleEnd = war_zone_duel_helper.IsSelfZoneDuelActiveOverStage_CongressBattleSettlement()
    local tmp = nil
    if isZoneDuel_CongressBattleEnd and self.props and self.props.occupy_duel_worldID and self.props.occupy_duel_worldID ~= 0 then
        tmp = self.props.occupy_duel_worldID
    end
    return tmp
end

--- 获取基地联盟名称
function comp:GetBaseUnionName()
    return self.strProps and self.strProps.unionName
end

--- 获取联盟简称（通用联盟简称）
function comp:GetUnionShortName()
    return self.strProps and self.strProps.unionShortName
end

--获取联盟简称（战区对决）
function comp:GetUnitShortName_ZoneDuel()
    return self.strProps and self.strProps.unionShortName_zoneDuel
end

function comp:GetBaseUnionFlag()
    if self.props and self.props.ncAllianceIconID then
        local alliance_data = require "alliance_data"
        local flagData = alliance_data.GetFlagIdData(self.props.ncAllianceIconID)
        if flagData then
            return flagData.iconID;
        end
    end
    return 1
end

--- 获取联盟完整名称
function comp:GetUnionFullName()
    if self:GetBaseUnionID() > 0 or self:GetStormAllianceId() > 0 then
        return util.SplicingUnionShortName(self:GetUnionShortName(), self:GetBaseUnionName())
    end
end

--- 获取头像id
function comp:GetFaceId()
    return self.props and self.props.faceId
end

function comp:GetFaceStr()
    return self.strProps and self.strProps.faceStr
end

function comp:GetNationalFlagID()
    return self.props and self.props.nationalFlagID
end

--- 获取边框id
function comp:GetFrameId()
    return self.props and self.props.frameId
end

--- 获取怪物刷新倒计时
function comp:GetMonsterCountDown()
    return self.props and self.props.monsterCountDown
end

--- 获取怪物首杀等级
function comp:GetMonsterFirstLevel()
    return self.props and self.props.monsterFirstLevel
end

--- 获取配置ID
function comp:GetCfgId()
    return self.props and self.props.cfgId
end

---中立联盟名称
function comp:GetCityUnionName()
    return self.strProps and self.strProps.unionName
end

--- 通用：获取基地最大hp
function comp:GetBaseMaxHp()
    return self.props and self.props.baseMaxHp
end

--- 通用：获取基地当前hp
function comp:GetBaseCurHp()
    return self.props and self.props.baseCurHp
end

--- 获取资源数量
function comp:GetResNum()
    return self.props and self.props.resNum
end

--- 获取资源开始采集时间
function comp:GetResBeginTime()
    return self.props and self.props.resBeginTime
end

--- 获取资源结束采集时间
function comp:GetResEndTime()
    return self.props and self.props.resEndTime
end

--- 获取官职属性，0表示没有官职
function comp:GetPositionId()
    return self.props and self.props.congressOfficial
end

--- 资源采集者名字
function comp:GetResRoleName()
    return self.strProps and self.strProps.roleName
end

--读表判断能不能攻打(野怪)
function comp:GetCanAttack()
    --读取SandMapMonster表,判断condition ,用分号分隔 1#野怪类型#已攻打的等级
    local conditionStr = self.cfg and self.cfg.condition or ""

    local conditionList = util.split(conditionStr, ";")

    local v = conditionList[1]
    if v and v ~= "" then
        local condition = util.split(v, "#")
        if condition[1] == "1" then
            local attackType = condition[2]
            local attackLevel = tonumber(condition[3])
            if attackLevel then
                local canAttack = self:GetMonsterFirstLevel() >= attackLevel
                local level = self:GetMonsterFirstLevel()
                return canAttack, { langId = 560018, level = level + 1 }
            end
        elseif condition[1] == "2" then
            local gw_home_building_data = require "gw_home_building_data"
            local buildingType = tonumber(condition[2])
            local buildingId = gw_home_building_data.GetBuildingIdByBuildingType(buildingType)
            local buildingLevel = tonumber(condition[3])
            local curBuilding = gw_home_building_data.GetMaxLevelBuildingDataByBuildingID(buildingId)
            if curBuilding then
                local canAttack = curBuilding.nLevel >= buildingLevel
                return canAttack, { langId = 560307, level = buildingLevel == 0 and 1 or buildingLevel }
            end
        end
        log.Error("条件不满足 conditionStr:" .. conditionStr)
        ----其他条件类型
        return false, {}
    else
        --没有配置条件直接返回true
        return true, {}
    end
end

--获取侦查结束时间戳
function comp:GetDetectTimeStamp()
    return self.props and self.props.detectTimeStamp
end

--获取侦查按钮CD结束时间戳
function comp:GetDetectButtonCDTimeStamp()
    return self.props and self.props.detectButtonCDTimeStamp
end

--获取侦查可掠夺奖励
function comp:GetDetectRewards()
    return self.props and {
        --itemId,num
        { 1, self.props.detectGold },
        { 36, self.props.detectIron },
        { 35, self.props.detectFood }
    }
end

--获取目标sid
function comp:GetTargetSid()
    return self.props and self.props.targetSid
end

--获取目标dbid（增援）
function comp:GetTargetDbid()
    return self.props and self.props.targetDbid
end

--获取目标名称
function comp:GetTargetName()
    return self.strProps and self.strProps.targetName
end


--获取服务器id
function comp:GetServerId()
    return self.props and self.props.serverId
end

function comp:GetFinalKey()
    return self.resProps and self.resProps.visionType
end
---@public GetFinalKey2  取到最终的viewLevel 类型并取余10000
function comp:GetFinalKey2()
    local viewLevel = self.resProps and self.resProps.visionType
    if viewLevel then
        return viewLevel % 10000
    end
    return 0
end

function comp:GetColorType()
    return self.resProps and self.resProps.colorType or gw_const.ESOwnerColor.other
end

--获取保护罩
function comp:GetBaseSafety()
    return self.props and self.props.baseSafety or 0
end

local radar_data = require "radar_data"
--获取是否是雷达帮助盟友任务
function comp:GetBaseRadarAllyTask()
    if self.type == EntityType.Base then
        local tempBool = radar_data.IsEntityRadarHelpAlly(self.sid)
        return tempBool
    end
    return false
end

---@public GetSandTreasureShow 宝箱是否显示领取图标 
function comp:GetSandTreasureShow()
    local player_mgr = require "player_mgr"
    local alliance_mgr = require "alliance_mgr"
    --1：全服  2：同联盟成员 3：玩家个人
    if self.cfg.attributionType == 1 then
        --全服 都显示
        return true
    elseif self.cfg.attributionType == 2 or self.cfg.attributionType == 4 then
        --同联盟成员
        local allianceID = alliance_mgr.GetUserAllianceId()
        --region TODO 先注释掉 方便服务器测试
        if allianceID ~= self.props.unionID and self.props.createRoleID ~= player_mgr.GetPlayerRoleID() then
            return false
        end
        --endregion
        return true
    elseif self.cfg.attributionType == 3 then
        --玩家个人
        return self.props.createRoleID == player_mgr.GetPlayerRoleID()
    end
    return false
end

---@public IsAllianceTreasure 是否是自己联盟宝箱
function comp:IsAllianceTreasure()
    local player_mgr = require "player_mgr"
    local alliance_mgr = require "alliance_mgr"

    local allianceID = alliance_mgr.GetUserAllianceId()
    local selfID = player_mgr.GetPlayerRoleID()
    --region TODO 先注释掉 方便服务器测试
    if self.props.unionID ~= allianceID and self.props.createRoleID ~= selfID then
        return false
    end
    --endregion
    return true
end

---@public IsWarRallyMonster 是否属于集结大作战
function comp:IsWarRallyMonster()
    return game_scheme:SandMapMonster_0(self.props.cfgId).type == 13
end

---@public IsWarRallyMonster 是否属于橡果酒馆任务怪
function comp:IsTavernTaskMonster()
    local monsterDataCfg = game_scheme:SandMapMonster_0(self.props.cfgId)
    return monsterDataCfg and monsterDataCfg.type == 16
end

---@public IsGeneralLeagueMonster 是否属于将军试炼联盟怪
function comp:IsGeneralLeagueMonster()
    local tempKey = game_scheme:SandMapMonster_0(self.props.cfgId).ModelResource
    return game_scheme:SandMapModelResource_0(tempKey).viewType == gw_const.ESEntityResViewType.GeneralTrialLeague
end

---@public IsDiggingTreasure 自己的队伍是否在挖掘宝藏中
function comp:IsDiggingTreasure()
    if self.roleInfoList then
        local player_mgr = require "player_mgr"
        local playerRoleID = player_mgr.GetPlayerRoleID()
        for _, v in ipairs(self.roleInfoList) do
            if v.roleId == playerRoleID then
                return true
            end
        end
    end

    return false
end


--获取开始采集时的最终采集速度
function comp:GetResSpeed()
    --开始采集时的最终速度; 除以10000才是速度;  20000 ==> 速度2
    return self.props and self.props.resCollectSpeed / 10000
end

--当前自己是否中毒
function comp:GetIsPoison()
    return band(self.props.base_bool_bit, gw_const.EnSandboxBaseBoolBit.enSandboxBaseBoolBit_ZombieApocalyse) == 1
end

--获取游荡怪id
function comp:GetMonsterId()
    local marchWonderId = self.props.lineEntityId
    if marchWonderId > 0 then
        return marchWonderId
    end
end

--获取货车id
function comp:GetCarriageId()
    local carriageId = self.props.lineEntityId
    if carriageId > 0 then
        return carriageId
    end
end

--获取联盟火车id
function comp:GetAllianceTrainId()
    local allianceTrainId = self.props.allianceTrainId
    if allianceTrainId > 0 then
        return allianceTrainId
    end
end

---@see 获取联盟火车特殊行军实体ID
function comp:GetAllianceTrainLineEntityId()
    local lineEntityTrainId = self.props.lineEntityId
    if lineEntityTrainId > 0 then
        return lineEntityTrainId
    end
end

--region ------------------------------------- 中立城池 -------------------------------------
--- 中立城市城防守军数量
function comp:GeNCDefenseNum()
    local number = self.props and self.props.ncDefendersNum
    if self.cfg and number < 0 then
        return self.cfg.Defendersquantity
    end
    return number
end

--- 中立城市城防值
function comp:GeNCDefenseValue()
    local value = self.props and self.props.ncDefenseValue
    if self.cfg and value < 0 then
        return self.cfg.cityHp
    end
    return value
end

--获得中立城池是否首次占领
function comp:GetNCFirstOccupy()
    return self.props and self.props.ncFirstOccupy
end

-- 获得中立城池是否收藏
function comp:GetNcFavoriteState()
    local sand_mark_data = require "sand_mark_data"
    local curMarkData = sand_mark_data.FindMarkExistByPos(self.pos)
    if curMarkData then
        return curMarkData.nType ~= 4
    end
    return false
end

-- 中立城市保护罩倒计时
function comp:GetNCProtectCD()
    if self.props and self.props.ncProtectCD and self.props.ncProtectCD > os.server_time() then
        return self.props.ncProtectCD
    end
    return 0
end

-- 中立城市解锁倒计时
function comp:GetNCDeclareWarUnlockCD()
    if self.cfg and not string.IsNullOrEmpty(self.cfg.declaration) then
        local cfg_util = require "cfg_util"
        local timeTable = cfg_util.ArrayToLuaArray(self.cfg.declaration)

        local time_util = require "time_util"
        local player_mgr = require "player_mgr"
        local openServerTime = time_util.GetTimeOfZero(os.server_zone(), player_mgr:GetRoleOpenSvrTime()) - 86400
        local days = (timeTable[2]) - 1
        local unlockCD = openServerTime + days * 86400 + (timeTable[3]) * 3600 + (timeTable[4]) * 60 + (timeTable[5])
        if unlockCD > os.server_time() then
            return unlockCD
        end
    end
    return 0
end

-- 中立城市攻城倒计时
function comp:GetNCDWEndTime()
    if self.props and self.props.ncDWEndTime and self.props.ncDWEndTime > os.server_time() then
        return self.props.ncDWEndTime
    end
    return 0
end

-- 获得中立城池宣战联盟数据
function comp:GetNCAllianceList()
    return self.strProps and self.strProps.ncAllianceList or nil
end

-- 获得中立城池宣战数量
function comp:GetNCDeclareWarCount()
    local allianceList = self:GetNCAllianceList()
    if not string.IsNullOrEmpty(allianceList) then
        local cfg_util = require "cfg_util"
        return #cfg_util.StringToArray(allianceList)
    end
    return 0
end

--获得中立城池的积分排行榜
function comp:GetNCRankInfoList()
    if self.rankInfoList then
        local rankList = {}
        for _, v in ipairs(self.rankInfoList) do
            if v.nScore and v.nScore > 0 then
                table.insert(rankList, v)
            end
        end
        return rankList
    end
    return nil
end

-- 获得国信息数据
function comp:GetNCCongressInfo()
    return self.strProps and self.strProps.ncCongressInfo or nil
end

-- 获得国会真实的占领信息
function comp:GetNCCongressInfoTable()
    local congressInfo = self:GetNCCongressInfo()
    if string.IsNullOrEmpty(congressInfo) then
        return nil
    end
    -- 进度#联盟ID#联盟简称#联盟旗帜#联盟名
    local cfg_util = require "cfg_util"
    return cfg_util.StringToArray(congressInfo)
end

-- 获得国会临时占领状态
function comp:GetNCCongressTempOccupyState()
    local tableInfo = self:GetNCCongressInfoTable()
    if not tableInfo then
        return false
    end

    local alliance_mgr = require "alliance_mgr"
    local userUnionId = alliance_mgr.GetUserAllianceId()
    if userUnionId > 0 then
        return userUnionId == tonumber(tableInfo[2])
    end
    return false
end

-- 获得战区对决国会真实的占领信息
function comp:GetNCCongressInfoTable_ZoneDuel()
    local tmpData = {}
    if self.props then
        tmpData.score = self.props.occupy_world_score
        tmpData.score_atk= self.props.occupy_world_score_atk
        tmpData.score_def= self.props.occupy_world_score_def
        tmpData.occupy_world_overTime = self.props.occupy_world_overTime
        tmpData.occupy_duel_worldID = self.props.occupy_duel_worldID
    end
    return tmpData
end

-- 获得战区对决国会临时占领状态,是否自己服
function comp:GetNCCongressTempOccupyState_ZoneDuel()
    if self.props and self.props.occupy_duel_worldID then
        local gw_sand_data = require "gw_sand_data"
        return gw_sand_data.selfData.GetZoneSandBoxSid() == self.props.occupy_duel_worldID
        --return true
    end
    return false
end

function comp:GetTmpOccupyDuelWorldID()
    return self.props and self.props.occupy_duel_worldID
end

--获取战区对决征服者头像框
function comp:GetZoneDuelFrameID()
    return self.props and self.props.zoneDuel_FrameID
end
--获取战区对决征服者头像配置id
function comp:GetZoneDuelFaceID()
    return self.props and self.props.zoneDuel_FaceID
end
--获取战区对决征服者头像自定义id
function comp:GetZoneDuelFaceStr()
    return self.strProps and self.strProps.zoneDuel_FaceStr
end

--是否战区对决的奖励积分进度
function comp:IsZoneDuelProgress()
    --战区对决国会争夺阶段 且 有积分>0
    local war_zone_duel_helper = require "war_zone_duel_helper"
    local isZoneDuel = war_zone_duel_helper.IsCongressAtk()
    return self.props and self.props.occupy_world_score > 0 and isZoneDuel
end

--战区对决 是否巨炮
function comp:IsSelfBigGun()
    if self.type == EntityType.NeutralCity then
        return self:GetFinalKey2() == gw_const.ESEntityResViewType.BigGun
    end
    return false
end

--获取放弃城池CD结束时间戳
function comp:GetNCAbandonCDTimeStamp()
    if self.props and self.props.ncAbandonCD and self.props.ncAbandonCD > os.server_time() then
        return self.props.ncAbandonCD
    end
    return 0
end

--获取全军出击CD结束时间戳
function comp:GetNCAllAttackTimeStamp()
    if self.props and self.props.ncOfflineAttackCD and self.props.ncOfflineAttackCD > os.server_time() then
        return self.props.ncOfflineAttackCD
    end
    return 0
end

--获取中立城池的解锁状态
function comp:GetNCUnlockState()
    return self:GetBaseUnionID() > 0 or self:GetNCProtectCD() == 0
end

--获取战区对决中立城池的解锁状态(国会争夺战斗状态)
function comp:GetNCUnlockState_WarZoneCongressBattle()
    local war_zone_duel_helper = require "war_zone_duel_helper"
    return war_zone_duel_helper.IsCongressAtk() and self:GetNCProtectCD()==0
end

--获取中立城池的宣战解锁状态
function comp:GetNCDeclareUnlockState()
    return self:GetBaseUnionID() > 0 or self:GetNCDeclareWarUnlockCD() == 0
end

--获取中立城池的占领状态
function comp:GetNCOccupyState()
    --如果是战区对决，则走其的占领状态
    local alliance_mgr = require "alliance_mgr"
    local userUnionId = alliance_mgr.GetUserAllianceId()
    if userUnionId > 0 then
        return self:GetBaseUnionID() == userUnionId
    end
    return false
end

-- 获得中立城池宣战状态
function comp:GetNCDeclareWarState()
    local allianceList = self:GetNCAllianceList()
    if not string.IsNullOrEmpty(allianceList) then
        local cfg_util = require "cfg_util"
        local alliance_mgr = require "alliance_mgr"

        local allianceListTable = cfg_util.StringToNumberArray(allianceList)
        return alliance_mgr.HasUserAllianceByAllianceList(allianceListTable)
    end
    return false
end

-- 获得中立城池即将开战状态
function comp:GetNCReadyWarState()
    return self:GetNCDeclareWarState() and self:GetNCProtectCD() > 0
end

-- 获得中立城池即将开战状态
function comp:GetNCAllianceIdx()
    return self.strProps and self.strProps.ncAllianceIdx
end

-- 获得中立城池即将开战状态
function comp:GetCongressPresName()
    return self.presName or nil
end

--沙漠风暴
function comp:GetStormUnlockTime()
    if self.cfg and self.cfg.Building then
        local gw_storm_data_battle = require "gw_storm_data_battle"
        local openTime = gw_storm_data_battle.GetBuildOpenTime(self.cfg.Building)
        return openTime
    end
    return 0
    --return (self.props and self.props.storm_UnlockTime) or 0
end
function comp:GetStormAllianceId()
    --return 5016
    -- if true then
    --     local alliance_mgr = require "alliance_mgr"
    --     local userAllianceId = alliance_mgr.GetUserAllianceId()
    --     return userAllianceId
    -- end
    return self.props and self.props.storm_AllianceId or 0
end
function comp:GetStormRoleId()
    return self.props and self.props.storm_BelongRoleId
end
function comp:GetStormFrameId()
    return self.props and self.props.storm_BelongRoleFrameId
end
function comp:GetStormAllianceFlag()
    return self.props and self.props.storm_AllianceFlag
end
function comp:GetStormMoveCityCD()
    return self.props and self.props.storm_MoveCityCD
end
function comp:GetStormOilWellChangeNum()
    return self.props and self.props.storm_OilWellChangeNum
end
function comp:GetStormOilWellChangeTime()
    return self.props and self.props.storm_OilWellChangeTime
end
function comp:GetStormHp()
    return self.props and self.props.storm_FirstOccupierHp / 100
end
function comp:GetStormBoxScore()
    return self.props and self.props.storm_ScoreBoxScoreNum
end

function comp:GetStormBuildStage()
    local gw_sand_external_mgr = require "gw_sand_external_mgr"
    return gw_sand_external_mgr.GetStormOwnerType(self.props, self)
end

---@public获取是否是同联盟
function comp:GetIsSameUnion()
    local alliance_mgr = require "alliance_mgr"
    local userUnionId = alliance_mgr.GetUserAllianceId()
    if userUnionId > 0 then
        return self:GetBaseUnionID() == userUnionId
    end
    return false
end
--endregion

--region ------ 橡木酒馆 ------
---@public 获取橡果酒馆任务完成时间
function comp:GetTavernDoneTime()
    if self.props and self.props.tavernDoneTime then
        return self.props.tavernDoneTime
    end
    return 0
end
---@public 获取橡果酒馆任务ID
function comp:GetTavernTaskId()
    if self.props and self.props.tavernTaskID then
        return self.props.tavernTaskID
    end
    return 0
end
--endregion

---@public 获取实体被集结数量
function comp:GetMassCount()
    return self.props and self.props.massCount
end

---@public 获取效果Id
function comp:GetEffectId()
    --return 9002
    return self.props and self.props.baseEffectID
end

---@public 获取铭牌Id
function comp:GetNamePlateId()
    return self.props and self.props.namePlateId
end

---@public 获取实体的原服ID
function comp:GetZoneSandboxSid()
    local worldId = math.max(self.props.serverId, self.props.worldID)
    return worldId
end

--规则1：玩家视野跨服需显示ID
--规则2：非本服玩家显示ID
local function needShowServerID(entityData, entityZoneSid)
    local visualCrossState = gw_common_util.GetSandVisualCrossServiceState()
    local visualSandSid = gw_common_util.GetSandVisualSandBoxSid()
    return visualCrossState or (visualSandSid and entityZoneSid > 0 and entityZoneSid ~= visualSandSid)
end

---@public 获取玩家带服务器ID的名字（必须跨服）
function comp:GetUnionShortAndRoleNameAndServerID()
    local entityZoneSid = self:GetZoneSandboxSid()
    if needShowServerID(self, entityZoneSid) then
        return string.format("#%s %s", (entityZoneSid % 10000), self:GetUnionShortAndRoleName())
    else
        return self:GetUnionShortAndRoleName()
    end
end

---@public 获取玩家带服务器ID的名字（必须跨服）
function comp:GetUnionShortAndUnionNameAndServerID()
    if string.IsNullOrEmpty(self:GetUnionShortName()) then
        return ""
    else
        local entityZoneSid = self:GetZoneSandboxSid()
        if needShowServerID(self, entityZoneSid) then
            return string.format("#%s %s", entityZoneSid, util.SplicingUnionShortName(self:GetUnionShortName(), self:GetBaseUnionName()))
        else
            return util.SplicingUnionShortName(self:GetUnionShortName(), self:GetBaseUnionName())
        end
    end
end

---@public 获取营寨已增援队伍数量
function comp:GetCampReinforceCount()
    return self.props and self.props.camp_reinforceCount
end

---@public 获取营寨耐久度
function comp:GetCampDurability()
    return self.props and self.props.camp_durability
end

---@public 获取营寨目标中立城市坐标
function comp:GetCampCityPos()
    local pos = { x = 0, y = 0 }
    if self.strProps and self.strProps.camp_cityPos then
        local cfg_util = require "cfg_util"
        local posList = cfg_util.StringToNumberArray(self.strProps.camp_cityPos)
        pos.x = posList[1]
        pos.y = posList[2]
    end
    return pos
end

---@public 获取通用实体的配置表id
function comp:GetCommonConfigId()
    return self.props and self.props.common_entityCfgID
end

return comp