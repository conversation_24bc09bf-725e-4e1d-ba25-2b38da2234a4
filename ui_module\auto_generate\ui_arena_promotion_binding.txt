local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform


module("ui_arena_promotion_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenapromotion.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	txt_RankTip = { path = "Main/txt_RankTip", type = Text, },
	rtf_rewardList = { path = "Main/RewardShow/Scroll View/Viewport/rtf_rewardList", type = RectTransform, },
	btn_reqPromotion = { path = "Main/btn_reqPromotion", type = Button, event_name = "OnBtnReqPromotionClickedProxy"},

}
