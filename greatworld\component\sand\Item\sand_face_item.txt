---
--- Created by connan.
--- DateTime: 2025/7/8
--- Desc: 沙盘头像对象
---

local require = require
local string = string
local UIUtil = CS.Common_Util.UIUtil
local tonumber = tonumber
local SpriteRenderer = CS.UnityEngine.SpriteRenderer
local typeof = typeof
local EntityManager = require "entity_manager"
local entityMode = EntityManager.FaceItemEcs
local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility
local async_entity = require "async_entity"

local util = require "util"
local gw_asset_mgr = require "gw_asset_mgr"
local gw_sand_hud_sorting = nil
local SandEntityBase = require "sand_entity_base"

-- 图集缓存，存储prefab中各节点的图集信息
local prefabAtlasCache = {}

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
module("sand_face_item")

---@class SandFaceItem : 沙盘头像对象
local SandFaceItem = {}

local defaultFaceResPath = "art/greatworld/sand/scenenew/hud/item/sand_face_item.prefab"
local defaultFaceSize = { x = 120, y = 120 }
local defaultFaceVector = { x = 0.75, y = 0.75, z = 1 }

-- SandFaceItem.widget_table = {
--     sp_head = { path = "sp_head", type = SpriteRenderer },
--     sp_frame = { path = "sp_frame", type = SpriteRenderer },
-- }

--- 构造器
function SandFaceItem:ctor()
    self.__base:ctor(nil, "sand_face_item")
end

function SandFaceItem:LoadTransform()
    if entityMode then
        if self.hybridEntity then
            local nameTab = EntityHybridUtility.GetEntityChildNameIndex(self.hybridEntity)
            local headIdx = nameTab["sp_head"]
            local frameIdx = nameTab["sp_frame"]
            local frameSactxIdx = nameTab["sp_frame_sactx"]
            
            self.sp_head = EntityHybridUtility.GetChild(self.hybridEntity, headIdx)
            self.sp_frame = EntityHybridUtility.GetChild(self.hybridEntity, frameIdx)
            self.sp_frame_sactx = frameSactxIdx and EntityHybridUtility.GetChild(self.hybridEntity, frameSactxIdx) or nil
            
            -- 获取frame节点的图集信息
            self.sp_frameAtlas = self.GetPrefabAtlas(defaultFaceResPath, "sp_frame")
            self.sp_frameSactxAtlas = self.GetPrefabAtlas(defaultFaceResPath, "sp_frame_sactx")
            
            -- 默认显示sp_frame节点，隐藏sp_frame_sactx节点
            if self.sp_frame then
                EntityHybridUtility.SetActive(self.sp_frame, true)                  
                -- 缓存frame节点的激活状态
                self.currentActiveFrameNode = "sp_frame" -- 默认激活sp_frame节点
            end
            if self.sp_frame_sactx then
                EntityHybridUtility.SetActive(self.sp_frame_sactx, false)
            end
            
            self.customFaceLayer = gw_sand_hud_sorting.BaseTopNodeSortingOrder() + 10
            self.customFrameLayer = gw_sand_hud_sorting.BaseTopNodeSortingOrder() + 20
        end
    else
        if self.UIRoot then
            local tf = self.UIRoot.transform
            self.sp_head = tf:Find("sp_head"):GetComponent(typeof(SpriteRenderer))
            self.sp_frame = tf:Find("sp_frame"):GetComponent(typeof(SpriteRenderer))
        end
    end
end

function SandFaceItem:Init(parent, callback)
    gw_sand_hud_sorting = gw_sand_hud_sorting or require "gw_sand_hud_sorting"
    self.isShow = true
    if entityMode then
        self.entityId = async_entity.RequestInstantiate(defaultFaceResPath, function(entity)
            self.hybridEntity = entity
            self.UIRoot = entity -- 兼容原有逻辑
            self:LoadTransform()
            self:SetFaceId(self.faceId)
            self:SetFaceStr(self.faceStr)
            self:SetFrameId(self.frameId)
            if self.isShow then
                self:OnShow()
            else
                self:OnHide()
            end
            if callback then
                callback(self)
            end
            EntityHybridUtility.AttachToTransform(entity, parent)
        end,gw_sand_hud_sorting.BaseTopNodeSortingOrder())
    else
        self:LoadResource(defaultFaceResPath, "", function()
            self:LoadTransform()
            self:SetFaceId(self.faceId)
            self:SetFaceStr(self.faceStr)
            self:SetFrameId(self.frameId)
            if self.isShow then
                self:OnShow()
            else
                self:OnHide()
            end
            if callback then
                callback(self)
            end
        end, false, parent)
    end
    return self
end

function SandFaceItem:SetFaceKey(faceKey)
    if not faceKey then
        return
    end

    local faceId = tonumber(faceKey)
    if faceId and faceId > 0 then
        self:SetFaceId(faceId)
    else
        self:SetFaceStr(faceKey)
    end
end

function SandFaceItem:SetFaceId(faceId)
    if not faceId or faceId == 0 then
        return
    end

    self.faceId = faceId
    if not self.UIRoot then
        return
    end

    gw_asset_mgr:SetFaceIcon(self.sp_head, self.faceId)
    if entityMode then
        EntityHybridUtility.SetLocalTransform(self.sp_head, 0, 0, 0, 0, defaultFaceVector.x)
        self:UpdateLayer()
    else
        UIUtil.SetLocalScale(self.sp_head.transform, defaultFaceVector.x, defaultFaceVector.y, defaultFaceVector.z)
    end
end

function SandFaceItem:SetFaceStr(faceStr)
    if string.IsNullOrEmpty(faceStr) then
        return
    end

    local faceId = tonumber(faceStr)
    if faceId and faceId > 0 then
        self:SetFaceId(faceId)
        return
    end

    self.faceStr = faceStr
    if not self.UIRoot then
        return
    end

    local custom_avatar_mgr = require "custom_avatar_mgr"
    local imageID = custom_avatar_mgr.ExtractFilename(faceStr):match("(.+)%..+$")
    local isLocalUrl = string.find(faceStr, "^file://", 1) == 1

    local faceLoadCallback = function(sprite)
        if sprite and not util.IsObjNull(self.sp_head) then
            local scaleX = defaultFaceSize.x / sprite.rect.width * defaultFaceVector.x;
            local scaleY = defaultFaceSize.y / sprite.rect.height * defaultFaceVector.y;
            if not entityMode then
                UIUtil.SetLocalScale(self.sp_head.transform, scaleX, scaleY, 1)
            else
                self:UpdateLayer()
            end
        end
    end

    if isLocalUrl then
        custom_avatar_mgr.SetAvatarIcon(self.sp_head, faceStr, faceLoadCallback)
    else
        local callback = function(url)
            if url and not string.IsNullOrEmpty(url) then
                custom_avatar_mgr.SetAvatarIcon(self.sp_head, faceStr, faceLoadCallback)
            end
        end
        custom_avatar_mgr.CheckAvatarLocalUrl(faceStr, imageID, callback)
    end
end

function SandFaceItem:UpdateLayer()
    EntityHybridUtility.SetEntitySortingOrder(self.sp_head,self.customFaceLayer)
    EntityHybridUtility.SetEntitySortingOrder(self.sp_frame,self.customFrameLayer)
    if self.sp_frame_sactx then
        EntityHybridUtility.SetEntitySortingOrder(self.sp_frame_sactx, self.customFrameLayer)
    end
end

function SandFaceItem:SetFrameId(frameId)
    if not frameId or frameId == 0 then
        return
    end

    self.frameId = frameId
    if not self.UIRoot then
        return
    end
    
    if entityMode then
        gw_asset_mgr:SetFrameIconWithAtlasCheck(self.frameId, function(sprite)
            if sprite then
                -- 根据sprite图集智能选择节点
                self:SetFrameIconSmart(function(targetEntity)
                    if not util.IsObjNull(targetEntity) and not targetEntity.Index and not targetEntity.Version then
                        targetEntity.sprite = sprite
                    else
                        EntityHybridUtility.SetSpriteRectParam(targetEntity, sprite)
                        EntityHybridUtility.SetSpritePivotScale(targetEntity, sprite)
                    end
                end, sprite)
            else
                -- 如果没有sprite，隐藏所有frame节点
                if self.sp_frame then
                    EntityHybridUtility.SetActive(self.sp_frame, false)
                end
                if self.sp_frame_sactx then
                    EntityHybridUtility.SetActive(self.sp_frame_sactx, false)
                end
            end
        end)
    else
        gw_asset_mgr:SetFrameIcon(self.sp_frame, self.frameId)
    end
end

function SandFaceItem:OnShow()
    self.isShow = true

    if not self.UIRoot then
        return
    end

    if entityMode then
        EntityHybridUtility.SetActive(self.UIRoot, true)
        -- 刷新frame节点状态，确保多图集切换逻辑正确
        self:RefreshFrameState()
    else
        UIUtil.SetActive(self.UIRoot, true)
    end
end

function SandFaceItem:OnHide()
    self.isShow = false
    if not self.UIRoot then
        return
    end

    if entityMode then
        EntityHybridUtility.SetActive(self.UIRoot, false)
    else
        UIUtil.SetActive(self.UIRoot, false)
    end
end

-- 外部调用接口，用于设置显示状态并自动刷新frame节点状态
function SandFaceItem:SetActive(visible)
    if not self.UIRoot then
        return
    end
    
    if entityMode then
        EntityHybridUtility.SetActive(self.UIRoot, visible)
        -- 如果显示，刷新frame节点状态，确保多图集切换逻辑正确
        if visible then
            self:RefreshFrameState()
        end
    else
        UIUtil.SetActive(self.UIRoot, visible)
    end
end

--- 弃用
function SandFaceItem:Dispose()
    self.__base:Dispose()
    if entityMode then
        async_entity.Dispose(self.entityId)
        self.hybridEntity = nil
        self.entityId = nil
        
        -- 清理frame节点引用
        self.sp_frame_sactx = nil
        self.sp_frameAtlas = nil
        self.sp_frameSactxAtlas = nil
        self.currentActiveFrameNode = nil
    end
end

---------------------------适配多图集处理------------------------------------------------------
-- 刷新frame节点状态，用于外部显示状态变化后的状态同步
function SandFaceItem:RefreshFrameState()
    if not entityMode or not self.UIRoot then
        return
    end

    -- 使用缓存的状态重新应用正确的节点状态
    if self.currentActiveFrameNode and self.currentActiveFrameNode ~= "" then
        -- 直接调用SwitchFrameEntityVisibility来重新应用正确的节点状态
        self:SwitchFrameEntityVisibility(self.currentActiveFrameNode)
        -- 更新层级
        self:UpdateLayer()
    else
        -- 如果没有缓存状态，确保都隐藏
        if self.sp_frame then
            EntityHybridUtility.SetActive(self.sp_frame, false)
        end
        if self.sp_frame_sactx then
            EntityHybridUtility.SetActive(self.sp_frame_sactx, false)
        end
    end
end

-- 获取prefab中指定节点的图集信息
function SandFaceItem.GetPrefabAtlas(prefabPath, nodeName)
    local cacheKey = prefabPath .. "_" .. nodeName
    if prefabAtlasCache[cacheKey] then
        return prefabAtlasCache[cacheKey]
    end
    
    local prefabSpriteRenderer = SandEntityBase.GetPrefabSpriteRenderer(prefabPath)
    if not prefabSpriteRenderer then
        return nil
    end
    
    local targetNode = prefabSpriteRenderer.transform:Find(nodeName)
    if not targetNode then
        return nil
    end
    
    local spriteRenderer = targetNode:GetComponent(typeof(SpriteRenderer))
    if not spriteRenderer or not spriteRenderer.sprite or not spriteRenderer.sprite.texture then
        return nil
    end
    
    local atlas = spriteRenderer.sprite.texture
    prefabAtlasCache[cacheKey] = atlas
    return atlas
end

-- 智能选择合适的frame节点并应用操作
function SandFaceItem:SetFrameIconSmart(applyFunc, sprite)
    if not entityMode then
        if applyFunc then applyFunc(self.sp_frame) end
        return
    end

    local targetEntity = nil
    local targetNodeName = ""

    -- 如果有sprite，根据图集选择对应节点
    if sprite and sprite.texture then
        if self.sp_frameAtlas and sprite.texture == self.sp_frameAtlas then
            targetEntity = self.sp_frame
            targetNodeName = "sp_frame"
        elseif self.sp_frameSactxAtlas and sprite.texture == self.sp_frameSactxAtlas then
            targetEntity = self.sp_frame_sactx
            targetNodeName = "sp_frame_sactx"
        else
            -- 默认使用sp_frame节点
            targetEntity = self.sp_frame
            targetNodeName = "sp_frame"
        end
    else
        -- 没有sprite信息时默认使用sp_frame节点
        targetEntity = self.sp_frame
        targetNodeName = "sp_frame"
    end

    if targetEntity then
        -- 切换节点显示状态
        self:SwitchFrameEntityVisibility(targetNodeName)

        -- 应用操作
        if applyFunc then
            applyFunc(targetEntity)
        end

        -- 更新层级
        self:UpdateLayer()
    end
end

-- 切换frame节点的显示状态
function SandFaceItem:SwitchFrameEntityVisibility(activeNodeName)
    if not entityMode then
        return
    end

    -- 先隐藏所有节点
    if self.sp_frame then
        EntityHybridUtility.SetActive(self.sp_frame, false)
    end
    if self.sp_frame_sactx then
        EntityHybridUtility.SetActive(self.sp_frame_sactx, false)
    end

    -- 激活目标节点
    if activeNodeName == "sp_frame" and self.sp_frame then
        EntityHybridUtility.SetActive(self.sp_frame, true)
        self.currentActiveFrameNode = "sp_frame" -- 更新缓存状态
    elseif activeNodeName == "sp_frame_sactx" and self.sp_frame_sactx then
        EntityHybridUtility.SetActive(self.sp_frame_sactx, true)
        self.currentActiveFrameNode = "sp_frame_sactx" -- 更新缓存状态
    end
end
---------------------------适配多图集处理------------------------------------------------------

local class = require "class"
local base_game_object = require "base_game_object"
CFaceItem = class(base_game_object, nil, SandFaceItem)
