--n:陈泳冰
--d:2022年1月28日
--v:1.0.0
--t:换包
local require = require
local util = require "util"
local lang = require "lang"
local class = require "class"
local event = require "event"
local ui_base = require "ui_base"
local ui_window_mgr = require "ui_window_mgr"
local ChangePackgeConfig = require "ChangePackgeConfig"
local util = require "util"

local Application = CS.UnityEngine.Application

module("DownLoadNewpopup")
local window = nil
local DownLoadNewpopup = {}
DownLoadNewpopup.widget_table =
{
    closeBtn = {path = "closeBtn", type = "Button",backEvent = true},
    title = { path = "bg/title", type = "Text" },

    BtnGO = { path = "bg/btns/BtnGO", type = "Button" },    --前往下载
    BtnReceive = { path = "bg/btns/BtnReceive", type = "RectTransform"},--灰色前往下载
    BtnReceiveText = { path = "bg/btns/BtnReceive/Text", type = "Text"},--灰色前往下载-倒计时文字

    BtnLeft = { path = "bg/btns/BtnLeft", type = "Button" },--向左翻页
    BtnRight = { path = "bg/btns/BtnRight", type = "Button" },--向右翻页
    BtnRightRed = { path = "bg/btns/BtnRight/redPoint", type = "RectTransform"},--向右翻页红点

    Page1 = { path = "bg/pageGroup/page1", type = "RectTransform"},
    Page2 = { path = "bg/pageGroup/page2", type = "RectTransform"},
    Page3 = { path = "bg/pageGroup/page3", type = "RectTransform"},
    Page4 = { path = "bg/pageGroup/page4", type = "RectTransform"},
}
local currPage = 1

function DownLoadNewpopup:Init()
    self:SubscribeEvents()
end

function DownLoadNewpopup:OnShow()
    self.__base:OnShow()
    self:RefreshUI()
end

function DownLoadNewpopup:RefreshUI()
    self.BtnGO.gameObject:SetActive(false)
    self.BtnReceive.gameObject:SetActive(true)
    local isFistInDL = ChangePackgeConfig.GetFistDownChangePackgePage()
    ChangePackgeConfig.setFistDownChangePackgePage(1)
    self.BtnRightRed.gameObject:SetActive(isFistInDL ~= 1)
    self.BtnReceiveText.text = lang.Get(257014)
    self.title.text = lang.Get(257002)
	util.IntervalCall(1,self.OnUpdate)
end

local stime =  0
function DownLoadNewpopup:OnUpdate()
    if not window or not window:IsValid() then   -- 窗口关闭也去掉
        return true
    end
    if stime < 5 then
        window.BtnReceiveText.text = lang.Get(257014)..(5-stime)
    end
    if stime >= 5 then
        window.BtnReceive.gameObject:SetActive(false)
        window.BtnGO.gameObject:SetActive(true)
        return true
    end
    stime = stime +1
end

function DownLoadNewpopup:SetPage(pageIndex)
    self.Page1.gameObject:SetActive(false)
    self.Page2.gameObject:SetActive(false)
    self.Page3.gameObject:SetActive(false)
    self.Page4.gameObject:SetActive(false)
    if pageIndex >=1 and pageIndex <=4 then
        if pageIndex == 1 then
            self.Page1.gameObject:SetActive(true)
        elseif  pageIndex == 2 then
            self.Page2.gameObject:SetActive(true)
        elseif  pageIndex == 3 then
            self.Page3.gameObject:SetActive(true)
        elseif  pageIndex == 4 then
            self.Page4.gameObject:SetActive(true)
        end
    else
        self.Page1.gameObject:SetActive(true)
    end
end

function DownLoadNewpopup:SubscribeEvents()
    --关闭
    self.closeBtnEvent = function()
        ui_window_mgr:UnloadModule("DownLoadNewpopup")
    end
    self.closeBtn.onClick:AddListener(self.closeBtnEvent)

    --去下载
    self.BtnGOEvent = function()
        event.Trigger(event.GAME_EVENT_REPORT, "Click_Down", {})
        local open_test_mgr = require "open_test_mgr"
        if ChangePackgeConfig.ChangeData then
            local url = ChangePackgeConfig.ChangeData.URL
            local q1sdk = require "q1sdk"
            q1sdk.ApplicationOpenURL(url)
        end
    end
    self.BtnGO.onClick:AddListener(self.BtnGOEvent)

    self.BtnLeftEvent = function()
        if currPage <= 1 then
            currPage = 1
        else
            currPage = currPage -1
            self:SetPage(currPage)
        end
    end

    self.BtnRightEvent = function()
        if currPage >= 4 then
            currPage = 4
        else
            currPage = currPage + 1
            self:SetPage(currPage)
        end
        local isFistInDL = ChangePackgeConfig.GetFistDownChangePackgePage()
        self.BtnRightRed.gameObject:SetActive(isFistInDL ~= 1)
    end
    self.BtnLeft.onClick:AddListener(self.BtnLeftEvent)
    self.BtnRight.onClick:AddListener(self.BtnRightEvent)
    self:SetPage(currPage)
end

function DownLoadNewpopup:UnsubscribeEvents()
    self.closeBtn.onClick:RemoveListener(self.closeBtnEvent)
    self.BtnGO.onClick:RemoveListener(self.BtnGOEvent)
    stime = 0
    currPage = 1
end

function DownLoadNewpopup:Close()
	if self and self:IsValid() then
		self:UnsubscribeEvents()
    end
	self.__base:Close()
	window = nil
end

local CM = class(ui_base, nil, DownLoadNewpopup)

function Show()
	if window == nil then
		window = CM()
		window._NAME = _NAME;
        window:LoadUIResource("ui/prefabs/uichangepackagepopup.prefab", nil, nil, nil, true)
	else
		window:Show()
	end
	return window
end

function Hide()
	if window ~= nil then
		window:Hide()
	end
end

function Close()
	if window ~= nil then
		window:Close()
		window = nil
	end
end

function OnSceneDestroy()
	Close()
end
event.Register(event.SCENE_DESTROY_NEW, OnSceneDestroy)
