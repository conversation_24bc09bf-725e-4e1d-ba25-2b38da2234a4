--- alliance_user_data.txt  --------------------------------------
--- author: 韩淑俊
--- Date:   2024/6/20 14:43
--- ver:    1.0
--- desc:   联盟玩家数据管理
-------------------------------------------------------------
local require = require
local ipairs = ipairs
local pairs = pairs
local table = table
local print = print
local math = math
local os = require "os"
local log = require "log"
local player_mgr = require "player_mgr"
local event = require "event"
local event_alliance_define = require "event_alliance_define"
local game_scheme = require "game_scheme"
local alliance_pb = require "alliance_pb"
local util = require "util"
module("alliance_user_data")
--当前玩家联盟数据
local allianceRoleBaseData = {} --联盟玩家基础数据
local roleByRank = {} --联盟玩家阶级id 1 2 3 4 5阶级 存储的是id  每个阶段的id
local allianceApplyData = {} -- 联盟申请数据
--查看联盟数据
local lookAllianceRoleData = {}
local lookRoleByRank = {}
--当前成员数据
local userRoleData = {}
--联盟官职信息表数据
local positionData = {} --官职数据 key 官职id 1\2\3\4
--权限界面数据展示
local classAuthorityData = {}
local user_init = false
PrivalegeType = {
    UnlockMail = 1,--解锁同盟邮件
    TrainSpeed = 3,--列车速度提升（10000=100%）
    R4Expand = 4,--R4扩充（增加N人）
    TrainDefense = 5,--列车防御强化（降低掠夺次数）
    AllReceive = 6,--全部领取
    TrainMiddleDefense = 7--列车中级防御（同一个玩家掠夺失败次数）
}
--初始化数据
function Init()
    if user_init then
        return
    end
    user_init = true
    InitPostionData();
end

--region 本地方法 刷新
--赋值联盟成员基础信息
local function UpdateAllianceRoleBase(baseDate, info)
    local data = baseDate
    if info:HasField("roleId") then
        data.roleId = info.roleId
    end
    if info:HasField("faceId") then
        data.faceId = info.faceId
    end
    if info:HasField("level") then
        data.level = info.level
    end
    if info:HasField("strName") then
        data.strName = info.strName
    end
    if info:HasField("personCE") then
        data.personCE = info.personCE
    end
    if info:HasField("logoutTime") then
        data.logoutTime = info.logoutTime
    end
    if info:HasField("passStage") then
        data.passStage = info.passStage
    end
    if info:HasField("frameID") then
        data.frameID = info.frameID
    end
    if info:HasField("serverID") then
        data.serverID = info.serverID
    end
    if info:HasField("authority") then
        data.authority = info.authority
    end
    if info:HasField("position") then
        data.position = info.position
    end
    if info:HasField("freeMoveCnt") then
        data.freeMoveCnt = info.freeMoveCnt
    end
    if info:HasField("joinAllianceTime") then
        data.joinAllianceTime = info.joinAllianceTime
    end
    if info:HasField("sex") then
        data.sex = info.sex
    end
    if info:HasField("FreeMassCnt") then
        data.FreeMassCnt = info.FreeMassCnt
    end
    if info:HasField("faceStr") then
        data.faceStr = info.faceStr
    end
    --缺少 数据 等待服务器联盟战争制作 
    if info.heroList then
        data.heroList = info.heroList
    end
    return data
end
-- 定义一个函数来检查表中是否包含指定的值
local function tableHasValue(t, value)
    for _, v in pairs(t) do
        if v == value then
            return true
        end
    end
    return false
end
--刷新联盟玩家列表 
local function RefreshRoleListData(data)
    local baseRoleData = { }
    local byRankData = { }
    if data then
        for i, roleBase in ipairs(data) do
            if roleBase.roleId then
                local roleId = roleBase.roleId
                --没有当前联盟id的时候进行复制
                if not baseRoleData[roleId] then
                    baseRoleData[roleId] = {}
                end
                --赋值数据
                baseRoleData[roleId] = UpdateAllianceRoleBase(baseRoleData[roleId], roleBase)
                --赋值阶级数据
                local rank = baseRoleData[roleId].authority
                if not byRankData[rank] then
                    byRankData[rank] = {}
                end
                table.insert(byRankData[rank], roleId)
            end
        end
    end
    return baseRoleData, byRankData;
end
--排序联盟成员 传入的是阶级 1 2 3 4等
local function SortMembers(rank, curUser)
    --当前联盟数据获取
    local baseData = curUser and allianceRoleBaseData or lookAllianceRoleData
    local rankData = curUser and roleByRank or lookRoleByRank
    if rankData[rank] then
        --region 写的好乱 弃用
        --table.sort(rankData[rank], function(a, b)
        --    -- 假设每个成员有一个 online 属性，true 表示在线，false 表示离线
        --    -- 以及一个 power 属性表示战力
        --    local roleA = baseData[a]
        --    local roleB = baseData[b]
        --    local isRoleAPlayer = roleA.roleId == player_mgr.GetPlayerRoleID()
        --    local isRoleBPlayer = roleB.roleId == player_mgr.GetPlayerRoleID()
        --    if isRoleAPlayer or isRoleBPlayer then
        --        if isRoleAPlayer then
        --            return true
        --        end
        --        if isRoleBPlayer then
        --            return false
        --        end
        --    elseif roleA.logoutTime == 0 and roleB.logoutTime ~= 0 then
        --        -- 首先根据在线状态排序：在线成员优于离线成员
        --        return true -- a 应该排在 b 前面
        --    elseif roleA.logoutTime ~= 0 and roleB.logoutTime == 0 then
        --        return false -- a 应该排在 b 后面
        --    else
        --        if roleA.position ~= alliance_pb.emAlliancePosition_NoOfficial and roleB.position == alliance_pb.emAlliancePosition_NoOfficial then
        --            return true -- a 应该排在 b 前面
        --        elseif roleA.position == alliance_pb.emAlliancePosition_NoOfficial and roleB.position ~= alliance_pb.emAlliancePosition_NoOfficial then
        --            return false -- a 应该排在 b 后面
        --        end
        --        -- 如果在线状态相同,官职想同，则根据战力排序（战力高的应该排在前面）
        --        return roleA.personCE > roleB.personCE
        --    end
        --end)
        --endregion

        table.sort(rankData[rank], function(a, b)
            local roleA = baseData[a]
            local roleB = baseData[b]
            --俩人都在线
            if roleA.logoutTime == 0 and roleB.logoutTime == 0 then
                local isRoleAPlayer = roleA.roleId == player_mgr.GetPlayerRoleID()
                local isRoleBPlayer = roleB.roleId == player_mgr.GetPlayerRoleID()
                if isRoleAPlayer then
                    return true
                end
                if isRoleBPlayer then
                    return false
                end
                --官职
                if roleA.position ~= alliance_pb.emAlliancePosition_NoOfficial and roleB.position == alliance_pb.emAlliancePosition_NoOfficial then
                    return true -- a 应该排在 b 前面
                elseif roleA.position == alliance_pb.emAlliancePosition_NoOfficial and roleB.position ~= alliance_pb.emAlliancePosition_NoOfficial then
                    return false -- a 应该排在 b 后面
                end
                --战力
                if roleA.personCE ~= roleB.personCE then
                     return roleA.personCE > roleB.personCE
                end
            end
            
            --是否在线
            if roleA.logoutTime == 0 and roleB.logoutTime ~= 0 then
                return true
            elseif roleA.logoutTime ~= 0 and roleB.logoutTime == 0 then
                return false
            end
            --俩人不在线
            if roleA.logoutTime ~= 0 and roleB.logoutTime ~= 0 then
                --离线时间
                return roleA.logoutTime > roleB.logoutTime
            end
        end)
    end
    return rankData[rank]
end
--endregion

--region ----- 配置信息 -----
--初始化官职信息
function InitPostionData()
    local posName_cfg = game_scheme:InitBattleProp_0(8029)
    local posInfo_cfg = game_scheme:InitBattleProp_0(8042)
    if posName_cfg and posInfo_cfg and posName_cfg.szParam.data and posInfo_cfg.szParam.data then
        local len = #posName_cfg.szParam.data
        for i = 0, len do
            local posData = {}
            --官职名称
            posData.title = posName_cfg.szParam.data[i]
            posData.titleInfo = posInfo_cfg.szParam.data[i]
            if not positionData then
                positionData = {}
            end
            table.insert(positionData, posData)
        end
    end
end
--获取当前官职信息
function GetPostionData(id)
    return positionData[id]
end
--获取阶级介绍
function GetAuthorityInfo(id)
    local index = 4 - id;
    local posName_cfg = game_scheme:InitBattleProp_0(8043)
    if posName_cfg and posName_cfg.szParam.data then
        return posName_cfg.szParam.data[index]
    end
    return nil;
end

---@public 获取职位描述信息
function GetPositionInfo(position)
    local posName_cfg = game_scheme:InitBattleProp_0(8042)
    if posName_cfg and posName_cfg.szParam.data then
        return posName_cfg.szParam.data[position - 1]
    end
    return nil;
end

--endregion

--region ----- 数据刷新 -----

--刷新查看联盟玩家数据
function UpdateLookAllianceRoleData(msg)
    if msg.arrMembers then
        lookAllianceRoleData, lookRoleByRank = RefreshRoleListData(msg.arrMembers);
        --刷新联盟玩家数据
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_ROLE_DATA)
    end
end

--玩家列表数据推送 刷新
function RoleListDataNtf(msg)
    if msg.arrMembers then
        allianceRoleBaseData, roleByRank = RefreshRoleListData(msg.arrMembers);
        for i, v in pairs(allianceRoleBaseData) do
            if v.roleId == player_mgr.GetPlayerRoleID() then
                userRoleData = v
            end
        end
        --刷新联盟玩家数据
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_ROLE_DATA)
    end
end

--修改玩家列表数据
function ModifyRoleListData(msg)
    if msg.arrMembers then
        local data = msg.arrMembers
        for i, v in pairs(data) do
            local roleId = v.roleId
            for key, value in pairs(v) do
                allianceRoleBaseData[roleId][key] = value
            end
        end
    end
    event.Trigger(event_alliance_define.UPDATE_ALLIANCE_ROLE_DATA)
end

--修改玩家免费迁城数据
function ModifyRoleFreeMoveCnt(msg)
    if userRoleData and msg.dbid == player_mgr.GetPlayerRoleID() then
        userRoleData.freeMoveCnt = msg.freeMoveCnt
    end

    if allianceRoleBaseData and allianceRoleBaseData[msg.dbid] then
        allianceRoleBaseData[msg.dbid].freeMoveCnt = msg.freeMoveCnt
    end
    event.Trigger(event_alliance_define.UPDATE_ALLIANCE_ROLE_DATA)
end

-- 查找联盟成员基础数据
function FindRoleDate(roleId, curUser)
    curUser = curUser or false;
    local baseData = curUser and allianceRoleBaseData or lookAllianceRoleData
    return baseData[roleId]
end

--获取当前联盟官职成员数据
function GetPosData(curUser)
    curUser = curUser or false;
    local baseData = curUser and allianceRoleBaseData or lookAllianceRoleData
    local posData = {} --官职
    for i, v in pairs(baseData) do
        if v.position and v.position > alliance_pb.emAlliancePosition_NoOfficial then
            posData[v.position] = {}
            posData[v.position] = v;
        end
    end
    return posData
end

function GetBossData(curUser)
    curUser = curUser or false;
    local baseData = curUser and allianceRoleBaseData or lookAllianceRoleData
    local posData = {}
    for i, v in pairs(baseData) do
        if v.authority and  v.authority >= alliance_pb.emAllianceAuthority_R5 then
            posData = v
            return posData
        end
    end
    return posData
end

--获取当前阶级成员数量
function GetRankCount(rank, curUser)
    curUser = curUser or false;
    local count = 0;
    --当前联盟数据获取
    local rankData = curUser and roleByRank or lookRoleByRank
    if rankData[rank] then
        for i, roleId in ipairs(rankData[rank]) do
            count = count + 1;
        end
    end
    return count
end

function GetOnLineRankCount(rank, curUser)
    curUser = curUser or false;
    local count = 0;
    --当前联盟数据获取
    local rankData = curUser and roleByRank or lookRoleByRank
    local roleData = curUser and allianceRoleBaseData or lookAllianceRoleData
    if rankData[rank] then
        for i, roleId in ipairs(rankData[rank]) do
            local roleInfo = roleData[roleId]
            if not curUser or (roleInfo and roleInfo.logoutTime == 0) then
                count = count + 1;
            end
        end
    end
    return count
end

--获取当前阶级成员
function GetRankSortData(rank, curUser)
    curUser = curUser or false;
    local roleRankData = {}
    local rankdata = SortMembers(rank, curUser)
    if rankdata then
        for i, v in ipairs(rankdata) do
            local roledata = FindRoleDate(v, curUser)
            local authorityInfo = GetAuthorityInfo(roledata.authority)
            if not authorityInfo then
                roledata.authorityInfo = authorityInfo
            end
            table.insert(roleRankData, roledata)
        end
    end
    return roleRankData
end

--endregion

--region -----   当前联盟数据获取   -----
local function updateData(original, updates)
    local lookupTable = {}

    -- 创建一个查找表
    for _, entry in ipairs(original) do
        lookupTable[entry.roleId] = entry
    end
    -- 进行更新
    for _, update in ipairs(updates) do
        local entry = lookupTable[update.roleId]
        if entry then
            for key, value in pairs(update) do
                if key ~= "roleId" then
                    entry[key] = value
                end
            end
        end
    end
end

--修改联盟成员基础数据
function ModifyAllianceRoleData(msg)
    if msg.arrMembers then
        local data = msg.arrMembers
        if data then
            updateData(allianceRoleBaseData, data)
            print(allianceRoleBaseData)
        end
    end
end

--获取任命官职玩家数据
function GetAppPosData()
    -- 根据authority字段对allianceRoleBaseData进行排序
    local sortedData = {}
    local sortedRoles = {}
    for rank, roles in pairs(allianceRoleBaseData) do
        if roles.position <= alliance_pb.emAlliancePosition_NoOfficial and roles.authority <= alliance_pb.emAllianceAuthority_R4 then
            --移出盟主 和 有官职的玩家
            table.insert(sortedRoles, roles)
        end
    end
    table.sort(sortedRoles, function(a, b)
        return a.authority > b.authority
    end)
    for _, role in ipairs(sortedRoles) do
        table.insert(sortedData, role)
    end
    return sortedData;
end

--获取用户在当前联盟的数据
function GetCurrentRank()
    local playerId = player_mgr.GetPlayerRoleID()
    for i, v in pairs(allianceRoleBaseData) do
        if v.roleId == playerId then
            return v
        end
    end
    return nil
end
--endregion

--region ----- 联盟玩家管理 -----
--阶级 官职 发生改变
function AllianceAuthorityRsp(msg)
    if msg.roleId and msg.authority then
        local tempData = allianceRoleBaseData[msg.roleId]
        if tempData then
            if tempData.authority ~= msg.authority then
                if roleByRank[tempData.authority] then
                    for i, v in ipairs(roleByRank[tempData.authority]) do
                        if v == msg.roleId then
                            table.remove(roleByRank[tempData.authority], i)
                            break
                        end
                    end
                end
                if not roleByRank[msg.authority] then
                    roleByRank[msg.authority] = {}
                end
                table.insert(roleByRank[msg.authority], msg.roleId)
                allianceRoleBaseData[msg.roleId].authority = msg.authority
            end
            --检测msg的 positionOpt 和 position字段是否存在
            if msg.position then
                allianceRoleBaseData[msg.roleId].position = msg.position
            end
        end
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_ROLE_DATA)
    end
end
--成员数据发生改变
function AllianceModifyArrMember(msg)
    if msg.arrMembers then
        local r5OldRoleId = GetBossData(true).roleId
        local r5NewRoleId = nil
        for i, v in ipairs(msg.arrMembers) do
            local data = allianceRoleBaseData[v.roleId]
            if data then
                local tempData = UpdateAllianceRoleBase(data, v)
                if tempData.roleId == userRoleData.roleId then
                    userRoleData = tempData
                end
                allianceRoleBaseData[v.roleId] = tempData
                if tempData.authority == alliance_pb.emAllianceAuthority_R5 then
                    r5NewRoleId = tempData.roleId
                end
                for i, v in pairs(roleByRank) do
                    for key, value in ipairs(v) do
                        if value == tempData.roleId then
                            if i ~= tempData.authority then
                                table.remove(v, key)
                                if not roleByRank[tempData.authority] then
                                    roleByRank[tempData.authority] = {}
                                end
                                table.insert(roleByRank[tempData.authority], tempData.roleId)
                            end
                            break ;
                        end
                    end
                end
            else
                local tempData = {}
                tempData = UpdateAllianceRoleBase(tempData, v)
                allianceRoleBaseData[v.roleId] = tempData
                if not roleByRank then
                    roleByRank = {}
                end
                if not roleByRank[tempData.authority] then
                    if tempData and tempData.authority then
                        roleByRank[tempData.authority] = {}
                        table.insert(roleByRank[tempData.authority], tempData.roleId)
                    end
                else
                    table.insert(roleByRank[tempData.authority], tempData.roleId)
                end
            end
        end
        if r5NewRoleId and (r5OldRoleId ~= r5NewRoleId) then
            event.Trigger(event_alliance_define.UPDATE_ALLIANCE_ROLE_R5_DATA, r5NewRoleId, r5OldRoleId)
        end
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_ROLE_DATA)

        RefreshApplyRedFunction()
    end
end
--开除成员
local function AllianceExpelData(roleId)
    if not allianceRoleBaseData[roleId] then
        return
    else
        for i, v in pairs(roleByRank) do
            for key, value in pairs(v) do
                if value == roleId then
                    allianceRoleBaseData[roleId] = nil
                    table.remove(v, key)
                    break ;
                end
            end
        end
    end
end
--开除成员
function AllianceExpel(msg)
    if msg.arrMemberDelRoleId then
        for i, v in ipairs(msg.arrMemberDelRoleId) do
            AllianceExpelData(v)
        end
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_EXPEL_ROLE)
    end
    if msg.roleId then
        local roleId = msg.roleId
        AllianceExpelData(roleId)
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_EXPEL_ROLE)
    end
end
--被踢出
function AllianceKickedOut()
    --这里弹出通用弹窗
    --message_box.Open(lang.Get(600383), message_box.STYLE_YES, nil, nil, lang.Get(602001))
    event.Trigger(event_alliance_define.EXIT_ALLIANCE)
    event.Trigger(event_alliance_define.ALLIANCE_STATE_UPDATE)
end
--转让盟主
function AllianceChangeR5(msg)
    if msg.roleId then
        if allianceRoleBaseData[msg.roleId] then
            local tempData = allianceRoleBaseData[msg.roleId]
            table.remove_value(roleByRank[tempData.authority], tempData.roleId)
            allianceRoleBaseData[msg.roleId].authority = alliance_pb.emAllianceAuthority_R5
            tempData = allianceRoleBaseData[msg.roleId]
            if not roleByRank[tempData.authority] then
                roleByRank[tempData.authority] = {}
            end
            table.insert(roleByRank[tempData.authority], tempData.roleId)
        end
        if userRoleData and userRoleData.roleId then
            table.remove_value(roleByRank[userRoleData.authority], userRoleData.roleId)
            allianceRoleBaseData[userRoleData.roleId].authority = alliance_pb.emAllianceAuthority_R1
            userRoleData.authority = alliance_pb.emAllianceAuthority_R1
            if not roleByRank[userRoleData.authority] then
                roleByRank[userRoleData.authority] = {}
            end
            table.insert(roleByRank[userRoleData.authority], userRoleData.roleId)
        end
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_ROLE_R5_DATA, msg.roleId, userRoleData.roleId)
    end
    event.Trigger(event_alliance_define.UPDATE_ALLIANCE_ROLE_DATA)
end
--返回阶级 人数上限 传入阶段id  阶段1 = 1 阶段5 = 5
function GetAuthorityPeopleLimit(authorityId)
    local index = 5 - authorityId
    local authorityCfg = game_scheme:InitBattleProp_0(8023);
    if authorityCfg and authorityCfg.szParam.data then
        if #authorityCfg.szParam.data >= index then
            local num = authorityCfg.szParam.data[index]
            --R4扩充（增加N人）
            if authorityId == alliance_pb.emAllianceAuthority_R4 then
                local cfg = game_scheme:LeagueGiftPrivilege_0(PrivalegeType.R4Expand)--特权
                if cfg then
                    local alliance_gift_data = require "alliance_gift_data"
                    local giftData = alliance_gift_data.GetGiftLevelData()
                    if giftData and giftData.allianceGiftLV >= cfg.UnlockLevel then
                        num = num + cfg.Parameter
                    end
                end
            end
            return num
        end
    end
    log.Error("##### 没有查找到对应的数据 #####")
    return nil;
end
--endregion
--region 联盟 申请 列表
--获取联盟申请列表
function GetApplyListData()
    return allianceApplyData
end
--刷新申请列表数据
local function RefreshApplyListData(data)
    if data then
        allianceApplyData = {}
        for i, roleBase in ipairs(data) do
            local index = i;
            --没有当前联盟id的时候进行复制
            if not allianceApplyData[index] then
                allianceApplyData[index] = {}
            end
            --赋值数据
            allianceApplyData[index] = UpdateAllianceRoleBase(allianceApplyData[index], roleBase.stPersonBase)
            allianceApplyData[index].iApplyTime = roleBase.iApplyTime
        end
    end
    RefreshApplyRedFunction()
end
--刷新申请列表
function UpdateApplyDataNTF(msg)
    if msg.arrApplyPersons then
        RefreshApplyListData(msg.arrApplyPersons)
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_APPLY_DATA, allianceApplyData)
    end
    RefreshApplyRedFunction()
end
--刷新申请列表
function UpdateApplyDataRsp(msg)
    if msg.roleInfo then
        RefreshApplyListData(msg.roleInfo)
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_APPLY_DATA, allianceApplyData)
    end
    RefreshApplyRedFunction()
end
--修改申请列表
function ModifyApplyDataNTF(msg)
    if msg.arrApplyPersons then
        for i, roleBase in ipairs(msg.arrApplyPersons) do
            local data = {}
            data = UpdateAllianceRoleBase(data, roleBase.stPersonBase)
            data.iApplyTime = roleBase.iApplyTime
            table.insert(allianceApplyData, data)
        end
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_APPLY_DATA, allianceApplyData)
    end
    RefreshApplyRedFunction()
end
--申请列表中删除
function RefuseApply(msg)
    if msg.arrApplyDelRoleId then
        for i, v in ipairs(msg.arrApplyDelRoleId) do
            RemoveApplyData(v)
        end
        --刷新申请列表
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_APPLY_DATA, allianceApplyData)
    end
    RefreshApplyRedFunction()
end
--申请列表中删除
function RemoveApplyData(id)
    if id then
        for i = #allianceApplyData, 1, -1 do
            if allianceApplyData[i].roleId == id then
                table.remove(allianceApplyData, i)
                break
            end
        end
        --刷新申请列表
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_APPLY_DATA, allianceApplyData)
    end
    RefreshApplyRedFunction()
end
--刷新申请红点逻辑
function RefreshApplyRedFunction()
    local red_system = require "red_system"
    local red_const = require "red_const"
    red_system.TriggerRed(red_const.Enum.AllianceApply)
    --if util.get_len(allianceApplyData) > 0 then
    --    --刷新展示红点
    --else
    --    --刷新隐藏红点
    --end
end
--endregion

--region -- 联盟用户等级权限 --
--获取联盟权限 1 2 3 4 5阶级 id = 阶级
function GetAuthorityPer(id)
    local cfg = game_scheme:LeagueAuthority_0(id)
    if cfg then
        --返回权限列表id 对应表数据LeaguePermissionEffect
        return cfg.PermissionID.data;
    end
end
--返回所有权限数据
function GetPermissionData()
    local len = game_scheme:LeaguePermissionEffect_nums();
    local permissionData = {}
    for i = 0, len - 1 do
        local cfg = game_scheme:LeaguePermissionEffect(i)
        if cfg then
            local data = {}
            data.id = cfg.AuthorityID
            data.lang = cfg.PermissionNameId;
            data.Display = cfg.Display
            table.insert(permissionData, data)
        end
    end
    return permissionData
end
--获取权限数据
function GetClassAuthorityData()
    if #classAuthorityData > 0 then
        return classAuthorityData
    end
    local authorityPerData = {}
    for i = 1, 5 do
        local data = GetAuthorityPer(6 - i)
        table.insert(authorityPerData, data)
    end
    local authorityData = GetPermissionData()
    for i, v in ipairs(authorityData) do
        if v.Display and v.Display == 0 then
            local data = {}
            data.id = v.id
            data.lang = v.lang
            data.perData = {}
            for j, t in ipairs(authorityPerData) do
                data.perData[j] = tableHasValue(t, v.id)
            end
            table.insert(classAuthorityData, data)
        end
       
    end
    return classAuthorityData
end

function GetJoinAllianceTime()
    local playerRoleId = player_mgr.GetPlayerRoleID()
    --print("GetJoinAllianceTime", playerRoleId)
    if allianceRoleBaseData[playerRoleId] then
        --print("GetJoinAllianceTime::allianceRoleBaseData", allianceRoleBaseData[playerRoleId].joinAllianceTime)
        return allianceRoleBaseData[playerRoleId].joinAllianceTime
    end
    return nil
end

--判断权限
function HasPermission(permission)
    local isSuccess, authority = GetRoleAuthority(player_mgr.GetPlayerRoleID())
    if not isSuccess then
        if not userRoleData or not userRoleData.authority then
            return false
        end
        authority = userRoleData.authority
    end
    local indexId = 6 - authority
    local perIds = GetAuthorityPer(indexId)
    return tableHasValue(perIds, permission)
end

--endregion

function GetUserRoleData()
    local roleInfo = GetAllianceRoleInfo(player_mgr.GetPlayerRoleID())
    if not roleInfo then
        return userRoleData
    end
    return roleInfo
end
---@public 设置邀请集结免费次数
function SetAllianceRoleFreeMassNum(freeMassCnt)
    local curUser = GetUserRoleData()
    if curUser then
        curUser.FreeMassCnt = freeMassCnt
    end
end

function GetUserAllianceRoleNum()
    return util.get_len(allianceRoleBaseData)
end

function GetRoleAuthority(id)
    if allianceRoleBaseData[id] then
        return true, allianceRoleBaseData[id].authority
    end
    return false, 0
end

function GetAllianceRoleInfo(roleId)
    return allianceRoleBaseData[roleId]
end

---获取当前玩家 阶级
---@return number 阶级 alliance_pb.emAllianceAuthority_R1
function GetPlayerAuthority()
    local isSuccess, authority = GetRoleAuthority(player_mgr.GetPlayerRoleID())
    if isSuccess then
        return authority
    end
    if userRoleData then
        return userRoleData.authority
    end
    return nil
end

---获取联盟成员id
---@return table 联盟成员id
function GetAllAllianceMemberIds(noSelf)
    local memberIds = {}
    if noSelf then
        local data_personalInfo = require "data_personalInfo"
        local roleId = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID)
        for i, v in pairs(allianceRoleBaseData) do
            if v.roleId ~= roleId then
                table.insert(memberIds, v.roleId)
            end
        end
        -- 当联盟只有我的时候
        if #memberIds == 0 then
            table.insert(memberIds, roleId)
        end
    else
        for i, v in pairs(allianceRoleBaseData) do
            table.insert(memberIds, v.roleId)
        end
    end
    return memberIds
end

--获取随机一个盟友,不包括自己
function GetRandomAllianceMember()
    local memberIds = GetAllAllianceMemberIds(true)
    local randomIndex = math.random(1, #memberIds)
    -- 使用随机索引获取相应的对象
    local randomKey = memberIds[randomIndex]
    local member = allianceRoleBaseData[randomKey]
    return member
end

---@public 获取24小时内在线的随机一个盟友
---@return table 盟友数据
function Get24HourOnlineRandomMember()
    if not allianceRoleBaseData then
         return nil
    end

    local memberArr = {}
    local roleId = player_mgr.GetPlayerRoleID()
    for _, v in pairs(allianceRoleBaseData) do
        if v.roleId ~= roleId then --不包括自己

            if v.logoutTime == 0 then
                table.insert(memberArr, v)
            else
                local leaveTime = os.server_time() - v.logoutTime
                if leaveTime < 86400 then
                    table.insert(memberArr, v)
                end
            end
        end
    end
    local memberLength = #memberArr
    if memberLength > 0 then
        local randomIndex = math.random(1, memberLength)
        return memberArr[randomIndex]
    end
    return nil
end


--获得剩余免费迁城次数
function GetAllianceRoleMoveCityCnt()
    if userRoleData and userRoleData.freeMoveCnt then
        return userRoleData.freeMoveCnt
    end
    return 0
end

---@public 获取联盟全部成员数据
function GetAllianceAllMemberData()
    return allianceRoleBaseData
end

function ClearAllianceData()
    allianceRoleBaseData = {}
    roleByRank = {}
    allianceApplyData = {}
    lookAllianceRoleData = {}
    lookRoleByRank = {}
    userRoleData = {}
end
--TODO清除数据 上下要对应
function ClearUserData()
    allianceRoleBaseData = {}
    roleByRank = {}
    allianceApplyData = {}
    lookAllianceRoleData = {}
    lookRoleByRank = {}
    userRoleData = {}
    classAuthorityData = {} --权限界面数据展示
end
event.Register(event.USER_DATA_RESET, ClearUserData)
event.Register(event_alliance_define.EXIT_ALLIANCE, ClearAllianceData)