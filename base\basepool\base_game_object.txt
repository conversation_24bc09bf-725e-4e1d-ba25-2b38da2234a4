local require = require
local print = print
local pairs = pairs
local assert = assert
local type = type
local typeof = typeof
local setmetatable = setmetatable

local GameObject = CS.UnityEngine.GameObject
local GameObjectPool = CS.War.Base.GameObjectPool
local UI = CS.UnityEngine.UI
local TextMeshProUGUIType = typeof(CS.TMPro.TextMeshProUGUI)
local TextType = typeof(CS.UnityEngine.UI.Text)
local Application = CS.UnityEngine.Application

local class = require "class"
local base_object = require "base_object"
local util = require "util"
local log = require "log"
local asset_loader = require "asset_loader"
local res_pool = require "res_pool"
local val = require "val"

local Release = GameObjectPool.Release
local GetAsync = GameObjectPool.GetAsync

module("base_game_object")

local all_pool = val.IsTrue("sw_gameobject_pool", 0)

Release = function(go, bg)
    if not all_pool then
        GameObjectPool.Release(go, bg)
        return
    end
    -- print("base_game_object Release", go and go.name, bg._go_assetBundleName)

    if not bg._res_vo then
        GameObjectPool.Release(go, bg)
        -- print("bg_Release _res_vo nil", go and go.name, bg._go_assetBundleName)
        return
    end
    res_pool.return_res(bg._res_vo)
end

GetAsync = function(abname, assetname, callback, bg)
    if not all_pool then
        return GameObjectPool.GetAsync(abname, assetname, callback)
    end
    -- res_pool.start()

    local res_vo = res_pool.get_res(
            abname,
            function(res_vo1)
                -- print("base_game_object GetAsync callback", abname, res_vo1)
                bg._res_vo = res_vo1
                callback(res_vo1.go)
            end,
            nil,
            bg and bg.tag
    )

    bg._res_vo = res_vo or bg._res_vo
    -- print("base_game_object GetAsync", abname, bg._res_vo)
end

local BaseGameObject = {}
BaseGameObject.widget_table = {}

function BaseGameObject:ctor(selfType, tag)
    self.gameObject = nil
    self.transform = nil
    self.translate = true; --是否翻译文本，默认为true
    --[[    if not tag then
        self.notag = true
    end]]
    self.notag = tag == nil
    self.tag = tag or "base_game_object"
end

function BaseGameObject:LoadResource(assetBundleName, assetName, callback, useIO, parentTrans, TranslateGameObject)
    if not assetBundleName then
        log.Error("BaseGameObject assetBundleName nil", assetBundleName)
        return
    end

    if Application.isEditor and self.notag then
        local tracetype = 1
        local tracefunc = log.Warning
        if tracetype == 1 then
            tracefunc = log.logfilter
        end
        tracefunc("BaseGameObject tag nil", assetBundleName, 2)
    end

    --print("LoadResource",assetBundleName)
    self.isDisposed = false

    if TranslateGameObject ~= nil then
        self.translate = TranslateGameObject
    end

    if all_pool then
        self.useIO = nil
        self.oneshoot = useIO
    else
        self.useIO = useIO
    end

    self.__bg_callback = callback

    if self.useIO then
        self.io_loader = self.io_loader or asset_loader(assetBundleName, self.tag)
        self.io_loader.assetPath = assetBundleName
        self.io_loader:load(
                function(loader)
                    local UIPrefab = loader.asset
                    if self:IsDisposed() or (parentTrans and util.IsObjNull(parentTrans)) then
                        return
                    end

                    -- 加载资源回调，如果当前物体与当前加载资源不一致，销毁
                    if self.gameObject and not util.IsObjNull(self.gameObject) then
                        if self._go_assetBundleName ~= loader.assetPath then
                            GameObject.DestroyImmediate(self.gameObject)
                            self.gameObject = nil
                            self.UIRoot = nil
                        end
                    end

                    if util.IsObjNull(self.gameObject) then
                        self.UIRoot = GameObject.Instantiate(UIPrefab, parentTrans, false)
                        self.gameObject = self.UIRoot
                    else
                        log.Warning("BaseGameObject self.gameObject exist:skip Instantiate", assetBundleName)
                    end

                    self._go_assetBundleName = assetBundleName
                    self.transform = self.gameObject.transform
                    self.gameObject:SetActive(true)

                    util.DontDestroyOnLoad(self.gameObject)

                    local lang_util = require "lang_util"
                    lang_util.SetFont(self.gameObject)
                    if self.UIRoot and self.translate then
                        lang_util.TranslateGameObject(self.UIRoot)
                    end
                    self:_OnLoadComplete(self.__bg_callback)
                end
        )
    else
        if self.loading_assetBundleName ~= assetBundleName then
            self.loading_assetBundleName = nil
        end
        if self.loading_assetBundleName then
            -- log.Warning("BaseGameObject loading_assetBundleName exist: wait to callback", self.loading_assetBundleName)
            return
        end

        if util.IsObjNull(self.gameObject) then
            self._go_assetBundleName = nil
        end

        local lcallback = function(gameObject, bCached)
            if self:IsDisposed() then
                self.loading_assetBundleName = nil
                Release(gameObject, self)
                return
            end
            if util.IsObjNull(gameObject) then
                return
            end

            if self.loading_assetBundleName then
                -- 当前加载回调与最后一次加载资源不一致
                if self.loading_assetBundleName ~= assetBundleName then
                    log.Warning("BaseGameObject loading_assetBundleName change: release", self.loading_assetBundleName, assetBundleName)
                    self.loading_assetBundleName = nil
                    Release(gameObject, self)
                    return
                end

                -- 加载资源回调，如果当前物体与当前加载资源不一致，销毁
                if self._go_assetBundleName ~= self.loading_assetBundleName then
                    if self.gameObject and not util.IsObjNull(self.gameObject) then
                        Release(self.gameObject, self)
                        self.gameObject = nil
                        self.UIRoot = nil
                        log.Warning("BaseGameObject gameObject reset", self.loading_assetBundleName, self._go_assetBundleName)
                    end
                end

                if util.IsObjNull(self.gameObject) then
                    self.gameObject = gameObject
                    self.UIRoot = gameObject
                else
                    Release(gameObject, self)
                    log.Warning("BaseGameObject self.gameObject exist: skip", self._go_assetBundleName, self.loading_assetBundleName)
                end
            else
                self.gameObject = gameObject
            end

            self.loading_assetBundleName = nil
            -- self._go_assetBundleName 标记当前self.gameObject
            self._go_assetBundleName = assetBundleName

            util.DontDestroyOnLoad(self.gameObject)
            self.transform = self.gameObject.transform

            self.transform:SetParent(parentTrans, false)
            self.transform.localPosition = {x = 0, y = 0, z = 0}
            self.gameObject:SetActive(true)
            local lang_util = require "lang_util"
            -- 2022/6/17 低端机（Redmi Note7）真机 profiler 显示 SetFont 中消耗较高
            if not bCached then
                lang_util.SetFont(self.gameObject)
                -- else
                -- 	print("Cached item:", self.gameObject.name)
            end
            if self.UIRoot and self.translate then
                lang_util.TranslateGameObject(self.UIRoot)
            end
            self:_OnLoadComplete(self.__bg_callback)
        end

        -- if self._go_assetBundleName then
        -- 	log.Warning("BaseGameObject _go_assetBundleName exist:  callback",self._go_assetBundleName)
        -- 	lcallback(self.gameObject)
        -- 	return
        -- end

        if self._go_assetBundleName == assetBundleName then
            -- log.Warning("BaseGameObject _go_assetBundleName exist:  callback",self._go_assetBundleName)
            lcallback(self.gameObject)
            return
        end

        self.loading_assetBundleName = assetBundleName

        GetAsync(assetBundleName, assetName, lcallback, self)
    end
end

function BaseGameObject:IsArabicEnvironment()
    local lang = require "lang"
    if lang.USE_LANG == lang.AR then
        return true
    end
    return false
end

function BaseGameObject:IsRTLEnvironment()
    local lang = require "lang"
    return lang.IsRTLEnvironment()
end

function BaseGameObject:FixMultiLang()
end

function BaseGameObject:_OnLoadComplete(callback)
    if self._res_vo then
        self._res_vo.oneshoot = self.oneshoot
    end
    local rootTrans = self.gameObject.transform
    self.transform = rootTrans

    for k, v in pairs(self.widget_table) do
        --assign widget to class member
        local child = nil
        if v.path == "" then
            child = rootTrans
        else
            child = rootTrans:Find(v.path)
        end
        assert(child, self.gameObject.name .. " " .. v.path .. " not found!")
        local childType = nil
        if type(v.type) == "string" then
            if v.type == "TextMeshProUGUI" then
                childType = TextMeshProUGUIType
            else
                childType = typeof(UI[v.type])
            end
        else
            childType = typeof(v.type)
        end
        self[k] = child:GetComponent(childType)
        assert(v.optional or self[k], self.gameObject.name .. " " .. childType.Name .. " Component not found!")
        if self:IsRTLEnvironment() then
            if v.fitArabic and childType then
                local text = self[k]
                if text then
                    if TextType:IsAssignableFrom(childType) then
                        util.SetTextLeft2Right(text)
                    elseif TextMeshProUGUIType:IsAssignableFrom(childType) then
                        util.SetTextMeshProUGUILeft2Right(text)
                    end
                end
            end
        end
    end
    self:FixMultiLang()
    self:OnResLoad()

    if callback ~= nil then
        callback(self.gameObject, self)
    end
    
    if self.nBaseParent then
        self:ChangeParent(self.nBaseParent, true)
    end
end

function BaseGameObject:OnResLoad()
    --------print("BaseGameObject:OnResLoad")
end

function BaseGameObject:IsDisposed()
    return self.isDisposed
end

function BaseGameObject:Dispose()
    if self.___prevent then
        log.Error("BaseGameObject prevent", self.loading_assetBundleName)
    end

    self.isDisposed = true

    if self.useIO then
        if self.gameObject ~= nil then
            if not self.gameObject:IsNull() then
                GameObject.DestroyImmediate(self.gameObject)
            end
            -- self.UIRoot = nil
        end
        if self.io_loader then
            self.io_loader:Dispose()
        end
    else
        if not util.IsObjNull(self.gameObject) then
            Release(self.gameObject, self)
        end
        self.loading_assetBundleName = nil
    end

    for k, v in pairs(self.widget_table) do
        self[k] = nil
    end
    self.gameObject = nil
    self.UIRoot = nil
    self.__bg_callback = nil

    self.__base:Dispose()
end

function BaseGameObject:IsValid()
    if self and self.UIRoot and not self.UIRoot:IsNull() and not self.isDisposed then
        return true
    else
        return false
    end
end

function BaseGameObject:ChangeParent(parent, resetPosition)
    self.nBaseParent = parent
    if self:IsValid() then
        self.transform:SetParent(self.nBaseParent, false)
        if resetPosition then
            self.transform.localPosition = { x = 0, y = 0, z = 0 }
        end
    end
end

return class(base_object, nil, BaseGameObject)


-- function new(...)
--     local self = {}
--     setmetatable(self, {__index = BaseGameObject})
--     self:ctor(BaseGameObject, ...)
--     return self
-- end
-- return new
