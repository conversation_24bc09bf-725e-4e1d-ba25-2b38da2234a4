﻿---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2025/9/12 15:41
--- 创建角色队列管理器
local table = table
local ipairs = ipairs
local require = require
local laymain_data = require "laymain_data"
local event = require "event"
local log = require "log"
local net = require "net"
local lang = require "lang"
local flow_text = require "flow_text"
local net_route = require "net_route"
local url_operation_mgr = require "url_operation_mgr"
local login_pb = require "login_pb"
local xManMsg_pb = require "xManMsg_pb"
local virtual_main_level_module = require "virtual_main_level_module"
local virtual_home_build_module = require "virtual_home_build_module"
local virtual_player_module = require "virtual_player_module"
local Application = CS.UnityEngine.Application
module("create_character_queue_mgr")
local isNetLocalNewBie = false

local isLocalNewBie = true
---@public 获取是否是本地新手
function GetLocalNewBie()
    local b_op_config = url_operation_mgr.GetConfig("enable_local_newbie") == true
    return isLocalNewBie and (b_op_config or Application.isEditor)
end

---@public 获取这次登录是本地新手
function GetNetLocalNewBie()
    return isNetLocalNewBie
end

function ClearLocalData()
    --清除本地数据
    virtual_player_module.DeleteNoviceEventValue()
    virtual_main_level_module.DeletePassLevel()
    virtual_home_build_module.DeleteLocalData()
end

--region 网络消息处理
function MSG_ACTOR_CREATE_DATA_SYNC_REQ()
    local req = login_pb.TMSG_ACTOR_CREATE_DATA_SYNC_REQ()
    req.nLevel = 1
    req.nLevelChallenge = virtual_main_level_module.GetPassLevel()
    local levelChallengeData = virtual_main_level_module.GetLevelChallengeReqData()
    if levelChallengeData then
        for i, v in ipairs(levelChallengeData) do
            table.insert(req.arrLevelChallengeReq, v)
        end
    end
    local arrRepairMapid = virtual_home_build_module.GetSaveRepairMapIdList()
    if arrRepairMapid then
        for i, v in ipairs(arrRepairMapid) do
            table.insert(req.arrRepairMapid, v)
        end
    end
    local arrOpenGiftMapid = virtual_home_build_module.GetSaveOpenGiftMapIdList()
    if arrOpenGiftMapid then
        for i, v in ipairs(arrOpenGiftMapid) do
            table.insert(req.arrOpenGiftMapid, v)
        end
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ACTOR_CREATE_DATA_SYNC_REQ, req)
end

function MSG_ACTOR_CREATE_DATA_SYNC_RSP(msg)
    if not msg then
        return
    end
    if msg.errCode and msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return false
    end
    if msg.nLevelChallenge and msg.nLevelChallenge >= 0 then
        laymain_data.setPassLevel(msg.nLevelChallenge)
    end
    if msg.arrBuilding then
        local GWHomeMgr = require "gw_home_mgr"
        GWHomeMgr.buildingData.UpdateData({ building = msg.arrBuilding }, true)
    end
    if msg.arrPropData then
        local player_mgr = require "player_mgr"
        for key, propData in ipairs(msg.arrPropData) do
            player_mgr.UpdateNumProp(propData.propid, propData.propvalue)
        end
    end
end

---@public 创建队列通知
function S2CCreateQueueNTF(msg)
    if not msg or not msg.state then
        return
    end
    --判断是否是本地新手 就算服务器下发 也不进入 domain控制
    local isOpen = GetLocalNewBie()
    if not isOpen then
        return
    end
    if msg.state == 1 then
        ClearLocalData()
        log.Warning("CreateQueue 开启本地新手")
        isNetLocalNewBie = true
        local virtual_server = require "virtual_server"
        virtual_server.Start()
    elseif msg.state == 2 then
        log.Warning("CreateQueue 关闭本地新手 转换为正常流程")
        local virtual_server = require "virtual_server"
        virtual_server.Stop()
        -- 登录数据处理完成事件
        --如果是登录创角排队  发送本地缓存数据
        if isNetLocalNewBie then
            MSG_ACTOR_CREATE_DATA_SYNC_REQ()
        end
        --收到玩家数据创建完成 清楚本地数据
        ClearLocalData()
    else
        log.Error("S2CCreateQueueNTF 暂未有处理这个状态 state = ", msg.state)
    end
end

local MessageTable = {
    { xManMsg_pb.MSG_ACTOR_CREATE_DATA_SYNC_RSP, MSG_ACTOR_CREATE_DATA_SYNC_RSP, login_pb.TMSG_ACTOR_CREATE_DATA_SYNC_RSP },
    --创角排队
    { xManMsg_pb.MSG_LOGIN_ACTOR_CREATE_QUEUE_NTF, S2CCreateQueueNTF, login_pb.TMSG_LOGIN_ACTOR_CREATE_QUEUE_NTF, },
}
net_route.RegisterMsgHandlers(MessageTable)
--endregion

function ClearUserData()
    isNetLocalNewBie = false
end
event.Register(event.USER_DATA_RESET, ClearUserData)
