--- Created by: 袁楠
--- DateTime: 2024/8/28
--- desc: 个性化控制器    
---
local require = require
local newClass = newclass

local unforced_guide_mgr = require "unforced_guide_mgr"
local controller_base = require "controller_base"
local windowMgr = require "ui_window_mgr"
local const_personalInfo = require "const_personalInfo"
local helper_personalInfo = require "helper_personalInfo"
local data_personalInfo = require "data_personalInfo"
local event_personalInfo = require "event_personalInfo"
local net_personalInfo = require "net_personalInfo"
local event = require "event"
local game_scheme = require "game_scheme"
local red_system = require "red_system"
local red_const = require "red_const"
local GWG = GWG
local cfg_util = require "cfg_util"
local tonumber =tonumber
local const = require "const"
local log = require "log"
local string = string
local tostring = tostring


module("ui_personalised_controller")
local controller = nil
local UIController = newClass("ui_personalised_controller", controller_base)

local isUnLockAnimalPage = false
--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)

    self:RegisterRedDot()

    self:ResetLabelList()
    self:CreateExpireTimer()
    
    if data then
        self.labelIndex = data.personalisedTag
        self.labelPreviewValues[data.personalisedTag] = data.goodsId or self.labelPreviewValues[data.personalisedTag]
        local custom_avatar_data = require "custom_avatar_data"
        if custom_avatar_data.GetMyAvatarUsed() then
            self.labelPreviewValues[const_personalInfo.PersonalisedTag.face] = custom_avatar_data.Custom_Config_Id
        end
    else
        self.labelIndex = 1
    end
    self:SwitchLabel(self.labelIndex)
    self:SetPlateInfo()
    self:IsOpenEffect()
end

local Person

function UIController:RegisterRedDot()
    red_system.RegisterRedFunc(red_const.Enum.PersonalisedPerTag, function(enumType) 
        return self:GetPersonalisedTypeRedDot(enumType) 
    end)
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    self.__base.Close(self)
    self.cdTimer = nil
    self.onClickItem = nil
    self.onUpdateGoodsCache = nil
end

function UIController:AutoSubscribeEvents()
    self.onClickItem = function(data, root)
        self:OnClickItem(data, root)
    end

    self.onUpdateGoodsCache = function()
        self:ResetLabelList()
    end
    self:RegisterEvent(event_personalInfo.UPDATE_CACHE_GOODS, self.onUpdateGoodsCache)
    
    self.onUpdateCustomAvatarStatus = function()
        local custom_avatar_data = require "custom_avatar_data"
        local data = helper_personalInfo.GetFaceItemConfig(custom_avatar_data.Custom_Config_Id)
        if not data then
            return
        end
        self:TriggerUIEvent("UpdateScrollListByCustomAvatar", self.labelIndex, data, data.customAvatarData)
    end
    self:RegisterEvent(event_personalInfo.UPDATE_CUSTOM_AVATAR_STATUS, self.onUpdateCustomAvatarStatus)

    self.onRemoveCustomAvatar = function()
        local custom_avatar_data = require "custom_avatar_data"
        local data = helper_personalInfo.GetFaceItemConfig(custom_avatar_data.Custom_Config_Id)
        if not data then
            return
        end
        local clickSystemAvatar = false
        if self.labelValues[const_personalInfo.PersonalisedTag.face] ~= data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FaceID)  then
            clickSystemAvatar = true
        end
        data.clickSystemAvatar = clickSystemAvatar
        self:TriggerUIEvent("UpdateScrollListByRemoveCustomAvatar", self.labelIndex, data, data.customAvatarData )
    end
    self:RegisterEvent(event_personalInfo.CUSTOM_AVATAR_REMOVE, self.onRemoveCustomAvatar)

    self.onVerifyCustomAvatar = function()
        --检查是否有通过的自定义头像
        local custom_avatar_data = require "custom_avatar_data"
        local custom_avatar_mgr = require "custom_avatar_mgr"
        local data = custom_avatar_data.GetMyAvatar()
        if data ~= nil then
            if data.status == const.Custom_Image_Enum.Reviewed or data.status == const.Custom_Image_Enum.MachineReviewedPass then
                --帮他使用这个新审核通过的头像
                custom_avatar_data.SetAvatarSystemClick(true)
                self:TriggerUIEvent("ClickScrollListByCustomAvatar", self.labelIndex, data)
            else
                --审核不通过，提示下，然后删除数据，刷新显示
                local flow_text = require "flow_text"
                local lang = require "lang"
                flow_text.Add(lang.Get(650091))
                custom_avatar_mgr.DelImage(data.imageId)
                custom_avatar_data.RemoveRejectedAvatar(data.imageId)
                self:TriggerUIEvent("UpdateScrollListByCustomAvatar", self.labelIndex, data, data.customAvatarData)
            end
        end
    end
    self:RegisterEvent(event_personalInfo.CUSTOM_AVATAR_VERIFY, self.onVerifyCustomAvatar)
end

function UIController:AutoUnsubscribeEvents()
    self.onUpdateGoodsCache = nil
    self.onRemoveCustomAvatar = nil
    self.onUpdateCustomAvatarStatus = nil
end

--region 功能逻辑
function UIController:SetLabelList(key, propEnum, configArrayFunc)
    self.labelValues[key] = data_personalInfo.GetPersonalInfoValue(propEnum)
    self.labelPreviewValues[key] = data_personalInfo.GetPersonalInfoValue(propEnum)
    local array = configArrayFunc()
    self:TriggerUIEvent("BindLabelList", key, array, self.onClickItem)
end

function UIController:GetPersonalisedTypeRedDot(enumType)
    if enumType == const_personalInfo.PersonalisedTag.namePlate then
        return helper_personalInfo.GetPersonalisedPageRedNum(enumType)
    end
    return 0
end

function UIController:OnClickItem(data, root)
    if not data.unlocked then
        self:OnShowPersonalisedTip(data.id, root)
    end
    self.labelPreviewValues[self.labelIndex] = data.id
    
    if self.labelIndex == const_personalInfo.PersonalisedTag.face then
        local faceItemData = helper_personalInfo.GetFaceItemConfig(self.labelPreviewValues[const_personalInfo.PersonalisedTag.face])
        if not faceItemData then
            return
        end
        local custom_avatar_data = require "custom_avatar_data"
        custom_avatar_data.SetMyAvatarUsed(faceItemData.id < custom_avatar_data.Custom_Config_MAX_ID)
    end
    
    self:SwitchLabel(self.labelIndex)
end

function UIController:CreateExpireTimer()
    if not self.cdTimer then
        self.cdTimer = self:CreateTimer(1,
                function()
                    if self.expireTimeUnlock then
                        self:UpdateExpireTimer()
                    end
                end)
    end
end

function UIController:UpdateExpireTimer()
    if self.expireTime and self.expireTime > 0 then
        self:TriggerUIEvent("UpdateExpireTime", self.expireTime)
    else
        self:TriggerUIEvent("UpdateExpireTime", 0)
    end
end

function UIController:SetExpireTimer(data)
    self.expireTimeUnlock = data and data.unlocked or false
    self.expireTime = data and (data.expireTime or 0) or 0
end

function UIController:IsOpenAnimal() 
    local functionOpenExterior = game_scheme:FunctionOpen_0(1203)
    local buildInfoExterior = cfg_util.StringToArray(functionOpenExterior.iBuildingID, "#")
    local buildingInfoExterior = GWG.GWHomeMgr.buildingData.GetMaxLevelBuildingDataByBuildingID(tonumber(buildInfoExterior[1]))
    if buildInfoExterior and buildInfoExterior[2] and buildingInfoExterior and buildingInfoExterior.nLevel then 
        isUnLockAnimalPage =buildingInfoExterior.nLevel >= tonumber(buildInfoExterior[2])
    end
    self:TriggerUIEvent("SetAnimalPageActive",isUnLockAnimalPage)
end

---@public 是否开启特效装扮页签
function UIController:IsOpenEffect()
    local net_module_open = require "net_module_open"
    local moduleOpenPro_pb = require "moduleOpenPro_pb"
    local moduleOpen = net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_SchlossEffect)
    self:TriggerUIEvent("SetEffectPageActive", moduleOpen)
end

function UIController:OnShowPersonalisedTip(id, root)
    local util = require "util"
    util.DelayCallOnce(0.1, function()
            local tipData = {
            id = id,
            type = self.labelIndex,
            position = root.transform.position,
            sizeDelta = root.sizeDelta,
        }

        windowMgr:ShowModule("ui_personalised_tip", nil, nil, tipData)
    end)

end

function UIController:SwitchLabel(index)
    if index == const_personalInfo.PersonalisedTag.schloss then
        self:OnTogSchlossValueChanged(true)
    elseif index == const_personalInfo.PersonalisedTag.namePlate then
        self:OnTogNamePlateValueChanged(true)
    elseif index == const_personalInfo.PersonalisedTag.frame then
        self:OnTogFrameValueChanged(true)
    elseif index == const_personalInfo.PersonalisedTag.title then
        self:OnTogTitleValueChanged(true)
    elseif index == const_personalInfo.PersonalisedTag.MythicalAnimals then 
        self:OnTogMythicalAniValueChanged(true)
    elseif index == const_personalInfo.PersonalisedTag.effect then
        self:OnTogEffectValueChanged(true)
    else
        self:OnTogHeadValueChanged(true)
    end
end

function UIController:ResetLabelList()
    -- 初始化 Label
    self.labelValues = {}
    self.labelPreviewValues = {}
    self.expireTime = 0
    -- 初始化 Label
    self:IsOpenAnimal() 
    self:SetLabelList(const_personalInfo.PersonalisedTag.face, data_personalInfo.PropEnum.FaceID, helper_personalInfo.GetFaceConfigArray)
    self:SetLabelList(const_personalInfo.PersonalisedTag.schloss, data_personalInfo.PropEnum.CityID, helper_personalInfo.GetSchlossConfigArray)
    self:SetLabelList(const_personalInfo.PersonalisedTag.namePlate, data_personalInfo.PropEnum.NamePlateID, helper_personalInfo.GetNamePlateConfigArray)
    self:SetLabelList(const_personalInfo.PersonalisedTag.frame, data_personalInfo.PropEnum.FrameID, helper_personalInfo.GetFrameConfigArray)
    self:SetLabelList(const_personalInfo.PersonalisedTag.MythicalAnimals, data_personalInfo.PropEnum.AnimalsID, helper_personalInfo.GetAnimalConfigArray)
    self:SetLabelList(const_personalInfo.PersonalisedTag.effect, data_personalInfo.PropEnum.EffectID, helper_personalInfo.GetEffectConfigArray)
    --self:SetLabelList(const_personalInfo.PersonalisedTag.title, data_personalInfo.PropEnum.TitleID, helper_personalInfo.GetTitleConfigArray)
end

function UIController:SetPageChecked(tag)
    --helper_personalInfo.SetPageItemsChecked(tag)
    helper_personalInfo.SetPageChecked(tag)
    red_system.TriggerRed(red_const.Enum.PersonalisedPerTag)
end

function UIController:OnTogHeadValueChanged(state)
    local tag = const_personalInfo.PersonalisedTag.face
    if state then

        self:SetPageChecked(const_personalInfo.PersonalisedTag.face)

        self.labelIndex = const_personalInfo.PersonalisedTag.face
        local data = helper_personalInfo.GetFaceItemConfig(self.labelPreviewValues[const_personalInfo.PersonalisedTag.face])
        if not data then
            return
        end
        if data.unlocked then
            if self.labelValues[const_personalInfo.PersonalisedTag.face] ~= data.id then
                event.EventReport("PlayerInfo_ChangeAvatar", {})
            end
            self.labelValues[const_personalInfo.PersonalisedTag.face] = data.id
        end
        self:SetExpireTimer(data)
        self:UpdateExpireTimer()
        local custom_avatar_data = require "custom_avatar_data"
        if data.id < custom_avatar_data.Custom_Config_MAX_ID then
            self:TriggerUIEvent("UpdateFaceByCustomAvatar", self.labelIndex, data, data.customAvatarData, self.labelValues[const_personalInfo.PersonalisedTag.face], false)
        else
            self:TriggerUIEvent("SwitchFaceLabel", self.labelIndex, data, data.id, self.labelValues[const_personalInfo.PersonalisedTag.face], false)
        end

    else
        helper_personalInfo.SetPageItemsChecked(tag)
        self.labelPreviewValues[const_personalInfo.PersonalisedTag.face] = self.labelValues[const_personalInfo.PersonalisedTag.face]

        local stepID = unforced_guide_mgr.GetCurStep()
        if unforced_guide_mgr.GetCurGuide() == 25  and (stepID == 30)then
            unforced_guide_mgr.CloseGuide()
        end
    end
end

function UIController:OnTogSchlossValueChanged(state)
    local tag = const_personalInfo.PersonalisedTag.schloss
    if state then
        self:SetPageChecked(const_personalInfo.PersonalisedTag.schloss)
        self.labelIndex = const_personalInfo.PersonalisedTag.schloss
        local data = helper_personalInfo.GetSchlossItemConfig(self.labelPreviewValues[const_personalInfo.PersonalisedTag.schloss])
        if not data then
            return
        end
        if data.unlocked then
            self.labelValues[const_personalInfo.PersonalisedTag.schloss] = data.id
        end
        self:SetExpireTimer(data)
        self:UpdateExpireTimer()
        self:TriggerUIEvent("SwitchSchlossLabel", self.labelIndex, data, helper_personalInfo.GetSchlossPrefabPath(helper_personalInfo.SchlossPrefabType.personalised, data.id))

        --还要取铭牌数据
        local data_plate = helper_personalInfo.GetNamePlateItemConfig(self.labelPreviewValues[const_personalInfo.PersonalisedTag.namePlate])
        if not data_plate then
            return
        end
        if data_plate.unlocked then
            self.labelValues[const_personalInfo.PersonalisedTag.namePlate] = data_plate.id
        end

        self:SetPlateInfo(data_plate)
    else
        helper_personalInfo.SetPageItemsChecked(tag)
        self.labelPreviewValues[const_personalInfo.PersonalisedTag.schloss] = self.labelValues[const_personalInfo.PersonalisedTag.schloss]
    end
end

function UIController:OnTogNamePlateValueChanged(state)
    local tag = const_personalInfo.PersonalisedTag.namePlate
    if state then
        self:SetPageChecked(const_personalInfo.PersonalisedTag.namePlate)
        self.labelIndex = const_personalInfo.PersonalisedTag.namePlate
        local data = helper_personalInfo.GetNamePlateItemConfig(self.labelPreviewValues[const_personalInfo.PersonalisedTag.namePlate])
        if not data then
            return
        end
        if data.unlocked then
            self.labelValues[const_personalInfo.PersonalisedTag.namePlate] = data.id
        end

        --还要取城堡数据
        local data_schloss = helper_personalInfo.GetSchlossItemConfig(self.labelPreviewValues[const_personalInfo.PersonalisedTag.schloss])
        if not data_schloss then
            return
        end
        if data_schloss.unlocked then
            self.labelValues[const_personalInfo.PersonalisedTag.schloss] = data_schloss.id
        end


        self:SetExpireTimer(data)
        self:UpdateExpireTimer()
        self:TriggerUIEvent("SwitchNamePlateLabel", self.labelIndex, data, helper_personalInfo.GetSchlossPrefabPath(helper_personalInfo.SchlossPrefabType.personalised, data_schloss.id))

        self:SetPlateInfo(data)
    else
        helper_personalInfo.SetPageItemsChecked(tag)
        self.labelPreviewValues[const_personalInfo.PersonalisedTag.namePlate] = self.labelValues[const_personalInfo.PersonalisedTag.namePlate]
    end
end

function UIController:SetPlateInfo(data)
    if not data then
        data = helper_personalInfo.GetNamePlateItemConfig(self.labelValues[const_personalInfo.PersonalisedTag.namePlate])
        if not data then
            return
        end
    end

    local roleName = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleName)
    local roleLevel = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleLevel)
    local paddingSetting = const_personalInfo.NamePlatePadding[data.iconId] or {left = 10, right = 10, top = 0, bottom = 0}
    self:TriggerUIEvent("SetPlateInfo", roleName, data.iconId, roleLevel, paddingSetting)
end

function UIController:OnTogFrameValueChanged(state)
    if state then
        self:SetPageChecked(const_personalInfo.PersonalisedTag.frame)
        self.labelIndex = const_personalInfo.PersonalisedTag.frame
        local data = helper_personalInfo.GetFrameItemConfig(self.labelPreviewValues[const_personalInfo.PersonalisedTag.frame])
        if not data then
            return
        end
        if data.unlocked then
            self.labelValues[const_personalInfo.PersonalisedTag.frame] = data.id
        end
        self:SetExpireTimer(data)
        self:UpdateExpireTimer()
        --适配faceID 2025.4.2 将faceID 转换为 faceStr
        local custom_avatar_data = require "custom_avatar_data"
        local customHeadData = custom_avatar_data.GetMyAvatar()
        local faceStr = self.labelValues[const_personalInfo.PersonalisedTag.face]
        if customHeadData then 
            faceStr = customHeadData.remoteUrl
        end
        self:TriggerUIEvent("SwitchFaceLabel", self.labelIndex, data, faceStr, data.id, true)
    else
        self.labelPreviewValues[const_personalInfo.PersonalisedTag.frame] = self.labelValues[const_personalInfo.PersonalisedTag.frame]
    end
end

function UIController:OnTogTitleValueChanged(state)
    if state then
        self:SetPageChecked(const_personalInfo.PersonalisedTag.title)
        self.labelIndex = const_personalInfo.PersonalisedTag.title
        local data = helper_personalInfo.GetTitleItemConfig(self.labelPreviewValues[const_personalInfo.PersonalisedTag.title])
        if not data then
            return
        end
        if data.unlocked then
            self.labelValues[const_personalInfo.PersonalisedTag.title] = data.id
        end
        self:SetExpireTimer(data)
        self:UpdateExpireTimer()
        self:TriggerUIEvent("SwitchTitleLabel", self.labelIndex, data)
    else
        self.labelPreviewValues[const_personalInfo.PersonalisedTag.title] = self.labelValues[const_personalInfo.PersonalisedTag.title]
    end
end

function UIController:OnTogMythicalAniValueChanged(state)
    if state then
        self:SetPageChecked(const_personalInfo.PersonalisedTag.MythicalAnimals)
        self.labelIndex = const_personalInfo.PersonalisedTag.MythicalAnimals
        local data = helper_personalInfo.GetAnimalItemConfig(self.labelPreviewValues[const_personalInfo.PersonalisedTag.MythicalAnimals],true)
        if not data then
            return
        end
        if data.unlocked then
            self.labelValues[const_personalInfo.PersonalisedTag.MythicalAnimals] = data.id
        end
        --设置限时
        self:SetExpireTimer(data)
        self:UpdateExpireTimer()
        self:TriggerUIEvent("SwitchAnimalLabel", self.labelIndex, data, helper_personalInfo.GetAnimalPrefabPath(data.id))
    else
        self.labelPreviewValues[const_personalInfo.PersonalisedTag.MythicalAnimals] = self.labelValues[const_personalInfo.PersonalisedTag.MythicalAnimals]
    end
end

function UIController:OnTogEffectValueChanged(state)
    if state then
        self:SetPageChecked(const_personalInfo.PersonalisedTag.effect)
        self.labelIndex = const_personalInfo.PersonalisedTag.effect
        local data = helper_personalInfo.GetSchlossEffectItemConfig(self.labelPreviewValues[const_personalInfo.PersonalisedTag.effect], false)
        if not data then
            return
        end
        if data.unlocked then
            self.labelValues[const_personalInfo.PersonalisedTag.effect] = data.id
        end
        --设置限时
        self:SetExpireTimer(data)
        self:UpdateExpireTimer()
        --家园默认城堡装扮id
        local selectSchlossId = self.labelValues[const_personalInfo.PersonalisedTag.schloss]
        local schlossPath = helper_personalInfo.GetSchlossPrefabPath(helper_personalInfo.SchlossPrefabType.personalised, selectSchlossId)
        --读取特效装扮
        local effectPath, _id, effectScale, effectPos,effectBottomPath = helper_personalInfo.GetSchlossEffectPrefabPath(helper_personalInfo.SchlossPrefabType.personalised,data.id)
        local effectParams = {
            effectPath = effectPath,
            effectScale = effectScale,
            effectRotation = { x = -5, y = 0, z = 0 },
            effectPos = effectPos,
        }
        local effectBottomParams = {
            effectPath = effectBottomPath,
            effectScale = effectScale,
            effectRotation = { x = -5, y = 0, z = 0 },
            effectPos = effectPos,
        }
        self:TriggerUIEvent("SwitchSchlossLabel", self.labelIndex, data, schlossPath, effectParams,effectBottomParams)
        --self:TriggerUIEvent("SwitchEffectLabel", self.labelIndex, data, effectId)
    else
        self.labelPreviewValues[const_personalInfo.PersonalisedTag.effect] = self.labelValues[const_personalInfo.PersonalisedTag.effect]
    end
end

function UIController:OnBtnOverviewEvent()
    local table = {
        FaceID = self.labelValues[const_personalInfo.PersonalisedTag.face],
        CityID = self.labelValues[const_personalInfo.PersonalisedTag.schloss],
        FrameID = self.labelValues[const_personalInfo.PersonalisedTag.frame],
        TitleID = self.labelValues[const_personalInfo.PersonalisedTag.title],
        AnimalID = self.labelValues[const_personalInfo.PersonalisedTag.MythicalAnimals],
        EffectID = self.labelValues[const_personalInfo.PersonalisedTag.effect],
        NamePlateID = self.labelValues[const_personalInfo.PersonalisedTag.namePlate],
    }
    windowMgr:ShowModule("ui_attribute_overview", nil, nil, table)
end

function UIController:OnCloseBtnClick()

    local custom_avatar_data = require "custom_avatar_data"
    --判断是否选择了自定义头像
    local myAvatar = custom_avatar_data.GetMyAvatar()
    local isDirty = custom_avatar_data.GetIsDirty()
    if self.labelValues[const_personalInfo.PersonalisedTag.face] ~= data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FaceID) or isDirty then
        if myAvatar and myAvatar.used then
            net_personalInfo.MSG_CUSTOM_FACE_USE_REQ(myAvatar.imageId,1)
        else
            --如果没有值变化，不发送请求，直接trigger event
            if self.labelValues[const_personalInfo.PersonalisedTag.face] ~= data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FaceID)  then
                net_personalInfo.MSG_ZONE_ROLE_FACE_UPDATE_REQ(self.labelValues[const_personalInfo.PersonalisedTag.face])
            end
        end
        custom_avatar_data.SetIsDirty(false)
    end
    custom_avatar_data.SetAvatarSystemClick(false)
    
    if self.labelValues[const_personalInfo.PersonalisedTag.schloss] ~= data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.CityID) then
        net_personalInfo.MSG_ZONE_ROLE_CITY_UPDATE_REQ(self.labelValues[const_personalInfo.PersonalisedTag.schloss])
    end

    log.Warning(string.format("cur name plate = %s",
    tostring(self.labelValues[const_personalInfo.PersonalisedTag.namePlate])))
    if self.labelValues[const_personalInfo.PersonalisedTag.namePlate] ~= data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.NamePlateID) then
        net_personalInfo.MSG_ZONE_ROLE_PLATE_UPDATE_REQ(self.labelValues[const_personalInfo.PersonalisedTag.namePlate])
    end

    if self.labelValues[const_personalInfo.PersonalisedTag.frame] ~= data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FrameID) then
        net_personalInfo.MSG_FRAME_SELECT_REQ(self.labelValues[const_personalInfo.PersonalisedTag.frame])
    end
    if self.labelValues[const_personalInfo.PersonalisedTag.MythicalAnimals] ~= data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.AnimalsID) then
        local droneID = GWG.GWHomeMgr.droneData.GetDroneId()
        event.Trigger(event.GAME_EVENT_REPORT,"AccessoryAdorn_Use",{adornID = self.labelValues[const_personalInfo.PersonalisedTag.MythicalAnimals],use_type = 2})
        net_personalInfo.MSG_DRONECENTER_ADORN_REQ(droneID,self.labelValues[const_personalInfo.PersonalisedTag.MythicalAnimals])
    end
    if self.labelValues[const_personalInfo.PersonalisedTag.title] ~= data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.TitleID) then
        net_personalInfo.MSG_TITLE_SELECT_REQ(self.labelValues[const_personalInfo.PersonalisedTag.title])
    end
    if self.labelValues[const_personalInfo.PersonalisedTag.effect] ~= data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.EffectID) then
        net_personalInfo.MSG_ZONE_ROLE_CITY_EFFECT_UPDATE_REQ(self.labelValues[const_personalInfo.PersonalisedTag.effect])
    end
    windowMgr:UnloadModule(self.view_name)
end

---********************end功能函数区**********---
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
