﻿---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2025/9/5 10:17
--- desc : 玩家模块
local require = require
local os = require "os"
local login_pb = require "login_pb"
local msg_pb = require "msg_pb"
local virtual_net = require "virtual_net"
local prop_pb = require "prop_pb"
local topic_pb = require "topic_pb"
local moduleOpenPro_pb = require "moduleOpenPro_pb"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
module("virtual_player_module")
local constRoleId = 000001
local constLevel = 1
local NoviceEventValue = 2
local NoviceEventValueKey = "NoviceEventValueKey"
function OnVirtualServerStarted()
    --DeleteNoviceEventValue()
    --玩家信息
    MSG_LOGIN_ACTOR_INFO_NTF()
    --创建玩家
    MSG_PROP_CREATEENTITY_NTF()
    --读取玩家数据
    GetNoviceEventValue()
    MSG_PROP_UPDATE_TOPICS_NTF()
    --
    local virtual_main_level_module = require "virtual_main_level_module"
    virtual_main_level_module.PlayerInit()
    MSG_MODULEOPEN_UPDATE_NTF();
    MSG_PLAYER_LOGIN_FINISH_NOTIFY()
end

function OnVirtualServerStoped()

end

---@public 玩家信息
function MSG_LOGIN_ACTOR_INFO_NTF()
    local actorInfoNtf = login_pb.TMSG_LOGIN_ACTOR_INFO_NTF()
    local a = actorInfoNtf.actor:add()
    a.areaID = 5
    a.profession = 0
    a.sex = 0
    a.faceId = 9211
    a.level = constLevel
    a.BCUserID = ""
    a.roleID = constRoleId
    a.name = "Traveler"
    a.faceStr = "9211"
    a.userID = constRoleId
    a.nation = 0
    virtual_net.SendMessageToClient(msg_pb.MSG_LOGIN_ACTOR_INFO_NTF, actorInfoNtf)
end

---@public 创建玩家实体数据
function MSG_PROP_CREATEENTITY_NTF()
    --创建玩家
    local createEntityNtf = prop_pb.TMSG_PROP_CREATEENTITY_NTF()
    createEntityNtf.name = "Traveler"
    createEntityNtf.roleCreateTime = os.server_time()
    createEntityNtf.openSvrTime = os.server_time()
    --玩家等级
    local lvInfo = createEntityNtf.prop:add()
    lvInfo.propid = prop_pb.PERSON_PROP_LV
    lvInfo.propvalue = constLevel
    --玩家id
    local roleInfo = createEntityNtf.prop:add()
    roleInfo.propid = prop_pb.PERSON_PROP_PDBID
    roleInfo.propvalue = constRoleId
    --建筑数据
    local virtual_home_build_module = require "virtual_home_build_module"
    local buildData = virtual_home_build_module.MSG_PROP_CREATEENTITY_NTF()
    local buildInfo = createEntityNtf.partData:add()
    buildInfo.id = prop_pb.PERSONPART_CITY
    buildInfo.data = buildData
    --引导数据
    local guideInfo = createEntityNtf.partData:add()
    guideInfo.id = prop_pb.PERSONPART_SUBJECT
    local guideMsg = prop_pb.TSubjectPart()
    guideInfo.data = guideMsg:SerializeToString()
    virtual_net.SendMessageToClient(msg_pb.MSG_PROP_CREATEENTITY_NTF, createEntityNtf)
end

---@public 玩家登录完成
function MSG_PLAYER_LOGIN_FINISH_NOTIFY()
    local ntf = prop_pb.TMSG_PLAYER_LOGIN_FINISH_NOTIFY()
    virtual_net.SendMessageToClient(msg_pb.MSG_PLAYER_LOGIN_FINISH_NOTIFY, ntf)
end

---@public 挑战回复
function MSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ(msg)
    if not msg then
        return
    end
    local hookValue = msg.hookLevel + 1
    SetNoviceEventValue(hookValue)
    MSG_PROP_UPDATE_TOPICS_NTF()
end

function MSG_PROP_UPDATE_TOPICS_NTF()
    local topicNtf = prop_pb.TMSG_PROP_UPDATE_TOPICS_NTF()
    -- 添加第1个 TopicData
    local t1 = topicNtf.topicdata:add()
    t1.topicName = topic_pb.TOPICNAME_NEWPLAYERACT_DATA
    t1.topicKey = 3
    t1.value = NoviceEventValue
    virtual_net.SendMessageToClient(msg_pb.MSG_PROP_UPDATE_TOPICS_NTF, topicNtf)
end

---@public 模块开启
function MSG_MODULEOPEN_UPDATE_NTF()
    local msg = moduleOpenPro_pb.TMSG_MODULEOPEN_UPDATE_NTF()
    virtual_net.SendMessageToClient(msg_pb.MSG_MODULEOPEN_UPDATE_NTF, msg)
end

function SetNoviceEventValue(value)
    --缓存本地
    NoviceEventValue = value
    -- 调用 Unity 的 PlayerPrefs（如果你能直接访问 CS namespace）
    PlayerPrefs.SetInt(NoviceEventValueKey, NoviceEventValue)
    PlayerPrefs.Save()
end

function GetNoviceEventValue()
    NoviceEventValue = PlayerPrefs.GetInt(NoviceEventValueKey, 2)
end

function DeleteNoviceEventValue()
    PlayerPrefs.DeleteKey(NoviceEventValueKey)
end