local require = require
local util = require "util"
local table = table
local debug = debug
local CS = CS
local BgDownloadMgr = CS.War.Base.BgDownloadMgr
local SplitServerHttpWebRequest = CS.War.Res.SplitServerHttpWebRequest
local SplitServerUnityWebRequest = CS.War.Res.SplitServerUnityWebRequest
local SplitServerPathMgr = CS.War.Res.SplitServerPathMgr
local Time = CS.UnityEngine.Time
local xpcall = xpcall
local split_define = require "split_server_res_define"
local split_server_res_download_mgr = require"split_server_res_download_mgr"
local Warning = split_define.logger.Warning
local DownloadErrorCode = split_define.DownloadErrorCode
local FileDownloadWay = split_define.FileDownloadWay
local DownloadTimeout = split_define.DownloadTimeout
local defaultFileDownloadWay = FileDownloadWay.HttpWebRequest

module("split_server_res_download_way_chooser")
local poolMaxSize = 10
local cache = {}

function InitBgDownloadMgr()
    if cache.isInitBgDownloadMgr then
        return
    end
    cache.isInitBgDownloadMgr = true
    BgDownloadMgr.Instance:Start()
    BgDownloadMgr.Instance:RegisterDownloadErrorHandler(OnDownloadError)
    BgDownloadMgr.Instance:RegisterDownloadFinishHandler(OnDownloadFinish)
    BgDownloadMgr.Instance:RegisterDownloadCancleHandler(OnDownloadCancel)
end

function DownloadFile(task, hashRemote)
    task.downloadWay = task.downloadWay or defaultFileDownloadWay
    Warning(2, "DownloadWay", task.downloadWay, task.savePath)
    if task.downloadWay == FileDownloadWay.BgDownloadMgr then
        InitBgDownloadMgr()
        local isSaveCachingHash = false  --改名后统一Save
        task.timeoutCheckTime = Time.realtimeSinceStartup + DownloadTimeout.bgDownloadMgrTimeout
        local hashCheck = task.isCheckSum and hashRemote or nil
        BgDownloadMgr.Instance:RequestWithSavePath(task.savePath, task.abName,
                task.downloadUrl, hashCheck, isSaveCachingHash)
        
    elseif task.downloadWay == FileDownloadWay.HttpInst then 
        local http_inst = require "http_inst"
        local requestTimeout = DownloadTimeout.unityWebReqTimeout - 1 --Request超时时间比重试超时时间少一秒
        SplitServerPathMgr.CreateDirectory(task.savePath)
        task.timeoutCheckTime = Time.realtimeSinceStartup + DownloadTimeout.unityWebReqTimeout
        task.downloadRequest = http_inst.RequestDownloadFile(task.downloadUrl, task.savePath,
                requestTimeout, OnDownloadTaskFinishInHttpInst, task)
        
    elseif task.downloadWay == FileDownloadWay.HttpWebRequest or
            task.downloadWay == FileDownloadWay.UnityWebRequest then
        DownloadFileInNewWay(task)
        
    else
        Warning(0, "downloadWay error", task.downloadWay)
    end
end

function DownloadFileInNewWay(task)
    cache.taskPool = cache.taskPool or {}
    if #cache.taskPool < 1 then
        task.downloadRequest = CreateRequestIns(task.downloadWay)
    else
        task.downloadRequest = table.remove(cache.taskPool, #cache.taskPool)
        if util.IsObjNull(task.downloadRequest) then
            task.downloadRequest = CreateRequestIns(task.downloadWay)
        end
    end

    task.timeoutCheckTime = Time.realtimeSinceStartup + DownloadTimeout.newWayTimeout
    task.downloadRequest:Download(task.taskFileInfo, function(isSuccess)
        local request = task.downloadRequest
        if util.IsObjNull(request) then
            OnDownloadError(task.savePath, task.downloadUrl, DownloadErrorCode.OtherError, "request is nil")
            return 
        end
        
        if isSuccess then
            OnDownloadFinish(task.savePath)
        else
            OnDownloadError(task.savePath, task.downloadUrl, request.ErrorCode, request.ErrorMsg)
        end
        request:Dispose()
        task.downloadRequest = nil
        if cache.taskPool and #cache.taskPool < poolMaxSize then
            table.insert(cache.taskPool, request)
        end
    end)
end

function CreateRequestIns(downloadWay)
    if downloadWay == FileDownloadWay.HttpWebRequest then
        return SplitServerHttpWebRequest()
    else
        return SplitServerUnityWebRequest()
    end
end

--http_inst的UnityWebReq下载成功回调
function OnDownloadTaskFinishInHttpInst(result, url, savePath, task)
    Warning(2, "OnDownloadTaskFinishInHttpInst", result, savePath)
    if result == 1 then
        if task.isCheckSum then
            split_server_res_download_mgr.RemoveProcessingTask(task.savePath)
            split_server_res_download_mgr.CheckFileSum(task)
        else
            OnDownloadFinish(savePath)
        end
    else
        OnDownloadError(savePath, url, DownloadErrorCode.HttpError, "HttpError")
    end
end

function OnDownloadError(savePath, url, errorCode, errorDes)
    Warning(2, "OnDownloadError", savePath, errorCode, errorDes)
    local task = split_server_res_download_mgr.GetProcessingTask(savePath)
    if task then  --下载失败切换方式为UnityWebRequest
        task.downloadWay = FileDownloadWay.UnityWebRequest
    end
    split_server_res_download_mgr.OnProcessTaskError(savePath, url, errorCode, errorDes)
end

function OnDownloadFinish(savePath)
    Warning(2, "OnDownloadFinish", savePath)
    split_server_res_download_mgr.OnProcessTaskFinish(savePath) 
end

function OnDownloadCancel(savePath)
    Warning(2, "OnDownloadCancel", savePath)
    split_server_res_download_mgr.OnProcessTaskCancel(savePath)
end

function CancelDownloadOnTimeout(task)
    if task.downloadWay == FileDownloadWay.BgDownloadMgr then
        BgDownloadMgr.Instance:CancleTask(task.savePath)
        --CancleTask内有调用OnDownloadTaskError
        --OnDownloadError(task.savePath, task.downloadUrl, DownloadErrorCode.Timeout, "检测超时，主动取消任务")
        
    elseif task.downloadWay == FileDownloadWay.HttpInst or 
            task.downloadWay == FileDownloadWay.HttpWebRequest or
            task.downloadWay == FileDownloadWay.UnityWebRequest then
        if task.downloadRequest then
            task.downloadRequest:Dispose()
            task.downloadRequest = nil
        end
        OnDownloadError(task.savePath, task.downloadUrl, DownloadErrorCode.Timeout, "检测超时，主动取消任务")
    end
end

function Dispose()
    cache.taskPool = nil
    if not cache.isInitBgDownloadMgr then
        return
    end

    cache.isInitBgDownloadMgr = false
    BgDownloadMgr.Instance:UnRegisterDownloadErrorHandler(OnDownloadError)
    BgDownloadMgr.Instance:UnRegisterDownloadFinishHandler(OnDownloadFinish)
    BgDownloadMgr.Instance:UnRegisterDownloadCancleHandler(OnDownloadCancel)
    local f,res = xpcall(function ()
        if BgDownloadMgr.ClearHttpSocketRepeatPool ~= nil then
            BgDownloadMgr.ClearHttpSocketRepeatPool()
        end
    end,debug.traceback)
    if not f then
        CS.UnityEngine.Debug.LogError(res)
    end
end
