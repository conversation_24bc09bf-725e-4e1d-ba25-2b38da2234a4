local tostring = tostring
local data_personalInfo = require "data_personalInfo"
local string = string
local json = require "dkjson"
local tonumber = tonumber

local lang = require "lang"
local game_scheme = require "game_scheme"
local halloween_activity_slot_machine_rate_data = require "halloween_activity_slot_machine_rate_data"
local halloween_activity_slot_machine_const = require "halloween_activity_slot_machine_const"

local halloween_activity_slot_machine_data_helper = {}


function halloween_activity_slot_machine_data_helper.GetRoleKey(key)
    local roleID = tostring(data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID))
    return string.format("%s_%s", roleID, key)
end

function halloween_activity_slot_machine_data_helper.GetJsonStr(data)
    return json.encode(data)
end

function halloween_activity_slot_machine_data_helper.DecodeJsonStr(jsonStr)
    return json.decode(jsonStr)
end

function halloween_activity_slot_machine_data_helper.GetActivityAllSlotGameCfg(activityID)
    local count = game_scheme:SlotGame_nums()
    local cfgs = {}
    for i = 0, count - 1 do
        local cfg = game_scheme:SlotGame(i)
        if cfg.AtyID == activityID then
            table.insert(cfgs, cfg)
        end
    end

    return cfgs
end

function halloween_activity_slot_machine_data_helper.GetSepreatedSlotGameCfgIntoSameCount(activityID)
    local cfgs = halloween_activity_slot_machine_data_helper.GetActivityAllSlotGameCfg(activityID)
    local countMap = {}
    for i = 1, #cfgs do
        local cfg = cfgs[i]
        local count = halloween_activity_slot_machine_data_helper.GetSlotGameCfgSameCount(cfg)
        countMap[count] = countMap[count] or {}
        table.insert(countMap[count], {
            cfg = cfg,
            index = i,
            rate = halloween_activity_slot_machine_rate_data.rate_dist[cfg.ID],
            count = 1,
        })
    end

    return countMap
end

function halloween_activity_slot_machine_data_helper.GetSlotGameCfgSameCount(cfg)
    local pattern_group_string = cfg.TaskID
    local pattern_group = string.split(pattern_group_string, "#")
    for i = 1, #pattern_group do
        pattern_group[i] = tonumber(pattern_group[i])
    end

    local countMap = {}
    
    -- 统计每个数字出现的次数（跳过0）
    for _, num in ipairs(pattern_group) do
        if num ~= 0 then
            countMap[num] = (countMap[num] or 0) + 1
        end
    end
    
    -- 找出最大的计数
    local maxCount = 0
    for num, count in pairs(countMap) do
        if count > maxCount then
            maxCount = count
        end
    end
    
    return maxCount
end

---@public 获取规则文字列表 现在暂不需要分组了
---@param help_id number
---@return string[]
function halloween_activity_slot_machine_data_helper.GetRuleGroupData(help_id)
    local help_id = halloween_activity_slot_machine_const.help_id
    local help_cfg = game_scheme:HelpTips_0(help_id)
    --配置数组数据[1]是一个从0开始的数组，[2]表示个数
    local help_lang_id_list = help_cfg.contentID[1]
    local count = help_cfg.contentID[2]
    local help_lang_str_list = {}
    for i=0, count-1 do
        -- 从1开始
        help_lang_str_list[i+1] = lang.Get(help_lang_id_list[i])
    end

    -- --解析分组
    -- local help_lang_group_list = {}
    -- local title_index_list = {}
    -- for i=1, count do
    --     if i == 1 then
    --         table.insert(title_index_list, i)
    --     else
    --         if help_lang_str_list[i]:find("\n") then
    --             table.insert(title_index_list, i)
    --         end
    --     end
    -- end

    return help_lang_str_list
end

return halloween_activity_slot_machine_data_helper