﻿---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/9/2 16:57
--- Desc: 事件模型
local LeanTween = CS.LeanTween
local require = require
local newclass = newclass
local GWG = GWG
local CS = CS
local typeof = typeof
local net_module_open = require "net_module_open"
local gw_sand_animator_helper = require "gw_sand_animator_helper"
local force_guide_system = require "force_guide_system"
local util = require "util"
local GWConst = require "gw_const"
local gw_home_grid_data = require "gw_home_grid_data"
local game_scheme = require "game_scheme"
local unit_base_object = require "unit_base_object"
local UIUtil = CS.Common_Util.UIUtil
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWEventModel : unit_base 事件基类
---@field __base UnitBaseObject
module("gw_home_comp_event_model")
local M = newclass("gw_home_comp_event_model", unit_base_object)
--- 构造器
function M:ctor()
    unit_base_object.ctor(self)
end
function M:InitData(eventId, cityMapId, parent, ResPath)
    local mapCfg = game_scheme:BuildMaincityMap_0(cityMapId)
    local eventCfg = game_scheme:BuildEvent_0(eventId)

    if not mapCfg then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject mapCfg = nil", cityMapId)
        return false
    end
    if not eventCfg then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg = nil", eventId)
        return false
    end
    if not ResPath then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg.ResPath = nil", eventId)
        return
    end

    self.mapCfg = mapCfg
    self.eventCfg = eventCfg
    self.eventType = eventCfg.type
    self:InstantiateModelAsync(ResPath, parent)
end

---@public 基类方法实例化模型
---@see override
function M:InstantiateModelAsync(path, parent)
    unit_base_object.InstantiateModelAsync(self, path, parent)
end

--- 实例化成功 （现必须基成设置名字和组件Id）
---@see override
function M:InstantiateSuccess(_obj)
    self:LoadModelPath(_obj)
    unit_base_object.InstantiateSuccess(self, _obj)
    if not self.eventCfg then
        return
    end
    --新手引导 单独处理
    local guideId = force_guide_system.GetCurStep()
    if self.eventCfg.EventID == 107 and guideId and guideId == 829 then
        self:SetActive(false)
    end
    local x, y, z = gw_home_grid_data.GetPosByGridXY(self.mapCfg.x, self.mapCfg.y)

    local resScale = self:GetEventResScale()

    if resScale == 0 then
        self:SetScale(1, true)
    else
        self:SetScale(resScale, true)
    end
    self:SetPosition(x + 0.5, y, z - 0.5)
    if self.eventType ~= GWConst.BuildEventType.Reward or self.eventCfg.ResType == 3 then
        self:SetRotation(0, self.mapCfg.angel, 0)
    else
        self.sortingGroup = UIUtil.GetComponent(_obj.transform, "SortingGroup")
        self:SetLayer(self.mapCfg.x, self.mapCfg.y)
    end
end

function M:GetEventResScale()
    if self.eventCfg then
        if self.eventCfg.moduleOpen and self.eventCfg.moduleOpen ~= 0 then
            local isOpen = net_module_open.CheckModuleOpen(self.eventCfg.moduleOpen)
            if isOpen then
                return self.eventCfg.ResScale2
            end
        end
        return self.eventCfg.ResScale
    end
    return 1
end


function M:LoadModelPath(_obj)
    if self.eventType == GWConst.BuildEventType.Reward then
        return
    end
    if util.IsObjNull(_obj) then
        return
    end
    --local gpuAnimate = UIUtil.GetComponent(_obj.transform, GpuAnimate)
    local gpuAnimate = GetGpuAnimate(_obj.gameObject)
    if util.IsObjNull(gpuAnimate) then
        local modelRes = UIUtil.GetTrans(_obj.transform, "modelRes")
        --local modelRes = UIUtil.GetComponent(_obj.transform, "Transform", "modelRes")
        if util.IsObjNull(modelRes) then
            return
        end
        local model = UIUtil.GetChild(modelRes, 1)
        if util.IsObjNull(model) then
            return
        end
        self._animator = gw_sand_animator_helper.new()
        self._animator:AddAnimator(model)
    else
        self._animator = gw_sand_animator_helper.new()
        self._animator:AddAnimator(_obj)
    end
    --self:UpdateValue(0)
end

function GetGpuAnimate(gameObject)
    local fboard_mgr = require("fboard_mgr")
    if fboard_mgr.IsOpen() then
        local ScriptConnector = CS.FBoard.ScriptConnector
        local connector = gameObject:GetComponent(typeof(ScriptConnector))
        if not util.IsObjNull(connector) then
            return connector.LogicObj
        end
        return nil
    else
        local GpuAnimate = CS.GPUAnimationBaker.Engine.GpuAnimatorBehaviour
        local animator = gameObject:GetComponent(typeof(GpuAnimate))
        return animator
    end
end

function M:PlayAnimator(state)
    if self._animator then
        self._animator:SetTrigger(state)
    end
end

function M:SetLayer(gridX, gridY)
    if gridX == nil or gridY == nil then
        gridX = self.serData.x
        gridY = self.serData.y
    end
    if self.mapCfg and self.mapCfg.layer and self.mapCfg.layer.count > 0 and self.mapCfg.layer.data[0]  == 6  then
        self.sortingGroup = UIUtil.GetOrAddComponent(self.transform, typeof(SortingGroup))
        --设置对应的层级
        if util.IsObjNull(self.sortingGroup) then
            return
        end
        --强制新增sortgroup
        if self.mapCfg.layer.count > 1 then
            self.sortingGroup.sortingOrder = self.mapCfg.layer.data[1]           
        else
            self.sortingGroup.sortingOrder = GWG.GWAdmin.HomeCommonUtil.GetCurSortingOrder(gridX, gridY)
        end
        return
    end
    --设置对应的层级
    if util.IsObjNull(self.sortingGroup) then
        return
    end
    self.sortingGroup.sortingOrder = GWG.GWAdmin.HomeCommonUtil.GetCurSortingOrder(gridX, gridY)
end
function M:ShowModel(value)
    if self.eventType == GWConst.BuildEventType.Reward then
        return
    end
    self:AddLoadEvent(function()
        self:PlayAnimator("Attack_Loop")
        if value then
            LeanTween.value(self.gameObject, function(value)
                self:UpdateValue(value)
            end, 1, 0, 1);
        end
        util.DelayCallOnce(1.05, function()
            if self.isDisposed then
                return
            end
            self:ShowStandTrigger()
        end)
    end)
end

function M:ShowStandTrigger()
    self:PlayAnimator("Stand")
end

function M:DeadModel(callBack, destroyCallBack)
    self:AddLoadEvent(function()
        local function timeOverFunc()
            self:StopTimer()
            if callBack then
                callBack()
            end
            if destroyCallBack then
                destroyCallBack()
            end
        end
        if self.eventType and self.eventType == GWConst.BuildEventType.Reward then
            timeOverFunc()
            return
        end
        self:PlayAnimator("Dead")
        self:StopTimer()
        LeanTween.cancel(self.gameObject)
        if self.LeanTimeTicker then
            util.RemoveDelayCall(self.LeanTimeTicker)
            self.LeanTimeTicker = nil
        end
        --创建定时器 倒计时进行气泡生产
        self.LeanTimeTicker = util.DelayCallOnce(1, function()
            if self.isDisposed then
                return
            end
            LeanTween.value(self.gameObject, function(value)
                self:UpdateValue(value)
            end, 0, 1, 1);
        end)
        self.TimeTicker = util.DelayCallOnce(2, function()
            if self.isDisposed then
                return
            end
            timeOverFunc()
        end)
    end)
end

---@public 新手剧情表演
function M:NovicePreform()
    self:AddLoadEvent(function()
        self:SetActive(true)
        self:PlayAnimator("Born")
        --self:PlayAnimator("Ability")
        util.DelayCallOnce(3, function()
            if self.isDisposed then
                return
            end
            self:PlayAnimator("Stand")
        end)
    end)
end

function M:endTimer()
    if self.novicePreformTimer then
        util.RemoveDelayCall(self.novicePreformTimer)
        self.novicePreformTimer = nil
    end
end

function M:SetVisible(show)
    self:AddLoadEvent(function()
        if show == nil then
            show = false
        end
        UIUtil.SetActive(self.gameObject, show)
    end)
end

function M:UpdateValue(value)
    if self._animator then
        self._animator:SetDissolve(value)
    end
end

function M:StopTimer()
    if self.TimeTicker then
        util.RemoveDelayCall(self.TimeTicker)
        self.TimeTicker = nil
    end
end

function M:ClearData()
    self.eventCfg = nil
    self.defaultZ = nil
    self.mapCfg = nil
    self.eventType = nil
    self.sortingGroup = nil
    self:StopTimer()
    self:endTimer()
    if self._animator then
        self._animator:SetAttackLoopInterval(false)
        self._animator:Dispose()
        self._animator = nil
    end
end
--- 重置为了循环利用
---@see override
function M:Recycle()
    self:ClearData()
    unit_base_object.Recycle(self)
end

function M:Dispose()
    if not util.IsObjNull(self.gameObject) then
        LeanTween.cancel(self.gameObject)
    end
    self:UpdateValue(0)
    self:ClearData()
    unit_base_object.Dispose(self)
end

return M