local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local RectTransform = CS.UnityEngine.RectTransform
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Text = CS.UnityEngine.UI.Text


module("ui_arena_first_enter_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenafirstenter.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	rtf_changeAni = { path = "rtf_changeAni", type = RectTransform, },
	rtf_RankShow = { path = "rtf_RankShow", type = RectTransform, },
	ss_rankTopBg = { path = "rtf_RankShow/ss_rankTopBg", type = SpriteSwitcher, },
	txt_playerNameTop = { path = "rtf_RankShow/ss_rankTopBg/txt_playerNameTop", type = Text, },
	rtf_faceTra = { path = "rtf_RankShow/ss_rankTopBg/rtf_faceTra", type = RectTransform, },
	rtf_otherRankShow = { path = "rtf_RankShow/rtf_otherRankShow", type = RectTransform, },
	txt_curRankNum = { path = "rtf_RankShow/rtf_otherRankShow/rankBg/txt_curRankNum", type = Text, },
	txt_playerName = { path = "rtf_RankShow/rtf_otherRankShow/playerNameBG/txt_playerName", type = Text, },

}
