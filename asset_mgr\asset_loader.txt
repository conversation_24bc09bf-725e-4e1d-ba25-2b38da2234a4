local require = require
local setmetatable = setmetatable

local IOSystem = require "iosystem_load"
local log = require "log"
local util = require "util"
local val = require "val"
local util = require "util"
local string = string
local Application = CS.UnityEngine.Application
local hashRemote = CS.War.Base.AssetBundleManager.hashRemote
local CS = CS

module("asset_loader")

--sealed class
local asset_loader = {}

function asset_loader:ctor(selfType, spriteAssetPath, tag)
    self.assetPath = spriteAssetPath
    self.tag = tag or "asset_loader"
    self.asset = nil -- asset资源引用，可能存在丢失情况
    self._assetPath = nil -- 标记是否已经经过加载状态，资源意外丢失时仍需重新发起下载
end
function asset_loader:load(callback)
    -- log.Error("asset_loader load",self.assetPath,"|",self._assetPath,self.callback ,"|",self.isDisposed,"|",self:Des())
    self.isDisposed = false --

    self.callback = callback

    if self._assetPath and (self._assetPath ~= self.assetPath) then
        log.Warning("asset_loader UnloadAssetBundle pre", self.assetPath, self._assetPath)
        IOSystem.UnloadAssetBundle(self._assetPath, self.tag, true)
        self._assetPath = nil
    end
    if self._assetPath then
        if not util.IsObjNull(self.asset) then
            if self.callback then
                self.callback(self)
            end
            -- log.Warning("asset_loader skip",self.assetPath,self._assetPath)
            return self
        else
            -- 未赋值状态，属于等待请求结果状态
            if not self.asset then
                --log.Error("asset_loader wait reloading",self.assetPath,self:Des())
                return self
            end
            -- 如果是资源已经被意外删除，继续走下载逻辑
            log.Error("asset_loader res reloading", self.assetPath, self:Des())
        end
    end

    --log.Error("asset_loader loading",self.assetPath,"|",self:Des())

    self._assetPath = self.assetPath
    local assetPath = self._assetPath
    -- 清除asset引用状态，走下载逻辑
    self.asset = nil

    local __cb = function(asset)
        -- log.Error("asset_loader loaded",self.assetPath,assetPath,self.callback ,"|",self.isDisposed,"|",self:Des())
        if self.isDisposed then
            return
        end
        if assetPath ~= self._assetPath then
            return
        end
        self.asset = asset

        if self.callback then
            self.callback(self)
        end
    end
    if val.IsTrue("sw_delay_asset_loader", 0) then
        IOSystem.LoadAssetAsync(
            self.assetPath,
            nil,
            function(asset)
                -- log.Error("asset_loader loaded",self.assetPath,assetPath,self.callback ,"|",self.isDisposed,"|",self:Des())

                util.DelayCallOnce(
                    0.5,
                    function()
                        __cb(asset)
                    end
                )
            end,
            self.tag
        )
    else
        IOSystem.LoadAssetAsync(self.assetPath, nil, __cb, self.tag)
    end

    return self
    
end

function IsExisit(assetPath)
    local size = hashRemote and hashRemote:GetSize(assetPath) or 0
    if size > 0 then
        return size > 0
    end
    if Application.isEditor then
        local AssetDatabase = CS.UnityEditor.AssetDatabase
        -- string[] assetPaths;
        -- if (string.IsNullOrEmpty(assetName))
        -- {
        --     assetPaths = AssetDatabase.GetAssetPathsFromAssetBundle(assetBundleName);
        -- }
        -- else
        -- {
        --     assetPaths = AssetDatabase.GetAssetPathsFromAssetBundleAndAssetName(assetBundleName, assetName);
        -- }
        -- if (assetPaths.Length == 0)
        -- {
        --     GameObjectPool.PrintError("There is no asset with name \"" + assetName + "\" in " + assetBundleName);
        --     return;
        -- }
        local assetPaths = AssetDatabase.GetAssetPathsFromAssetBundle(assetPath)
        if assetPaths and assetPaths.Length > 0 then
            return true
        end
    end
end

function asset_loader:IsExisit()
    if self.__isExist ~= nil then
        return self.__isExist
    end
    local b = IsExisit(self.assetPath)
    self.__isExist = b
    return self.__isExist
end

function asset_loader:IsReady()
    return not self.isDisposed and not util.IsObjNull(self.asset)
end

function asset_loader:IsLoading()
    return not self.isDisposed and util.IsObjNull(self.asset)
end

function asset_loader:IsValid()
    return not self.isDisposed and self._assetPath
end
function asset_loader:Des()
    local mark = string.format("%s|%s|%s", self._assetPath and "1" or "0", self.asset and "1" or "0", not (util.IsObjNull(self.asset)) and "1" or "0")
    return mark
end

function asset_loader:Dispose()
    self.isDisposed = true
    self.asset = nil
    self.callback = nil
    if self._assetPath then
        if self:IsExisit() then
            IOSystem.UnloadAssetBundle(self._assetPath, self.tag, true)
        end
        self._assetPath = nil
    end
end


function new(...)
    local self = {}
    setmetatable(self, {__index = asset_loader})
    self:ctor(asset_loader,...)
    return self
end

-- local class = require "class"
-- local object = require "object"
-- local c_vo = class(object, nil, asset_loader)
return new
