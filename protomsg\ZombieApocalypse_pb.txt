-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
module('ZombieApocalypse_pb')


V1M=V(4,"enZombieApocalypseCheckType_None",0,0)
V2M=V(4,"enZombieApocalypseCheckType_All",1,1)
V3M=V(4,"enZombieApocalypseCheckType_Preview",2,2)
V4M=V(4,"enZombieApocalypseCheckType_Appointment",3,3)
V5M=V(4,"enZombieApocalypseCheckType_Running",4,4)
V6M=V(4,"enZombieApocalypseCheckType_CoolDown",5,5)
V7M=V(4,"enZombieApocalypseCheckType_RecallChat",6,6)
V8M=V(4,"enZombieApocalypseCheckType_PassBoxMail",7,7)
V9M=V(4,"enZombieApocalypseCheckType_PoisonExpire",8,8)
V10M=V(4,"enZombieApocalypseCheckType_Max",9,9)
E1M=E(3,"enZombieApocalypseCheckType",".CSMsg.enZombieApocalypseCheckType")
V11M=V(4,"enZombieApocalypseState_None",0,0)
V12M=V(4,"enZombieApocalypseState_Preview",1,1)
V13M=V(4,"enZombieApocalypseState_Open",2,2)
V14M=V(4,"enZombieApocalypseState_Running",3,3)
V15M=V(4,"enZombieApocalypseState_Cool",4,4)
V16M=V(4,"enZombieApocalypseState_Close",5,5)
V17M=V(4,"enZombieApocalypseState_Max",6,6)
E2M=E(3,"enZombieApocalypseState",".CSMsg.enZombieApocalypseState")
V18M=V(4,"enZombieApocalypsePointState_None",0,0)
V19M=V(4,"enZombieApocalypsePointState_Prepare",1,1)
V20M=V(4,"enZombieApocalypsePointState_PreAttack",2,2)
V21M=V(4,"enZombieApocalypsePointState_Attack",3,3)
V22M=V(4,"enZombieApocalypsePointState_Max",4,4)
E3M=E(3,"enZombieApocalypsePointState",".CSMsg.enZombieApocalypsePointState")
V23M=V(4,"enZombieApocalypseCardType_None",0,0)
V24M=V(4,"enZombieApocalypseCardType_Prepare",1,1)
V25M=V(4,"enZombieApocalypseCardType_MutantTruck",2,2)
V26M=V(4,"enZombieApocalypseCardType_Pass",3,3)
V27M=V(4,"enZombieApocalypseCardType_PassFail",4,4)
V28M=V(4,"enZombieApocalypseCardType_Max",5,5)
E4M=E(3,"enZombieApocalypseCardType",".CSMsg.enZombieApocalypseCardType")
V29M=V(4,"enZombieApocalypseListType_None",0,0)
V30M=V(4,"enZombieApocalypseListType_AssistDetoxification",1,1)
V31M=V(4,"enZombieApocalypseListType_AssistDefence",2,2)
V32M=V(4,"enZombieApocalypseListType_Max",3,3)
E5M=E(3,"enZombieApocalypseListType",".CSMsg.enZombieApocalypseListType")
V33M=V(4,"enZombieApocalypseDetailType_None",0,0)
V34M=V(4,"enZombieApocalypseDetailType_DetoxifyReward",1,1)
V35M=V(4,"enZombieApocalypseDetailType_MutantTruck",2,2)
V36M=V(4,"enZombieApocalypseDetailType_Max",3,3)
E6M=E(3,"enZombieApocalypseDetailType",".CSMsg.enZombieApocalypseDetailType")
V37M=V(4,"enZombieApocalypseZombieType_None",0,0)
V38M=V(4,"enZombieApocalypseZombieType_Normal",1,1)
V39M=V(4,"enZombieApocalypseZombieType_Poisonous",2,2)
V40M=V(4,"enZombieApocalypseZombieType_Truck",3,3)
V41M=V(4,"enZombieApocalypseZombieType_MutantTruck",4,4)
E7M=E(3,"enZombieApocalypseZombieType",".CSMsg.enZombieApocalypseZombieType")
F1D=F(2,"nRoleId",".CSMsg.TZombieApocalypsePlayerPos.nRoleId",1,0,1,false,0,5,1)
F2D=F(2,"nPosX",".CSMsg.TZombieApocalypsePlayerPos.nPosX",2,1,1,false,0,5,1)
F3D=F(2,"nPosY",".CSMsg.TZombieApocalypsePlayerPos.nPosY",3,2,1,false,0,5,1)
M1G=D(1,"TZombieApocalypsePlayerPos",".CSMsg.TZombieApocalypsePlayerPos",false,{},{},nil,{})
F4D=F(2,"nPosX",".CSMsg.TZombieApocalypsePointPos.nPosX",1,0,1,false,0,5,1)
F5D=F(2,"nPosY",".CSMsg.TZombieApocalypsePointPos.nPosY",2,1,1,false,0,5,1)
M2G=D(1,"TZombieApocalypsePointPos",".CSMsg.TZombieApocalypsePointPos",false,{},{},nil,{})
F6D=F(2,"nActivityId",".CSMsg.TZombieApocalypseActivityData.nActivityId",1,0,1,false,0,5,1)
F7D=F(2,"nState",".CSMsg.TZombieApocalypseActivityData.nState",2,1,1,false,nil,14,8)
F8D=F(2,"nRunningState",".CSMsg.TZombieApocalypseActivityData.nRunningState",3,2,1,false,nil,14,8)
F9D=F(2,"nEntitySid",".CSMsg.TZombieApocalypseActivityData.nEntitySid",4,3,1,false,0,5,1)
F10D=F(2,"nCluePoint",".CSMsg.TZombieApocalypseActivityData.nCluePoint",5,4,1,false,0,5,1)
F11D=F(2,"nMaxUnlockDifficulty",".CSMsg.TZombieApocalypseActivityData.nMaxUnlockDifficulty",6,5,1,false,0,5,1)
F12D=F(2,"nCooldownTime",".CSMsg.TZombieApocalypseActivityData.nCooldownTime",7,6,1,false,0,5,1)
F13D=F(2,"nAppointmentTime",".CSMsg.TZombieApocalypseActivityData.nAppointmentTime",8,7,1,false,0,5,1)
F14D=F(2,"nAppointmentDifficulty",".CSMsg.TZombieApocalypseActivityData.nAppointmentDifficulty",9,8,1,false,0,5,1)
F15D=F(2,"nDifficulty",".CSMsg.TZombieApocalypseActivityData.nDifficulty",10,9,1,false,0,5,1)
F16D=F(2,"nWave",".CSMsg.TZombieApocalypseActivityData.nWave",11,10,1,false,0,5,1)
F17D=F(2,"nCountdown",".CSMsg.TZombieApocalypseActivityData.nCountdown",12,11,1,false,0,5,1)
F18D=F(2,"nAllDefendNumber",".CSMsg.TZombieApocalypseActivityData.nAllDefendNumber",13,12,1,false,0,5,1)
F19D=F(2,"tPlayerPos",".CSMsg.TZombieApocalypseActivityData.tPlayerPos",14,13,3,false,{},11,10)
F20D=F(2,"tZombieBasePos",".CSMsg.TZombieApocalypseActivityData.tZombieBasePos",15,14,1,false,nil,11,10)
M3G=D(1,"TZombieApocalypseActivityData",".CSMsg.TZombieApocalypseActivityData",false,{},{},nil,{})
F21D=F(2,"nRoleId",".CSMsg.TZombieApocalypseRoleWaveData.nRoleId",1,0,1,false,0,5,1)
F22D=F(2,"nWave",".CSMsg.TZombieApocalypseRoleWaveData.nWave",2,1,1,false,0,5,1)
M6G=D(1,"TZombieApocalypseRoleWaveData",".CSMsg.TZombieApocalypseRoleWaveData",false,{},{},nil,{})
F23D=F(2,"nRoleId",".CSMsg.TZombieApocalypseDetailRoleData.nRoleId",1,0,1,false,0,5,1)
F24D=F(2,"strRoleName",".CSMsg.TZombieApocalypseDetailRoleData.strRoleName",2,1,1,false,"",9,9)
F25D=F(2,"nRoleLv",".CSMsg.TZombieApocalypseDetailRoleData.nRoleLv",3,2,1,false,0,5,1)
F26D=F(2,"strAvatar",".CSMsg.TZombieApocalypseDetailRoleData.strAvatar",4,3,1,false,"",9,9)
F27D=F(2,"nAvatarId",".CSMsg.TZombieApocalypseDetailRoleData.nAvatarId",5,4,1,false,0,5,1)
F28D=F(2,"nFrameId",".CSMsg.TZombieApocalypseDetailRoleData.nFrameId",6,5,1,false,0,5,1)
F29D=F(2,"nHelpNum",".CSMsg.TZombieApocalypseDetailRoleData.nHelpNum",7,6,1,false,0,5,1)
F30D=F(2,"nRewardList",".CSMsg.TZombieApocalypseDetailRoleData.nRewardList",8,7,3,false,{},5,1)
M7G=D(1,"TZombieApocalypseDetailRoleData",".CSMsg.TZombieApocalypseDetailRoleData",false,{},{},nil,{})
F31D=F(2,"nRoleId",".CSMsg.TZombieApocalypseListRoleData.nRoleId",1,0,1,false,0,5,1)
F32D=F(2,"nRoleLv",".CSMsg.TZombieApocalypseListRoleData.nRoleLv",2,1,1,false,0,5,1)
F33D=F(2,"strRoleName",".CSMsg.TZombieApocalypseListRoleData.strRoleName",3,2,1,false,"",9,9)
F34D=F(2,"strAvatar",".CSMsg.TZombieApocalypseListRoleData.strAvatar",4,3,1,false,"",9,9)
F35D=F(2,"nAvatarId",".CSMsg.TZombieApocalypseListRoleData.nAvatarId",5,4,1,false,0,5,1)
F36D=F(2,"nFrameId",".CSMsg.TZombieApocalypseListRoleData.nFrameId",6,5,1,false,0,5,1)
F37D=F(2,"nCurNums",".CSMsg.TZombieApocalypseListRoleData.nCurNums",7,6,1,false,0,5,1)
F38D=F(2,"nEntitySid",".CSMsg.TZombieApocalypseListRoleData.nEntitySid",8,7,1,false,0,5,1)
F39D=F(2,"tPos",".CSMsg.TZombieApocalypseListRoleData.tPos",9,8,1,false,nil,11,10)
F40D=F(2,"tRoleList",".CSMsg.TZombieApocalypseListRoleData.tRoleList",10,9,3,false,{},5,1)
M8G=D(1,"TZombieApocalypseListRoleData",".CSMsg.TZombieApocalypseListRoleData",false,{},{},nil,{})
F41D=F(2,"nActivityId",".CSMsg.TZombieApocalypseCardChat.nActivityId",1,0,1,false,0,5,1)
F42D=F(2,"nType",".CSMsg.TZombieApocalypseCardChat.nType",2,1,1,false,nil,14,8)
F43D=F(2,"nRoleId",".CSMsg.TZombieApocalypseCardChat.nRoleId",3,2,1,false,0,5,1)
F44D=F(2,"strRoleName",".CSMsg.TZombieApocalypseCardChat.strRoleName",4,3,1,false,"",9,9)
F45D=F(2,"strAvatar",".CSMsg.TZombieApocalypseCardChat.strAvatar",5,4,1,false,"",9,9)
F46D=F(2,"nAvatarId",".CSMsg.TZombieApocalypseCardChat.nAvatarId",6,5,1,false,0,5,1)
F47D=F(2,"nFrameId",".CSMsg.TZombieApocalypseCardChat.nFrameId",7,6,1,false,0,5,1)
F48D=F(2,"nDifficulty",".CSMsg.TZombieApocalypseCardChat.nDifficulty",8,7,1,false,0,5,1)
F49D=F(2,"nPassNum",".CSMsg.TZombieApocalypseCardChat.nPassNum",9,8,1,false,0,5,1)
F50D=F(2,"nEntitySid",".CSMsg.TZombieApocalypseCardChat.nEntitySid",10,9,1,false,0,3,2)
F51D=F(2,"tPos",".CSMsg.TZombieApocalypseCardChat.tPos",11,10,1,false,nil,11,10)
M9G=D(1,"TZombieApocalypseCardChat",".CSMsg.TZombieApocalypseCardChat",false,{},{},nil,{})
F52D=F(2,"nActivityId",".CSMsg.TZombieApocalypseListChat.nActivityId",1,0,1,false,0,5,1)
F53D=F(2,"nType",".CSMsg.TZombieApocalypseListChat.nType",2,1,1,false,nil,14,8)
F54D=F(2,"nDifficulty",".CSMsg.TZombieApocalypseListChat.nDifficulty",3,2,1,false,0,5,1)
F55D=F(2,"nWave",".CSMsg.TZombieApocalypseListChat.nWave",4,3,1,false,0,5,1)
F56D=F(2,"nSandboxSid",".CSMsg.TZombieApocalypseListChat.nSandboxSid",5,4,1,false,0,5,1)
F57D=F(2,"tRoleList",".CSMsg.TZombieApocalypseListChat.tRoleList",6,5,3,false,{},11,10)
M11G=D(1,"TZombieApocalypseListChat",".CSMsg.TZombieApocalypseListChat",false,{},{},nil,{})
F58D=F(2,"nActivityId",".CSMsg.TZombieApocalypseDetailChat.nActivityId",1,0,1,false,0,5,1)
F59D=F(2,"nType",".CSMsg.TZombieApocalypseDetailChat.nType",2,1,1,false,nil,14,8)
F60D=F(2,"nDifficulty",".CSMsg.TZombieApocalypseDetailChat.nDifficulty",3,2,1,false,0,5,1)
F61D=F(2,"nWave",".CSMsg.TZombieApocalypseDetailChat.nWave",4,3,1,false,0,5,1)
F62D=F(2,"nRoleId",".CSMsg.TZombieApocalypseDetailChat.nRoleId",5,4,1,false,0,5,1)
F63D=F(2,"nRoleLv",".CSMsg.TZombieApocalypseDetailChat.nRoleLv",6,5,1,false,0,5,1)
F64D=F(2,"strRoleName",".CSMsg.TZombieApocalypseDetailChat.strRoleName",7,6,1,false,"",9,9)
F65D=F(2,"strAvatar",".CSMsg.TZombieApocalypseDetailChat.strAvatar",8,7,1,false,"",9,9)
F66D=F(2,"nAvatarId",".CSMsg.TZombieApocalypseDetailChat.nAvatarId",9,8,1,false,0,5,1)
F67D=F(2,"nFrameId",".CSMsg.TZombieApocalypseDetailChat.nFrameId",10,9,1,false,0,5,1)
F68D=F(2,"tRoleList",".CSMsg.TZombieApocalypseDetailChat.tRoleList",11,10,3,false,{},11,10)
M13G=D(1,"TZombieApocalypseDetailChat",".CSMsg.TZombieApocalypseDetailChat",false,{},{},nil,{})
F69D=F(2,"nRoleId",".CSMsg.TZombieApocalypseAllianceReward.nRoleId",1,0,1,false,0,5,1)
F70D=F(2,"strRoleName",".CSMsg.TZombieApocalypseAllianceReward.strRoleName",2,1,1,false,"",9,9)
F71D=F(2,"strAvatar",".CSMsg.TZombieApocalypseAllianceReward.strAvatar",3,2,1,false,"",9,9)
F72D=F(2,"nAvatarId",".CSMsg.TZombieApocalypseAllianceReward.nAvatarId",4,3,1,false,0,5,1)
F73D=F(2,"nFrameId",".CSMsg.TZombieApocalypseAllianceReward.nFrameId",5,4,1,false,0,5,1)
F74D=F(2,"nPower",".CSMsg.TZombieApocalypseAllianceReward.nPower",6,5,1,false,0,5,1)
F75D=F(2,"nWave",".CSMsg.TZombieApocalypseAllianceReward.nWave",7,6,1,false,0,5,1)
M15G=D(1,"TZombieApocalypseAllianceReward",".CSMsg.TZombieApocalypseAllianceReward",false,{},{},nil,{})
F76D=F(2,"nPoisonedRoleId",".CSMsg.TZombieApocalypsePoisonedInfo.nPoisonedRoleId",1,0,1,false,0,5,1)
F77D=F(2,"nHelpList",".CSMsg.TZombieApocalypsePoisonedInfo.nHelpList",2,1,3,false,{},5,1)
M16G=D(1,"TZombieApocalypsePoisonedInfo",".CSMsg.TZombieApocalypsePoisonedInfo",false,{},{},nil,{})
F78D=F(2,"tAllianceData",".CSMsg.TZombieApocalypseAllianceRewardMail.tAllianceData",1,0,3,false,{},11,10)
M17G=D(1,"TZombieApocalypseAllianceRewardMail",".CSMsg.TZombieApocalypseAllianceRewardMail",false,{},{},nil,{})
F79D=F(2,"tActivityData",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_ACTIVITY_DATA_NTF.tActivityData",1,0,2,false,nil,11,10)
M18G=D(1,"TMSG_ZOMBIEAPOCALYPSE_ACTIVITY_DATA_NTF",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_ACTIVITY_DATA_NTF",false,{},{},nil,{})
F80D=F(2,"nActivityId",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_REQ.nActivityId",1,0,2,false,0,5,1)
M19G=D(1,"TMSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_REQ",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_REQ",false,{},{},nil,{})
F81D=F(2,"nErrorCode",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_RSP.nErrorCode",1,0,2,false,0,5,1)
F82D=F(2,"tActivityData",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_RSP.tActivityData",2,1,1,false,nil,11,10)
M20G=D(1,"TMSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_RSP",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_RSP",false,{},{},nil,{})
F83D=F(2,"nActivityId",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_REQ.nActivityId",1,0,2,false,0,5,1)
F84D=F(2,"nDifficulty",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_REQ.nDifficulty",2,1,2,false,0,5,1)
M21G=D(1,"TMSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_REQ",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_REQ",false,{},{},nil,{})
F85D=F(2,"nActivityId",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_RSP.nActivityId",1,0,2,false,0,5,1)
F86D=F(2,"nErrorCode",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_RSP.nErrorCode",2,1,2,false,0,5,1)
M22G=D(1,"TMSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_RSP",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_RSP",false,{},{},nil,{})
F87D=F(2,"nActivityId",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_REQ.nActivityId",1,0,2,false,0,5,1)
F88D=F(2,"nDifficulty",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_REQ.nDifficulty",2,1,2,false,0,5,1)
F89D=F(2,"tRoleList",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_REQ.tRoleList",3,2,3,false,{},5,1)
M23G=D(1,"TMSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_REQ",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_REQ",false,{},{},nil,{})
F90D=F(2,"nActivityId",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_RSP.nActivityId",1,0,2,false,0,5,1)
F91D=F(2,"nErrorCode",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_RSP.nErrorCode",2,1,2,false,0,5,1)
F92D=F(2,"tWaveList",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_RSP.tWaveList",3,2,3,false,{},11,10)
M24G=D(1,"TMSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_RSP",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_RSP",false,{},{},nil,{})
F93D=F(2,"nActivityId",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_REQ.nActivityId",1,0,2,false,0,5,1)
F94D=F(2,"nDifficulty",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_REQ.nDifficulty",2,1,2,false,0,5,1)
F95D=F(2,"nTimeStamp",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_REQ.nTimeStamp",3,2,2,false,0,5,1)
M25G=D(1,"TMSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_REQ",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_REQ",false,{},{},nil,{})
F96D=F(2,"nActivityId",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_RSP.nActivityId",1,0,2,false,0,5,1)
F97D=F(2,"nErrorCode",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_RSP.nErrorCode",2,1,2,false,0,5,1)
M26G=D(1,"TMSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_RSP",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_RSP",false,{},{},nil,{})
F98D=F(2,"nActivityId",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_REQ.nActivityId",1,0,2,false,0,5,1)
M27G=D(1,"TMSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_REQ",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_REQ",false,{},{},nil,{})
F99D=F(2,"nActivityId",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_RSP.nActivityId",1,0,2,false,0,5,1)
F100D=F(2,"nErrorCode",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_RSP.nErrorCode",2,1,2,false,0,5,1)
M28G=D(1,"TMSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_RSP",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_RSP",false,{},{},nil,{})
F101D=F(2,"strChat",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_CHAT_CHANGE_NTF.strChat",1,0,2,false,"",9,9)
F102D=F(2,"chatData",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_CHAT_CHANGE_NTF.chatData",2,1,2,false,nil,11,10)
M29G=D(1,"TMSG_ZOMBIEAPOCALYPSE_CHAT_CHANGE_NTF",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_CHAT_CHANGE_NTF",false,{},{},nil,{})
M30G=D(1,"TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_REQ",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_REQ",false,{},{},{},{})
F103D=F(2,"nErrorCode",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_RSP.nErrorCode",1,0,2,false,0,5,1)
F104D=F(2,"poisonedInfo",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_RSP.poisonedInfo",2,1,3,false,{},11,10)
M31G=D(1,"TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_RSP",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_RSP",false,{},{},nil,{})
F105D=F(2,"poisonedInfo",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_NTF.poisonedInfo",1,0,3,false,{},11,10)
M32G=D(1,"TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_NTF",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_NTF",false,{},{},nil,{})
F106D=F(2,"nSandboxSid",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_JUMP_REQ.nSandboxSid",1,0,2,false,0,5,1)
F107D=F(2,"nEntitySid",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_JUMP_REQ.nEntitySid",2,1,2,false,0,3,2)
M33G=D(1,"TMSG_ZOMBIEAPOCALYPSE_JUMP_REQ",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_JUMP_REQ",false,{},{},nil,{})
F108D=F(2,"nErrorCode",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_JUMP_RSP.nErrorCode",1,0,2,false,0,5,1)
F109D=F(2,"nSandboxSid",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_JUMP_RSP.nSandboxSid",2,1,1,false,0,5,1)
F110D=F(2,"nEntitySid",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_JUMP_RSP.nEntitySid",3,2,1,false,0,3,2)
F111D=F(2,"pos",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_JUMP_RSP.pos",4,3,1,false,nil,11,10)
M34G=D(1,"TMSG_ZOMBIEAPOCALYPSE_JUMP_RSP",".CSMsg.TMSG_ZOMBIEAPOCALYPSE_JUMP_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M}
E2M.values = {V11M,V12M,V13M,V14M,V15M,V16M,V17M}
E3M.values = {V18M,V19M,V20M,V21M,V22M}
E4M.values = {V23M,V24M,V25M,V26M,V27M,V28M}
E5M.values = {V29M,V30M,V31M,V32M}
E6M.values = {V33M,V34M,V35M,V36M}
E7M.values = {V37M,V38M,V39M,V40M,V41M}
M1G.fields={F1D, F2D, F3D}
M2G.fields={F4D, F5D}
F7D.enum_type=M4G
F8D.enum_type=M5G
F19D.message_type=M1G
F20D.message_type=M2G
M3G.fields={F6D, F7D, F8D, F9D, F10D, F11D, F12D, F13D, F14D, F15D, F16D, F17D, F18D, F19D, F20D}
M6G.fields={F21D, F22D}
M7G.fields={F23D, F24D, F25D, F26D, F27D, F28D, F29D, F30D}
F39D.message_type=M2G
M8G.fields={F31D, F32D, F33D, F34D, F35D, F36D, F37D, F38D, F39D, F40D}
F42D.enum_type=M10G
F51D.message_type=M2G
M9G.fields={F41D, F42D, F43D, F44D, F45D, F46D, F47D, F48D, F49D, F50D, F51D}
F53D.enum_type=M12G
F57D.message_type=M8G
M11G.fields={F52D, F53D, F54D, F55D, F56D, F57D}
F59D.enum_type=M14G
F68D.message_type=M7G
M13G.fields={F58D, F59D, F60D, F61D, F62D, F63D, F64D, F65D, F66D, F67D, F68D}
M15G.fields={F69D, F70D, F71D, F72D, F73D, F74D, F75D}
M16G.fields={F76D, F77D}
F78D.message_type=M15G
M17G.fields={F78D}
F79D.message_type=M3G
M18G.fields={F79D}
M19G.fields={F80D}
F82D.message_type=M3G
M20G.fields={F81D, F82D}
M21G.fields={F83D, F84D}
M22G.fields={F85D, F86D}
M23G.fields={F87D, F88D, F89D}
F92D.message_type=M6G
M24G.fields={F90D, F91D, F92D}
M25G.fields={F93D, F94D, F95D}
M26G.fields={F96D, F97D}
M27G.fields={F98D}
M28G.fields={F99D, F100D}
F102D.message_type=M11G
M29G.fields={F101D, F102D}
F104D.message_type=M16G
M31G.fields={F103D, F104D}
F105D.message_type=M16G
M32G.fields={F105D}
M33G.fields={F106D, F107D}
F111D.message_type=M2G
M34G.fields={F108D, F109D, F110D, F111D}

TMSG_ZOMBIEAPOCALYPSE_ACTIVITY_DATA_NTF =M(M18G)
TMSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_REQ =M(M27G)
TMSG_ZOMBIEAPOCALYPSE_CANCEL_PRE_START_CHALLENGE_RSP =M(M28G)
TMSG_ZOMBIEAPOCALYPSE_CHAT_CHANGE_NTF =M(M29G)
TMSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_REQ =M(M19G)
TMSG_ZOMBIEAPOCALYPSE_GET_ACTIVITY_DATA_RSP =M(M20G)
TMSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_REQ =M(M23G)
TMSG_ZOMBIEAPOCALYPSE_GET_WAVE_LIST_RSP =M(M24G)
TMSG_ZOMBIEAPOCALYPSE_JUMP_REQ =M(M33G)
TMSG_ZOMBIEAPOCALYPSE_JUMP_RSP =M(M34G)
TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_NTF =M(M32G)
TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_REQ =M(M30G)
TMSG_ZOMBIEAPOCALYPSE_POISONEDINFO_RSP =M(M31G)
TMSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_REQ =M(M25G)
TMSG_ZOMBIEAPOCALYPSE_PRE_START_CHALLENGE_RSP =M(M26G)
TMSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_REQ =M(M21G)
TMSG_ZOMBIEAPOCALYPSE_START_CHALLENGE_RSP =M(M22G)
TZombieApocalypseActivityData =M(M3G)
TZombieApocalypseAllianceReward =M(M15G)
TZombieApocalypseAllianceRewardMail =M(M17G)
TZombieApocalypseCardChat =M(M9G)
TZombieApocalypseDetailChat =M(M13G)
TZombieApocalypseDetailRoleData =M(M7G)
TZombieApocalypseListChat =M(M11G)
TZombieApocalypseListRoleData =M(M8G)
TZombieApocalypsePlayerPos =M(M1G)
TZombieApocalypsePointPos =M(M2G)
TZombieApocalypsePoisonedInfo =M(M16G)
TZombieApocalypseRoleWaveData =M(M6G)
enZombieApocalypseCardType_Max = 5
enZombieApocalypseCardType_MutantTruck = 2
enZombieApocalypseCardType_None = 0
enZombieApocalypseCardType_Pass = 3
enZombieApocalypseCardType_PassFail = 4
enZombieApocalypseCardType_Prepare = 1
enZombieApocalypseCheckType_All = 1
enZombieApocalypseCheckType_Appointment = 3
enZombieApocalypseCheckType_CoolDown = 5
enZombieApocalypseCheckType_Max = 9
enZombieApocalypseCheckType_None = 0
enZombieApocalypseCheckType_PassBoxMail = 7
enZombieApocalypseCheckType_PoisonExpire = 8
enZombieApocalypseCheckType_Preview = 2
enZombieApocalypseCheckType_RecallChat = 6
enZombieApocalypseCheckType_Running = 4
enZombieApocalypseDetailType_DetoxifyReward = 1
enZombieApocalypseDetailType_Max = 3
enZombieApocalypseDetailType_MutantTruck = 2
enZombieApocalypseDetailType_None = 0
enZombieApocalypseListType_AssistDefence = 2
enZombieApocalypseListType_AssistDetoxification = 1
enZombieApocalypseListType_Max = 3
enZombieApocalypseListType_None = 0
enZombieApocalypsePointState_Attack = 3
enZombieApocalypsePointState_Max = 4
enZombieApocalypsePointState_None = 0
enZombieApocalypsePointState_PreAttack = 2
enZombieApocalypsePointState_Prepare = 1
enZombieApocalypseState_Close = 5
enZombieApocalypseState_Cool = 4
enZombieApocalypseState_Max = 6
enZombieApocalypseState_None = 0
enZombieApocalypseState_Open = 2
enZombieApocalypseState_Preview = 1
enZombieApocalypseState_Running = 3
enZombieApocalypseZombieType_MutantTruck = 4
enZombieApocalypseZombieType_None = 0
enZombieApocalypseZombieType_Normal = 1
enZombieApocalypseZombieType_Poisonous = 2
enZombieApocalypseZombieType_Truck = 3

