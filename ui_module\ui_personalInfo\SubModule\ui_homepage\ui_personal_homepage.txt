--- Created by: 袁楠
--- DateTime: 2024/8/27
--- desc: 主页View
---

--region Require
local require = require
local string = string
local tostring = tostring
local stringFormat = string.format

local unforced_guide_mgr = require "unforced_guide_mgr"
local enum_define = require "enum_define"
local class = require "class"
local lang = require "lang"
local ui_base = require "ui_base"
local face_item = require "face_item_new"
local const_personalInfo = require "const_personalInfo"
local string_util = require "string_util"
local red_const = require "red_const"
--endregion 

--region View Life
module("ui_personal_homepage")
local ui_path = "ui/prefabs/gw/gw_personalinfo/uihomepage.prefab"
local window = nil
local UIView = {}

UIView.widget_table = {
    Btn_change = { path = "content/battleInfo/Auto_changeBtn", type = "Button", event_name = "OnBtnChangeEvent" },
    Btn_headChange = { path = "content/playerInfo/Auto_changeHeadBtn", type = "Button", event_name = "OnBtnHeadChangeEvent" },
    
    Btn_schlossChange = { path = "content/eventGroup/Auto_schlossChangeBtn", type = "Button", event_name = "OnBtnSchlossChangeEvent" },
    Btn_rank = { path = "content/eventGroup/Auto_rankBtn", type = "Button", event_name = "OnBtnRankEvent" },
    Btn_alliance = { path = "content/bottom/Auto_allianceBtn", type = "Button", event_name = "OnBtnAllianceEvent" },
    Btn_world = { path = "content/bottom/Auto_worldBtn", type = "Button", event_name = "OnBtnWorldEvent" },

    RTan_faceItem = { path = "content/playerInfo/Auto_faceItem", type = "RectTransform" },
    RTan_sex = { path = "content/battleInfo/socialInfo/Auto_sex", type = "RectTransform" },

    Text_name = { path = "content/battleInfo/nameBg/Auto_nameText", type = "Text" },
    Text_sex = { path = "content/battleInfo/socialInfo/Auto_sex/Auto_sexText", type = "Text" },
    Text_praise = { path = "content/battleInfo/socialInfo/praise/Auto_praiseText", type = "Text" },
    Text_id = { path = "content/battleInfo/socialInfo/id/Auto_idText", type = "Text" },
    Text_battlePower = { path = "content/battleInfo/battlePower/Auto_battlePowerText", type = "Text" },
    Text_enemyKill = { path = "content/battleInfo/enemyKill/Auto_enemyKillText", type = "Text" },
    
    Text_alliance = { path = "content/bottom/Auto_allianceBtn/Auto_allianceText", type = "Text" },
    Text_world = { path = "content/bottom/Auto_worldBtn/Auto_worldText", type = "Text" },

    Img_sex = { path = "content/battleInfo/socialInfo/Auto_sex/Auto_sexImage", type = "Image" },
    Img_nationalFlag = { path = "content/battleInfo/nameBg/Img_nationalFlag", type = "Image" },

    Btn_likeRecord = {path = "content/eventGroup/Auto_Likerecord", type = "Button", event_name = "OnBtnLikeRecordEvent"},
}

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    -- 初始化头像
    self.faceItem = face_item.CFaceItem()
    self.faceItem:Init(self.RTan_faceItem, nil, 1)

    self:BindUIRed(self.Btn_world.transform,red_const.Enum.Congress,nil,{pos = { x = -85, y = 22 },redPath = red_const.Type.Mark})
    self:BindUIRed(self.Btn_rank.transform, red_const.Enum.RankingMain, nil, {redPath = red_const.Type.Default })
    self:BindUIRed(self.Btn_schlossChange.transform, red_const.Enum.Personalised, nil, {redPath = red_const.Type.Default })
    self:BindUIRed(self.Btn_likeRecord.transform,red_const.Enum.PersonalLikeRecord,nil,{redPath = red_const.Type.Default})
    
    self:ShowNationalFlag()
end

function UIView:OnShow()
    self.__base:OnShow()
end

function UIView:OnHide()
    self.__base:OnHide()
    local stepID = unforced_guide_mgr.GetCurStep()
    if (unforced_guide_mgr.GetCurGuide() == 25 or unforced_guide_mgr.GetCurGuide() == 26)  and (stepID == 29 or stepID == 31)then
        unforced_guide_mgr.CloseGuide()
    end
end

function UIView:Close()
    if self.faceItem then
        self.faceItem:Dispose()
        self.faceItem = nil
    end
    local stepID = unforced_guide_mgr.GetCurStep()
    if (unforced_guide_mgr.GetCurGuide() == 25 or unforced_guide_mgr.GetCurGuide() == 26)  and (stepID == 29 or stepID == 31)then
        unforced_guide_mgr.CloseGuide()
    end
    self.__base:Close()
end
--endregion

--region 功能函数区
function UIView:UpdateUserInfo(data)
    if data then
        if data.customAvatarData and data.customAvatarData.used then
            self.faceItem:SetCustomFaceInfo(data.customAvatarData, function()
                data.faceClickEvent()
            end)
        else
            self.faceItem:SetFaceInfo(data.faceID, function()
                data.faceClickEvent()
            end)
        end
        
        self.faceItem:SetFrameID(data.frameID, true)

        self.Text_name.text = stringFormat("Lv.%d  %s", data.level, data.name)
        
        -- 国旗
        self:ShowNationalFlag()
    end
end
--- 国旗显示
function UIView:ShowNationalFlag()
    -- 国旗
    local nationalFlag = require("national_flag_mgr")
    if nationalFlag.CheckNationalFlagIsOpen() then
        self.Img_nationalFlag.gameObject:SetActive(true)
        nationalFlag.SetNationalFlagInfo(self,self.Img_nationalFlag)
    else
        self.Img_nationalFlag.gameObject:SetActive(false)
    end
end

function UIView:UpdateSocialInfo(data)
    if data then
        if data.sex == const_personalInfo.PersonalSexTag.man then
            self:SetSexLabel(const_personalInfo.lang_Man, const_personalInfo.asset_manSprite)
        elseif data.sex == const_personalInfo.PersonalSexTag.woman then
            self:SetSexLabel(const_personalInfo.lang_Woman, const_personalInfo.asset_womanSprite)
        else
            self.RTan_sex.gameObject:SetActive(false)
        end
        self.Text_praise.text = tostring(data.praise)
        self.Text_id.text = tostring(data.roleID)
    end
end

function UIView:UpdateBattleInfo(data)
    if data then
        self.Text_battlePower.text = string_util.toBalancesString(data.battlePower)
        self.Text_enemyKill.text = string_util.toBalancesString(data.enemyKill)
    end
end

function UIView:UpdateWorldInfo(data)
    if data then
        local helper_personalInfo = require "helper_personalInfo"
        local ui_util = require "ui_util"
        -- helper_personalInfo.SetWorldAndServerNameToShow 这个接口分支不上 不要同步分支 ！！！！
        local str = helper_personalInfo.SetWorldAndServerNameToShow(stringFormat("#%s", ui_util.GetWorldIDToShowWorldID(data.worldId, nil, ui_util.WorldIDRangeType.Normal))) -- 可传入第二个参数改变是否显示当前服的名字 默认关闭显示
        self.Text_world.text = str
        if data.allianceName then
            self.Text_alliance.text = stringFormat("[%s]", data.allianceName)
        else
            self.Text_alliance.text = lang.Get(const_personalInfo.lang_NA)
        end
    end
end

function UIView:SetSexLabel(langID, icon)
    self.RTan_sex:SetActive(true)
    self.Text_sex.text = lang.Get(langID)
    self:CreateSubSprite("CreateSpriteAsset", self.Img_sex, icon)
end

--endregion

--region View Static
local CUIView = class(ui_base, nil, UIView)

function Show(parentTransform)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window:LoadUIResource(ui_path, nil, parentTransform, nil, nil, nil, nil, false)
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
