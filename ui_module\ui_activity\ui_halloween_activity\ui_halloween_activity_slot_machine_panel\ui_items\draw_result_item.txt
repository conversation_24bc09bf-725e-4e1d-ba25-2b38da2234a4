local string = string
local tostring = tostring
local tonumber = tonumber

local reward_mgr = require "reward_mgr"

local game_scheme = require("game_scheme")
local lang = require "lang"
local halloween_activity_slot_machine_const = require "halloween_activity_slot_machine_const"


local draw_result_item = {}


function draw_result_item.RenderByData(rtf_item, act_id, data)
    draw_result_item.Render(rtf_item, data.cfg, act_id, data.rate, data.count)
end

---@public 显示一个抽奖结果item
---@param rtf_item transform transform
---@param cfg_id number int
---@param rate number float
---@param count number int or nil
function draw_result_item.Render(rtf_item, cfg, act_id, rate, count)
    count = count or 1
    local patternString = cfg.TaskID
    local patternGroup = string.split(patternString, "#")

    for i = 1, #patternGroup do
        local patternID = tonumber(patternGroup[i])
        local patternCfg = game_scheme:SlotPattern_0(patternID, act_id)
        local iconID = 0
        local iconIDValid = false
        if patternCfg then
            iconID = patternCfg.cardID
            iconIDValid = true
        end

        local img_com = rtf_item:Find("left/img_icon_" .. tostring(i)):GetComponent("Image")
        local img_ramdom = rtf_item:Find("left/img_icon_" .. tostring(i+3)):GetComponent("Image")

        img_com:SetActive(iconIDValid)
        img_ramdom:SetActive(not iconIDValid)

        if iconIDValid then
            ---@type GWAssetMgr
            local gw_asset_mgr = require "gw_asset_mgr"
            gw_asset_mgr:LoadGoodsIcon(iconID, function(sprite)
                img_com.sprite = sprite
            end)
        end
    end

    local goodsItemParent = rtf_item:Find("right/rtf_item"):GetComponent("RectTransform")
    local goodsItems = reward_mgr.GetRewardItemList(cfg.RewardID, goodsItemParent,true,0.5)
    goodsItems[1]:SetCountEnable(false)
    local goodsItemDatas = reward_mgr.GetRewardGoodsList2(cfg.RewardID)
    local itemCount = goodsItemDatas[1].num

    local txt_num = rtf_item:Find("right/txt_num"):GetComponent("Text")
    -- if count > 1 then
    --     txt_num.text = lang.Get(halloween_activity_slot_machine_const.lang_key.x5)
    -- else
    --     txt_num.text = lang.Get(halloween_activity_slot_machine_const.lang_key.x1)
    -- end
    txt_num.text = string.format("x%d", itemCount)

    local txt_rate_num = rtf_item:Find("right/txt_rate_num"):GetComponent("Text")
    -- rate 转化为百分比
    txt_rate_num.text = string.format("%.1f", (rate or 0) * 100) .. "%"

    local img_times_bg_obj = rtf_item:Find("right/img_times_bg")
    if img_times_bg_obj then
        local img_times_bg = img_times_bg_obj:GetComponent("Image")
        local txt_times = img_times_bg_obj:Find("txt_times"):GetComponent("Text")
        txt_times.text = string.format("x%d", count)
    end
end



return draw_result_item