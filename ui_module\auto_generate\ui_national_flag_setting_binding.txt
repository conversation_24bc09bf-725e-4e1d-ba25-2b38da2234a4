local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem


module("ui_national_flag_setting_binding")

UIPath = "ui/prefabs/gw/gw_nationalflag/uinationalflagsetting.prefab"

WidgetTable ={
	back_Close = { path = "Bg/back_Close", type = Button, event_name = "OnBtnCloseClickedProxy", backEvent = true},
	btn_Confirm = { path = "Bg/btn_Confirm", type = Button, event_name = "OnBtnConfirmClickedProxy"},
	txt_CountDown = { path = "Bg/btn_Confirm/txt_CountDown", type = Text, },
	srt_Content = { path = "Auto_List/Viewport/srt_Content", type = ScrollRectTable, },
	scrItem_nationalFlag = { path = "Auto_List/Viewport/srt_Content/scrItem_nationalFlag", type = ScrollRectItem, },

}
