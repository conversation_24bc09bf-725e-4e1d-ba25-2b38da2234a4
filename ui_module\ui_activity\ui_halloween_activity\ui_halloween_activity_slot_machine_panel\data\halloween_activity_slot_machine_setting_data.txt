﻿local PlayerPrefs = CS.UnityEngine.PlayerPrefs

local tostring = tostring
local data_personalInfo = require "data_personalInfo"
local string = string

local halloween_activity_slot_machine_data_helper = require "halloween_activity_slot_machine_data_helper"

local halloween_activity_slot_machine_setting_data = {
    data = {
        speed_up = {
            key = "halloween_activity_slot_machine_setting_data_speed_up",
            value = 0,
            defaultValue = 0,
        },
        draw_count_per_click = {
            key = nil, -- 无key表示不存
            value = 1,
            defaultValue = 1,

        }
    }
}




function halloween_activity_slot_machine_setting_data:Init()
    for k, v in pairs(self.data) do
        self.data[k].value = v.key and PlayerPrefs.GetInt(halloween_activity_slot_machine_data_helper.GetRoleKey(v.key), v.defaultValue) or v.defaultValue
    end
end

function halloween_activity_slot_machine_setting_data:Save()
    for k, v in pairs(self.data) do
        local keyStr = v.key
        if keyStr then
            PlayerPrefs.SetInt(halloween_activity_slot_machine_data_helper.GetRoleKey(keyStr), v.value)
        end
        
    end
    PlayerPrefs.Save()
end


function halloween_activity_slot_machine_setting_data:GetValue(key)
    local value = self.data[key]
    if value then
        return value.value
    end
    return nil
end

function halloween_activity_slot_machine_setting_data:SetValue(key, value)
    local v = self.data[key]
    if v then
        v.value = value
        local keyStr = v.key
        if keyStr then
            PlayerPrefs.SetInt(halloween_activity_slot_machine_data_helper.GetRoleKey(keyStr), v.value)
        end
    end
    PlayerPrefs.Save()
end


return halloween_activity_slot_machine_setting_data