local reward_mgr = require "reward_mgr"
local log = require "log"
local string = string
local tostring = tostring

--老虎机进度面板上的一个进度item
---@class slot_game_progress_item
local slot_game_progress_item = {}

---@class slot_game_progress_item_data
local slot_game_progress_item_data = {
    reward_id = 0,
    progress = 0,
    bShowLine = false,
    bProgressEnough = false,
    bTaken = false,
}

local activityRechargeLiuGuang = "art/effects/effects/effect_ui_payreward/prefabs/effect_ui_payreward.prefab"
local activityRechargeLiuGuangScale = 1.45
local activityGetLiuGuang = "art/effects/effects/effect_ui_getreward/prefabs/effect_ui_getreward.prefab"
local activityGetLiuGuangScale = 1.25

---@public Render
---@param rtf_item RectTransform
---@param parent_order int
---@param item_data slot_game_progress_item_data
---@param on_click function
function slot_game_progress_item.Render(rtf_item, parent_order, item_data, on_click)
    local rtf_reward_item_parent = rtf_item:Find("rtf_item_parent"):GetComponent("RectTransform")
    local txt_progress = rtf_item:Find("txt_progress"):GetComponent("Text")
    local img_line = rtf_item:Find("img_line"):GetComponent("Image")

    ---@type GoodsItem_new
    local goodsItem = reward_mgr.GetRewardItemList(item_data.reward_id,  rtf_reward_item_parent, function (id, count, reward_type, _, _ ,_, _goodsItem) 
        --log.Log("slot_game_progress_item.Render", "id:", id, "count:", count, "reward_type:", reward_type)
        on_click(item_data, id, count, reward_type, _goodsItem)
    end, 0.5)[1]

    local overrideEffectPath = nil
    local overrideEffectScale = nil

    local lockState = false
    local checkState = false
    local maskState = false
    local getState = false


    lockState = not item_data.bProgressEnough
    getState = item_data.bTaken
    maskState = not item_data.bProgressEnough
    if lockState then
        getState = false
        checkState = true
        overrideEffectPath = activityRechargeLiuGuang
        overrideEffectScale = activityRechargeLiuGuangScale
    else
        overrideEffectPath = activityGetLiuGuang
        overrideEffectScale = activityGetLiuGuangScale
    end
    
    --goodsList[key]:SetGoods(nil, goodsData.id, goodsData.num, onClick)
    --goodsItem:SetLockMaskEnable(lockState or maskState, checkState, false)
    goodsItem:SetBattleMaskEnable(getState)

    goodsItem:SetCustomEffectEnable(item_data.bProgressEnough and not item_data.bTaken, overrideEffectPath, overrideEffectScale, parent_order + 2, true, nil, true)

    txt_progress.text = tostring(item_data.progress)
    img_line.gameObject:SetActive(item_data.bShowLine == true)
end

return slot_game_progress_item