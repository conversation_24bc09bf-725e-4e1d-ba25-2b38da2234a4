local require = require
local typeof = typeof

local Image = CS.UnityEngine.UI.Image
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local GameObject = CS.UnityEngine.GameObject
local SpriteMask = CS.UnityEngine.SpriteMask
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local RectTransform = CS.UnityEngine.RectTransform


module("ui_halloween_recharge_reward_binding")

UIPath = "ui/prefabs/gw/activity/halloweenactivity/halloweenrechargereward/uihalloweenrechargereward.prefab"

WidgetTable ={
	img_Bg = { path = "img_Bg", type = Image, },
	img_Top = { path = "img_Top", type = Image, },
	txt_ActiveName = { path = "Active/txt_ActiveName", type = Text, },
	txt_AllTime = { path = "Active/Image/txt_AllTime", type = Text, },
	btn_Detail = { path = "btn_Detail", type = Button, event_name = "OnBtnDetailClickedProxy"},
	txt_Total = { path = "Image_1/txt_Total", type = Text, },
	img_Total = { path = "Image_1/img_Total", type = Image, },
	obj_DayTime = { path = "obj_DayTime", type = GameObject, },
	txt_DayTimeDes = { path = "obj_DayTime/txt_DayTimeDes", type = Text, },
	txt_DayTime = { path = "obj_DayTime/txt_DayTime", type = Text, },
	spm_effectMask = { path = "activity_rewards/spm_effectMask", type = SpriteMask, },
	sr_Reward = { path = "activity_rewards/sr_Reward", type = ScrollRect, },
	srt_reward = { path = "activity_rewards/sr_Reward/Viewport/srt_reward", type = ScrollRectTable, },
	obj_RewardItem = { path = "activity_rewards/sr_Reward/Viewport/srt_reward/obj_RewardItem", type = GameObject, },
	txt_Title = { path = "activity_rewards/sr_Reward/Viewport/srt_reward/obj_RewardItem/btnGo/txt_Title", type = Text, },
	sr_Progress = { path = "activity_rewards/progress/sr_Progress", type = ScrollRect, },
	srt_progress = { path = "activity_rewards/progress/sr_Progress/Viewport/srt_progress", type = ScrollRectTable, },
	sr_Level = { path = "activity_rewards/level/sr_Level", type = ScrollRect, },
	srt_level = { path = "activity_rewards/level/sr_Level/Viewport/srt_level", type = ScrollRectTable, },
	rtf_RewardItem1 = { path = "Scroll View/Viewport/rewardParent/rtf_RewardItem1", type = RectTransform, },
	rtf_RewardItem2 = { path = "Scroll View/Viewport/rewardParent/rtf_RewardItem2", type = RectTransform, },

}
