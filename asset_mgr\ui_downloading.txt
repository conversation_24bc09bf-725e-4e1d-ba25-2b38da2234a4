--@region FileHead
-- ui_downloading.txt ---------------------------------
-- author:  李婉璐
-- date:    8/21/2019 12:00:00 AM
-- ver:     1.0
-- desc:    后台下载相关信息
-------------------------------------------------
--@endregion 

--@region Require
local require   = require

local GameObject    = CS.UnityEngine.GameObject
local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text

local class                 = require "class"
local ui_base               = require "ui_base"
local util                  = require "util"
--@endregion 

--@region ModuleDeclare
module("ui_downloading")
--local interface = require "iui_downloading"
local window = nil
local UIDownloading = {}
local curNum = 0
local totalNum = 0
--@endregion 

--@region WidgetTable
UIDownloading.widget_table = {
--@region User
	progressText = {path = "Text", type = "Text",},
--@endregion 
}
--@endregion 

--@region WindowCtor
function UIDownloading:ctor(selfType)
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function UIDownloading:Init()
    self:SubscribeEvents()
	self.progressText.text = "当前下载进度:"..curNum.."/"..totalNum
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIDownloading:OnShow()
    self:UpdateUI()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnHide
--[[资源加载完成，被显示的时候调用]]
function UIDownloading:OnHide()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UIDownloading:UpdateUI()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowClose
function UIDownloading:Close()
    if self:IsValid() then
        self:UnsubscribeEvents()
    end
	self.__base:Close()
	curNum = 0
	totalNum = 0
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIDownloading:SubscribeEvents()
----///<<< Button Proxy Line >>>///-----

end --///<<< function

--@endregion 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIDownloading:UnsubscribeEvents()
--@region User
--@endregion 
end --///<<< function

function SetDownloadData(cur, total)
	curNum = cur
	totalNum = total
	if window and window.UIRoot and not util.IsObjNull(window.UIRoot) then
		window.progressText.text = "当前下载进度:"..curNum.."/"..totalNum
	end
end

--@region WindowInherited
local CUIDownloading = class(ui_base, nil, UIDownloading)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIDownloading()
        window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uidownloading.prefab", nil, GameObject.Find("UIRoot/Canvas").transform, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end