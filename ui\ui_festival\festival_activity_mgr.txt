-- festival_activity_mgr.txt ------------------------------------------
-- author:  赖嘉明
-- date:    2019.11.18
-- ver:     1.0
-- desc:    节日活动数据管理

--modified： liangqixian
--------------------------------------------------------------
local require = require
local os = os
local ipairs = ipairs
local type = type
local string = string
local tonumber = tonumber
local tostring = tostring
local pairs = pairs
local table = table
local print = print
local math = math
local main_slg_const = require "main_slg_const"
local event_activity_define = require "event_activity_define"
local table_util = require "table_util"

local net_script = require "net_script"
local net_login_module = require "net_login_module"
local bit = require "bit"
local event = require "event"
local game_scheme = require "game_scheme"
local activity_pb = require "activity_pb"

local util = require "util"
local topic_pb = require "topic_pb"
local ui_pop_mgr = require "ui_pop_mgr"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local log = require("log")
local reward_mgr = require "reward_mgr"
local ui_window_mgr = require "ui_window_mgr"
local ReviewingUtil = require "ReviewingUtil"
local festival_activity_cfg = require "festival_activity_cfg"
local lang = require "lang"
local player_mgr = require "player_mgr"
local task_order_util = require "task_order_util"
local gw_event_activity_define = require "gw_event_activity_define"
local table_util = require "table_util"
local dump = dump

local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local Application = CS.UnityEngine.Application
--local Uri = CS.System.Uri
local festivalActivityData = {}
local cacheFestivalActivityState = {}
local isLoginFirstReceiveData = true
-- local festivalActivityExtraTaskData = {}
local INVALID_INT_VALUE = 0X7FFFFFFF

local lastSendChristmasCardTime = nil
local christmasCardSendInfo = {}
local ProgressData = {}
local taskData = {}
local SuperMonthlyCardFestivalFisrtEnter = {}--超级月卡礼包活动首次进入

---@type table
---@field startSecond number 开始秒数
---@field endSecond number 结束秒数
local exchangeShopShowLimitData = nil

module("festival_activity_mgr")
showChristmasSocksRed = false
showChristmasGiftRed = false

redPackageType = {
    NewYear = 1, --春节
    Anniversary = 2, --周年庆
}

nowRedPackageType = nil

local valentineIntimacyHeadingCode = 23
local valentineArcheryHeadingCode = 24

local dragonBoatGiftData = {} --端午节活动充值数据
local isMissActivity = true -- 跳过热更后活动配置找不到

-- 开启log日志
logger = require("logger").new("sw_festival_activity_print", 0)
IsLogLevel = logger.IsLogLevel
Warning = logger.Warning
Warning0 = logger.Warning0
---------------------外部调用接口--------------------------------------------------------

--- 活动类型枚举
ActivityCodeType = festival_activity_cfg.ActivityCodeType

--- 设置最后一次赠送贺卡的时间
---@param time number
function SetLastSendChristmasCardTime(time)
    lastSendChristmasCardTime = time
    if GetAllChristCardIsOn() == 0 then
        christmasCardSendInfo = {}
    end
end

--[[获取最后一次赠送贺卡的时间]]
function GetAllChristCardIsOn()
    if lastSendChristmasCardTime == nil then
        return 1
    end

    local lastData = os.date("*t", lastSendChristmasCardTime)
    local curDate = os.date("*t", os.server_time())

    if lastData.year == curDate.year and lastData.month == curDate.month and lastData.day == curDate.day then
        return 0
    else
        return 1
    end
end

function ContainDbid(id)
    for _, v in pairs(christmasCardSendInfo) do
        if v == id then
            return true
        end
    end
    return false
end

--[[获取黑金卡领取次数]]
function GetAcitivityCardReceivedNum(topicID)
    local topicIDData = festivalActivityData[topicID]
    local cardReceiveNum_1 = festivalActivityData[topicID] and festivalActivityData[topicID].cardReceiveNum_1 or 0
    local cardReceiveNum_2 = festivalActivityData[topicID] and festivalActivityData[topicID].cardReceiveNum_2 or 0
    return cardReceiveNum_1, cardReceiveNum_2
end

--[[获取黑金卡是否购买]]
function GetAcitivityCardPurchased(topicID)
    local completeData_1 = GetAcitivityContentCompleteTimes1(topicID)
    return completeData_1 and completeData_1[1] or 0, completeData_1 and completeData_1[2] or 0
end

--[[获取圣诞节活动树ID]]
function GetAcitivityTreeID(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].christmasTreeID
end

--[[获取圣诞节活动树阶段]]
function GetAcitivityTreeLevel(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].christmasTreeLevel
end

--[[获取圣诞节活动树经验]]
function GetAcitivityTreeExp(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].christmasTreeExp
end



--[[获取活动识别码]]
function GetAcitivityCode(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].headingCode
end

--[[获取活动版本号]]
function GetAcitivityVersion(topicID)
    log.Error("获取版本号：已废弃，请更换成ActivityID来使用")
    return festivalActivityData[topicID] and festivalActivityData[topicID].versionNumber
end

--[[获取活动完成次数]]
function GetAcitivityCompleteTimes(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].activityCompleteTimes1 or 0, festivalActivityData[topicID] and festivalActivityData[topicID].activityCompleteTimes2 or 0
end
function GetActivityCompleteTimes2(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].activityCompleteTimes2 or 0
end
--[[获取活动签到天数]]
function GetAcitivitySignDays(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].activitySignDays
end

--[[获取活动签到具体数据]]
function GetAcitivitySignData(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].activitySignData
end

--[[通过天数获取活动签到具体数据]]
function GetAcitivitySignDataByDay(topicID, day)
    return festivalActivityData[topicID] and festivalActivityData[topicID].activitySignData and festivalActivityData[topicID].activitySignData[day]
end

--[[获取活动内容轮回完成次数]]
function GetActivityContentRountCompleteTime(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].activityContentRoundTimes or {}
end

--[[获取活动内容1完成次数]]
function GetAcitivityContentCompleteTimes1(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].activityContentCompleteTimes1 or {}
end

--[[获取活动内容2完成次数]]
function GetAcitivityContentCompleteTimes2(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].activityContentCompleteTimes2 or {}
end

--[[获取购买次数，当前升星之路用]]
function GetAcitivityContentBuyTimes(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].buyData
end

--[[获取进度活动总大将，春节活动用]]
function GetProgressRewardState(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].progressRewardState
end

--[[获取活动结束时间戳]]
function GetAcitivityEndStamp(topicID)
    if festivalActivityData[topicID] and festivalActivityData[topicID].endTimeStamp then

        return festivalActivityData[topicID] and festivalActivityData[topicID].endTimeStamp
    else
        if IsActivityOpen(topicID) then
            local cfg = GetActivityCofigByTopicID(topicID)
            if cfg then
                local timeEnd = festival_activity_cfg.GetActivityEndTimeStr(cfg.AtyID)
                local rankEndDate = util.SplitString(timeEnd, "-")
                local rankEndTime = util.SplitString(rankEndDate[4], ":")
                local rankEndStamp = util.GetTimeStamp(tonumber(rankEndDate[1]), tonumber(rankEndDate[2]), tonumber(rankEndDate[3]), tonumber(rankEndTime[1]), tonumber(rankEndTime[2]), tonumber(rankEndTime[3] or 0))
                return rankEndStamp
            else
                return 0
            end
        else
            return 0
        end
    end
end

--[[角色的活动是否开启]]
function IsActivityOpenForRole()
    local player_mgr = require "player_mgr"
    local createTime = player_mgr.GetRoleCreateTime() or 0
    local notOpenDay = game_scheme:InitBattleProp_0(796).szParam.data[0]
    local notOpenTime = notOpenDay * 86400

    local date = os.date("!*t", createTime)
    date.hour = 0
    date.min = 0
    date.sec = 0
    local createZeroTime = os.time(date)
    createTime = createZeroTime-- + util.GetTimeArea() * 3600

    if not createTime or (createTime > 0 and (os.server_time() < createTime + notOpenTime)) then
        return false
    end
    return true
end

--[[活动是否开启]]
function IsActivityOpen(topicID)
    --活动开启只判断结束时间
    return festivalActivityData[topicID] and festivalActivityData[topicID].endTimeStamp and festivalActivityData[topicID].endTimeStamp > os.server_time() or false
end

--[[获取开放等级]]
function GetActivityOpenLevel(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].openLv
end

--[[获得全部数据]]
function GetFestivalActivityData()
    return festivalActivityData or {}
end

--- 获取活动数据
---@param topicID number
function GetFestivalActivityDataByTopicID(topicID)
    return festivalActivityData[topicID]
end

--[[获取活动剩余时间]]
function GetActivityLeftTime(topicID)
    if IsActivityOpen(topicID) then
        if festivalActivityData[topicID] and festivalActivityData[topicID].endTimeStamp then
            return (festivalActivityData[topicID].endTimeStamp - os.server_time() > 0) and (festivalActivityData[topicID].endTimeStamp - os.server_time()) or 0
        else
            return 0
        end
    else
        return 0
    end
end

--[[获取排行榜结束时间戳]]
function GetActivityRankEndStamp(topicID)
    if IsActivityOpen(topicID) then
        local cfg = GetActivityCofigByTopicID(topicID)
        if cfg and cfg.nRankType ~= 0 then
            local rankEndDate = util.SplitString(cfg.nRankEndTime, "-")
            local rankEndTime = util.SplitString(rankEndDate[4], ":")
            local rankEndStamp = util.GetTimeStamp(tonumber(rankEndDate[1]), tonumber(rankEndDate[2]), tonumber(rankEndDate[3]), tonumber(rankEndTime[1]), tonumber(rankEndTime[2]), tonumber(rankEndTime[3] or 0))
            return rankEndStamp
        else
            return 0
        end
    else
        return 0
    end
end

--[[获取排行榜剩余时间]]
function GetActivityRankLeftTime(topicID)
    if IsActivityOpen(topicID) then
        local cfg = GetActivityCofigByTopicID(topicID)
        if cfg and cfg.nRankType ~= 0 then

            local rankEndDate = util.SplitString(cfg.nRankEndTime, "-")
            local rankEndTime = util.SplitString(rankEndDate[4], ":")
            local rankEndStamp = util.GetTimeStamp(tonumber(rankEndDate[1]), tonumber(rankEndDate[2]), tonumber(rankEndDate[3]), tonumber(rankEndTime[1]), tonumber(rankEndTime[2]), tonumber(rankEndTime[3] or 0))
            return rankEndStamp - os.server_time()
        else
            return GetActivityLeftTime(topicID)
        end
    else
        return 0
    end
end

--[[获取虫母皮肤礼包时间]]
function GetSkinGiftLeftTime()
    local allActivities = GetAllActivity()
    if allActivities then
        for key, activity in pairs(allActivities) do
            if (activity.activityCodeType == festival_activity_cfg.ActivityCodeType.ResidentSkinGift) then
                -- --print("获取虫母皮肤礼包时间",GetActivityLeftTime(_activity.topicID),GetAcitivityEndStamp(_activity.topicID))
                return GetAcitivityEndStamp(activity.topicID), GetActivityLeftTime(activity.topicID)
            end
        end
    end
    return 0
end

---根据festival_activity_cfg的ActivityCodeType来查找判断活动是否开启
function IsActivityOpenByCodeType(codeType)
    for k, v in pairs(festivalActivityData) do
        if v.activityCodeType == codeType then
            local endTime = GetAcitivityEndStamp(v.topicID)
            if endTime and endTime > 0 then
                return endTime > os.server_time()
            end
            return false
        end
    end
    return false
end

---根据festival_activity_cfg的ActivityCodeType来获取活动的结束时间
function GetActivityEndTimeByCodeType(codeType)
    for k, v in pairs(festivalActivityData) do
        if v.activityCodeType == codeType then
            local endTime = GetAcitivityEndStamp(v.topicID)
            if endTime then
                return endTime
            end
            return 0
        end
    end
    return 0
end

--- 是否在当前区服开启
---@param activityCfg table
---@return boolean
local function IsEnabledInCurWorld(activityCfg)
    if activityCfg.OpenServerId == "" then
        return true
    end

    local setting_server_data = require "setting_server_data"
    local loginWorldID = setting_server_data.GetLoginWorldID()

    local sub1 = util.SplitString(activityCfg.OpenServerId, ";")
    for _, _v in pairs(sub1) do
        local sub2 = util.SplitString(_v, "#", tonumber)              
        if sub2 then
            local range1, range2 = sub2[1], sub2[2]
            if range1 and not range2 then
                range2 = range1
            end
            if  range1 and range2  and loginWorldID >= range1 and loginWorldID <= range2  then
                return true
            end
        end
        --local sub3 = util.SplitString(_v, "-", tonumber)
        --for _, __v in pairs(sub3) do
        --    if __v == loginWorldID then
        --        return true
        --    end
        --end
    end

    --log.Log("PP* 区服ID不匹配！| loginWorldID: " .. tostring(loginWorldID) .. ", openServerId: " .. tostring(activityCfg.OpenServerId) .. ", headingCode: " .. tostring(activityCfg.headingCode) .. ")
    return false
end

--全局函数，外部调用接口
--是否在当前区服开启
function IsEnabledInCurWorld_Global(activityCfg)
    return IsEnabledInCurWorld(activityCfg)
end
function GetAllActivityOfAnyType()
    return GetAllActivity(nil, nil, nil, true)
end

--全局函数，外部调用接口
--是否在当前区服开启
function IsEnabledInCurWorld_Global(activityCfg)
    return IsEnabledInCurWorld(activityCfg)
end

--- 获取所有开启的活动
---@param actType number 子活动归属的活动类型（1、新英雄活动 2、感恩节活动（带宝典） 3、节日活动 4、运营活动 5、战甲宝典 6、圣物活动 7、星河神殿活动, 9、周年庆）
---@param isNewbie boolean 是否为新手玩家
---@param newType number 新手活动类型（1、活动入口1 2、活动入口2）
---@return { topicID: number, activityID: number, headingCode: number, versionNumber: number, activityCodeType: number, parentsCode: number, parentsVersion: number, bindTopicID: number, actType: number }[]
function GetAllActivity(actType, isNewbie, newType, isAll)
    local activities = {}
    local associated = {}
    for _, v in pairs(festivalActivityData) do
        local activityCfg = GetActivityCofigByTopicID(v.topicID, isNewbie)

        if activityCfg then

            local limit = (not isNewbie) or ((not newType and (not activityCfg.newType or activityCfg.newType == 0 or activityCfg.newType == 1)) or (newType and activityCfg.newType == newType))
            local isMatchActType = isAll or actType == -1 or (((not actType) and (activityCfg.actType == 0 or activityCfg.actType == 2)) or (actType and (activityCfg.actType == actType)))
            --log.Warning("周活动isMatchActType",isMatchActType,limit,IsActivityOpen(v.topicID), v.activityID,IsEnabledInCurWorld(activityCfg))
            if limit and isMatchActType and IsActivityOpen(v.topicID) then

                -- 是否在当前区服开启
                if IsEnabledInCurWorld(activityCfg) then
                    -- 是否被另一个活动绑定，是另一个活动的子活动
                    local isBoundByOther = (activityCfg.bindActivity.count > 0 and activityCfg.bindActivity.data[0] == 2)
                    if (not isBoundByOther) then
                        -- 是否已解锁
                        local isUnlocked = ui_pop_mgr.CheckIsUnlock(activityCfg.levelLimite.data[0], activityCfg.levelLimite.data[1], nil, false)
                        -- log.Warning("周活动isUnlocked", v.activityID,isUnlocked)
                        if isUnlocked then
                            local data = {
                                topicID = v.topicID,
                                activityID = v.activityID,
                                headingCode = v.headingCode,
                                versionNumber = v.versionNumber,
                                activityCodeType = v.activityCodeType,
                                parentsCode = v.parentsCode,
                                parentsVersion = v.parentsVersion,
                                actType = activityCfg.actType,
                                popui = activityCfg.newHeroType
                            }

                            -- 拥有关联活动 需要单独处理
                            if activityCfg.associatedID and activityCfg.associatedID.count > 0 then
                                --local exist = false
                                local associatedID = activityCfg.associatedID.data[0]
                                if associated[associatedID] then
                                    --exist = true
                                else
                                    associated[associatedID] = {}
                                end
                                local associatedData = {
                                    topicID = 0,
                                    headingCode = 0,
                                    versionNumber = 0,
                                }
                                table.insert(associated[associatedID], associatedData)
                                if activityCfg.associatedID.data[1] == 2 then
                                    -- 规避关联活动多次添加
                                    -- if not exist then
                                    table.insert(activities, data)
                                    -- end
                                end
                            else
                                table.insert(activities, data)
                            end
                        end
                    end
                end

                -- 绑定了另一个需要显示切页的独立活动
                if activityCfg.bindActivity.count > 0 and activityCfg.bindActivity.data[0] == 3 then
                    local bindActivityID = activityCfg.bindActivity.data[1]
                    local bindActivityCfg = GetActivityCfgByAtyID(bindActivityID, isNewbie)
                    if bindActivityCfg then
                        -- 是否已解锁
                        local isUnlocked = ui_pop_mgr.CheckIsUnlock(bindActivityCfg.levelLimite.data[0], bindActivityCfg.levelLimite.data[1], nil, false)
                        if isUnlocked then
                            local data = {
                                topicID = 0,
                                activityID = bindActivityID,
                                headingCode = bindActivityCfg.headingCode,
                                versionNumber = bindActivityCfg.versionNumber,
                                activityCodeType = bindActivityCfg.codeType,
                                bindTopicID = v.topicID,
                                parentsCode = v.parentsCode,
                                parentsVersion = v.parentsVersion,
                                actType = bindActivityCfg.actType,
                                popui = bindActivityCfg.newHeroType
                            }

                            -- 拥有关联活动 需要单独处理
                            if bindActivityCfg.associatedID and bindActivityCfg.associatedID.count > 0 then
                                --local exist = false
                                local associatedID = bindActivityCfg.associatedID.data[0]
                                if associated[associatedID] then
                                    --exist = true
                                else
                                    associated[associatedID] = {}
                                end
                                local associatedData = {
                                    topicID = 0,
                                    headingCode = 0,
                                    versionNumber = 0,
                                }
                                table.insert(associated[associatedID], associatedData)
                                if bindActivityCfg.associatedID.data[1] == 2 then
                                    -- 规避关联活动多次添加
                                    -- if not exist then
                                    table.insert(activities, data)
                                    -- end
                                end
                            else
                                table.insert(activities, data)
                            end
                        end
                    end
                end

            end
        end
    end

    return activities
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--根据开启活动的识别号，插入对应ID的活动
InsertConfig = {
    [6] = 99998,
}
--插入其他奇奇怪怪的页面（需要配置festivalActivity.csv 和 FestivalUIDetail.csv）
function InsertOtherPage(activityData, topicID)
    if InsertConfig[activityData.headingCode] then
        local data = {
            topicID = nil,
            headingCode = 29,
            versionNumber = 1,
            activityCodeType = festival_activity_cfg.ActivityCodeType.NonActivity,
            endTimeStamp = activityData.endTimeStamp,
            parentsTopicID = topicID,
            parentsCode = activityData.headingCode,
            parentsVersion = activityData.versionNumber,
        }
        festivalActivityData[0] = data
    end
end

--[[获得优先级最高的活动]]
function GetHeightestPriorityActitity(actType, isNewbee, newType)
    local activity = nil
    local priority = 0
    local allActivity = GetAllActivity(actType, isNewbee, newType)
    for k, v in ipairs(allActivity) do

        if v.parentsCode and InsertConfig[v.parentsCode] then
            ------ --print("排除插入页面")
        else
            --2021.1.21 曾琳琳 排除插入虫母皮肤礼包
            if (v.activityCodeType ~= festival_activity_cfg.ActivityCodeType.ResidentSkinGift) then
                local cfg = GetActivityCfgByAtyID(v.activityID, isNewbee)
                if cfg and cfg.Seqencing > priority then
                    priority = cfg.Seqencing
                    activity = v
                end
            end
        end
    end
    return activity
end

--[[获得当前活动版本]]
function GetNowVersion()
    log.Error("获取版本号：已废弃，请更换成ActivityID来使用")
    local data = GetHeightestPriorityActitity()
    ------ print("获得当前活动版本>>>>>>",data.versionNumber)
    return data.versionNumber
end

--[[通过活动ID获得节日活动UI入口配置]]
function GetActivityUICofig(activityID, isNewbee)
    log.Error("获取活动入口配置：已废弃，请更换成GetActivityEntranceCfgByActivityID来使用")
    local cfg = GetActivityCfgByAtyID(activityID, isNewbee)
    if cfg and cfg.uiConfigID then
        return festival_activity_cfg.GetActivityEntranceCfg(cfg.uiConfigID)
    end
    return nil
end

--[[通过活动识别码和活动版本号获得topicID]]
function GetTopicIDByCodeAndVersion(headingCode, versionNumber)
    log.Error("GetTopicIDByCodeAndVersion 已废弃，请替festival_activity_mgr.GetTopicIDByActivityID(GetTopicIDByActivityID)")
    for k, v in pairs(festivalActivityData) do
        if v.headingCode == headingCode and v.versionNumber == versionNumber then
            return k
        end
    end
    return nil
end

--[[获取三选一兑换的神器索引]]
function GetArtifactIndex(topicID, hotFlag)
    if hotFlag == 1 then
        return festivalActivityData[topicID] and festivalActivityData[topicID].artifactIndex1 and festivalActivityData[topicID].artifactIndex1 + 1 or -1
    elseif hotFlag == 2 then
        return festivalActivityData[topicID] and festivalActivityData[topicID].artifactIndex2 and festivalActivityData[topicID].artifactIndex2 + 1 or -1
    end
    return -1
end

--local activityType = 
--{
--    [0] = "warmup",--活动前瞻
--    [1] = "sign", -- 签到
--    [2] = "rank", -- 排行榜
--    [3] = "exchange", -- 兑换
--    [4] = "tree", --圣诞树
--    [5] = "card", --黑金卡
--    [6] = "gift", --圣诞礼包
--    [7] = "content", --购买活动
--    [8] = "prosses", --积分活动
--    [9] = "anniversary", --周年庆活动
--}

function GetRedTips(activityID, isNewbee)
    local cfg = nil
    if isNewbee then
        cfg = festival_activity_cfg.GetNewbieActivityCfgByAtyID(activityID)
    else
        cfg = festival_activity_cfg.GetActivityCfgByAtyID(activityID)
    end
    if cfg then
        for index_2 = 0, cfg.uidetailID.count - 1 do
            local detailConfig = game_scheme:FestivalUIDetail_0(cfg.uidetailID.data[index_2])
            if not detailConfig then
                log.Error(" missing acyID ", activityID, " uidetail >> ", cfg.uidetailID.data[index_2])
            end
            if detailConfig and GetRedByType(cfg, detailConfig.toggle, isNewbee) then
                -- print("redcfg",cfg.uiConfigID,detailConfig.toggle)
                return true
            end
        end
    end
    return false
end

local redTypeArr = {}
function DiscoverRedByType(uidetailID)
    local detailConfig = game_scheme:FestivalUIDetail_0(uidetailID)
    redTypeArr[uidetailID] = true
end

--type:UIDestail中配置
function GetRedByType(cfg, _type, isNewbee, topicID)
    if not cfg then
        return false
    end
    local uiCfgID = cfg.uidetailID

    --绑定活动有红点就显示
    if cfg.bindActivity and cfg.bindActivity.count > 1 and cfg.bindActivity.data[0] == 1 then
        local bindCfg = nil
        if isNewbee then
            bindCfg = festival_activity_cfg.GetNewbieActivityCfgByAtyID(cfg.bindActivity.data[1])
        else
            bindCfg = festival_activity_cfg.GetActivityCfgByAtyID(cfg.bindActivity.data[1])
        end
        if bindCfg then
            local bindUICfg = game_scheme:FestivalUIDetail_0(bindCfg.uidetailID.data[0])
            if bindUICfg then
                if GetRedByType(bindCfg, bindUICfg.toggle, isNewbee) then
                    return true
                end
            end
        end
    end

    --- 活动类型码
    local activityCodeType = cfg.codeType

    if _type == 1 then
        local topicID = GetTopicIDByActivityID(cfg.AtyID)
        if topicID then
            local day = GetAcitivitySignDays(topicID)
            if day then
                for i = 1, day do
                    if GetAcitivitySignDataByDay(topicID, i) == 0 then
                        return true
                    end
                end
            else
                log.Error("day is nil", "topicID:", topicID)
            end
        end
    elseif _type == 2 and uiCfgID then
        local uicfg = game_scheme:FestivalUIDetail_0(uiCfgID.data[0] or 0)
        if not uicfg then
            return false
        end
        local itemID = uicfg.itemID
        local skep_mgr = require "skep_mgr"
        local entity = skep_mgr.GetGoodsEntity(itemID)
        if entity and entity:GetGoodsNum() > 0 then
            return true
        end
    elseif _type == 3 then
        local player_mgr = require "player_mgr"
        local ctnList = cfg.ctnID1
        for i = 0, ctnList.count - 1 do
            local ctn = ctnList.data[i]
            local ctnCfg = game_scheme:ActivityContent_0(ctn)
            if ctnCfg then
                local need = util.SplitString(ctnCfg.expenditure, "#", tonumber)
                local item = need[1]
                local itemNum = need[2]
                if itemNum and item then
                    if itemNum > 1 and player_mgr.GetPlayerOwnNum(item) >= itemNum then
                        local isConfict = IsContentConflict(ctnCfg.ConflictID)
                        if not isConfict then
                            return true
                        end
                    end
                else
                    local log = require "log"
                    log.Error("读取物品错误，ContentID = ", ctn, "goodsID = ", item)
                end
            end
        end

    elseif _type == 4 then
        local topicID = GetTopicIDByActivityID(cfg.AtyID)
        local treeID = GetAcitivityTreeID(topicID)
        if treeID == nil then
            return false
        end
        local treeCfg = game_scheme:ChristmasTree_0(treeID)
        if treeCfg == nil then
            return false
        end

        local itemID = treeCfg.CostItem
        local player_mgr = require "player_mgr"
        local num = player_mgr.GetPlayerOwnNum(itemID)
        return num > 0
    elseif _type == 5 then
        local topicID = GetTopicIDByActivityID(cfg.AtyID)
        return GetPerDayCardRed(topicID)
    elseif _type == 6 then
        return GetPerDayGiftRed(cfg)
    elseif _type == 8 then
        return GetProsseseActivityRed(cfg)
    elseif _type == 9 then
        return GetAnniversaryActivityRed(cfg)
    elseif _type == 0 then
        -- 活动前瞻
        local activity_mgr = require "activity_mgr"
        return not activity_mgr.GetWarmUpState()
    elseif _type == 10 then
        --TODO:徽章活动红点
        local reddot = GetBadgeActivityRed(cfg, topicID)
        return reddot
    elseif _type == 13 then
        -- local monopoly_data = require "monopoly_data"
        -- --print(396,"获取大富翁红点 >>>> ",cfg.AtyID,cfg.ActivityItem)
        -- --dump(cfg.ActivityItem)
        -- return monopoly_data.GetRedDotSum(cfg.ActivityItem)
    elseif _type == 14 then
        -- local new_week_activity_data = require "new_week_activity_data"
        -- --  --print("获取宝典红点 >>>> ",new_week_activity_data.GetAllRed())
        -- return new_week_activity_data.GetAllRed(nil, true)

    elseif _type == 15 then
        -- local activity_flop_data = require "activity_flop_data"
        -- if activity_flop_data.IsInit() then
        --     if not activity_flop_data.GetBigReward() then
        --         return true
        --     end

        --     local costItem = activity_flop_data.GetCostItem()

        --     local skep_mgr = require "skep_mgr"
        --     local player_mgr = require "player_mgr"
        --     local num = player_mgr.GetPlayerOwnNum(costItem.id or skep_mgr.const_id.christmasKey)
        --     return num >= (costItem.num or 1)
        -- else
        --     util.DelayOneCall("REQ_FlippingInfo", function()
        --         local net_activity_flop_module = require "net_activity_flop_module"
        --         net_activity_flop_module.REQ_FlippingInfo(cfg.AtyID)
        --     end, 1.5)
        -- end
    elseif _type == 16 then
        --情人节亲密度活动
        local topicID = GetTopicIDByActivityID(cfg.AtyID)

        local allMax = true
        for i = 0, game_scheme:ActivityIntimacy_nums() - 1 do
            local cfg = game_scheme:ActivityIntimacy(i)
            if cfg and cfg.versionNumber == cfg.versionNumber then
                local intimacy, level = GetValentineIntimacyByID(topicID, cfg.ID)
                local maxLevel = cfg.IntimacyLevel.data[cfg.IntimacyLevel.count - 1]
                if level < maxLevel then
                    allMax = false
                    break
                end
            end
        end
        if allMax then
            return false
        end
        local giftList = {}
        local cfg_gift = game_scheme:InitBattleProp_0(680).szParam
        for i = 0, cfg_gift.count - 1 do
            giftList[i + 1] = cfg_gift.data[i]
        end
        for k, v in pairs(giftList) do
            local player_mgr = require "player_mgr"
            if player_mgr.GetPlayerOwnNum(v) > 0 then
                return true
            end
        end
        return false
    elseif _type == 17 then
        --情人节射箭活动
        local topicID = GetTopicIDByActivityID(cfg.AtyID)
        local curRound = GetValentineArcheryCurRound(topicID)
        local buyRound = GetValentineArcheryBuyTimes(topicID)
        local remainRoundNum = GetValentineArcheryFreeRound() + buyRound - curRound
        local result = remainRoundNum > 0
        if not result then
            result = GetValentineArcheryIsNextDay(topicID)
        end
        return result
    elseif _type == 35 then
        --情人节特惠礼包活动
        local topicID = GetTopicIDByActivityID(cfg.AtyID)
        if topicID then
            ValentinePreferentialDayRefresh(topicID)
            local data = GetValentinePreferentialRedPoint(topicID)
            for index, value in ipairs(data) do
                if value == 1 then
                    return true
                end
            end
        end
        return false
    elseif _type == 18 then
        -- local activity_bingo_data = require "activity_bingo_data"
        -- return activity_bingo_data.GetSumBingoRed()
    elseif _type == 19 then
        event.Trigger(event.UPDATE_BINGO_RED)
    elseif _type == 22 then
        local topicID = GetTopicIDByActivityID(cfg.AtyID)
        if (activityCodeType == festival_activity_cfg.ActivityCodeType.BossChallengeV2) then
            -- 2022 中秋节 圆月之战
            -- local festival_boss_challenge_v2_data = require "festival_boss_challenge_v2_data"
            -- return festival_boss_challenge_v2_data.HasRedDot(topicID, cfg)
        elseif (activityCodeType == festival_activity_cfg.ActivityCodeType.BossChallenge) then
            -- Boss试炼
            -- local activity_boss_challenge = require "activity_boss_challenge"
            -- return activity_boss_challenge.IsBossChallengeRed(topicID, cfg)
        end
    elseif _type == 23 then
        --升星星之路
        -- local activity_boss_challenge = require "activity_boss_challenge"
        -- return activity_boss_challenge.SetTodayRed()
        -- return true
    elseif _type == 24 then
        --集字活动
        local reddot = GetCollectLetterReddot(cfg.headingCode, cfg.versionNumber)
        return reddot
    elseif _type == 27 then
        --周卡（签到形式）
        local topicID = GetTopicIDByActivityID(cfg.AtyID)

        local signDay = GetAcitivitySignDays(topicID)
        local freeGetState = GetAcitivitySignDataByDay(topicID, signDay)
        --免费奖励没领
        if freeGetState == 0 then
            return true
        end

        --是否购买付费奖励
        local buyTimesData = GetAcitivityContentCompleteTimes1(topicID)
        local buyTimes = buyTimesData and buyTimesData[1] or 0
        if buyTimes == 0 then
            return false
        end
        --付费奖励领取次数
        local getTimesData = GetAcitivityContentCompleteTimes2(topicID)
        local getTimes = 0
        for k, v in ipairs(getTimesData) do
            if signDay >= k and v == 1 then
                getTimes = getTimes + 1
            end
        end
        --是否有付费奖励可以领取
        return getTimes < signDay
    elseif _type == 28 then
        -- 英雄试炼
        local hero_exclusive_mgr = require "hero_exclusive_mgr"
        local reddot = hero_exclusive_mgr.GetChallengeReddot(isNewbee)
        return reddot
    elseif _type == 29 then
        -- 有限圣诞树
        if (activityCodeType == festival_activity_cfg.ActivityCodeType.ChristmasTree) then
            local topicID = GetTopicIDByActivityID(cfg.AtyID)
            local reddot = false
            local treeID = GetAcitivityTreeID(topicID) or 1
            local treeExp = GetAcitivityTreeExp(topicID) or 0
            local treeCfg = game_scheme:ChristmasTree_0(treeID)
            local totalExp = treeCfg.stageExpMax.data[treeCfg.stageNum - 1]
            local isMax = treeExp >= totalExp
            local player_mgr = require "player_mgr"
            local itemCount = player_mgr.GetPlayerOwnNum(treeCfg.CostItem) or 0
            reddot = (itemCount > 0) and not isMax
            return reddot
        end
    elseif _type == 30 then
        -- 无限圣诞树
        if (activityCodeType == festival_activity_cfg.ActivityCodeType.ChristmasTree) then
            local topicID = GetTopicIDByActivityID(cfg.AtyID)
            local reddot = false
            local treeID = GetAcitivityTreeID(topicID) or 1
            local treeCfg = game_scheme:ChristmasTree_0(treeID)
            local player_mgr = require "player_mgr"
            local itemCount = player_mgr.GetPlayerOwnNum(treeCfg.CostItem) or 0
            reddot = (itemCount > 0)
            return reddot
        end
    elseif (_type == 42) then
        --端午节抽奖活动
        return HasDrawCradItem()
    elseif _type == 45 then
        -- （通用）节日活动小游戏
        -- if cfg then
        --     local festival_game_mgr = require "festival_game_mgr"
        --     local player_mgr = require "player_mgr"
        --     local goodsNum = player_mgr.GetPlayerOwnNum(cfg.ActivityItem.data[0])
        --     return goodsNum > 9 and festival_game_mgr.GetRealSteps() < festival_game_mgr.GetRealMaxSteps()
        -- end
        ---- 清明节活动小游戏（踏青）
        --if cfg then
        --    local festival_hiking_mgr = require "festival_hiking_mgr"
        --    local player_mgr = require "player_mgr"
        --    local goodsNum = player_mgr.GetPlayerOwnNum( cfg.ActivityItem.data[0])
        --    return goodsNum>9 and festival_hiking_mgr.GetPosId() < festival_hiking_mgr.GetMaxPosId()
        --end
    elseif _type == 51 then
        --甜品大师
        -- local festival_make_foods_game_mgr = require "festival_make_foods_game_mgr"
        -- local reddot = festival_make_foods_game_mgr.AllFoodsShowRed()
        -- return reddot
        -- 玩家属性值变化监听
    elseif _type == 46 then
        -- （通用）节日活动皮肤礼包
        event.Trigger(event.FESTIVAL_SKIN_GIFT)
    elseif _type == 47 then
        return GetZhounianLoginActivityRed(cfg)
    elseif (_type == 52) then
        --英雄试用红点
        --英雄试用活动开启，且状态未领取
        local hero_trial_mgr = require "hero_trial_mgr"
        return hero_trial_mgr.GetHeroTrialRedDot(cfg.AtyID)
    elseif _type == 58 then
        -- 双旦救援
        local pick_the_route_mgr = require "pick_the_route_mgr"
        return pick_the_route_mgr.GetRedTip(cfg.AtyID)
    elseif _type == 60 then
        -- local halloween_day_mgr = require "halloween_day_mgr"
        -- return halloween_day_mgr.GetRedTip()
    elseif _type == 59 then
        -- 双旦拼团
        -- local christmas_newyear_mgr = require "christmas_newyear_mgr"

        -- return christmas_newyear_mgr.GetChristmasNewyearRedTip()
    elseif _type == 61 then
        local topicID = GetTopicIDByActivityID(cfg.AtyID)
        local drawSugarmanData = GetDrawSugarmanData(topicID)
        local dayFreeTimes = GetDrawSugarmanDayFreeTimes()
        if drawSugarmanData and drawSugarmanData.buyTimes + dayFreeTimes - drawSugarmanData.curTurn > 0 then
            return true
        else
            return false
        end
    -- elseif _type == 62 then
    --     --新春祝福
    --     local new_year_blessing_mgr = require "new_year_blessing_mgr"
    --     return new_year_blessing_mgr.GetRedTip()
    elseif _type == 70 then
        --新春福袋（弹窗改页签）
        -- local Chinese_new_year_mgr = require "Chinese_new_year_mgr"
        -- local rewardState = Chinese_new_year_mgr.IsGotNewYearReward()
        -- local gotReward = rewardState and rewardState == 1
        -- local canGetReward = Chinese_new_year_mgr.IsCanGetNewYearReward()
        -- return (canGetReward and not gotReward)
    -- elseif (_type == 71) then
    --     -- 翻牌小游戏
    --     local festival_turn_over_card_mgr = require "festival_turn_over_card_mgr"
    --     return festival_turn_over_card_mgr.RedPoint()
    end
    return false
end

--端午节活动抽卡道具
function HasDrawCradItem()
    local DrawCardRewardId = game_scheme:InitBattleProp_0(2706).szParam.data[0] -- 端午节抽奖物品
    local DrawCardItemId = reward_mgr.GetRewardGoods(DrawCardRewardId).id
    local player_mgr = require "player_mgr"
    local itemCount = player_mgr.GetPlayerOwnNum(DrawCardItemId) or 0
    if itemCount > 0 then
        return true
    end
end

--运营活动皮肤触发事件
function UpdateOperateSkinRedTips(actType, isNewbee, newType)
    --如果运营活动有皮肤，那么触发皮肤弹窗事件
    local activity = GetAllActivity(actType, isNewbee, newType)
    for i, activity in ipairs(activity) do
        local cfg = GetActivityCfgByAtyID(activity.activityID, isNewbee)
        if cfg and actType == 4 and cfg.headingCode == 107 then
            --遍历一遍
            GetRedTips(cfg.AtyID, isNewbee)
        end
    end
    return false
end
function GetCollectLetterReddot(headingCode, versionNumber)
    local collectLetterReddot = false
    local cfg = festival_activity_cfg.GetActivityCfgByCodeAndVersion(headingCode, versionNumber)
    local topicID = GetTopicIDByActivityID(cfg.AtyID)
    local totalTime = GetAcitivityContentCompleteTimes1(topicID)
    -- 周年庆两期兑换活动会根据时间隐藏某个兑换活动
    local nowTimestamp = os.server_time()
    local startTimestamp = GetActivityStartTime(topicID)
    local limitData = GetExchangeShopShowLimitData()
    if cfg then
        local cardEntity
        local skep_mgr = require "skep_mgr"
        for i = 0, cfg.ctnID1.count do
            local ctnID = cfg.ctnID1.data[i]
            local limitInfo = limitData[ctnID]
            local isShow = true
            if limitInfo then
                if startTimestamp + limitInfo.startSecond > nowTimestamp then
                    isShow = false
                elseif startTimestamp + limitInfo.endSecond < nowTimestamp then
                    isShow = false
                end
            end
            if ctnID and ctnID ~= 0 and isShow then
                local ctnCfg = game_scheme:ActivityContent_0(ctnID)
                if ctnCfg then
                    local curTime = totalTime[i + 1] and totalTime[i + 1] or 0
                    local isNoLimit = ctnCfg.LimitNumber == 0
                    local tarTime = ctnCfg.LimitNumber
                    local isComplete
                    if isNoLimit then
                        isComplete = false
                    else
                        isComplete = curTime >= tarTime
                    end
                    if not isComplete then
                        if not cardEntity then
                            local cardID = ctnCfg.SpecialItem.data[0] -- 万能卡id
                            cardEntity = skep_mgr.GetGoodsEntity(cardID)
                        end
                        local curCardCount = cardEntity and cardEntity:GetGoodsNum() or 0
                        local group = string.split(ctnCfg.expenditure, ';')
                        local itemList = {}
                        for k, v in ipairs(group) do
                            if v then
                                local data = string.split(v, '#', tonumber)
                                itemList[k] = { id = data[1], num = data[2] }
                            end
                        end

                        local isEnough = true
                        local cardEnough = true -- 算上万能卡是否充足
                        for i = 1, 4 do
                            local index = i - 1
                            local data = itemList[i]
                            if data and data.num then
                                local entity = skep_mgr.GetGoodsEntity(data.id)
                                local curCount = entity and entity:GetGoodsNum() or 0
                                if curCount + curCardCount / data.num < 1 then
                                    curCardCount = curCardCount - (data.num - curCount) -- 每一回合都减少一些
                                    if cardEnough and curCardCount < 0 then
                                        cardEnough = false
                                    end
                                end
                                if curCount / data.num < 1 then
                                    isEnough = false
                                end
                            end
                        end
                        -- if isEnough or cardEnough then
                        if isEnough then
                            collectLetterReddot = true
                            break
                        end
                    end
                end
            end
        end
    end
    return collectLetterReddot
end

--获取所有红点
function GetLobbyRedTips(actType, isNewbee, newType)
    local activity = GetAllActivity(actType, isNewbee, newType)
    for i, activity in ipairs(activity) do
        local cfg = GetActivityCfgByAtyID(activity.activityID, isNewbee)
        if cfg and ((actType == nil) or (actType == cfg.actType)) then
            local isred = GetRedTips(cfg.AtyID, isNewbee)
            if isred then
                return true
            end
        end
    end
    return false
end

--活动内容和当前活动冲突（用于特殊活动的条目开放）
function IsContentConflict(ConflictID)
    if ConflictID == 0 then
        return false
    end
    local allactivity = GetAllActivity()
    for key, value in pairs(allactivity) do
        if value.activityID == ConflictID then
            if GetActivityRankLeftTime(value.topicID) > 0 then
                return true
            end
            break
        end
    end
    return false
end

-----------------------------------------------------------------------------------------------------

function SetFestivalActivityData(topicID, bitData)
    festivalActivityData[topicID] = festivalActivityData[topicID] or {}
    local activityData = festivalActivityData[topicID]
    -- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

    local festival_activity_data = require "festival_activity_data"
    festival_activity_data.ProcessActivityData(topicID, bitData, activityData)

    -- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

    InsertOtherPage(activityData, topicID)

    -- 跳过热更后活动配置找不到
    if not isMissActivity and activityData and activityData.activityID == nil and activityData.endTimeStamp then
        local now = os.server_time()
        if now < activityData.endTimeStamp then
            isMissActivity = true
        end
    end
    
    if ReviewingUtil.IsReviewing() and activityData and activityData.activityID then
        if ReviewingUtil.GetActivityIsShieldByAtyID(activityData.activityID) then
            festivalActivityData[topicID] = nil
        end
    end
    
    -- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

    --event.Trigger(event.UPDATE_FESTIVAL_DATA, topicID)
    -- 编辑器内打印活动列表
    Warning(5, "PP*[新活动数据topic] > " ..
            "【" .. tostring(activityData.topicID) .. "】" ..
            "activityID: " .. tostring(activityData.activityID) ..
            ", headingCode: " .. tostring(activityData.headingCode) ..
            ", atyEntrance: " .. tostring(activityData.atyEntrance) ..
            ", topicID: " .. tostring(activityData.topicID) ..
            ", starttime: " .. tostring(activityData.startTimeStamp) ..
            ", endtime: " .. tostring(activityData.endTimeStamp) ..
            "\n\n\n"
    )
    -- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -    
    --先进行帧末操作，清空过时活动
    task_order_util.AddWaitForEndOfFrame(OnEndFrameFestivalActivitiesUpdate, true, 0)
    task_order_util.AddWaitForEndOfFrame(OnFestivalActivitiesUpdate, true, 1, activityData.activityID, activityData.headingCode)
    --有活动变化事件，抛出在最后
    task_order_util.AddWaitForEndOfFrame(OnHaveFestivalActivitiesUpdate, true, 1)
end

-----------------------------------------------------------------------------------------------------

local GameEventData = {}
--活动掉落打点关键数据摘录
function PickGameEventReport(festivalData, _topicID)
    GameEventData[12] = {}
    GameEventData[12]["prossesSumCount"] = festivalData.activityCompleteTimes1
    GameEventData[12]["topicID"] = _topicID
end

--获得打点相关数据
function GetGameEventReportData(iCode)
    return GameEventData[iCode]
end

--- 英雄重生，新版摘录祭坛商店相关数据
function PickDecomposeShopData(festivalData)
    if festivalData.endTimeStamp and festivalData.endTimeStamp > os.server_time() then
        local activity_mgr = require "activity_mgr"
        local contentConfig = GetActivityCfgByAtyID(festivalData.activityID)
        if contentConfig then
            local fakeActivityEntity = {
                activityID = contentConfig.AtyID,
                endTime = festivalData.endTimeStamp,
                content = {},
            }

            local ctnList = contentConfig.ctnID1
            for i = 0, ctnList.count - 1 do
                local ctn = ctnList.data[i]
                local completeData = festivalData.activityContentCompleteTimes1
                fakeActivityEntity["content"][i + 1] = {
                    contentID = ctn,
                    getNumber = completeData and completeData[i + 1] or 0
                }
                activity_mgr.PickDecomposeShopData(ctn, fakeActivityEntity)
            end
        end
    end
end

--圣诞树、复活蛋打点
local lastChristmasData = {}
local delayChecker = nil
function ChristmasActivityEventReport(_topicID)
    if delayChecker then
        util.RemoveDelayCall(delayChecker)
    end
    delayChecker = util.DelayCallOnce(0.1, function()
        local isNew = false
        if not lastChristmasData[_topicID] then
            lastChristmasData[_topicID] = { point_round = 0, point_stage = 0 }
            isNew = true
        end

        local treeID = GetAcitivityTreeID(_topicID) or 1
        local treeLevel = GetAcitivityTreeLevel(_topicID) or 0
        local treeCfg = game_scheme:ChristmasTree_0(treeID)
        local treeExp = GetAcitivityTreeExp(_topicID) or 0

        local treeExpMax = treeCfg and (treeCfg.stageExpMax.data[treeLevel - 1] or 100) or 100

        local isMax = false
        if treeExp == treeExpMax then
            isMax = true
        end
        local stage = 0
        if not isMax then
            if treeCfg then
                local rewards_0 = util.SplitString(treeCfg.stageReward, ";")
                for k, v in ipairs(rewards_0) do
                    if treeLevel > k then
                        stage = k
                    end
                end
            end
        else
            stage = 5
        end

        local newRound = treeID
        local newStage = stage

        local lastRound = lastChristmasData[_topicID].point_round
        local lastStage = lastChristmasData[_topicID].point_stage

        ------  --print("积分活动打点检测：Round = "..lastRound.."/"..newRound,"Stage = "..lastStage.."/"..newStage)

        if newRound ~= lastRound or newStage ~= lastStage then
            if not isNew then
                if newRound - lastRound == 0 then
                    for tStage = lastStage + 1, newStage do
                        RealChristmasTreeReport(newRound, tStage)
                    end
                else
                    for tRount = lastRound, newRound do
                        if tRount == lastRound then
                            --阶段比原来大，而且是原来的那个阶段
                            for tStage = lastStage + 1, 5 do
                                RealChristmasTreeReport(tRount, tStage)
                            end
                        elseif tRount < newRound and tRount > lastRound then
                            --阶段比原来大，是中间的那个阶段
                            for tStage = 1, 5 do
                                RealChristmasTreeReport(tRount, tStage)
                            end
                        elseif tRount == newRound then
                            --是最后一个阶段
                            for tStage = 1, newStage do
                                RealChristmasTreeReport(tRount, tStage)
                            end
                        end
                    end
                end
            end
            lastChristmasData[_topicID] = { point_round = newRound, point_stage = newStage }
        end
        util.RemoveDelayCall(delayChecker)
        delayChecker = nil
    end)
end

function RealChristmasTreeReport(_round, _stage)
    if _round ~= 0 and _stage ~= 0 then
        local player_mgr = require "player_mgr"
        local json = require "dkjson"
        local json_str = json.encode({
            role_name = tostring(player_mgr.GetRoleName()),
            point_round = _round,
            point_stage = _stage,
        })
        event.Trigger(event.GAME_EVENT_REPORT, "ChristmasTree_point", json_str)
        ------ --print("复活蛋活动打点>>>>>>>>>>",_round,_stage)
    end
end

--普通积分活动打点
local lastReportData = {}
function ProgressActivityEventReport(_topicID)
    if delayChecker then
        util.RemoveDelayCall(delayChecker)
    end
    delayChecker = util.DelayCallOnce(0.1, function()
        local isNew = false
        if not lastReportData[_topicID] then
            lastReportData[_topicID] = { point_round = 0, point_stage = 0 }
            isNew = true
        end

        --TODO:构建新打点数据
        local stage = 0
        local activityData = GetProgressData(_topicID)

        for k, content in pairs(activityData.contentList) do
            local curValue = content.value - (content.contentSum * activityData.realRepeatRound)
            local progress = curValue / tonumber(content.conditionTimes)
            if progress >= 1 then
                stage = k
            end
        end

        local newRound = activityData.realRepeatRound + 1
        local newStage = stage

        local lastRound = lastReportData[_topicID].point_round
        local lastStage = lastReportData[_topicID].point_stage

        ------  --print("积分活动打点检测：LastTime = ".. newRound.."/"..newStage,"LastTime = "..lastRound.."/"..lastStage,_count)
        -- dump(activityData)

        local _count = activityData.contentCount

        if newRound > lastRound or newStage > lastStage then
            if not isNew then
                if newRound - lastRound == 0 then
                    for tStage = lastStage + 1, newStage do
                        RealProgressActivityReport(newRound, tStage, activityData.maxRepeatTime)
                    end
                else
                    for tRount = lastRound, newRound do
                        if tRount == lastRound then
                            --阶段比原来大，而且是原来的那个阶段
                            for tStage = lastStage + 1, _count do
                                RealProgressActivityReport(tRount, tStage, activityData.maxRepeatTime)
                            end
                        elseif tRount < newRound and tRount > lastRound then
                            --阶段比原来大，是中间的那个阶段
                            for tStage = 1, _count do
                                RealProgressActivityReport(tRount, tStage, activityData.maxRepeatTime)
                            end
                        elseif tRount == newRound then
                            --是最后一个阶段
                            for tStage = 1, newStage do
                                RealProgressActivityReport(tRount, tStage, activityData.maxRepeatTime)
                            end
                        end
                    end
                end
            end
            lastReportData[_topicID] = { point_round = newRound, point_stage = newStage }
        end

        util.RemoveDelayCall(delayChecker)
        delayChecker = nil
    end)
end

function RealProgressActivityReport(_round, _stage, _maxRepeate)
    if _maxRepeate < _round then
        return
    end

    if _round ~= 0 and _stage ~= 0 then
        local player_mgr = require "player_mgr"
        local json = require "dkjson"
        local json_str = json.encode({
            role_name = tostring(player_mgr.GetRoleName()),
            point_round = _round,
            point_stage = _stage,
        })
        event.Trigger(event.GAME_EVENT_REPORT, "activity_point", json_str)
        ------ --print("积分活动打点！！！",_round,_stage)
    end
end

--- 新版徽章活动-道具累积获得，获取积分活动构建数据
function BuildProgressData(_topicID)

    local activityID = GetActivityIDByTopicID(_topicID)
    local isNewbieActivity = (headingCode == 61)
    local activityCfg = GetActivityCfgByActivityID(activityID, isNewbieActivity)
    if not activityCfg then
        return
    end

    local isProgress = false
    if activityCfg.associatedID and activityCfg.associatedID.count > 0 then
        isProgress = tonumber(activityCfg.associatedID.data[0]) == 2 -- 1类型为progress
    end
    if (not isProgress) then
        return
    end

    ProgressData[_topicID] = {
        isAllComplete = true,
        contentList = {}, --活动条目数据
        completeData = 0, --已经达到的总积分数
        repeatRound = 0, --当前积分达到的轮次
        realRepeatRound = 0, --当前界面所在的轮子
        contentCount = 0, --条目总数
        maxRepeatTime = 0, --最大轮次
    }

    local activity_mgr = require "activity_mgr"
    local dataList = {}
    local ctnList = activityCfg.ctnID1
    local completeData = GetAcitivityCompleteTimes(_topicID)
    local contentCompleteTime = GetAcitivityContentCompleteTimes1(_topicID)
    local contentSum, contnetEach = activity_mgr.GetActivityContentSum(ctnList.data)
    ProgressData[_topicID]["contentCount"] = ctnList.count
    local tempMiniRepear = 100
    for i = 0, ctnList.count - 1 do
        if contentCompleteTime[i + 1] then
            tempMiniRepear = math.min(tempMiniRepear, contentCompleteTime[i + 1])
            local ctn = ctnList.data[i]
            local data = {
                value = completeData,
                activityID = activityCfg.AtyID,
                contentID = ctn,
                type = activity_pb.ACTIVITY_TYPE_FESTIVAL,
                complete = contentCompleteTime[i + 1],
                contentSum = contentSum,
                conditionTimes = contnetEach[ctn]
            }
            table.insert(dataList, data)
            if contentCompleteTime[i + 1] < activityCfg.repeatTimes then
                ProgressData[_topicID]["isAllComplete"] = false
            end
        end
    end

    ProgressData[_topicID]["realRepeatRound"] = math.floor(completeData / contentSum)

    ProgressData[_topicID]["contentList"] = dataList

    ProgressData[_topicID]["maxRepeatTime"] = activityCfg.repeatTimes

    if activityCfg.repeatTimes > 1 then
        if tempMiniRepear + 1 > activityCfg.repeatTimes then
            tempMiniRepear = activityCfg.repeatTimes - 1
        end
        ProgressData[_topicID]["repeatRound"] = tempMiniRepear
    end
    ProgressData[_topicID]["completeData"] = completeData

    ProgressActivityEventReport(_topicID)
end

function GetProgressData(_topicID)
    return ProgressData[_topicID]
end

function GetAllProgressData()
    return ProgressData or {}
end

--[[解析徽章任务的当前完成值和当前轮次]]
function SetFestivalCtn1CompleteTimes(data)
    local ctn1 = bit.rshift(data, 16)
    local ctn2 = bit.band(data, 0xFFFF)
    local completeTime1 = bit.rshift(ctn1, 8)        --1-8位表示完成进度值
    local roundTime1 = bit.band(ctn1, 0xFF)          --9-16位表示完成轮次
    local completeTime2 = bit.rshift(ctn2, 8)        --17-24位表示完成进度值
    local roundTime2 = bit.band(ctn2, 0xFF)          --25-32表示完成轮次
    return completeTime1, roundTime1, completeTime2, roundTime2
end

--获得周年庆登陆红点
function GetZhounianLoginActivityRed(cfg)
    local topicID = GetTopicIDByActivityID(cfg.AtyID)
    local ctnList = cfg.ctnID1
    local completeData = GetAcitivityContentCompleteTimes1(topicID)

    if not completeData then
        return false
    end
    local createDay = GetActivityCompleteTimes2(topicID)

    for i = 0, ctnList.count - 1 do
        local ctn = ctnList.data[i]
        local ctnCfg = game_scheme:ActivityContent_0(ctn)
        if ctnCfg then
            --大于限定天数
            local cond = ctnCfg.conditionTimes.data[0]
            if cond and createDay and createDay >= cond and completeData[i + 1] and completeData[i + 1] == 0 then
                return true
            end
        else
            log.ErrorReport(log.ENUM_LOGTYPE.CONFIG, "请检查 ActivityContent表！读取活动content error，请检查配置！contentID=", ctn)
        end
    end
    return false
end

--获得徽章活动红点
function GetBadgeActivityRed(cfg, topicID)
    local activity_mgr = require "activity_mgr"
    local topicID = topicID and topicID or GetTopicIDByActivityID(cfg.AtyID)
    local ctnList = cfg.ctnID1
    local completeData = GetAcitivityCompleteTimes(topicID)

    if not completeData then
        return false
    end
    completeData = GetAcitivityContentCompleteTimes1(topicID)
    local roundData = GetActivityContentRountCompleteTime(topicID)
    --TODO:奖励可领取显示红点
    for i = 0, ctnList.count - 1 do
        local ctn = ctnList.data[i]
        local conCsv = game_scheme:ActivityContent_0(ctn)
        if not conCsv then
            log.Error(" missing ctn ", ctn)
        else
            local _condition = conCsv.conditionTimes.data[0]
            local value = completeData[i]
            local round = roundData[i]
            ------ --print("ID",conCsv.ID,"remark",conCsv.remark,"value",value,"_condition",_condition,"round",round,"LimitNumber",conCsv.LimitNumber)
            if value and _condition and value >= _condition and round and round < conCsv.LimitNumber then
                return true
            end
        end
    end
    return false
end

function SetFestivalChristmasCardSendData(data)
    if type(data) == "table" then
        for _, v in pairs(data) do
            table.insert(christmasCardSendInfo, v)
        end
    elseif type(data) == "number" then
        table.insert(christmasCardSendInfo, data)
    end
    event.Trigger(event.UPDATE_CHRISTMAS_CARD_INFO)
end

--获取每日黑金卡红点
function GetPerDayCardRed(topicid)
    local purcha_1, purcha_2 = GetAcitivityCardPurchased(topicid)
    local cardReceivedNum_1, cardReceivedNum_2 = GetAcitivityCardReceivedNum(topicid)

    local diffDay = GetPassActivityPassDay(topicid)

    local signDay = GetAcitivitySignDataByDay(topicid, diffDay)
    if signDay == 0 then
        return true
    end

    if purcha_1 >= 1 and diffDay - cardReceivedNum_1 > 0 then
        return true
    end

    if purcha_2 >= 1 and diffDay - cardReceivedNum_2 > 0 then
        return true
    end
    return false
end

--获得已经经过的天数
function GetPassActivityPassDay(topicid)
    local activityCfg = GetActivityCofigByTopicID(topicid)
    if activityCfg then
        local activityTimeCfg = festival_activity_cfg.GetActivityStartTimeStrByCfg(activityCfg)
        local dateStr = util.SplitString(activityTimeCfg, "-")
        local timeStr = util.SplitString(dateStr[4], ":")
        local startTime = util.GetTimeStamp(tonumber(dateStr[1]), tonumber(dateStr[2]), tonumber(dateStr[3]), tonumber(timeStr[1]), tonumber(timeStr[2]), tonumber(timeStr[3] or 0))
        local nowTime = net_login_module.GetServerTime()
        --print("开始时间 >>>> "..startTime,"结束时间>>>>"..nowTime,"天数 >>>> ",util.DiffDay(startTime,nowTime),string.countdown(nowTime-startTime),"蠢：",(nowTime - startTime)/60/60/24)
        --减去时差
        startTime = startTime - util.GetTimeAreaBySeconds()
        return string.countdown(nowTime - startTime) + 1
    else
        log.Error("没有找到活动配置！topicid = ", topicid)
    end
    return 0
end

function GetActivityStartTime(topicid)
    local activityCfg = GetActivityCofigByTopicID(topicid)
    if activityCfg then
        local activityTimeCfg = festival_activity_cfg.GetActivityStartTimeStrByCfg(activityCfg)
        local dateStr = util.SplitString(activityTimeCfg, "-")
        local timeStr = util.SplitString(dateStr[4], ":")
        local startTime = util.GetTimeStampInServerTimeZone(tonumber(dateStr[1]), tonumber(dateStr[2]), tonumber(dateStr[3]), tonumber(timeStr[1]), tonumber(timeStr[2]), tonumber(timeStr[3] or 0))
        return startTime
    else
        log.Error("没有找到活动配置！topicid = ", topicid)
    end
    return 0
end

---GetExchangeShopShowLimitData 获取兑换商店显示限制数据
---@return table exchangeShopShowLimitData 
function GetExchangeShopShowLimitData()
    if exchangeShopShowLimitData ~= nil then
        return exchangeShopShowLimitData
    end

    exchangeShopShowLimitData = {}
    local InitBattlePropCfg = game_scheme:InitBattleProp_0(2813)
    if InitBattlePropCfg == nil then
        log.Error("InitBattleProp not exist 2813")
        return exchangeShopShowLimitData
    end

    local limitData = InitBattlePropCfg.szParam
    local count = math.floor((limitData.count + 1) / 4)
    for i = 1, count do
        local id = limitData.data[(i - 1) * 4]
        local startHours = limitData.data[(i - 1) * 4 + 1]
        local endHours = limitData.data[(i - 1) * 4 + 2]
        if i < count then
            local verify = limitData.data[(i - 1) * 4 + 3]
            if verify ~= 0 then
                log.Error("Please Check InitBattleProp 2813, verify fail")
            end
        end
        exchangeShopShowLimitData[id] = {
            startSecond = startHours * 3600,
            endSecond = endHours * 3600
        }
    end

    return exchangeShopShowLimitData
end

--获取每日礼包红点
function GetPerDayGiftRed()
    -- local serverID = 0 --服务器id
    -- serverID = serverID or 0
    -- local timeStamp = net_login_module.GetServerTime()
    -- local szCurTime = os.date("%Y%m%d", timeStamp)
    -- local szPreShowTimeKey = string.format("ChristmasGiftRed_%d", serverID)
    -- local szPreShowTime = PlayerPrefs.GetString(szPreShowTimeKey, "0")
    -- local nCurTime = tonumber(szCurTime)
    -- local nPreShowTime = tonumber(szPreShowTime)

    -- if nCurTime - nPreShowTime >= 1 then
    --     return true
    -- end
    --return false
    --超级月卡礼包活动
    if cfg then
        local activity_mgr = require "activity_mgr"
        local player_mgr = require "player_mgr"
        local IsSuperMonthCard = activity_mgr.IsMonthCardUser()
        local topicID = GetTopicIDByActivityID(cfg.AtyID)
        local completeData = GetAcitivityContentCompleteTimes1(topicID) or {}
        local ctnList = cfg.ctnID1
        for i = 0, ctnList.count - 1 do
            local conCsv = game_scheme:ActivityContent_0(ctnList.data[i])
            if conCsv.integralType == 15 then
                if IsSuperMonthCard then
                    if (completeData[i + 1] or 0) < conCsv.LimitNumber then
                        return true
                    end
                else
                    -- local isFirstLogin = PlayerPrefs.GetInt(player_mgr.GetPlayerUserID() .. "FirstLogin",1)
                    if SuperMonthlyCardFestivalFisrtEnter[cfg.AtyID] == nil then
                        SuperMonthlyCardFestivalFisrtEnter[cfg.AtyID] = true
                    end
                    return SuperMonthlyCardFestivalFisrtEnter[cfg.AtyID]
                end
            end
        end
    end
    return false
end

function SetSuperMonthlyCardFestivalFisrtEnter(data)
    SuperMonthlyCardFestivalFisrtEnter = data
end

function GetSuperMonthlyCardFestivalFisrtEnter()
    return SuperMonthlyCardFestivalFisrtEnter
end

--获得积分活动红点
function GetProsseseActivityRed(cfg)
    local activity_mgr = require "activity_mgr"
    local topicID = GetTopicIDByActivityID(cfg.AtyID)
    local ctnList = cfg.ctnID1
    local completeData = GetAcitivityCompleteTimes(topicID)

    if not completeData then
        return false
    end
    local contentCompleteTime = GetAcitivityContentCompleteTimes1(topicID)
    local contentSum = activity_mgr.GetActivityContentSum(ctnList.data)

    local curValue = 0
    local tempMiniRepear = 100
    if cfg.repeatTimes > 1 then
        for i = 0, ctnList.count - 1 do
            tempMiniRepear = math.min(tempMiniRepear, contentCompleteTime[i + 1])
        end
        curValue = completeData - (contentSum * tempMiniRepear)
    else
        tempMiniRepear = 0
        curValue = completeData
    end

    for i = 0, ctnList.count - 1 do
        local ctn = ctnList.data[i]
        local conCsv = game_scheme:ActivityContent_0(ctn)
        if not conCsv then
            log.Error(" missing ctn ", ctn)
        end
        local _condition = conCsv.conditionTimes.data[0]
        if curValue and _condition and curValue >= _condition and contentCompleteTime[i + 1] and tempMiniRepear and contentCompleteTime[i + 1] <= tempMiniRepear then
            return true
        end
    end
    return false
end

-- 获取周年庆红点
function GetAnniversaryActivityRed(cfg)
    local activity_mgr = require "activity_mgr"
    local topicID = GetTopicIDByActivityID(cfg.AtyID)
    local ctnList = cfg.ctnID1
    local completeData = GetAcitivityCompleteTimes(topicID)

    if not completeData then
        return false
    end

    --[[
    local contentCompleteTime = GetAcitivityContentCompleteTimes1(topicID)
    local contentSum = activity_mgr.GetActivityContentSum(ctnList.data)

    local curValue = 0
    local tempMiniRepear = 100
    if cfg.repeatTimes > 1 then
        for i=0, ctnList.count-1 do
            tempMiniRepear = math.min(tempMiniRepear, contentCompleteTime[i+1])
        end
        curValue = completeData - (contentSum * tempMiniRepear)
    else
        tempMiniRepear = 0
        curValue = completeData
    end

    for i=0, ctnList.count-1 do
        local ctn = ctnList.data[i]
        local conCsv= game_scheme:ActivityContent_0(ctn)
        if not conCsv then
            log.Error(" missing ctn ",ctn)
        end
        local _condition = conCsv.conditionTimes.data[0]
        if curValue and _condition and curValue >= _condition and contentCompleteTime[i+1] and tempMiniRepear and contentCompleteTime[i+1] <= tempMiniRepear then
            return true
        end
    end
    --]]
    return false
end

--获取活动掉落奖励
function GetExtraDropRewardID(iType, isReport)
    if iType == 0 or ReviewingUtil.IsReviewing() then
        return nil
    end

    local allactiity = GetAllActivity()
    for k, _activity in pairs(allactiity) do
        local cfg = GetActivityCofigByTopicID(_activity.topicID)
        if cfg.nExtraDropType.count > 0 then
            local ui_pop_mgr = require "ui_pop_mgr"
            if ui_pop_mgr.CheckIsUnlock(cfg.levelLimite.data[0], cfg.levelLimite.data[1], nil, false) then
                local boxArr = util.SplitString(cfg.nExtraDropBox, ";")
                for k_2, value in pairs(cfg.nExtraDropType.data) do
                    if value == iType then
                        local temprewardTable = {}
                        local boxInfo = game_scheme:LotteryBox_0(tonumber(boxArr[k_2 + 1]))
                        if boxInfo then
                            for j = 1, 10 do
                                local reward = boxInfo["arrRandomRewardID" .. j]
                                for k = 1, reward.count do
                                    if reward.data[k - 1] > 0 then
                                        table.insert(temprewardTable, reward.data[k - 1])
                                    end
                                end
                            end
                        end
                        return temprewardTable
                    end
                end
            end
        end
    end
end

--获取活动掉落奖励，Table模式，节省计算次数
function GetExtraDropRewardIDInTable(typeTable)
    if typeTable == nil or isReviewingVersion then
        return {}
    end

    local allType = {}
    local result = {}
    local count = 0
    for key, value in ipairs(typeTable) do
        allType[value] = true
        result[value] = {}
        count = count + 1
    end

    local finishCount = 0
    local allactiity = GetAllActivity()
    for k, _activity in pairs(allactiity) do
        local cfg = GetActivityCofigByTopicID(_activity.topicID)
        if cfg.nExtraDropType.count > 0 then
            local ui_pop_mgr = require "ui_pop_mgr"
            if ui_pop_mgr.CheckIsUnlock(cfg.levelLimite.data[0], cfg.levelLimite.data[1], nil, false) then
                local boxArr = util.SplitString(cfg.nExtraDropBox, ";")
                for k_2, value in pairs(cfg.nExtraDropType.data) do
                    if allType[value] then
                        local temprewardTable = result[value]
                        local boxInfo = game_scheme:LotteryBox_0(tonumber(boxArr[k_2 + 1]))
                        if boxInfo then
                            for j = 1, 10 do
                                local reward = boxInfo["arrRandomRewardID" .. j]
                                for k = 1, reward.count do
                                    if reward.data[k - 1] > 0 then
                                        table.insert(temprewardTable, reward.data[k - 1])
                                    end
                                end
                            end
                        end
                        finishCount = finishCount + 1
                        if finishCount == count then
                            break
                        end
                    end
                end
            end
            if finishCount == count then
                break
            end
        end
    end

    return result
end

--获得打点物品ID
function GetFestivalExtraDropID(_topicID)
    ------  --print("获得打点物品ID>>>>>",_topicID)
    local cfg = GetActivityCofigByTopicID(_topicID)
    return cfg and cfg.topItemID.data[0] or 0
end

function ShowReward(eventname, rewardList, rewardListNew)
    local reward = {}
    if rewardList then
        for k, v in pairs(rewardList) do
            -- k: rewardID v:num
            local data = reward_mgr.GetRewardGoods(k)
            data.num = data.num * v
            local insert = true
            for m, n in pairs(reward) do
                if n.id == data.id then
                    n.num = n.num + data.num
                    insert = false
                    break
                end
            end
            if insert then
                table.insert(reward, data)
            end
        end
    end
    if rewardListNew then
        local temp = {}
        for k, v in ipairs(rewardListNew) do
            -- k: rewardID v:num
            local data = reward_mgr.GetRewardGoods(v)
            temp[data.id] = temp[data.id] or 0
            temp[data.id] = temp[data.id] + data.num
        end
        for k, v in pairs(temp) do
            table.insert(reward, { id = k, num = v })
        end
    end

    if #reward > 0 then
        -- if ui_window_mgr:IsModuleShown("ui_festival_prosses_get") then
        --     ui_window_mgr:UnloadModule("ui_festival_prosses_get")
        -- end

        -- if ui_window_mgr:IsModuleShown("ui_festival_prosses_pay") then
        --     ui_window_mgr:UnloadModule("ui_festival_prosses_pay")
        -- end

        local iui_reward = require "iui_reward"
        iui_reward.Show(reward, true)
    end
end
event.Register(event.FESTIVAL_PROSSES_RESPONSE_NEW, ShowReward)

function SetValentineIntimacyData(data, key, value)
    -- local version = nil
    -- if data then
    --     version = data[1]
    -- elseif key then
    --     if key == 0 then
    --         version = value
    --     end
    -- end
    -- if version and (version ~= INVALID_INT_VALUE) then
    --     if festivalActivityData then
    --         for k, v in pairs(festivalActivityData) do
    --             if v.headingCode == valentineIntimacyHeadingCode then
    --                 --版本号不相等则请求最新的亲密度数据
    --                 if v.versionNumber ~= version then
    --                     local activityCfg = GetActivityCofigByTopicID(k)
    --                     if activityCfg then
    --                         local net_festival_activity_module = require "net_festival_activity_module"
    --                         net_festival_activity_module.Request_ValentinesIntimacyInfo(activityCfg.AtyID)
    --                     end
    --                 end
    --                 break
    --             end
    --         end
    --     end
    -- end
end

function SetValentineIntimacyHeroData(tpID, data, key, value)
    --每个key对应10个英雄亲密度
    local offset = tpID % topic_pb.TOPICNAME_INTIMACY_HERO_START * 10
    local topicID = nil
    for k, v in pairs(festivalActivityData) do
        if v.headingCode == valentineIntimacyHeadingCode then
            topicID = k
            break
        end
    end
    if data then
        if topicID and festivalActivityData[topicID] then
            festivalActivityData[topicID].heroIntimacy = festivalActivityData[topicID].heroIntimacy or {}
            local length = #data
            for i = 1, length do
                if data[i] ~= INVALID_INT_VALUE then
                    festivalActivityData[topicID].heroIntimacy[i + offset] = data[i]
                end
            end
        end
    elseif key then
        if topicID and festivalActivityData[topicID] and (value ~= INVALID_INT_VALUE) then
            if not festivalActivityData[topicID].heroIntimacy then
                festivalActivityData[topicID].heroIntimacy = {}
            end
            festivalActivityData[topicID].heroIntimacy[key + 1 + offset] = value
            event.Trigger(event.UPDATE_VALENTINE_SEND_GIFT)
        end
    end
end

--情人节活动：通过ActivityIntimacy配置表id获取亲密度
function GetValentineIntimacyByID(topicID, id)
    if (not festivalActivityData[topicID]) or (not festivalActivityData[topicID].heroIntimacy) then
        return 0, 0
    end
    local intimacy = festivalActivityData[topicID].heroIntimacy[id] or 0
    local cfg = game_scheme:ActivityIntimacy_0(id)
    local index = nil
    for i = 0, cfg.Intimacy.count - 1 do
        if intimacy >= cfg.Intimacy.data[i] then
            index = i
        end
    end
    local intimacyLevel = 0
    if index then
        intimacyLevel = cfg.IntimacyLevel.data[index]
    end
    return intimacy, intimacyLevel
end

--情人节活动：通过等级获取亲密度总值及奖励id
function GetValentineTotalIntimacyByLevel(id, level)
    local cfg = game_scheme:ActivityIntimacy_0(id)
    if not cfg then
        return 0, {}
    end
    local reward = {}
    local rewardData = util.SplitString(cfg.RewardID, ";")
    rewardData = util.SplitString(rewardData[level], "#", tonumber)
    for i = 1, #rewardData do
        reward[i] = rewardData[i]
    end
    return cfg.Intimacy.data[level - 1], reward
end

--情人节活动：获取礼物对应亲密度
function GetValentineGiftIntimacy(index, id)
    if index then
        return game_scheme:InitBattleProp_0(681).szParam.data[index - 1]
    elseif id then
        local cfg = game_scheme:InitBattleProp_0(680)
        for i = 0, cfg.szParam.count - 1 do
            if cfg.szParam.data[i] == id then
                return game_scheme:InitBattleProp_0(681).szParam.data[i]
            end
        end
    end
    return 0
end

function SetValentineArcheryData(tpID, data, key, value)
    local version = nil
    local topicID = nil
    for k, v in pairs(festivalActivityData) do
        if v.headingCode == valentineArcheryHeadingCode then
            topicID = k
            break
        end
    end
    if data then
        if topicID and festivalActivityData[topicID] then
            version = festivalActivityData[topicID][topic_pb.TopicKey_Archery_Version + 1]
            festivalActivityData[topicID].archeryData = festivalActivityData[topicID].archeryData or {}
            local length = #data
            for i = 1, length do
                if data[i] ~= INVALID_INT_VALUE then
                    if (i >= topic_pb.TopicKey_Archery_FirstRing + 1) and (i <= topic_pb.TopicKey_Archery_ThirdRing + 1) then
                        festivalActivityData[topicID].archeryData[i] = festivalActivityData[topicID].archeryData[i] or {}
                        festivalActivityData[topicID].archeryData[i].posx = bit.rshift(data[i], 24)
                        festivalActivityData[topicID].archeryData[i].posy = bit.band(bit.rshift(data[i], 16), 0xFF)
                        festivalActivityData[topicID].archeryData[i].record = bit.band(data[i], 0xFFFF)
                    else
                        festivalActivityData[topicID].archeryData[i] = data[i]
                    end
                end
            end
            RecordValentineArcheryDay(topicID)
        end
    elseif key then
        if topicID and festivalActivityData[topicID] then
            local v = festivalActivityData[topicID]
            if value ~= INVALID_INT_VALUE then
                v.archeryData = v.archeryData or {}
                if (key >= topic_pb.TopicKey_Archery_FirstRing) and (key <= topic_pb.TopicKey_Archery_ThirdRing) then
                    v.archeryData[key + 1] = v.archeryData[key + 1] or {}
                    v.archeryData[key + 1].posx = bit.rshift(value, 24)
                    v.archeryData[key + 1].posy = bit.band(bit.rshift(value, 16), 0xFF)
                    v.archeryData[key + 1].record = bit.band(value, 0xFFFF)
                else
                    v.archeryData[key + 1] = value
                    if key == topic_pb.TopicKey_Archery_State then
                        util.DelayCallOnce(0.2, function()
                            event.Trigger(event.UPDATE_VALENTINE_ARCHERY_STATE)
                        end)
                    elseif key == topic_pb.TopicKey_Archery_BuyTimes then
                        event.Trigger(event.UPDATE_VALENTINE_ARCHERY_BUY_TIMES)
                    elseif key == topic_pb.TopicKey_Archery_CurTurn then
                        event.Trigger(event.UPDATE_VALENTINE_ARCHERY_BUY_TIMES)
                    elseif key == topic_pb.TopicKey_Archery_Version then
                        version = value
                    end
                end
            end
            RecordValentineArcheryDay(topicID)
        end
    end
    if version and (version ~= INVALID_INT_VALUE) then
        -- if topicID and festivalActivityData[topicID] then
        --     if festivalActivityData[topicID].headingCode == valentineIntimacyHeadingCode then
        --         --版本号不相等则请求最新的亲密度数据
        --         if v.versionNumber ~= version then
        --             local activityCfg = GetActivityCofigByTopicID(topicID)
        --             if activityCfg then
        --                 local net_festival_activity_module = require "net_festival_activity_module"
        --                 net_festival_activity_module.Request_ValentinesArcheryInfo(activityCfg.AtyID)
        --             end
        --         end
        --     end
        -- end
    end
end

--红点用 , 记录服务器的 [所在天] 数据
function RecordValentineArcheryDay(topicID)
    local fd = festivalActivityData[topicID]
    if fd and fd.archeryData then
        local serverTime = os.server_time()
        local serverDay = os.date("%d", serverTime)
        fd.archeryData.DateRefreshServerDay = serverDay
    end
end

--红点用 判断是否和数据设置时不同一天
function GetValentineArcheryIsNextDay(topicID)
    local fd = festivalActivityData[topicID]
    if fd and fd.archeryData then
        if fd.archeryData.DateRefreshServerDay ~= os.date("%d", os.server_time()) then
            return true
        end
    end
    return false
end


--情人节活动：获得射箭小游戏开始状态
function GetValentineArcheryState(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].archeryData and (festivalActivityData[topicID].archeryData[topic_pb.TopicKey_Archery_State + 1] == 2)
end

--情人节活动：获得射箭小游戏上一次时间
function GetValentineArcheryLastTime(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].archeryData and festivalActivityData[topicID].archeryData[topic_pb.TopicKey_Archery_LastTime + 1] or 0
end

--情人节活动：获得射箭小游戏总轮次
function GetValentineArcheryTotalRound()
    return GetValentineArcheryFreeRound() + GetValentineArcheryChargeRound()
end

--情人节活动：获得射箭小游戏免费轮次
function GetValentineArcheryFreeRound()
    return game_scheme:InitBattleProp_0(676).szParam.data[0]
end

--情人节活动：获得射箭小游戏收费轮次
function GetValentineArcheryChargeRound()
    return game_scheme:InitBattleProp_0(677).szParam.data[0]
end

--情人节活动：获得射箭小游戏当前轮次
function GetValentineArcheryCurRound(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].archeryData and festivalActivityData[topicID].archeryData[topic_pb.TopicKey_Archery_CurTurn + 1] or 0
end

--情人节活动：获得射箭小游戏购买轮次次数
function GetValentineArcheryBuyTimes(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].archeryData and festivalActivityData[topicID].archeryData[topic_pb.TopicKey_Archery_BuyTimes + 1] or 0
end

--情人节活动：通过购买的轮次获得射箭小游戏购买轮次价格
function GetValentineArcheryBuyPrice(round)
    return game_scheme:InitBattleProp_0(678).szParam.data[round - 1]
end

--情人节活动：获得射箭小游戏当前箭矢序号
function GetValentineArcheryCurArrow(topicID)
    local index = GetValentineArcheryTotalArrowPerRound() + 1
    for i = topic_pb.TopicKey_Archery_FirstRing + 1, topic_pb.TopicKey_Archery_ThirdRing + 1 do
        if festivalActivityData[topicID] and festivalActivityData[topicID].archeryData and festivalActivityData[topicID].archeryData[i] then
            if (festivalActivityData[topicID].archeryData[i].record == 0) or (festivalActivityData[topicID].archeryData[i] == INVALID_INT_VALUE) then
                index = i % topic_pb.TopicKey_Archery_FirstRing
                break
            end
        end
    end
    return index
end

--情人节活动：获得射箭小游戏每轮总箭矢数
function GetValentineArcheryTotalArrowPerRound()
    return game_scheme:InitBattleProp_0(679).szParam.data[0]
end

--情人节活动：获得射箭小游戏当前轮次总环数
function GetValentineArcheryRecord(topicID)
    local record = 0
    for i = topic_pb.TopicKey_Archery_FirstRing + 1, topic_pb.TopicKey_Archery_ThirdRing + 1 do
        if festivalActivityData[topicID] and festivalActivityData[topicID].archeryData and festivalActivityData[topicID].archeryData[i] then
            if (festivalActivityData[topicID].archeryData[i].record ~= 100) and (festivalActivityData[topicID].archeryData[i] ~= INVALID_INT_VALUE) then
                record = record + festivalActivityData[topicID].archeryData[i].record
            end
        end
    end
    return record
end

--情人节活动：获得射箭小游戏总环数对应奖励档次及奖励id
function GetValentineArcheryReward(topicID, record)
    local highest = 0
    local reward = {}
    local temp = {}
    local activityCfg = GetActivityCofigByTopicID(topicID)
    for i = 0, activityCfg.ctnID1.count - 1 do
        local ctnCfg = game_scheme:ActivityContent_0(activityCfg.ctnID1.data[i])
        if ctnCfg then
            if record >= ctnCfg.activityConditions.data[0] then
                highest = i + 1
                local rewardData = util.SplitString(ctnCfg.rewardGroup, "#", tonumber)
                for i = 1, #rewardData do
                    local reward_mgr = require "reward_mgr"
                    local rewardData = reward_mgr.GetRewardGoods(rewardData[i])
                    if temp[rewardData.id] then
                        temp[rewardData.id].num = temp[rewardData.id].num + rewardData.num
                    else
                        temp[rewardData.id] = rewardData
                    end
                end
            end
        end
    end
    for k, v in pairs(temp) do
        table.insert(reward, v)
    end
    return highest, reward
end

--情人节活动：获得射箭小游戏箭矢数据
function GetValentineArcheryArrowData(topicID, index)
    local realIndex = index + topic_pb.TopicKey_Archery_FirstRing
    return festivalActivityData[topicID].archeryData[realIndex]
end

local valentinePFkey = "valentine_pft_"
local valentinePFStates = {
    Close = 0,
    Show = 1,
    Watched = 2
}
function GetValentineDayBuy(topicID)
    local nowDay = topicID and festivalActivityData[topicID].valentineDayBuy
    nowDay = nowDay or {}
    return nowDay
end
function GetPreferentialKey(index, topicID)
    local player_mgr = require "player_mgr"
    if not index or not topicID then
        log.Error("[festival_activity_mgr]>>GetPreferentialKey index", index, "topicid", topicID)
        return valentinePFkey .. player_mgr.GetPlayerRoleID() .. "default"
    end
    local versionNum = GetAcitivityVersion(topicID) or "versionNum"
    local headingCode = GetAcitivityCode(topicID) or "headingCode"
    local endStamp = GetAcitivityEndStamp(topicID) or "endTime"
    return valentinePFkey .. player_mgr.GetPlayerRoleID() .. "_" .. index .. "_" .. headingCode .. "_" .. versionNum .. "_" .. endStamp
end
--情人节活动：特惠礼包红点数据
function GetValentinePreferentialRedPoint(topicID)
    local player_mgr = require "player_mgr"
    local result = {}
    local dayBuy = topicID and festivalActivityData[topicID].valentineDayBuy or {}
    for i = 1, 8 do
        local key = GetPreferentialKey(i, topicID)
        local saveData = PlayerPrefs.GetInt(key, valentinePFStates.Close)
        if dayBuy[i] then
            saveData = valentinePFStates.Watched
        end
        table.insert(result, saveData)
    end
    return result
end

--设置红点为开启
function SetValentinePreferentialRedPointOn(index, topicID)
    local key = GetPreferentialKey(index, topicID)
    local value = PlayerPrefs.GetInt(key, valentinePFStates.Close)
    if value == 0 then
        PlayerPrefs.SetInt(key, valentinePFStates.Show)
    end
end

--设置红点为已查看
function SetValentinePreferentialRedPoint(index, topicID)
    local key = GetPreferentialKey(index, topicID)
    PlayerPrefs.SetInt(key, valentinePFStates.Watched)
end


--情人节特惠礼包 天刷新
function ValentinePreferentialDayRefresh(topicID)
    local countDown = GetAcitivityEndStamp(topicID)
    if countDown > 0 then
        countDown = ValentinePreferentialCountDownCorrect(countDown)
        local dayRemain, timeStr = string.countdown(countDown - os.server_time())
        dayRemain = tonumber(dayRemain) - 1
        if dayRemain <= 7 then
            local targetDay = 7 - dayRemain
            for i = 1, targetDay - 1 do
                SetValentinePreferentialRedPointOn(i, topicID)
            end
        end
    end
end

--特惠礼包倒计时校正 活动结束为22:00导致需要矫正到00:00
function ValentinePreferentialCountDownCorrect(countDown)
    local result = countDown + (60 * 60 * 2)
    return result
end

--情人节特惠打点上报
function ValentinePreferentialReport(topicID, origin, new)
    if not origin or not new then
        return
    end
    for i = 1, 7 do
        if origin[i] ~= new[i] and new[i] then

            local json = require "dkjson"
            --打点数据上报
            local id = i
            local rewards = ""
            local activityCfg = GetActivityCofigByTopicID(topicID)
            if activityCfg and activityCfg.ctnID1 and activityCfg.ctnID1.data and activityCfg.ctnID1.data[i] then
                local acId = activityCfg.ctnID1.data[i - 1]
                local ctnCfg = game_scheme:ActivityContent_0(acId)
                if ctnCfg and ctnCfg.rewardGroup then
                    rewards = ctnCfg.rewardGroup
                end
            end

            local json_str = json.encode({
                id = id,
                reward = rewards
            })
            event.Trigger(event.GAME_EVENT_REPORT, "Valentine_preferential_buy", json_str)

        end
    end
end

--心愿单获取已选择奖励ID&&圣物活动
function GetWishListSelectedRewardID(topicID)
    return festivalActivityData[topicID].selectedRewardID
end

--周卡获取购买次数
function GetWeekCardBuyTimes(topicID)
    return festivalActivityData[topicID].buyTimes or 0
end

----type=0 或者 nil 是圣物活动  type==1 ==天命活动
function GetTaskBigRewardState(ntype)
    if taskData and taskData.bigRewardState then
        --print("bigRewardState",taskData.bigRewardState[ntype+1],ntype+1)
        return taskData.bigRewardState[ntype + 1] or false
    else
        return false
    end

end

--获取抢红包游戏数据
function GetSnatchRedenvlopData(topicID)
    return festivalActivityData[topicID]
end

function SetTaskData(key, value, isUpdate)
    --print("-----任务大奖",key,value)
    if not taskData then
        taskData = {}
    end

    if not taskData.bigRewardState then
        taskData.bigRewardState = {}
    end

    local increase = 0
    if not isUpdate then
        increase = 1
    end

    if value == INVALID_INT_VALUE then
        value = 0
    end

    --local cfg = fes
    if key == topic_pb.emTopic_SubTask_GainFlag + increase then
        taskData.bigRewardState[1] = value == 1 --圣物活动的
        --print("圣物任务大奖--",value == 1)
    elseif key == topic_pb.emTopic_SubTask_GainFlag + increase + 1 then
        taskData.bigRewardState[2] = value == 1  --天命星途的
        --print("天命星途大奖--",value == 1)
    end
    event.Trigger(event.UPDATE_FESTIVAL_DATA)
end

function SetDragonBoatGiftData(key, value, isUpdate)
    -- dump({"hjh 设置支付累充数据",key=key,value=value,isUpdate=isUpdate})
    if not isUpdate then
        for i, v in pairs(key) do
            if i == topic_pb.emTopicKey_DragonBoatRechargeTime + 1 then
                dragonBoatGiftData.rechargeAmount = value
            end
        end
        return
    end
    local lastData = GetDragonBoatGiftData()
    if value ~= INVALID_INT_VALUE then
        if key == topic_pb.emTopicKey_DragonBoatRechargeTime then

        elseif key == topic_pb.emTopicKey_DragonBoatRechargeAmount then
            --端午节期间当天累计充值12元，打点上报
            local limitAmount = game_scheme:InitBattleProp_0(2705).szParam.data[0]
            if lastData.rechargeAmount and lastData.rechargeAmount < limitAmount and value >= limitAmount then
                local property = {
                }
                event.Trigger(event.GAME_EVENT_REPORT, "Zongzi_WinningALottery", property)

            end
            dragonBoatGiftData.rechargeAmount = value
            event.Trigger(event.UPDATE_DRAGONBOATGIFT_RECHARGEAMOUNT)
        elseif key == topic_pb.emTopicKey_DragonBoatRechargeAllTimes then
            lastData.rechargeAllTimes = value
        end
    end
end
function GetDragonBoatGiftData()
    return dragonBoatGiftData
end

-------------------------------画糖人-------------------------------
-- 返回假数据
local b_fake_draw_sugarman_data = false
util.RegisterConsole("fake_draw_sugarman_data", 0, function(st)
    b_fake_draw_sugarman_data = st == 1
end)

--- 单个画糖人数据
--- @class DrawSugarmanData
--- @field randomSub boolean[] 随机下标是否已使用
--- @field buyTimes number 购买次数
--- @field curTurn number 当前轮次
--- @field rewardFlag number 0:未领取 1:领取了第一次奖励 2:领取了第二次奖励 3:领取了第三次奖励 4:领取了第四次奖励 5:领取了第五次奖励

--- 画糖人数据
---@type table<number, DrawSugarmanData>
local drawSugarmanDatas = {}

--- 画糖人游戏存档
--- @class DrawSugarmanSavedGameData
--- @field topicID number 活动topicID
--- @field invalidTimestamp number 失效时间
--- @field drawTime number 已画的时间
--- @field tangrenResID number 糖人id

--- @type DrawSugarmanSavedGameData|nil
local drawSugarmanSavedGameData = nil

--- 设置画糖人数据
function SetDrawSugarmanData(activityData, key, val)
    if activityData == nil then
        log.Error("[SetDrawSugarmanData] activityData == nil")
        return
    end
    local topicID = activityData.topicID
    if drawSugarmanDatas[topicID] == nil then
        drawSugarmanDatas[topicID] = GetDefaultDrawSugarmanData()
    end

    if key == topic_pb.ETOPICNAMEFESTIVAL_SUGARMAN_RandomSub + 1 then
        drawSugarmanDatas[topicID].randomSub = util.BitValue2ArrayBoolean(val)
    elseif key == topic_pb.ETOPICNAMEFESTIVAL_SUGARMAN_BuyTimes + 1 then
        -- 购买次数
        drawSugarmanDatas[topicID].buyTimes = val
    elseif key == topic_pb.ETOPICNAMEFESTIVAL_SUGARMAN_CurTurn + 1 then
        -- 当前轮次
        drawSugarmanDatas[topicID].curTurn = val
    elseif key == topic_pb.ETOPICNAMEFESTIVAL_SUGARMAN_RewardFlag + 1 then
        -- 奖励标记
        drawSugarmanDatas[topicID].rewardFlag = val
    end

    event.Trigger(event.DRAW_THE_SUGAR_MAN_DATA_UPDATE, key)
end

--- 获取画糖人数据
---@return DrawSugarmanData
function GetDrawSugarmanData(topicID)
    if b_fake_draw_sugarman_data then
        local data = GetDefaultDrawSugarmanData()
        return data
    end

    if topicID == nil then
        log.Error("[GetDrawSugarmanData] topicID == nil")
        return GetDefaultDrawSugarmanData()
    end
    return drawSugarmanDatas[topicID] or GetDefaultDrawSugarmanData()
end

local drawSugarmanDayFreeTimes = -1
--- 获取画糖人每日免费次数
function GetDrawSugarmanDayFreeTimes()
    if drawSugarmanDayFreeTimes ~= -1 then
        return drawSugarmanDayFreeTimes
    end

    drawSugarmanDayFreeTimes = 0
    local cfg3861 = game_scheme:InitBattleProp_0(3861)
    if cfg3861 and cfg3861.szParam.count ~= 0 then
        drawSugarmanDayFreeTimes = cfg3861.szParam.data[0]
    else
        log.Error("InitBattleProp 3861 error")
    end
    return drawSugarmanDayFreeTimes
end

--- 获取默认画糖人数据
function GetDefaultDrawSugarmanData()
    local defaultData = {
        randomSub = {},
        buyTimes = 0,
        curTurn = 0,
        rewardFlag = 0,
    }
    return defaultData
end

--- 获取画糖人存档数据
function GetdDrawSugarmanSavedGameData()
    return drawSugarmanSavedGameData
end

--- 设置画糖人存档数据
function SetDrawSugarmanSavedGameData(topicID, invalidTimestamp, drawTime, tangrenResID)
    drawSugarmanSavedGameData = {}
    drawSugarmanSavedGameData.topicID = topicID
    drawSugarmanSavedGameData.invalidTimestamp = invalidTimestamp
    drawSugarmanSavedGameData.drawTime = drawTime
    drawSugarmanSavedGameData.tangrenResID = tangrenResID
end

--- 清除画糖人存档数据
function CleanDrawSugarmanSavedGameData()
    drawSugarmanSavedGameData = nil
end

--- 伪造画糖人数据
function FakeDrawSugarmanData()
    for _, drawSugarmanData in pairs(drawSugarmanDatas) do
        drawSugarmanData.randomSub = {}
        drawSugarmanData.buyTimes = 3
        drawSugarmanData.curTurn = 0
        drawSugarmanData.rewardFlag = 0
    end
end
-------------------------------画糖人-------------------------------

--获取已完成的活动任务ctn--------
function GetCompleteBadgeContentIDs(topicID)
    local activityData = GetFestivalActivityDataByTopicID(topicID)
    local activityTimeCfg = game_scheme:festivalActivityTime_0(activityData.activityID)
    local activityCfg = game_scheme:festivalActivity_0(activityTimeCfg.cfgID)
    local ctnList = activityCfg.ctnID1--任务列表
    local completeData = GetAcitivityContentCompleteTimes1(topicID)
    local roundData = GetActivityContentRountCompleteTime(topicID)
    local contentIDs = {}
    for i = 0, ctnList.count - 1 do
        local ctn = ctnList.data[i]
        local ctnCfg = game_scheme:ActivityContent_0(ctn)
        if ctnCfg then
            local complete = completeData[i] or 0
            local LimitNumber = ctnCfg.LimitNumber
            local round = roundData[i] or 0
            local conditionTimes = tonumber(ctnCfg.conditionTimes.data[0])--最大进度值
            if complete >= conditionTimes and round < LimitNumber then
                table.insert(contentIDs, ctn)
            end
        end
    end
    return contentIDs
end
function ActivityIDToTopicID(activityID)
    local cfg = game_scheme:ActivityMain_0(activityID)
    local topicID = GetTopicIDByActivityID(cfg.AtyID)
    return topicID or 0
end
--获取可签到的天数--------
function GetCanSignDays(topicID)

    local receivedDays = {}
    local allDayState = GetAcitivitySignData(topicID) or {}
    local signDay = GetAcitivitySignDays(topicID) or 0
    for i = 1, signDay do
        if allDayState[i] == 0 then
            table.insert(receivedDays, i)
        end
    end
    return receivedDays
end

--检测福卡活动是否开启 headingCode为41是分享福卡专用
function IsOpenFuCard()
    local cfg = game_scheme:InitBattleProp_0(2708)--所有福字id
    if cfg and cfg.szParam and cfg.szParam.data then
        local version
        for k, v in pairs(cfg.szParam.data) do
            version = v
            local activityCfg = GetActivityCofig(41, version)
            if activityCfg and activityCfg.versionNumber == version then
                local topicId = GetTopicIDByActivityID(activityCfg.AtyID)
                if topicId then
                    local endTime = GetAcitivityEndStamp(topicId)
                    return endTime > os.server_time()
                end
            end
        end
    end
    return false
end

-- 获取所有集卡、集字活动
function GetAllCollectCardActivities()
    local activities = {}
    --暂时屏蔽集卡活动，这个活动和兔子集市活动冲突了
    --local now = os.server_time()
    --for _, v in pairs(festivalActivityData) do
    --    if v.headingCode == 41 and v.endTimeStamp > now then
    --        table.insert(activities, v)
    --    end
    --end

    return activities
end

function IsOpenCollectCard(activities, actType)
    if activities == nil then
        activities = GetAllCollectCardActivities()
    end
    for _, v in pairs(activities) do
        local activityCfg = GetActivityCofig(v.headingCode, v.versionNumber)
        if activityCfg and activityCfg.actType == actType and v.endTimeStamp > os.server_time() then
            return true
        end
    end

    return false
end

--- 获取活动跳转链接
---@param urlId number 跳转链接id
---@return string | nil 跳转链接url
function GetUrl(urlId)
    if urlId == 0 then
        return nil
    end

    local cfg = game_scheme:festivalActivityUrl_0(urlId)
    if cfg == nil then
        log.Error("festivalActivityUrl not find urlId:", urlId)
        return nil
    end

    local url = cfg["lan_" .. lang.USE_LANG]
    if string.IsNullOrEmpty(url) then
        url = cfg.lan_other
    end

    if string.IsNullOrEmpty(url) then
        log.Error("festivalActivityUrl url is empty, urlId:", urlId)
        return nil
    end

    -- 用户
    local userId = player_mgr.GetPlayerRoleID()
    url = string.gsub(url, "{1}", tostring(userId))

    -- 区服
    --[[大区ID: 1.外网中国区 2.外网欧美区 3.外网亚太区 ]]
    local setting_server_data = require "setting_server_data"
    local regionID = setting_server_data.GetRegionID()
    local serverName = ""
    if regionID == 2 then
        serverName = "ea"
    elseif regionID == 3 then
        serverName = "sa"
    end
    url = string.gsub(url, "{2}", serverName)

    --token
    local net_login_module = require "net_login_module"
    local token = util.URLEncode(net_login_module.GetLoginSession() or "testSession")
    url = string.gsub(url, "{3}", tostring(string.gsub(token, "%%", "%%%%")))

    return url
end

--- 打开活动跳转链接
---@param urlId number 跳转链接id
function OpenUrl(urlId)
    local url = GetUrl(urlId)
    if string.IsNullOrEmpty(url) then
        log.Error("OpenUrl url is empty")
        return
    end

    -- if Application.platform == RuntimePlatform.IPhonePlayer or  Application.platform == RuntimePlatform.Android then
    --url = Uri.EscapeUriString(url)
    local q1sdk = require "q1sdk"
    q1sdk.ApplicationOpenURL(url)
end


-- 获取活动已经开放到第几天
function GetActivityPastDayByActivityID(activityID)
    local activityCfg = festival_activity_cfg.GetActivityCfgByAtyID(activityID)
    if activityCfg == nil then
        log.Error("festival_activity_mgr activityCfg == nil", activityID)
        return 0
    end
    local topicID = GetTopicIDByActivityID(activityCfg.AtyID)
    return GetActivityPastDayBytopicID(topicID)
end

-- 获取活动已经开放到第几天
function GetActivityPastDayBytopicID(topicID)
    if topicID == nil then
        log.Error("festival_activity_mgr topicID == nil")
        return 0
    end
    local startTimestamp = GetActivityStartTime(topicID)
    if startTimestamp == nil then
        log.Error("festival_activity_mgr startTimestamp == nil", topicID)
        return 0
    end
    local openTime0 = util.GetServerTime0(startTimestamp)
    local pastDay = math.ceil((os.server_time() - openTime0) / 86400)
    return pastDay
end


-- 获取活动结束时间
function GetAcitivityEndStampByActivityID(activityID)
    local activityCfg = festival_activity_cfg.GetActivityCfgByAtyID(activityID)
    if activityCfg == nil then
        log.Error("festival_activity_mgr activityCfg == nil", activityID)
        return 0
    end
    local topicID = GetTopicIDByActivityID(activityCfg.AtyID)
    return GetAcitivityEndStamp(topicID)
end

-- 跳过热更后活动配置找不到
function GetIsMissActivity()
    return isMissActivity
end

function OnSceneDestroy()
    for k, v in pairs(SuperMonthlyCardFestivalFisrtEnter) do
        SuperMonthlyCardFestivalFisrtEnter[k] = true
    end
    festivalActivityData = {}
    lastSendChristmasCardTime = nil
    christmasCardSendInfo = {}
    GameEventData = {}
    redTypeArr = {}
    lastChristmasData = {}
    lastReportData = {}
    ProgressData = {}
    taskData = {}
    dragonBoatGiftData = {}
    nowRedPackageType = nil
    exchangeShopShowLimitData = nil
    isMissActivity = false
    CleanDrawSugarmanSavedGameData()
end
local reportTIme = 0 --打点计时
---@see 节日活动打点,弹窗间跳转
function FestivalGameEventReport(eventName, type, cur, open)
    local nowTime = net_login_module.GetServerTime()
    if nowTime > 0 and nowTime - reportTIme < 0.5 then
        return
    end
    reportTIme = nowTime

    if type == 1 then
        cur = GetSceneID()
    end

    event.Trigger(event.GAME_EVENT_REPORT, "FestivalUI_enter", {
        interface_type = type, --1大厅、2弹窗、3页签间切换
        interface_id = cur or 0, --当前界面id
        interface_open = open, --打开界面id
    })
end
event.Register(event.FESTIVAL_GAME_EVENT_REPORT, FestivalGameEventReport)

--大厅活动入口打点ID
function GetSceneID()
    local menu_bot_data = require "menu_bot_data"
    if menu_bot_data.IsHubPage() then
        --主城
        return 1
    elseif menu_bot_data.IsRiskPage() then
        --野外
        return 2
    elseif menu_bot_data.IsHuntPage() then
        --冒险
        return 3
    else
        return 1
    end
end

--region  删除versionNumber的新接口

-- - - - - - - - - - - - - - - - - - - - - --配置表相关 - - - - - - - - - - - - - - - - - - - -

--- 通过topicID获得节日活动配置
---@param topicID number topicID
---@param isNewbie number 是否新手
--原接口，保留而已，请更换调用
function GetActivityCofigByTopicID(topicID, isNewbie)
    if festivalActivityData[topicID] then
        local activityID = festivalActivityData[topicID].activityID
        return festival_activity_cfg.GetActivityCfgByActivityID(activityID)
    end
    return nil
end

--- 通过topicID获得节日活动时间配置 new的 ActivityMain --暂时不改名ActivityTime实际上就是ActivityMain
---@param topicID number topicID
--原接口，保留而已，请更换调用
function GetActivityTimeCfgByTopicID(topicID)
    if festivalActivityData[topicID] then
        local activityID = festivalActivityData[topicID].activityID
        return festival_activity_cfg.GetActivityMainCfgByActivityID(activityID)
    end
    return nil
end

--- 通过活动识别码和活动版本号获得节日活动配置
---@param headingCode number 活动识别码
---@param versionNumber number 活动版本号
---@param isNewbie number 是否新手
function GetActivityCofig(headingCode, versionNumber, isNewbie)
    log.Error("versionNumber已被删除，不应该有调用,使用 festival_activity_cfg.GetActivityCfgByAtyID")
end

--- 通过活动ID获得节日活动配置
---@param AtyID number 活动ID
---@param isNewbie number 是否新手
---@return table 会返回俩个表 festivalActivity.csv  ActivityMain.csv
function GetActivityCfgByAtyID(AtyID, isNewbie)
    if isNewbie then
        return festival_activity_cfg.GetNewbieActivityCfgByAtyID(AtyID)
    else
        return festival_activity_cfg.GetActivityCfgByAtyID(AtyID)
    end
end
---------------------------------------------旧配置读取方式-------------------------------------------
-- - - - - - - - - - - - - - - - - - - - - 新接口分割线 - - - - - - - - - - - - - - - - - - - -
------------------新增的Logic区域----------------------------------------------
---@public 帧末活动处理
function OnEndFrameFestivalActivitiesUpdate()
    --当前直接把过时的活动删掉
    for i, v in pairs(festivalActivityData) do
        isOpen = IsOpenActivity(v)
        if not isOpen then
            festivalActivityData[i] = nil
        end
    end
end

---@public 具体是哪个活动发生了变化
function OnFestivalActivitiesUpdate(activityID, headingCode)
    if not activityID or activityID == 0 then
        return
    end
    local isOpen, endType = GetIsOpenByActivityID(activityID)
    local oldOpen
    if isLoginFirstReceiveData then
        oldOpen = isOpen
    else
        oldOpen = cacheFestivalActivityState[activityID]
    end  
    local curTimeStamp = os.server_time()
    Warning(3, "新活动OnFestivalActivitiesUpdate=", activityID, " isOpen =", isOpen, "oldOpen=",oldOpen, " endType=", endType, "curTimeStamp=", curTimeStamp)
    event.Trigger(gw_event_activity_define.GW_ONE_ACTIVITY_UPDATE, activityID, isOpen, true,oldOpen, headingCode)
    event.Trigger(gw_event_activity_define.GW_UPDATE_CITY_SIEGE_BUBBLE_STATE)
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE, activityID)
    --缓存活动状态的
    cacheFestivalActivityState[activityID] = isOpen and true or nil
end

---@public 有活动发生了变化
function OnHaveFestivalActivitiesUpdate()
    Warning(3, "新活动 OnHaveFestivalActivitiesUpdate")
    event.Trigger(gw_event_activity_define.GW_HAVE_ACTIVITIES_UPDATE)
    isLoginFirstReceiveData = false
end

---@public 获取当前所有的活动入口
---@param checkCrossServer boolean 是否检测跨服
---@return table 主界面的右边第一排活动入口1，右边第二排的活动入口2，未配置在FunctionBar的活动直接筛除
function GetCurFestivalActivityEntrances(checkCrossServer)
    local main_slg_data = require "main_slg_data"
    local gw_const = require("gw_const")
    --右边第一排
    local entrances = {} 
    --右边第二排
    local entrances2 = {}
    --右边第一排所有的id，key是entranceID
    local entranceIds = {}
    --右边第二排所有的id，key是entranceID
    local entranceIds2 = {}
    local showScene = main_slg_data.GetCurSceneType()
    if showScene ~= gw_const.ESceneType.Home and showScene ~= gw_const.ESceneType.Sand then
        showScene = gw_const.ESceneType.Home
    end
    local isBaseCross = false
    if checkCrossServer then
        local gw_common_util = require "gw_common_util"
        isBaseCross = gw_common_util.GetSandBaseCrossServiceState()     
    end
    local isReviewing = ReviewingUtil.IsReviewing()
    for _, v in pairs(festivalActivityData) do
        local activityCfg = GetActivityCfgByTopicID(v.topicID)
        if activityCfg then
            local isActReviewingWhite = (festival_activity_cfg.ActivityReviewingWhiteList[activityCfg.headingCode]
                    or festival_activity_cfg.ActivityReviewingWhiteListByActId[activityCfg.AtyID])
            -- 是否在当前区服开启
            if IsEnabledInCurWorld(activityCfg) and (not isReviewing or isActReviewingWhite) then
                --需要判断一下这个活动是否在跨服状态展示
                if not isBaseCross  or  (isBaseCross and  festival_activity_cfg.CrossServerActivity[activityCfg.headingCode]) then
                    -- 是否活动是否已解锁                        
                    local isUnlocked = IsOpenActivity(v, true)
                    if isUnlocked then
                        local haveEntrance = activityCfg.AtyEntrance and activityCfg.AtyEntrance > 0
                        if haveEntrance then
                            local entranceCfg = festival_activity_cfg.GetActivityEntranceCfg(activityCfg.AtyEntrance,true)
                            --获取当前展示场景是否展示该活动入口
                            if entranceCfg and entranceCfg.shownPage.data[showScene-1] and entranceCfg.shownPage.data[showScene-1] > 0 then
                                local curTab
                                local curCheckTab
                                --1是右侧第一栏，2是右侧第二栏
                                if entranceCfg.showLine == 1 then
                                    curTab = entrances
                                    curCheckTab = entranceIds
                                elseif entranceCfg.showLine == 2 then
                                    curTab = entrances2
                                    curCheckTab = entranceIds2
                                end
                                local allReadyContain = curCheckTab[activityCfg.AtyEntrance]
                                if not allReadyContain and activityCfg.AtyEntrance ~= 0 then
                                    curCheckTab[activityCfg.AtyEntrance] = true
                                    table.insert(curTab, { seqencing = entranceCfg.Seqencing, entranceID = activityCfg.AtyEntrance })
                                end
                                local _atyEntrance2 = activityCfg.AtyEntrance2
                                if _atyEntrance2 and _atyEntrance2 ~= 0 then
                                    local _entranceCfg = festival_activity_cfg.GetActivityEntranceCfg(_atyEntrance2)
                                    if _entranceCfg.showLine == 1 then
                                        curTab = entrances
                                        curCheckTab = entranceIds
                                    elseif _entranceCfg.showLine == 2 then
                                        curTab = entrances2
                                        curCheckTab = entranceIds2
                                    end
                                    local allReadyContain = curCheckTab[_atyEntrance2]
                                    if not allReadyContain then
                                        curCheckTab[_atyEntrance2] = true
                                        table.insert(curTab, { seqencing = _entranceCfg.Seqencing, entranceID = _atyEntrance2 ,isQuickEntrance = true,activityID = activityCfg.AtyID})
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
    end
    CreatePreviewEntrance(entrances,entrances2,entranceIds,entranceIds2,showScene)
    return entrances,entrances2
end



---@public function 预告支持
function CreatePreviewEntrance(entrances,entrances2,entranceIds,entranceIds2,showScene)

    local previewCfg = festival_activity_cfg.PreviewActivityList
    for entranceID, v in pairs(previewCfg) do
        local id = v.activityID() or 0
        if IsOpenActivityBase(id) or v.isIgnoreBaseCondition then
            if v.isShowFunction() then --检查是否满足预告条件
                if v.mainBtnGroup == main_slg_const.MainButtonGroupType.RightTop then
                    --检查入口 entrances
                    local isIn = IsInEntrances(entrances,entranceID)
                    if not isIn then -- 不在数组中
                        InsertEntrance(entranceID,entrances,entrances2,entranceIds,entranceIds2,showScene)
                    end
                elseif v.mainBtnGroup == main_slg_const.MainButtonGroupType.RightTop2 then
                    --检查入口 entrances2
                    local isIn = IsInEntrances(entrances2,entranceID)
                    if not isIn then -- 不在数组中
                        InsertEntrance(entranceID,entrances,entrances2,entranceIds,entranceIds2,showScene)
                    end
                end
            end
        end
    end
end

---@public function 检测活动是否满足基本条件
function IsOpenActivityBase(activityID)
    local function_open = require "function_open"
    local activityCfg = GetActivityCfgByActivityID(activityID)
    if activityCfg == nil then
        return false, -1
    end
    local open = function_open.CheckFunctionIsOpenByParas(activityCfg.BaseLevel, activityCfg.HooklevelID, activityCfg.SeverTime, nil, activityCfg.iBuildingID)
    if not open then
        return false, 2
    end
    
    return true
end

---@public function 检测是否在指定入口
function IsInEntrances(entranceArr,entranceID)
    for _, v in ipairs(entranceArr) do
        if v == entranceID then
            return true
        end
    end
    return false
end

---@public function 将制定入口ID 插入数据
function InsertEntrance(entranceID,entrances,entrances2,entranceIds,entranceIds2,showScene)
    --local entranceID = festival_activity_cfg.ActivityEntranceType.WarZonePK

    local entranceCfg = game_scheme:FunctionBar_0(entranceID)
    --获取当前展示场景是否展示该活动入口
    if entranceCfg and entranceCfg.shownPage.data[showScene-1] and entranceCfg.shownPage.data[showScene-1] > 0 then
        local curTab
        local curCheckTab
        --1是右侧第一栏，2是右侧第二栏
        if entranceCfg.showLine == 1 then
            curTab = entrances
            curCheckTab = entranceIds
        elseif entranceCfg.showLine == 2 then
            curTab = entrances2
            curCheckTab = entranceIds2
        end
        local allReadyContain = curCheckTab[entranceCfg.AtyEntrance]
        if not allReadyContain and entranceCfg.AtyEntrance ~= 0 then
            curCheckTab[entranceCfg.AtyEntrance] = true
            table.insert(curTab, { seqencing = entranceCfg.Seqencing, entranceID = entranceCfg.AtyEntrance })
        end
    end
end


---@public 获取指定入口对应的处于活跃状态的活动
---@param entranceID number 主界面的右边第一排活动入口1，右边第二排的活动入口2，未配置在FunctionBar的活动直接筛除
---@param isQuickEntrance boolean 是否快速入口
---@param needCheckUnlock boolean 是否检测活动是否已解锁 因为有些活动有服务器数据但没有解锁的
function GetAllActivityInEntrance(entranceID,isQuickEntrance,needCheckUnlock)
    if not entranceID then
        return
    end
    local activityIDs = {}
    for _, v in pairs(festivalActivityData) do
        local matchEntrance = false
        if isQuickEntrance then
            if v.atyEntrance2 == entranceID then
                matchEntrance = true
            end
        else
            if v.atyEntrance == entranceID then
                matchEntrance = true
            end
        end
        if matchEntrance then
            local isUnlocked = IsOpenActivity(v, needCheckUnlock)
            if  isUnlocked then
                table.insert(activityIDs, v.activityID)
            end
        end
    end
    return activityIDs
end
-----------------------------------------------------------------------------
--------------------------------活动数据和配置区---------------------------------
---@public 获取所有服务器下发的活动
---@param needOpen boolean 是否需要活动是否在开放期间
---@param needCheckUnlock boolean 是否检测活动是否已解锁
---@param needCheckCrossServer boolean 是否检测跨服
function GetAllServerActivity(needOpen,needCheckUnlock,needCheckCrossServer)
    local activities = {}
    local isMatch = false
    local isBaseCross = false
    if needCheckCrossServer then
        local gw_common_util = require "gw_common_util"
        isBaseCross = gw_common_util.GetSandBaseCrossServiceState()
    end
    for _, v in pairs(festivalActivityData) do       
        isMatch = not needOpen or IsOpenActivity(v,needCheckUnlock)
        if isMatch then
            if not needCheckCrossServer or (isBaseCross and festival_activity_cfg.CrossServerActivity[v.activityCodeType]) then
                table.insert(activities, v)
            end
        end
    end
    return activities
end

---这里需要想办法优化，因为每次get都在new
--- 获取所有开启的活动 通过传入入口
---@param entranceID number 入口id 分组
---@param isQuickEntrance boolean 是否快速入口
function GetAllActivityByEntrance(entranceID,isQuickEntrance) 
    local isAll = not entranceID --不传入入口id 则表示获取全部的开启活动
    local activities = {}
    for _, v in pairs(festivalActivityData) do
        local activityCfg = GetActivityCfgByTopicID(v.topicID)
        if activityCfg then
            local isMatchActType = false
            if isAll  then
                isMatchActType = true
            else
                if isQuickEntrance then
                    isMatchActType = activityCfg.AtyEntrance2 == entranceID
                else
                    isMatchActType = activityCfg.AtyEntrance == entranceID
                end
            end               
            if isMatchActType then
                -- 是否在当前区服开启
                if IsEnabledInCurWorld(activityCfg) then
                    -- 是否被另一个活动绑定，是另一个活动的子活动
                    local isBoundByOther = (activityCfg.bindActivity.count > 0 and activityCfg.bindActivity.data[0] == 2)
                    if (not isBoundByOther) then
                        -- 是否活动是否已解锁     
                        local isUnlocked = IsOpenActivity(v, true)
                        -- log.Warning("周活动isUnlocked", v.activityID,isUnlocked)
                        if isUnlocked then
                            local bubbleId = 0
                            if activityCfg.uiTemplateID then
                                local uiData = game_scheme:ActivityCommonUI_0(activityCfg.uiTemplateID)
                                if uiData then
                                    bubbleId = uiData.Bubble or 0
                                end
                            end
                            
                            local data = {
                                topicID = v.topicID,
                                activityID = v.activityID,
                                headingCode = v.headingCode,
                                activityCodeType = v.activityCodeType,
                                actType = activityCfg.actType,
                                atyEntrance = activityCfg.AtyEntrance,
                                atyEntrance2 = activityCfg.AtyEntrance2,
                                bubble = bubbleId,
                                popui = 0
                            }
                            --关联逻辑直接删除
                            table.insert(activities, data)
                        end
                    end
                end
            end
        end
    end
    return activities
end

---@public 获取所有的活动入口 --有活动开放的
---返回的是所有的开放的入口
function GetAllActivityEntrances()
    local entrances = {}
    local QuickEntrances = {}
    local addEntrance = function(entranceID,isQuickEntrance)
        if entranceID and  entranceID > 0 then
            if isQuickEntrance then
                if not table_util.Contains(QuickEntrances, entranceID) then
                    table.insert(QuickEntrances, entranceID)
                end
            else
                if not table_util.Contains(entrances, entranceID) then
                    table.insert(entrances, entranceID)
                end                
            end
        end
    end
    for _, v in pairs(festivalActivityData) do
        local activityCfg = GetActivityCfgByTopicID(v.topicID)
        if activityCfg then
            -- 是否在当前区服开启
            if IsEnabledInCurWorld(activityCfg) then
                --判断活动有没有开启
                local isOpen = IsOpenActivity(v, true)
                if isOpen then
                    addEntrance(activityCfg.AtyEntrance)
                    addEntrance(activityCfg.AtyEntrance2,true)
                end
            end
        end
    end
    return entrances,QuickEntrances
end

--配置表相关
---@public 通过活动Id获取活动配置 FestivalActivity，
---注意，因为内部将ActivityMain.csv合并进来了，GetActivityCfg的表中能获取到ActivityMain的字段
---@param activityID number 活动ID
function GetActivityCfgByActivityID(activityID)
    return festival_activity_cfg.GetActivityCfgByActivityID(activityID)
end

--- 通过topicID获得节日活动配置FestivalActivity
---注意，因为内部将ActivityMain.csv合并进来了，GetActivityCfg的表中能获取到ActivityMain的字段
---@param topicID number topicID
function GetActivityCfgByTopicID(topicID)
    if festivalActivityData[topicID] then
        return GetActivityCfgByActivityID(festivalActivityData[topicID].activityID)
    end
    return nil
end

---@public 通过活动Id获取活动配置 ActivityMain
---@param activityID number 活动ID
function GetActivityMainCfgByActivityID(activityID)
    return festival_activity_cfg.GetActivityMainCfgByActivityID(activityID)
end
--- 通过topicID获得节日活动配置ActivityMain
---@param topicID number topicID
function GetActivityMainCfgByTopicID(topicID)
    if festivalActivityData[topicID] then
        local activityID = festivalActivityData[topicID].activityID
        return GetActivityMainCfgByActivityID(activityID)
    end
    return nil
end
---@public 通过活动Id获取活动入口配置 functionBar表
function GetActivityEntranceCfgByActivityID(activityID)
    local activityCfg = GetActivityCfgByActivityID(activityID)
    if not activityCfg then
        return
    end
    return festival_activity_cfg.GetActivityEntranceCfg(activityCfg.AtyEntrance)
end

---@public 通过活动Id获取活动UI模板表 ActivityCommonUI.csv
function GetActivityCommonUICfgByActivityID(activityID)
    return festival_activity_cfg.GetActivityCommonUICfgByActivityID(activityID)
end
--数据相关
---@public 通过TopicId获取活动Id
---@param topicID number
function GetActivityIDByTopicID(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID].activityID
end

---@public 通过活动Id获取活动TopicID
---@param activityID number
function GetTopicIDByActivityID(activityID)
    for k, v in pairs(festivalActivityData) do
        if v.activityID == activityID then
            return k
        end
    end
    return nil
end

---@public 通过活动Id获取活动ActivityData
---@param activityID number
---@return table topicID下发的活动数据
function GetActivityDataByActivityID(activityID)
    for k, v in pairs(festivalActivityData) do
        if v.activityID == activityID then
            return v
        end
    end
    return nil
end

---@public 通过活动类型获取活动All ActivityData
---@param activityCodeType number
---@return table activityCodeType 活动类型
function GetAllActivityDataByActivityCodeType(activityCodeType)
    local array = {}
    for k, v in pairs(festivalActivityData) do
        if v.activityCodeType == activityCodeType then
            table.insert(array, v)
        end
    end
    return array
end

---@public 通过活动CodeType获取活动ActivityData
---@param activityCodeType number
---@return table topicID下发的活动数据
function GetActivityDataByActivityCodeType(activityCodeType)
    for k, v in pairs(festivalActivityData) do
        if v.activityCodeType == activityCodeType then
            return v
        end
    end
end

---@public 通过活动CodeType获取活动Id  --注意获取当前服务器下发中的活跃的活动Id,如果不开放则返回0
---@param activityCodeType number 配置表中定义的headingCode
---@return  number activityID  活动类activityID  
function GetActivityIdByCodeType(activityCodeType)
   local activityData =  GetActivityDataByActivityCodeType(activityCodeType)
    if activityData then
        return activityData.activityID
    end
    return 0
end

---@public 通过活动topicID获取活动ActivityData
---@param topicID number
---@return table topicID下发的活动数据
function GetActivityDataByTopicID(topicID)
    return festivalActivityData[topicID] and festivalActivityData[topicID]
end

---@public 通过活动Id 获取绑定活动Id集合，也就是子活动
function GetBindActivityIDsByActivityID(activityID)
    local activityCfg = GetActivityCfgByActivityID(activityID)
    if activityCfg and activityCfg.bindActivity and activityCfg.bindActivity.count > 0 and activityCfg.bindActivity.data[0] == 1 then
        local activityData = {}
        for i, v in pairs(activityCfg.bindActivity.data) do
            if i ~= 0 then
                table.insert(activityData, activityCfg.bindActivity.data[i])
            end
        end
        return activityData
    end
    log.Error("没有找到活动绑定的活动集合 通过活动Id：", activityID)
    return nil
end

---@public 通过活动Id 获取绑定活动Id集合，也就是子活动
function GetBindActivityIDByActivityIDAndCodeType(activityID, codeType)
    local ids = GetBindActivityIDsByActivityID(activityID)
    if not ids then
        return
    end
    for i, v in ipairs(ids) do
        local activityCfg = GetActivityCfgByActivityID(v)
        if activityCfg and activityCfg.headingCode == codeType then
            return v
        end
    end
    return nil
end

---@public 通过活动Id获取活动TimeStamp
---@param activityID number
---@return number timeStamp 活动结束时间+ 活动开始时间  --注意返回的第一个值是活动结束
function GetActivityTimeStampByActivityID(activityID)
    local activityData = GetActivityDataByActivityID(activityID)
    if activityData == nil then
        return nil
    end
    return activityData.endTimeStamp, activityData.startTimeStamp
end

---@public 判断活动入口是否开放
---@param entranceID number
function IsOpenByEntranceID(entranceID)
    for k, v in pairs(festivalActivityData) do
        if v.atyEntrance == entranceID then
            if IsOpenActivity(v,true) then
                return true
            end           
        end
    end
    return false
end

--根据活动id获取活动时间是否开启
---@public 通过活动Id获取活动是否开启,注意不能写死activityID,策划有可能会改，固定的功能使用headingCode
---@param activityID number
---@param needCheckUnlock number 是否要判断解锁条件
---@return boolean 不满足的类型 -1/0/1/2/3/4/5  其他-1/没有数据0/不在活动时间内1/不满足解锁条件2/自定义条件不满足3
function GetIsOpenByActivityID(activityID, needCheckUnlock)
    local activityData = GetActivityDataByActivityID(activityID)
    if activityData == nil then
        return false, 0
    end
    return IsOpenActivity(activityData, needCheckUnlock)
end


--根据活动id获取活动时间是否开启
---@public 通过headingCode获取活动是否开启
---@param headingCode number
---@param needCheckUnlock number 是否要判断解锁条件
---@return boolean 不满足的类型 -1/0/1/2/3/4/5  其他-1/没有数据0/不在活动时间内1/不满足解锁条件2/自定义条件不满足3
function GetIsOpenByHeadingCode(headingCode, needCheckUnlock)
    local activityData =  GetActivityDataByActivityCodeType(headingCode)
    if activityData == nil then
        return false, 0
    end
    return IsOpenActivity(activityData, needCheckUnlock)
end


---@public 判断活动是否处于开启状态
---@return boolean 不满足时返回的类型 -1/0/1/2/3/4/5  其他-1/没有数据0/不在活动时间内1/不满足解锁条件2/自定义条件不满足3
function IsOpenActivity(activityData, needCheckUnlock)
    if activityData == nil then
        return false, 0
    end
    --判断是否解锁 
    
    local timeOpen = (activityData.endTimeStamp and activityData.startTimeStamp) 
            and activityData.endTimeStamp > os.server_time() 
            and activityData.startTimeStamp < (os.server_time() + 2 ) --注意，这里故意提前俩秒，防止收到服务器时间因为心跳同步问题有偏差
    if not timeOpen then
        return false, 1
    end
    if not needCheckUnlock then
        return true
    end
    local function_open = require "function_open"
    local activityCfg = GetActivityCfgByActivityID(activityData.activityID)
    if activityCfg == nil then
        return false, -1
    end
    local open = function_open.CheckFunctionIsOpenByParas(activityCfg.BaseLevel, activityCfg.HooklevelID, activityCfg.SeverTime, nil, activityCfg.iBuildingID)
    if not open then
        return false, 2
    end
    --检测对应活动是否有自己独立的条件   
    local customCondition = festival_activity_cfg.CustomActivityUnlockCondition[activityData.activityCodeType]
    if customCondition then
        local success = customCondition(activityData.activityID)
        return success, (not success) and 3 or nil
    end
    return true
end
----------------------------------------表现上的功能接口--------------------------------------------

--region 标准入口
---@public 打开对应的活动UI 通过入口id和活动id
---@param entranceID number
---@param activityID number  活动id为可选项，为空则相当于直接跳转到对一个的入口活动中心，默认会打开上次的活动
---@param param table 活动参数
---des 活动ui打开时传的data的默认参数   activityID = self.curModuleActivityID, --传入当前活动ID
--                     uiPath = toggleInfo.uiTemplatePrefab, --传入当前模板预设ActivityCommonUI.csv中uiPath对应的配置
--                     uiParent = self.w_contentTrans, --绑定ui挂载节点
--                     isNewbie = _isNewbie,   --预留，是否是新手
--                     param = param
--预留功能函数回调        para.activityCenterBackBtnFun  跳转活动后，点击返回按钮的回调，会同时抛出俩个参数entranceID,activityID 返回时的活动入口+当前选择的活动ID
--预留功能函数回调  table para.activityUIOnOpenCallBack 对应活动UI成功打开后的回调(UI打开后就会触发) --如果不满足你需求，可以在 param.customOpenCallBack中自己实现
--预留功能函数回调  table para.customOpenCallBack 自定义打开活动回调（因为ui中可能需要等待网络数据等）--注意这个需要对应的目标ui去实现
function OpenActivityUIByEntranceID(entranceID, activityID,param)
    local activityCfg
    if not entranceID then
        if not activityID then
            log.Error("OpenActivityUIByEntranceID() 时entranceID and activityID 不能同时为空！")
            return
        end
        activityCfg = GetActivityCfgByActivityID(activityID)
        if activityCfg == nil then
            log.Error("活动配置未找到activityID= ",activityID)
            return
        end
        entranceID = activityCfg.AtyEntrance
    end
    if not entranceID or  entranceID == 0 then
        log.Error(string.format("对应的activityId(%s) 未配置活动入口！！！ ", tostring(activityID)))
        return
    end
    --检测活动是否开启
    if activityID and activityCfg == nil then
        activityCfg = GetActivityCfgByActivityID(activityID)
        if activityCfg == nil then
            log.Error("活动配置未找到activityID= ",activityID)
            return
        end       
    end
    if activityID then
        local function_open = require "function_open"
        local open = function_open.CheckFunctionIsOpenByParas(activityCfg.BaseLevel, activityCfg.HooklevelID, activityCfg.SeverTime, nil, activityCfg.iBuildingID,true)
        if not open then
            return false
        end
        local activityData = GetActivityDataByActivityID(activityID)
        if not activityData  then
            local flow_text = require "flow_text"
            flow_text.Add(lang.Get(15649))          
            return
        end  
        --如果是自定义的活动入口，则打开自己的
        local main_slg_common_define = require "main_slg_common_define"
        local custom = main_slg_common_define.customActivityButtonConfig[entranceID]
        if custom and custom.clickFunc then
            --注意，这里把activityID和entranceID传进去，但未维护main_slg_common_define中自定义的其他条件
            custom.clickFunc({activityID = activityID, entranceID = entranceID})
            return true
        end
    end
    local targetName = "ui_festival_activity_center"
    if not activityID  and  param then
        log.Error("未确定活动id时传入了参数？？？？？")
        return
    end
    --先判定活动中心是否打开，没打开则打开
    local ui_festival_activity_center = require(targetName)
    local alreadyOpen = ui_window_mgr:IsModuleShown(targetName)
    if alreadyOpen then
        local curEntrance = ui_festival_activity_center.GetEntranceId()
        --不是同一个活动中心
        if curEntrance and curEntrance ~= entranceID then
            ui_window_mgr:UnloadModule(targetName)
            ui_festival_activity_center.SetInputParam(nil, nil, nil, entranceID,param)
            ui_festival_activity_center.SetInitialModuleActivityID(activityID)
            ui_window_mgr:ShowModule(targetName)
        else
            --判定是不是同一个活动
            if activityID then
                local curActivityId = ui_festival_activity_center.GetCurActivityID()
                if curActivityId ~= activityID then
                    local curView = ui_window_mgr:GetWindowObj(targetName)
                    ui_festival_activity_center.SetInputParam(nil, nil, nil, entranceID,param)
                    curView:SwitchSubmoduleByActivityID(activityID,nil,param)
                    curView:TurnOnToggle(activityID)
                    ui_window_mgr:UpdateUIToTop(targetName)
                end
            end
        end
    else
        ui_festival_activity_center.SetInputParam(nil, nil, nil, entranceID,param)
        ui_festival_activity_center.SetInitialModuleActivityID(activityID)
        ui_window_mgr:ShowModule(targetName)
    end
    return true
end
---@public 打开活动 通过活动Id
---@param activityID number
---@param param table 活动参数
function OpenActivityUIByActivityID(activityID,param)   
    return  OpenActivityUIByEntranceID(nil, activityID,param)
end

---@public 尝试打开活动 通过活动Id（带返回值true or false）
---@param activityID number
---@param param table 活动参数
function TryOpenActivityUIByActivityID(activityID,param)   
    return OpenActivityUIByActivityID( activityID,param)
end

---@public 打开活动 通过活动类型 也就是codeType
---@param activityCodeType number 活动类型
---@param param table 活动参数
---注意，同一个类型有多个的时候，默认直接打开了第一个
function OpenActivityUIByActivityCodeType(activityCodeType,param)
    local activityData = GetActivityDataByActivityCodeType(activityCodeType)
    if not activityData then
        local flow_text = require "flow_text"
        flow_text.Add(lang.Get(15649))
        return
    end
    return OpenActivityUIByEntranceID(activityData.atyEntrance, activityData.activityID,param)
end
--endregion

--region 快捷入口Id
---@public 打开对应的活动UI 通过快捷入口Id
---@param QuickEntranceID number 快捷入口Id
---@param param table 活动参数
function OpenActivityUIByQuickEntranceID(QuickEntranceID,param)
    local targetV = nil
    for k, v in pairs(festivalActivityData) do
        if v.atyEntrance2 == QuickEntranceID then
            if targetV then
                --log.Error("一个快捷入口对应多个活动！配置错误了 快捷入口ID =", QuickEntranceID)
            end
            targetV = v
        end
    end
    if not targetV then
        log.Error(QuickEntranceID, "快捷入口没有找到对应的活动！快捷入口ID = ", QuickEntranceID)
        return
    end
    return OpenActivityUIByEntranceID(targetV.atyEntrance, targetV.activityID,param)
end

--endregion
----@public 关闭退出活动中心
function CloseActivityCenterUI(data)
    local module_name = "ui_festival_activity_center"
    local isShow = ui_window_mgr:IsModuleShown(module_name)
    if not isShow then
        return
    end
    ui_window_mgr:UnloadModule(module_name, nil, data)
end

--region 奖励领取的弹窗
-- 获得未解锁的战令充值ID
function Respond_GetActivityRewards(msgParam)
    if msgParam then
        -- 这里是战令手动排序一下
        local rewardData = reward_mgr.GetSummaryRewards(msgParam.rewardIds, true)
        if rewardData and #rewardData >= 1 then
            local listData = { title = "", dataList = rewardData }
            local showData = {}
            table.insert(showData, listData)
            local ui_reward_result = require "ui_reward_result_new"
            ui_reward_result.SetInputParam(showData, nil, nil, nil, nil, nil, nil, nil, nil, nil, true)
            ui_window_mgr:ShowModule("ui_reward_result_new")
        end
    end
end
--根据主活动入口获取dong 获取子活动id
function GetSubActiveId(actId)
    local activityId
    local ActivityMain = game_scheme:ActivityMain_0(actId)
    if ActivityMain then
        if ActivityMain.bindActivity.count > 0 and ActivityMain.bindActivity.data[0] == 1 then
            activityId = ActivityMain.bindActivity.data[1]
        else
            log.Error("GetBaseGiftList 主活动关联不对 actId = ", actId)
        end
    end
    return activityId
end

--region 登录自动请求活动数据，区分于活动开启才下发数据的逻辑

--[[// 获取活动数据
message TNewActivityData
{
optional uint32 atyID = 1;				//活动id
optional uint32 startTime = 2;			//本轮开始时间
optional uint32 endTime = 3;			//本轮结束时间
optional uint32 nextStartTime = 4;		//下轮开始时间
optional uint32 nextEndTime = 5;		//下轮结束时间
optional uint32 currentPeriod = 6;		//第N期活动
}]]

local requested_activityData = {}

--登录时请求活动数据
function LoginRequestActivityData()
    requested_activityData = {}
    if festival_activity_cfg.AutoRequestDataWhenLogin and #festival_activity_cfg.AutoRequestDataWhenLogin ~= 0 then
        local net_activity_module = require "net_activity_module"
        net_activity_module.MSG_ACTIVITY_DATA_REQ(festival_activity_cfg.AutoRequestDataWhenLogin)
    end
end

--服务器返回活动数据
function OnACTIVITY_DATA_RSP(_,msg)
    if msg.errorCode and msg.errorCode ~= 0 then
        log.Error(msg.errorCode + 100000)
        return
    end
    if msg.datas then
        for i,v in ipairs(msg.datas) do
            requested_activityData[v.atyID] = v
        end
    end
end

event.Register(event.FIRST_LOGIN_CREATE_DATA_FINISH,LoginRequestActivityData)
event.Register(event_activity_define.TMSG_ACTIVITY_DATA_RSP,OnACTIVITY_DATA_RSP)

--判断活动是否结束了,目前用于新手挑战赛隐藏试炼入口
function IsRequestedActivityEnd(atyID)
    if requested_activityData[atyID] then
        local curTime = os.server_time()
        local activityData = requested_activityData[atyID]
        if activityData.endTime and activityData.endTime > 0 and curTime < activityData.endTime then
            return false
        else
            return true
        end
    else
        return false
    end
end

--获取某个活动的开启时间等
function GetRequestedActivityData(atyID)
    if requested_activityData[atyID] then
        return requested_activityData[atyID]
    else
        return nil
    end
end

--endregion
---@public 数据重置
function Dispose()
    OnSceneDestroy()
    cacheFestivalActivityState = {}
    isLoginFirstReceiveData = true
end
----------------------------------------表现上的功能接口End--------------------------------------------
-- 注册事件响应
net_script.RegisterResponseLuaFuncNew("Respond_GetActivityRewards", Respond_GetActivityRewards)
event.Register(event.USER_DATA_RESET, Dispose)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, Dispose)
--endregion



