local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local typeof = typeof
local math = math
local UIUtil = CS.Common_Util.UIUtil
local GameObject    = CS.UnityEngine.GameObject
local Text          = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local ImageGray        = CS.War.UI.ImageGray
local SortingGroup  = CS.UnityEngine.Rendering.SortingGroup
local HorizontalLayoutGroup = CS.UnityEngine.UI.HorizontalLayoutGroup

local game_scheme = require "game_scheme"
local hero_item = require "hero_item_new"
local player_mgr = require "player_mgr"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local CModelViewer 		    = require "modelviewer"
local face_item = require "face_item_new"
local binding = require "ui_arena_battle_result_normal_binding"

--region View Life
module("ui_arena_battle_result_normal")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self.heroItem = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.heroItem then
        for k,v in pairs(self.heroItem) do
            if v then
                v:Dispose()
                v = nil
            end
        end
        self.heroItem = {}
    end
    if self.faceItemDefend then
        self.faceItemDefend:Dispose()
    end
    if self.faceItemAttack then
        self.faceItemAttack:Dispose()
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
function UIView:SetHeroList(data, maxDamageValue, maxHurtValue, maxHealValue, rootGameObj, mvpPos)
    if not data then
        return
    end
    local go = GameObject.Instantiate(rootGameObj, rootGameObj.transform.parent)
    if util.IsObjNull(go) then
        return
    end
    go.transform.localScale = {x=1, y= 1, z= 1}
    go.transform.localPosition = {x=0, y= 0, z= 0}
    self:SetActive(go, data ~= nil)
    local icon = go.transform:Find("Frame"):GetComponent(typeof(RectTransform))--头像
    local name = go.transform:Find("name"):GetComponent(typeof(Text))--名字
    local damageTxt = go.transform:Find("damage/num"):GetComponent(typeof(Text))--伤害
    local damagePro = go.transform:Find("damage/value"):GetComponent(typeof(RectTransform))
    local hurtTxt = go.transform:Find("hurt/num"):GetComponent(typeof(Text))--承伤
    local hurtPro = go.transform:Find("hurt/value"):GetComponent(typeof(RectTransform))
    local healTxt = go.transform:Find("heal/num"):GetComponent(typeof(Text))--治疗
    local healPro = go.transform:Find("heal/value"):GetComponent(typeof(RectTransform))
    local damageImg = go.transform:Find("damage/damageImg"):GetComponent(typeof(RectTransform))
    local hurtImg = go.transform:Find("hurt/hurtImg"):GetComponent(typeof(RectTransform))
    local healImg = go.transform:Find("heal/healImg"):GetComponent(typeof(RectTransform))
    local mvp = go.transform:Find("mvp"):GetComponent(typeof(ImageGray))
    local heroID = data.heroID
    local pos = data.pos
    local isHero = data.pos ~= 14 and data.pos ~= 13
    
    self:SetActive(damageImg, data.pos==0 or data.pos == 6)
    self:SetActive(hurtImg, data.pos==0 or data.pos == 6)
    self:SetActive(healImg, data.pos==0 or data.pos == 6)
    
    local isGray = false
    local scale = isHero and 0.7 or 0.75
    local result = self.isVictory
    if self.attackerId ~= player_mgr.GetPlayerRoleID() then
        --不是玩家自己为攻击方
        result = not isVictory
    end
    if isHero then
        isGray = data.statistics.isDead or (not data.statistics.hp) or (data.statistics.hp and data.statistics.hp <= 0)
        self.heroItem[pos] = hero_item.CHeroItem()
        local cfg_hero = game_scheme:Hero_0(heroID)
        if cfg_hero then
            name.text = lang.Get(cfg_hero.HeroNameID)
        end
    else
        isGray = not result
        local pet_item = require"pet_item"
        self.heroItem[pos] = pet_item.CPetItem()
        local helper_personalInfo = require "helper_personalInfo"
        local weaponCfg = helper_personalInfo.GetAnimalItemConfig(data.id)
        if weaponCfg then
            name.text = lang.Get(weaponCfg.nameId)
        end
    end
    self.heroItem[pos]:Init(icon.transform, function ()
        self.heroItem[pos]:SetHero(data)
        self.heroItem[pos]:GrayHeroIcon(isGray)--死亡英雄头像需要置灰
        self.heroItem[pos]:DisplayInfo()
    end, scale)

    damageTxt.text = util.PriceConvert(data.statistics.damage)
    hurtTxt.text = util.PriceConvert(data.statistics.hurt)
    healTxt.text = util.PriceConvert(data.statistics.heal)
    local damageX = data.statistics.damage/math.max(maxDamageValue,0.0001) < 1 and data.statistics.damage/math.max(maxDamageValue,0.0001) or 1
    local hurtX = data.statistics.hurt/math.max( maxHurtValue,0.0001) < 1 and data.statistics.hurt/math.max( maxHurtValue,0.0001) or 1
    local healX = data.statistics.heal/math.max(maxHealValue,0.0001) < 1 and data.statistics.heal/math.max(maxHealValue,0.0001) or 1
    damagePro.transform.localScale = {x=damageX,y=1,z=1}
    hurtPro.transform.localScale = {x=hurtX,y=1,z=1}
    healPro.transform.localScale = {x=healX,y=1,z=1}
    
    self:SetActive(mvp, mvpPos == pos)
end

function UIView:UpdateBattleDetails(HeroData)
    if not HeroData then
        return
    end
    local arena_battle_mgr = require "arena_battle_mgr"
    if self.heroItem then
        for k,v in pairs(self.heroItem) do
            if v then
                v:Dispose()
                v = nil
            end
        end
        self.heroItem = {}
    end
    local p = self.defenderHeroesTrans.transform
    for i = p.childCount-1,1,-1 do
        GameObject.Destroy(p:GetChild(i).gameObject)
    end
    local p2 = self.attackerHeroesTrans.transform
    for i = p2.childCount-1,1,-1 do
        GameObject.Destroy(p2:GetChild(i).gameObject)
    end
    local parentRect = nil
    local gameObj = nil
    local maxDamageValue = 0
    local maxHurtValue = 0
    local maxHealValue = 0
    local attackerCount = 0
    local defenderCount = 0
    local mvpPos = arena_battle_mgr.CalculateMvp(HeroData)
    
    for i = 0, 14 do
        local data = HeroData[i]
        if data then
            if maxDamageValue < data.statistics.damage then
                maxDamageValue = data.statistics.damage
            end
            if maxHurtValue < data.statistics.hurt then
                maxHurtValue = data.statistics.hurt
            end
            if maxHealValue < data.statistics.heal then
                maxHealValue = data.statistics.heal
            end
            if i>=0 and i<=5 or i == 13 then
                attackerCount = attackerCount + 1
            elseif i >= 6 or i == 14 then
                defenderCount = defenderCount + 1
            end
        end
    end

--[[    p:GetComponent(typeof(HorizontalLayoutGroup)).childAlignment = attackerCount <= 6 and 4 or 3
    p2:GetComponent(typeof(HorizontalLayoutGroup)).childAlignment = defenderCount <= 6 and 4 or 3]]
    
    p.pivot = attackerCount >6 and { x=0, y=0.5} or { x=0.5, y=0.5}
    p2.pivot = defenderCount >6 and { x=0, y=0.5} or { x=0.5, y=0.5}
    
    for i = 0, 14 do
        if i>=0 and i<=5 or i==13 then
            parentRect = p
            gameObj = self.rtf_attackHeroItem.gameObject
        elseif i >= 6 or i == 14 then
            parentRect = p2
            gameObj = self.rtf_defendHeroItem.gameObject
        end
        local data = HeroData[i]
        local battle_data = require"battle_data"
        if data and (data.pos == 14 or data.pos == 13) then
            data.id = battle_data.GetWeaponAdorn(data.pos)
        end
        if self:IsValid() then
            self:SetHeroList(data, maxDamageValue, maxHurtValue , maxHealValue, gameObj, mvpPos)
        end
    end
end

function UIView:LoadResultEffect()
    if true then
        return
    end
    local modelPath = ""
    if self.isVictory then
        modelPath = "art/effects/prefabs/ui/ui_zhandoushenglixin.prefab"
        if lang.USE_LANG ~= lang.ZH then
            modelPath = "art/effects/prefabs/ui/ui_zhandoushenglixin_en.prefab"
        end
    else
        modelPath = "art/effects/prefabs/ui/ui_zhandoushibaixin.prefab"
        if lang.USE_LANG ~= lang.ZH then
            modelPath = "art/effects/prefabs/ui/ui_zhandoushibaixin_en.prefab"
        end
    end
    if self.effectModelViewer then
        self.effectModelViewer:Dispose()
        self.effectModelViewer = nil
    end

    if self.effectModelViewer == nil then
        self.effectModelViewer = CModelViewer()
        self.effectModelViewer:Init(self.rtf_effectTrans.transform, function()
            self.effectModelViewer:ShowGameObject(modelPath, function(goEffect)
                local sortingGroup = goEffect:GetComponent(typeof(SortingGroup))
                if not sortingGroup or util.IsObjNull(sortingGroup) then
                    sortingGroup = goEffect:AddComponent(typeof(SortingGroup))
                end
                sortingGroup.sortingOrder = self.orderEnd
            end)
        end)
    end
end

function UIView:InitPlayerInfo(playerInfo, nameObj, headObj, scoreObj, scoreChangeObj, faceItem)
    if not playerInfo then
        return
    end
    if util.IsObjNull(nameObj) then
        return
    end
    if util.IsObjNull(headObj) then
        return
    end
    if util.IsObjNull(scoreObj) then
        return
    end
    if util.IsObjNull(scoreChangeObj) then
        return
    end
    nameObj.text = string.format("%s %s", playerInfo.worldStr, util.SplicingUnionShortName(playerInfo.unionShortName, playerInfo.name, true))
    local item = faceItem or face_item.CFaceItem()
    item:Init(headObj.transform, nil, 0.7)
    item:SetNewBg(true)
    item:SetFaceInfo(playerInfo.faceStr, function()
        if playerInfo.roleID ~= player_mgr.GetPlayerRoleID() then
            local mgr_personalInfo = require "mgr_personalInfo"
            mgr_personalInfo.ShowRoleInfoView(playerInfo.roleID)
        end
    end)
    item:SetActorLvText(true, playerInfo.roleLv)
    item:FrameEffectEnable(true, self.curOrder+1)
    item:SetFrameID(playerInfo.frameID, true)
    item:SetEnableState(true,true)
    faceItem = item

    scoreObj.text = playerInfo.oldScore
    scoreChangeObj.text = playerInfo.scoreChange
end

function UIView:InitBaseData(data)
    if not data then
        return
    end
    self.isVictory = data.isVictory
    self.attackerId = data.attackerId
    self:UpdateBattleDetails(data.HeroData)
    self:InitPlayerInfo(data.defendInfo, self.rtf_defendName, self.rtf_defendTra, self.rtf_defendScore, self.rtf_defendScoreChange, self.faceItemDefend)
    self:InitPlayerInfo(data.attackInfo, self.rtf_attackName, self.rtf_attackTra, self.rtf_attackScore, self.rtf_attackScoreChange, self.faceItemAttack)
    self:LoadResultEffect()
    
    --1007471 排名
    self.txt_oldRank.text = string.format("%s %s", lang.Get(1007471), data.oldRank)
    self.txt_curRank.text = data.curRank
    self.ss_rankChange:Switch(data.oldRank > data.curRank and 0 or 1)
    
    --设置标题
    self:SetWinUIActive()
    self:SetDefaultUIActive()
    
end

function UIView:SetWinUIActive()
    local isZH = lang.USE_LANG == lang.ZH

    self:SetActive(self.rtf_winText, isZH)
    self:SetActive(self.rtf_winLeftBG, isZH)
    self:SetActive(self.rtf_winRightBG, isZH)
    self:SetActive(self.rtf_victoryEffect, isZH)
    self:SetActive(self.rtf_victoryEffect1, isZH)
    self:SetActive(self.rtf_winTextEn, not isZH)
    self:ChangeLangRes("victory_shengli",self.img_winTextEn)
end

function UIView:SetDefaultUIActive()
    local isZH = lang.USE_LANG == lang.ZH
    self:SetActive(self.rtf_loseText, isZH)
    self:SetActive(self.rtf_loseLeftBG, isZH)
    self:SetActive(self.rtf_loseRightBG, isZH)
    self:SetActive(self.rtf_defeatEffect, isZH)
    self:SetActive(self.rtf_loseEnText, not isZH)
    self:ChangeLangRes("lose_shibai",self.img_loseEnText)
end

function UIView:ChangeLangRes(ImgPath,Img)
    local oversea_res = require "oversea_res"
    local res = ImgPath
    local callBack = function(asset)
        if self:IsValid() then
            if asset then
                --加载多语言图片成功后的回调
                Img.sprite = asset
            end
        end
    end
    return oversea_res.LoadSprite(res,"ui_arena_battle_result_normal",callBack)
end


--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
