local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local item_rank_reward_template1 = require "item_rank_reward_template1"
local item_reward_template1 = require "item_reward_template1"
local reward_mgr = require "reward_mgr"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_arena_reward_binding"

--region View Life
module("ui_arena_reward")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self:InitScrollRectTable()
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.isInitPerson = false
    self.isInitDaily = false
    self.isInitAlliance = false
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:InitScrollRectTable()
    --当前换页的时候采用直接换了一个滑动组件  --测试对比每次重新生成
    self.onPersonRewardRender = function(...)
        self:OnPersonRewardRender(...)
    end
    self.srt_content_person.onItemRender=self.onPersonRewardRender
    self.srt_content_person.onItemDispose=function(scroll_rect_item,index)
        if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data.rewardList then
            for i, v in ipairs(scroll_rect_item.data.rewardList) do
                if v then
                    v:Dispose()
                end
            end
            scroll_rect_item.data.rewardList = nil
        end
    end

    self.onDailyRewardRender = function(...)
        self:OnDailyRewardRender(...)
    end
    self.srt_content_daily.onItemRender=self.onDailyRewardRender
    self.srt_content_daily.onItemDispose=function(scroll_rect_item,index)
        if scroll_rect_item.data and scroll_rect_item.data["templateItem"] then
            scroll_rect_item.data["templateItem"]:Dispose()
        end
    end

    self.onAllianceRewardRender = function(...)
        self:OnAllianceRewardRender(...)
    end
    self.srt_content_alliance.onItemRender=self.onAllianceRewardRender
    self.srt_content_alliance.onItemDispose=function(scroll_rect_item,index)
        if scroll_rect_item.data and scroll_rect_item.data["templateItem"] then
            scroll_rect_item.data["templateItem"]:Dispose()
        end
    end
end

function UIView:ShowPanel(index, dataList)
    self:SetActive(self.rtf_Viewport1, index == 1)
    self:SetActive(self.rtf_Viewport2, index == 2)
    self:SetActive(self.rtf_Viewport3, index == 3)
    if not dataList then
        return
    end
    if index == 2 and not self.isInitDaily then
        self.isInitDaily = true
        self.srt_content_daily.data = dataList
        self.srt_content_daily:Refresh(0, -1)
    elseif index == 3 and not self.isInitAlliance then
        self.isInitAlliance = true
        self.srt_content_alliance.data = dataList
        self.srt_content_alliance:Refresh(0, -1)
    elseif index == 1 and not self.isInitPerson then
        self.isInitPerson = true
        self.srt_content_person.data = dataList
        self.srt_content_person:Refresh(0, -1)
    end
end

--初始化个人奖励
function UIView:OnPersonRewardRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local rank1 = scroll_rect_item:Get("rank1")
    local rank2 = scroll_rect_item:Get("rank2")
    local rank3 = scroll_rect_item:Get("rank3")
    local rank4 = scroll_rect_item:Get("rank4")
    local rewardList = scroll_rect_item:Get("rewardList")
    local rankBgSS = scroll_rect_item:Get("rankBgSS")
    local rankNum = tonumber(dataItem.rankNum)
    self:SetActive(rank1, rankNum == 1)
    self:SetActive(rank2, rankNum == 2)
    self:SetActive(rank3, rankNum == 3)
    self:SetActive(rank4, rankNum == nil or rankNum > 3)
    if rankNum and rankNum<=3 then
        rankBgSS:Switch(rankNum-1)
    end
    self:SetActive(rankBgSS, rankNum and rankNum<=3)
    if rankNum == nil or rankNum > 3 then
        rank4.text = dataItem.rankDesc
    end
    scroll_rect_item.data.rewardList = reward_mgr.GetRewardItemList(dataItem.reward, rewardList, true, 0.6)
end
--初始化联盟奖励
function UIView:OnAllianceRewardRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    --绑定上点击等事件，主要是为了抛给controller层进行分离   
    scroll_rect_item.InvokeFunc = scroll_rect_item.InvokeFunc or function(funcname,obj)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index,dataItem)
        end
    end
    if scroll_rect_item.data["templateItem"] then
        scroll_rect_item.data["templateItem"]:Dispose()
    end
    local title = dataItem.title
    scroll_rect_item.data["templateItem"] = item_rank_reward_template1.NewItem(scroll_rect_item.transform,index,dataItem.reward,title)
    scroll_rect_item.data["templateItem"]:SetTipClickFunc(dataItem.GetTipFunc )
end
--初始化每日奖励
function UIView:OnDailyRewardRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    --绑定上点击等事件，主要是为了抛给controller层进行分离   
    scroll_rect_item.InvokeFunc =  function(funcname,obj)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index,dataItem)
        end
    end
    if scroll_rect_item.data["templateItem"] then
        scroll_rect_item.data["templateItem"]:Dispose()
    end
    local title = dataItem.title
    scroll_rect_item.data["templateItem"] = item_reward_template1.NewItem(scroll_rect_item.transform,title,dataItem.reward)
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
