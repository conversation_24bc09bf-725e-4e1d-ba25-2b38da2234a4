local require = require
local table = table
local pairs = pairs
local GameObject = CS.UnityEngine.GameObject
local util = require "util"
module("LuaGameObjectPool")

local PoolGameObject =nil;--GameObject.Find("Pool")
if util.IsObjNull(PoolGameObject) then
    PoolGameObject=GameObject()
    util.DontDestroyOnLoad(PoolGameObject)
    PoolGameObject.name="LuaGameObjectPool"
    PoolGameObject:SetActive(false)
else
    PoolGameObject=PoolGameObject.gameObject;
end

function GetParentTrans()
    return PoolGameObject.transform;
end

local poolSize=300;
local cSize=0;
local AllGameObjectMap = {};

--[[
    @desc: 获得一个table,table本身可能存在老的数据，需要自己记录维护有效元素，存的数据以虚表方式存在，可能会被提前回收
    author:{author}
    time:2021-06-03
    @return:
]]
function Instantiate(typeName,gameObject,parentTrans,instantiateInWorldSpace)

    local gameObjectItem=nil;
    if instantiateInWorldSpace==nil then
        instantiateInWorldSpace=false
    end
    if typeName then
        local gameObjectList= AllGameObjectMap[typeName]
        if gameObjectList and #gameObjectList>0 then
            gameObjectItem = gameObjectList[1];
            table.remove(gameObjectList, 1)
            gameObjectItem.transform:SetParent(parentTrans,instantiateInWorldSpace)
            gameObjectItem.transform.localPosition = { x = 0, y = 0, z = 0 }
            cSize=cSize-1;
        end
    end


    if util.IsObjNull(gameObjectItem) then
        if parentTrans==nil then
            gameObjectItem= GameObject.Instantiate(gameObject)
        else
            gameObjectItem= GameObject.Instantiate(gameObject,parentTrans,instantiateInWorldSpace)
        end
    end


    return  gameObjectItem;

end

function Release(typeName,gameObject,instantiateInWorldSpace)
    if util.IsObjNull(gameObject) then
        return
    end
    if cSize> poolSize then
        GameObject.DestroyImmediate(gameObject)
        return;
    end
    local gameObjectList= AllGameObjectMap[typeName]
    if  gameObjectList==nil then
        gameObjectList={};
        AllGameObjectMap[typeName]=gameObjectList;
    end
    table.insert(gameObjectList,gameObject);
    cSize=cSize+1;
    if instantiateInWorldSpace==nil then
        instantiateInWorldSpace=false
    end
    gameObject.transform:SetParent(PoolGameObject.transform,instantiateInWorldSpace);
end


--初始模板
local isInitComType=false;
function InitItemComType()
    if isInitComType then
        return
    end
    isInitComType=true;
    local util = require "util"
    local delayTime=0.2;
    local item;
    local goods_item = require "goods_item_com"
    for i, v in pairs(goods_item.ComCfgType) do
        if v~=goods_item.ComCfgType.BaseType and v~=goods_item.ComCfgType.AllType then
            util.DelayCall(delayTime, function ()
                item= goods_item.CGoodsItem():Init(GetParentTrans(),function()
                    util.DelayCall(1, function ()
                        item:Dispose()
                    end)

                end,1,v)
            end)
            delayTime=delayTime+0.2;
        end
    end

    local hero_item = require "hero_item_com"
    for i, v in pairs(hero_item.ComCfgType) do
        if v~=hero_item.ComCfgType.BaseType and v~=hero_item.ComCfgType.AllType then
            util.DelayCall(delayTime, function ()
                item= hero_item.CHeroItem():Init(GetParentTrans(),function()
                    util.DelayCall(1, function ()
                        item:Dispose()
                    end)
                end,1,v)
            end)
            delayTime=delayTime+0.2;
        end
    end

end


