--- alliance_data.txt  --------------------------------------
--- author: 韩淑俊
--- Date:   2024/6/20 14:43
--- ver:    1.0
--- desc:   联盟Net层处理
-------------------------------------------------------------
local require = require
local print = print
local type = type

local event = require "event"
local xManMsg_pb = require "xManMsg_pb"
local msg_pb = require "msg_pb"
local alliance_pb = require "alliance_pb"
local net = require "net"
local net_route = require "net_route"
local log = require "log"
local lang = require "lang"
local alliance_data = require "alliance_data"
local alliance_user_data = require "alliance_user_data"
local alliance_gift_data = require "alliance_gift_data"
local alliance_function_data = require "alliance_function_data"
local alliance_rank_data = require "alliance_rank_data"
local alliance_mutualAid_data = require "alliance_mutualAid_data"
local alliance_medal_data = require "alliance_medal_data"
local flow_text = require "flow_text"
local event_alliance_define = require "event_alliance_define"
module("net_alliance_module")


--region  #####      创建联盟      #####
-- 推荐联盟请求
function MSG_ALLIANCE_RECOMMEND_REQ(languageId)
    local msg = alliance_pb.TMSG_ALLIANCE_RECOMMEND_REQ()
    msg.languageId = languageId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_RECOMMEND_REQ, msg)
end

-- 推荐联盟响应
function MSG_ALLIANCE_RECOMMEND_RSP(msg)
    alliance_data.UpdateAllianceData(msg)
end


-- 查找联盟请求
function MSG_ALLIANCE_SEARCH_REQ(strName)
    local msg = alliance_pb.TMSG_ALLIANCE_SEARCH_REQ()
    msg.strName = strName
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_SEARCH_REQ, msg)
end

-- 查找联盟响应
function MSG_ALLIANCE_SEARCH_RSP(msg)
    if Error_Code(msg) then
        alliance_data.SelectUpdateAllianceData(msg)
    end
end

-- 联盟基本信息请求
function MSG_ALLIANCE_INFO_REQ(allianceId)
    local msg = alliance_pb.TMSG_ALLIANCE_INFO_REQ()
    msg.allianceId = allianceId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_INFO_REQ, msg)

end

-- 联盟基本信息响应
function MSG_ALLIANCE_INFO_RSP(msg)
    if Error_Code(msg) then
        alliance_data.UpdateLookAllianceBaseInfo(msg)
    end
end

-- 联盟成员详情请求
function MSG_ALLIANCE_ROLE_INFO_REQ(allianceId)
    local msg = alliance_pb.TMSG_ALLIANCE_ROLE_INFO_REQ()
    msg.allianceId = allianceId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_ROLE_INFO_REQ, msg)
end

-- 联盟成员详情响应
function MSG_ALLIANCE_ROLE_INFO_RSP(msg)
    if Error_Code(msg) then
        alliance_user_data.UpdateLookAllianceRoleData(msg)
        
        event.Trigger(event_alliance_define.ALLIANCE_MEMBER_DATA_RSP)
    end
end

-- 快速加入联盟请求
function MSG_ALLIANCE_QUICK_ADD_REQ()
    local msg = alliance_pb.TMSG_ALLIANCE_QUICK_ADD_REQ()
    local id = alliance_data.GetQuickAddID()
    msg.allianceId = 0
    if id then
        msg.allianceId = id
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_QUICK_ADD_REQ, msg)
end

-- 快速加入联盟响应
function MSG_ALLIANCE_QUICK_ADD_RSP(msg)
    if Error_Code(msg) then
        if msg.leaveAllianceTime then
            alliance_data.RefreshLeaveAllianceTime(msg.leaveAllianceTime)
        end
    end
end

-- 申请加入联盟请求
function MSG_ALLIANCE_APPLY_REQ(allianceId, isInvitation)
    local msg = alliance_pb.TMSG_ALLIANCE_APPLY_REQ()
    msg.allianceId = allianceId
    msg.joinType = isInvitation and alliance_pb.EAllianceJoinType_invitation or alliance_pb.EAllianceJoinType_default
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_APPLY_REQ, msg)
end

-- 申请加入联盟响应
function MSG_ALLIANCE_APPLY_RSP(msg)
    if Error_Code(msg) then
        if msg.applyType == alliance_pb.emAllianceApplyType_Apply then
            flow_text.Add(lang.Get(600620))
        end
        alliance_data.RefreshApplyRsp(msg)
    else
        if msg.leaveAllianceTime then
            alliance_data.RefreshLeaveAllianceTime(msg.leaveAllianceTime)
        end
    end
end

-- 检查联盟名称/简称是否可用请求
function MSG_ALLIANCE_CHECKNAME_REQ(name, type)
    local msg = alliance_pb.TMSG_ALLIANCE_CHECKNAME_REQ()
    msg.checkName = name
    msg.checkType = type
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_CHECKNAME_REQ, msg)
end

-- 检查联盟名称/简称是否可用响应
function MSG_ALLIANCE_CHECKNAME_RSP(msg)
    --[[if Error_Code(msg) then
        
    end]]
    alliance_data.RefreshNetAllianceName(msg)
end

-- 随机生成一个可用的简称请求
function MSG_ALLIANCE_RANDOM_NAME_REQ()
    local msg = alliance_pb.TMSG_ALLIANCE_RANDOM_NAME_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_RANDOM_NAME_REQ, msg)
end

-- 随机生成一个可用的简称响应
function MSG_ALLIANCE_RANDOM_NAME_RSP(msg)
    if Error_Code(msg) then
        alliance_data.UpdateRandomNameData(msg)
    end
end

-- 创建联盟请求
function MSG_ALLIANCE_CREATE_REQ(name, shortName, flag, language,nationalFlagID)
    local msg = alliance_pb.TMSG_ALLIANCE_CREATE_REQ()
    msg.name = name
    msg.shortName = shortName
    msg.flag = flag
    msg.language = language
    msg.nationalFlagID = nationalFlagID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_CREATE_REQ, msg)
end

-- 创建联盟响应
function MSG_ALLIANCE_CREATE_RSP(msg)
    if Error_Code(msg) then
        alliance_data.CreateAllianceRsp(msg)
    else
        if msg.leaveAllianceTime then
            alliance_data.RefreshLeaveAllianceTime(msg.leaveAllianceTime)
        end
    end
end

--endregion

-- 成员升阶/降级请求  官职、解除官职
function MSG_ALLIANCE_AUTHORITY_REQ(authority, roleId, position)
    local msg = alliance_pb.TMSG_ALLIANCE_AUTHORITY_REQ()
    msg.authority = authority
    msg.roleId = roleId
    if position then
        msg.position = position
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_AUTHORITY_REQ, msg)
end

-- 成员升阶/降级响应 官职、解除官职
function MSG_ALLIANCE_AUTHORITY_RSP(msg)
    if Error_Code(msg) then
        alliance_user_data.AllianceAuthorityRsp(msg)
    end
end

-- 开除成员请求
function MSG_ALLIANCE_EXPEL_REQ(roleId)
    local msg = alliance_pb.TMSG_ALLIANCE_EXPEL_REQ()
    msg.roleId = roleId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_EXPEL_REQ, msg)
end

-- 开除成员响应
function MSG_ALLIANCE_EXPEL_RSP(msg)
    if Error_Code(msg) then
        local alliance_ui_util = require "alliance_ui_util"
        local AllianceMgr = require "alliance_mgr"
        local isExist, authority = AllianceMgr.GetRoleAuthority(msg.roleId)
        local reportMsg = {
            Role_ID = msg.roleId and msg.roleId or 0, --被踢玩家角色ID
            Member_Level = authority, --被踢玩家联盟阶级
        }
        alliance_ui_util.EventReport("league_KickOutMembers", reportMsg, true)
        alliance_user_data.AllianceExpel(msg)
    end
end
--(一键)领取 战利品/盟友赠礼 宝箱请求
function MSG_ALLIANCE_GIFT_GET_REQ(giftType, oneKey, boxId)
    local msg = alliance_pb.TMSG_ALLIANCE_GIFT_GET_REQ()
    msg.giftType = giftType
    msg.oneKey = oneKey
    if boxId then
        msg.boxId = boxId
    else
        msg.boxId = "0"
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_GIFT_GET_REQ, msg)
end
--(一键)领取 战利品/盟友赠礼 宝箱响应
function MSG_ALLIANCE_GIFT_GET_RSP(msg)
    if Error_Code(msg) then
        alliance_gift_data.ReceiveDataRsp(msg)
        event.Trigger(event_alliance_define.ALL_GIFT_RECEIVE,msg)
    end
end
-- 联盟礼物推送详情响应
function MSG_ALLIANCE_GIFT_NTF(msg)
    if msg.sendType == alliance_pb.EAllianceGiftSendType_Login then
        alliance_gift_data.InitNTF(msg)
    elseif msg.sendType == alliance_pb.EAllianceGiftSendType_Produce then
        alliance_gift_data.RefreshNTF(msg)
        flow_text.Add(lang.Get(600582))
    end
end

-- 开启匿名赠送盟友礼物请求  alliance_pb.emAllianceRSwitchOpt_Open
function MSG_ALLIANCE_ANONYMOUS_REQ(switchOpt)
    local msg = alliance_pb.TMSG_ALLIANCE_ANONYMOUS_REQ()
    msg.switchOpt = switchOpt
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_ANONYMOUS_REQ, msg)
end

-- 开启匿名赠送盟友礼物响应
function MSG_ALLIANCE_ANONYMOUS_RSP(msg)
    if Error_Code(msg) then
        alliance_gift_data.UpdateAnonymousData(msg)
    end
end

-- 联盟科技信息请求
function MSG_ALLIANCE_TECHNOLOGY_REQ(data)
    local msg = alliance_pb.TMSG_ALLIANCE_TECHNOLOGY_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_TECHNOLOGY_REQ, msg)
end

-- 联盟科技信息响应
function MSG_ALLIANCE_TECHNOLOGY_RSP(msg)
    if Error_Code(msg) then
        alliance_function_data.UpdateTechnoloyData(msg)
    end
end
--科技基本信息请求
function MSG_ALLIANCE_TECHNOLOGY_DETAILS_REQ(id)
    local msg = alliance_pb.TMSG_ALLIANCE_TECHNOLOGY_DETAILS_REQ()
    msg.effectIDs = id
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_TECHNOLOGY_DETAILS_REQ, msg)
end
--科技基本信息响应
function MSG_ALLIANCE_TECHNOLOGY_DETAILS_RSP(msg)
    if Error_Code(msg) then
        alliance_function_data.UpdateTechnoloyInfo(msg)
    end
end
-- 设置/取消联盟推荐请求  opt 1 设置 2 取消
function MSG_ALLIANCE_TECHNOLOGY_RECOMMEND_REQ(effectIDs, opt)
    local msg = alliance_pb.TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_REQ()
    msg.effectIDs = effectIDs
    msg.opt = opt
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_TECHNOLOGY_RECOMMEND_REQ, msg)
end
-- 设置/取消联盟推荐响应
function MSG_ALLIANCE_TECHNOLOGY_RECOMMEND_RSP(msg)
    if Error_Code(msg) then
        alliance_function_data.UpdateRecommendData(msg)
    end
end

-- 解锁/研究科技请求
function MSG_ALLIANCE_TECHNOLOGY_STUDY_REQ(effectIDs)
    local msg = alliance_pb.TMSG_ALLIANCE_TECHNOLOGY_STUDY_REQ()
    msg.effectIDs = effectIDs
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_TECHNOLOGY_STUDY_REQ, msg)
end

-- 解锁/研究科技响应
function MSG_ALLIANCE_TECHNOLOGY_STUDY_RSP(msg)
    if Error_Code(msg) then
        alliance_function_data.ResearchRsp(msg)
    end
end

-- 捐献钻石/金币请求 1：金币 2：钻石
function MSG_ALLIANCE_TECHNOLOGY_DONATE_REQ(opt, effectIDs)
    local msg = alliance_pb.TMSG_ALLIANCE_TECHNOLOGY_DONATE_REQ()
    msg.opt = opt
    msg.effectIDs = effectIDs
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_TECHNOLOGY_DONATE_REQ, msg)
end

-- 捐献钻石/金币响应
function MSG_ALLIANCE_TECHNOLOGY_DONATE_RSP(msg)
    alliance_function_data.DonateRsp(msg)
end

-- 修改联盟公告请求
function MSG_ALLIANCE_MODIFY_ANNOUNCEMENT_REQ(content)
    local msg = alliance_pb.TMSG_ALLIANCE_MODIFY_ANNOUNCEMENT_REQ()
    msg.content = content
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MODIFY_ANNOUNCEMENT_REQ, msg)
end

-- 修改联盟公告响应
function MSG_ALLIANCE_MODIFY_ANNOUNCEMENT_RSP(msg)
    if Error_Code(msg) then
        event.Trigger(event_alliance_define.NET_ALLIANCE_SEND_NOTICE_RSP, msg)
    end
end

-- 查看申请列表请求
function MSG_ALLIANCE_APPLICATION_LIST_REQ(data)
    local msg = alliance_pb.TMSG_ALLIANCE_APPLICATION_LIST_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_APPLICATION_LIST_REQ, msg)
end

-- 查看申请列表响应
function MSG_ALLIANCE_APPLICATION_LIST_RSP(msg)
    if Error_Code(msg) then
        alliance_user_data.UpdateApplyDataRsp(msg)
    end
end

-- 同意/拒绝联盟申请请求 opt  1 同意  2 拒绝
function MSG_ALLIANCE_HANDLE_APPLICATION_REQ(opt, roleId)
    local msg = alliance_pb.TMSG_ALLIANCE_HANDLE_APPLICATION_REQ()
    msg.opt = opt
    msg.roleId = roleId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_HANDLE_APPLICATION_REQ, msg)
end

-- 同意/拒绝联盟申请响应
function MSG_ALLIANCE_HANDLE_APPLICATION_RSP(msg)
    Error_Code(msg)
    alliance_user_data.RemoveApplyData(msg.roleId)
end

-- 修改联盟旗帜请求
function MSG_ALLIANCE_MODIFY_FLAG_REQ(flag)
    local msg = alliance_pb.TMSG_ALLIANCE_MODIFY_FLAG_REQ()
    msg.flag = flag
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MODIFY_FLAG_REQ, msg)
end

-- 修改联盟旗帜响应
function MSG_ALLIANCE_MODIFY_FLAG_RSP(msg)
    if Error_Code(msg) then
        alliance_data.SetModifyFlagData(msg)
    end
end

-- 修改联盟名称/简称请求
function MSG_ALLIANCE_MODIFY_NAME_REQ(name, type)
    local msg = alliance_pb.TMSG_ALLIANCE_MODIFY_NAME_REQ()
    msg.modifyType = type
    msg.modifyName = name
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MODIFY_NAME_REQ, msg)
end

-- 修改联盟名称/简称响应
function MSG_ALLIANCE_MODIFY_NAME_RSP(msg)
    if Error_Code(msg) then
        local alliance_ui_util = require "alliance_ui_util"
        if msg.modifyType == alliance_pb.emAllianceNameType_Name then
            local reportMsg = {
                Old_AllianceName = alliance_data.GetUserAllianceName(),
                New_AllianceName = msg.modifyName,
            }
            alliance_ui_util.EventReport("league_ChangeAllianceName", reportMsg, false)
        elseif msg.modifyType == alliance_pb.emAllianceNameType_ShortName then
            local reportMsg = {
                Old_AllianceCode = alliance_data.GetUserAllianceShortName(),
                New_AllianceCode = msg.modifyName,
            }
            alliance_ui_util.EventReport("league_ChangeAllianceCode", reportMsg, false)
        end
        alliance_data.SetModifyNameData(msg)
    end
end

-- 修改联盟语言请求
function MSG_ALLIANCE_MODIFY_LANGUAGE_REQ(language)
    local msg = alliance_pb.TMSG_ALLIANCE_MODIFY_LANGUAGE_REQ()
    msg.language = language
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MODIFY_LANGUAGE_REQ, msg)
end

-- 修改联盟语言响应
function MSG_ALLIANCE_MODIFY_LANGUAGE_RSP(msg)
    if Error_Code(msg) then
        local alliance_ui_util = require "alliance_ui_util"
        local reportMsg = {
            Old_AllianceLanguage = lang.Get(alliance_data.GetUserAllianceLanguage()),
            New_AllianceLanguage = lang.Get(msg.language),
        }
        alliance_ui_util.EventReport("league_ChangeLanguage", reportMsg, false)
        alliance_data.SetModifyLanguageData(msg)
    end
end

-- 修改联盟入盟条件请求
function MSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ(applySet, power, headOffice, clearSet)
    local msg = alliance_pb.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ()
    msg.applySet = applySet
    msg.power = power
    msg.headOffice = headOffice
    msg.clearSet = clearSet
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ, msg)
end

-- 修改联盟入盟条件响应
function MSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP(msg)
    if Error_Code(msg) then
        alliance_data.SetModifyJoinConditions(msg)
    end
end

-- 联盟邮件发送请求
function MSG_ALLIANCE_MAIL_SEND_REQ(data)
    local msg = alliance_pb.TMSG_ALLIANCE_MAIL_SEND_REQ()
    msg.content = data.content
    msg.title = data.title
    msg.sendAuthority = data.sendAuthority
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MAIL_SEND_REQ, msg)
end

-- 联盟邮件发送响应
function MSG_ALLIANCE_MAIL_SEND_RSP(msg)
    if Error_Code(msg) then
        --flow_text.Add("##配置多语言 --- 发送成功")
        flow_text.Add(lang.Get(600393))
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_MAIL_DATA)
        local AllianceMgr = require "alliance_mgr"
        local alliance_ui_util = require "alliance_ui_util"
        local authority = AllianceMgr.GetPlayerAuthority()
        local reportMsg = {
            Member_Level = authority and authority or 0,
        }
        alliance_ui_util.EventReport("league_SendAllianceEmail", reportMsg, true)
    end
end

-- 联盟数据更新通知
function MSG_ALLIANCE_UPDATE_NTF(msg)
    alliance_data.RefreshAllianceNtf(msg)
    event.Trigger(event_alliance_define.UPDATE_ALLIANCE_MARK, msg)
    --国旗自己组织数据
    require("national_flag_mgr").SetAllianceNationalFlagInfoByServer(msg)
end

-- 联盟杀敌排行榜请求
function MSG_ALLIANCE_KILLNUM_REQ(worldId)
    local msg = alliance_pb.TMSG_ALLIANCE_KILLNUM_REQ()
    local setting_server_data = require "setting_server_data"
    msg.toWorldId = worldId or setting_server_data.GetLoginWorldID()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_KILLNUM_REQ, msg)
end
--联盟杀敌排行榜响应
function MSG_ALLIANCE_KILLNUM_RSP(msg)
    if Error_Code(msg) then
        alliance_data.UpdateAllianceRankData(msg)
    end
end

-- 联盟战力排行请求
function MSG_ALLIANCE_POWERRANK_REQ(worldId)
    local msg = alliance_pb.TMSG_ALLIANCE_POWERRANK_REQ()
    local setting_server_data = require "setting_server_data"
    msg.toWorldId = worldId or setting_server_data.GetLoginWorldID()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_POWERRANK_REQ, msg)
end
--联盟战力排行回应
function MSG_ALLIANCE_POWERRANK_RSP(msg)
    if Error_Code(msg) then
        alliance_data.UpdateAllianceRankData(msg)
    end
end
--1; //战力 2; //击杀 3; //每日捐献 4; //每日捐献
function MSG_ALLIANCE_ROLERANK_REQ(rankType)
    local msg = alliance_pb.TMSG_ALLIANCE_ROLERANK_REQ()
    msg.rankType = rankType
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_ROLERANK_REQ, msg)
end
--联盟玩家战力排行回应
function MSG_ALLIANCE_ROLERANK_RSP(msg)
    if Error_Code(msg) then
        alliance_rank_data.UpdateRoleRankData(msg)
    end
end
--退出联盟
function MSG_ALLIANCE_EXIT_REQ()
    local msg = alliance_pb.TMSG_ALLIANCE_EXIT_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_EXIT_REQ, msg)
end
function MSG_ALLIANCE_EXIT_RSP(msg)
    if Error_Code(msg) then
        --退出成功
        alliance_data.ExitAlliance()
    end
end

function Error_Code(msg)
    if not msg.errorCode then
        log.Error("#### errorCode==nil ####")
        return false;
    end
    if msg.errorCode ~= 0 then
        -- 搜索失败
        flow_text.Add(lang.Get(100000 + msg.errorCode))
        return false
    end
    return true
end

--region 联盟徽章
--请求微服协议
function TransmitLuaFuncReq(msgID, msg, type, id)
    ------print("请求执行一个服务器Lua函数", msgID,msg)
    local lua_pb = require "lua_pb"
    local transmitLuaFuncReq = lua_pb.TMSG_LUA_TRANSMIT_MICRO_REQ()
    transmitLuaFuncReq.msgID = msgID
    transmitLuaFuncReq.msg = msg and msg:SerializeToString() or ""
    transmitLuaFuncReq.spType = type or lua_pb.MicroService_LeagueDominator
    if id then
        transmitLuaFuncReq.spID = id
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_LUA_TRANSMIT_MICRO_REQ, transmitLuaFuncReq)
end
--联盟徽章基本信息请求
function MSG_ALLIANCE_MEDAL_BASE_REQ(id)
    local msg = alliance_pb.TMSG_ALLIANCE_MEDAL_BASE_REQ()
    --msg.id = id--联盟id
    --net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MEDAL_BASE_REQ, msg)
    TransmitLuaFuncReq(xManMsg_pb.MSG_ALLIANCE_MEDAL_BASE_REQ, msg)
    
    --print("联盟徽章基本信息请求",id)
end
--联盟徽章基本信息响应
function MSG_ALLIANCE_MEDAL_BASE_RSP(msg)
    if msg and msg.id and msg.id > 0 then
        --print("联盟徽章基本信息响应",msg.id,"msg.arrMedal",msg.arrMedal and #msg.arrMedal)
        alliance_medal_data.SetArrMedalList(msg.id, msg.arrMedal)
    end
end
--联盟徽章基本信息更新
function MSG_ALLIANCE_MEDAL_BASE_NTF(msg)
    --print("联盟徽章基本信息更新",msg.id,"msg.arrMedal",msg.arrMedal and #msg.arrMedal)
    alliance_medal_data.SetArrMedalList(msg.id, msg.arrMedal)
end

--联盟徽章详情请求
function MSG_ALLIANCE_MEDAL_DETAIL_REQ(id)
    local msg = alliance_pb.TMSG_ALLIANCE_MEDAL_DETAIL_REQ()
    msg.id = id--徽章id
    --print("联盟徽章详情请求",id)
    --net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MEDAL_DETAIL_REQ, msg)
    TransmitLuaFuncReq(xManMsg_pb.MSG_ALLIANCE_MEDAL_DETAIL_REQ, msg)
end

--联盟徽章详情响应
function MSG_ALLIANCE_MEDAL_DETAIL_RSP(msg)
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
    else
        --print("联盟徽章排行榜响应",msg.id,#msg.arrRank,msg.auto,#msg.arrProgress)
        alliance_medal_data.SetMedalRank(msg.id, msg.arrRank, msg.auto, msg.arrProgress)--徽章id;排行榜;是否系统自动提醒;每个级别的进度
    end
    event.Trigger(event.UPDATE_LEAGUE_MEDAL_RANK)
end

--联盟徽章领奖请求
function MSG_ALLIANCE_MEDAL_REWARD_REQ(id, lv)
    local msg = alliance_pb.TMSG_ALLIANCE_MEDAL_REWARD_REQ()
    msg.id = id--徽章id
    msg.lv = lv--哪个等级对应的宝箱 1~6
    --print("联盟徽章领奖请求",id,lv)
    --net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MEDAL_REWARD_REQ, msg)
    TransmitLuaFuncReq(xManMsg_pb.MSG_ALLIANCE_MEDAL_REWARD_REQ, msg)
end

--联盟徽章领奖响应
function MSG_ALLIANCE_MEDAL_REWARD_RSP(msg)
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
    else
        event.Trigger(event.UPDATE_LEAGUE_MEDAL_REWARD, msg.id, msg.lv)--徽章id;哪个等级对应的宝箱 1~6
    end
end

--联盟徽章删除提醒请求
function MSG_ALLIANCE_MEDAL_WARNDEL_REQ(id)
    local msg = alliance_pb.TMSG_ALLIANCE_MEDAL_WARNDEL_REQ()
    msg.id = id--徽章id
    --print("联盟徽章删除提醒请求",id)
    --net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MEDAL_WARNDEL_REQ, msg)
    TransmitLuaFuncReq(xManMsg_pb.MSG_ALLIANCE_MEDAL_WARNDEL_REQ, msg)
end

--联盟徽章删除提醒响应
function MSG_ALLIANCE_MEDAL_WARNDEL_RSP(msg)
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
    else
        --print("联盟徽章删除提醒响应",id)
    end
end

--联盟徽章设置/取消自动提醒请求
function MSG_ALLIANCE_MEDAL_WARNAUTO_REQ(id, auto)
    local msg = alliance_pb.TMSG_ALLIANCE_MEDAL_WARNAUTO_REQ()
    msg.id = id--徽章id
    msg.auto = auto--是否自动提醒
    --print("联盟徽章设置/取消自动提醒请求",id,auto)
    --net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MEDAL_WARNAUTO_REQ, msg)
    TransmitLuaFuncReq(xManMsg_pb.MSG_ALLIANCE_MEDAL_WARNAUTO_REQ, msg)
end

--联盟徽章设置/取消自动提醒响应
function MSG_ALLIANCE_MEDAL_WARNAUTO_RSP(msg)
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
    else
        --print("联盟徽章设置/取消自动提醒响应",msg.id,msg.auto)
        alliance_medal_data.SetMedalAuto(msg.id, msg.auto)
        event.Trigger(event.UPDATE_LEAGUE_MEDAL_WARN_AUTO, msg.id, msg.auto)--徽章id;是否自动提醒
    end
end

--联盟徽章一键提醒请求
function MSG_ALLIANCE_MEDAL_WARN_REQ(id, auto)
    local msg = alliance_pb.TMSG_ALLIANCE_MEDAL_WARN_REQ()
    msg.id = id--徽章id
    --print("联盟徽章一键提醒请求",id)
    --net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MEDAL_WARN_REQ, msg)
    TransmitLuaFuncReq(xManMsg_pb.MSG_ALLIANCE_MEDAL_WARN_REQ, msg)
end

--联盟徽章一键提醒响应
function MSG_ALLIANCE_MEDAL_WARN_RSP(msg)
    if msg.errorcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorcode))
    else
        flow_text.Add(lang.Get(190006))--提醒成功
        --print("联盟徽章一键提醒响应",msg.id)
        --event.Trigger(event.UPDATE_LEAGUE_MEDAL_WARN,msg.id)--徽章id
    end
end
--endregion

--region 互助
-- 个人互助数据-请求; 时机: 登录, 跨天; To微服
function MSG_ALLIANCE_HELP_SELF_REQ(data)
    local msg = alliance_pb.TMSG_ALLIANCE_HELP_SELF_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_HELP_SELF_REQ, msg)
end

-- 个人互助数据-响应
function MSG_ALLIANCE_HELP_SELF_RSP(msg)
    alliance_mutualAid_data.RefreshHelpSelfRsp(msg)
end

-- 互助列表信息-请求; 时机: 登录且已加入联盟, 新加入联盟; To微服
function MSG_ALLIANCE_HELP_LIST_REQ()
    local msg = alliance_pb.TMSG_ALLIANCE_HELP_LIST_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_HELP_LIST_REQ, msg)
end

-- 互助列表信息-响应; 断线重连时,服务器推送;
function MSG_ALLIANCE_HELP_LIST_RSP(msg)
    alliance_mutualAid_data.InitHelpInfoList(msg)
end

-- 互助列表信息-推送
function MSG_ALLIANCE_HELP_LIST_NTF(msg)
    alliance_mutualAid_data.TMSG_ALLIANCE_HELP_LIST_NTF(msg)
end

-- 发起互助-请求; To场景服
function MSG_ALLIANCE_HELP_START_REQ(data)
    local msg = alliance_pb.TMSG_ALLIANCE_HELP_START_REQ()
    msg.strKey = data
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_HELP_START_REQ, msg)
end

-- 发起互助-响应
function MSG_ALLIANCE_HELP_START_RSP(msg)
    if Error_Code(msg) then
        alliance_mutualAid_data.MSG_ALLIANCE_HELP_START_RSP(msg)
    end
    event.Trigger(event_alliance_define.NET_TO_ALLIANCE, "MSG_ALLIANCE_HELP_START_RSP", msg)
end

-- 一键帮助-请求; To微服
function MSG_ALLIANCE_HELP_CLICK_REQ()
    local msg = alliance_pb.TMSG_ALLIANCE_HELP_CLICK_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_HELP_CLICK_REQ, msg)
end

-- 一键帮助-响应
function MSG_ALLIANCE_HELP_CLICK_RSP(msg)
    alliance_mutualAid_data.MSG_ALLIANCE_HELP_CLICK_RSP(msg)
end

-- 一键帮助-推送; 每个客户端只会收到玩家自己发起的列表的推送;
function MSG_ALLIANCE_HELP_CLICK_NTF(msg)
    alliance_mutualAid_data.MSG_ALLIANCE_HELP_CLICK_NTF(msg)
end

--endregion
function MSG_ALLIANCE_CHECKCONTENT_REQ(checkType, checkContent)
    local msg = alliance_pb.TMSG_ALLIANCE_CHECKCONTENT_REQ()
    msg.checkType = checkType
    msg.checkContent = checkContent
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_CHECKCONTENT_REQ, msg)
end

function MSG_ALLIANCE_CHECKCONTENT_RSP(msg)
    if Error_Code(msg) then
        alliance_data.CheckNotifyOrMailData(msg)
    end
end

--转让盟主
function MSG_ALLIANCE_CHANGECEO_REQ(roleId)
    local msg = alliance_pb.TMSG_ALLIANCE_CHANGECEO_REQ()
    msg.roleId = roleId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_CHANGECEO_REQ, msg)
end

function MSG_ALLIANCE_CHANGECEO_RSP(msg)
    if Error_Code(msg) then
        --转让盟主成功
        flow_text.Add(lang.Get(600565))
        alliance_user_data.AllianceChangeR5(msg)
    end
end

function MSG_ALLIANCE_CHANGE_NTF(msg)
    event.Trigger(event_alliance_define.NET_ALLIANCE_CHANGE_NTF, msg)
end

--region 联盟二期 网络消息

--联盟成就信息请求
function MSG_ALLIANCE_ACHIEVEMENT_REQ(data)
    -- dbid
    local msg = alliance_pb.TMSG_ALLIANCE_ACHIEVEMENT_REQ()
    msg.dbid = data.dbid

    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_ACHIEVEMENT_REQ, msg)
end

-- 联盟成就信息响应（只推错误码，具体信息NTF）
function MSG_ALLIANCE_ACHIEVEMENT_RSP(msg)
    -- errorCode
    if msg.errorCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errorCode)
    else
        event.Trigger(event_alliance_define.MSG_ALLIANCE_ACHIEVEMENT_RSP, msg)
    end

end

-- 联盟成就推送
function MSG_ALLIANCE_ACHIEVEMENT_NTF(msg)
    -- dbid
    -- achInfo
    event.Trigger(event_alliance_define.MSG_ALLIANCE_ACHIEVEMENT_NTF, msg)
end

-- 联盟成就奖励领取请求
function MSG_ALLIANCE_ACHIEVEMENT_REWARD_REQ(data)
    -- achID
    local msg = alliance_pb.TMSG_ALLIANCE_ACHIEVEMENT_REWARD_REQ()
    msg.achID = data.achID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_ACHIEVEMENT_REWARD_REQ, msg)
end

-- 联盟成就奖励领取回复
function MSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP(msg)
    if msg.errorCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errorCode)
    else
        -- errorCode
        -- achID
        -- rewardId
        event.Trigger(event_alliance_define.MSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP, msg)
    end


end

-- 联盟邀请请求
function MSG_ALLIANCE_INVITE_REQ(data)
    -- dbid
    local msg = alliance_pb.TMSG_ALLIANCE_INVITE_REQ()
    msg.dbid = data.dbid
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_INVITE_REQ, msg)
end

-- 联盟邀请回复
function MSG_ALLIANCE_INVITE_RSP(msg)
    -- errorCode
    if msg.errorCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errorCode)
    else
        --邀请成功
        flow_text.Add(lang.Get(600557))
        event.Trigger(event_alliance_define.MSG_ALLIANCE_INVITE_RSP, msg)
    end
end

-- 联盟邀请推送
function MSG_ALLIANCE_INVITE_NTF(msg)
    -- allianceInfo
    local alliance_invite_data = require "alliance_invite_data"
    alliance_invite_data.InitInviteData(msg.allianceInfo)
    event.Trigger(event_alliance_define.MSG_ALLIANCE_INVITE_NTF, msg)
end

-- 接受/拒绝邀请请求
function MSG_ALLIANCE_ISACCEPT_INVITE_REQ(data)
    -- isAccept
    -- allianceID
    local msg = alliance_pb.TMSG_ALLIANCE_ISACCEPT_INVITE_REQ()
    msg.isAccept = data.isAccept
    msg.allianceID = data.allianceID

    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_ISACCEPT_INVITE_REQ, msg)
end

-- 接受/拒绝邀请回复
function MSG_ALLIANCE_ISACCEPT_INVITE_RSP(msg)
    -- errorCode
    -- isAccept
    -- allianceID
    event.Trigger(event_alliance_define.MSG_ALLIANCE_IS_ACCEPT_INVITE_RSP, msg)
end

-- 邀请信息请求
function MSG_ALLIANCE_INVITE_INFO_REQ(data)
    local msg = alliance_pb.TMSG_ALLIANCE_INVITE_INFO_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_INVITE_INFO_REQ, msg)
end

-- 邀请信息回复  具体推送   TMSG_ALLIANCE_INVITE_NTF
function MSG_ALLIANCE_INVITE_INFO_RSP(msg)
    -- errorCode
    event.Trigger(event_alliance_define.MSG_ALLIANCE_INVITE_INFO_RSP,msg)
end

-- 自动研究设置请求
function MSG_ALLIANCE_AUTO_RESEARCH_REQ(data)
    -- opt
    local msg = alliance_pb.TMSG_ALLIANCE_AUTO_RESEARCH_REQ()
    msg.opt = data.opt
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_AUTO_RESEARCH_REQ, msg)
end

-- 自动研究设置回复
function MSG_ALLIANCE_AUTO_RESEARCH_RSP(msg)
    -- errorCode
    if msg.errorCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errorCode)
    else
        event.Trigger(event_alliance_define.MSG_ALLIANCE_AUTO_RESEARCH_RSP, msg)
    end
    -- opt

end


-- 离线破城补偿
function MSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF(msg)
    -- dbid
    -- moveCityTime
    -- enemyCnt
    -- byAttackCnt
    -- maxAttackInfo
    -- byMemberInfo
    -- byCeoInfo
    event.Trigger(event_alliance_define.MSG_OFFLINE_CITY_DESTROY_REWARD_NTF, msg)
end

-- 新玩家免费迁移城池推送
function MSG_NEWPALYER_FREEMOVE_NTF(msg)
    -- canMove
    event.Trigger(event_alliance_define.MSG_NEW_PLAYER_FREE_MOVE_NTF, msg)
end

-- 盟友免费迁城次数更新
function MSG_ALLIANCE_FREE_MOVE_CITY_NTF(msg)
    -- canMove
    event.Trigger(event_alliance_define.MSG_ALLIANCE_FREE_MOVE_CITY_NTF, msg)
end

--endregion

--region 联盟三期 网络消息
-- 首次加入联盟时间通知
function MSG_ALLIANCE_FIRST_JOINTIME_NTF(msg)
    alliance_data.SetAllianceFirstJoinTime(msg.firstJoinTime)
end
-- 联盟一键入盟请求
function MSG_ALLIANCE_ONE_CLICK_JOIN_REQ(data)
    local msg = alliance_pb.TMSG_ALLIANCE_ONE_CLICK_JOIN_REQ()
    msg.languageId = data.languageId
    msg.devLanguageId = data.devLanguageId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_ONE_CLICK_JOIN_REQ, msg)
end

-- 联盟一键入盟回复
function MSG_ALLIANCE_ONE_CLICK_JOIN_RSP(msg)
    if Error_Code(msg) then

        if msg.leaveAllianceTime then
            alliance_data.RefreshLeaveAllianceTime(msg.leaveAllianceTime)
        end
    else
        if msg.errorCode == 30113 then
            local ui_window_mgr = require "ui_window_mgr"
            local alliance_mgr_extend = require "alliance_mgr_extend"
            alliance_mgr_extend.OpenAllianceWindow(1)
        end
    end
end
--endregion

--请求设置联盟标记
function MSG_ALLIANCE_MARK_REQ(data)
    local msg = alliance_pb.TMSG_ALLIANCE_MARK_REQ()
    msg.operate = data.operate
    msg.info.x = data.info.pos.x
    msg.info.y = data.info.pos.y
    msg.info.sRemark = data.info.sRemark
    msg.info.bServerID = data.info.bServerID
    msg.info.nIndex = data.info.nIndex
    msg.info.nSymbol = data.info.nSymbol
    msg.info.name = data.info.name or ""
    msg.info.sid = data.info.sid or 0
    msg.info.entitylevel = data.info.entitylevel or 0
    msg.info.entityName = data.info.entityName or 0
    msg.info.quality = data.info.quality or 0
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MARK_REQ, msg)
end

--设置联盟标记回复
function MSG_ALLIANCE_MARK_RSP(msg)
    if Error_Code(msg) then
        -- Success Handling
        local gw_ed = require("gw_ed")
        gw_ed.mgr:Trigger(gw_ed.GW_SAND_NET_EVENT, "OnSandBoxMarkRsp", msg)
    end
end

--region 联盟记录
-- 联盟记录请求
function MSG_ALLIANCE_RECORD_REQ(data)
    -- recordId
    local msg = alliance_pb.TMSG_ALLIANCE_RECORD_REQ()
    msg.recordId = data.recordId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_RECORD_REQ, msg)
end

-- 联盟记录响应
function MSG_ALLIANCE_RECORD_RSP(msg)
    -- errorCode
    if Error_Code(msg) then
        -- records
        event.Trigger(event_alliance_define.MSG_ALLIANCE_RECORD_RSP, msg)
        --除以1000 因为是毫秒级的
    end
end

--endregion

--region 邀请集结
-- 邀请集结请求
function MSG_ALLIANCE_MASS_REQ(data)
    local msg = alliance_pb.TMSG_ALLIANCE_MASS_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_MASS_REQ, msg)
end

-- 邀请集结回复
function MSG_ALLIANCE_MASS_RSP(msg)
    -- errorCode
    -- x
    -- y

    if Error_Code(msg) then
        --邀请集结成功
        flow_text.Add(lang.Get(600583))
    end
    --event.Trigger(event_alliance_define.TMSG_ALLIANCE_MASS_RSP,msg)
end

-- 玩家收到邀请集结推送
function MSG_ALLIANCE_MASS_NTF(msg)
    -- x
    -- y
    -- freeMassCnt
    alliance_user_data.SetAllianceRoleFreeMassNum(msg.freeMassCnt)
    alliance_data.SetAllianceLastMassTime(msg.lastMassTime)
    
    local alliance_mgr_extend = require "alliance_mgr_extend"
    alliance_mgr_extend.ShowChatTopMethod()
    
    --集结气泡触发事件
    event.Trigger(event_alliance_define.MAIN_MASS_BUBBLE_REFRESH)
    
end
-- 邀请集结在线的推送
function MSG_ALLIANCE_MASS_CNT_NTF(msg)
    -- allianceId
    -- dbid
    -- freeMassCnt
    --lastMassTime
    alliance_user_data.SetAllianceRoleFreeMassNum(msg.freeMassCnt)
    --event.Trigger(event_alliance_define.TMSG_ALLIANCE_MASS_CNT_NTF,msg)
end

---@public 联盟里距离过远的人数推送
function MSG_ALLIANCE_SURPASS_RALLYPOINT_NUMS_NTF(msg)
    local alliance_mgr_extend = require "alliance_mgr_extend"
    alliance_mgr_extend.SetFarAwayMemberNum(msg.nums)
    event.Trigger(event_alliance_define.MSG_ALLIANCE_SURPASS_RALLY_POINT_NUMS_RSP,msg)

    local red_system = require "red_system"
    local red_const = require "red_const"
    red_system.TriggerRed(red_const.Enum.AllianceQuickMoveCity)
    red_system.TriggerRed(red_const.Enum.AllianceSendMass)
end

--endregion
--region 单人情报

-- 联盟单人情报请求
function MSG_ALLIANCE_INTELLIGENCE_REQ(data)
    local msg = alliance_pb.TMSG_ALLIANCE_INTELLIGENCE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_INTELLIGENCE_REQ, msg)
end

-- 联盟单人情报响应
function MSG_ALLIANCE_INTELLIGENCE_RSP(msg)
    -- errorCode
    -- intelligences
    if Error_Code(msg) then
        event.Trigger(event_alliance_define.MSG_ALLIANCE_INTELLIGENCE_RSP,msg)
    end
end

function MSG_ALLIANCE_INTELLIGENCE_NTF(msg)
    
    local alliance_gen_data = require "alliance_gen_data"
    alliance_gen_data.SetWarGenData(msg)

    local alliance_mgr_extend = require "alliance_mgr_extend"
    alliance_mgr_extend.RefreshIntelligenceTimer()
    
    -- intelligences
end
--endregion

--region 联盟登录弹窗
function MSG_ALLIANCE_NEEDPOPUP_NTF(msg)
    local function_open_mgr = require("function_open_mgr")
    local isUnlock = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.Alliance)
    local userAllianceData = alliance_data.GetUserAllianceData()
    if not userAllianceData or userAllianceData.allianceId == nil then
        if isUnlock then
            local joinTime = alliance_data.GetAllianceFirstJoinTime()
            --打开联盟
            if not userAllianceData or userAllianceData.allianceId == nil then
                local ui_window_mgr = require "ui_window_mgr"
                ui_window_mgr:ShowModule("ui_alliance_first_join", nil, nil, { joinTime = joinTime })
                
            end
        end
    end
end
--endregion

--region 联盟分享
function MSG_ALLIANCE_SHARE_REQ(chat)
    -- chat TMSG_CHAT_SPEAK_REQ
    local msg = alliance_pb.TMSG_ALLIANCE_SHARE_REQ()
    msg.chat = chat and chat:SerializeToString() or ""
    local alliance_mgr = require "alliance_mgr"
    -- TransmitLuaFuncReq(xManMsg_pb.MSG_ALLIANCE_SHARE_REQ, msg, nil, alliance_mgr.GetUserAllianceId())
    local lua_pb = require "lua_pb"
    net.TransmitLuaFuncReqNew(xManMsg_pb.MSG_ALLIANCE_SHARE_REQ, msg, lua_pb.MicroService_LeagueDominator, nil, nil, alliance_mgr.GetUserAllianceId())
end
function MSG_ALLIANCE_SHARE_RSP(msg)
    -- errorCode
    Error_Code(msg)
end
--endregion

--region 联盟邀请函-奖励推送
function MSG_ALLIANCE_INVITATION_REWARD_NTF(msg)
    -- rewardId
    if msg.rewardId then        
        local reward_mgr = require "reward_mgr"
        local rewardData = reward_mgr.GetRewardGoodsMergers(msg.rewardId)
        local showData = {{dataList = rewardData}}
        local ui_reward_result = require "ui_reward_result_new"
        ui_reward_result.SetInputParam(showData)			--设置数据
        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:ShowModule("ui_reward_result_new")
    end
end
--endregion
--region 联盟邀请函-数据请求
function MSG_ALLIANCE_INVITATION_REQ()
    local msg = alliance_pb.TMSG_ALLIANCE_INVITATION_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_INVITATION_REQ, msg)
end
function MSG_ALLIANCE_INVITATION_RSP(msg)
	-- 无异常时推送TMSG_ALLIANCE_UPDATE_NTF邀请函数据
    Error_Code(msg)
end
--endregion

--region 联盟分享
function MSG_ALLIANCE_SHARE_RSP(msg)
    -- errorCode
    Error_Code(msg)
end

-- 联盟灭火请求
function MSG_ALLIANCE_OUTFIRE_REQ(data)
    -- fireRoleId
    local msg = alliance_pb.TMSG_ALLIANCE_OUTFIRE_REQ()
    msg.fireRoleId = data
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_OUTFIRE_REQ, msg)
end

-- 联盟灭火回复
function MSG_ALLIANCE_OUTFIRE_RSP(msg)
    -- errorCode
    Error_Code(msg)
end

-- 联盟灭火推送
function MSG_ALLIANCE_OUTFIRE_NTF(msg)
    -- fireRoleId
    -- outFireRoleId
    -- allianceId
    -- FireCnt
    event.Trigger(event_alliance_define.TMSG_ALLIANCE_OUTFIRE_NTF,msg)
    if msg.fireRoleId and msg.fireRoleId ~= 0 then 
        local data = {
            TargetRoleID = msg.fireRoleId
        }
        event.Trigger(event.GAME_EVENT_REPORT, "Outfire_Assist", data)
    end
    if msg.FireCnt then
        local player_mgr = require "player_mgr"
        player_mgr.SetFireCnt(msg.FireCnt)
    end
end

---@public function 联盟日志请求
function MSG_ALLIANCELOG_RECORD_REQ(data)
    -- allianceId
    --logType
    local msg = alliance_pb.TMSG_ALLIANCELOG_RECORD_REQ()
    msg.logType = data.logType
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCELOG_RECORD_REQ, msg)
end

---@public function 联盟日志回复
function MSG_ALLIANCELOG_RECORD_RSP(msg)
    --errorCode     错误码
    --logType   日志类型
    --arrBaseInfo 日志列表
    if Error_Code(msg) then
        --触发个事件
        event.Trigger(event_alliance_define.MSG_ALLIANCE_RECORD_OTHER_TYPE_RSP, msg)
        --更新数据
    end
end

local MessageTable = {
    { xManMsg_pb.MSG_ALLIANCE_RECOMMEND_RSP, MSG_ALLIANCE_RECOMMEND_RSP, alliance_pb.TMSG_ALLIANCE_RECOMMEND_RSP },
    { xManMsg_pb.MSG_ALLIANCE_SEARCH_RSP, MSG_ALLIANCE_SEARCH_RSP, alliance_pb.TMSG_ALLIANCE_SEARCH_RSP },
    { xManMsg_pb.MSG_ALLIANCE_INFO_RSP, MSG_ALLIANCE_INFO_RSP, alliance_pb.TMSG_ALLIANCE_INFO_RSP },
    { xManMsg_pb.MSG_ALLIANCE_ROLE_INFO_RSP, MSG_ALLIANCE_ROLE_INFO_RSP, alliance_pb.TMSG_ALLIANCE_ROLE_INFO_RSP },
    { xManMsg_pb.MSG_ALLIANCE_QUICK_ADD_RSP, MSG_ALLIANCE_QUICK_ADD_RSP, alliance_pb.TMSG_ALLIANCE_QUICK_ADD_RSP },
    { xManMsg_pb.MSG_ALLIANCE_APPLY_RSP, MSG_ALLIANCE_APPLY_RSP, alliance_pb.TMSG_ALLIANCE_APPLY_RSP },
    { xManMsg_pb.MSG_ALLIANCE_CHECKNAME_RSP, MSG_ALLIANCE_CHECKNAME_RSP, alliance_pb.TMSG_ALLIANCE_CHECKNAME_RSP },
    { xManMsg_pb.MSG_ALLIANCE_RANDOM_NAME_RSP, MSG_ALLIANCE_RANDOM_NAME_RSP, alliance_pb.TMSG_ALLIANCE_RANDOM_NAME_RSP },
    { xManMsg_pb.MSG_ALLIANCE_CREATE_RSP, MSG_ALLIANCE_CREATE_RSP, alliance_pb.TMSG_ALLIANCE_CREATE_RSP },
    { xManMsg_pb.MSG_ALLIANCE_AUTHORITY_RSP, MSG_ALLIANCE_AUTHORITY_RSP, alliance_pb.TMSG_ALLIANCE_AUTHORITY_RSP },
    { xManMsg_pb.MSG_ALLIANCE_EXPEL_RSP, MSG_ALLIANCE_EXPEL_RSP, alliance_pb.TMSG_ALLIANCE_EXPEL_RSP },
    { xManMsg_pb.MSG_ALLIANCE_GIFT_GET_RSP, MSG_ALLIANCE_GIFT_GET_RSP, alliance_pb.TMSG_ALLIANCE_GIFT_GET_RSP },
    { xManMsg_pb.MSG_ALLIANCE_GIFT_NTF, MSG_ALLIANCE_GIFT_NTF, alliance_pb.TMSG_ALLIANCE_GIFT_NTF },
    { xManMsg_pb.MSG_ALLIANCE_ANONYMOUS_RSP, MSG_ALLIANCE_ANONYMOUS_RSP, alliance_pb.TMSG_ALLIANCE_ANONYMOUS_RSP },
    { xManMsg_pb.MSG_ALLIANCE_TECHNOLOGY_RSP, MSG_ALLIANCE_TECHNOLOGY_RSP, alliance_pb.TMSG_ALLIANCE_TECHNOLOGY_RSP },
    { xManMsg_pb.MSG_ALLIANCE_TECHNOLOGY_DETAILS_RSP, MSG_ALLIANCE_TECHNOLOGY_DETAILS_RSP, alliance_pb.TMSG_ALLIANCE_TECHNOLOGY_DETAILS_RSP },
    { xManMsg_pb.MSG_ALLIANCE_TECHNOLOGY_RECOMMEND_RSP, MSG_ALLIANCE_TECHNOLOGY_RECOMMEND_RSP, alliance_pb.TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_RSP },
    { xManMsg_pb.MSG_ALLIANCE_TECHNOLOGY_STUDY_RSP, MSG_ALLIANCE_TECHNOLOGY_STUDY_RSP, alliance_pb.TMSG_ALLIANCE_TECHNOLOGY_STUDY_RSP },
    { xManMsg_pb.MSG_ALLIANCE_TECHNOLOGY_DONATE_RSP, MSG_ALLIANCE_TECHNOLOGY_DONATE_RSP, alliance_pb.TMSG_ALLIANCE_TECHNOLOGY_DONATE_RSP },
    { xManMsg_pb.MSG_ALLIANCE_MODIFY_ANNOUNCEMENT_RSP, MSG_ALLIANCE_MODIFY_ANNOUNCEMENT_RSP, alliance_pb.TMSG_ALLIANCE_MODIFY_ANNOUNCEMENT_RSP },
    { xManMsg_pb.MSG_ALLIANCE_APPLICATION_LIST_RSP, MSG_ALLIANCE_APPLICATION_LIST_RSP, alliance_pb.TMSG_ALLIANCE_APPLICATION_LIST_RSP },
    { xManMsg_pb.MSG_ALLIANCE_HANDLE_APPLICATION_RSP, MSG_ALLIANCE_HANDLE_APPLICATION_RSP, alliance_pb.TMSG_ALLIANCE_HANDLE_APPLICATION_RSP },
    { xManMsg_pb.MSG_ALLIANCE_MODIFY_FLAG_RSP, MSG_ALLIANCE_MODIFY_FLAG_RSP, alliance_pb.TMSG_ALLIANCE_MODIFY_FLAG_RSP },
    { xManMsg_pb.MSG_ALLIANCE_MODIFY_NAME_RSP, MSG_ALLIANCE_MODIFY_NAME_RSP, alliance_pb.TMSG_ALLIANCE_MODIFY_NAME_RSP },
    { xManMsg_pb.MSG_ALLIANCE_MODIFY_LANGUAGE_RSP, MSG_ALLIANCE_MODIFY_LANGUAGE_RSP, alliance_pb.TMSG_ALLIANCE_MODIFY_LANGUAGE_RSP },
    { xManMsg_pb.MSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP, MSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP, alliance_pb.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP },
    { xManMsg_pb.MSG_ALLIANCE_CLEAN_MEMBER_RSP, MSG_ALLIANCE_CLEAN_MEMBER_RSP, alliance_pb.TMSG_ALLIANCE_CLEAN_MEMBER_RSP },
    { xManMsg_pb.MSG_ALLIANCE_MAIL_SEND_RSP, MSG_ALLIANCE_MAIL_SEND_RSP, alliance_pb.TMSG_ALLIANCE_MAIL_SEND_RSP },
    { xManMsg_pb.MSG_ALLIANCE_UPDATE_NTF, MSG_ALLIANCE_UPDATE_NTF, alliance_pb.TMSG_ALLIANCE_UPDATE_NTF },
    { xManMsg_pb.MSG_ALLIANCE_POWERRANK_RSP, MSG_ALLIANCE_POWERRANK_RSP, alliance_pb.TMSG_ALLIANCE_POWERRANK_RSP },
    { xManMsg_pb.MSG_ALLIANCE_KILLNUM_RSP, MSG_ALLIANCE_KILLNUM_RSP, alliance_pb.TMSG_ALLIANCE_KILLNUM_RSP },
    { xManMsg_pb.MSG_ALLIANCE_ROLERANK_RSP, MSG_ALLIANCE_ROLERANK_RSP, alliance_pb.TMSG_ALLIANCE_ROLERANK_RSP },
    { xManMsg_pb.MSG_ALLIANCE_EXIT_RSP, MSG_ALLIANCE_EXIT_RSP, alliance_pb.TMSG_ALLIANCE_EXIT_RSP },

    --检测邮件
    { xManMsg_pb.MSG_ALLIANCE_CHECKCONTENT_RSP, MSG_ALLIANCE_CHECKCONTENT_RSP, alliance_pb.TMSG_ALLIANCE_CHECKCONTENT_RSP },
    --检测通知
    { xManMsg_pb.MSG_ALLIANCE_CHANGE_NTF, MSG_ALLIANCE_CHANGE_NTF, alliance_pb.TMSG_ALLIANCE_CHANGE_NTF },
    --修改盟主
    { xManMsg_pb.MSG_ALLIANCE_CHANGECEO_RSP, MSG_ALLIANCE_CHANGECEO_RSP, alliance_pb.TMSG_ALLIANCE_CHANGECEO_RSP },
    --互助
    { xManMsg_pb.MSG_ALLIANCE_HELP_SELF_RSP, MSG_ALLIANCE_HELP_SELF_RSP, alliance_pb.TMSG_ALLIANCE_HELP_SELF_RSP },
    { xManMsg_pb.MSG_ALLIANCE_HELP_LIST_RSP, MSG_ALLIANCE_HELP_LIST_RSP, alliance_pb.TMSG_ALLIANCE_HELP_LIST_RSP },
    { xManMsg_pb.MSG_ALLIANCE_HELP_LIST_NTF, MSG_ALLIANCE_HELP_LIST_NTF, alliance_pb.TMSG_ALLIANCE_HELP_LIST_NTF },
    { xManMsg_pb.MSG_ALLIANCE_HELP_START_RSP, MSG_ALLIANCE_HELP_START_RSP, alliance_pb.TMSG_ALLIANCE_HELP_START_RSP },
    { xManMsg_pb.MSG_ALLIANCE_HELP_CLICK_RSP, MSG_ALLIANCE_HELP_CLICK_RSP, alliance_pb.TMSG_ALLIANCE_HELP_CLICK_RSP },
    { xManMsg_pb.MSG_ALLIANCE_HELP_CLICK_NTF, MSG_ALLIANCE_HELP_CLICK_NTF, alliance_pb.TMSG_ALLIANCE_HELP_CLICK_NTF },
    --联盟徽章 新协议
    {xManMsg_pb.MSG_ALLIANCE_MEDAL_BASE_RSP, MSG_ALLIANCE_MEDAL_BASE_RSP, alliance_pb.TMSG_ALLIANCE_MEDAL_BASE_RSP},
    {xManMsg_pb.MSG_ALLIANCE_MEDAL_BASE_NTF, MSG_ALLIANCE_MEDAL_BASE_NTF, alliance_pb.TMSG_ALLIANCE_MEDAL_BASE_NTF},
    {xManMsg_pb.MSG_ALLIANCE_MEDAL_DETAIL_RSP, MSG_ALLIANCE_MEDAL_DETAIL_RSP, alliance_pb.TMSG_ALLIANCE_MEDAL_DETAIL_RSP},
    {xManMsg_pb.MSG_ALLIANCE_MEDAL_REWARD_RSP, MSG_ALLIANCE_MEDAL_REWARD_RSP, alliance_pb.TMSG_ALLIANCE_MEDAL_REWARD_RSP},
    {xManMsg_pb.MSG_ALLIANCE_MEDAL_WARNDEL_RSP, MSG_ALLIANCE_MEDAL_WARNDEL_RSP, alliance_pb.TMSG_ALLIANCE_MEDAL_WARNDEL_RSP},
    {xManMsg_pb.MSG_ALLIANCE_MEDAL_WARNAUTO_RSP, MSG_ALLIANCE_MEDAL_WARNAUTO_RSP, alliance_pb.TMSG_ALLIANCE_MEDAL_WARNAUTO_RSP},
    {xManMsg_pb.MSG_ALLIANCE_MEDAL_WARN_RSP, MSG_ALLIANCE_MEDAL_WARN_RSP, alliance_pb.TMSG_ALLIANCE_MEDAL_WARN_RSP},

    --region 联盟二期
    { xManMsg_pb.MSG_ALLIANCE_ACHIEVEMENT_RSP, MSG_ALLIANCE_ACHIEVEMENT_RSP, alliance_pb.TMSG_ALLIANCE_ACHIEVEMENT_RSP },
    { xManMsg_pb.MSG_ALLIANCE_ACHIEVEMENT_NTF, MSG_ALLIANCE_ACHIEVEMENT_NTF, alliance_pb.TMSG_ALLIANCE_ACHIEVEMENT_NTF },
    { xManMsg_pb.MSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP, MSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP, alliance_pb.TMSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP },
    { xManMsg_pb.MSG_ALLIANCE_INVITE_RSP, MSG_ALLIANCE_INVITE_RSP, alliance_pb.TMSG_ALLIANCE_INVITE_RSP },
    { xManMsg_pb.MSG_ALLIANCE_INVITE_NTF, MSG_ALLIANCE_INVITE_NTF, alliance_pb.TMSG_ALLIANCE_INVITE_NTF },
    { xManMsg_pb.MSG_ALLIANCE_AUTO_RESEARCH_RSP, MSG_ALLIANCE_AUTO_RESEARCH_RSP, alliance_pb.TMSG_ALLIANCE_AUTO_RESEARCH_RSP },
    { xManMsg_pb.MSG_NEWPALYER_FREEMOVE_NTF, MSG_NEWPALYER_FREEMOVE_NTF, alliance_pb.TMSG_NEWPALYER_FREEMOVE_NTF },
    { xManMsg_pb.MSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF, MSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF, alliance_pb.TMSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF },
    { xManMsg_pb.MSG_ALLIANCE_ISACCEPT_INVITE_RSP, MSG_ALLIANCE_ISACCEPT_INVITE_RSP, alliance_pb.TMSG_ALLIANCE_ISACCEPT_INVITE_RSP },
    { xManMsg_pb.MSG_ALLIANCE_FREE_MOVE_CITY_NTF, MSG_ALLIANCE_FREE_MOVE_CITY_NTF, alliance_pb.TMSG_ALLIANCE_FREE_MOVE_CITY_NTF },
    {xManMsg_pb.MSG_ALLIANCE_INVITE_INFO_RSP, MSG_ALLIANCE_INVITE_INFO_RSP, alliance_pb.TMSG_ALLIANCE_INVITE_INFO_RSP},

    {xManMsg_pb.MSG_ALLIANCE_MASS_RSP, MSG_ALLIANCE_MASS_RSP, alliance_pb.TMSG_ALLIANCE_MASS_RSP},
    {xManMsg_pb.MSG_ALLIANCE_SURPASS_RALLYPOINT_NUMS_NTF, MSG_ALLIANCE_SURPASS_RALLYPOINT_NUMS_NTF, alliance_pb.TMSG_ALLIANCE_SURPASS_RALLYPOINT_NUMS_NTF},
    {xManMsg_pb.MSG_ALLIANCE_MASS_NTF, MSG_ALLIANCE_MASS_NTF, alliance_pb.TMSG_ALLIANCE_MASS_NTF},
    {xManMsg_pb.MSG_ALLIANCE_MASS_CNT_NTF, MSG_ALLIANCE_MASS_CNT_NTF, alliance_pb.TMSG_ALLIANCE_MASS_CNT_NTF},

    {xManMsg_pb.MSG_ALLIANCE_INTELLIGENCE_RSP, MSG_ALLIANCE_INTELLIGENCE_RSP, alliance_pb.TMSG_ALLIANCE_INTELLIGENCE_RSP},
    {xManMsg_pb.MSG_ALLIANCE_INTELLIGENCE_NTF, MSG_ALLIANCE_INTELLIGENCE_NTF, alliance_pb.TMSG_ALLIANCE_INTELLIGENCE_NTF},
    --endregion

    {xManMsg_pb.MSG_ALLIANCE_FIRST_JOINTIME_NTF, MSG_ALLIANCE_FIRST_JOINTIME_NTF, alliance_pb.TMSG_ALLIANCE_FIRST_JOINTIME_NTF},
    {xManMsg_pb.MSG_ALLIANCE_ONE_CLICK_JOIN_RSP, MSG_ALLIANCE_ONE_CLICK_JOIN_RSP, alliance_pb.TMSG_ALLIANCE_ONE_CLICK_JOIN_RSP},
    --联盟标记
    {xManMsg_pb.MSG_ALLIANCE_MARK_RSP, MSG_ALLIANCE_MARK_RSP, alliance_pb.TMSG_ALLIANCE_MARK_RSP},
    {xManMsg_pb.MSG_ALLIANCE_RECORD_RSP, MSG_ALLIANCE_RECORD_RSP, alliance_pb.TMSG_ALLIANCE_RECORD_RSP},
    
    --一键入盟弹窗临时协议
    {xManMsg_pb.MSG_ALLIANCE_NEEDPOPUP_NTF, MSG_ALLIANCE_NEEDPOPUP_NTF, alliance_pb.TMSG_ALLIANCE_NEEDPOPUP_NTF},
    -- 联盟分享 
    {xManMsg_pb.MSG_ALLIANCE_SHARE_RSP, MSG_ALLIANCE_SHARE_RSP, alliance_pb.TMSG_ALLIANCE_SHARE_RSP},
    -- 联盟邀请函-奖励推送
    {xManMsg_pb.MSG_ALLIANCE_INVITATION_REWARD_NTF, MSG_ALLIANCE_INVITATION_REWARD_NTF, alliance_pb.TMSG_ALLIANCE_INVITATION_REWARD_NTF},
    -- 联盟邀请函-数据回复 无异常时推送TMSG_ALLIANCE_UPDATE_NTF邀请函数据
    {xManMsg_pb.MSG_ALLIANCE_INVITATION_RSP, MSG_ALLIANCE_INVITATION_RSP, alliance_pb.TMSG_ALLIANCE_INVITATION_RSP},

    {xManMsg_pb.MSG_ALLIANCE_OUTFIRE_RSP, MSG_ALLIANCE_OUTFIRE_RSP, alliance_pb.TMSG_ALLIANCE_OUTFIRE_RSP},
    {xManMsg_pb.MSG_ALLIANCE_OUTFIRE_NTF, MSG_ALLIANCE_OUTFIRE_NTF, alliance_pb.TMSG_ALLIANCE_OUTFIRE_NTF},
    {xManMsg_pb.MSG_ALLIANCELOG_RECORD_RSP, MSG_ALLIANCELOG_RECORD_RSP, alliance_pb.TMSG_ALLIANCELOG_RECORD_RSP},
}
net_route.RegisterMsgHandlers(MessageTable)