local os = os
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil
local time_util = require "time_util"
local face_item = require "face_item_new"

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_arena_main_not_start_binding"

--region View Life
module("ui_arena_main_not_start")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

local playerItemName = "scrItem_PlayerItem_"

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self.faceItemList = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.faceItemList then
        for k, v in pairs(self.faceItemList) do
            if v then
                v:Dispose()
            end
        end
        self.faceItemList = nil
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:InitBaseShow(data)
    if not data then
        return
    end
    self:SetActive(self.txt_arenaStartTime,true)
    self:CreateTimer(1, function()
        local curTime = data.startTime - os.server_time()
        if curTime <= 0 then
            self:SetActive(self.txt_arenaStartTime,false)
            return true
        end
        local time = time_util.FormatTimeXMan(curTime)
        self.txt_arenaStartTime.text = time
    end )
    self.txt_openServerID.text = data.serverIdsStr
    self.txt_curPowerRank.text = string.format2(lang.Get(1009112), data.curPowerRank)
    self.txt_AdvancedNeedPowerRank.text = data.advancedRankStr
    self.txt_MiddleNeedPowerRank.text = data.middleRankStr
end


--设置前三名玩家消息
function UIView:SetTopPlayerInfo(data, likingClick)
    if not data then
        return
    end
    for i = 1, 3 do
        local playerData = data[i]
        if playerData then
            self:SetSinglePlayerItem(i, playerData, likingClick)
        end
    end
end

function UIView:SetSinglePlayerItem(index, playerData, likingClick)
    if not playerData then
        return
    end
    local itemName = string.format("%s%d", playerItemName, index)
    local item = self[itemName]
    if not item then
        return
    end
    
    local faceTra = item:Get("faceTra")
    local unionShortName = item:Get("unionShortName")
    local playerName = item:Get("playerName")

    local faceItem = self.faceItemList[index] or face_item.CFaceItem()
    faceItem:Init(faceTra.transform, nil, index == 1 and 1.4 or 1.2)
    faceItem:SetNewBg(true)
    faceItem:SetFaceInfo( playerData.faceStr, function()
        local player_mgr = require "player_mgr"
        if playerData.roleID ~= player_mgr.GetPlayerRoleID() then
            local mgr_personalInfo = require "mgr_personalInfo"
            mgr_personalInfo.ShowRoleInfoView(playerData.roleID)
        end
    end)
    faceItem:SetActorLvText(true, playerData.roleLv)
    faceItem:FrameEffectEnable(true, self.curOrder+1)
    faceItem:SetFrameID(playerData.frameID, true)
    faceItem:SetEnableState(true,true)
    
    self.faceItemList[index] = faceItem
    unionShortName.text = string.format("%s %s", playerData.worldStr, util.SplicingUnionShortName(playerData.unionShortName))
    playerName.text = playerData.name
    
    --初始化点赞按钮事件
    local ClickLikeBtn = item:Get("ClickLikeBtn")
    UIUtil.SetActive(ClickLikeBtn,true)
    self:AddBtnOnClick(ClickLikeBtn,function()
        likingClick(playerData)
    end)
    
    --初始化点赞信息
    local iconNum = item:Get("iconNum")
    local icon = item:Get("icon")
    if playerData.itemInfo then
        self:CreateSubSprite("CreateSpriteAsset",icon, playerData.itemInfo.icon)
        if not util.IsObjNull(iconNum) then
            iconNum.text = string.format("+%s", playerData.itemInfo.num)
        end
    end
end

--每日奖励点击方法
function UIView:ShowDailyReward(data)
    if not data then
        return
    end
    data.uiParent = self.btn_showDailyReward.transform
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:ShowModule("ui_arena_daily_box", nil, nil, data) 
end

--TODO 点赞无封装只能复制
--region 点赞
--刷新单个点赞数量显示
function UIView:RefreshSingleLikingNum(index,data)
    local itemName = string.format("%s%d", playerItemName, index)
    local item = self[itemName]
    if not item then
        return
    end
    local LikeNum = item:Get("LikeNum")
    LikeNum.text = data.likeNum
end

--刷新单个点赞红点显示
function UIView:RefreshSingleLikingRedDot(index,isShow)
    local itemName = string.format("%s%d", playerItemName, index)
    local item = self[itemName]
    if not item then
        return
    end
    local RedDot = item:Get("RedDot")
    UIUtil.SetActive(RedDot, isShow)
end

--显示点赞动画
function UIView:ShowLikingAnimation(index)
    local itemName = string.format("%s%d", playerItemName, index)
    local item = self[itemName]
    if not item then
        return
    end
    local Ani = item:Get("Ani")
    Ani:Play("AreaLike")
end
--endregion

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
