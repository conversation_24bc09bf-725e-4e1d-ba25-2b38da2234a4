--- Created by fgy.
--- DateTime: 2024/5/31 10:15
--- Des:资源管理

local require = require
local table = table
local pairs = pairs
local newclass = newclass
local GWConst = GWConst
local xpcall = xpcall
local string = string

local card_sprite_asset = require "card_sprite_asset"
local util = require "util"
local base_game_object = require "base_game_object"
local gw_disposable_object = require "gw_disposable_object"
local game_scheme = require "game_scheme"
local gw_hero_mgr = require "gw_hero_mgr"
local GWG = require "gw_g"
local tonumber = tonumber
local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@module gw_asset_mgr 大世界地图 资源管理器
module("gw_asset_mgr")

---@class GWAssetMgr : gw_disposable_object 大世界地图 资源管理器
local GWAssetMgr = newclass("gw_asset_mgr", gw_disposable_object)

--- 节点默认位置
local _GAME_OBJECT_DEFAULT_POS = GWConst.Vec3Zero

--- 节点默认缩放
local _GAME_OBJECT_DEFAULT_SCALE = GWConst.Vec3One
--- 构造器
function GWAssetMgr:ctor()
    self.__base:ctor()
end

--- 初始化
function GWAssetMgr:Init()
    ---@type { Dispose: fun(self: any) }[]
    self.disposables = {}

    ---@type table<string, "asset_loader">
    self.assetLoaders = {}
    ---@type table<string, fun(asset: userdata)>
    self.loadCallbacks = {}
    ---@type table<string, userdata>
    self.prefabList = {}
    ---@type table<string, "gw_pool">
    self.poolList = {}
    self.DefaultPos = _GAME_OBJECT_DEFAULT_POS
    self.DefaultScale = _GAME_OBJECT_DEFAULT_SCALE
    self:InitSpriteAsset()
end

buffSpriteAsset = nil
faceSpriteAsset = nil
goodsSpriteAsset = nil
homeButtonAsset = nil
sandMinMapAsset = nil
sandMarkAsset = nil
buildingButtonAsset = nil
congressPositionIconAsset = nil
sandMapAsset = nil

function GWAssetMgr:InitSpriteAsset()
    --该资源为常驻资源，不会进行删除
    buffSpriteAsset = buffSpriteAsset or card_sprite_asset.CreateBuffAsset()
    faceSpriteAsset = faceSpriteAsset or card_sprite_asset.CreateHeroAsset()
    goodsSpriteAsset = goodsSpriteAsset or card_sprite_asset.CreateSpriteAsset()
    homeButtonAsset = homeButtonAsset or card_sprite_asset.CreateHomeButtonAsset()
    sandMinMapAsset = sandMinMapAsset or card_sprite_asset.CreateGWSandMinMap()
    sandMarkAsset = sandMarkAsset or card_sprite_asset.CreateGWSandMarkAsset()
    buildingButtonAsset = buildingButtonAsset or card_sprite_asset.CreateBuildingBtnAsset()
    congressPositionIconAsset = congressPositionIconAsset or card_sprite_asset.CreateCongressPositionIconAsset()
    sandMapAsset = sandMapAsset or card_sprite_asset.CreateGWSandmapAsset()
end

--- 弃用
function GWAssetMgr:Dispose()
    if self.assetLoaders then
        for i, v in pairs(self.assetLoaders) do
            v:Dispose()
        end
        self.assetLoaders = nil
    end
    self.loadCallbacks = nil
    if self.disposables then
        for i, v in pairs(self.disposables) do
            v:Dispose()
        end
        self.disposables = nil
    end

    self.__base:Dispose()
end

--- 回收
function GWAssetMgr:Recycle()
    self:Init()
    self.__base:Recycle()
end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -


--- 加载资源
---@param path string 资源AB路径
---@param callback fun(asset: userdata) 加载回调
function GWAssetMgr:Load(path, callback, parentTrans, tag)
    local baseGameObject = base_game_object(tag or "gw_sand_mgr")
    baseGameObject:LoadResource(path, "", function(obj)
        if callback then
            xpcall(function()
                callback(obj, baseGameObject)
            end, GWG.GWAdmin.SwitchUtility.OnLuaErr) --返回 gameObject对象 和 baseGameObject Entity组件（新增）
        end
    end, false, parentTrans)
    return baseGameObject
end

--- 加载资源城建专用
---@param path string 资源AB路径
---@param callback fun(asset: userdata) 加载回调
function GWAssetMgr:LoadBS(path, callback, parentTrans, tag)
    local baseGameObject = base_game_object(tag or "gw_sand_mgr")
    baseGameObject:LoadResource(path, "", function(obj)
        if callback then
            xpcall(function()
                callback(obj)
            end, GWG.GWAdmin.SwitchUtility.OnHomeLuaErr)
        end
    end, false, parentTrans)
    return baseGameObject
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 加载沙盘标记
---@param icon userdata(image or sprite)
---@param name string 资源名称
function GWAssetMgr:LoadSandMarkIcon(icon, name)
    sandMarkAsset:GetSprite(name, function(sprite)
        if not util.IsObjNull(icon) then
            icon.sprite = sprite
        end
    end)
end

--- 加载沙盘小地图
---@param icon userdata(image or sprite)
---@param name string 资源名称
function GWAssetMgr:LoadSandMinMapIcon(icon, name)
    sandMinMapAsset:GetSprite(name, function(sprite)
        if not util.IsObjNull(icon) then
            icon.sprite = sprite
        end
    end)
end

--- 加载沙盘小地图callback版本
---@param icon number
---@param callback fun(sprite: "Sprite")
function GWAssetMgr:LoadSandMinMapIconV2(icon, callback)
    sandMinMapAsset:GetSprite(icon, function(sprite)
        if callback then
            callback(sprite)
        end
    end)
end

--- 加载物品图标
---@param icon number
---@param callback fun(sprite: "Sprite")
function GWAssetMgr:LoadGoodsIcon(icon, callback)
    goodsSpriteAsset:GetSprite(icon, function(sprite)
        if callback then
            callback(sprite)
        end
    end)
end

--- 加载国会官职图标
---@param icon number
---@param callback fun(sprite: "Sprite")
function GWAssetMgr:LoadCongressPositionIcon(icon, callback)
    congressPositionIconAsset:GetSprite(icon, function(sprite)
        if callback then
            callback(sprite)
        end
    end)
end

--- 加载物品图标
---@param icon number
---@param callback fun(sprite: "Sprite")
function GWAssetMgr:LoadGWIcon(icon, id)
    --------------同上----------------
end

--[[通用更改头像图标]]
function GWAssetMgr:SetFaceIcon(icon, faceId)
    if not faceId then
        GWG.GWAdmin.SwitchUtility.Error("GWAssetMgr:SetFaceIcon faceId = nil")
        return
    end
    local faceDefault = game_scheme:InitBattleProp_0(8127).szParam.data[0]
    local faceCfg = nil
    if faceId > 0 then
        faceCfg = game_scheme:RoleFace_0(faceId)
    end
    if faceCfg == nil then
        faceCfg = game_scheme:RoleFace_0(faceDefault)
    end
    if faceCfg then
        faceSpriteAsset:GetSprite(faceCfg.rivalType, function(sprite)
            if sprite and not util.IsObjNull(icon) and not icon.Index and not icon.Version then
                --区分是否entity
                icon.sprite = sprite
            else
                EntityHybridUtility.SetSpriteRectParam(icon, sprite)
                EntityHybridUtility.SetSpritePivotScale(icon, sprite)
            end
        end)
    end
end

--更改通用设置头像图标
function GWAssetMgr:SetFaceStringIcon(transform, faceStr)
    local faceNumber = tonumber(faceStr)
    if faceNumber then
        --self:SetFaceIcon(transform,faceStr)
        self:SetFaceIcon(transform, faceNumber)
        return
    end
    local custom_avatar_mgr = require "custom_avatar_mgr"
    custom_avatar_mgr.SetAvatarIcon(transform, faceStr)
end

--[[通用设置头像框]]
local defaultFrameID
function GWAssetMgr:SetFrameIcon(icon, frameId)
    if not defaultFrameID then
        defaultFrameID = game_scheme:InitBattleProp_0(1228).szParam.data[0]
    end
    frameId = (frameId == 0 or frameId == "0") and defaultFrameID or frameId --无头像框的情况下显示默认头像框
    local frameCfg = game_scheme:RoleFrame_0(frameId)
    if frameCfg then
        faceSpriteAsset:GetSprite(frameCfg.rivalType, function(sprite)
            if sprite and not util.IsObjNull(icon) and not icon.Index and not icon.Version then
                --区分是否entity
                icon.sprite = sprite
            else
                EntityHybridUtility.SetSpriteRectParam(icon, sprite)
                EntityHybridUtility.SetSpritePivotScale(icon, sprite)
            end
        end)
    else
        if icon.gameObject.activeSelf == true then
            icon.gameObject:SetActive(false)
        end
    end
end

--[[Entity专用设置头像框，支持图集智能选择]]
function GWAssetMgr:SetFrameIconWithAtlasCheck(frameId, smartCallback)
    if not defaultFrameID then
        defaultFrameID = game_scheme:InitBattleProp_0(1228).szParam.data[0]
    end
    frameId = (frameId == 0 or frameId == "0") and defaultFrameID or frameId --无头像框的情况下显示默认头像框
    local frameCfg = game_scheme:RoleFrame_0(frameId)
    if frameCfg then
        faceSpriteAsset:GetSprite(frameCfg.rivalType, function(sprite)
            if sprite and smartCallback then
                -- 调用智能选择回调，传入sprite进行图集判断
                smartCallback(sprite)
            end
        end)
    else
        -- 如果没有配置，调用智能选择回调处理隐藏逻辑
        if smartCallback then
            smartCallback(nil)
        end
    end
end

function GWAssetMgr:SetBuffIcon(icon,buffID)
    buffSpriteAsset:GetSprite(buffID,function(sprite)
        if sprite and not util.IsObjNull(icon) then
            icon.sprite = sprite
        end
    end)
end

--[[通用设置英雄图标]]
function GWAssetMgr:SetHeroIcon(icon, heroID, starsLv)
    local currentFace = gw_hero_mgr.ChangeHeroIcon(heroID, starsLv)
    if currentFace then
        faceSpriteAsset:GetSprite(currentFace, function(sprite)
            if sprite and not util.IsObjNull(icon) then
                icon.sprite = sprite
            end
        end)
    end

end

--[[加载中立城市资源]]
function GWAssetMgr:SetNeutralCityIcon(icon, callback)
    if not string.IsNullOrEmpty(icon) then
        sandMapAsset:GetSprite(icon, function(sprite)
            if callback then
                callback(sprite)
            end
        end)
    end
end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -


--- 预加载
---@param model_path table<string, string> 模型资源
---@param eff_path table<string, string> 特效资源
function GWAssetMgr:Preload(listPath, callback)
    local assetMgr = self.asset_mgr
    for i, v in pairs(listPath) do
        -- 加载并缓存预制体
        assetMgr:Load(v, function(asset)
            self.prefabList[v] = asset
        end)
    end

end
--local iconPre ="SLGlinshi_HomeBtn_"
--- 设置沙盘主页按钮图标
function GWAssetMgr:SetGWHomeButtonIcon(icon, iconId)
    --local iconIdFinal = iconPre..iconId
    homeButtonAsset:GetSprite(iconId, function(sprite)
        if sprite and not util.IsObjNull(icon) then
            icon.sprite = sprite
        end
    end)
end

local iconPre = "SLGlinshi_HomeBtn_"

function GWAssetMgr:SetGWBuildingButtonIcon(icon, iconId)
    local iconIdFinal = iconId--iconPre..iconId
    buildingButtonAsset:GetSprite(iconIdFinal, function(sprite)
        if sprite and not util.IsObjNull(icon) then
            icon.sprite = sprite
        end
    end)
end

--return GWAssetMgr

local manager
if manager == nil then
    manager = GWAssetMgr.new()
end
return manager