local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local event_arena_common_define = require "event_arena_common_define"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local arena_common_mgr = require "arena_common_mgr"

--region Controller Life
module("ui_arena_main_common_controller")
local controller = nil
local UIController = newClass("ui_arena_main_common_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    self.isInit = false
    self.activityId = data.activityID
    if data.arenaType then
        self.arenaType = data.arenaType
    else
        self.arenaType = arena_common_mgr.GetArenaTypeByActivityId(self.activityId)
    end
    arena_common_mgr.ReqArenaRankData(nil, self.arenaType)
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    if self.enterRank then
        arena_common_mgr.SetPlayerOldRank(self.arenaType, self.enterRank)
    end
    self.arenaType = nil
    self.isInit = false
    self.enterRank = nil

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
    local OpenArenaRankPanel = function(_, msg)
        if self.isInit then
            return
        end
        self.isInit = true
        if msg.tSelfRankInfo then
            self.enterRank = msg.tSelfRankInfo.nRank
        end
        self.enterRank = nil
        local isOpen = arena_common_mgr.IsArenaOpen(self.arenaType)
        local canJoin = arena_common_mgr.IsCanJoinArena(self.arenaType)
        local data = {
            arenaType = self.arenaType,
            activityId = self.activityId,
        }
        if isOpen and canJoin then
            self:TriggerUIEvent("LoadArenaUI", data, "ui_arena_main_rank")
        else
            self:TriggerUIEvent("LoadArenaUI", data, "ui_arena_main_not_start")
        end
    end
    
    self:RegisterEvent(event_arena_common_define.UPDATE_ARENA_RANK,OpenArenaRankPanel)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic

function UIController:GetShowArenaType()
    
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
