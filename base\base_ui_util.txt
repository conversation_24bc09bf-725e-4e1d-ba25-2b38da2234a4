--- base_create.lua -------------------------------
--- author:  lzx
--- date: 2024/6/25 15.36
--- desc: 将UIUtil封装的接口绑定到ui_base上
--------------------------------------------------
local require = require
local newclass = newclass
local table = table
local unpack  = unpack
local string = string
local Component = require "base_comp"
local e_handler_mgr = require "e_handler_mgr"
local Common_Util = CS.Common_Util.UIUtil
local util = require "util"


module("base_ui_util")
local Subset = newclass("base_ui_util",Component,true)

function Subset:ctor()
    Subset.__base.ctor(self, "base_ui_util")    
end
--@function 设置transform的显隐
---@param   component table 传递的unity组件,只要继承Component都行 不限定是transform,目前GameObject也支持了
---@param   active boolean
---@param    path   string   对比root（component）的 相对路径 
---@param   compatibilityMode  boolean 是否递归设置
function Subset:SetActive(component, active, path,compatibilityMode)
    if compatibilityMode == nil then
        if path == nil then
            Common_Util.SetActive(component,active or false)
        else
            Common_Util.SetActive(component,active or false,path)
        end
    else
        Common_Util.SetActive(component,active or false,path,compatibilityMode or false)        
    end
end

function Subset:GetActive(component, path)
    return Common_Util.GetActive(component,path)
end

function Subset:GetComponent(tf,comp,path)
    return Common_Util.GetComponent(tf,comp,path)
end
---@public 设置组件的的localScale
function Subset:SetLocalScale(comp,x,y,z,path)
    return Common_Util.SetLocalScale(comp,x,y,z,path)
end

---@public 设置组件的的localPos
function Subset:SetLocalPos(comp,x,y,z,path)
    return Common_Util.SetLocalPos(comp,x,y,z,path)
end

---@public 设置组件的的localEuler
function Subset:SetLocalEuler(comp,x,y,z,path)
    return Common_Util.SetLocalEuler(comp,x,y,z,path)
end

--@function 获取某个组件，如果没有则添加
--@param componentType是Type类型 不是字符串
function Subset:GetOrAddComponent(tf, componentType,path )
    return Common_Util.GetOrAddComponent(tf,componentType,path)
end

function Subset:GetTrans(component,path)
    return Common_Util.GetTrans(component,path)
end

--@function find物体 --通过child的name
--@ param 注意 name是要查找的物体名
--@param includeInactive是否包括隐藏的物体
function Subset:FindTrans(component, name, includeInactive)
    return Common_Util.FindTrans(component,name,includeInactive or false)
end

function Subset:FindTransByEndName(component, name, includeInactive)
    return Common_Util.FindTransByEndName(component,name,includeInactive)
end
---@function 获取transform下面所有的transform
---@param tranName  目标tf的name
function Subset:GetAllTrans(root, tranName)
    return Common_Util.GetAllTrans(root,tranName)
end

---@function 设置Transform的默认状态，也就是默认位置，角度，缩放
---@param tf  传入的transform
function Subset:SetTransformDefault(tf)
    Common_Util.SetTransformDefault(tf)
end
function Subset:SetText(component,str,path)
    Common_Util.SetText(component,str,path)
end
function Subset:SetTextMeshPro(component,str,path)
    Common_Util.SetTextMeshPro(component,str,path)
end

function Subset:SetColor(component,color, path)
    Common_Util.SetColor(component,color,path)
end

function Subset:SetAlpha(component,alpha,path)
    Common_Util.SetAlpha(component,alpha,path)
end

---@public ScrollTable相关
---@des 特别注意 该刷新方法只会刷新对应数据index的Item,且该item正被展示，
---@param rect_table table 指定滑动列表的scrollRectTable脚本实例
---@param index number ScrollTable.data 传入的data的索引  1开始 --也就是当前滑动Item的index
---@param data table 对应下标index需要更新刷新的新数据  如果data为nil 则使用当前的（因为可能数据已经被更新了）
function Subset:RefreshScrollTableItem(rect_table,index,data)
    if  not index or util.IsObjNull(rect_table) then
        return false
    end   
    if data then
        if rect_table.data[index] then
            rect_table.data[index] = data
        end
    end
    rect_table:Refresh(index-1,index-1)
    return true
end

----region Unity事件的监听
function Subset:AddOnClickListener(entity,component,proxy_name,...)
    if not component then
        return
    end
    if not proxy_name then
        proxy_name = string.format("On{0}Click",component.name)
    end
    
    local t = {...}  --当前这里直接在TriggerHandler函数中使用（...）会造成require失败，只有先包装，再解包才会没问题
    component.onClick:RemoveAllListeners()
    component.onClick:AddListener(
            function()               
                e_handler_mgr.TriggerHandler(entity.controller_name,proxy_name,unpack(t))
            end
    )
end
function Subset:AddOnValueChangedListener(entity,component,proxy_name,...)
    if not proxy_name then
        proxy_name = string.format("On{0}ValueChanged",component.name)
    end
    local t = {...}
    component.onValueChanged:RemoveAllListeners()
    component.onValueChanged:AddListener(
            function()
                e_handler_mgr.TriggerHandler(entity._NAME, proxy_name,unpack(t))
            end
    )
end

function Subset:AddOnEndEditListener(entity,component,proxy_name,...)
    if not proxy_name then
        proxy_name = string.format("On{0}EndEdit",component.name)
    end
    local t = {...}
    component.onEndEdit:RemoveAllListeners()
    component.onEndEdit:AddListener(
            function()
                e_handler_mgr.TriggerHandler(entity._NAME, proxy_name,unpack(t))
            end
    )
end
--endregion

function Subset:exportMethods()
    self:exportMethods_({
        "SetActive",
        "GetActive",
        "GetComponent",
        "SetLocalScale",
        "SetLocalPos",
        "SetLocalEuler",
        "GetOrAddComponent",
        "GetTrans",
        "FindTrans",
        "FindTransByEndName",
        "GetAllTrans",
        "SetTransformDefault",
        "SetText",
        "SetTextMeshPro",
        "SetColor",
        "SetAlpha",
        "RefreshScrollTableItem",
        "AddOnClickListener",
        "AddOnValueChangedListener",
        "AddOnEndEditListener"
    })
    return self.target_
end
return Subset
