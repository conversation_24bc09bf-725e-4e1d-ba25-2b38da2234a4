local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local GameObject = CS.UnityEngine.GameObject
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local RectTransform = CS.UnityEngine.RectTransform
local ScrollRect = CS.UnityEngine.UI.ScrollRect


module("ui_halloween_activity_slot_machine_rule_info_pop_binding")

UIPath = "ui/prefabs/gw/activity/halloweenactivity/slotmachine/uihalloweenactivityslotmachineruleinfopop.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	item_draw_result_item = { path = "item_draw_result_item", type = GameObject, },
	txt_rule_page_group_template = { path = "txt_rule_page_group_template", type = Text, },
	txt_rule_page_title_template = { path = "txt_rule_page_group_template/txt_rule_page_title_template", type = Text, },
	txt_rule_page_content_template = { path = "txt_rule_page_group_template/txt_rule_page_content_template", type = Text, },
	img_bg = { path = "center/img_bg", type = Image, },
	rtf_reward_page = { path = "center/img_bg/rtf_reward_page", type = RectTransform, },
	sr_reward_page_ScrollView = { path = "center/img_bg/rtf_reward_page/sr_reward_page_ScrollView", type = ScrollRect, },
	rtf_reward_page_content = { path = "center/img_bg/rtf_reward_page/sr_reward_page_ScrollView/Viewport/rtf_reward_page_content", type = RectTransform, },
	txt_reward_page_line1 = { path = "center/img_bg/rtf_reward_page/sr_reward_page_ScrollView/Viewport/rtf_reward_page_content/txt_reward_page_line1", type = Text, },
	rtf_reward_page_3same = { path = "center/img_bg/rtf_reward_page/sr_reward_page_ScrollView/Viewport/rtf_reward_page_content/rtf_reward_page_3same", type = RectTransform, },
	txt_reward_page_line2 = { path = "center/img_bg/rtf_reward_page/sr_reward_page_ScrollView/Viewport/rtf_reward_page_content/txt_reward_page_line2", type = Text, },
	rtf_reward_page_2same = { path = "center/img_bg/rtf_reward_page/sr_reward_page_ScrollView/Viewport/rtf_reward_page_content/rtf_reward_page_2same", type = RectTransform, },
	txt_reward_page_line3 = { path = "center/img_bg/rtf_reward_page/sr_reward_page_ScrollView/Viewport/rtf_reward_page_content/txt_reward_page_line3", type = Text, },
	rtf_reward_page_0same = { path = "center/img_bg/rtf_reward_page/sr_reward_page_ScrollView/Viewport/rtf_reward_page_content/rtf_reward_page_0same", type = RectTransform, },
	rtf_rule_page = { path = "center/img_bg/rtf_rule_page", type = RectTransform, },
	sr_rule_page_ScrollView = { path = "center/img_bg/rtf_rule_page/sr_rule_page_ScrollView", type = ScrollRect, },
	rtf_rule_page_content = { path = "center/img_bg/rtf_rule_page/sr_rule_page_ScrollView/Viewport/rtf_rule_page_content", type = RectTransform, },
	btn_reward_dis = { path = "center/imt_title_bg/btn_reward_dis", type = Button, event_name = "OnBtnReward_disClickedProxy"},
	btn_rule_dis = { path = "center/imt_title_bg/btn_rule_dis", type = Button, event_name = "OnBtnRule_disClickedProxy"},
	btn_rule = { path = "center/imt_title_bg/btn_rule", type = Button, event_name = "OnBtnRuleClickedProxy"},
	btn_reward = { path = "center/imt_title_bg/btn_reward", type = Button, event_name = "OnBtnRewardClickedProxy"},

}
