--@region FileHead
-- ui_menu_side.txt ---------------------------------
-- author:  林金成
-- date:    1/9/2019 12:00:00 AM
-- ver:     1.0
-- desc:    Description
-------------------------------------------------
--@endregion 

--@region Require
local print     = print
local require   = require
local typeof    = typeof
local math      = math
local ipairs    = ipairs
local pairs     = pairs

local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local OptionData = CS.UnityEngine.UI.Dropdown.OptionData

local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local util                   = require "util"
local lang                  = require "lang"
local ui_window_mgr = require "ui_window_mgr"
local split_define = require "split_server_res_define"


--@endregion 

--@region ModuleDeclare
module("ui_grayload")


local window = nil
local M = {}

--@endregion 

--@region WidgetTable
M.widget_table = {
    stateToggle = { path = "top/p/stateToggle", type = "Button", event_name = "onStateToggle"},
    closebtn = { path = "top/p/newanniu1", type = "Button", event_name = "onClose" },
    blackbtn = { path = "top/black", type = "Button", event_name = "onClose" },
    rate_text = { path = "top/p/rate", type = "Text", },
    desc_text = { path = "top/p/desc", type = "Text", },
    wifi_text = { path = "top/p/wifi", type = "Text", },
    process = { path = "top/p/process", type = "RectTransform", },
    dropdown = {path = "top/p/Dropdown", type = "Dropdown", value_changed_event = "OnChangedDropdown"},
}
--@endregion 

--@region WindowCtor
function M:ctor(selfType)
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function M:Init()
    
    self:SubscribeEvents() 
end --///<<< function

--@endregion 

--更新UI根据参数
function M:UpdateUIOnParam()
    local split_server_res_download_mgr = require "split_server_res_download_mgr"
    if split_server_res_download_mgr.IsStartSplitServerResDownload() then
        local isShow = split_server_res_download_mgr.IsShowStartPause()
        if self.stateToggle then
            self.stateToggle:SetActive(isShow)
        end
        return 
    end
    
    local gray_load_mgr = require "gray_load_mgr"
    local param = gray_load_mgr.GetGrayLoadParam()
    local notShowStartPause = param.notShowStartPause
    if self.stateToggle then
        self.stateToggle:SetActive(not notShowStartPause)
    end
end

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function M:OnShow() 
 
	self.__base:OnShow()
    self:UpdateUI()
 
--@region User
--@endregion 
end --///<<< function
 

--@endregion 

--@region WindowOnHide
--[[资源加载完成，被显示的时候调用]]
function M:OnHide()
--@region User

--@endregion 
end --///<<< function

--@endregion 

--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function M:SetInputParam(p)
	self.inputParam = p

    --如果正在显示，则更新一次窗口
    if self:IsValid() then
        self:UpdateUI()
    end
	
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowBuildUpdateData
--[[构建UI更新数据]]
function M:BuildUpdateData()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function M:UpdateUI()
    self:BuildUpdateData()
    local split_server_res_download_mgr = require "split_server_res_download_mgr"
    if split_server_res_download_mgr.IsStartSplitServerResDownload() then
        self:SetDropdownData()
        self.dropdown.gameObject:SetActive(true)
    elseif not util.IsObjNull(self.dropdown) then
        self.dropdown.gameObject:SetActive(false)
    end
    
    --  print("updateUI")
    local gray_load_mgr = require "gray_load_mgr"

    local loadSize ,total,start_or_pause = gray_load_mgr.GetLoadParam()

    self.state = start_or_pause
    -- print("state",self.state,self.state and 0 or 1)
    util.toggle_child(self.stateToggle.transform,self.state and 0 or 1)
    self:UpdateUIOnParam()
    if self.ticker then
        util.RemoveDelayCall(self.ticker)
        self.ticker = nil
    end

    self.ticker = util.DelayCallOnce(0,TickFunc)
--@region User
--@endregion 
end --///<<< function
function toeNumber( v )
    return (math.round(v/1000000)).."M" 
end --///<<< function
function TickFunc(  )
    local self = window
    local gray_load_mgr = require "gray_load_mgr"
    local loadSize ,total,start_or_pause = gray_load_mgr.GetLoadParam()
    
    self.state = start_or_pause
    util.toggle_child(self.stateToggle.transform,self.state and 0 or 1)
    self:UpdateUIOnParam()
    local rate = math.floor(loadSize * 100 / total )
    -- print("tick",rate,total)
    self.rate_text.text = rate.."%"
    local loadDesc = lang.Get(401)  --正在下载:
    self.desc_text.text = loadDesc..toeNumber(loadSize).."\t/"..toeNumber(total)
    self.process.sizeDelta = { x = (rate / 100) * 380,y = 20 }
    local networkType = util.GetNetworkType()
    self.wifi_text = networkType == util.ENETWORK_TYPE.WIFI and lang.Get(404) or lang.Get(405)
    return 0.1
end
--@endregion 

--@region WindowClose
function M:Close() 
    if self:IsValid() then
        self:UnsubscribeEvents()
    end
    if self.ticker then
        util.RemoveDelayCall(self.ticker)
        self.ticker = nil
    end
    self.__base:Close()
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function M:SubscribeEvents()
    self.onStateToggle = function (  )
        self.state = not self.state

        util.toggle_child(self.stateToggle.transform,self.state and 0 or 1)

        local split_server_res_download_mgr = require "split_server_res_download_mgr"
        if split_server_res_download_mgr.IsStartSplitServerResDownload() then
            local eName = self.state and "DownloadRes_continue" or "DownloadRes_stop"
            split_server_res_download_mgr.Report(eName, {})
            if self.state then
                split_server_res_download_mgr.ResumeDownload()
            else
                split_server_res_download_mgr.PauseDownload()
            end
            return
        end
        
        local gray_load_mgr = require "gray_load_mgr"
        if self.state then
            gray_load_mgr.Report("DownloadRes_continue",{})
        else
            gray_load_mgr.Report("DownloadRes_stop",{})
        end
        gray_load_mgr.SetupLoad(self.state)
    end
    self.onClose = function ()
        ui_window_mgr:UnloadModule("ui_grayload")
    end

    self.OnChangedDropdown = function()
        if not self.ResVersionTaskNameList then
            return
        end
        
        local taskName = self.dropdown.captionText.text
        local version
        for ver, name in pairs(self.ResVersionTaskNameList) do
            if taskName == name then
                version = ver
                break
            end
        end
        if not version then return end

        local split_server_res_download_mgr = require "split_server_res_download_mgr"
        split_server_res_download_mgr.SwitchDownloadVersion(version)
        TickFunc()
    end
    
    self.OnTaskListChange = function()
        util.DelayCallOnce(0, function()
            self:SetDropdownData()
        end)
    end
    event.Register(split_define.TaskListChangeEvent, self.OnTaskListChange)
end --///<<< function

function M:SetDropdownData()
    local split_server_res_download_mgr = require "split_server_res_download_mgr"
    if split_server_res_download_mgr.IsStartSplitServerResDownload() then
        local versionQueue, versionTaskList = split_server_res_download_mgr.GetResVersionTasks()
        self.dropdown.options:Clear()
        self.ResVersionTaskNameList = {}
        for i, v in ipairs(versionQueue) do
            local resVerTaskName = versionTaskList[v]:GetTaskName()
            local option = OptionData()
            option.text = resVerTaskName
            self.dropdown.options:Add(option)
            if i == 1 then
                self.dropdown.value = i
                self.dropdown.captionText.text = option.text
            end
            self.ResVersionTaskNameList[v] = resVerTaskName
        end 
    end
end

--@endregion 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function M:UnsubscribeEvents()
    event.Unregister(split_define.TaskListChangeEvent, self.OnTaskListChange)
    --@region User
--@endregion 
end --///<<< function 
 
--@region WindowInherited
local CM = class(ui_base, nil, M)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CM()
        window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uigrayload.prefab", nil, nil)
    else
        window:Show()
    end
    return window
end

function OnShowLobbyUI(eventName)
    if window ~= nil and not not window.UIRoot then
        window:OnShowLobbyUI()
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

--@endregion 
 
function OnSceneDestroy()
	Close() 

end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)
 