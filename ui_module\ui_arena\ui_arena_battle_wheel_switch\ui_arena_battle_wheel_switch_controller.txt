local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local event_arena_common_define = require "event_arena_common_define"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_arena_battle_wheel_switch_controller")
local controller = nil
local UIController = newClass("ui_arena_battle_wheel_switch_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name) 
    self.defendData = data
    self:TriggerUIEvent("OnRefreshDefendData", self.defendData.defendInfo)
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
    local changeLineup = function()
        if self.defendData.GetDefendLineUp then
            self.defendData.defendInfo = self.defendData.GetDefendLineUp(self.defendData.nArenaID)
            self:TriggerUIEvent("OnRefreshDefendData", self.defendData.defendInfo)
        end
    end
    self:RegisterEvent(event_arena_common_define.TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP, changeLineup)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnLeftBtnClickedProxy()
    self:CloseView()
end
function  UIController:OnBtnSwitch1ClickedProxy()
    if self.defendData.SwitchDefenseLineUp then
        self.defendData.SwitchDefenseLineUp(1,2)
    end
end
function  UIController:OnBtnSwitch2ClickedProxy()
    if self.defendData.SwitchDefenseLineUp then
        self.defendData.SwitchDefenseLineUp(2, 3)
    end
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
