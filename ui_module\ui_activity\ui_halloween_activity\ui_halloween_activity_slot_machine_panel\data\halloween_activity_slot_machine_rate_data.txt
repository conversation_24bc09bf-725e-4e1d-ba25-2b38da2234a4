local game_scheme = require "game_scheme"

---@class halloween_activity_slot_machine_rate_data
local halloween_activity_slot_machine_rate_data = {
    rate_dist = {}
}


function halloween_activity_slot_machine_rate_data.Init(act_id)
    local rate_dist = {}
    local total_weight = 0

    local this_act_cfg_list = {}

    local cfgNum = game_scheme:SlotGame_nums()
    for i = 0, cfgNum - 1 do
        local cfg = game_scheme:SlotGame(i)
        if cfg.AtyID == act_id then
            table.insert(this_act_cfg_list, cfg)
        end
    end

    -- 第一遍遍历：计算总权重
    for i = 1, #this_act_cfg_list do
        local cfg = game_scheme:SlotGame_0(i, act_id)
        local weight = cfg.Probability
        total_weight = total_weight + weight
    end

    -- 第二遍遍历：计算每个配置项的概率
    for i = 1, #this_act_cfg_list do
        local cfg = game_scheme:SlotGame_0(i, act_id)
        local weight = cfg.Probability
        local id = cfg.ID  
        
        if total_weight > 0 then
            rate_dist[id] = weight / total_weight
        else
            rate_dist[id] = 0  -- 防止除零错误
        end
    end

    halloween_activity_slot_machine_rate_data.rate_dist = rate_dist
    return rate_dist
end

return halloween_activity_slot_machine_rate_data