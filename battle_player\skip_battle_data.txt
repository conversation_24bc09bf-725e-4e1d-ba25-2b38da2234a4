-- skip_battle_data.txt ------------------------------------------
-- author:  赖嘉明
-- date:    2021.04.25
-- ver:     1.0
-- desc:    跳过战斗数据管理
--------------------------------------------------------------
local require = require

local player_mgr = require "player_mgr"
local ui_pop_mgr = require "ui_pop_mgr"
local game_scheme = require "game_scheme"

local PlayerPrefs = CS.UnityEngine.PlayerPrefs

module("skip_battle_data")

VOID_CHAMPIONSHIPS = "voidChampionshipsSkipBattle"
LOST_LAND = "lostLandSkipBattle"
ARENA = "arenaSkipBattle"
SPACE_DISCOVERY = "spaceDiscoverySkipBattle"
QUANTUM_INVASION = "quantumInvasionSkipBattle"
HUB_TRIALS = "hubTrialsSkipBattle"
WANTED = "IllusionTowerSkipBattle"

local ConfigIDMap = 
{
    --configID ： InitBattleProp表ID
    --type ： 条件类型，1：关卡开放 2：挑战次数开放
    ["voidChampionshipsSkipBattle"] = {configID = 801, type = 1},
    ["lostLandSkipBattle"] = {configID = 802, type = 1},
    ["arenaSkipBattle"] = {configID = 806, type = 2},
    ["spaceDiscoverySkipBattle"] = {configID = 803, type = 1},
    ["quantumInvasionSkipBattle"] = {configID = 804, type = 1},
    ["hubTrialsSkipBattle"] = {configID = 805, type = 1},
    ["IllusionTowerSkipBattle"] = {configID = 392, type = 1},
}

function SetSkipState(mark, lv, bool)
	local prefs = bool and 1 or 0
	PlayerPrefs.SetInt(player_mgr.GetPlayerRoleID()..mark..(lv or ""), prefs)
end

function GetSkipState(mark, lv)
    local config = ConfigIDMap[mark]
    if not config then
        return false 
    end
    if config.type == 1 then
        local skipUnlock = ui_pop_mgr.CheckIsOpen(config.configID, false)
        if not skipUnlock then
            return false
        end
    elseif config.type == 2 then
        local cfg = game_scheme:InitBattleProp_0(config.configID)
        if not cfg then
            return false
        end
        if GetChallengeTimes(mark) < cfg.szParam.data[0] then
            return false
        end
    end
    local prefs = PlayerPrefs.GetInt(player_mgr.GetPlayerRoleID()..mark..(lv or ""), 0)
    if prefs == 0 then
        return false
    else
        return true
    end
end

function CanSkip(mark)
    local config = ConfigIDMap[mark]
    if not config then
        return false 
    end
    if config.type == 1 then
        local skipUnlock = ui_pop_mgr.CheckIsOpen(config.configID, true)
        if not skipUnlock then
            return false
        end
    elseif config.type == 2 then
        local cfg = game_scheme:InitBattleProp_0(config.configID)
        if not cfg then
            return false
        end
        local requireTimes = cfg.szParam.data[0]
        if GetChallengeTimes(mark) < requireTimes then
            return false
        end
    end
    return true
end

function GetRequireTimes(mark)
    local config = ConfigIDMap[mark]
    if not config then
        return false 
    end
    if config.type == 2 then
        local cfg = game_scheme:InitBattleProp_0(config.configID)
        if not cfg then
            return false
        end
        return cfg.szParam.data[0]
    end
    return 0
end

function GetChallengeTimes(mark)
    if mark == ARENA then
        local arena_data = require "arena_data"
        return arena_data.GetChallengeTimes()
    end
    return 0
end