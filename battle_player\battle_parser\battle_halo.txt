local util = require "util"
local battle_config = require "battle_config"
local event = require "event"
local ui_active_array = require "ui_active_array"


module("battle_halo")

leftHaloId = nil
rightHaloId = nil

leftHaloData = nil
rightHaloData = nil

battleEnd = false

local leftArray = nil
local rightArray = nil

function ShowHalo(player, leftPalId, rightPalId)
    battleEnd = false
    player:SetupContainer(OnShow)
    
    local array_res = battle_config.GetCommonActiveArrayRes()
    local float_res = battle_config.CommonActiveArrayFloatText

    if leftHaloId ~= 0 then
        player:AppendAction(leftPalId, array_res)
    end

    if rightHaloId ~= 0 then
        player:AppendAction(rightPalId, array_res)
    end

    if leftHaloId ~= 0 or rightPalId ~= 0 then
        player:SetupPrelude(float_res)
    end
end

function OnShow()
    if battleEnd then
        return 
    end

    util.DelayCall(0.2, function()
        if not battleEnd then
            if leftHaloId and leftHaloId ~= 0 then
                leftArray = ui_active_array.CActiveArray():Init()
                leftArray:SetHalo(leftHaloId, 1)
            end

            if rightHaloId and rightHaloId ~= 0 then
                rightArray = ui_active_array.CActiveArray():Init()
                rightArray:SetHalo(rightHaloId, 2)
            end
        end
    end)

    util.DelayCall(4, function()
        if rightArray then
            rightArray:Dispose()
            rightArray = nil
        end
        if leftArray then
            leftArray:Dispose()
            leftArray = nil
        end
    end)
end

function OnBattleSkip(eventName, str)
    battleEnd = true
    if rightArray then
        rightArray:Dispose()
        rightArray = nil
    end
    if leftArray then
        leftArray:Dispose()
        leftArray = nil
    end
end

event.Register(event.BATTLE_SKIP, OnBattleSkip)
