local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_arena_rank_change_binding"

--region View Life
module("ui_arena_rank_change")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
function UIView:InitBaseShow(data)
    if not data then
        return
    end
    local imgIndex = data.isUp and 0 or 1
    self.ss_BG:Switch(imgIndex)
    self.ss_rankBg:Switch(imgIndex)
    self.ss_playerNameBG:Switch(imgIndex)
    self.ss_changeArrow:Switch(imgIndex)
    
    self:SetActive(self.txt_curRankNumUp, data.isUp)
    self:SetActive(self.txt_curRankNumDown, not data.isUp)
    
    self.txt_oldRank.text = data.oldRank
    self.txt_curRank.text = data.curRank
    self.txt_playerName.text = data.playerName
    
    if data.isUp then
        self.txt_curRankNumUp.text = data.curRank
    else
        self.txt_curRankNumDown.text = data.curRank
    end
    --1009127 排名上升
    --1009126 排名下降
    self.txt_changeDes.text = data.isUp and lang.Get(1009127) or lang.Get(1009126) 
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
