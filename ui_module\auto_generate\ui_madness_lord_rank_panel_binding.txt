local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local GameObject = CS.UnityEngine.GameObject
local Text = CS.UnityEngine.UI.Text
local Transform = CS.UnityEngine.Transform
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local Toggle = CS.UnityEngine.UI.Toggle


module("ui_madness_lord_rank_panel_binding")

UIPath = "ui/prefabs/gw/activity/madnesslord/uimadnesslordrankpanel.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	obj_selfPage = { path = "panel/obj_selfPage", type = GameObject, },
	obj_rankInfo = { path = "panel/obj_selfPage/obj_rankInfo", type = GameObject, },
	txt_rank1Damage = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank1Obj/damageBg/txt_rank1Damage", type = Text, },
	obj_rank1PlayerInfo = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank1Obj/obj_rank1PlayerInfo", type = GameObject, },
	tf_rank1Head = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank1Obj/obj_rank1PlayerInfo/tf_rank1Head", type = Transform, },
	btn_rank1Like = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank1Obj/obj_rank1PlayerInfo/btn_rank1Like", type = Button, event_name = "OnBtnRank1LikeClickedProxy"},
	txt_rank1LikeCount = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank1Obj/obj_rank1PlayerInfo/btn_rank1Like/txt_rank1LikeCount", type = Text, },
	txt_rank1ShortName = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank1Obj/obj_rank1PlayerInfo/txt_rank1ShortName", type = Text, },
	txt_rank1Name = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank1Obj/obj_rank1PlayerInfo/txt_rank1Name", type = Text, },
	obj_rank1Head_Empty = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank1Obj/obj_rank1Head_Empty", type = GameObject, },
	txt_rank1Damage = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank2Obj/damageBg/txt_rank1Damage", type = Text, },
	obj_rank2PlayerInfo = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank2Obj/obj_rank2PlayerInfo", type = GameObject, },
	tf_rank2Head = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank2Obj/obj_rank2PlayerInfo/tf_rank2Head", type = Transform, },
	btn_rank2Like = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank2Obj/obj_rank2PlayerInfo/btn_rank2Like", type = Button, event_name = "OnBtnRank2LikeClickedProxy"},
	txt_rank2LikeCount = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank2Obj/obj_rank2PlayerInfo/btn_rank2Like/txt_rank2LikeCount", type = Text, },
	txt_rank2ShortName = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank2Obj/obj_rank2PlayerInfo/txt_rank2ShortName", type = Text, },
	txt_rank2Name = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank2Obj/obj_rank2PlayerInfo/txt_rank2Name", type = Text, },
	obj_rank2Head_Empty = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank2Obj/obj_rank2Head_Empty", type = GameObject, },
	txt_rank3Damage = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank3Obj/damageBg/txt_rank3Damage", type = Text, },
	obj_rank3PlayerInfo = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank3Obj/obj_rank3PlayerInfo", type = GameObject, },
	tf_rank3Head = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank3Obj/obj_rank3PlayerInfo/tf_rank3Head", type = Transform, },
	btn_rank3Like = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank3Obj/obj_rank3PlayerInfo/btn_rank3Like", type = Button, event_name = "OnBtnRank3LikeClickedProxy"},
	txt_rank1LikeCount = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank3Obj/obj_rank3PlayerInfo/btn_rank3Like/txt_rank1LikeCount", type = Text, },
	txt_rank3ShortName = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank3Obj/obj_rank3PlayerInfo/txt_rank3ShortName", type = Text, },
	txt_rank3Name = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank3Obj/obj_rank3PlayerInfo/txt_rank3Name", type = Text, },
	obj_rank1Head_Empty = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/rank3Obj/obj_rank1Head_Empty", type = GameObject, },
	txt_rankTitle = { path = "panel/obj_selfPage/obj_rankInfo/rankBg/titleBg/txt_rankTitle", type = Text, },
	srt_selfRankPage = { path = "panel/obj_selfPage/obj_rankInfo/ScrollView/Scroll View/Viewport/srt_selfRankPage", type = ScrollRectTable, },
	srt_myRank = { path = "panel/obj_selfPage/obj_rankInfo/srt_myRank", type = ScrollRectTable, },
	obj_SelfPageComEmpty = { path = "panel/obj_selfPage/obj_SelfPageComEmpty", type = GameObject, },
	obj_bossDamagePage = { path = "panel/obj_bossDamagePage", type = GameObject, },
	obj_bossDamageInfo = { path = "panel/obj_bossDamagePage/obj_bossDamageInfo", type = GameObject, },
	srt_bossDamagePage = { path = "panel/obj_bossDamagePage/obj_bossDamageInfo/ScrollView/Scroll View/Viewport/srt_bossDamagePage", type = ScrollRectTable, },
	srt_myBossDamage = { path = "panel/obj_bossDamagePage/obj_bossDamageInfo/srt_myBossDamage", type = ScrollRectTable, },
	obj_bossDamageComEmpty = { path = "panel/obj_bossDamagePage/obj_bossDamageComEmpty", type = GameObject, },
	obj_alliancePage = { path = "panel/obj_alliancePage", type = GameObject, },
	obj_allianceInfo = { path = "panel/obj_alliancePage/obj_allianceInfo", type = GameObject, },
	srt_alliancePage = { path = "panel/obj_alliancePage/obj_allianceInfo/ScrollView/Scroll View/Viewport/srt_alliancePage", type = ScrollRectTable, },
	srt_myAllianceInfo = { path = "panel/obj_alliancePage/obj_allianceInfo/srt_myAllianceInfo", type = ScrollRectTable, },
	obj_allianceComEmpty = { path = "panel/obj_alliancePage/obj_allianceComEmpty", type = GameObject, },
	btn_LeftBtn = { path = "panel/btn_LeftBtn", type = Button, event_name = "OnBtnLeftBtnClickedProxy"},
	tog_selfRank = { path = "panel/Scroll View/Viewport/Content/tog_selfRank", type = Toggle, value_changed_event = "OnTogSelfRankValueChange"},
	obj_selfRank_txt = { path = "panel/Scroll View/Viewport/Content/tog_selfRank/Background/obj_selfRank_txt", type = GameObject, },
	obj_selfRank_sel_txt = { path = "panel/Scroll View/Viewport/Content/tog_selfRank/Background/Checkmark/obj_selfRank_sel_txt", type = GameObject, },
	tog_boss1 = { path = "panel/Scroll View/Viewport/Content/tog_boss1", type = Toggle, value_changed_event = "OnTogBoss1ValueChange"},
	txt_boss1_txt = { path = "panel/Scroll View/Viewport/Content/tog_boss1/Background/txt_boss1_txt", type = Text, },
	txt_boss1_sel_txt = { path = "panel/Scroll View/Viewport/Content/tog_boss1/Background/Checkmark/txt_boss1_sel_txt", type = Text, },
	tog_boss2 = { path = "panel/Scroll View/Viewport/Content/tog_boss2", type = Toggle, value_changed_event = "OnTogBoss2ValueChange"},
	txt_boss2_txt = { path = "panel/Scroll View/Viewport/Content/tog_boss2/Background/txt_boss2_txt", type = Text, },
	txt_boss2_sel_txt = { path = "panel/Scroll View/Viewport/Content/tog_boss2/Background/Checkmark/txt_boss2_sel_txt", type = Text, },
	tog_boss3 = { path = "panel/Scroll View/Viewport/Content/tog_boss3", type = Toggle, value_changed_event = "OnTogBoss3ValueChange"},
	txt_boss3_txt = { path = "panel/Scroll View/Viewport/Content/tog_boss3/Background/txt_boss3_txt", type = Text, },
	txt_boss3_sel_txt = { path = "panel/Scroll View/Viewport/Content/tog_boss3/Background/Checkmark/txt_boss3_sel_txt", type = Text, },
	tog_alliance = { path = "panel/Scroll View/Viewport/Content/tog_alliance", type = Toggle, value_changed_event = "OnTogAllianceValueChange"},
	obj_alliance_txt = { path = "panel/Scroll View/Viewport/Content/tog_alliance/Background/obj_alliance_txt", type = GameObject, },
	obj_alliance_sel_txt = { path = "panel/Scroll View/Viewport/Content/tog_alliance/Background/Checkmark/obj_alliance_sel_txt", type = GameObject, },
	btn_playerDamageMask = { path = "btn_playerDamageMask", type = Button, event_name = "OnBtnPlayerDamageMaskClickedProxy"},
	tf_playerDamageHead = { path = "btn_playerDamageMask/tf_playerDamageHead", type = Transform, },
	txt_playerDamageName = { path = "btn_playerDamageMask/txt_playerDamageName", type = Text, },
	txt_playerDamagePower = { path = "btn_playerDamageMask/powerBg/txt_playerDamagePower", type = Text, },
	txt_playerDamageRank = { path = "btn_playerDamageMask/txt_playerDamageRank", type = Text, },
	txt_damageTipsBoss1Name = { path = "btn_playerDamageMask/damageLayout/boss1/txt_damageTipsBoss1Name", type = Text, },
	txt_damageTipsBoss1Damage = { path = "btn_playerDamageMask/damageLayout/boss1/txt_damageTipsBoss1Damage", type = Text, },
	txt_damageTipsBoss2Name = { path = "btn_playerDamageMask/damageLayout/boss2/txt_damageTipsBoss2Name", type = Text, },
	txt_damageTipsBoss2Damage = { path = "btn_playerDamageMask/damageLayout/boss2/txt_damageTipsBoss2Damage", type = Text, },
	txt_damageTipsBoss3Name = { path = "btn_playerDamageMask/damageLayout/boss3/txt_damageTipsBoss3Name", type = Text, },
	txt_damageTipsBoss3Damage = { path = "btn_playerDamageMask/damageLayout/boss3/txt_damageTipsBoss3Damage", type = Text, },
	txt_damageTipsBoss4Name = { path = "btn_playerDamageMask/damageLayout/boss4/txt_damageTipsBoss4Name", type = Text, },
	txt_damageTipsBoss4Damage = { path = "btn_playerDamageMask/damageLayout/boss4/txt_damageTipsBoss4Damage", type = Text, },

}
