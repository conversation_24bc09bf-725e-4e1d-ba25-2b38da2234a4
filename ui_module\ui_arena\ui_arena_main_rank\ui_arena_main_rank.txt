local os = os
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local e_handler_mgr = require "e_handler_mgr"
local time_util = require "time_util"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_arena_main_rank_binding"
local face_item = require "face_item_new"

local playerItemName = "scrItem_PlayerItem_"

--region View Life
module("ui_arena_main_rank")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)
    self.onRankRenderFunc = function(...)
        self:OnRankRenderFunc(...)
    end
    self.srt_rankList.onItemRender = self.onRankRenderFunc
    self.srt_rankList.onItemDispose = function(scroll_rect_item,index)
        if scroll_rect_item.data and scroll_rect_item.data["faceItem"] then
            scroll_rect_item.data["faceItem"]:Dispose()
            scroll_rect_item.data["faceItem"] = nil
        end
    end
    self.faceItemList = {}

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
    if not util.IsObjNull(self.canv_showDailyReward) then
        self.canv_showDailyReward.sortingOrder = self.curOrder + 2
    end
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.faceItemList then
        for k, v in pairs(self.faceItemList) do
            if v then
                v:Dispose()
            end
        end
        self.faceItemList = nil
    end
    if self.profileItem then
        self.profileItem:Dispose()
        self.profileItem = nil
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

--设置基础信息
function UIView:InitBaseShow(data)
    if not data then
        return
    end
    self:CreateTimer(1, function()
        local curTime = data.endTime - os.server_time()
        if curTime <= 0 then
            return true
        end
        local time = time_util.FormatTimeXMan(curTime)
        self.txt_arenaEndTime.text = time
    end )
    self.txt_openServerID.text = data.serverIdsStr
    self.txt_arenaName.text = lang.Get(data.arenaTypeLang)
    --TODO 晋升红点
end

--刷新界面显示
function UIView:RefreshShow(data)
    if not data then
        return
    end
    
    --设置自己排名的显示
    self.profileItem = self:SetListItem(data.myRankInfo, self.scrItem_selfInfo, self.profileItem)
    
    --挑战次数：
    self.txt_challengeNum.text = string.format("%s%d", lang.Get(1009114), data.challengeNum)
    
    self:SetActive(self.btn_Promotion, data.canPromote and data.canPromote ~= 0)
end

--刷新每日宝箱显示
function UIView:RefreshDailyBoxShow(index)
    if not index then
        return
    end
    self.ss_showDailyReward:Switch(index)
end

--刷新排行榜
function UIView:UpdateRand(data, updateNum)
    if not data then
        return
    end
    self.srt_rankList:SetData(data, #data)
    self.srt_rankList:Refresh(-1, -1)
    self.srt_rankList.renderPerFrames = 10
    if updateNum then
        self:CreateDelayTimer(0.2, function()
            if self:IsValid() then
                self.srt_rankList:ScrollTo(updateNum)
            end

        end )
    end
end

function UIView:OnRankRenderFunc(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    scroll_rect_item.data[3] = scroll_rect_item.data[3] or {}

    scroll_rect_item.data["faceItem"] = self:SetListItem(dataItem, scroll_rect_item, scroll_rect_item.data["faceItem"])
    e_handler_mgr.TriggerHandler(window.controller_name, "OnScrollRectItemRender", index, dataItem)
end

--设置排行榜item显示
function UIView:SetListItem(dataItem, scroll_rect_item, faceItem)
    local name = scroll_rect_item:Get("name")
    local rank = scroll_rect_item:Get("rank")
    local faceTra = scroll_rect_item:Get("faceTra")
    local powerNum = scroll_rect_item:Get("powerNum")
    local scoreNum = scroll_rect_item:Get("scoreNum")
    local soldierIcon = scroll_rect_item:Get("soldierIcon")
    local soldierLv = scroll_rect_item:Get("soldierLv")

    rank.text = dataItem.rank
    name.text = string.format("%s %s", dataItem.worldStr, util.SplicingUnionShortName(dataItem.unionShortName, dataItem.name, true))
    powerNum.text = util.NumberWithUnit2(dataItem.power)
    scoreNum.text = util.NumberWithUnit2(dataItem.score) 
    self:CreateSubSprite("CreateSoldierHeadAsset", soldierIcon, dataItem.soldierIcon)
    soldierLv.text = string.format("Lv.%d", dataItem.soldierLv)

    local item = faceItem or face_item.CFaceItem():Init(faceTra, nil, 1)
    item:SetFaceInfo(dataItem.faceStr, function()
        local player_mgr = require "player_mgr"
        if dataItem.roleID ~= player_mgr.GetPlayerRoleID() then
            local mgr_personalInfo = require "mgr_personalInfo"
            mgr_personalInfo.ShowRoleInfoView(dataItem.roleID)
        end
    end)
    item:SetNewBg(true)
    item:SetFrameID(dataItem.frameID, true)
    item:FrameEffectEnable(true, self.curOrder+1)
    return item
end

--设置前三名玩家消息
function UIView:SetTopPlayerInfo(data, likingClick)
    if not data then
        return
    end
    for i = 1, 3 do
        local playerData = data[i]
        if playerData then
            self:SetSinglePlayerItem(i, playerData, likingClick)
        end
    end
end

function UIView:SetSinglePlayerItem(index, playerData, likingClick)
    if not playerData then
        return
    end
    local itemName = string.format("%s%d", playerItemName, index)
    local item = self[itemName]
    if not item then
        return
    end

    local faceTra = item:Get("faceTra")
    local unionShortName = item:Get("unionShortName")
    local playerName = item:Get("playerName")
    local unionName = item:Get("unionName")
    local scoreNum = item:Get("scoreNum")
    local powerNum = item:Get("powerNum")

    local faceItem = self.faceItemList[index] or face_item.CFaceItem()
    faceItem:Init(faceTra.transform, nil, index == 1 and 1.4 or 1.2)
    faceItem:SetNewBg(true)
    faceItem:SetFaceInfo( playerData.faceStr, function ()
        local mgr_personalInfo = require "mgr_personalInfo"
        mgr_personalInfo.ShowRoleInfoView(playerData.roleId)
        --weekend_arena_mgr.Send_PLAYER_DETAILS(data.baseinfo.roleID)
    end)
    faceItem:SetActorLvText(true, playerData.roleLv)
    faceItem:FrameEffectEnable(true, self.curOrder+1)
    faceItem:SetFrameID(playerData.frameID, true)
    faceItem:SetEnableState(true,true)

    self.faceItemList[index] = faceItem
    unionShortName.text = string.format("%s %s", playerData.worldStr, util.SplicingUnionShortName(playerData.unionShortName))
    playerName.text = playerData.name
    scoreNum.text = util.NumberWithUnit2(playerData.score)
    powerNum.text = util.NumberWithUnit2(playerData.power)

    --初始化点赞按钮事件
    local ClickLikeBtn = item:Get("ClickLikeBtn")
    UIUtil.SetActive(ClickLikeBtn,true)
    self:AddBtnOnClick(ClickLikeBtn,function()
        likingClick(playerData)
    end)

    --初始化点赞信息
    local iconNum = item:Get("iconNum")
    local icon = item:Get("icon")
    if playerData.itemInfo then
        self:CreateSubSprite("CreateSpriteAsset",icon, playerData.itemInfo.icon)
        if not util.IsObjNull(iconNum) then
            iconNum.text = string.format("+%s", playerData.itemInfo.num)
        end
    end
end

--每日奖励点击方法
function UIView:ShowDailyReward(data)
    if not data then
        return
    end
    data.uiParent = self.btn_showDailyReward.transform
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:ShowModule("ui_arena_daily_box", nil, nil, data)
end


--TODO 点赞无封装只能复制
--region 点赞
--刷新单个点赞数量显示
function UIView:RefreshSingleLikingNum(index,data)
    local itemName = string.format("%s%d", playerItemName, index)
    local item = self[itemName]
    if not item then
        return
    end
    local LikeNum = item:Get("LikeNum")
    LikeNum.text = data.likeNum
end

--刷新单个点赞红点显示
function UIView:RefreshSingleLikingRedDot(index,isShow)
    local itemName = string.format("%s%d", playerItemName, index)
    local item = self[itemName]
    if not item then
        return
    end
    local RedDot = item:Get("RedDot")
    UIUtil.SetActive(RedDot, isShow)
end

--显示点赞动画
function UIView:ShowLikingAnimation(index)
    local itemName = string.format("%s%d", playerItemName, index)
    local item = self[itemName]
    if not item then
        return
    end
    local Ani = item:Get("Ani")
    Ani:Play("AreaLike")
end
--endregion

function UIView:UpdateUI()
    if not util.IsObjNull(self.canv_showDailyReward) then
        self.canv_showDailyReward.sortingOrder = self.curOrder + 2
    end
end


--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
