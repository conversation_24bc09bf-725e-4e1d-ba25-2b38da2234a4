﻿---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2025/6/4 11:52
--- Desc: 独立小游戏实体(处理开始战斗之后的时间计时等逻辑)
local math = math
local newclass = newclass
local Input = CS.UnityEngine.Input
local DateTime = CS.System.DateTime
local gw_independent_game_const = require "gw_independent_game_const"
local event = require "event"
local game_scheme = require "game_scheme"
local log = require "log"
local util = require "util"

module("gw_independent_game_entity")

local GWMiniGameEntity = newclass("gw_independent_game_entity")

function GWMiniGameEntity:ctor()
    --战斗开始时间
    self.enterGameTimeRecord = 0
    --小游戏信息
    self.miniGameInfo = {}
    --
    self.startGameTime = 0
    --鼠标事件记录
    self.respondMouseTimer = nil
end

function GWMiniGameEntity:OpenMiniGame(gameType, levelId, miniGameName)
    self.miniGameName = miniGameName
    --进入游戏
    self:ResetStartGameTime(DateTime.Now)
    self.curGameLevelCfg = game_scheme:MiniGameLevelControl_1(0, gameType, levelId)
end

--进入游戏/重置游戏开始时间
function GWMiniGameEntity:ResetStartGameTime(setValue)
    if setValue then
        self.startGameTime = setValue
        self:MiniGameTimer()
    else
        self.startGameTime = nil
    end
end

function GWMiniGameEntity:ResetMiniGame(reason)
    local miniLevelCfg = self.curGameLevelCfg
    if miniLevelCfg then
        local stayTime = self:TimeCount(DateTime.Now)
        self.miniGameInfo.stayTime = stayTime
        local data = {
            type = miniLevelCfg.LevelType,
            LevelID = miniLevelCfg and miniLevelCfg.LevelID or 0,
            StayTime = stayTime,
            mini_game_name = self.miniGameName or "",
            Retry_reason = reason or 0
        }
        event.Trigger(event.GAME_EVENT_REPORT, "MiniGame_Retry", data)
    end
    self:ResetStartGameTime(DateTime.Now)
end

function GWMiniGameEntity:MiniGameTimer()
    --计算用户开始游戏到结束游戏的时间差（s）
    if self.respondTimer then
        util.RemoveDelayCall(self.respondTimer)
        self.respondTimer = nil
    end
    self.respondTimer = util.IntervalCall(1, function()
        if self.startGameTime and self.enterGameTimeRecord and self.enterGameTimeRecord <= 300 then
            self.enterGameTimeRecord = self.enterGameTimeRecord + 1
        else
            return true
        end
    end)
    --实时监听用户点击界面响应
    if self.respondMouseTimer then
        util.RemoveDelayCall(self.respondMouseTimer)
        self.respondMouseTimer = nil
    end
    self.respondMouseTimer = util.IntervalCall(0, function()
        if self.miniGameInfo.loadTime == nil then
            self.miniGameInfo.loadTime = self:TimeCount(DateTime.Now)
        end
        if self.startGameTime and self.miniGameInfo and enterGameTimeRecord and enterGameTimeRecord <= 300 then
            if Input.GetMouseButtonDown(0) then
                if enterGameTimeRecord <= 5 then
                    self.miniGameInfo[5] = true
                elseif enterGameTimeRecord <= 15 then
                    self.miniGameInfo[15] = true
                elseif enterGameTimeRecord <= 30 then
                    self.miniGameInfo[30] = true
                else
                    self.miniGameInfo[300] = true
                end
            end
        else
            return true
        end
    end, 0.1)
end

---@public 获取小游戏时间
function GWMiniGameEntity:TimeCount(endTime)
    if self.startGameTime then
        return math.floor((endTime - self.startGameTime).TotalSeconds)
    else
        log.Error("//<进入小游戏时获取本地时间为nil>//")
    end
end

---@public 获取小游戏信息
function GWMiniGameEntity:GetMiniGameInfo()
    return self.miniGameInfo
end

---@public 获取当前小游戏配置
function GWMiniGameEntity:GetCurGameLevelCfg()
    return self.curGameLevelCfg
end

---@public 小游戏结束
---@param isWin boolean 是否胜利
---@param miniGameName string 小游戏名称
---@param gameType number 游戏类型
---@param LevelId number 关卡id
function GWMiniGameEntity:MiniGameFinish(isWin, miniGameName, gameType, LevelId, hookLevelCfg, reasonType)
    if not self.curGameLevelCfg then
        self.curGameLevelCfg = game_scheme:MiniGameLevelControl_1(0, gameType, LevelId)
    end
    local stayTime = self:TimeCount(DateTime.Now)
    self.miniGameInfo.stayTime = stayTime
    miniGameName = miniGameName or self.miniGameName

    if not gw_independent_game_const.SoldiersSortieTypeFight[gameType] then
        if isWin then
            self:ReportMiniGameSuccess(stayTime, miniGameName)
        else
            self:ReportMiniGameFail(stayTime, miniGameName, reasonType)
        end
    end

    --打点主线
    if hookLevelCfg then
        if isWin then
            self:ReportHookSuccess(hookLevelCfg, stayTime)
        else
            self:ReportHookFail(hookLevelCfg, stayTime)
        end
    end
    self:ReportMiniGameInfo(isWin, stayTime, miniGameName)
end

---@public 打点小游戏成功
function GWMiniGameEntity:ReportMiniGameSuccess(stayTime, miniGameName)
    local reportData = {
        type = self.curGameLevelCfg and self.curGameLevelCfg.LevelType or 0,
        LevelID = self.curGameLevelCfg and self.curGameLevelCfg.LevelID or 0,
        StayTime = stayTime,
        mini_game_name = miniGameName or ""
    }
    event.Trigger(event.GAME_EVENT_REPORT, "MiniGame_Success", reportData)
end
---@public 打点小游戏失败
---@param reasonType integer 失败原因 1=正常失败，2=主动退出
function GWMiniGameEntity:ReportMiniGameFail(stayTime, miniGameName, reasonType)
    local reportData = {
        type = self.curGameLevelCfg and self.curGameLevelCfg.LevelType or 0,
        LevelID = self.curGameLevelCfg and self.curGameLevelCfg .LevelID or 0,
        StayTime = stayTime,
        mini_game_name = miniGameName or "",
        Fail_reason = reasonType or 1
    }
    event.Trigger(event.GAME_EVENT_REPORT, "MiniGame_Fail", reportData)
end
---@public 打点小游戏信息
function GWMiniGameEntity:ReportMiniGameInfo(isWin, stayTime, miniGameName)
    local minigameInfo = self:GetMiniGameInfo()
    if not minigameInfo then
        return
    end
    local reportData = {
        type = self.curGameLevelCfg and self.curGameLevelCfg.LevelType or 0,
        LevelID = self.curGameLevelCfg and self.curGameLevelCfg .LevelID or 0,
        StayTime = stayTime,
        StayTime5number = (minigameInfo[5] and 1) or 0,
        StayTime15number = (minigameInfo[15] and 1) or 0,
        StayTime30number = (minigameInfo[30] and 1) or 0,
        StayTime300number = (minigameInfo[300] and 1) or 0,
        isWin = (iswin == true and 1) or 0,
        loadTime = minigameInfo.loadTime,
        mini_game_name = miniGameName or ""
    }
    event.Trigger(event.GAME_EVENT_REPORT, "MiniGame_info", reportData)
end

---@public 打点主线成功
function GWMiniGameEntity:ReportHookSuccess(hookLevelCfg, endTime)
    --主线打点
    local reportMsg = {
        Level_id = hookLevelCfg.checkNumber, --关卡ID
        Hook_type = 2, --关卡类型
        Cost_time = endTime, --耗时
        battle_round = 0, --回合数(跳过战斗回合数是0)
    }
    event.Trigger(event.GAME_EVENT_REPORT, "Hook_success", reportMsg)
end
---@public 打点主线失败
function GWMiniGameEntity:ReportHookFail(hookLevelCfg, endTime)
    --主线打点
    local reportMsg = {
        Level_id = hookLevelCfg.checkNumber, --关卡ID
        Hook_type = 2, --关卡类型
        Cost_time = endTime, --耗时
        battle_round = 0, --回合数(跳过战斗回合数是0)
    }
    event.Trigger(event.GAME_EVENT_REPORT, "Hook_fail", reportMsg)
end

function GWMiniGameEntity:Dispose()
    --战斗开始时间
    self.enterGameTimeRecord = 0
    --小游戏信息
    self.miniGameInfo = {}
    --
    self.startGameTime = 0
    --实时监听用户点击界面响应
    if self.respondMouseTimer then
        util.RemoveDelayCall(self.respondMouseTimer)
        self.respondMouseTimer = nil
    end
    if self.respondTimer then
        util.RemoveDelayCall(self.respondTimer)
        self.respondTimer = nil
    end
    --当前小游戏配置
    self.curGameLevelCfg = nil
    self.miniGameName = nil
end

return GWMiniGameEntity