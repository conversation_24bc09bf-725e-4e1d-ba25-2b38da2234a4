local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type
local player_mgr = require "player_mgr"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local event_activity_define = require "event_activity_define"
local alliance_data = require "alliance_data"
local activity_pb = require "activity_pb"
local game_scheme = require "game_scheme"
local reward_mgr = require "reward_mgr"
local halloween_wingding_rank_mgr = require "halloween_wingding_rank_mgr"
local MainToggleType = halloween_wingding_rank_mgr.MainToggleType
local SubToggleType = halloween_wingding_rank_mgr.SubToggleType
--region Controller Life
module("ui_halloween_wingding_rank_controller")
local controller = nil
local UIController = newClass("ui_halloween_wingding_rank_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    self.subToggleType = data.subToggleType or SubToggleType.Rank
    self.mainToggleType = data.mainToggleType or MainToggleType.Personal
    self.allData = {}
    self.allData[MainToggleType.Personal] = {}
    self.allData[MainToggleType.Alliance] = {}
    self.activeID = data.activeID
    self:BuildRewardData()


    --排行请求
    local net_activity_module = require "net_activity_module"
    net_activity_module.MSG_COMM_ACTIVITY_RANK_REQ(activity_pb.ACTTYPE_PARTY_PERSON,self.activeID)
    net_activity_module.MSG_COMM_ACTIVITY_RANK_REQ(activity_pb.ACTTYPE_PARTY_ALLIANCE,self.activeID)
end

function UIController:OnShow()
    self.__base.OnShow(self)
    local festival_activity_mgr = require "festival_activity_mgr"
    local endTime = festival_activity_mgr.GetAcitivityEndStampByActivityID(self.activeID)
    if endTime > 0 then
        self:TriggerUIEvent("SetTopTime",endTime)
    end

    self:OpenPage(self.mainToggleType, self.subToggleType)
end

function UIController:OpenPage(mainToggleType,subToggleType)
    if mainToggleType == MainToggleType.Personal then
        self:OnTogPersonToggleValueChange(true)
    else
        self:OnTogAllianceToggleValueChange(true)
    end
    if subToggleType == SubToggleType.Rank then
        self:OnTogRankToggleValueChange(true)
    else
        self:OnTogRewardToggleValueChange(true)
    end
end

--初始化奖励数据
function UIController:BuildRewardData()
local gw_activity_ranking_data = require "gw_activity_ranking_data"
    for k, mainToggleType in pairs(MainToggleType) do
        if not self.allData[mainToggleType][SubToggleType.Reward] then
            self.allData[mainToggleType][SubToggleType.Reward] = {}
        end
        local rewardListData = gw_activity_ranking_data.GetRankingRewards(13,mainToggleType)
        self.allData[mainToggleType][SubToggleType.Reward].rewardList = rewardListData
        local rewardData = reward_mgr.GetRewardGoodsList2(rewardListData[1].rewardId)
        self.allData[mainToggleType][SubToggleType.Reward].firstRewardData = rewardData[1]
    end
end


function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
    self.onCommonActivityRankRsp = function(_,msg)
        if not msg then 
            return 
        end
        if self.allData then

        --监听倒排行版信息
            local rankType = MainToggleType.Personal
            if msg.actRankType == activity_pb.ACTTYPE_PARTY_PERSON then
                rankType = MainToggleType.Personal
            elseif msg.actRankType == activity_pb.ACTTYPE_PARTY_ALLIANCE then
                rankType = MainToggleType.Alliance
            end

            local data = {
                rankInfo = {},
                selfRank = {},
            }
            local userData = alliance_data.GetUserAllianceData()
            local selfRank = msg.selfRank
            for k,v in ipairs(msg.rankInfo) do
                table.insert(data.rankInfo,self:GetRankData(v,msg.actRankType))
                --同盟排行，服务器没有返回selfRank暂定客户端从rankInfo中获取,后续服务器修改
                if msg.actRankType == activity_pb.ACTTYPE_PARTY_ALLIANCE and userData and userData.allianceId == v.leagueid then
                    selfRank = v
                end
            end
            data.selfRank = self:GetRankData(selfRank,msg.actRankType)
            self.allData[rankType][SubToggleType.Rank] = data
            self.allData[rankType][SubToggleType.Reward].curReward = self:GetCurRankReward(data.selfRank,rankType)
            self:UpdatePage()
        end
    end
    self:RegisterEvent(event_activity_define.TMSG_COMM_ACTIVITY_RANK_RSP,self.onCommonActivityRankRsp)

    self.chooseEvent = function(...)
        self:ChooseEvent(...)
    end
end

function UIController:ChooseEvent(scroll_rect_item,index,dataItem)
    if dataItem.rankType == activity_pb.ACTTYPE_PARTY_PERSON then
        local mgr_personalInfo = require "mgr_personalInfo"
        mgr_personalInfo.ShowRoleInfoView(dataItem.dbid)
    elseif dataItem.rankType == activity_pb.ACTTYPE_PARTY_ALLIANCE then
        local data = {
            allianceId = dataItem.leagueid
        }
        ui_window_mgr:ShowModule("ui_alliance_detail", nil, nil, data)
    end
end

function UIController:GetCurRankReward(selfRank,rewardType) 
    if selfRank and self.allData[rewardType] and self.allData[rewardType][SubToggleType.Reward] then
        local  rewardList = self.allData[rewardType][SubToggleType.Reward].rewardList
        for k,v in pairs(rewardList or {}) do
            if selfRank.rank >= v.rankRange1 and selfRank.rank <= v.rankRange2 then
                return {rewardId = v.rewardId,rankRange1 = selfRank.rank,rankRange2=selfRank.rank}
            end
        end
    end
    return {}
end

function UIController:GetRankData(data,rankType) 
    local _data = {}
    if data then
        _data.rank = data.rank
        _data.faceStr = data.faceStr and data.faceStr or data.faceID 
        _data.frameID = data.frameID
        _data.score = data.score
        _data.rankType = rankType
        _data.chooseEvent = self.chooseEvent
        if rankType == activity_pb.ACTTYPE_PARTY_PERSON then
            
            local leagueShortName = ""
            if data.leagueShortName and data.leagueShortName ~="" then
                leagueShortName = string.format2("[{%s1}]",data.leagueShortName )
            end
            local roleName = data.name
            _data.dbid = data.dbid
            if data.dbid == player_mgr.GetPlayerRoleID() then
                _data.faceID = player_mgr.GetRoleFaceID()
                local custom_avatar_data = require "custom_avatar_data"
                local customHeadData = custom_avatar_data.GetMyAvatar()
                local faceStr = player_mgr.GetRoleFaceID() 
                if customHeadData then 
                    faceStr = customHeadData.remoteUrl
                end
                _data.faceStr = faceStr
                _data.frameID = player_mgr.GetPlayerFrameID()
                roleName = player_mgr.GetRoleName()
                local userData = alliance_data.GetUserAllianceData()
                local log = require "log"
                log.Error("userData",userData.shortName)
                if userData and userData.shortName ~="" then
                    leagueShortName = string.format2("[{%s1}]",userData.shortName )
                end
            end
            _data.name = leagueShortName..roleName
        end
        if rankType == activity_pb.ACTTYPE_PARTY_ALLIANCE then
            local leagueShortName = ""
            if data.leagueShortName and data.leagueShortName ~="" then
                leagueShortName = string.format2("[{%s1}]{%s2}",data.leagueShortName,data.leagueName)
            end
            _data.name = leagueShortName
            local cfg = game_scheme:LeagueIcon_0(data.leagueFlag)
            if cfg then
                _data.flagIconName = string.format("qizhi%s",cfg.iconID)
            end
            _data.leagueid = data.leagueid
        end
    end
    if not data or (data and data.rank == 0) then
        _data.noneTip = lang.Get(2420)
        if rankType == activity_pb.ACTTYPE_PARTY_ALLIANCE then
            local userData = alliance_data.GetUserAllianceData()
            if not userData then
                local log = require "log"
                log.Error("alliance", "userData is nil")
                _data.noneTip = lang.Get(1715)
            end
        end
    end

    return _data
end


function UIController:AutoUnsubscribeEvents() 
end
--endregion

function UIController:UpdatePage()
    self:TriggerUIEvent("UpdatePage",self.allData,self.mainToggleType,self.subToggleType)
end
--region Controller Logic
function  UIController:OnTogPersonToggleValueChange(state)
    if state then
        self.mainToggleType = MainToggleType.Personal
        self:UpdatePage()
    end
end
function  UIController:OnTogAllianceToggleValueChange(state)
    if state then
        self.mainToggleType = MainToggleType.Alliance
        self:UpdatePage()
    end
end
function  UIController:OnTogRankToggleValueChange(state)
    if state then
        self.subToggleType = SubToggleType.Rank
        self:UpdatePage()
    end
end
function  UIController:OnTogRewardToggleValueChange(state)
    if state then
        self.subToggleType = SubToggleType.Reward
        self:UpdatePage()
    end
end

function  UIController:OnBtnCloseBtnClickedProxy()
    ui_window_mgr:UnloadModule(self.view_name)
end


--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
