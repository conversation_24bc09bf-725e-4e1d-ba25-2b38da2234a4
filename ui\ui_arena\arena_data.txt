local print             = print
local player_mgr        = require "player_mgr"
local game_scheme       = require "game_scheme"
local bit               = require "bit"
local net_arena_module  = require "net_arena_module"
-- local net_topgame_module = require "net_topgame_module"
local event             = require "event"
local gw_sand_event_define = require "gw_sand_event_define"
local gw_event_activity_define             = require "gw_event_activity_define"
local sand_ui_event_define = require "sand_ui_event_define"
local PlayerPrefs       = CS.UnityEngine.PlayerPrefs
local tonumber          = tonumber
local string            = string
local table             = table
local require = require
local ipairs = ipairs
local pairs = pairs
local lang              = require "lang_util"
local lang2              = require "lang"
local common_new_pb     = require "common_new_pb"
local main_slg_const = require "main_slg_const"
local net_script = require "net_script"
local ReviewingUtil         = require "ReviewingUtil"

local click_liking_mgr = require "click_liking_mgr"
local net_click_liking = require "net_click_liking"
local role_pb = require "role_pb"
local data_personalInfo = require "data_personalInfo"
local util = require "util"
local click_liking_define = require "click_liking_define"
local UIUtil        = CS.Common_Util.UIUtil

module("arena_data")

isShowLevelRewardReddot = false
isShowVivtoryReddot = false
isGetTwoVictoryReward = false
SingleIntoBattleType = {
    None = 0 ,
    Challenge = 1,
    ChallengeReplay = 2,
    History = 3,
    Chat = 4
}

local challengeTimes = 0
local singleIntoBattleType = SingleIntoBattleType.None

function getSingleIntoBattleType()
    return singleIntoBattleType
end

function SetSingleIntoBattleType( type )
    singleIntoBattleType = type
end

--获取本地武器数据
function GetLocalizedWeaponData(type)
    local rt = {}
    local key = getKey(type).."weapon_"..player_mgr.GetPlayerRoleID()
    local localWeaponID = PlayerPrefs.GetString(key, "0#0#0#0#0")
    rt = string.split(localWeaponID, '#', tonumber)
    return rt
end

function SetLocalizedWeaponData(type, weapons)
    local tempWeapon = {}
    for i = 1, 5 do
        local temp = weapons[i] or 0
        table.insert(tempWeapon, temp)
    end
    local v = table.concat(tempWeapon, "#")
    local key = getKey(type).."weapon_"..player_mgr.GetPlayerRoleID()
    PlayerPrefs.SetString(key, v)
end

function getKey(type)
    return "advance_multi_lineups_"..type.."_"
end

function GetReddot(arenaType)
    local reddot = IsHaveLevelRewardByStageId(net_arena_module.highestStageId) or getVictoryRewardReddot()
    if arenaType then
        if arenaType == common_new_pb.CrystalCrown then
            reddot = reddot or net_arena_module.GetFreeTimes(common_new_pb.CrystalCrown) > 0
        elseif arenaType == common_new_pb.Advance then
            reddot = net_arena_module.GetFreeTimes(common_new_pb.Advance) > 0 and net_arena_module.GetMineRank(common_new_pb.Advance) ~= 1
        end
    else
        local topGame_data      = require "topGame_data"
        local legend_championships_mgr = require "legend_championships_mgr"
        reddot = reddot or net_arena_module.GetFreeTimes(common_new_pb.CrystalCrown) > 0 or (net_arena_module.GetFreeTimes(common_new_pb.Advance) > 0 and net_arena_module.GetMineRank(common_new_pb.Advance) ~= 1) or topGame_data.IsShowReddot() or legend_championships_mgr.GetEntranceTips()
        
        local peakGame_data = require "peakGame_data"
        if peakGame_data.IsOpen() then
            -- reddot判断加上 巅峰赛相关的红点逻辑
            local peakGameDot = peakGame_data.GetPeakGameReddot()
            reddot = reddot or peakGameDot
        end

        event.Trigger(event.SCENE_GAMEITEMREDDOT, 3, reddot)
    end

    return reddot
end

function GetLevelCfgByScore(_score)
    local curlevelCfg = nil
    for i = 1, game_scheme:ArenaLevel_nums() do
        curlevelCfg = game_scheme:ArenaLevel_0(i)
        if _score >= curlevelCfg.scoreSection.data[0] and _score <= curlevelCfg.scoreSection.data[1] then
            return curlevelCfg
        end
    end
    return curlevelCfg
end


function getVictoryRewardReddot()
    --[[local rewardSec = game_scheme:InitBattleProp_0(395).szParam.data[0] or 0
    local rewardMax = game_scheme:InitBattleProp_0(395).szParam.data[1] or 0
    if net_arena_module.victoryNum >= rewardSec then
        local val = bit.lshift(1, rewardSec)
        if bit.band(net_arena_module.victoryRewardFlag, val) == 0 then
            return true
        end
    end
    if net_arena_module.victoryNum == rewardMax then
        local val = bit.lshift(1, rewardMax)
        if bit.band(net_arena_module.victoryRewardFlag, val) == 0 then
            return true
        end
    end]]

    if net_arena_module.victoryRewardFlag and net_arena_module.victoryNum then
        local cfg = game_scheme:InitBattleProp_0(8171)

        for i = 1 , 3 do
            local val = bit.lshift(1, i)
            local isGet = bit.band(net_arena_module.victoryRewardFlag, val) ~= 0

            local needNum = cfg and cfg.szParam.data[i-1] or 999
            local canGet = net_arena_module.victoryNum >= needNum
            if canGet and not isGet then
                return true
            end
        end
    end
    return false
end

function IsHaveLevelReward(scores)
    local curLevel = GetLevelCfgByScore(scores)
    if not curLevel then return false end
    for i=1,curLevel.StageID do
        local curlevelCfg = game_scheme:ArenaLevel_0(i)
        if curlevelCfg and curlevelCfg.reward.data[0] ~= nil then
            local val = bit.lshift(1, curlevelCfg.StageID-1)
            if bit.band(net_arena_module.levelRewardInfo, val) == 0 then
                SetLevelRewardRewardReddot(true)
                return true
            end  
        end
    end
    SetLevelRewardRewardReddot(false)
    return false
end

function IsHaveLevelRewardByStageId(stageId)
    for i=1,stageId do
        local curlevelCfg = game_scheme:ArenaLevel_0(i)
        if curlevelCfg and curlevelCfg.reward.data[0] ~= nil then
            local val = bit.lshift(1, curlevelCfg.StageID-1)
            if bit.band(net_arena_module.levelRewardInfo, val) == 0 then
                SetLevelRewardRewardReddot(true)
                return true
            end
        end
    end
    SetLevelRewardRewardReddot(false)
    return false
end

function SetLevelRewardRewardReddot(state)
    isShowLevelRewardReddot = state
end

function UpdateArenaReddot()
    -- event.Trigger(event.SCENE_MATCHPLACEREDDOT, 3, isShowLevelRewardReddot or isGetHangupRewardReddot)
end

function GetLevelName(leveltype,level)
    local curlevelNameId = ""
    local curLevelType = 0
    local maxType = 0
    local count = 0
    for i = 1, game_scheme:ArenaLevel_nums() do
        local curlevelCfg = game_scheme:ArenaLevel_0(i)
        if leveltype == curlevelCfg.type then
            curLevelType = curlevelCfg.type
            curlevelNameId = curlevelCfg.nameID
            count = count + 1
        end
        if curlevelCfg.type ~= maxType then
            maxType = curlevelCfg.type
        end
    end
    level = count - level + 1
    if curLevelType ~= maxType then
        local space=""
        if lang2.USE_LANG==lang2.AR then
            space=" "
        end
        return lang.Get(curlevelNameId)..space..GetSign(level)
    else
        return lang.Get(curlevelNameId)
    end
end
function GetSign(level)
    local sign = nil
    if level == 9 then
        sign =  "IX"
    elseif level == 8 then
        sign =  "VIII"
    elseif level == 7 then
        sign =  "VII"
    elseif level == 6 then
        sign = "VI"
    elseif level == 5 then
        sign = "V"
    elseif level == 4 then
        sign = "IV "
    elseif level == 3 then
        sign = "III"
    elseif level == 2 then
        sign = "II"
    else
        sign = "I"
    end
    return sign
end

function SetChallengeTimes(times)
    challengeTimes = times
end

function GetChallengeTimes()
    return  challengeTimes
end

local ExtraRewardList = {}--日常竞技场/传奇竞标赛结算奖励（经验通行证奖励）
function Notify_BattleAddExtraItem(prarm)
    local data = {}
    data.id = prarm.itemIDmyranking_bg
    data.num = prarm.count
   
    local item_data = require "item_data"
    data.nType = item_data.Reward_Type_Enum.Item
    data.rewardid = nil
    if prarm.fightType then
        --print("prarm.fightType",prarm.fightType,prarm.itemID,prarm.count)
        if not ExtraRewardList[prarm.fightType] then 
            ExtraRewardList[prarm.fightType] = {}
        end
        --ExtraRewardList[prarm.fightType]=data
       table.insert(ExtraRewardList[prarm.fightType],data)
    end
   
end


function Notify_BattleAddExtraItem_DecorPass(prarm)
    local data = {}
    data.id = prarm.itemID
    data.num = prarm.itemAddCount
    local item_data = require "item_data"
    data.nType = item_data.Reward_Type_Enum.Item
    data.rewardid = nil
   if prarm.sourceType then
       --print("prarm.sourceType",prarm.sourceType,prarm.itemID,prarm.itemAddCount)
       if not ExtraRewardList[prarm.sourceType] then 
            ExtraRewardList[prarm.sourceType] = {}
       end
       table.insert(ExtraRewardList[prarm.sourceType],data)
    end
end

--策划说不要passport的表里的奖励了
--net_script.RegisterResponseLuaFuncNew("Notify_BattleAddExtraItem", Notify_BattleAddExtraItem)




function ResetExtraRewardList(fightType)
    ExtraRewardList[fightType] = nil
end

function GetExtraRewardList(fightType)
    return ExtraRewardList and ExtraRewardList[fightType]
end

--function AddExtraRewardListItem(param)
--    Notify_BattleAddExtraItem(param)
--end

function  ReportBattleResult( arenaType , victory)
    local scores = net_arena_module.GetArenaScores(arenaType)
    if arenaType ~= 1 then
        scores = net_arena_module.MineGradeId[arenaType] or 0
    end
    if victory == true then
        event.Trigger(event.GAME_EVENT_REPORT, "arena_win", "{\"arena_type\":"..arenaType..",\"match_point\":"..scores.."}")
    else
        event.Trigger(event.GAME_EVENT_REPORT, "arena_lost", "{\"arena_type\":"..arenaType..",\"match_point\":"..scores.."}")
    end
end

--region xman逻辑
--获取新手挑战赛是否解锁
function IsAreaNewOpen()
    local festival_activity_mgr = require "festival_activity_mgr"
    local festival_activity_cfg = require "festival_activity_cfg"
    local finishTime = net_arena_module.SyncTime and net_arena_module.SyncTime.crownTime and net_arena_module.SyncTime.crownTime  or -1
    return festival_activity_mgr.GetIsOpenByHeadingCode(festival_activity_cfg.ActivityCodeType.Arena_New, true) and finishTime > 0
end

--获取巅峰赛是否解锁
function IsAreaWeekendOpen()
    local festival_activity_mgr = require "festival_activity_mgr"
    local festival_activity_cfg = require "festival_activity_cfg"
    return festival_activity_mgr.GetIsOpenByHeadingCode(festival_activity_cfg.ActivityCodeType.Arena_Weekend, true)
end

--获取3v3是否解锁
function IsArea3v3Open()
    local festival_activity_mgr = require "festival_activity_mgr"
    local festival_activity_cfg = require "festival_activity_cfg"
    return festival_activity_mgr.GetIsOpenByHeadingCode(festival_activity_cfg.ActivityCodeType.Arena_3v3, true)
end

--判断巅峰赛是否有参赛资格
function IsAreaWeekendCanJoin()
    local weekend_arena_mgr = require "weekend_arena_mgr"
    return IsAreaWeekendOpen() and weekend_arena_mgr.GetCanChallenge()
end

--获取3v3是否有参赛资格
function IsArea3v3CanJoin()
    local legend_championships_mgr = require "legend_championships_mgr"
    return IsArea3v3Open() and legend_championships_mgr.GetCanChallenge()
end

--打开新手挑战赛界面
function OpenAreaNew(closeFunc, isReturnExperiment)
    --请求新手竞技场最新数据
    net_arena_module.Send_ARENA_GET_TIME()
    net_arena_module.Send_ARENA_ENTER(common_new_pb.CrystalCrown)
    
    --local isOpen = festival_activity_mgr.GetIsOpenByActivityID(106, true)
    local isOpen = IsAreaNewOpen()
    if not isOpen then
        local flow_text = require "flow_text"
        flow_text.Add(lang.Get(15649))
        return
    end
    --如果时间已经结束,则弹活动已关闭
    local finishTime = net_arena_module.SyncTime and net_arena_module.SyncTime.crownTime and net_arena_module.SyncTime.crownTime  or -1
    local isFinish = finishTime <= 0
    if isFinish then
        --活动已关闭
        local flow_text = require "flow_text"
        flow_text.Add(lang.Get(665033))
        return
    end
    
    if GetAreaIsCross(main_slg_const.MainExperimentType.Arena_New) then
        local flow_text = require "flow_text"
        flow_text.Add(lang.Get(7432))
        --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
        return
    end

    if isReturnExperiment then
        local main_slg_data = require "main_slg_data"
        main_slg_data.SetCurExperimentRecord(main_slg_const.MainExperimentType.Arena_New)
    else
        closeFunc = function()
            --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
        end
    end
    
    local ui_match_single_rank = require "ui_match_single_rank"
    ui_match_single_rank.SetCloseCallBack(function()
        if closeFunc then
            closeFunc()
        end
    end)
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:ShowModule("ui_match_single_rank")
    SetArenaIsFirstEnter(main_slg_const.MainExperimentType.Arena_New)
    --event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
end

--打开巅峰赛界面
function OpenAreaWeekend(closeFunc, isReturnExperiment)
    if GetAreaIsCross(main_slg_const.MainExperimentType.Arena_Weekend) then
        local flow_text = require "flow_text"
        flow_text.Add(lang.Get(7432))
        --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
        return
    end
    if isReturnExperiment then
        local main_slg_data = require "main_slg_data"
        main_slg_data.SetCurExperimentRecord(main_slg_const.MainExperimentType.Arena_Weekend)
    else
        closeFunc = function()
            --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
        end
    end
    local festival_activity_mgr = require "festival_activity_mgr"
    local festival_activity_cfg = require "festival_activity_cfg"
    local param = {}
    param.activityCenterBackBtnFun = closeFunc
    festival_activity_mgr.OpenActivityUIByActivityCodeType(festival_activity_cfg.ActivityCodeType.Arena_Weekend,param)
    SetArenaIsFirstEnter(main_slg_const.MainExperimentType.Arena_Weekend)
    --event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
end

--打开3v3界面
function OpenArea3v3(closeFunc, isReturnExperiment)
    if GetAreaIsCross(main_slg_const.MainExperimentType.Arena_3v3) then
        local flow_text = require "flow_text"
        flow_text.Add(lang.Get(7432))
        --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
        return
    end
    if isReturnExperiment then
        local main_slg_data = require "main_slg_data"
        main_slg_data.SetCurExperimentRecord(main_slg_const.MainExperimentType.Arena_3v3)
    else
        closeFunc = function()
            --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
        end
    end
    local festival_activity_mgr = require "festival_activity_mgr"
    local festival_activity_cfg = require "festival_activity_cfg"
    local param = {}
    param.activityCenterBackBtnFun = closeFunc
    festival_activity_mgr.OpenActivityUIByActivityCodeType(festival_activity_cfg.ActivityCodeType.Arena_3v3,param)
    SetArenaIsFirstEnter(main_slg_const.MainExperimentType.Arena_3v3)
    --event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
end

--通用打开竞技场判断（有新手竞技场优先进入，无新手竞技场进入巅峰赛）
function OpenArena()
    local openList = {
        [1] = {
            isOpenFun = IsAreaNewOpen,
            jumpFunc = OpenAreaNew,
        },
        [2] = {
            isOpenFun = IsAreaWeekendOpen,
            jumpFunc = OpenAreaWeekend,
        },
        [3] = {
            isOpenFun = IsArea3v3Open,
            jumpFunc = OpenArea3v3,
        },
    }
    for k, define in ipairs(openList) do
        if define.isOpenFun() then
            define.jumpFunc()
            return true
        end
    end
    return false
end

--获取竞技场是否跨服判断
function GetAreaIsCross(gameplayType)
    local gw_common_util = require "gw_common_util"
    --local cfg = game_scheme:DailyGameplay_1(gameplayType)
    local cfg = game_scheme:DailyGameplay_0(gameplayType)
    if cfg and cfg.DisplayConditions4 and cfg.DisplayConditions4 == 1 and gw_common_util.GetSandBaseCrossServiceState() then
        return true
    end
end


--region 竞技场主界面气泡
local arenaBubbleData = {}
local curEnterData = {}

--主界面竞技场气泡事件定义
local MainButtonBubbleDefine = {
    [main_slg_const.MainExperimentType.Arena_New] = {
        type = main_slg_const.MainExperimentType.Arena_New,
        getChallengeNum = function()
            return net_arena_module.GetChallengeNum()
        end,
        cfgID = 991,
        limitNum = 0,
        isOpen = function()
            return IsAreaNewOpen()
        end,
        showFunc = function()
            OpenAreaNew()
        end,
    },
    [main_slg_const.MainExperimentType.Arena_Weekend] = {
        type = main_slg_const.MainExperimentType.Arena_Weekend,
        getChallengeNum = function()
            local weekend_arena_mgr = require "weekend_arena_mgr"
            return weekend_arena_mgr.GetChallengeNum()
        end,
        cfgID = 992,
        limitNum = 0,
        isOpen = IsAreaWeekendCanJoin,
        showFunc = function()
            OpenAreaWeekend()
        end,
    },
    [main_slg_const.MainExperimentType.Arena_3v3] = {
        type = main_slg_const.MainExperimentType.Arena_3v3,
        getChallengeNum = function()
            local legend_championships_mgr = require "legend_championships_mgr"
            return legend_championships_mgr.GetChallengeNum()
        end,
        cfgID = 993,
        limitNum = 0,
        isOpen = IsArea3v3CanJoin,
        showFunc = function()
            OpenArea3v3()
        end,
    },
}

--主界面竞技场气泡显示顺序
local MainButtonBubbleShowFlow = {
    [1] = main_slg_const.MainExperimentType.Arena_New,
    [2] = main_slg_const.MainExperimentType.Arena_Weekend,
    [3] = main_slg_const.MainExperimentType.Arena_3v3,
}

--主界面竞技场气泡跳转方法
function JumpArenaByBubble()
    local gw_storm_mgr = require "gw_storm_mgr"
    for k, define in ipairs(arenaBubbleData) do
        if define.isOpen() and define.getChallengeNum() < define.limitNum and not GetAreaIsCross(define.type) and not gw_storm_mgr.GetIsStorm() then
            define.showFunc()
--[[            local sand_ui_event_define = require "sand_ui_event_define"
            --event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)]]
            break
        end
    end
end

--获取气泡不显示上限配置
function GetArenaBubbleLimit(data)
    if data.limitNum ~= 0 then
        data.isShow = data.getChallengeNum() < data.limitNum
    else
        local cfg = game_scheme:InitBattleProp_0(data.cfgID)
        if cfg then
            local num = cfg.szParam.data[0] or 0
            data.isShow = data.getChallengeNum() < num
            data.limitNum = num
        end
    end
    return data
end

--设置主界面竞技场气泡数据
function SetArenaBubbleData(_)
    local gw_storm_mgr = require "gw_storm_mgr"
    arenaBubbleData = {}
    for k, type in ipairs(MainButtonBubbleShowFlow) do
        local define = MainButtonBubbleDefine[type]
        if define then
            local isOpen = define.isOpen() and not GetAreaIsCross(define.type) and not gw_storm_mgr.GetIsStorm()
            if isOpen then
                define = GetArenaBubbleLimit(define)
                table.insert(arenaBubbleData, define)
            end 
        end
    end
    event.Trigger(event.REFRESH_ARENA_BUBBLE_SHOW)
end

event.Register(event.REFRESH_ARENA_CHALLENGE_NUM,SetArenaBubbleData)
event.Register(event.RECEIVE_WEEKEND_ARENA_ENEMY_LIST,SetArenaBubbleData)
event.Register(event.SERVER_CROSS_DAY,SetArenaBubbleData)
event.Register(gw_sand_event_define.GW_SAND_UPDATE_SELF_DATA,SetArenaBubbleData)

--获取主界面竞技场气泡数据方法
function GetArenaBubbleIsShow()
    local gw_storm_mgr = require "gw_storm_mgr"
    if ReviewingUtil.IsReviewing() then
        return false
    end
    if gw_storm_mgr.GetIsStorm() then
        return
    end
    local isShow = false
    for k, define in ipairs(arenaBubbleData) do
        if not isShow then
            isShow = define.getChallengeNum() < define.limitNum and define.isOpen() and not GetAreaIsCross(define.type)
        end
        if curEnterData[define.type] then
            return false
        end
    end
    return isShow
end
--endregion

--region 竞技场红点
--获取新手竞技场红点
function GetAreaNewRedPoint()
    local freeNum = net_arena_module.MineRemainFreeTimes[common_new_pb.CrystalCrown]
    if freeNum and freeNum > 0 then
        return true
    else
        local skep_mgr = require "skep_mgr"
        local areaNewTicketID = skep_mgr.GetConstGoodsSidByID(skep_mgr.const_id.arenaTicket)
        local entity = player_mgr.GetPacketPartDataBySid(areaNewTicketID)
        if entity and entity.numProp.goodsNum > 0 then
            return true
        else
            return false
        end
    end
end

local ARENA_NEW_RED = "ARENA_NEW_RED_"
local enterNewList = {}
local buttonGroupType = main_slg_const.MainButtonGroupType.bottom
local mainButtonType = main_slg_const.MainButtonType.trial
local enterList = {}
local enterActivityIdList = {}

--登录完成请求竞技场数据
function ArenaDataReq()
    local arena_data = require"arena_data"
    local isOpen = arena_data.IsAreaWeekendOpen()
    --请求巅峰竞技场
    if isOpen then
        local arena_pb = require"arena_pb"
        net_arena_module.MSG_WEEKEND_ARENA_QUALIFICATE_REQ(arena_pb.ArenaQualificateType_WeekendArena)
        local weekend_arena_mgr = require "weekend_arena_mgr"
        weekend_arena_mgr.MSG_WEEKEND_ARENA_RANK_INFO_REQ()
    end
    --请求3v3竞技场
    isOpen = arena_data.IsArea3v3Open()
    if isOpen then
        local arena_pb = require"arena_pb"
        net_arena_module.MSG_WEEKEND_ARENA_QUALIFICATE_REQ(arena_pb.ArenaQualificateType_Arena3v3)
    end
    --请求新手竞技场时间
    net_arena_module.Send_ARENA_GET_TIME()
    SetEnterList()
    event.Trigger(event.TRIAL_BUTTON_RED_CHANGE, {buttonGroupType = buttonGroupType, mainButtonType = mainButtonType})
end

event.Register(event.FIRST_LOGIN_CREATE_DATA_FINISH,ArenaDataReq)

--活动事件刷新
function RefreshEnterList(_,enterActivityId, isOpen)
    if isOpen then
        if enterActivityIdList[enterActivityId] then
            SetEnterList()
            local red_system = require "red_system"
            local red_const = require "red_const"
            --red_system.TriggerRed(red_const.Enum.CampTrial_Main_Icon)
            red_system.TriggerRed(red_const.Enum.ExperienceItem_New)
            local arena_pb = require"arena_pb"
            net_arena_module.MSG_WEEKEND_ARENA_QUALIFICATE_REQ(arena_pb.ArenaQualificateType_WeekendArena)
            net_arena_module.MSG_WEEKEND_ARENA_QUALIFICATE_REQ(arena_pb.ArenaQualificateType_Arena3v3)
            red_system.TriggerRed(red_const.Enum.Arena3v3_ChallengeTip)
            red_system.TriggerRed(red_const.Enum.ArenaWeekend_ChallengeTip)
            RefreshMainButtonRedTye()
            SetArenaBubbleData()
        end
    end
end
event.Register(gw_event_activity_define.GW_ONE_ACTIVITY_UPDATE,RefreshEnterList)

function RefreshMainButtonRedTye()
    event.Trigger(event.TRIAL_BUTTON_RED_CHANGE, {buttonGroupType = buttonGroupType, mainButtonType = mainButtonType})
end


--获取试炼解锁红点
function GetArenaNewRed(id)
    local isNew = false
    local player_prefs = require "player_prefs"
    for k,arenaType in ipairs(enterList) do
        if arenaType then
            if id then
                --查找单个解锁红点
                if id == arenaType then
                    if enterNewList[arenaType] == nil or enterNewList[arenaType] == true then
                        local str = string.format("%s%d", ARENA_NEW_RED, arenaType)
                        isNew = player_prefs.GetCacheData(str, true)
                        enterNewList[arenaType] = isNew
                        return enterNewList[arenaType]
                    end
                end
            else
                if enterNewList[arenaType] == nil or enterNewList[arenaType] == true then
                    local str = string.format("%s%d", ARENA_NEW_RED, arenaType)
                    isNew = player_prefs.GetCacheData(str, true)
                    enterNewList[arenaType] = isNew
                end
                if enterNewList[arenaType] then
                    break
                end
            end
        end
    end
    return isNew
end

--获取试炼解锁列表
function SetEnterList()
    enterList = {}
    enterActivityIdList = {}
--[[    if #enterActivityIdList > 0 then
        isSetActivity = false
    end]]
    local festival_activity_mgr = require "festival_activity_mgr"
    local num = game_scheme:DailyGameplay_nums()
    for i = 0 ,num-1 do
        local cfg = game_scheme:DailyGameplay(i)
        local isUnlock = false
        if cfg then
            if  cfg.UnlockConditions ~= 0 then
                local function_open = require "function_open"
                isUnlock = function_open.CheckFunctionIsOpen(cfg.UnlockConditions)
            else
                if cfg.UnlockConditions2 ~= 0 then
                    local activityId = cfg.UnlockConditions2
                    enterActivityIdList[activityId] = true
                    isUnlock = festival_activity_mgr.GetIsOpenByActivityID(activityId,true)
                end
            end
            if isUnlock then
                table.insert(enterList, cfg.gameplayType)
            end
        end
    end
end

--设置试炼解锁红点
function SetArenaIsFirstEnter(arenaType)
    if enterNewList[arenaType] == nil or enterNewList[arenaType] == true then
        local player_prefs = require "player_prefs"
        local red_system = require "red_system"
        local red_const = require "red_const"
        local str = string.format("%s%d", ARENA_NEW_RED, arenaType)
        player_prefs.SetCacheData(str, false)
        enterNewList[arenaType] = false
        event.Trigger(event.TRIAL_BUTTON_RED_CHANGE, {buttonGroupType = buttonGroupType, mainButtonType = mainButtonType})
        --red_system.TriggerRed(red_const.Enum.CampTrial_Main_Icon)
        red_system.TriggerRed(red_const.Enum.ExperienceItem_New)
    end
    if not curEnterData[arenaType] then
        curEnterData[arenaType] = true
        event.Trigger(event.REFRESH_ARENA_BUBBLE_SHOW)
    end
end
--endregion

--endregion

----@see 获取机器人的本地化配置id,换算通过角色id换算
---@param roleID number 角色id
---@return boolean 是否是机器人 isRobot
---@return string 机器人本地化Name
function GetArenaRobotLangName(roleID)
    local log = require "log"
    local cfg = game_scheme:InitBattleProp_0(220).szParam.data
    local isRobot = false
    if cfg then
        local robotStartID = cfg[0]
        if  roleID > robotStartID then
            isRobot = true
        --获取对应的排名
            local rank = roleID - robotStartID
            local num = game_scheme:ArenaRobot_nums()
            for i = 0 ,num-1 do
                local cfg = game_scheme:ArenaRobot(i)
                local startRank = cfg.NPCRanking.data[0]
                local endRank = cfg.NPCRanking.data[1]
                if startRank <= rank and endRank >= rank  then
                    return isRobot,cfg.NPCName
                end

            end
        end
    end
    return isRobot,nil
end

--------------------------- 竞技场点赞相关接口 start --------------------
--初始化时获取前三名点赞数量
function SendLikeDataReq(allRankInfo,arenaType) 
    local specialPlayerGroup = click_liking_mgr.GetTopPlayerRoleID(3,allRankInfo)--获取前三名的玩家ID
    net_click_liking.MSG_GET_SP_LIKENUM_REQ(arenaType,specialPlayerGroup)
end

-- 红点监听事件
function RefreshRedDot(arenaType,ScrollRectItem,clickType) 
    local curPlayCanLikeNum = data_personalInfo.GetPersonalInfoValue(arenaType)
    local LikeMaxCount = click_liking_mgr.GetArenaMaxLikeCount(clickType)
    for i = 1,3 do 
        if not util.IsObjNull(ScrollRectItem) then 
            local RedDot = ScrollRectItem:Get("redDot")--点赞红点

            if not util.IsObjNull(RedDot) and curPlayCanLikeNum and curPlayCanLikeNum < LikeMaxCount and i == 1 then 
                UIUtil.SetActive(RedDot,true)
            else
                UIUtil.SetActive(RedDot,false)
            end
        end
    end
end

--点赞数量刷新函数
function RefreshLikeNum(arenaType,ScrollRectItem,topThreeData,likeData) 
    for i=1,3 do 
        if not util.IsObjNull(ScrollRectItem) then  
            local LikeNum = ScrollRectItem:Get("LikeNum")--点赞数量
            local value = topThreeData and topThreeData[i] --玩家数据
            if likeData and likeData[value.roleID] then 
                if not util.IsObjNull(LikeNum) then
                    LikeNum.text = likeData[value.roleID]
                end
            else
                if not util.IsObjNull(LikeNum) then
                    LikeNum.text = 0
                end
            end
        end
    end
end

--点赞按钮点击方法
function ClickBtnEventFunc(clickType,defineType,playerId,arenaType) 
    local canclickTimes = data_personalInfo.GetPersonalInfoValue(clickType)
    local LikeMaxCount = click_liking_mgr.GetArenaMaxLikeCount(defineType)
    if canclickTimes and canclickTimes >= LikeMaxCount then 
        local flow_text = require "flow_text"
        flow_text.Add(string.format2(lang.Get(1007309),LikeMaxCount))
        return
    end
    local net_click_liking = require "net_click_liking"
    net_click_liking.MSG_ZONE_ROLE_PRAISE_UPDATE_REQ(playerId,arenaType)
end
--------------------------- 竞技场点赞相关接口 end --------------------

function OnSceneDestroy()
    challengeTimes = 0
    ExtraRewardList = {}
    singleIntoBattleType = SingleIntoBattleType.None
    enterNewList = {}
    enterList = {}
    arenaBubbleData = {}
    curEnterData = {}
    enterActivityIdList = {}
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, OnSceneDestroy)