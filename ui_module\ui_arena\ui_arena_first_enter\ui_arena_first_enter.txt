local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local face_item             = require "face_item_new"
local binding = require "ui_arena_first_enter_binding"

--region View Life
module("ui_arena_first_enter")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.faceItem then
        self.faceItem:Dispose()
        self.faceItem = nil
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:InitShow(data)
    if not data then
        return
    end
    
    local isTop = data.curRank <= 3
    self:SetActive(self.ss_rankTopBg, isTop)
    self:SetActive(self.rtf_otherRankShow, not isTop)
    if isTop then
        --前三名显示
        self.ss_rankTopBg:Switch(data.curRank-1)
        self.txt_playerNameTop.text = data.playerName
        self.faceItem = self.faceItem or face_item.CFaceItem():Init(self.rtf_faceTra, nil, 1.2)
        self.faceItem:SetFaceInfo(data.faceStr)
        self.faceItem:SetNewBg(true)
        self.faceItem:SetFrameID(data.frameID, true)
        self.faceItem:FrameEffectEnable(true, self.curOrder+1)
    else
        --前三往后名次
        self.txt_playerName.text = data.playerName
        self.txt_curRankNum.text = data.curRank
    end
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
