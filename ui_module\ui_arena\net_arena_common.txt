
local require   = require
local print     = print
local ipairs    = ipairs
local pairs     = pairs
local table     = table
local string    = string
local tonumber  = tonumber
local lua_pb = require "lua_pb"
local flow_text = require "flow_text"
local lang = require "lang"
local event         = require "event"
local xManMsg_pb        = require "xManMsg_pb"
local newarena_pb       = require "newarena_pb"
local net           = require "net"
local net_route     = require "net_route"
local event_arena_common_define = require("event_arena_common_define")
module("net_arena_common")

-- 新版竞技场设置防守阵容
function MSG_NEW_ARENA_SET_DEFENCE_LINEUP_REQ(data)
    -- nArenaID
    -- arrLineup
    local gw_home_drone_data = require "gw_home_drone_data"
    local msg = newarena_pb.TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_REQ()
    msg.nArenaID = data.nArenaID or 1

    --设置战斗阵容
    for i, v in ipairs(data.lineUp) do
        local msgLineup = msg.arrLineup:add()--特殊的数据的处理方式
        local pals = v.palList
        if not pals then
            pals = v.pals
        end
        if pals then
            for _i, _v in pairs(pals) do
                local pal = msgLineup.palList:add()
                pal.palId = _v.palId or _v.heroSid
                pal.row = _v.row
                pal.col = _v.col
            end
        end
        msgLineup.weaponId = v.weaponId or gw_home_drone_data.GetDroneId()

    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_NEW_ARENA_SET_DEFENCE_LINEUP_REQ, msg)
end

-- 新版竞技场设置防守阵容
function MSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP(msg)
    -- nArenaID
    -- nErrCode
    if msg.errorCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorCode))
        return
    end
    event.Trigger(event_arena_common_define.TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP,msg)
end

-- 请求获取防守阵容
function MSG_NEW_ARENA_GET_DEFENCE_LINEUP_REQ(data)
    -- nArenaID
    -- nTargetRoleID
    local msg = newarena_pb.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_REQ()
    msg.nArenaID = data.nArenaID or 0
    msg.nTargetRoleID = data.nTargetRoleID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_NEW_ARENA_GET_DEFENCE_LINEUP_REQ, msg)
end

-- 请求获取防守阵容回复
function MSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP(msg)
    -- nArenaID
    -- nTargetRoleID
    -- arrTroopData
    -- nErrCode
    if msg.errorCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorCode))
        return
    end
    event.Trigger(event_arena_common_define.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP,msg)
end

-- 获取排行榜信息
function MSG_NEW_ARENA_GET_RANK_INFO_REQ(data)
    -- nArenaID
    -- nPageID
    -- nArenaType
    local msg = newarena_pb.TMSG_NEW_ARENA_GET_RANK_INFO_REQ()
    msg.nArenaID = data.nArenaID or 0
    msg.nPageID = data.nPageID
    msg.nArenaType = data.nArenaType
    net.TransmitLuaFuncReqNew(xManMsg_pb.MSG_NEW_ARENA_GET_RANK_INFO_REQ, msg, lua_pb.MicroService_Arena, nil, nil, nil)
end

-- 获取排行榜信息回复
function MSG_NEW_ARENA_GET_RANK_INFO_RSP(msg)
    -- nArenaID
    -- nErrCode
    -- nCurRank
    -- lsTopNRoleArenaRankInfo
    -- lsRoleArenaRankInfo
    -- nPageID
    -- nChallengedNum
    -- nTotalChallengedNum
    -- nLastRank
    -- nActivityStartTime
    -- nActivityEndTime
    -- bGetTopRankList
    -- nUpgradeArenaID
    if msg.errorCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorCode))
        return
    end
    local arena_common_mgr     = require "arena_common_mgr"
    arena_common_mgr.SetArenaData(msg)
    event.Trigger(event_arena_common_define.TMSG_NEW_ARENA_GET_RANK_INFO_RSP,msg)
end

-- 刷新可挑战列表请求
function MSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_REQ(data)
    -- nArenaID
    -- bRefresh
    local msg = newarena_pb.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_REQ()
    msg.nArenaID = data.nArenaID or 0
    msg.bRefresh = data.bRefresh
    net.TransmitLuaFuncReqNew(xManMsg_pb.MSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_REQ, msg, lua_pb.MicroService_Arena, nil, nil, nil)
end

-- 刷新可挑战列表请求
function MSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP(msg)
    -- nArenaID
    -- bRefresh
    -- nErrCode
    -- arrChallengeInfo
    -- nRefreshedNum
    if msg.errorCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorCode))
        return
    end
    event.Trigger(event_arena_common_define.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP,msg)
end

-- 竞技场战斗请求
function MSG_NEW_ARENA_ENTER_BATTLE_REQ(data)
    -- nArenaID
    -- nRivalRoleID
    -- arrLineUp
    local msg = newarena_pb.TMSG_NEW_ARENA_ENTER_BATTLE_REQ()
    msg.nArenaID = data.nArenaID or 1
    msg.nRivalRoleID = data.nRivalRoleID or 0
    
    --设置战斗阵容
    for i, v in ipairs(data.lineUp) do
        local msgLineup = msg.arrLineUp:add()--特殊的数据的处理方式
        local pals = v.palList
        if not pals then
            pals = v.pals
        end
        if pals then
            for _i, _v in pairs(pals) do
                local pal = msgLineup.palList:add()
                pal.palId = _v.palId or _v.heroSid
                pal.row = _v.row
                pal.col = _v.col
            end
        end
        msgLineup.weaponId = v.weaponId or 0
        
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_NEW_ARENA_ENTER_BATTLE_REQ, msg)
end

-- 竞技场战斗回复
function MSG_NEW_ARENA_ENTER_BATTLE_RSP(msg)
    -- nArenaID
    -- nRivalRoleID
    -- nOldScore
    -- nNewScore
    -- nRivalOldScore
    -- nRivalNewScore
    -- nOldRank
    -- nNewRank
    if msg.errorCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorCode))
        return
    end
    local arena_common_mgr     = require "arena_common_mgr"
    arena_common_mgr.SetBattleInfoByRsp(msg)
    event.Trigger(event_arena_common_define.TMSG_NEW_ARENA_ENTER_BATTLE_RSP,msg)
end

-- 拉取战斗回放报告，里面会带有录像的信息的
function MSG_NEW_ARENA_GET_BATTLE_RECORDS_REQ(data)
    -- nArenaID
    -- nPageID
    local msg = newarena_pb.TMSG_NEW_ARENA_GET_BATTLE_RECORDS_REQ()
    msg.nArenaID = data.nArenaID or 1
    msg.nPageID = data.nPageID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_NEW_ARENA_GET_BATTLE_RECORDS_REQ, msg)
end

-- 拉取战斗回放报告，里面会带有录像的信息的
function MSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP(msg)
    -- nArenaID
    -- nPageID
    -- arrBattleReportInfo
    event.Trigger(event_arena_common_define.TMSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP,msg)
end

-- 新版竞技场确定切换竞技场类型
function MSG_NEW_ARENA_CHANGE_ARENA_REQ(data)
    -- nOldArenaID
    -- nNewArenaID
    local msg = newarena_pb.TMSG_NEW_ARENA_CHANGE_ARENA_REQ()
    msg.nOldArenaID = data.nOldArenaID or 0
    msg.nNewArenaID = data.nNewArenaID or 0
    net.TransmitLuaFuncReqNew(xManMsg_pb.MSG_NEW_ARENA_CHANGE_ARENA_REQ, msg, lua_pb.MicroService_Arena, nil, nil, nil)
end

-- 新版竞技场确定切换竞技场类型
function MSG_NEW_ARENA_CHANGE_ARENA_RSP(msg)
    -- nOldArenaID
    -- nNewArenaID
    -- nErrCode
    -- nRefreshedNum
    -- nDayBattleNum
    if msg.errorCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorCode))
        return
    end
    event.Trigger(event_arena_common_define.TMSG_NEW_ARENA_CHANGE_ARENA_RSP,msg)
end

-- 新版竞技场玩家进入竞技场通知
--通知能够切换竞技场
function MSG_NEW_ARENA_ENTER_ARENA_NTF(msg)
    -- nArenaType
    local arena_common_mgr = require "arena_common_mgr"
    arena_common_mgr.SetArenaSwitchData(msg.nNewArenaID, msg.nOldArenaID)
    event.Trigger(event_arena_common_define.TMSG_NEW_ARENA_ENTER_ARENA_NTF,msg)
end

-- 新版竞技场玩家积分排行榜变化通知
function MSG_NEW_ARENA_RANK_UPDATE_NTF(msg)
    -- nArenaID
    -- nOldRank
    -- nNewRank
    event.Trigger(event_arena_common_define.TMSG_NEW_ARENA_RANK_UPDATE_NTF,msg)
end

local MessageTable = {
    {xManMsg_pb.MSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP, MSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP, newarena_pb.TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP},
    {xManMsg_pb.MSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP, MSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP, newarena_pb.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP},
    {xManMsg_pb.MSG_NEW_ARENA_GET_RANK_INFO_RSP, MSG_NEW_ARENA_GET_RANK_INFO_RSP, newarena_pb.TMSG_NEW_ARENA_GET_RANK_INFO_RSP},
    {xManMsg_pb.MSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP, MSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP, newarena_pb.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP},
    {xManMsg_pb.MSG_NEW_ARENA_ENTER_BATTLE_RSP, MSG_NEW_ARENA_ENTER_BATTLE_RSP, newarena_pb.TMSG_NEW_ARENA_ENTER_BATTLE_RSP},
    {xManMsg_pb.MSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP, MSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP, newarena_pb.TMSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP},
    {xManMsg_pb.MSG_NEW_ARENA_CHANGE_ARENA_RSP, MSG_NEW_ARENA_CHANGE_ARENA_RSP, newarena_pb.TMSG_NEW_ARENA_CHANGE_ARENA_RSP},
    {xManMsg_pb.MSG_NEW_ARENA_ENTER_ARENA_NTF, MSG_NEW_ARENA_ENTER_ARENA_NTF, newarena_pb.TMSG_NEW_ARENA_ENTER_ARENA_NTF},
    {xManMsg_pb.MSG_NEW_ARENA_RANK_UPDATE_NTF, MSG_NEW_ARENA_RANK_UPDATE_NTF, newarena_pb.TMSG_NEW_ARENA_RANK_UPDATE_NTF},
}
net_route.RegisterMsgHandlers(MessageTable)