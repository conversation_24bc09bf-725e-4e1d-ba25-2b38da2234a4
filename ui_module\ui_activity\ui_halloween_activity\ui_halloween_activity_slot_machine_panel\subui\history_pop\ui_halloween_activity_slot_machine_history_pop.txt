local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type


local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_halloween_activity_slot_machine_history_pop_binding"

local GameObject = CS.UnityEngine.GameObject

--region View Life
module("ui_halloween_activity_slot_machine_history_pop")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}


end

function UIView:OnShow()
    self.__base.OnShow(self)


end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic


function UIView:RenderList(act_id, data_list)
    --清理rtf_content下的所有子物体
    local rtf_content = self.rtf_content
    for i = 0, rtf_content.childCount - 1 do
        local child = rtf_content:GetChild(i)
        if child then
            GameObject.Destroy(child.gameObject)
        end
    end

    local draw_result_day_group_item = require "draw_result_day_group_item"

    self.draw_result_day_group_item_list = {}
    local go_prefab = self.item_history_group_item
    for i, v in ipairs(data_list) do 
        local go = GameObject.Instantiate(go_prefab)
        go.name = "item_history_group_item_"..i
        go.transform:SetParent(rtf_content, false)
        
        draw_result_day_group_item.Render(go, act_id , v)
        self.draw_result_day_group_item_list[i] = go
    end

    util.DelayCallOnce(0.05, function ()
        draw_result_day_group_item.Extend(self.draw_result_day_group_item_list[1], true)
    end)
    
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
