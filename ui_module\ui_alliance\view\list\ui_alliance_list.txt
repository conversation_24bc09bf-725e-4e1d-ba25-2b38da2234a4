--@region FileHead
-- ui_alliance_list.txt ---------------------------------
-- author:  马睿
-- date:    2024/6/24 9:15:46
-- ver:     1.0
-- desc:    Description
-------------------------------------------------
--@endregion 

--@region Require
local require = require
local type = type
local string = string

local GameObject = CS.UnityEngine.GameObject
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local Common_Util = CS.Common_Util.UIUtil
local game_scheme = require "game_scheme"
local alliance_mgr_extend = require "alliance_mgr_extend"
local alliance_ui_util = require "alliance_ui_util"
local alliance_data = require "alliance_data"
local alliance_mgr = require "alliance_mgr"
local util = require "util"
local enum_define = require "enum_define"
local class = require "class"
local ui_base = require "ui_base"
local module_scroll_list = require "scroll_list"
local e_handler_mgr = require "e_handler_mgr"
local RectTransform = CS.UnityEngine.RectTransform
local Toggle = CS.UnityEngine.UI.Toggle
local windowMgr = require "ui_window_mgr"
local player_Mgr = require "player_mgr"
local alliance_pb = require "alliance_pb"
local lang = require "lang"
local card_sprite_asset = require "card_sprite_asset"
local sprite_asset = nil
local spriteAsset = nil
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
--local GameObject 	= CS.UnityEngine.GameObject
--@endregion 

--@region ModuleDeclare
module("ui_alliance_list")
local ui_path = "ui/prefabs/gw/alliancesystem/uialliancelist.prefab"
local window = nil
local UIAllianceList = {}

--@endregion 

--@region WidgetTable
UIAllianceList.widget_table = {

    Btn_return = { path = "btn_ReturnClose", type = "Button", event_name = "OnBtn_returnClickedProxy" },
    Btn_refresh = { path = "topArea/Auto_refresh", type = "Button", event_name = "OnBtn_refreshClickedProxy" },
    Btn_btnJoin = { path = "bottomUI/Auto_btnJoin", type = "Button", event_name = "OnBtn_btnJoinClickedProxy" },
    Btn_btnRank = { path = "bottomUI/Auto_btnRank", type = "Button", event_name = "OnBtn_btnRankClickedProxy" },
    --联盟列表组件
    rect_table = { path = "AllianceList/Viewport/Content", type = ScrollRectTable },
    --语言列表组件
    --@region User0
    --加入分页toggle
    Toggle_Join = { path = "ToggleGroup/Join_Toggle", type = "Toggle", value_changed_event = "OnToggle_JoinAllianceValueChanged" },
    Toggle_Create = { path = "ToggleGroup/Create_Toggle", type = "Toggle", value_changed_event = "OnToggle_AllianceCreateValueChanged" },
    Toggle_Invite = { path = "ToggleGroup/Invite_Toggle", type = "Toggle", value_changed_event = "OnToggle_InviteAllianceValueChanged" },
    --搜索按钮
    Btn_SearchBtn = { path = "topArea/searchBtn", type = "Button", event_name = "OnBtn_SearchClicked" },

    --创建界面
    --替换旗帜按钮
    Btn_replace = { path = "createAlliance/Auto_replaceBtn", type = "Button", event_name = "OnBtn_replaceClicked" },
    --建立按钮
    Btn_setup = { path = "createAlliance/Auto_setupBtn", type = "Button", event_name = "OnBtn_setupClicked" },
    Sprite_setUp = { path = "createAlliance/Auto_setupBtn", type = SpriteSwitcher },

    --建立按钮图片
    --Img_setup = { path = "createAlliance/Auto_setupBtn", type = "Image"},

    --随机简称按钮
    Btn_random = { path = "createAlliance/randomButton", type = "Button", event_name = "OnBtn_randomClicked" },
    --选择语言按钮
    Btn_choseLanguage = { path = "createAlliance/Group/downMenu/Auto_downMenu", type = "Button", event_name = "OnBtn_choseLanguageClicked" },
    -- 选择国旗
    Btn_chooseFlag = { path = "createAlliance/Group/NationalFlagBtn/Auto_downMenu_NationalFlag", type = "Button", event_name = "OnBtn_chooseFlagClicked" },
    --输入搜索
    InputField_Search = { path = "topArea/InputName", type = "InputField",
        --value_changed_event = "OnInputField_SearchFieldValueChanged",
                          end_edit_event = "OnInputField_SearchFieldEndEdit",
    },
    --输入联盟名字
    InputField_Name = { path = "createAlliance/Auto_InputName", type = "InputField",
        --value_changed_event="OnInputField_searchFieldValueChanged",
                        end_edit_event = "OnInputField_NameFieldEndEdit" },
    --输入简称
    InputField_Abb = { path = "createAlliance/Auto_InputAbb", type = "InputField",
                       end_edit_event = "OnInputField_AbbFieldEndEdit" },

    --region 创建联盟面板
    --创建联盟面板
    createAlliance = { path = "createAlliance", type = RectTransform },

    --字符数量
    charNum = { path = "createAlliance/charNum", type = "Text" },
    --名字是否可用
    Sprite_nameTrue = { path = "createAlliance/nameEnable/rightImg", type = SpriteSwitcher },

    --简称是否可用
    Sprite_abbTrue = { path = "createAlliance/abbEnable/rightImg", type = SpriteSwitcher },

    --名字提示
    nameErrorText = { path = "createAlliance/nameEnable/nameTipText", type = "Text" },
    abbErrorText = { path = "createAlliance/abbEnable/abbTipText", type = "Text" },

    --快速加入倒计时
    countDownText = { path = "bottomUI/Auto_btnJoin/Text", type = "Text" },
    --建立消耗资源数量
    setUpSpend_Text = { path = "createAlliance/setupSpendText", type = "Text" },
    --建立消耗资源图标
    setUpSpend_Icon = { path = "createAlliance/setupSpendText/setUpSpendIcon", type = "Image" },

    --联盟创建界面的旗帜图标
    Img_AllianceFlag = { path = "createAlliance/Auto_flagIcon", type = "Image" },
    --联盟当前语言
    Text_CurrentLanguage = { path = "createAlliance/Group/Auto_Dropdown/Auto_langText", type = "Text" },
    --联盟当前国旗
    Img_CurrentNationalFlag = { path = "createAlliance/Group/NationalFlag/Image", type = "Image" },
    
    --endregion

    --region 邀请玩家面板
    --邀请玩家面板
    invitePanel = { path = "invitePanel", type = RectTransform },

    ScrollTable_invite = { path = "invitePanel/inviteList/Viewport/Content", type = ScrollRectTable },

    --endregion

    Trans_NoAlliance = { path = "NoAlliance", type = "RectTransform" },
    --@endregion
}
--@endregion 



--@function 设置View-Controller模式的UI
--@ return type  ---- 未定义/VC/纯V   
--@ 注意，View-Controller模式的ui必须要重写这个接口
function UIAllianceList:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

--@endregion 


--@region WindowInit

--[[窗口初始化]]

--@region WindowInit
--[[窗口初始化]]
function UIAllianceList:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)

    --@region User
    self:InitUI()
    sprite_asset = sprite_asset or card_sprite_asset.CreateLeagueAsset()
    spriteAsset = spriteAsset or card_sprite_asset.CreateSpriteAsset()
    --@endregion 

    local red_const = require "red_const"
    self:BindUIRed(self.Toggle_Invite.transform, red_const.Enum.AllianceInvite,nil,{pos ={ x = 24 ,y = 42 }})
    
end --///<<< function

--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIAllianceList:OnShow()

end --///<<< function

--@endregion 

--@region WindowOnHide
--[[界面隐藏时调用]]
function UIAllianceList:OnHide()
    --@region User
    --@endregion 
end --///<<< function

--@endregion 


--@region WindowClose
function UIAllianceList:Close()
    self.__base:Close()
    window = nil

    --滑动列表的ItemsDispose
    if self.rect_table then
        self.rect_table:ItemsDispose()
    end
    
    if self.ScrollTable_invite then
        self.ScrollTable_invite:ItemsDispose()
    end
    
    if self.sprite_asset then
        self.sprite_asset:Dispose()
        self.sprite_asset = nil
    end
    if spriteAsset then
        spriteAsset:Dispose()
        spriteAsset = nil
    end
    --region User
    --@region User
    --@endregion 
end --///<<< function

--@endregion 

---********************功能函数区**********---

--界面初始化
function UIAllianceList:InitUI()
    self:InitScrollRectTable()

    self:NameIsEnabled(true)
    self:AbbIsEnabled(true)
    self:NameIsEnabled(false)
    self:AbbIsEnabled(false)
    --self:ToggleColor(true)
    player_Mgr.GetPlayerEntity()
    --self:SetActive(self.createAlliance, false)
    self:SwitchPage(0)
    self:RefreshNationalFlag()
end

--初始化联盟列表
function UIAllianceList:InitScrollRectTable()
    --如果需要可以设置动画
    --local scrollAnim = require "scroll_rect_anim"
    --scrollAnim.SetAnimCfg(self.UIModuleName, self.rect_table, OnItemRender, nil, 0.1, 0.2, true, 0, true, 3, true, 0.9)
    --self.rect_table.onItemRender = scrollAnim.OnRenderItem

    self.rect_table.onItemRender = function(...)
        self:OnItemRender(...)
    end 
    --一定在UI 销毁时处理rect_table的Dispose  eg： self.rect_table:ItemsDispose()
    self.rect_table.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            --rect_table Dispose时 Item上相关的资源是否需要Dispose           
        end
    end

    self.ScrollTable_invite.onItemRender = function(scroll_rect_item, index, dataItem)
        self:InviteOnRender(scroll_rect_item, index, dataItem)
    end
    self.ScrollTable_invite.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            --rect_table Dispose时 Item上相关的资源是否需要Dispose           
        end

        if scroll_rect_item then
            local refuseBtn = scroll_rect_item:Get("refuseBtn")
            local agreeBtn = scroll_rect_item:Get("agreeBtn")

            refuseBtn.onClick:RemoveAllListeners()
            agreeBtn.onClick:RemoveAllListeners()
        end
    end

end

function UIAllianceList:testScrollTo(index)
    self.rect_table:ScrollTo(index)
end

--更新联盟列表
function UIAllianceList:UpdateScrollList(data, begin)
    if not data then
        return
    end
    self.rect_table:SetData(data, #data)
    if begin == 0 then
        self.rect_table:Refresh(-1, -1)
    else
        self.rect_table:Refresh(0, -1)
    end
end
---更新联盟列表Item
function UIAllianceList:UpdateScrollItem(index, data)
    self:RefreshScrollTableItem(self.rect_table,index, data)
end

---@public SetNoAlliance 设置有没有联盟列表
----@param bool boolean 是否显示 true 有联盟列表，false 没有联盟列表
function UIAllianceList:SetNoAlliance(bool)
    self:SetActive(self.Trans_NoAlliance, bool)
end

local powerLimit = {}
local levelLimit = {}
local join_BtnBlue = {}
local join_BtnGray = {}
local join_BtnGreen = {}

--列表刷新回调方法
function UIAllianceList:OnItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    --
    local flagIcon = scroll_rect_item:Get("flagIcon")
    local abbNameText = scroll_rect_item:Get("abbNameText")
    local NameText = scroll_rect_item:Get("NameText")
    local memberText = scroll_rect_item:Get("memberText")
    local languageText = scroll_rect_item:Get("languageText")
    local powerText = scroll_rect_item:Get("powerText")

    local powerLimit = scroll_rect_item:Get("powerLimit")
    local levelLimit = scroll_rect_item:Get("levelLimit")

    --等级，战力要求显示
    local TipInfo = scroll_rect_item:Get("TipInfo")
    join_BtnBlue = scroll_rect_item:Get("joinBtn_Blue")
    join_BtnGray = scroll_rect_item:Get("joinBtn_Gray")
    join_BtnGreen = scroll_rect_item:Get("joinBtn_Green")

    --国旗
    local natinoal_flag_mgr = require("national_flag_mgr")
    
    --根据旗帜ID设置旗帜图标 
    if dataItem.flag then
        local flagData = alliance_data.GetFlagIdData(dataItem.flag)
        if sprite_asset then
            sprite_asset:GetSprite("qizhi" .. flagData.iconID, function(sprite)
                if sprite then
                    flagIcon.sprite = sprite
                end
            end)
        end
    end
    --abbNameText.text = "【" .. dataItem.shortName .. "】"
    --NameText.text = dataItem.allianceName
    abbNameText.text = string.format("[%s] %s", dataItem.shortName, dataItem.allianceName)

    memberText.text = lang.Get(600264) .. ": " .. dataItem.count .. "/" .. dataItem.peopleMaxNum
    languageText.text = lang.Get(600352) .. ": " .. lang.Get(dataItem.language)

    if lang.USE_LANG == alliance_mgr.GetAllianceLanguage(dataItem.language) then
        Common_Util.SetColor(languageText, "#319F38")
    else
        Common_Util.SetColor(languageText, "#597688")
    end

    --powerText.text = "总战力: " .. dataItem.power
    if dataItem.power ~= nil then
        powerText.text = string.format("%s:%s", lang.Get(600278), util.NumberWithUnit(dataItem.power))
    end

    if dataItem.ceLimit ~= nil then
        powerLimit.text = string.format("%s  ≥%s", lang.Get(600260), dataItem.ceLimit)
    end

    if dataItem.lvLimit ~= nil then
        --powerLimit.text = "总部要求:  ≥" .. dataItem.ceLimit .. "k"
        --levelLimit.text = "总部要求:  ≥" .. dataItem.lvLimit .. "级"
        levelLimit.text = string.format("%s  ≥%s%s", lang.Get(600261), dataItem.lvLimit, lang.Get(600347))
    end

    if dataItem.isLevelLimit and dataItem.IsPowerLimit then
        TipInfo.gameObject:SetActive(false)
        --这部分在处理按钮
        if dataItem.apply then
            self:SetApplyButton(2)
        elseif dataItem.applySet == alliance_pb.emAllianceApplyType_Auto then
            self:SetApplyButton(3)
        else
            self:SetApplyButton(1)
        end
    else
        TipInfo.gameObject:SetActive(true)
        self:CloseAllButton()

        if not dataItem.IsPowerLimit then
            powerLimit.text = string.format("<color=#597688>%s</color><color=#FF4D2A>≥%s</color>", lang.Get(600260), dataItem.ceLimit)
        end

        if not dataItem.isLevelLimit then
            levelLimit.text = string.format("<color=#597688>%s</color><color=#FF4D2A>≥%s%s</color>", lang.Get(600261), dataItem.lvLimit, lang.Get(600347))
        end
    end
    --ClickAllianceButton
    --ClickJoinButton
    --ClickJoinedButton
    --ClickApplyButton
    e_handler_mgr.TriggerHandler(window.controller_name, "OnScorllRectItemRender", index, dataItem)
    --绑定上点击等事件，主要是为了抛给controller层进行分离   
    scroll_rect_item.InvokeFunc = function(funcname)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index, dataItem)
        end
    end

end

--切换分页 索引0为加入 1为创建 2 为邀请
function UIAllianceList:SwitchPage(index)

    self.Toggle_Join.isOn = index == 0
    self.Toggle_Create.isOn = index == 1
    self.Toggle_Invite.isOn = index == 2
    --self.Text_title.text = index == 0 and "#配置lang 加入联盟" or "#配置lang 创建联盟"
    --这里加颜色变化
    --self:ToggleColor(index == 0)

    --根据索引决定分页
    --self.createAlliance.gameObject:SetActive(index == 1)
    self:SetActive(self.createAlliance, index == 1)
    self:SetActive(self.invitePanel, index == 2)
end

--快速加入
function UIAllianceList:Quicklist(boolean)
    if boolean then
        --有合适的跳转到联盟主界面
    else
        --没有合适的弹出文字提示“当前没有可加入的联盟请稍后再试”
        local flow_text = require "flow_text"
        flow_text.Add(lang.Get(600262))
        --flow_text.Add("当前没有可加入的联盟请稍后再试")
    end
end
--名字是否可用
function UIAllianceList:NameIsEnabled(boolean)

    self.Sprite_nameTrue:Switch(boolean and 0 or 1)
    self:SetActive(self.nameErrorText, not boolean)

end
--简称是否可用
function UIAllianceList:AbbIsEnabled(boolean)
    self.Sprite_abbTrue:Switch(boolean and 0 or 1)
    self:SetActive(self.abbErrorText, not boolean)
end
--刷新输入提示
---@param state number 1 请在输入框内输入名称，最多16个字符  2 长度不符合规范 3 与其他联盟重名 4 名称中有限制字符 5 钻石不足
function UIAllianceList:SetAbbInputName(state)
    if state == 1 then
        --请在输入框内输入名称，最多16个字符
        self.abbErrorText.text = lang.Get(600154)
    elseif state == 2 then
        --长度不符合规范
        self.abbErrorText.text = lang.Get(600377)
    elseif state == 3 then
        --与其他联盟重名
        self.abbErrorText.text = lang.Get(600378)
    elseif state == 4 then
        --名称中有限制字符
        self.abbErrorText.text = lang.Get(600379)
    else
        self.abbErrorText.text = string.empty()
    end
end

--刷新输入提示
---@param state number 1 请在输入框内输入名称，最多16个字符  2 长度不符合规范 3 与其他联盟重名 4 名称中有限制字符 5 钻石不足
function UIAllianceList:SetInputName(state)
    if state == 1 then
        --请在输入框内输入名称，最多16个字符
        self.nameErrorText.text = lang.Get(600153)
    elseif state == 2 then
        --长度不符合规范
        self.nameErrorText.text = lang.Get(600377)
    elseif state == 3 then
        --与其他联盟重名
        self.nameErrorText.text = lang.Get(600378)
    elseif state == 4 then
        --名称中有限制字符
        self.nameErrorText.text = lang.Get(600379)
    else
        self.nameErrorText.text = string.empty()
    end
end

function UIAllianceList:RefreshNationalFlag()
    if require("national_flag_mgr").CheckNationalFlagIsOpen() then
        self.Btn_chooseFlag.transform.parent.gameObject:SetActive(true)
        self.Img_CurrentNationalFlag.transform.parent.gameObject:SetActive(true)
        -- 设置国旗
        local national_flag_mgr = require("national_flag_mgr")
        local nationalSelectID = national_flag_mgr.GetCreateAllianceNationalFlagID()
        if nationalSelectID == nil or nationalSelectID == 0 then
            nationalSelectID = national_flag_mgr.GetSelfNationalFlagInfo()
        end
        national_flag_mgr.SetNationalFlagInfo(self, self.Img_CurrentNationalFlag, nationalSelectID)
    else
        self.Btn_chooseFlag.transform.parent.gameObject:SetActive(false)
        self.Img_CurrentNationalFlag.transform.parent.gameObject:SetActive(false)
    end
end

--建设按钮是否可用
--@param boolean true可用 false不可用
function UIAllianceList:SetUpIsEnable(boolean)
    --如果不可用，按钮变灰
    self.Sprite_setUp:Switch(boolean and 0 or 1)
end

--更新建立联盟的消耗
--@param num 消耗数量
--@param iconID 消耗图标ID
function UIAllianceList:InitSetUpSpend(num, iconID, max)

    self.setUpSpend_Text.text = string.format("%s %s", lang.Get(600268), num) -- num
    self.InputField_Search.characterLimit = max

    if spriteAsset then
        spriteAsset:GetSprite(iconID, function(sprite)
            self.setUpSpend_Icon.sprite = sprite
        end)
    end
end

--更新输入的字符数量
function UIAllianceList:SetCurCharNum(str)
    self.charNum.text = str
end

--设置随机简称返回的字符
function UIAllianceList:RandomName(str)
    self.InputField_Abb.text = str
end

--按钮倒计时
--@time 倒计时时间
--@bool 是否十分钟内退过联盟
function UIAllianceList:ButtonCountDown(time, bool)
    if bool then
        self.countDownText.text = time
    else
        --"快速加入"
        self.countDownText.text = lang.Get(16049)
    end
end

--设置不同的按钮
--1:申请
--2:已申请
--3:加入
function UIAllianceList:SetApplyButton(btnState)
    if btnState == 1 then
        join_BtnBlue.gameObject:SetActive(true)
        join_BtnGray.gameObject:SetActive(false)
        join_BtnGreen.gameObject:SetActive(false)
    elseif btnState == 2 then
        join_BtnGray.gameObject:SetActive(true)
        join_BtnBlue.gameObject:SetActive(false)
        join_BtnGreen.gameObject:SetActive(false)
    elseif btnState == 3 then
        join_BtnGreen.gameObject:SetActive(true)
        join_BtnBlue.gameObject:SetActive(false)
        join_BtnGray.gameObject:SetActive(false)
    end
end
--隐藏全部按钮
function UIAllianceList:CloseAllButton()
    join_BtnGreen.gameObject:SetActive(false)
    join_BtnBlue.gameObject:SetActive(false)
    join_BtnGray.gameObject:SetActive(false)
end

--显示关闭设置语言窗口
function UIAllianceList:ShowLangWindow()
    windowMgr:ShowModule("ui_alliance_lang")
end
--region 创建界面分页功能函数区
--设置联盟旗帜 
--@id 旗帜ID
function UIAllianceList:SetFlag(id)
    --根据旗帜ID设置旗帜图标 dataItem.flag
    if sprite_asset then
        sprite_asset:GetSprite("qizhi" .. id, function(sprite)
            if sprite then
                self.Img_AllianceFlag.sprite = sprite
            end
        end)
    end
end

--设置当前语言显示 
--@str 字符串 (请传入Getlang 后的字符串)
function UIAllianceList:SetCurrentLangText(str)
    self.Text_CurrentLanguage.text = str
end

---@public SetSpendTextColor 设置消耗钻石数量颜色
----@param bool boolean true 黑 false 红
function UIAllianceList:SetSpendTextColor(bool)
    self:SetColor(self.setUpSpend_Text, bool and "#000000" or "#FF0000")--黑
end

--endregion

--region 联盟邀请分页
function UIAllianceList:UpdateInviteList(data)
    if not data then

        self.ScrollTable_invite.data = nil
        self.ScrollTable_invite:Refresh(-1, -1)
        return
    end
    self.ScrollTable_invite:SetData(data, #data)
    self.ScrollTable_invite:Refresh(-1, -1)
end

function UIAllianceList:InviteOnRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local flagIcon = scroll_rect_item:Get("flagIcon")
    local name = scroll_rect_item:Get("name")
    local number = scroll_rect_item:Get("number")
    local language = scroll_rect_item:Get("language")
    local power = scroll_rect_item:Get("power")
    local refuseBtn = scroll_rect_item:Get("refuseBtn")
    local agreeBtn = scroll_rect_item:Get("agreeBtn")

    name.text = string.format("[%s]%s", dataItem.shortName, dataItem.allianceName)
    number.text = string.format("%s：%s/%s", lang.Get(600346), dataItem.count, alliance_data.GetPeopleMaxLimit())
    language.text = string.format("%s: %s", lang.Get(600352),lang.Get(dataItem.language))
    power.text = string.format("%s: %s", lang.Get(600278), alliance_ui_util.KiloSeparator(dataItem.power))


    --根据旗帜ID设置旗帜图标 dataItem.flag
    local cfg = game_scheme:LeagueIcon_0(dataItem.flag)
    if cfg then
        if sprite_asset then
            sprite_asset:GetSprite("qizhi" .. cfg.iconID, function(sprite)
                if sprite then
                    flagIcon.sprite = sprite
                end
            end)
        end
    end
    
    refuseBtn.onClick:RemoveAllListeners()
    refuseBtn.onClick:AddListener(function()
        --拒绝邀请
        alliance_mgr_extend.HandleAllianceInviteReq(0,dataItem.allianceId)
    end)
    agreeBtn.onClick:RemoveAllListeners()
    agreeBtn.onClick:AddListener(function()
        alliance_mgr_extend.HandleAllianceInviteReq(1,dataItem.allianceId)
    end)
    
end

--endregion

---********************end功能函数区**********---

--@region WindowInherited
local CUIAllianceList = class(ui_base, nil, UIAllianceList)
--@endregion 

--@region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
    if data and data["uipath"] then
        ui_path = data["uipath"];
    end
    if window == nil then
        window = CUIAllianceList()
        window._NAME = _NAME
        window.delayOpenMain = 0
        window.delayCloseMain = 0
        window:LoadUIResource(ui_path, nil, nil, nil,nil,true)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--@endregion 






