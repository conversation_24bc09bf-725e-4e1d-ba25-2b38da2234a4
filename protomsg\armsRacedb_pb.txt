-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
module('armsRacedb_pb')


F1D=F(2,"dbid",".CSMsg.ArmsRaceBaseInfo.dbid",1,0,2,false,0,5,1)
F2D=F(2,"startTime",".CSMsg.ArmsRaceBaseInfo.startTime",2,1,2,false,0,5,1)
F3D=F(2,"teamId",".CSMsg.ArmsRaceBaseInfo.teamId",3,2,2,false,0,5,1)
F4D=F(2,"endTime",".CSMsg.ArmsRaceBaseInfo.endTime",4,3,2,false,0,5,1)
F5D=F(2,"point",".CSMsg.ArmsRaceBaseInfo.point",5,4,2,false,0,5,1)
F6D=F(2,"pointDay",".CSMsg.ArmsRaceBaseInfo.pointDay",6,5,2,false,0,5,1)
F7D=F(2,"pointProcess",".CSMsg.ArmsRaceBaseInfo.pointProcess",7,6,3,false,{},5,1)
F8D=F(2,"roundsId",".CSMsg.ArmsRaceBaseInfo.roundsId",8,7,2,false,0,5,1)
F9D=F(2,"rankGroupId",".CSMsg.ArmsRaceBaseInfo.rankGroupId",9,8,2,false,0,5,1)
F10D=F(2,"matchRoundsId",".CSMsg.ArmsRaceBaseInfo.matchRoundsId",10,9,2,false,0,5,1)
F11D=F(2,"matchRankGroupId",".CSMsg.ArmsRaceBaseInfo.matchRankGroupId",11,10,2,false,0,5,1)
F12D=F(2,"pointWeek",".CSMsg.ArmsRaceBaseInfo.pointWeek",12,11,3,false,{},5,1)
F13D=F(2,"rankTime",".CSMsg.ArmsRaceBaseInfo.rankTime",13,12,2,false,0,5,1)
F14D=F(2,"matchTime",".CSMsg.ArmsRaceBaseInfo.matchTime",14,13,2,false,0,5,1)
F15D=F(2,"ArmsRaceThemeTimeID",".CSMsg.ArmsRaceBaseInfo.ArmsRaceThemeTimeID",15,14,2,false,0,5,1)
F16D=F(2,"actOpenTime",".CSMsg.ArmsRaceBaseInfo.actOpenTime",16,15,2,false,0,5,1)
F17D=F(2,"pointDayClearTime",".CSMsg.ArmsRaceBaseInfo.pointDayClearTime",17,16,1,false,0,5,1)
M1G=D(1,"ArmsRaceBaseInfo",".CSMsg.ArmsRaceBaseInfo",false,{},{},nil,{})
F18D=F(2,"dbidList",".CSMsg.ArmsRaceDbidList.dbidList",1,0,3,false,{},5,1)
M2G=D(1,"ArmsRaceDbidList",".CSMsg.ArmsRaceDbidList",false,{},{},nil,{})
F19D=F(2,"matchTimeList",".CSMsg.ArmsRaceRetakeMatch.matchTimeList",1,0,3,false,{},5,1)
M3G=D(1,"ArmsRaceRetakeMatch",".CSMsg.ArmsRaceRetakeMatch",false,{},{},nil,{})
F20D=F(2,"worldIds",".CSMsg.ArmsRaceWorldIdList.worldIds",1,0,3,false,{},5,1)
M4G=D(1,"ArmsRaceWorldIdList",".CSMsg.ArmsRaceWorldIdList",false,{},{},nil,{})
F21D=F(2,"groupId",".CSMsg.RankGroupInfo.groupId",1,0,2,false,0,5,1)
F22D=F(2,"count",".CSMsg.RankGroupInfo.count",2,1,2,false,0,5,1)
M5G=D(1,"RankGroupInfo",".CSMsg.RankGroupInfo",false,{},{},nil,{})
F23D=F(2,"roundsId",".CSMsg.RankRoundsInfo.roundsId",1,0,2,false,0,5,1)
F24D=F(2,"groupInfo",".CSMsg.RankRoundsInfo.groupInfo",2,1,3,false,{},11,10)
M6G=D(1,"RankRoundsInfo",".CSMsg.RankRoundsInfo",false,{},{},nil,{})
F25D=F(2,"worldId",".CSMsg.ArmsRaceRankGroup.worldId",1,0,1,false,0,5,1)
F26D=F(2,"roundsRank",".CSMsg.ArmsRaceRankGroup.roundsRank",2,1,3,false,{},11,10)
F27D=F(2,"matchRoundsRank",".CSMsg.ArmsRaceRankGroup.matchRoundsRank",3,2,3,false,{},11,10)
F28D=F(2,"areaId",".CSMsg.ArmsRaceRankGroup.areaId",4,3,2,false,0,5,1)
F29D=F(2,"roundsTime",".CSMsg.ArmsRaceRankGroup.roundsTime",5,4,1,false,0,5,1)
M7G=D(1,"ArmsRaceRankGroup",".CSMsg.ArmsRaceRankGroup",false,{},{},nil,{})

M1G.fields={F1D, F2D, F3D, F4D, F5D, F6D, F7D, F8D, F9D, F10D, F11D, F12D, F13D, F14D, F15D, F16D, F17D}
M2G.fields={F18D}
M3G.fields={F19D}
M4G.fields={F20D}
M5G.fields={F21D, F22D}
F24D.message_type=M5G
M6G.fields={F23D, F24D}
F26D.message_type=M6G
F27D.message_type=M6G
M7G.fields={F25D, F26D, F27D, F28D, F29D}

ArmsRaceBaseInfo =M(M1G)
ArmsRaceDbidList =M(M2G)
ArmsRaceRankGroup =M(M7G)
ArmsRaceRetakeMatch =M(M3G)
ArmsRaceWorldIdList =M(M4G)
RankGroupInfo =M(M5G)
RankRoundsInfo =M(M6G)

