local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local festival_activity_mgr = require "festival_activity_mgr"
local shining_market_define = require "shining_market_define"
local log = require "log"
local game_scheme = require "game_scheme"
local reward_mgr = require "reward_mgr"
local festival_activity_cfg = require "festival_activity_cfg"
local activity_mgr = require "activity_mgr"
local player_mgr = require "player_mgr"
local event = require "event"
local cfg_util = require "cfg_util"

--region Controller Life
module("ui_halloween_exchange_shop_controller")
local controller = nil
local UIController = newClass("ui_halloween_exchange_shop_controller", controller_base)

local isExchangeTip = true
function GetIsExchangeTip()
    return isExchangeTip
end
function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.activityID = data.activityID
    self.__base.Init(self, view_name, controller_name)   

    self.activityCfg = festival_activity_mgr.GetActivityCfgByAtyID(self.activityID)
    self.subActivityID = festival_activity_mgr.GetSubActiveId(self.activityID)
    self.subActivityCfg = festival_activity_cfg.GetActivityCfgByAtyID(self.subActivityID)
    self.uidetailCfg = game_scheme:ActivityCommonUI_0(self.activityID)
    if self.activityCfg then
        self.buidingInfo = cfg_util.ArrayToLuaArray(self.activityCfg.ctnID1)
    end

end

function UIController:InitInfo() 
    local endTime = festival_activity_mgr.GetAcitivityEndStampByActivityID(self.activityID)
    self:TriggerUIEvent("SetTopTime",endTime)
    self:UpdateShopList()
    local data = {
        endTime = endTime,
        topAddItemID = self.topAddItemID or 8039 ,
        isExchangeTip = isExchangeTip
    }
    self:TriggerUIEvent("ShowTopInfo",data)
    self:InitBuilding()
end

function UIController:InitBuilding()
    if self.buidingInfo then
        local helper_personalInfo = require "helper_personalInfo"
        local schlossId = self.buidingInfo[1]
        local schlossEffectId = self.buidingInfo[2]
        local schlossPath = helper_personalInfo.GetSchlossPrefabPath(helper_personalInfo.SchlossPrefabType.personalised, schlossId)
        --读取特效装扮
        local effectPath, _id, effectScale, effectPos,effectBottomPath = helper_personalInfo.GetSchlossEffectPrefabPath(helper_personalInfo.SchlossPrefabType.personalised,schlossEffectId)
        local effectParams = {
            effectPath = effectPath,
            effectScale = effectScale,
            effectRotation = { x = -5, y = 0, z = 0 },
            effectPos = effectPos,
        }
        local effectBottomParams = {
            effectPath = effectBottomPath,
            effectScale = effectScale,
            effectRotation = { x = -5, y = 0, z = 0 },
            effectPos = effectPos,
        }
        local net_module_open = require "net_module_open"
        local moduleOpenPro_pb = require "moduleOpenPro_pb"
        local moduleOpen = net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_SchlossEffect)
        self:TriggerUIEvent("DispalyBuilding",  schlossPath, effectParams,effectBottomParams,moduleOpen)

    end

end

function UIController:OnShow()
    self.__base.OnShow(self)
    self:InitInfo()
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
    self.openDetail = function(...) 
        self:OpenDetail(...)
    end
    self.updateShopList = function(...) 
        self:UpdateShopList(...)
    end
    local gw_event_activity_define = require "gw_event_activity_define"
    self:RegisterEvent(gw_event_activity_define.GW_FULL_SIEGE_EXCHANGE_SHOP_DATA_UPDATE, self.updateShopList)
end

function UIController:OpenDetail(index,data) 

    if not data or not data.canBuyState then 
        return 
    end
    local goodData = {}
    goodData.AtyID = self.subActivityID
    goodData.ActivityContentID = data.contentID
    goodData.exchangeType = 1
    goodData.LimitNumber = data.maxBuyNum-data.curCount
    goodData.OnClickCallBack = function(hasNum,costNum)
        local evt_sourceSupply_define = require "evt_sourceSupply_define"
        local supplyData = {
            list = {
                {
                    goodsId = data.priceInfo.costItem,
                    needNum = costNum,
                }
            }
        }
        event.Trigger(evt_sourceSupply_define.Evt_ShowSupplyPanel, supplyData)
    end
    -- print("OpenDetail")
    -- dump(goodData)
    ui_window_mgr:ShowModule("ui_general_exchange_shop",nil,nil,goodData)
end
function UIController:UpdateShopList() 
    local allData ={}
    if not self.subActivityCfg then
        return
    end
    local gw_fully_war_mgr = require "gw_fully_war_mgr"

    local CfgData = cfg_util.ArrayToLuaArray(self.subActivityCfg.ctnID1)
    -- print("UpdateShopList #1")
    -- dump(CfgData)
    if CfgData then 
        for i,v in ipairs(CfgData) do 
            local Cfg = game_scheme:ActivityContent_0(v)
            if Cfg then 
                --设置道具显示
                local rewardInfo = reward_mgr.GetRewardGoodsList(Cfg.rewardGroup)
                local data ={}
                data.contentID = v
                if rewardInfo and #rewardInfo > 0 then 
                    data.rewardId = rewardInfo[1].id
                    data.rewardNum = rewardInfo[1].num
                else
                    log.Error("@策划 请检查Reward.csv表 ActivityContent.csv的rewardGroup不存在Reward.csv的id",Cfg.rewardGroup)
                    return
                end
                --设置标签类型
                if Cfg.hotFlag - 3 == shining_market_define.GoodsFlagType.rare then 
                    data.flagType = shining_market_define.GoodsFlagType.rare
                elseif Cfg.hotFlag - 3 == shining_market_define.GoodsFlagType.superValue then 
                    data.flagType = shining_market_define.GoodsFlagType.superValue
                elseif Cfg.hotFlag - 3 == shining_market_define.GoodsFlagType.backreturn then 
                    data.flagType = shining_market_define.GoodsFlagType.backreturn
                end
                --设置价格信息
                local priceInfo = activity_mgr.GetExpenditurByCSV(Cfg.expenditure)
                data.priceInfo = {}
                if priceInfo then
                    self.topAddItemID = self.topAddItemID or priceInfo[1][1]
                    data.priceInfo.costItem = priceInfo[1][1]
                    data.priceInfo.num = priceInfo[1][2]
                    local ownNum = player_mgr.GetPlayerOwnNum(priceInfo[1][1])
                    data.canBuyItem = ownNum >= priceInfo[1][2] 
                end
                --设置剩余数量
                local exchangeInfo = gw_fully_war_mgr.GetExchangeInfoByID(self.subActivityID,v)
                data.curCount = exchangeInfo.exchangeNum or 0
                --设置最大可购买数量
                data.maxBuyNum = Cfg.LimitNumber
                --设置可购买状态
                data.canBuyState = data.curCount < Cfg.LimitNumber
                --限购类型 每日刷新 活动刷新 
                data.limitType = Cfg.RefreshType
                --设置是否足够货币购买该物品
                data.isExchangeTip = isExchangeTip
                --设置点击事件
                data.OnClickItem = self.openDetail
                table.insert(allData,data)
            end
        end
        local sortallData=self:SortShopList(allData)
        self:TriggerUIEvent("UpdateShopScrollList",sortallData)
    end
end

function UIController:SortShopList(data) 
    local sortFunc = function(a,b) 
        if a.limitType > b.limitType then 
            return true
        elseif a.limitType < b.limitType then 
            return false
        else
            if a.limitType ==  b.limitType and a.limitType == 0 then 
                 if a.curCount == 0 and b.curCount > 0 then 
                    return false
                elseif a.curCount > 0 and b.curCount == 0 then
                    return true
                else
                    return a.rewardId > b.rewardId
                end
            elseif a.limitType ==  b.limitType and a.limitType == 1 then 
                return a.rewardId > b.rewardId
            end
        end
    end
    table.sort(data,sortFunc)
    return data
end
function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnTipBtnClickedProxy()
    if self.uidetailCfg then
        local ui_help = require "ui_help"
        ui_help.ShowWithDate(self.uidetailCfg.helpTips)
    end
end
function  UIController:OnBtnAddClickedProxy()
end
function  UIController:OnTogExchangeTipSwitchValueChange(state)
    isExchangeTip = state
    self:TriggerUIEvent("OnTogExchangeTipSwitchValueChange",state)
    self:UpdateShopList()
    local gw_event_activity_define = require "gw_event_activity_define"
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,self.activityID)
end
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end

function UIController:OnBtnBuildingClickedProxy()
    if self.buidingInfo then
        local iui_item_detail = require "iui_item_detail"
        local item_data = require "item_data"
        iui_item_detail.Show(self.buidingInfo[1], nil, item_data.Item_Show_Type_Enum.Reward_Interface)
    end
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end

function ClearData()
    isExchangeTip = true
end

event.Register(event.USER_DATA_RESET, ClearData)

--endregion
