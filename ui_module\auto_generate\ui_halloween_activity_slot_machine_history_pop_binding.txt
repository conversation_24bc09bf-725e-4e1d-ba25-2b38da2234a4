local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local GameObject = CS.UnityEngine.GameObject
local Image = CS.UnityEngine.UI.Image
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local RectTransform = CS.UnityEngine.RectTransform


module("ui_halloween_activity_slot_machine_history_pop_binding")

UIPath = "ui/prefabs/gw/activity/halloweenactivity/slotmachine/uihalloweenactivityslotmachinehistorypop.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	item_history_group_item = { path = "item_history_group_item", type = GameObject, },
	img_bg = { path = "center/img_bg", type = Image, },
	sr_ScrollView = { path = "center/img_bg/sr_ScrollView", type = ScrollRect, },
	rtf_content = { path = "center/img_bg/sr_ScrollView/Viewport/rtf_content", type = RectTransform, },

}
