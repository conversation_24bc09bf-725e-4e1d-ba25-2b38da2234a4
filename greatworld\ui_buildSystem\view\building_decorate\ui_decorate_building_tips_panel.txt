﻿--region FileHead
--- ui_decorate_building_tips_panel
-- author:  zy
-- ver:     1.0
-- desc:    
-------------------------------------------------
--endregion 

--region Require
local require = require
local type = type
local ipairs = ipairs
local tostring = tostring
local string = string
local tonumber = tonumber

local GameObject = CS.UnityEngine.GameObject
local Image = CS.UnityEngine.UI.Image
local enum_define = require "enum_define"
local class = require "class"
local lang = require "lang"
local ui_base = require "ui_base"
local game_scheme = require "game_scheme"
local GWG = GWG
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local gwhomebuildingiconAsset = nil
local gw_home_card_sprite_asset_mgr = require "gw_home_card_sprite_asset_mgr"
--endregion 

--region ModuleDeclare
module("ui_decorate_building_tips_panel")
local ui_path = "ui/prefabs/gw/buildsystem/uidecoratebulidingtipspanel.prefab"
local window = nil
local UIView = {}
--endregion 

--region WidgetTable
UIView.widget_table = {
    buildingIcon = {path = "TopPart/BuildingIcon", type = Image},
    buildingName = {path = "TopPart/BuildingName", type = "Text"},
    buildingDesc = {path = "TopPart/BuildingDesc", type = "Text"},
    propChunkItem = {path = "MiddlePart/ScrollView/Viewport/Content/PropItem", type = GameObject},
    ItemRoot = {path = "MiddlePart/ScrollView/Viewport/Content", type = "RectTransform"},
    closeBtn = {path = "MiddlePart/closeBtn", type = "Button", backEvent = true},
    imgBg = { path = "imgBg", type = SpriteSwitcher },
}
--endregion 

--region function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end
--endregion 

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self:SubscribeEvents()

    gwhomebuildingiconAsset  = gw_home_card_sprite_asset_mgr.GetOrCreateCardSpriteAsset("gwhomebuildingicon")
    
    --region User
    --endregion 
end --///<<< function
--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self.__base:OnShow()
    self:UpdateContentList()
end --///<<< function

--endregion 

--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()    
end --///<<< function

--endregion 


--region WindowClose
function UIView:Close()
    self.__base:Close()
    self:UnsubscribeEvents() 
    self:RecyclePool()
    window = nil
end --///<<< function
--endregion 

--region 事件注册
function UIView:SubscribeEvents()
    self.widget_table["closeBtn"].event_name = "closeBtnEvent"
end

function UIView:UnsubscribeEvents()
end

--endregion

--region 功能函数区
---********************功能函数区**********---
function UIView:UpdateContentList()
    local decorateData = GWG.GWHomeMgr.buildingData.GetDecorateBuildingData()
    if not decorateData then
        return
    end
    local bgIndex = decorateData.rarity -2
    self.imgBg:Switch(bgIndex)
    
    gwhomebuildingiconAsset:GetSprite( decorateData.buildingIcon,function(sprite)
        self.buildingIcon.sprite = sprite
    end)

    local nameID =  decorateData.buildingNameID
    local nameStr = lang.Get(nameID)
    self.buildingName.text = nameStr

    self.buildingDesc.text = lang.Get(decorateData.buildingDesc)
    
    self.item_chunk_active_pool = self.item_chunk_active_pool or {}
    self.item_active_pool = self.item_active_pool or {}
    local item_root = self.propChunkItem.transform:Find("Active/ItemRoot")
    self.prop_item = item_root:Find("PropItem").gameObject
    self.propChunkItem:SetActive(false)
    self.prop_item:SetActive(false)

    local building_cfg_table = GWG.GWHomeMgr.buildingData.GetPropIdLstByBuildingId(decorateData.buildingID)
    for k,v in ipairs(building_cfg_table) do
        local building_cfg = v
        local item_chunk = GameObject.Instantiate(self.propChunkItem,self.ItemRoot,false)
        item_chunk:SetActive(true)
        self.item_chunk_active_pool[#self.item_chunk_active_pool+1] = item_chunk
        local cur_select = k == decorateData.buildingLv
        
        if self.data and self.data.show_cur~=nil and self.data.show_cur == false then
            cur_select = false
        end
        
        local point_img = item_chunk.transform:Find("Active/Point").gameObject:GetComponent("Image")
        local lv = item_chunk.transform:Find("Active/Point/Level").gameObject:GetComponent("Text")
        local mark = item_chunk.transform:Find("Active/Point/Mark")
        local bg_img = item_chunk.transform:Find("Active").gameObject:GetComponent("Image")
        lv.text = "Lv."..tostring(k)
        if cur_select then
            point_img.color = {r=114/255, g=150/255, b=111/255, a=1}
            lv.color = {r=58/255, g=104/255, b=53/255, a=1}
            bg_img.color = {r=198/255,g=238/255,b=166/255,a=1}
            mark:SetActive(true)
        else
            point_img.color = {r=166/255, g=138/255, b=119/255, a=1}
            lv.color = {r=104/255, g=74/255, b=53/255, a=1}
            bg_img.color = {r=225/255,g=221/255,b=214/255,a=1}
            mark:SetActive(false)
        end

        for i=1,5 do
            local buildingFuncCfg = building_cfg["FunctionParam"..tostring(i).."Des"]
            if not buildingFuncCfg or #buildingFuncCfg[1] == 0 then
                break
            end
            local log = require "log"
            log.Warning("buildingFuncCfg:",buildingFuncCfg[1][0],buildingFuncCfg[1][1])
            local langDesc = buildingFuncCfg[1][0]
            local curLevelParam = buildingFuncCfg[1][1]
            
            local item_prop_root = item_chunk.transform:Find("Active/ItemRoot")
            local prop_item = GameObject.Instantiate(self.prop_item,item_prop_root,false)
            prop_item:SetActive(true)
            self.item_active_pool[#self.item_active_pool+1] = prop_item
            local prop_name_go = prop_item.transform:Find("PropName")
            if prop_name_go then
                local name_txt = prop_name_go.gameObject:GetComponent("Text")
                name_txt.text = lang.Get(langDesc)
                if cur_select then
                    name_txt.color = {r=44/255,g=67/255,b=41/255,a=1}
                else
                    name_txt.color = {r=67/255,g=57/255,b=41/255,a=1}
                end
            end

            local prop_value_go = prop_item.transform:Find("PropValue")
            if prop_value_go then
                local value_txt = prop_value_go.gameObject:GetComponent("Text")

                value_txt.text = lang.Get(curLevelParam)
                if cur_select then
                    value_txt.color = {r=44/255,g=67/255,b=41/255,a=1}
                else
                    value_txt.color = {r=67/255,g=57/255,b=41/255,a=1}
                end
            end
        end

       
        for i=1,5 do
            local cur_prop_id = building_cfg["BuildingPara"..tostring(i)]
            local curProp =  game_scheme:GWMapEffect_0(cur_prop_id)
            if curProp then
                local typeId = curProp.nGroupID
                local proData = GWG.GWHomeMgr.buildingData.GetDecorateProTable(typeId)
                if curProp then
                    local curPropValue = curProp.strParam[0]
                    if curPropValue >0 then
                        local curPropDes = proData.proLang
                        local item_prop_root = item_chunk.transform:Find("Active/ItemRoot")
                        local prop_item = GameObject.Instantiate(self.prop_item,item_prop_root,false)
                        prop_item:SetActive(true)
                        self.item_active_pool[#self.item_active_pool+1] = prop_item
                        local prop_name_go = prop_item.transform:Find("PropName")
                        if prop_name_go then
                            local name_txt = prop_name_go.gameObject:GetComponent("Text")
                            name_txt.text = lang.Get(curPropDes)
                            if cur_select then
                                name_txt.color = {r=44/255,g=67/255,b=41/255,a=1}
                            else
                                name_txt.color = {r=67/255,g=57/255,b=41/255,a=1}
                            end
                        end

                        local prop_value_go = prop_item.transform:Find("PropValue")
                        if prop_value_go then
                            local value_txt = prop_value_go.gameObject:GetComponent("Text")

                            local proCfg = game_scheme:ProToLang_0(proData.proType)
                            local isPercentage = proCfg.isPercentage and proCfg.isPercentage==1
                            local percentage = isPercentage and "%" or ""
                            value_txt.text = "+"..(isPercentage and (curPropValue / 100) or curPropValue)..percentage
                            if cur_select then
                                value_txt.color = {r=44/255,g=67/255,b=41/255,a=1}
                            else
                                value_txt.color = {r=67/255,g=57/255,b=41/255,a=1}
                            end
                        end
                    end
                end
            end
        end
    end
end

function UIView:RecyclePool()
    if self.item_chunk_active_pool then
    end
    if self.item_active_pool then
    end
end

---********************end功能函数区**********---
--endregion

--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
     if  data  and type(data) == "table" and data["uipath"] then
        ui_path = data["uipath"];   
    end  
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window:LoadUIResource(ui_path, nil, nil, nil,nil,true)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window.data = data
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion
