local require = require
local typeof = typeof

local RectTransform = CS.UnityEngine.RectTransform
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem


module("ui_arena_main_not_start_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenamainnotstart.prefab"

WidgetTable ={
	rtf_ArenaShow = { path = "rtf_ArenaShow", type = RectTransform, },
	txt_openServerID = { path = "TopShow/txt_openServerID", type = Text, },
	btn_tipBtn = { path = "TopShow/btn_tipBtn", type = Button, event_name = "OnBtnTipBtnClickedProxy"},
	btn_showDailyReward = { path = "TopShow/btn_showDailyReward", type = But<PERSON>, event_name = "OnBtnShowDailyRewardClickedProxy"},
	btn_showRecordList = { path = "TopShow/btn_showRecordList", type = Button, event_name = "OnBtnShowRecordListClickedProxy"},
	scrItem_PlayerItem_1 = { path = "TopPlayerShow/scrItem_PlayerItem_1", type = ScrollRectItem, },
	scrItem_PlayerItem_2 = { path = "TopPlayerShow/scrItem_PlayerItem_2", type = ScrollRectItem, },
	scrItem_PlayerItem_3 = { path = "TopPlayerShow/scrItem_PlayerItem_3", type = ScrollRectItem, },
	txt_arenaStartTime = { path = "BottomShow/txt_arenaStartTime", type = Text, },
	txt_AdvancedNeedPowerRank = { path = "BottomShow/ArenaShow/ArenaItem_1/NeedPowerRankShow/txt_AdvancedNeedPowerRank", type = Text, },
	btn_showAdvancedReward = { path = "BottomShow/ArenaShow/ArenaItem_1/btn_showAdvancedReward", type = Button, event_name = "OnBtnShowAdvancedRewardClickedProxy"},
	txt_MiddleNeedPowerRank = { path = "BottomShow/ArenaShow/ArenaItem_2/NeedPowerRankShow/txt_MiddleNeedPowerRank", type = Text, },
	btn_showMiddleReward = { path = "BottomShow/ArenaShow/ArenaItem_2/btn_showMiddleReward", type = Button, event_name = "OnBtnShowMiddleRewardClickedProxy"},
	txt_curPowerRank = { path = "BottomShow/txt_curPowerRank", type = Text, },
	btn_JumpHeroList = { path = "BottomShow/btn_JumpHeroList", type = Button, event_name = "OnBtnJumpHeroListClickedProxy"},

}
