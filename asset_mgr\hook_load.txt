local require = require
local table = table
local pairs = pairs
local string = string
local debug = debug
local type = type
local print = print
local os = os
local _G = _G
local log = require "log"
local util = require "util"
local json = require "dkjson"
local hook_count = require "hook_count"
local val = require "val"
local CS = CS

local Application = CS.UnityEngine.Application
local Time = CS.UnityEngine.Time
local System = CS.System
local AssetBundleManager = CS.War.Base.AssetBundleManager
local IOSystem = CS.War.Script.IOSystem
local Object = CS.UnityEngine.Object
local GameObjectPool = CS.War.Base.GameObjectPool
local PlayerPrefs = CS.UnityEngine.PlayerPrefs

local LogDebug = CS.War.Base.LogDebug

module("hook_load")

local cache = {console_func = {}}
function CheckInit()
    util.RegisterConsole(
        "sw_hook_abref",
        0,
        function(s)
            if s == 0 then
                -- DebugJson()
            elseif s == 2 then
                DebugJson()
            end
        end
    )

    HookABRef()

    HookDestroy()

    Hook_iosystem_load()

    HookFrame()
end

function HookABM()
    local tickers = {}

    if AssetBundleManager.on_event then
        AssetBundleManager.on_event = function(mark, args)
            tickers[mark] = tickers[mark] or hook_count(mark)
            hook_count:Count(Time.frameCount, unpack(args))
        end
    end
end
function Hook_iosystem_load()
    if not val.IsTrue("sw_hook_ab_load", 0) then
        return
    end

    local iosystem_load = require "iosystem_load"
    local LoadAssetAsync = iosystem_load.LoadAssetAsync
    local UnloadAssetBundle = iosystem_load.UnloadAssetBundle
    local event = require "event"
    iosystem_load.LoadAssetAsync = function(...)
        event.Trigger("HOOK_COUNT_EVENT", "LoadAssetAsync", "Count", ...)
        return LoadAssetAsync(...)
    end
    iosystem_load.UnloadAssetBundle = function(...)
        event.Trigger("HOOK_COUNT_EVENT", "UnloadAssetBundle", "Count", ...)
        return UnloadAssetBundle(...)
    end
end
function HookPlayerPref()
    if not val.IsTrue("sw_hook_playerpref", 0) then
        return
    end

    -- log.Warning("HookPlayerPref")
    local c = {}
    local cTime = {}
    local time = 0

    util.DelayCallOnce(
        0,
        function()
            time = Time.realtimeSinceStartup
            return 1
        end
    )

    local SetInt = PlayerPrefs.SetInt
    PlayerPrefs.SetInt = function(mark, value)
        c[mark] = value
        cTime[mark] = time
        SetInt(mark, value)
        log.Warning("SetInt", mark, value)
    end
    local GetInt = PlayerPrefs.GetInt
    PlayerPrefs.GetInt = function(mark, default)
        if cTime[mark] and time < cTime[mark] + 10 then
        else
            c[mark] = GetInt(mark)
            cTime[mark] = time
            log.Warning("GetInt", mark)
        end
        -- c[mark] = c[mark] or GetInt(mark)
        return c[mark] or default
    end
end

function HookFrame()
    if not val.IsTrue("sw_hook_frame", 0) then
        return
    end

    local Time = CS.UnityEngine.Time
    local hooking = false

    local hc = hook_count("HookFrame")

    local function hook(event, line)
        if hooking == true then
            return
        end
        hooking = true
        hc:SecCount(Time.frameCount)
        hooking = false
    end

    debug.sethook(hook, "cr")

    cache.console_func["sw_hook_frame"] = function(st)
        if st == 1 then
            hc:DebugCsv()
        end
    end
end

function HookDestroy()
    if not val.IsTrue("sw_hook_destroy", 0) then
        return
    end

    if Application.isEditor then
        local oldDestroy = Object.Destroy
        Object.Destroy = function(obj)
            -- print("Destroy")
            GameObjectPool.CheckRef(obj)
            if not util.IsObjNull(obj) then
                oldDestroy(obj)
            end
        end
        local oldDestroyImdy = Object.DestroyImmediate
        Object.DestroyImmediate = function(obj)
            -- print("DestroyImmediate", obj and obj.name)

            GameObjectPool.CheckRef(obj)
            if not util.IsObjNull(obj) then
                oldDestroyImdy(obj)
            end
        end
        local Release = GameObjectPool.Release
        GameObjectPool.Release = function(obj)
            print("Release", obj and obj.name)
            Release(obj)
        end
    end
end

function DebugJson()
    local rTable = _G["rTable"]
    if rTable then
        local json = require "json"
        local s = json.encode(rTable)
        log.Warning(s)
    end
end

function HookABRef()
    if not val.IsTrue("sw_hook_abref", 0) then
        return
    end


    local rTable = {}
    _G["rTable"] = rTable

    local oldUnloadfunc = IOSystem.UnloadAssetBundle
    IOSystem.UnloadAssetBundle = function(assetBundleName, unloadalll)
        print("UnloadAssetBundle", assetBundleName, unloadalll)

        local ifo = debug.traceback("", 2)
        local mark = string.match(ifo, "traceback:\n\t(.-):")

        rTable[mark] = rTable[mark] or {}
        local tt = rTable[mark]
        tt[assetBundleName] = tt[assetBundleName] or 0
        tt[assetBundleName] = tt[assetBundleName] - 1

        if unloadalll then
            oldUnloadfunc(assetBundleName, unloadalll)
        else
            oldUnloadfunc(assetBundleName)
        end
    end

    local oldloadfunc = IOSystem.LoadAssetAsync
    IOSystem.LoadAssetAsync = function(assetBundleName, aName, lcb)
        print("loadAssetBundle", assetBundleName)
        local ifo = debug.traceback("", 2)
        local mark = string.match(ifo, "traceback:\n\t(.-):")

        rTable[mark] = rTable[mark] or {}
        local tt = rTable[mark]
        tt[assetBundleName] = tt[assetBundleName] or 0
        tt[assetBundleName] = tt[assetBundleName] + 1

        oldloadfunc(assetBundleName, aName, lcb)
    end

    local GetAsync = GameObjectPool.GetAsync
    GameObjectPool.GetAsync = function(assetBundleName, assetName, cb)
        print("GetAsync", assetBundleName)

        local ifo = debug.traceback("", 2)
        local mark = string.match(ifo, "traceback:\n\t(.-):")
        rTable[mark] = rTable[mark] or {}
        local tt = rTable[mark]
        tt[assetBundleName] = tt[assetBundleName] or 0
        tt[assetBundleName] = tt[assetBundleName] + 1

        GetAsync(assetBundleName, assetName, cb)
    end
    local Release = GameObjectPool.Release
    GameObjectPool.Release = function(go)
        print("Release", go)

        local assetBundleName = GameObjectPool.GetABNameViaObj(go)
        if assetBundleName then
            local ifo = debug.traceback("", 2)
            local mark = string.match(ifo, "traceback:\n\t(.-):")
            rTable[mark] = rTable[mark] or {}
            local tt = rTable[mark]
            tt[assetBundleName] = tt[assetBundleName] or 0
            tt[assetBundleName] = tt[assetBundleName] - 1
        end
        Release(go)
    end
end

function fixTable(t)
    local l = {}
    local func = nil
    func = function(tt)
        for k, v in pairs(tt) do
            if type(v) == "table" then
                func(v)
            else
                l[k] = l[k] or 0
                l[k] = l[k] + v
            end
        end
    end
    func(t)
    local prettyjsonfix = json.encode(l, {indent = true})
    -- print(prettyjsonfix)

    for k, v in pairs(l) do
        if v == 0 then
            l[k] = nil
        end
    end
    return l
end

function DebugJson()
    local rTable = _G["rTable"]
    if not rTable then
        return
    end

    local fix = fixTable(rTable)

    local prettyjson = json.encode(rTable, {indent = true})
    local prettyjsonfix = json.encode(fix, {indent = true})
    -- print(prettyjson)

    local timeStr = os.date("%Y_%m_%d_%H_%M_%S", os.time())

    local pathRoot = LogDebug.Instance:GetLogFileRootPath()

    local path = pathRoot .. "/Json/Json__" .. timeStr .. ".json"

    if Application.isEditor then
        path = Application.dataPath .. "/Json/Json__" .. timeStr .. ".json"
    end
    log.Warning(path)
    util.WriteFile(path, prettyjson .. "\n" .. prettyjsonfix, "wb")
end

CheckInit()
