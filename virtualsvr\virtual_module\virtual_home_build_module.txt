﻿---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2025/9/5 10:17
--- desc : 家园建筑模块
local pairs = pairs
local table = table
local debug = debug
local ipairs = ipairs
local xpcall = xpcall
local string = string
local require = require
local flow_text = require "flow_text"
local gw_const = require "gw_const"
local virtual_net = require "virtual_net"
local xManMsg_pb = require "xManMsg_pb"
local city_pb = require "city_pb"
local game_scheme = require "game_scheme"
local bit = require("bit")
local bor = bit.bor
local bnot = bit.bnot
local log = require "log"
local json = require "dkjson"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
module("virtual_home_build_module")

local buildingList = {}
local saveBuildingList = {}
local saveRepairMapIdList = {}
local saveOpenGiftMapIdList = {}

local BUILDING_SAVE_KEY = "BUILDING_SAVE_KEY"

--region 持久化 本地方法
local function encode_safe(tbl)
    local ok, s = xpcall(function()
        return json.encode(tbl)
    end, function(err)
        log.Error("json.encode error: " .. tostring(err))
        return nil
    end)
    return ok and s or nil
end

local function decode_safe(str)
    if not str or str == "" then
        return nil
    end
    local ok, res = xpcall(function()
        return json.decode(str)
    end, function(err)
        log.Error("json.decode error: " .. tostring(err) .. "\n" .. debug.traceback())
        return nil
    end)
    return ok and res or nil
end

local function send_city_building_update(uSid, info)
    if not info then
        return
    end
    local nftMsg = city_pb.TMSG_CITY_BUILDING_UPDATE_NTF()
    local ntfBuildInfo = nftMsg.building:add()
    ntfBuildInfo.uSid = uSid
    ntfBuildInfo.nBuildingID = info.nBuildingID
    ntfBuildInfo.nLevel = info.nLevel
    ntfBuildInfo.x = info.x
    ntfBuildInfo.y = info.y
    ntfBuildInfo.nState = info.nState or city_pb.enBuildingState_Normal
    ntfBuildInfo.uFlag = info.uFlag or 0
    ntfBuildInfo.uMapUnitID = info.uMapUnitID
    if virtual_net and virtual_net.SendMessageToClient then
        virtual_net.SendMessageToClient(xManMsg_pb.MSG_CITY_BUILDING_UPDATE_NTF, nftMsg)
    else
        log.Warn("virtual_net.SendMessageToClient not available")
    end
end

local function add_unique(list, val)
    if not val then
        return
    end
    for _, v in ipairs(list) do
        if v == val then
            return
        end
    end
    table.insert(list, val)
end
--endregion

function OnVirtualServerStarted()

end

function OnVirtualServerStoped()

end

function InitRoleCityData()
    --读取本地数据
    ReadLocalData()
    local uSidIndex = 0
    --创建城建数据
    local count = game_scheme:BuildMaincityMap_nums()
    for i = 0, count - 1 do
        local cfg = game_scheme:BuildMaincityMap(i)
        if cfg and cfg.type == 1 and cfg.Hiddenbuildings == 0 then
            uSidIndex = uSidIndex + 1
            local saveInfo = GetSavaBuildingInfoByMapId(cfg.MapID)
            local build = {}
            if saveInfo then
                -- 使用保存的数据（容错字段检查）
                build.nLevel = saveInfo.nLevel or cfg.buildingID.data[1] or 1
                build.nState = saveInfo.nState or (cfg.broken == 1 and city_pb.enBuildingState_Broken or city_pb.enBuildingState_Normal)
                build.uFlag = saveInfo.uFlag or 0
            else
                build.nLevel = cfg.buildingID and cfg.buildingID.data and cfg.buildingID.data[1] or 1
                build.nState = cfg.broken == 1 and city_pb.enBuildingState_Broken or city_pb.enBuildingState_Normal
                build.uFlag = 0
                if cfg.broken == 1 then
                    build.uFlag = bor(build.uFlag, city_pb.enCityFlag_CityWaitRepair)
                end
            end
            build.uSid = uSidIndex
            build.nBuildingID = cfg.buildingID and cfg.buildingID.data and cfg.buildingID.data[0] or 0
            build.x = cfg.x or 0
            build.y = cfg.y or 0
            build.uMapUnitID = cfg.MapID
            buildingList[build.uSid] = build
        end
    end
end

function MSG_PROP_CREATEENTITY_NTF()
    InitRoleCityData()
    local msg = city_pb.TMSG_CITY_GET_ALLINFO_RSP()
    --创建城建数据
    for i, info in pairs(buildingList) do
        local build = msg.building:add()
        build.uSid = info.uSid
        build.nBuildingID = info.nBuildingID
        build.nLevel = info.nLevel
        build.x = info.x
        build.y = info.y
        build.nState = info.nState
        build.uFlag = info.uFlag
        build.uMapUnitID = info.uMapUnitID
    end

    local areaMapCount = game_scheme:BuildAreaMap_nums()
    for i = 0, areaMapCount - 1 do
        local buildAreaCityCfg = game_scheme:BuildAreaMap(i)
        if buildAreaCityCfg and string.IsNullOrEmpty(buildAreaCityCfg.AreaUnlock) then
            local areaInfo = msg.area:add()
            areaInfo.nID = buildAreaCityCfg.AreaID
            areaInfo.uFlag = 0
            areaInfo.uFlag = bor(areaInfo.uFlag, city_pb.enCityFlag_AreaUnlock)
            areaInfo.uFlag = bor(areaInfo.uFlag, city_pb.enCityFlag_AreaCanBuilding)
        end
    end
    local data = msg:SerializeToString()
    return data
end

---@public 修复建筑请求
function MSG_CITY_REPAIR_A_BUILDING_REQ(msg)
    if not msg or not msg.uSid then
        log.Error("MSG_CITY_REPAIR_A_BUILDING_REQ invalid msg")
        return
    end
    local info = buildingList[msg.uSid]
    if not info then
        log.Error("MSG_CITY_REPAIR_A_BUILDING_REQ not found uSid:", msg.uSid)
        return
    end

    info.uFlag = bor(info.uFlag or 0, city_pb.enCityFlag_BuildingOpenGift)
    info.nState = city_pb.enBuildingState_Normal

    -- 广播更新
    send_city_building_update(msg.uSid, info)

    -- 记录持久化相关列表
    add_unique(saveRepairMapIdList, info.uMapUnitID)
    AddSaveBuildingList(info.uMapUnitID, info.nLevel, info.uFlag, city_pb.enBuildingState_Normal)
    SaveLocalData()

    --回复
    local rspMsg = city_pb.TMSG_CITY_REPAIR_A_BUILDING_RSP()
    rspMsg.errCode = 0
    rspMsg.uSid = msg.uSid
    virtual_net.SendMessageToClient(xManMsg_pb.MSG_CITY_REPAIR_A_BUILDING_RSP, rspMsg)
end
---@public 建筑剪彩
function MSG_CITY_GET_BUILDING_OPENGIFT_REQ(msg)
    if not msg or not msg.uSid then
        log.Error("MSG_CITY_GET_BUILDING_OPENGIFT_REQ error")
        return
    end
    local info = buildingList[msg.uSid]
    if not info then
        log.Error("MSG_CITY_GET_BUILDING_OPENGIFT_REQ error", msg.uSid)
        return
    end
    if info.nBuildingID == gw_const.enBuildingType.enBuildingType_Main * 1000 then
        flow_text.Add("配置多语言 - 到达界限 创角还未结束")
        return
    end
    -- 翻转 flag 并升级等级
    info.uFlag = bnot(info.uFlag or 0, bnot(city_pb.enCityFlag_BuildingOpenGift))
    info.nLevel = (info.nLevel or 0) + 1
    info.nState = city_pb.enBuildingState_Normal

    -- 广播更新
    send_city_building_update(msg.uSid, info)

    -- 记录持久化相关列表
    add_unique(saveOpenGiftMapIdList, info.uMapUnitID)
    AddSaveBuildingList(info.uMapUnitID, info.nLevel, info.uFlag, city_pb.enBuildingState_Normal)
    SaveLocalData()

    --回复
    local rspMsg = city_pb.TMSG_CITY_GET_BUILDING_OPENGIFT_RSP()
    rspMsg.errCode = 0
    rspMsg.uSid = msg.uSid
    virtual_net.SendMessageToClient(xManMsg_pb.MSG_CITY_GET_BUILDING_OPENGIFT_RSP, rspMsg)
end

function AddSaveBuildingList(mapId, nLevel, uFlag, nState)
    if not mapId then
        return
    end
    local found = false
    for _, v in ipairs(saveBuildingList) do
        if v.uMapUnitID == mapId then
            v.nLevel = nLevel
            v.uFlag = uFlag
            v.nState = nState
            found = true
            break
        end
    end
    if not found then
        table.insert(saveBuildingList, {
            uMapUnitID = mapId,
            nLevel = nLevel,
            uFlag = uFlag,
            nState = nState
        })
    end
end

function GetSavaBuildingInfoByMapId(mapId)
    for i, v in ipairs(saveBuildingList) do
        if v.uMapUnitID == mapId then
            return v
        end
    end
end

function SaveLocalData()
    local save = {
        saveBuildingList = saveBuildingList or {},
        saveRepairMapIdList = saveRepairMapIdList or {},
        saveOpenGiftMapIdList = saveOpenGiftMapIdList or {}
    }
    local s = encode_safe(save)
    if s then
        PlayerPrefs.SetString(BUILDING_SAVE_KEY, s)
        PlayerPrefs.Save()
    else
        log.Error("SaveLocalData failed to encode json")
    end
end

function ReadLocalData()
    local loaded = PlayerPrefs.GetString(BUILDING_SAVE_KEY, "")
    if loaded == "" then
        saveBuildingList = {}
        saveRepairMapIdList = {}
        saveOpenGiftMapIdList = {}
        return
    end
    local dec = decode_safe(loaded)
    if dec then
        saveBuildingList = dec.saveBuildingList or {}
        saveRepairMapIdList = dec.saveRepairMapIdList or {}
        saveOpenGiftMapIdList = dec.saveOpenGiftMapIdList or {}
    else
        saveBuildingList = {}
        saveRepairMapIdList = {}
        saveOpenGiftMapIdList = {}
    end
end

function DeleteLocalData()
    PlayerPrefs.DeleteKey(BUILDING_SAVE_KEY)
    saveBuildingList = {}
    saveRepairMapIdList = {}
    saveOpenGiftMapIdList = {}
end

function GetSaveRepairMapIdList()
    return saveRepairMapIdList
end

function GetSaveOpenGiftMapIdList()
    return saveOpenGiftMapIdList
end

virtual_net.RegisterMsgHandler(xManMsg_pb.MSG_CITY_REPAIR_A_BUILDING_REQ, MSG_CITY_REPAIR_A_BUILDING_REQ, city_pb.TMSG_CITY_REPAIR_A_BUILDING_REQ)
virtual_net.RegisterMsgHandler(xManMsg_pb.MSG_CITY_GET_BUILDING_OPENGIFT_REQ, MSG_CITY_GET_BUILDING_OPENGIFT_REQ, city_pb.TMSG_CITY_GET_BUILDING_OPENGIFT_REQ)