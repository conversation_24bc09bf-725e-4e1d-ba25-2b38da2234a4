local require = require

local table = table
local pairs = pairs
local ipairs = ipairs
local type = type
local string = string
local math = math

local data_mgr = require "data_mgr"
local event = require "event"
local red_system = require "red_system"
local red_const = require "red_const"
local event_task_define = require "event_task_define"
local lang = require "lang"
local game_scheme = require "game_scheme"
local util = require "util"
local net_national_flag_module = require "net_national_flag_module"
local event_NationalFlag_define = require "event_NationalFlag_define"
local AssetsUpdator = CS.AssetsUpdator
local Application = CS.UnityEngine.Application

local compTable = {}
local compSet = {}
local selfCountryID = nil
local allianceCountryID
local countDown
local countDownAlliance = 0

local createAllianceNationalFlagID

local countryIDAtEditor

local convertCountry = {
    ["Taiwan"] = 9, -- 台湾
    ["Hong Kong"] = 9, -- 香港
    ["Macau"] = 9, -- 澳门
}

module("national_flag_mgr")

---所有数据存储
local _d = data_mgr:CreateData("national_flag_mgr")
---非服务器数据存储
local mc = _d.mde.const

function Init()
    local isoCodeUrl = "https://api-ea.q1.com/ip/api/ips/selfcountry"
    local http_inst = require "http_inst"
    event.Trigger(event.GAME_EVENT_REPORT, "GetISOCode_start", {})
    http_inst.Req_Timeout(isoCodeUrl, 2, function(data, hasError)
        if not hasError and data then
            local country_EN = string.match(data, '"country_EN"%s*:%s*"(.-)"')
            -- 遍历country表
            for i = 1, game_scheme:Country_nums() - 1 do
                local countryData = game_scheme:Country_0(i)
                if countryData then
                    if lang.Get(countryData.name, EN) == country_EN then
                        countryIDAtEditor = i
                    end
                end
            end
            -- 遍历 convertCountry，countryIDByIso转为对应的值
            for k, v in pairs(convertCountry) do
                if k == country_EN then
                    countryIDByIso = v
                end
            end
            if countryIDAtEditor == nil then
                -- 默认联合国
                countryIDAtEditor = 217
            end
        end
    end)
    -- 监听服务器消息
    event.Register(event_NationalFlag_define.ON_NATIONAL_FLAG_REFRESH, SetNationalFlagIDByServer)
end

-- 当条件发生变化时，对国旗进行处理
function OnNationalFlagRefresh()
    -- 当关闭时对所有国旗图片组件进行处理
    NationalFlagComp()
    if CheckNationalFlagIsOpen() then
    else
        -- 关闭
        for _, v in pairs(compTable) do
            if not (require "util").IsObjNull(v) then
                v.gameObject:SetActive(false)
            end
        end
    end
end

-- TODO 国旗功能待修改
--- 检测当前玩家自身的国家旗帜功能是否开启
function CheckNationalFlagIsOpen()
    -- 1.包体开关是否开启
    local currChannel = util.GetChannelTag()
    local unShowChannel = string.split(game_scheme:InitBattleProp_0(8512).szParamString, "#")
    for _, v in pairs(unShowChannel) do
        if v and v == currChannel then
            return false
        end
    end
    -- 2.ModuleOpen
    local net_module_open = require "net_module_open"
    local moduleOpenPro_pb = require "moduleOpenPro_pb"
    local isShow = net_module_open.GetModuleIsOn(moduleOpenPro_pb.emModuleID_NationalFlag)
    if not isShow then
        return false
    end
    -- 3.玩家当前语言
    local currLang = lang.GetUseLang()
    local langList = lang.GetLangListData()
    local unShowFlagLang = game_scheme:InitBattleProp_0(8511).szParam.data
    for _, v in pairs(unShowFlagLang) do
        if v and currLang == langList[v].Lang then
            return false
        end
    end
    -- 4.注册国家
    if selfCountryID == nil then
        return false
    end
    ---- 以上条件都满足则返回true
    return true
end

--- 获取自身国旗信息并缓存
function GetSelfNationalFlagInfo()
    -- 获取自身国旗信息
    if selfCountryID == 0 then
        selfCountryID = 217
    end
    return selfCountryID
end

--- 设置国家旗帜信息
--- @param compImage Image组件
--- @param countryID 国家旗帜ID_Country表_为空时表示显示的是自身的国旗
function SetNationalFlagInfo(wnd, compImage, countryID)
    if compImage == nil then
        return
    end
    if CheckNationalFlagIsOpen() == false then
        compImage.gameObject:SetActive(false)
        return
    end
    if countryID == nil then
        ShowNationalFlagUI(wnd, compImage, GetSelfNationalFlagInfo())
    else
        -- 给定nationalFlagID的国旗
        ShowNationalFlagUI(wnd, compImage, countryID)
    end
    compImage.gameObject:SetActive(true)
end

--- 显示国家旗帜界面
--- @param compImage Image组件
--- @param countryID 国家旗帜ID_Country表
--- @param wnd 当前窗口
function ShowNationalFlagUI(wnd, compImage, countryID)
    -- 自身国旗
    NationalFlagComp()
    -- 避免添加重复组件，虚拟列表等会隐藏的情况
    if not compSet[compImage] then
        compSet[compImage] = wnd
        table.insert(compTable, compImage)
    end
    -- 通过nationalFlagID获取nationalFlag图片
    local nationFlagConfig = game_scheme:Country_0(countryID)
    local img = nationFlagConfig.information
    -- 在 compImage 设置图片
    wnd:CreateSubSprite("CreateNationalFlagAsset", compImage, img)
end

--- 处理无效图片组件
function NationalFlagComp()
    -- 倒序遍历，直接移除无效项
    for i = #compTable, 1, -1 do
        if (require "util").IsObjNull(compTable[i]) then
            compSet[compTable[i]] = nil
            table.remove(compTable, i)
        end
    end
end

--- 获取玩家国家ID,这个值要发给服务器
function GetPlayerCountryIDByISO()
    local country_EN
    local countryIDByIso
    
    -- 编辑器下
    if Application.isEditor then
        return countryIDAtEditor
    else
        -- 获取country_EN
        if AssetsUpdator.ISOReqStr == nil or AssetsUpdator.ISOReqStr == "" then
            country_EN = "default"
        else
            local kv = string.gmatch(AssetsUpdator.ISOReqStr, '\"country_EN\":\"(%a+)\"')
            for k in kv do
                country_EN = k
                isSucceed = true
                print("初始化区域码成功:", country_EN)
                break
            end
        end
        -- 遍历country表
        for i = 1, game_scheme:Country_nums() - 1 do
            local countryData = game_scheme:Country_0(i)
            if countryData then
                if lang.Get(countryData.name, EN) == country_EN then
                    countryIDByIso = i
                end
            end
        end
        -- 遍历 convertCountry，countryIDByIso转为对应的值
        for k, v in pairs(convertCountry) do
            if k == country_EN then
                countryIDByIso = v
            end
        end
        
        if countryIDByIso == nil then
            -- 默认联合国
            countryIDByIso = 217
        end
        return countryIDByIso
    end
end

--- 发送玩家国家ID、包名给服务器
function SendCountryIDToServer()
    local countryIDByIso = GetPlayerCountryIDByISO()
    if countryIDByIso == nil then
        print("没有找到对应的国家ID,无法发送给服务器")
        return
    end
    -- 发送给服务器
    net_national_flag_module.MSG_NATIONAL_FLAG_COUNTRYID_REQ({ countryID = countryID })
end

--- 设置，自身国旗ID，仅在服务器回包时设置
function SetNationalFlagIDByServer(content)
    if content == nil then
        return
    end
    -- 根据服务器发来的消息设置玩家自身的国旗
    selfCountryID = content.nationalFlagID
    OnNationalFlagRefresh()
end

--- 获取，自身切换国旗倒计时
function GetChangeFlagCountDownEndTime()
    local countDownCfg = game_scheme:InitBattleProp_0(8514).szParam.data
    return countDown + countDownCfg[0]
end

--- 获取 联盟切换国旗倒计时
function GetChangeFlagCountDownEndTimeAlliance()
    local countDownCfg = game_scheme:InitBattleProp_0(8516).szParam.data
    return countDownAlliance + countDownCfg[0]
end

--- 存储，设置创建联盟时的国旗ID
function SetCreateAllianceNationalFlagID( data )
    createAllianceNationalFlagID = data
end

--- 存储，获取创建联盟时的国旗ID
function GetCreateAllianceNationalFlagID( )
    return createAllianceNationalFlagID
end

--- 设置个人国旗ID+倒计时，依赖服务器的数据，仅在回包之后执行
function SetNationalFlagInfoByServer(roleInfo)
    selfCountryID = roleInfo.nationalFlagID
    if roleInfo.nationalFlagTime then
        countDown = roleInfo.nationalFlagTime
    end
    OnNationalFlagRefresh()
end

--- 设置联盟国旗ID+倒计时，依赖服务器的数据，仅在回包之后执行
function SetAllianceNationalFlagInfoByServer(roleInfo)
    if roleInfo == nil then
        return
    end
    if roleInfo.nationalFlagID then
        allianceCountryID = roleInfo.nationalFlagID
    end
    if roleInfo.nationalFlagTime then
        countDownAlliance = roleInfo.nationalFlagTime
    end
end

--- 获取联盟国旗ID
function GetAllianceNationalFlagID()
    return allianceCountryID
end

--- 请求切换自身国旗
function RequestChangeSelfNationalFlag(countryID)
    -- 请求服务器切换国旗
    net_national_flag_module.MSG_NATIONAL_FLAG_CHANGE_REQ({
        flagType = 1, -- 个人国旗
        countryID = countryID
    })
end

--- 请求切换联盟国旗
function RequestChangeAllianceNationalFlag(countryID)
    -- 请求服务器切换国旗、联盟
    net_national_flag_module.MSG_NATIONAL_FLAG_CHANGE_REQ({
        flagType = 2, -- 联盟国旗
        countryID = countryID
    })
end

function Clear()
    _d.mde:clear()
    mc = _d.mde.const
end