local setmetatable = setmetatable
local tostring     = tostring
local require      = require
local math = math

local lang        = require "lang"
local battle_data = require "battle_data"
local game_scheme = require "game_scheme"
local ui_select_model_node = require "ui_select_model_node"

module("battle_config")

--死亡时音效
local _defaultDie = "art/skill/common/common_die.playable"
--如果多人同时死亡时，只播放一次音效，其余不播放音效
local _defaultDieNoAudio = "art/skill/common/common_die_noaudio.playable"

--死亡时音效,带破碎效果
local _defaultDieCrazing = "art/skill/common/common_die_crazing.playable"
--如果多人同时死亡时，只播放一次音效，其余不播放音效,带破碎效果
local _defaultDieCrazingNoAudio = "art/skill/common/common_die_crazing_noaudio.playable"

function GetDefaultDieBundleName(isNoAudio)
    local util = require "util"
    if util.IsEnableCrazingBehaviour() then
        if isNoAudio then
            return _defaultDieCrazingNoAudio
        else
            return _defaultDieCrazing
        end
    else
        if isNoAudio then
            return _defaultDieNoAudio
        else
            return _defaultDie
        end
    end
end

--假死
local _defaultFakeDead = "art/skill/common/common_fake_die.playable"
function GetDefaultFakeDeadBundleName()
    return _defaultFakeDead
end


defaultHit = "art/skill/common/common_hit.playable"
defaultRst = "art/skill/utility/default_reset.playable"
defaultAtk = "art/skill/common/common_atk.playable"
defaultSuspend = "art/maps/battle/guide/missamerican.prefab"

faction = {
    "duoluo",
    "chaoren",
    "jixie",
    "ziran",
    "lvzhe",
    "shenming"
}

BattleErrorCode = {
    NoBattlePlayer = 1,
}

-- 战斗背景
local battleBackground = nil

function GetConfigField(cfg, field, default)
    if cfg == nil then
        return default
    end
    local filedValue = cfg[field]
    if filedValue ~= nil and filedValue ~= "" then
        return filedValue
    else
        return default
    end
end

----------------------------------------------------------
DefaultEN = {
    __index = function(mytable, key)
        return mytable[lang.EN]
    end
}

local PreludeRes = 
{
    [lang.ZH] = "art/skill/common/common_prelude_zh.playable",
    [lang.EN] = "art/skill/common/common_prelude.playable",
}

local CommonActiveArrayRes = 
{
    [lang.ZH] = "art/skill/common/common_active_array_zh.playable",
    [lang.EN] = "art/skill/common/common_active_array.playable",
}

CommonActiveArrayFloatText =  "art/skill/common/common_active_array_float_text.playable"

local BossCommingEffectPath = 
{
    [lang.ZH] = "art/skill/common/common_boss_comming_zh.playable",
    [lang.EN] = "art/skill/common/common_boss_comming_en.playable",
}

local BubbleFontSize = 
{
    [lang.ZH] = 26,
    [lang.EN] = 22,
}

local Language = 
{
    [lang.ZH] = lang.ZH,
    [lang.EN] = lang.EN,
}

local weaponEnterRes = "art/skill/common/common_weapon_enter.playable"

setmetatable(PreludeRes, DefaultEN)
setmetatable(CommonActiveArrayRes, DefaultEN)
setmetatable(BubbleFontSize, DefaultEN)
setmetatable(Language, DefaultEN)

function GetPreludeTimelineRes()
    return PreludeRes[lang.USE_LANG]
end

function GetCommonActiveArrayRes()
    return CommonActiveArrayRes[lang.USE_LANG]
end

function GetBubbleFontSize()
    return BubbleFontSize[lang.USE_LANG]
end

function GetBossCommingEffectPath()
    local path = BossCommingEffectPath[lang.USE_LANG]
    if path == nil then
        path = BossCommingEffectPath[lang.EN]
    end
    return path
end

function GetWeaponEnterRes()
    return weaponEnterRes
end

---------------------------------------------------------

function GetCutsceneRes(cutsceneID)
    return "art/maps/battle/cutscene/scene1shot".. cutsceneID ..".prefab"
end
---------------------------------------------------------

function GetBuffIconData(buffCfg)
    local icon = nil
    if buffCfg and buffCfg.IconID ~= nil and buffCfg.IconID~="" and buffCfg.IconID ~= "0" and buffCfg.IconID ~= 0 then
        icon = tostring(buffCfg.IconID)
    end
    return icon
end

function GetBuffFxData(buffCfg)
    local particleType = nil
    local particleRes = nil

    if buffCfg and buffCfg.unLightEfficiencyID and buffCfg.unLightEfficiencyID ~= 0 then
        particleType, particleRes = GetParticle(buffCfg.unLightEfficiencyID)
    end
    return particleType, particleRes
end

function GetParticle(id)
    local particleType = nil
    local particleRes = nil
    local particle = game_scheme:Particle_0(id)
    if particle and particle.strResPath ~= nil and particle.strResPath ~= "" then
        particleRes = particle.strResPath
        particleType = particle.nType
    end
    return particleType, particleRes
end

function GetBuffFloatTextData(buffCfg)
    local style = nil
    local text = nil
    local colorInfos = nil --字体颜色信息1顶部渐变色2底部渐变色3描边

    if buffCfg and buffCfg.nFloatTextID ~= nil  and buffCfg.nFloatTextID ~= "" and buffCfg.nFloatTextID ~= 0 then
        local ftCfg = game_scheme:FloatText_0(buffCfg.nFloatTextID)
        if not ftCfg then
            return
        end
        --组合飘字处理,使用tab分割
        if ftCfg.nIsArtFont == 3 then
            local subItems = ftCfg.strcom.data
            local itemCgf
            local styleName 
            local subText
            
            local colorInfo
            for index = 0, ftCfg.strcom.count do
                itemCgf = game_scheme:FloatText_0(subItems[index])
                if itemCgf then 
                    styleName = GetBuffFloatTextStyleName(itemCgf.strStyle)
                    subText = GetBuffFloatText(itemCgf)
                    style = style and style  .. "\t" .. styleName or styleName
                    text = text and text  .. "\t" .. subText or subText
                    if itemCgf.topColor ~= "" then
                        colorInfo = itemCgf.topColor .. "-" .. itemCgf.btnColor .. "-" .. itemCgf.OutLine
                    else
                        colorInfo = ""
                    end
                    colorInfos = colorInfos and colorInfos  .. "\t" .. colorInfo or colorInfo
                end
            end
        else
            style = GetBuffFloatTextStyleName(ftCfg.strStyle)
            text = GetBuffFloatText(ftCfg)
            if ftCfg.topColor ~= "" then
                colorInfos = ftCfg.topColor .. "-" .. ftCfg.btnColor .. "-" .. ftCfg.OutLine
            end
        end
    end
    return style, text, colorInfos
end


function GetBuffFloatText(ftCfg)
    if not ftCfg then 
        return nil
    end
    if ftCfg.nIsArtFont == 1 then 
        return lang.Get(ftCfg.strText)
    elseif ftCfg.nIsArtFont == 2 then 
        return lang.Get(ftCfg.strLang)
    end
    return nil
end

function GetBuffFloatTextStyleName(style)
    return style .. "_" .. (Language[lang.USE_LANG] or lang.EN)
end

-------------------------------------------------------------
function SelectRandomIndex(player, ids, probabilityDensity)
    local r = player.random:Range(0, 10000)
    local index = nil 
    local distribution = 0
    for i = 0, probabilityDensity.count-1 do 
        distribution = distribution + probabilityDensity.data[i]
        if r < distribution then
            index = i
            break
        end
    end
    return index
end

function GetBubbleSkillData(player, skillId, palID)
    local text = nil
    local soundID = nil

    local bubbleCfg = game_scheme:BubbleSkill_0(skillId)
    --皮肤技能cv 
    local util = require "util"
    local hero = battle_data.GetHeroByID(palID)
    local bubbleSkillCfg = nil
    if hero and hero.skinID and hero.skinID > 0 then
        bubbleSkillCfg = game_scheme:SkinBubbleSkill_0(hero.skinID, skillId)
        if bubbleSkillCfg and (not bubbleSkillCfg.soundIDs or util.get_len(bubbleSkillCfg.soundIDs.data) <= 0) then --皮肤没有配技能CV 就用默认展示CV
            bubbleSkillCfg = nil
        end
    end
    
    if bubbleCfg ~= nil then
        local index = SelectRandomIndex(player, bubbleCfg.nLangID, bubbleCfg.nProbability)
        if index ~= nil then
            text = bubbleCfg.nLangID and bubbleCfg.nLangID.data and bubbleCfg.nLangID.data[index] and lang.Get(bubbleCfg.nLangID.data[index],nil,nil,true)
            --皮肤技能cv
            if bubbleSkillCfg then
                soundID = bubbleSkillCfg.soundIDs and bubbleSkillCfg.soundIDs.data and bubbleSkillCfg.soundIDs.data[index] or 0
            else
                soundID = bubbleCfg.soundIDs and bubbleCfg.soundIDs.data and bubbleCfg.soundIDs.data[index] or 0
            end
        end
    end
    return text, soundID
end

--获取技能详情，用于大招前介绍弹窗
function GetBubbleSillDetail(skillID)
    local bubbleCfg = game_scheme:BubbleSkill_0(skillID)
    -- local skillConfig = game_scheme:Skill_0(skillID)

    if bubbleCfg then
        local data = {}
        -- data["skill"] = lang.Get(skillConfig.strName)
        -- data["desc"] = lang.Get(skillConfig.strDesc)
        data["name"] = lang.Get(bubbleCfg.heroName or lang.KEY_FRIEND_ERROR_CODE_ABNORMAL)
        data["image"] = bubbleCfg.heroImage
        return data
    else
        local log = require"log"
        log.Error("获取技能详情时失败，请检查配置，skill id="..skillID)
        return nil
    end
end

function GetBubbleHeroData(player, heroId)
    local text = nil
    local soundID = nil

    local bubbleCfg = game_scheme:BubbleHero_0(heroId)
    if bubbleCfg ~= nil then
        local index = SelectRandomIndex(player, bubbleCfg.nLangID, bubbleCfg.nProbability)
        if index ~= nil then
            text = lang.Get(bubbleCfg.nLangID.data[index])
            soundID = bubbleCfg.soundIDs.data[index] or 0
        end
    end
    return text, soundID
end

function GetBubbleHeroDataByPalID(palID)

end

------------------------------------------------------------
function GetSuspendResBeforeSkill(palId, skillRes)
    
    if battle_data.stageType == 1 and battle_data.stageLv < 2 then
        local h = battle_data.GetHeroByID(palId)
        --if h ~= nil and h.pos < 6 and skillRes == "art/skill/100_zhihuinvshen/100_zhihuinvshen_skill.playable" then    
            --[[
        if h ~= nil and h.pos < 6 and skillRes == "art/skill/63_cibaozhanxiong/63_cibaozhanxiong_attack.playable" then
            --player:SetupSuspend(SkillContext.casterID, battle_config.defaultSuspend)
            return defaultSuspend
        end]]
        --[[if h ~= nil and h.pos < 6 and skillRes == "art/skill/47_shenhaijubo/47_shenhaijubo_skill.playable" then
            --player:SetupSuspend(SkillContext.casterID, battle_config.defaultSuspend)
            return defaultSuspend
        end]]
    end

    return nil
end

-- return: 第一个参数：战斗背景 assetbundle name，第二个参数：错误码(BattleErrorCode)
function GetBattleBackground(stageType, stageLevel)
    -- if stageType == 1 and stageLevel <= 10 then
    --     battleBackground = "art/maps/battle/background/changjing_03.prefab"
    --     return battleBackground
    -- else
    --     --由于 battle_config来管理每场战斗所使用的背景图，而不再依赖 C# 中 BattleBackgroundLoader 完全不可控的随机
    --     if battleBackground == nil then
    --         --随机一个背景
    --         local battle_player = require "battle_player"
    --         local player = battle_player.GetBattlePlayer()
    --         if not player then 
    --             return battleBackground,BattleErrorCode.NoBattlePlayer
    --         end

    --         battleBackground = player.backgroundDirectory:SelectBackround()
    --     end
    --     return battleBackground
    -- end
    --local temp,value = ui_select_model_node.GetBackgroundSprite(true)
    --local log = require "log"
    --log.Error(temp)
    return ui_select_model_node.GetBackgroundSprite(true)
end

--选择战斗背景
function SelectBattleBackground()
    local battle_player = require "battle_player"
    local player = battle_player.GetBattlePlayer()
    if not player then return end

    local groundPath, Yoffset = GetBattleBackground()
    player:SetBackground(groundPath, Yoffset)
    --每随机一次背景，只能使用一次
    battleBackground = nil
end

-- 检测是否触发引导

function CheckIsExcuteGuide(player, round)
    local button_tips_trigger = require "button_tips_trigger"
    local battle_manager = require "battle_manager"
    local laymain_data = require "laymain_data"

    local speed = math.floor(battle_manager.GetPlaySpeed()+0.5)
    local playMaxSpeed = game_scheme:InitBattleProp_0(153).szParam.data[0] * 0.01 

    local battle_manager = require"battle_manager"
    local battle_type = battle_manager.GetLastBattle()
    button_tips_trigger.SetCacheLevel(laymain_data.GetNextLevelCfg().checkPointID) -- 缓存 关卡数据
    
    local story_mgr = require "story_mgr"
    local lv = story_mgr.GetCacheLevel()
    local isCanExcute = story_mgr.PlayStory(4,lv)
    
    --if isCanExcute then
    --    if player then
    --        player:SetupSuspendNew(1,button_tips_trigger.emptyBgSource,story_mgr.CheckStoryIsComplete)
    --    end
    --else
    --    if battle_type and speed < playMaxSpeed and round <= 1 and button_tips_trigger.IsCanExcute(26) then
    --        if player then
    --            player:SetupSuspend(1,button_tips_trigger.emptyBgSource)
    --        end
    --        button_tips_trigger.SetPrecondition(26,true)
    --    end
    --end
    -- if battle_type and speed < playMaxSpeed and round <= 1 and button_tips_trigger.IsCanExcute(26) then
    --     if player then
    --         player:SetupSuspend(1,button_tips_trigger.emptyBgSource)
    --     end
    --     button_tips_trigger.SetPrecondition(26,true)
    -- end
end

--技能名相关配置
local skillNameConfigCache = {}
function GetBattleSkillName(heroData)
    if skillNameConfigCache[heroData.heroID] then
        return skillNameConfigCache[heroData.heroID]
    end
    local cfg_hero = game_scheme:Hero_0(heroData.heroID)
    if cfg_hero then
        local heroType = cfg_hero.type
        local rarityType =cfg_hero.rarityType
        local config = game_scheme:BattleSkillName_0(rarityType,heroType)
        skillNameConfigCache[heroData.heroID] = config
        return config
    end
end