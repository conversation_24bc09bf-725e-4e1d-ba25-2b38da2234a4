--@region FileHead
-- ui_ads_loading.txt ---------------------------------
-- author:  无名氏
-- date:    1/20/2021 4:37:01 PM
-- ver:     1.0
-- desc:    Description
-------------------------------------------------
--@endregion 

--@region Require
local require   = require

local Button        = CS.UnityEngine.UI.Button

local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local util                  = require "util"
local lottery_data          = require "lottery_data"
local ui_window_mgr         = require "ui_window_mgr"
--@endregion 

--@region ModuleDeclare
module("ui_ads_loading")
local window = nil
local UIAdsLoading = {}
--@endregion 

--@region WidgetTable
UIAdsLoading.widget_table = {
--@region User
--@endregion 
}
--@endregion 

--@region WindowCtor
function UIAdsLoading:ctor(selfType)
	self.__base:ctor(selfType)
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function UIAdsLoading:Init()
    self:SubscribeEvents()
    util.DelayCall(lottery_data.timeout,function()
        if ui_window_mgr:IsModuleShown("ui_ads_loading") then
            ui_window_mgr:UnloadModule("ui_ads_loading")
        end
    end)
    
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIAdsLoading:OnShow()

--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnHide
--[[界面隐藏时调用]]
function UIAdsLoading:OnHide()
--@region User
--@endregion 
end --///<<< function

--@region WindowClose
function UIAdsLoading:Close()
    if self:IsValid() then
		self:UnsubscribeEvents()
	end
	self.__base:Close()
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIAdsLoading:SubscribeEvents()
----///<<< Button Proxy Line >>>///-----

end --///<<< function

--@endregion 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIAdsLoading:UnsubscribeEvents()
--@region User
--@endregion 
end --///<<< function

local CUIAdsLoading = class(ui_base, nil, UIAdsLoading)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIAdsLoading()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uiadsloading.prefab", nil, nil, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

--@endregion 

--@region RegisterMsg
MessageTable =
{ --///<<< tableStart
} --///<<< tableEnd
--@endregion 

