--- Created by <PERSON><PERSON>
--- DateTime: 2025/3/7
--- Des: 玩家自己的基地信息,注意玩家自己基地的信息是不销毁的

local require = require

local gw_sand_event_define = require "gw_sand_event_define"
local event = require "event"
local gw_const = require "gw_const"

local SandSelfData = {
    -- 角色初始化状态
    initState = false,
    -- 角色主沙盘sid(账号沙盘)
    zoneSandBoxSid = nil,
    -- 角色主沙盘数据
    mainSandboxInfo = {},
    -- 多地图沙盘数据
    multiMapSandboxInfos = {},
    -- 视野沙盘类型
    visualSandType = nil,
    -- 视野沙盘Sid
    visualSandBoxSid = nil,
    -- 视野基地Sid
    visualBaseSid = nil,
    -- 视野基地坐标
    visualBasePosition = nil,
    -- 视野沙盘实体（进入沙盘后赋值）
    visualBaseEntity = nil,
    -- 视野王城实体（进入沙盘后赋值）
    visualCongressEntity = nil,

    -- 缓存的玩家基地实体
    baseEntityCaches = nil
}

--region 模块懒加载器
local _modules = {}
local function _requireModule(name)
    if not _modules[name] then
        _modules[name] = require(name)
    end
    return _modules[name]
end
--endregion

--region 生命周期
function SandSelfData.Init()

end

function SandSelfData.InitSelfBaseInfo(zoneSandBoxSid, roleSandBoxSid, basePosition, baseSid)
    SandSelfData.zoneSandBoxSid = zoneSandBoxSid
    SandSelfData.mainSandboxInfo = {
        sandboxSid = roleSandBoxSid,
        baseSid = baseSid,
        basePosition = basePosition,
    }
    if not SandSelfData.initState then
        SandSelfData.initState = true
        event.Trigger(gw_sand_event_define.GW_SAND_INIT_SELF_DATA_FINISH)
    end
    event.Trigger(gw_sand_event_define.GW_SAND_UPDATE_SELF_DATA)
end

function SandSelfData.UpdateSelfBaseInfo(exist, roleSandBoxSid, baseSid, basePosition)
    local sandMgr = _requireModule("gw_sand_mgr")
    local sandType = sandMgr.GetSandTypeBySid(roleSandBoxSid)
    local sandInfo = SandSelfData.GetSandInfoByType(sandType)

    if not exist then
        if sandInfo and sandInfo.sandboxSid == roleSandBoxSid then
            SandSelfData.SetSandInfoByType(sandType, nil)
            -- 清除实体数据缓存
            SandSelfData.SetBaseEntityDataCache(sandInfo.sandboxSid, nil)
        end
        return
    end

    SandSelfData.SetSandInfoByType(sandType, {
        sandboxSid = roleSandBoxSid,
        baseSid = baseSid,
        basePosition = basePosition,
    })
    -- 请求预览数据缓存
    SandSelfData.RequestAloneEntityData(roleSandBoxSid, baseSid)
end

function SandSelfData.ClearData()
    SandSelfData.visualSandType = nil
    SandSelfData.visualSandBoxSid = nil
    SandSelfData.visualBaseSid = nil
    SandSelfData.visualBasePosition = nil
    SandSelfData.visualBaseEntity = nil
end

function SandSelfData.Dispose()
    SandSelfData.ClearData()
    SandSelfData.initState = false
    SandSelfData.zoneSandBoxSid = nil
    SandSelfData.baseEntityCaches = nil
    SandSelfData.mainSandboxInfo = {}
    SandSelfData.multiMapSandboxInfos = {}
    event.Trigger(gw_sand_event_define.GW_SAND_DISPOSE_SELF_DATA)
end
--endregion

--region 基地数据缓存
local callIdMarking = 999999

function SandSelfData.RequestAloneEntityData(sandboxSid, entitySid)
    if not sandboxSid or sandboxSid == 0 or not entitySid or entitySid == 0 then
        return
    end
    
    local netSandbox = _requireModule("net_sandbox_module")
    local data = {
        sandboxSid = sandboxSid,
        sid = entitySid,
        nViewLev = 6,
        callId = callIdMarking,
        callback = function(msg)
            SandSelfData.ResponseAloneEntityData(msg)
        end
    }
    netSandbox.MSG_SANDBOX_ALONE_ENTITY_DATA_REQ(data)
end

function SandSelfData.ResponseAloneEntityData(msg)
    local sandPb = _requireModule("sandbox_pb")
    if msg and msg.callId == callIdMarking and msg.entityData and msg.entityData.type == sandPb.enSandboxEntity_Base then
        SandSelfData.SetBaseEntityDataCache(msg.sandboxSid, msg.entityData)
    end
end

function SandSelfData.SetBaseEntityDataCache(sandboxSid, entityData)
    if not sandboxSid then
        return
    end

    if not SandSelfData.baseEntityCaches then
        SandSelfData.baseEntityCaches = {}
    end
    SandSelfData.baseEntityCaches[sandboxSid] = entityData
end

function SandSelfData.GetBaseEntityDataCache(sandboxSid)
    if not sandboxSid or not SandSelfData.baseEntityCaches then
        return
    end

    return SandSelfData.baseEntityCaches[sandboxSid]
end
--endregion

---@public 设置沙盘信息,通过沙盘的type
function SandSelfData.SetSandInfoByType(sandType, sandInfo)
    if sandType == gw_const.SandType[gw_const.ESceneType.Sand] then
        SandSelfData.mainSandboxInfo = sandInfo or {
            sandboxSid = SandSelfData.zoneSandBoxSid,
            baseSid = 0,
            basePosition = { x = -1, y = -1 }
        }
    else
        SandSelfData.multiMapSandboxInfos[sandType] = sandInfo
    end
    -- 发送信息变更消息
    if SandSelfData.initState then
        event.Trigger(gw_sand_event_define.GW_SAND_UPDATE_SELF_DATA)
    end
end

---@public 设置玩家视野服数据
function SandSelfData.SetSelfVisualSandBoxSid(sandBoxSid, sandType)
    SandSelfData.visualSandBoxSid = sandBoxSid
    SandSelfData.visualSandType = sandType

    local sandInfo = SandSelfData.GetSandInfoByType(sandType)
    if sandInfo and sandInfo.sandboxSid == sandBoxSid then
        SandSelfData.visualBaseSid = sandInfo.baseSid
        SandSelfData.visualBasePosition = sandInfo.basePosition
    end

    -- 发送视野变更消息
    event.Trigger(gw_sand_event_define.GW_SAND_VISUAL_CHANGE)
end

---@public 获得视野沙盘玩家位置坐标
function SandSelfData.SetSelfVisualSandBoxInfo(sid, position)
    SandSelfData.visualBaseSid = sid
    SandSelfData.visualBasePosition = position
end

---@public 设置玩家实体数据
function SandSelfData.SetSelfVisualBaseEntity(entity)
    if entity and entity.serData and not entity.serData.MarchEntityInScene and entity.serData:GetIfBase() and entity.serData:GetIfSelf() then
        SandSelfData.visualBaseEntity = entity
        -- 校准一次数据
        SandSelfData.visualBaseSid = entity.serData.sid
        SandSelfData.visualBasePosition = { x = entity.serData.pos.x, y = entity.serData.pos.y }
        --数数打点
        local gw_sand_event_report_helper = require "gw_sand_event_report_helper"
        gw_sand_event_report_helper.CreateSandBoxSelfData(entity)

        -- 刷新一下缓存
        SandSelfData.RequestAloneEntityData(SandSelfData.visualSandBoxSid, SandSelfData.visualBaseSid)
        return true
    end
end

---@public 设置视野王城数据
function SandSelfData.SetVisualCongressEntity(entity)
    if entity and entity.serData and not entity.serData.MarchEntityInScene and entity.serData.cfg.congress == 1 then
        SandSelfData.visualCongressEntity = entity
        event.Trigger(gw_sand_event_define.GW_SAND_UPDATE_CONGRESS_ENTITY)
        return true
    end
end

---@public 获取沙盘信息,通过沙盘的type
function SandSelfData.GetSandInfoByType(sandType)
    if sandType == gw_const.SandType[gw_const.ESceneType.Sand] then
        return SandSelfData.mainSandboxInfo
    else
        return SandSelfData.multiMapSandboxInfos[sandType]
    end
end

---@public 获得玩家沙盘sid
function SandSelfData.GetZoneSandBoxSid()
    return SandSelfData.zoneSandBoxSid
end

---@public 获得玩家视野沙盘sid
function SandSelfData.GetSandBoxSid()
    return SandSelfData.visualSandBoxSid or SandSelfData.mainSandboxInfo.sandboxSid
end

---@public 获得玩家基地沙盘sid
function SandSelfData.GetRoleSandBoxSid()
    return SandSelfData.mainSandboxInfo.sandboxSid
end

---@public 获得玩家实体Sid
function SandSelfData.GetSelfBaseSid()
    return SandSelfData.mainSandboxInfo.baseSid
end

---@public 获得玩家实体坐标
function SandSelfData.GetSelfBasePosition()
    return SandSelfData.mainSandboxInfo.basePosition
end

---@public 获得玩家视野沙盘sid
function SandSelfData.GetVisualSandBoxSid()
    return SandSelfData.visualSandBoxSid
end

---@public 获取视野沙盘类型
function SandSelfData.GetCurSandType()
    return SandSelfData.visualSandType
end

---@public 获得玩家视野实体数据
function SandSelfData.GetSelfBaseEntity()
    return SandSelfData.visualBaseEntity
end

---@public 获得视野沙盘玩家位置坐标
function SandSelfData.GetSelfVisualSandBoxSid()
    return SandSelfData.visualBaseSid or SandSelfData.mainSandboxInfo.baseSid
end

---@public 获得视野沙盘玩家位置坐标
function SandSelfData.GetSelfVisualSandBoxPosition()
    return SandSelfData.visualBasePosition or SandSelfData.mainSandboxInfo.basePosition
end

---@public 是否是视野和基地同服状态
function SandSelfData.IsVisualAndBaseCoServiceState()
    if SandSelfData.visualSandType ~= nil then
        if SandSelfData.visualSandType == gw_const.SandType[gw_const.ESceneType.Sand] then
            return SandSelfData.visualSandBoxSid == SandSelfData.mainSandboxInfo.sandboxSid
        else
            local sandInfo = SandSelfData.GetSandInfoByType(SandSelfData.visualSandType)
            return sandInfo and SandSelfData.visualSandBoxSid == sandInfo.sandboxSid
        end
    else
        return false
    end
end

---@public 是否是视野跨服状态
function SandSelfData.IsVisualCrossServiceState()
    if SandSelfData.zoneSandBoxSid and SandSelfData.visualSandType == gw_const.SandType[gw_const.ESceneType.Sand] then
        return SandSelfData.visualSandBoxSid ~= SandSelfData.zoneSandBoxSid
    else
        return false
    end
end

---@public 是否是基地跨服状态
function SandSelfData.IsBaseCrossServiceState()
    if SandSelfData.zoneSandBoxSid and (SandSelfData.visualSandType == nil or SandSelfData.visualSandType == gw_const.SandType[gw_const.ESceneType.Sand]) then
        return SandSelfData.mainSandboxInfo.sandboxSid ~= SandSelfData.zoneSandBoxSid
    else
        return false
    end
end

---@public 是否是跨服状态
function SandSelfData.IsCrossServiceState()
    return SandSelfData.IsVisualCrossServiceState() or SandSelfData.IsBaseCrossServiceState()
end

---@public 获得视野王城数据
function SandSelfData.GetVisualCongressEntity()
    return SandSelfData.visualCongressEntity
end

return SandSelfData
