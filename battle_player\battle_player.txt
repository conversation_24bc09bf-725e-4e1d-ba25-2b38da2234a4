local typeof = typeof
local require = require
local UIUtil = CS.Common_Util.UIUtil

local util = require "util"
local tbs_pb = require "tbs_pb"
local base_game_object = require "base_game_object"

local BattlePlayer = CS.War.Battle.BattlePlayer

module("battle_player")

local mainPlayer = nil
local MainBattleZone = nil
local sceneLight = nil

function ParseReport(msg, playByTargetReport)
    InitializeMainPlayer()

    local parser_v0 = require "battle_parser_v0"
    local parser_v1 = require "battle_parser_v1"

    local tbs_version = 0
    if #msg.reports > 1 then
        local firstReport = msg.reports[1]
        if firstReport.reportType == tbs_pb.ReportType_MiscReport then
            tbs_version = firstReport.miscReport.nVersion
        end
    end

    if BattlePlayer and BattlePlayer.Instance then
        if BattlePlayer.Instance.hub then
            BattlePlayer.Instance.hub.outsideManager:Clear()
        end
    end

    if tbs_version == 0 then
        parser_v0.ParseReport(mainPlayer, msg)
    elseif tbs_version == 1 then
        parser_v1.ParseReport(mainPlayer, msg, playByTargetReport)
    end
end

function GetBattlePlayer()
    if mainPlayer == nil or util.IsObjNull(mainPlayer) then
        InitializeMainPlayer()
    end
    return mainPlayer
end

function InitializeMainPlayer()
    if mainPlayer ~= nil and (not util.IsObjNull(mainPlayer)) then
        return
    end
    if MainBattleZone == nil or util.IsObjNull(MainBattleZone) then
        return
    end
    local go = MainBattleZone.transform:Find("ComicBattlePlayer")
    -- 可能是在载入Lobby场景时就收到战斗消息。很可疑
    if not go then
        mainPlayer = nil
        return
    end

    mainPlayer = go:GetComponent(typeof(BattlePlayer))
end

function GetMainBattleZoneObj()
    return MainBattleZone and MainBattleZone.transform or nil
end

function GetScreenLightObj()
    return sceneLight and sceneLight.transform or nil
end

function LoadMainPlayer()
    if MainBattleZone and not util.IsObjNull(MainBattleZone) then
        return
    end
    base_game_object("battle_player"):LoadResource(
            "art/battleplayer/mainbattlezone.prefab",
            nil,
            function(obj)
                if obj then
                    UIUtil.SetWorldPos(obj.transform, -1000, -10, -1000)
                    MainBattleZone = obj
                    sceneLight = obj.transform:Find("ComicBattlePlayer/SceneLight").gameObject
                end
            end
    )
end

function ShowSceneLight(isActive)
    if sceneLight and not util.IsObjNull(sceneLight) then
        sceneLight.gameObject:SetActive(isActive)
    end
end
