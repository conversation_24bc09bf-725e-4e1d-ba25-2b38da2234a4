{
    "editor.wordWrap": "on",
    "files.associations": {
        "*.txt": "lua",
        "luaide.apiType":"xlua"
    },
    "files.exclude": {
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/CVS": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "**/*.meta": true,
        "**/*.asset": true,
        "Tables/en_oumei": true
    },
    "workbench.colorCustomizations": {
        "activityBar.background": "#3A1D63",
        "titleBar.activeBackground": "#52298A",
        "titleBar.activeForeground": "#FCFBFE"
    },
    "editor.snippetSuggestions": "bottom",
    "explorerExclude.backup": {},
}