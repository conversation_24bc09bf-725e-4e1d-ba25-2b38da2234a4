
local require = require
local net_script = require "net_script"
local player_mgr = require "player_mgr"
local game_scheme 	= require "game_scheme"
local game_config   =       require "game_config"
local PlayerPrefs   = CS.UnityEngine.PlayerPrefs
local ChangePackgeConfig = {}

local _CPFO = "_ChangePackge_FistOpen"
ChangePackgeConfig.GetFistOpenChangePackgeStatus = function ()
    return PlayerPrefs.GetInt(_CPFO .. player_mgr.GetPlayerRoleID(), 0)
end
ChangePackgeConfig.setFistOpenChangePackgeStatus = function (set)
   PlayerPrefs.SetInt(_CPFO .. player_mgr.GetPlayerRoleID(), set)
end
local _CPFD = "_ChangePackge_FistDown"
ChangePackgeConfig.GetFistDownChangePackgePage = function ()
    return PlayerPrefs.GetInt(_CPFD .. player_mgr.GetPlayerRoleID(), 0)
end
ChangePackgeConfig.setFistDownChangePackgePage = function (set)
   PlayerPrefs.SetInt(_CPFD .. player_mgr.GetPlayerRoleID(),set)
end
ChangePackgeConfig.ChangeData = {}
ChangePackgeConfig.Load = function ()
    local count = game_scheme:ChangeApk_nums()
    for i= 0,count do
        local data = game_scheme:ChangeApk(i)
        if data then
            if data.A_pid == game_config.CHANNEL_ID then
                ChangePackgeConfig.ChangeData.nParticleID = data.nParticleID
                ChangePackgeConfig.ChangeData.A_pid = data.A_pid
                ChangePackgeConfig.ChangeData.B_pid = data.B_pid
                ChangePackgeConfig.ChangeData.BindGoogleRewardID = data.BindGoogleRewardID
                ChangePackgeConfig.ChangeData.ChangeSuccessRewardID = data.ChangeSuccessRewardID
                ChangePackgeConfig.ChangeData.URL = data.URL
                return
            end
        end
    end
    ChangePackgeConfig.ChangeData = nil
end

--发送绑定信息   1：之前绑定过，登录上报 2：触发绑定成功 
ChangePackgeConfig.Request_ExchangeBindGoogle = function(type)
    print("发送绑定信息")
    net_script.RequestServerLuaFuncNew("Request_ExchangeBindGoogle", {bindtype = type})
end

function Response_ExchangeBindGoogle(msg)   --返回消息
    print("上报绑定消息完成")
end
net_script.RegisterResponseLuaFuncNew("Response_ExchangeBindGoogle", Response_ExchangeBindGoogle)
return ChangePackgeConfig
