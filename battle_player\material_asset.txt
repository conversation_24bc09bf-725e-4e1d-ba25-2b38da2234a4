local require = require
local print = print
local pairs = pairs
local assert = assert
local table = table

local asset_loader = require "asset_loader"

module("material_asset")

--sealed class
local MatetialAsset = {}
local matetialAssetPath = "art/effects_source/charectermaterial.asset"
function MatetialAsset:ctor()
    print("matetialAssetPath >>> ",matetialAssetPath)
	self.assetPath = matetialAssetPath
	self.matetials = nil
	self.isDisposed = false
	
	self.callbacks = {}
	if not self.assetLoader then
		self.assetLoader = asset_loader(matetialAssetPath,"material_asset")
	end
	self.assetLoader:load(function (obj)
		local matetials = obj.asset
		if self.isDisposed then
			return
		end
		self.matetials = matetials
		
		for k, v in pairs(self.callbacks) do
			local matetial = nil
			if v.name then
				matetial = self.matetials:GetMatetialByName(v.name)
			end
			if v.index then
				matetial = self.matetials:GetMatetialByIndex(v.index)
			end
			v.callback(matetial)
		end
		
		self.callbacks = {}
	end)
end

function MatetialAsset:GetMatetial(name, callback)
	assert(callback ~= nil, "MatetialAsset:GetMatetial callback parameter can't be nil!")
    if name == nil then
        print("Warnning : MatetialAsset:GetMatetial(name, callback), name is nil!")
        return
    end
	if self.matetials then
		local matetial = self.matetials:GetMatetialByName(name)
		if callback ~= nil and not self.isDisposed then
			callback(matetial)
		end
	else
		table.insert(self.callbacks, {name = name, callback = callback})
	end
end

function MatetialAsset:GetMatetialByIndex(index, callback)
	assert(callback ~= nil, "MatetialAsset:GetMatetial callback parameter can't be nil!")
    if index == nil then
        print("Warnning : MatetialAsset:GetMatetial(index, callback), index is nil!")
        return
    end
	if self.matetials then
		local matetial = self.matetials:GetMatetialByIndex(index)
		if callback ~= nil then
			callback(matetial)
		end
	else
		table.insert(self.callbacks, {index = index, callback = callback})
	end
end

function MatetialAsset:Dispose()
	self.isDisposed = true
	self.matetials = nil
	self.callbacks = {}
	if self.assetLoader then
		self.assetLoader:Dispose()
		self.assetLoader = nil
	end
	if self.assetPath then
		self.assetPath = nil
	end
end

local class = require "class"
local object = require "object"
CMatetialAsset = class(object, nil, MatetialAsset)