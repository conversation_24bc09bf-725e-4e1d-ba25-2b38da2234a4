local require = require
local ipairs = ipairs
local math = math
local table = table
local event = require "event"
local os = os
local xpcall = xpcall
local log = require "log"
local gw_event_activity_define = require "gw_event_activity_define"
local red_const = require "red_const"
local red_system = require "red_system"
local gw_world_activity_data = require "gw_world_activity_data"
local festival_activity_cfg = require "festival_activity_cfg"
local festival_activity_mgr = require "festival_activity_mgr"
local player_prefs = require "player_prefs"
local util = require "util"
local main_slg_common_define = require "main_slg_common_define"

module("gw_activity_red_mgr")
ActivityCodeType = festival_activity_cfg.ActivityCodeType
local RegisterActivityEntranceRedFunctions = {}
local RegisterActivityTabRedFunctions = {}
local redMsgReqInfo = {}
local redMsgReqCfg = {
    delayReqTime = 18,-----红点协议登录后延迟请求的时间
    reqIntervalTime = 0.1,-----每个红点协议请求的间隔时间
    reqWaitRspTime = 3,-----协议请求后等待Rsp的时间 --默认认定成功了
}
local weakRedPoint = {} --用来存储当前的弱红点，点一次后消失。
local commonDisposedWeakRedPoint = {} --用来存储通用弱红点，点一次后消失。 key-value = activityId-bool --注意，存储的是已经消除了的红点
function Init()   
    --监听活动
    event.Register(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE, OnGWActivityRedNeedUpdate)
    event.Register(gw_event_activity_define.GW_ACTIVITY_SET_WEAK_RED, OnSetActivityWeakRed)
    event.Register(event.GW_SCENE_CHANGE_SUCCESS, OnGWSceneChangeSuccess)
    event.Register(event.FIRST_LOGIN_CREATE_DATA_FINISH, InitRedReqMsg)
    event.Register(event.USER_DATA_RESET, OnDataReset)
end

function Dispose()   
    event.Unregister(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE, OnGWActivityRedNeedUpdate)
    event.Unregister(gw_event_activity_define.GW_ACTIVITY_SET_WEAK_RED, OnSetActivityWeakRed)
    event.Unregister(event.GW_SCENE_CHANGE_SUCCESS, OnGWSceneChangeSuccess)
    OnDataReset()
end

function OnDataReset()
    commonDisposedWeakRedPoint = {}
    weakRedPoint = {}
    RemoveRedReqMsgTimer()
end

--region 自定义红点函数  主要处理活动和入口有很多，且是动态的
---@public 活动入口红点枚举类型拼接函数
function ConcatEntranceRedType(entranceId)
    return red_const.Enum.ActivityEntrance..(entranceId or 0)
end
---@public 活动tab红点枚举类型拼接函数
function ConcatTabRedType(activityId)
    return red_const.Enum.ActivityCenterTab..(activityId or 0)
end
---@public 活动入口红点统一注册函数
function RegisterActivityEntranceRed(entranceId,isQuickEntrance, func)
    if not entranceId then
        log.Error("RegisterActivityEntranceRed entranceId is nil")
        return 0
    end
    local targetEnumType = ConcatEntranceRedType(entranceId) 
    local oldFunc = RegisterActivityEntranceRedFunctions[targetEnumType]
    if oldFunc then
        red_system.UnRegisterRedFunc(targetEnumType, oldFunc)
    end
    RegisterActivityEntranceRedFunctions[targetEnumType] = func or function()
        return GetActivityEntranceRed(entranceId,isQuickEntrance)
    end
    red_system.RegisterRedFunc(targetEnumType, RegisterActivityEntranceRedFunctions[targetEnumType])
end

---@public 活动Tab红点统一注册函数
function RegisterActivityTabRed(activityId, func)
    if not activityId then
        log.Error("RegisterActivityTabRed activityId is nil")
        return 0
    end
    local targetEnumType = ConcatTabRedType(activityId)
    local oldFunc = RegisterActivityTabRedFunctions[targetEnumType]
    if oldFunc then
        red_system.UnRegisterRedFunc(targetEnumType, oldFunc)
    end
    RegisterActivityTabRedFunctions[targetEnumType] = func or function()
        return GetActivityCenterTabRed(activityId)
    end
    red_system.RegisterRedFunc(targetEnumType, RegisterActivityTabRedFunctions[targetEnumType])
end
---@public 获取活动的newInfo 状态；是否是新活动
function IsNewActivity(activityId)
    local activity = festival_activity_mgr.GetActivityDataByActivityID(activityId)
    if not activity then
        return false
    end    
    ---获取某活动的最近一次操作时间戳
    local operatorStamp =  player_prefs.GetCacheData("gw_activity_operator_stamp_"..activityId, 0)
    return operatorStamp == 0 or operatorStamp < activity.startTimeStamp or operatorStamp > activity.endTimeStamp 
end
---@public 设置活动的newInfo 状态
function SetNewActivityStamp(activityId)
    local stamp = os.server_time()
    stamp = math.floor(stamp)
    player_prefs.SetCacheData("gw_activity_operator_stamp_"..activityId, stamp)
end
---@public 获取活动Tab红点类型Path
---@param activityID number 活动ID
function GetActivityTabRedPath(activityID)
    local count = GetActivityCenterTabRed(activityID,true)
    --规则是： 1，有常规红点则返回数字红点  2，没有常规红点，有新红点则返回新红点  3，没有常规红点，没有新红点则返回默认红点（用于处理弱红点） 
    if count > 0 then
        return red_const.Type.Num
    end
    local isNew = IsNewActivity(activityID)
    if isNew then
        return red_const.Type.New,{x = -32,y = 50}
    end
    return red_const.Type.Default
end


function GetActivityTabRedPathFunc(activityID)
    local param = {
        redPath = red_const.Type.Default
    }
    local count = GetActivityCenterTabRed(activityID,true)
    --规则是： 1，有常规红点则返回数字红点  2，没有常规红点，有新红点则返回新红点  3，没有常规红点，没有新红点则返回默认红点（用于处理弱红点） 
    if count > 0 then
        param = {
            redPath = red_const.Type.Num,
            pos = {x = 54,y = 50}
        }
    elseif IsNewActivity(activityID) then
        param = {
            redPath = red_const.Type.New,
            pos = {x = -32,y = 50}
        }
    end
    return param
end
--endregion


function OnGWSceneChangeSuccess(scene)    
    RefreshAllEntranceRed()
end
---@public 刷新所有活动入口红点
function RefreshAllEntranceRed()
    local entrances,quickEntrances = festival_activity_mgr.GetAllActivityEntrances()
    for i, v in ipairs(entrances) do
        red_system.TriggerRed(ConcatEntranceRedType(v), v)
    end
    for i, v in ipairs(quickEntrances) do
        red_system.TriggerRed(ConcatEntranceRedType(v), v,true)
    end
end

---@public 获取活动分页红点
---@param activityID number 活动ID
---@param onlyCheckDefault boolean 仅仅检测默认红点，包括new红点和弱红点
---@return number,number 红点数量，红点show类型（0/1/2 默认/new红点/弱红点）
function GetActivityCenterTabRed(activityID,onlyCheckDefault)
    --先获取对应的活动类型
    local cfg = festival_activity_cfg.GetActivityMainCfgByActivityID(activityID)
    if not cfg then
        return 0,0
    end
    local func = function()
        return RedCheckCheckFunction[cfg.headingCode].redCheckFunc(activityID) or 0
    end
    local number  = 0
    local redType = 0
    local checkDefine = RedCheckCheckFunction[cfg.headingCode]
    if checkDefine and checkDefine.redCheckFunc then
        local check, result = xpcall(func,
                function(err)
                    log.Error("活动获取红点函数接口错误，类型", cfg.headingCode,"活动ID",activityID, "错误信息:", err)
                end)
        if check then
            number = result
        else
            number =  0
        end   
    end
    if  not onlyCheckDefault and number == 0 then
        local isNew = IsNewActivity(activityID)
        local  needCheckWeak = checkDefine and checkDefine.existWeakRed       
        --先判定是否需要展示new红点
        if isNew or needCheckWeak then
            --如果没有存在常规红点的情况下；再去判断是否有弱红点
            number = commonDisposedWeakRedPoint[activityID] and 0 or 1
            if number > 0 then
                redType = isNew and 1 or 2
            end
        end
    end
    --log.Error("活动获取红点函数接口ing，类型", cfg.headingCode,"活动ID",activityID, "number=", number,"类型",redType)
    return number,redType
end

---@public 获取活动入口红点
---@param entranceID number 入口ID
---@pram isQuickEntrance boolean 是否是快速入口
function GetActivityEntranceRed(entranceID,isQuickEntrance)
    --log.Error("获取活动入口红点",entranceID,"isQuickEntrance",isQuickEntrance)
    if not entranceID then
        log.Error("活动入口红点入口ID为空")
        return 0
    end    
    local activities = festival_activity_mgr.GetAllActivityByEntrance(entranceID,isQuickEntrance)
    if not activities then
        return 0
    end
    local onlyCheckDefault = false
    --判断该入口是否是自定义的点击函数，如果是则不统计其对应的弱红点   
    local custom = main_slg_common_define.customActivityButtonConfig[entranceID]
    if custom and custom.clickFunc then
        onlyCheckDefault = true
    end
    local redCount = 0
    --检测跨服状态
    local gw_common_util = require "gw_common_util"
    local isBaseCross = gw_common_util.GetSandBaseCrossServiceState()  
    for i, v in ipairs(activities) do
        if not  isBaseCross  or (isBaseCross and  festival_activity_cfg.CrossServerActivity[v.activityCodeType]) then
            local tempCount = GetActivityCenterTabRed(v.activityID,onlyCheckDefault)
            if not tempCount  then
                tempCount = 0
            end
            redCount = redCount + tempCount            
        end
    end
    return redCount
end
---@public 设置活动的弱红点 --注意活动的new 标志其实也是弱红点
---@param activityID number 活动ID
---@param weekRedUpdate boolean 更新弱红点 默认为nil，  nil/0/1 ----不关注弱红点/表示消除弱红点/重置弱红点（即又展示弱红点）
function OnSetActivityWeakRed(_, activityID,weekRedUpdate)
    if not activityID or  not weekRedUpdate then
        return
    end
    if weekRedUpdate ~= 0 and weekRedUpdate ~= 1 then
        return
    end
    --log.Error("设置弱红点",activityID,weekRedUpdate)    
    --先判断该活动是否有弱红点；有则立即刷新红点检测
    local cfg = festival_activity_cfg.GetActivityMainCfgByActivityID(activityID)
    if not cfg then
        return
    end   
    local needTriggerCheck = false
    --先判断是否是新活动
    if IsNewActivity(activityID) then
        needTriggerCheck = true
    else
        local checkDefine = RedCheckCheckFunction[cfg.headingCode]
        if checkDefine and checkDefine.existWeakRed then
            needTriggerCheck = true            
        end        
    end  
    --设置新活动的状态时间戳
    SetNewActivityStamp(activityID)
    --注意，只有RedCheckCheckFunction配置中 existWeakRed字段设置为真的时候才会存在和处理弱红点
    if weekRedUpdate == 0 then
        --消除红点，一般就是进入了界面，消除了的弱红点存储起来（反向存储）
        commonDisposedWeakRedPoint[activityID] = true
    elseif weekRedUpdate == 1 then
        commonDisposedWeakRedPoint[activityID] = nil
    end
    if needTriggerCheck then
        --发出一个事件，统计当前红点
        red_system.TriggerRed(ConcatTabRedType(activityID))
        red_system.TriggerRed(ConcatEntranceRedType(cfg.AtyEntrance))
        --同时刷新一下快捷入口
        if cfg.AtyEntrance2  and cfg.AtyEntrance2 ~= 0 then
            red_system.TriggerRed(ConcatEntranceRedType(cfg.AtyEntrance2))
        end
    end
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_REFRESH_RED_TIPS,activityID)
end
---@public 活动红点检测事件
---@param activityID number 活动ID
function OnGWActivityRedNeedUpdate(_, activityID)   
    local cfg = festival_activity_cfg.GetActivityMainCfgByActivityID(activityID)
    --默认随时需要刷主界面红点
    if not cfg then
        return
    end
    --发出一个事件，统计当前红点
    red_system.TriggerRed(ConcatTabRedType(activityID))
    red_system.TriggerRed(ConcatEntranceRedType(cfg.AtyEntrance))
    --同时刷新一下快捷入口
    if cfg.AtyEntrance2  and cfg.AtyEntrance2 ~= 0 then
        red_system.TriggerRed(ConcatEntranceRedType(cfg.AtyEntrance2))
    end
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_REFRESH_RED_TIPS,activityID)
end

function OnSetWeakRedPoint(activityId,value)
    weakRedPoint[activityId] = value;
end

--region 主动请求活动协议 ，处理红点问题
function RemoveRedReqMsgTimer()
    if redMsgReqInfo and redMsgReqInfo.timer then
        util.RemoveDelayCall(redMsgReqInfo.timer)
        redMsgReqInfo.timer = nil
    end
end

---@public 请求活动协议处理红点
function InitRedReqMsg()
    RemoveRedReqMsgTimer()
    redMsgReqInfo.timer = util.DelayCallOnce(redMsgReqCfg.delayReqTime,function()      
        ReqRedMsg()
    end    
    )
end
---@public 请求活动协议处理红点
function ReqRedMsg()
    --先获取所有开放了的活动，
    local allOpenActivities = festival_activity_mgr.GetAllServerActivity(true,true)
    if not allOpenActivities  or #allOpenActivities == 0 then
        return
    end
    local matchReqData = {}
    for i, v in ipairs(allOpenActivities) do
        local checkCfg = RedCheckCheckFunction[v.headingCode]
        if checkCfg and checkCfg.autoReqActivityMsg then
            --当前这里每个都new一个{}，因数量少，无需优化
            table.insert(matchReqData,{func = checkCfg.autoReqActivityMsg , param = v.activityID})
        end
    end
    if #matchReqData == 0 then
        return
    end
    BackGroundExeReq(matchReqData,redMsgReqCfg.reqIntervalTime,function()
        RemoveRedReqMsgTimer()
        redMsgReqInfo.timer = util.DelayCallOnce(redMsgReqCfg.reqWaitRspTime,function()           
            event.Trigger(gw_event_activity_define.GW_ACTIVITY_ENTRANCE_UPDATE)
        end)
    end)
end
---@public 后台请求执行函数
---@param functions table 需要执行的函数列表
---@param delay number 每个函数执行间隔时间
---@param callBack function 回调
function BackGroundExeReq(functions,delay,callBack)
    local count  = functions and #functions or 0
    if count  == 0 then
        if callBack then
            callBack()
        end
        return        
    end  
    local index = 1
    local reqFunc = function()       
        if index > count then
            if callBack then
                
                callBack()
            end           
            return
        end
        local v = functions[index]
        index = index + 1
        if v then
            local check, result = xpcall(v.func,
                    function(err)
                        log.Error("登录后台请求协议Error", "param:", v.param, "错误信息:", err)
                    end,v.param)
            if check then                
                if index > count then
                    --注意，如果是最后一个了，立即执行下一帧
                    return 0
                end
                return delay
            end
        end
        return 0
    end
    util.DelayCallOnce(
            0,
            reqFunc
    )
end

--endregion

----------------------------------------------定义活动类型的红点检测函数------------
--当前活动通用的红点（主界面入口、快捷入口、活动中心tab页）
--步骤，1，在RedCheckCheckFunction中定义自己对应活动类型的红点检测函数--特别注意，这个红点检测函数要求返回红点个数 0表示没有，这里时为了以后要扩展支持显示红点数字
-------2，自己的活动红点数据发生变化时 抛出一个事件即可 event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,curActivityID)
--其中的类型ActivityCodeType.WorldBoss对应 festival_activity_cfg.ActivityCodeType中的类型

--各字段的定义
---redCheckFunc 为红点检测函数，这个函数要求返回红点个数
---existWeakRed 为是否存在弱红点 --注意该弱红点指的其每次登录都有，但进入该活动就消失的红点（红点逻辑和具体活动没关系）
--- autoReqActivityMsg 自动后台请求活动协议，去拿取红点需要的数据
RedCheckCheckFunction = {
    [ActivityCodeType.WorldBoss] = {
        --检测当前活动是否有红点，要求返回红点个数，这是为了以后可能需要支持显示红点数字
        redCheckFunc = function(activityId)
            return gw_world_activity_data.GetCurRedCount(activityId)
        end,
        --自动请求活动协议
        autoReqActivityMsg = function(activityId)           
            local net_activity_module = require "net_activity_module"
            net_activity_module.MSG_WORDBOSS_INFO_REQ()           
        end
    },
    [ActivityCodeType.CommunityGift] = {        
        redCheckFunc = function(activityId)
            local gw_community_gift_activity_data = require "gw_community_gift_activity_data"
            return gw_community_gift_activity_data.GetCurRedCount()
        end,
    },
    [ActivityCodeType.AllianceBoss] = {
        --检测当前活动是否有红点，要求返回红点个数，这是为了以后可能需要支持显示红点数字
        redCheckFunc = function(activityId)
            local gw_alliance_boss_activity_data = require "gw_alliance_boss_activity_data"
            return gw_alliance_boss_activity_data.GetChallengeRed(activityId)
        end,
    },
    -- 二档战令
    [ActivityCodeType.BattlePass2] = {
        redCheckFunc = function(activityId)
            local mgr_battle_pass = require "mgr_battle_pass"
            local rewardCount = mgr_battle_pass.GetCanGetRewardCount(activityId)
            return rewardCount
        end,
    },
    -- 三档战令
    [ActivityCodeType.BattlePass3] = {
        redCheckFunc = function(activityId)
            local mgr_battle_pass = require "mgr_battle_pass"
            local rewardCount = mgr_battle_pass.GetCanGetRewardCount(activityId)
            return rewardCount
        end,
    },
    -- 二档战令无任务战令
    [ActivityCodeType.BattlePass2UnTask] = {
        redCheckFunc = function(activityId)
            local mgr_battle_pass = require "mgr_battle_pass"
            local rewardCount = mgr_battle_pass.GetCanGetRewardCount(activityId)
            return rewardCount
        end,
    },
    --繁荣基金
    [ActivityCodeType.UniversalRecharge] = {
        redCheckFunc = function(activityId)
            if activityId == 1018 or activityId == 1022 then
                local mgr_prosperity_fund = require "mgr_prosperity_fund"
                local num = mgr_prosperity_fund.GetCanGetTaskCount(activityId)
                return num
            end
            return 0
        end,
    },
    [ActivityCodeType.ArmCompetition] = {
        redCheckFunc = function(activityId)
            local arm_competition_activity_data = require "gw_arm_competition_activity_data"
            --log.Error("检测军备竞赛的红点",activityId)
            return arm_competition_activity_data.GetRedDotCount()
            --return 0
        end
    },
    [ActivityCodeType.Universal] = {
        --Warning TODO 通用类型中有不同的活动，现在是硬编码判断ID
        redCheckFunc = function(activityId)
            local gw_bestleader_data = require "gw_bestleader_data"
            if activityId == 105 then
                local mgr_road_light = require "mgr_road_light"
                local num = mgr_road_light.GetCanGetTaskCount()
                return num
            elseif activityId == 113 then 
                local card_draw_data = require "card_draw_data"
                local redCount=card_draw_data.UpdateRedInfo()
                return redCount
            elseif activityId == 1803 then
                local gw_find_treasure_mgr = require "gw_find_treasure_mgr"
                return gw_find_treasure_mgr.GetTaskRedNumByActID(activityId)
            elseif activityId == gw_bestleader_data.GetTodayStageActId() then
                --领主巡游阶段界面
                local gw_bestleader_mgr = require "gw_bestleader_mgr"
                local mainActivityId = gw_bestleader_data.GetTodayMainAtyID()
                local todayStage = gw_bestleader_mgr.GetTodayStage(mainActivityId)
                return gw_bestleader_mgr.GetBestLeaderRedStage(mainActivityId, todayStage)
            elseif activityId == 125 then
                --任务是否可领取
                local face_transplant_data = require "face_transplant_data"
                local redCount = face_transplant_data.IsTaskCanReceiveRewardRed()
                return redCount
            elseif activityId == 128 or activityId == 129 then
                local mgr_strong_road = require "mgr_strong_road"
                local num = mgr_strong_road.GetCanGetTaskCount()
                return num
            elseif activityId == 227 or activityId == 50005 then
                local mythical_beast_mgr = require"mythical_beast_mgr"
                local num = mythical_beast_mgr.TaskCanReceiveRewardRed()
                return num
            elseif activityId == 228 or activityId == 50004 then
                local mythical_beast_sign_in_mgr = require"mythical_beast_sign_in_mgr"
                local num = mythical_beast_sign_in_mgr.TaskCanReceiveRewardRed()
                return num
            elseif activityId == 101 or activityId == 3001 then
                local halloween_sign_in_mgr = require "halloween_sign_in_mgr"
                local num = halloween_sign_in_mgr.TaskCanReceiveRewardRed()
                return num
            elseif activityId == 108 or activityId == 3003 then
                local halloween_slot_machine_mgr = require "halloween_slot_machine_mgr"
                local num = halloween_slot_machine_mgr.TaskCanReceiveRewardRed()
                return num
            else
                --下面是英雄试炼的红点判断，若非英雄试炼请列在上面（因为英雄试炼包含的活动id过多，不方便逐一判断）
                local gw_hero_trial_mgr = require "gw_hero_trial_mgr"
                
                local redDot = gw_hero_trial_mgr.GetInsideRedDot(activityId)
                if redDot < 0 then
                    redDot = gw_hero_trial_mgr.GetRedDot(activityId)
                end
                if weakRedPoint[activityId] then
                    return 0
                else
                    return redDot
                end
            end
            return 0
        end
    },
    --先锋目标
    [ActivityCodeType.PioneerActivity] = {
        redCheckFunc = function(activityId)
            --log.Error("检测先锋活动的红点", activityId)
            local pioneer_target_data = require "pioneer_target_data"
            return pioneer_target_data.GetTargetRed(activityId)
        end,
    },
    --全面备战
    [ActivityCodeType.FullyWar] = {
        redCheckFunc = function(activityId)
            local gw_fully_war_mgr = require "gw_fully_war_mgr"
            local hasRed = 0
            hasRed = gw_fully_war_mgr.GetRechargeRed(activityId)
            hasRed = hasRed + gw_fully_war_mgr.GetExchangeShopEnterRed(activityId)
            hasRed = hasRed + gw_fully_war_mgr.GetProgressRewardRed(activityId)
            return hasRed
        end,
    },
    --幸运转盘
    [ActivityCodeType.LuckyWheel] = {
        redCheckFunc = function(activityId)
            local gw_lucky_wheel_mgr = require "gw_lucky_wheel_mgr"
            local hasRed = 0
            gw_lucky_wheel_mgr.InitRewardProgressList(activityId)
            hasRed = gw_lucky_wheel_mgr.GetRewardProgressList(activityId)
            hasRed = hasRed + gw_lucky_wheel_mgr.GetRechargeRed(activityId)
            return hasRed
        end,
    },
    --联盟对决
    [ActivityCodeType.AllianceDuel] = {
        redCheckFunc = function(activityId)
            local alliance_duel_data = require "alliance_duel_data"
            return alliance_duel_data.OnGetThemeRedDot()
        end,
    },
    --累计充值
    [ActivityCodeType.TotalRecharge] = {
        redCheckFunc = function(activityId)
            local gw_totalrecharge_mgr = require "gw_totalrecharge_mgr"
            return gw_totalrecharge_mgr.GetTotalRechargeRed(activityId)
        end,
    },
    --最强指挥官
    [ActivityCodeType.BestLeader] = {
        redCheckFunc = function(activityId)
            local gw_bestleader_mgr = require "gw_bestleader_mgr"
            return gw_bestleader_mgr.GetBestLeaderRed(activityId)
        end,
    },
    [ActivityCodeType.WeekendCard] = {
        redCheckFunc = function(activityId)
            local weekend_card_data = require "weekend_card_data"
            return weekend_card_data.GetWeekendCardRed(activityId)
        end,
    },
    [ActivityCodeType.MonthCard] = {
        redCheckFunc = function(activityId)
            local month_card_data = require "month_card_data"
            return month_card_data.GetMonthCardRed(activityId)
        end,
    },
    [ActivityCodeType.LoginGift] ={
        redCheckFunc = function(activityId)
            local login_gift_data = require "login_gift_data"
            return login_gift_data.EntranceRed()
        end,
    },
 --每日必买
    [ActivityCodeType.DailyGifts] = {
        redCheckFunc = function(activityId)
            local gw_dailygift_mgr = require "gw_dailygift_mgr"
            return gw_dailygift_mgr.GetDailyGiftRed(activityId)
        end,
    },	
    [ActivityCodeType.GeneralTrail] = {
        redCheckFunc = function(activityId)
            local ui_general_trials_mgr = require "ui_general_trials_mgr"
            return ui_general_trials_mgr.CheckLeagueItemRedPointCount()
        end,
    },  
    [ActivityCodeType.WarRally] = {
        --existWeakRed = true,
        redCheckFunc = function(activityId)
            local ui_war_rally_mgr = require "ui_war_rally_mgr"
            return ui_war_rally_mgr.GetActivityRed()
        end,
    },
    --每日特惠
    [ActivityCodeType.DailyGift] = {
        redCheckFunc = function(activityId)
            local ui_daily_gift_activity_controller = require "ui_daily_gift_activity_controller"
            return ui_daily_gift_activity_controller.GetTabRedDot()
        end,
    },
    --丧尸来袭
    [ActivityCodeType.ZombiesAttack] = {
        redCheckFunc = function(activityId)
            local zombies_attacking_data = require "zombies_attacking_data"
            return zombies_attacking_data.GetShopRed()
        end,
    },
    --闪金集市
    [ActivityCodeType.ShiningShop] = {
        redCheckFunc = function(activityId)
            local shining_market_data = require "shining_market_data"
            return shining_market_data.GetAllRedCount()
        end,
    },
    [ActivityCodeType.CitySiege] = {       
        existWeakRed = true, ---存在弱红点；只需要进入一次就会被消除掉
        redCheckFunc = function(activityId)
            if activityId ~= 110 and activityId ~= 111 then
                local city_siege_activity_data = require "city_siege_activity_data"
                return city_siege_activity_data.OnGetCitySiegeRedDotCount()
            end
            return 0
        end
    },
        --神迹宝箱
    [ActivityCodeType.MiracleBox] = {
        redCheckFunc = function(activityId)
            local miracle_box_data = require "miracle_box_data"
            return miracle_box_data.GetActivityRed()
        end,
    },
    [ActivityCodeType.DesertStorm] = {
        redCheckFunc = function(activityId)
            local gw_storm_mgr = require "gw_storm_mgr"
            local event_DesertStrom_define = require("event_DesertStrom_define")
            local activityData = gw_storm_mgr.GetStormDataActivity()
            local activityState = activityData.GetActivityState()
            local signUpState = activityData.OnCheckSignUpState()
            if activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_SignUp then
                local alliance_pb = require "alliance_pb"
                local alliance_mgr = require "alliance_mgr"
                local player_mgr = require "player_mgr"
                local temp, authority = alliance_mgr.GetRoleAuthority(player_mgr.GetPlayerRoleID())
                local isR4 = (authority >= alliance_pb.emAllianceAuthority_R4)
                if isR4 then
                    if signUpState then
                        return 0
                    end
                    local RedDot = gw_storm_mgr.GetRedDotState()
                    if not RedDot then
                        return 1
                    end
                    return 0
                else
                    return 0
                end
            elseif activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_Battle then
                local mySignUpInfo = activityData.OnGetMySignInfo()
                local signUpInfo = activityData.OnGetSignUpInfo()
                --TODO 这里只考虑了小队A的情况，若非小队A，则需要做额外处理
                if mySignUpInfo.enTeam and mySignUpInfo.enTeam ~= event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_None then
                    local timeList = activityData.OnGetBattleTimeList()[signUpInfo.teamData[event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A].battleTimeIndex]
                    local enterTime = timeList.enterTime - os.server_time()
                    if enterTime > 0 then
                        return 0
                    else
                        local startTime = timeList.battleTime - os.server_time()
                        if startTime > 0 then
                            return mySignUpInfo.main and 1 or 0
                        else
                            local endTime = timeList.endTime - os.server_time()
                            if endTime > 0 then
                                return 1
                            else
                                return 0
                            end
                        end
                    end
                    return 1
                end
                return 0
            end
            return 0
        end,
    },
    [ActivityCodeType.FindTreasure] = {
        redCheckFunc = function(activityId)
            local gw_find_treasure_mgr = require "gw_find_treasure_mgr"
            return (gw_find_treasure_mgr.GetTaskProgressRed(activityId) + gw_find_treasure_mgr.GetRewardRed())
        end,
    },
    --神迹宝箱
    [ActivityCodeType.Doomsday] = {
        redCheckFunc = function(activityId)
            local doomsday_data = require "doomsday_data"
            return doomsday_data.GetToggleRed()
        end,
    },
    [ActivityCodeType.OptionalWeekCard] ={
        redCheckFunc = function(activityId)
            local optional_weekcard_mgr = require "optional_weekcard_mgr"
            return optional_weekcard_mgr.GetActivityRed()
        end,
    },
    [ActivityCodeType.FirstRecharge] = {
        redCheckFunc = function(activityId)
            local gw_firstrecharge_mgr = require "gw_firstrecharge_mgr"
            return gw_firstrecharge_mgr.GetAllRedCount(activityId)
        end,
    },
    [ActivityCodeType.SevenDayLogin] = {
        redCheckFunc = function(activityId)
            local gw_seven_day_login_mgr = require "gw_seven_day_login_mgr"
            return gw_seven_day_login_mgr.GetSevenDayLoginRed(activityId)
        end,
    },
    [ActivityCodeType.WarZoneDuel] = {
        redCheckFunc = function(activityId)
            local war_zone_duel_mgr = require "war_zone_duel_mgr" 
            local red = war_zone_duel_mgr.GetWarZoneMainRed()
            return red
        end,
    },
    [ActivityCodeType.StormRescue] = {
        redCheckFunc = function(activityId)
            local red = 0
            local rewardResult = red_system.GetBuildFunRed(red_const.Enum.StormRescueReward)
            red = red + rewardResult[1]
            return red
        end,
    },
    [ActivityCodeType.StormRescue2] = {
        redCheckFunc = function(activityId)
            local red = 0
            local rewardResult = red_system.GetBuildFunRed(red_const.Enum.StormRescueReward)
            red = red + rewardResult[1]
            return red
        end,
    },
    [ActivityCodeType.StormRescue3] = {
        redCheckFunc = function(activityId)
            local red = 0
            local rewardResult = red_system.GetBuildFunRed(red_const.Enum.StormRescueReward)
            red = red + rewardResult[1]
            return red
        end,
    },
    [ActivityCodeType.Arena_Weekend] = {
        redCheckFunc = function() 
            local click_liking_mgr = require "click_liking_mgr"
            local click_liking_define = require "click_liking_define"
            return click_liking_mgr.GetRedDotByAreaType(click_liking_define.Enum_AreaLikeType.PeekArea)
        end
    },
    [ActivityCodeType.Arena_3v3] = {
        redCheckFunc = function() 
            local click_liking_mgr = require "click_liking_mgr"
            local click_liking_define = require "click_liking_define"
            return click_liking_mgr.GetRedDotByAreaType(click_liking_define.Enum_AreaLikeType.the3v3Area)
        end
    },
    [ActivityCodeType.SuperMonthGift] = {
        redCheckFunc = function(activityId)
            local super_month_gift_mgr = require "super_month_gift_mgr"
            return super_month_gift_mgr.GetActivityRed()
        end
    },
    [ActivityCodeType.HeroFirstRecharge] = {
        redCheckFunc = function(activityId)
            local hero_first_charge_mgr = require "hero_first_charge_mgr"
            return hero_first_charge_mgr.HeroFirstChargeActivityRedPoint()
        end
    },
    [ActivityCodeType.LandRevivalBattlePass] = {
        redCheckFunc = function(activityId)
            local mgr_battle_pass = require "mgr_battle_pass"
            local red = mgr_battle_pass.GetCanGetRewardCount(activityId)
            return red
        end
    },
    [ActivityCodeType.LandRevivalMain] = {
        redCheckFunc = function(activityId)
            local land_revival_mgr = require "land_revival_mgr"
            local red = land_revival_mgr.LandRevivalMainRed()
            return red
        end
    },
    [ActivityCodeType.LandRevivalTask] = {
        redCheckFunc = function(activityId)
            local land_revival_mgr = require "land_revival_mgr"
            local red = land_revival_mgr.LandRevival7DayPageRed()
            return red
        end
    },
    [ActivityCodeType.HalloweenExchangeShop] = {
        redCheckFunc = function(activityId)
            local ui_halloween_exchange_shop_controller = require "ui_halloween_exchange_shop_controller"
            local redNum = 0
            if ui_halloween_exchange_shop_controller.GetIsExchangeTip() then 
                local gw_fully_war_mgr = require "gw_fully_war_mgr"
                redNum = redNum + gw_fully_war_mgr.GetExchangeShopEnterRed(activityId,true)
            end
            return redNum
        end
    },
    [ActivityCodeType.HalloweenWingding] = {
        redCheckFunc = function(activityId)
            return 0
        end
    }
}




