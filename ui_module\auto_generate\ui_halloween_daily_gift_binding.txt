local require = require
local typeof = typeof

local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local Button = CS.UnityEngine.UI.Button
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local RectTransform = CS.UnityEngine.RectTransform


module("ui_halloween_daily_gift_binding")

UIPath = "ui/prefabs/gw/activity/halloweenactivity/slotmachine/uihalloweendailygift.prefab"

WidgetTable ={
	txt_TitleText = { path = "TtileBg/txt_TitleText", type = Text, },
	img_Diamond = { path = "GoodContent/DiamondBg/img_Diamond", type = Image, },
	txt_Diamond = { path = "GoodContent/DiamondBg/txt_Diamond", type = Text, },
	btn_DrawActivityCoin = { path = "GoodContent/DrawActivityCoinsBg/btn&img_DrawActivityCoin", type = Button, event_name = "OnBtnDrawActivityCoinClickedProxy"},
	img_DrawActivityCoin = { path = "GoodContent/DrawActivityCoinsBg/btn&img_DrawActivityCoin", type = Image, },
	txt_DrawActivityCoinsNum = { path = "GoodContent/DrawActivityCoinsBg/txt_DrawActivityCoinsNum", type = Text, },
	scrItem_RechargeItem = { path = "Scroll View/Viewport/Content/scrItem_RechargeItem", type = ScrollRectItem, },
	txt_Title = { path = "Scroll View/Viewport/Content/scrItem_RechargeItem/CharegeParent/GetBtn/txt_Title", type = Text, },
	rtf_RewardContent = { path = "Scroll View/Viewport/Content/scrItem_RechargeItem/CharegeParent/Scroll View_1/Viewport/rtf_RewardContent", type = RectTransform, },
	btn_closeBtn = { path = "Bottom/btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},

}
