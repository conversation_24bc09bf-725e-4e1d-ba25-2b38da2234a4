local require = require
local typeof = typeof

local RectTransform = CS.UnityEngine.RectTransform
local Image = CS.UnityEngine.UI.Image
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local SpriteSwitcher = CS.War.UI.SpriteSwitcher


module("ui_arena_battle_result_normal_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenabattleresultnormal.prefab"

WidgetTable ={
	rtf_effectTrans = { path = "rtf_effectTrans", type = RectTransform, },
	rtf_Lose = { path = "rtf_Lose", type = RectTransform, },
	rtf_loseLeftBG = { path = "rtf_Lose/rtf_loseLeftBG", type = RectTransform, },
	rtf_loseRightBG = { path = "rtf_Lose/rtf_loseRightBG", type = RectTransform, },
	rtf_loseText = { path = "rtf_Lose/rtf_loseText", type = RectTransform, },
	rtf_loseEnText = { path = "rtf_Lose/rtf&img_loseEnText", type = RectTransform, },
	img_loseEnText = { path = "rtf_Lose/rtf&img_loseEnText", type = Image, },
	rtf_Win = { path = "rtf_Win", type = RectTransform, },
	rtf_winLeftBG = { path = "rtf_Win/rtf_winLeftBG", type = RectTransform, },
	rtf_winRightBG = { path = "rtf_Win/rtf_winRightBG", type = RectTransform, },
	rtf_winText = { path = "rtf_Win/rtf_winText", type = RectTransform, },
	rtf_winTextEn = { path = "rtf_Win/rtf&img_winTextEn", type = RectTransform, },
	img_winTextEn = { path = "rtf_Win/rtf&img_winTextEn", type = Image, },
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	rtf_attackTra = { path = "AttackInfo/rtf_attackTra", type = RectTransform, },
	txt_attackName = { path = "AttackInfo/txt_attackName", type = Text, },
	txt_attackScore = { path = "AttackInfo/score/txt_attackScore", type = Text, },
	txt_attackScoreChange = { path = "AttackInfo/score/txt_attackScoreChange", type = Text, },
	rtf_attackHeroItem = { path = "AttackInfo/HeroList/Viewport/Content/rtf_attackHeroItem", type = RectTransform, },
	rtf_defendTra = { path = "DefendInfo/rtf_defendTra", type = RectTransform, },
	txt_defendName = { path = "DefendInfo/txt_defendName", type = Text, },
	txt_defendScore = { path = "DefendInfo/score/txt_defendScore", type = Text, },
	txt_defendScoreChange = { path = "DefendInfo/score/txt_defendScoreChange", type = Text, },
	rtf_defendHeroItem = { path = "DefendInfo/HeroList/Viewport/Content/rtf_defendHeroItem", type = RectTransform, },
	txt_oldRank = { path = "RankChange/txt_oldRank", type = Text, },
	txt_curRank = { path = "RankChange/txt_curRank", type = Text, },
	ss_rankChange = { path = "RankChange/ss_rankChange", type = SpriteSwitcher, },

}
