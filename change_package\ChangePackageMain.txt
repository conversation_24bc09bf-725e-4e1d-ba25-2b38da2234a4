--n:陈泳冰
--d:2022年1月28日
--v:1.0.0
--t:换包+绑定账号
local print         =       print
local require       =       require
local game_config   =       require "game_config"
local q1sdk         =       require "q1sdk"
local windowMgr     =       require "ui_window_mgr"
local ChangePackgeConfig = require "ChangePackgeConfig"

local ChangePackageMain = {}
--------------------------------------------------------------------------
-- 【开始进入绑定流程】
--------------------------------------------------------------------------
-- 判断版本渠道正确性，以及是否已绑定账号等
ChangePackageMain.Run = function ()
    print("【换包+绑定主流程】-- 加载配置")
    ChangePackgeConfig:Load()
    if ChangePackgeConfig.ChangeData then
        print("【换包+绑定主流程】-- 配置存在")
        if game_config.CHANNEL_ID == ChangePackgeConfig.ChangeData.A_pid then
            --渠道是塔楼
            print("从换包模块进入功能 【换包逻辑】")
            ChangePackageMain.ShowBandinGoogleOrDownLoadNowPackage()
        end
    end
end

ChangePackageMain.ShowBandinGoogleOrDownLoadNowPackage = function()
    local bindTypeList = q1sdk.GetRoleBindType()
    if bindTypeList["GooglePlay"] then
        --已绑定googlePlay账号
        ChangePackageMain.DownLoadNewPackage()
    else
        --未绑定
       windowMgr:ShowModule("BindGooglePlayPopup")
    end
end

--下载页
ChangePackageMain.DownLoadNewPackage = function()
    windowMgr:ShowModule("DownLoadNewpopup")
end

return ChangePackageMain