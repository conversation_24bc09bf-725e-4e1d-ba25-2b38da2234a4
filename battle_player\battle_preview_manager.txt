local io = io
local typeof = typeof
local require = require
local print = print
local table = table
local type = type
local pairs = pairs
local math = math
local string = string
local tonumber = tonumber
local event = require "event"

local log = require "log"
local util = require "util"
local game_scheme = require "game_scheme"
local gw_hero_mgr = require "gw_hero_mgr"
local lang = require "lang"
local model_res = require "model_res"
local asset_load_mgr = require "asset_load_mgr"
local battle_config = require "battle_config"
local base_game_object = require "base_game_object"
local device_level_controller = require "device_level_controller"
local device_param_util = require "device_param_util"

local Application = CS.UnityEngine.Application
local Debug = CS.UnityEngine.Debug

local IOSystem		= require "iosystem_load"
local GameObject	= CS.UnityEngine.GameObject

local BattlePlayer = CS.War.Battle.BattlePlayer

module("battle_preview_manager" )
local assetBundleName = "art/battleplayer/laysceneview.prefab"
local assetLoadMgr = asset_load_mgr.CAssetLoadMgr()

local prepareBattle = nil
local finalizeBattle = nil 
local board = nil
local player = {}
local playerRoot = {}
local closed = true
local baseGameObj = nil
local sid = 1;
local faction = 
{
    "youan",
    "baolei",
    "shenyuan",
    "senlin",
    "anying",
    "guangming",
}

local curHeros
local curMonsters

--预加载敌方英雄资源
local loadEnemyList = {}
--预加载上阵英雄资源
local loadSelectHeroList = {}
--预加载场景布局资源
local loadBattleLayoutList = {}
--战斗背景
local loadBattleBackground = nil
--战斗开场序幕资源
local loadPreludus = nil

function GetCurHeros(  )
    return curHeros
end
function GetCurMonsters(  )
    return curMonsters
end
 
function InitPlayer(rawImage)
    board = rawImage
    Test()
end 

function Close()
----    print("close")
    closed = true
    
    if not util.IsObjNull(playerRoot) then
        playerRoot.gameObject:SetActive(false)
    end
    if not util.IsObjNull(player) then
        player:ClearBattle() 
    end
end

function BuildSkill(srcCfg, destCfg, skill, dead, segment)
    local seg = math.max(srcCfg[segment], 1)
    player:BeginSkill(srcCfg.sid, srcCfg[skill])
    for i = 1, seg - 1 do
        player:AppendAction(destCfg.sid, destCfg.hit, i-1)
    end
    if dead and destCfg.die then
        local battle_parser_utility = require "battle_parser_utility"
        battle_parser_utility.AppendDieForSegment(player, destCfg.sid, seg, destCfg.die)
    else
        player:AppendAction(destCfg.sid, dead and destCfg.die or destCfg.hit, seg-1)
    end
    player:EndSkill()
end

function SimulatePreview(banRandSort)
    playerRoot.gameObject:SetActive(true)
    sid = 1

    local reset = "art/skill/utility/default_reset.playable"

    curHeros = GetHeroCfg(3,banRandSort)
    curMonsters = GetMonsterCfg(1)
    local idOffset = #curHeros

    player:ClearBattle()
    player:BattleBegin ()
    if player.SetUseMeshSimplify then
        player:SetUseMeshSimplify(true)
    end
    for i = 1, #curHeros do 
        local pos = i - 1
        player:RegisterActor (curHeros[i].sid, pos, curHeros[i].modelPath)
        player:SetActorHudData(curHeros[i].sid, 100, 100, 100, 100, curHeros[i].level, curHeros[i].fraction)
        player:Reset(curHeros[i].sid, reset)
    end

    for i = 1, #curMonsters do 
        local pos = i + 5
        player:RegisterActor (curMonsters[i].sid, pos, curMonsters[i].modelPath)
        player:SetActorHudData(curHeros[i].sid, 100, 100, 100, 100, curHeros[i].level, curHeros[i].fraction)
        player:Reset(curMonsters[i].sid, reset)
    end

    local useAkt = true
    for i = 1, math.random(1, 3) do
        player:RoundStart(i)

        local skill = useAkt and "atk" or "ult"
        local segment = useAkt and "atkSeg" or "ultSeg"
        for j = 1, #curHeros do
            BuildSkill(curHeros[j], curMonsters[1], skill, false, segment)
        end
        BuildSkill(curMonsters[1], curHeros[math.random(1, #curHeros)], skill, false, segment)

        useAkt = not useAkt
    end

    local action = math.random(1, #curHeros)

    for j = 1, #curHeros do
        local dead = j == action
        BuildSkill(curHeros[j], curMonsters[1], useAkt and "atk" or "ult", dead, useAkt and "atkSeg" or "ultSeg")
        if dead then
            break
        end
    end

    OnBattleReportReceived()
end

function GetHeroCfg(count,banSort)
	local hook_hero_data = require "hook_hero_data"
    local lineups = {}
    for k,v in pairs(hook_hero_data.GetSaveHeroData()) do
        table.insert(lineups, v)
    end 
    if banSort==nil then
        util.RandSort(lineups)
    end
    local cfgs = {}
    local c = #lineups < count and #lineups or count

    if #lineups == 0 then
        for i = 1, count do
            local lookup = {4, 8, 11, 23, 24, 25}
            local cfg = BuildHeroCfg(lookup[i] or i)
            table.insert(cfgs, cfg)
        end
    else
        for i = 1 , c do
            local cfg = BuildHeroCfg(lineups[i].heroID, lineups[i].numProp.lv, lineups[i].numProp.starLv)
            table.insert(cfgs, cfg)
        end 

        if c < count then
            local lookup = {4, 8, 11, 23, 24, 25}
            for i = 1, count - c do
                local cfg = BuildHeroCfg(lookup[i] or i)
                table.insert(cfgs, cfg)
            end
        end
    end

    return cfgs
end

function GetMonsterCfg(count)
    local laymain_data = require "laymain_data"
    local hookingLevel = laymain_data.GetHangLevel()
    local hookLevelInfo = game_scheme:HookLevel_0(hookingLevel)

    if not hookLevelInfo then
        log.Error("hookingLevel find null:"..hookingLevel)
    end

    local lineups = {}
    for k,v in pairs(hookLevelInfo.ranksID.data) do
        if v ~= 0 then
            table.insert(lineups, v)
        end
    end 
    util.RandSort(lineups)

    local cfgs = {}
    local c = #lineups < count and #lineups or count

    for i = 1 , c do
        local cfg = BuildHeroCfg(lineups[i])
        table.insert(cfgs, cfg)
    end 

    return cfgs
end

function GetCfgField(cfg, field, default)
    if cfg ~= nil and cfg[field] ~= nil and cfg[field] ~= "" then
        return cfg[field]
    else
        return default
    end
end

function BuildHeroCfg(heroId, level, star)
    local heroCfg = game_scheme:Hero_0(heroId)
    if not heroCfg then
        log.Error("can't find Config(Hero) by id = ", heroId)
    end
    local starsLv = star or heroCfg.starLv
    local currentModel = gw_hero_mgr.ChangeHeroModel(heroCfg.heroID, starsLv)
    local modulCfg = game_scheme:Modul_0(currentModel)
    local heroSkillCfg = game_scheme:HeroSkill_0(heroCfg.heroSkillId.data[0], 1)

    local params = string.split(heroSkillCfg.paramGroup, ';')
    local ultId = string.split(params[1], "#")[1]
    local atkId = params[2] == nil and ultId or string.split(params[2], "#")[1]
    local ultcfg = game_scheme:Skill_0(tonumber(ultId))
    local atkcfg = game_scheme:Skill_0(tonumber(atkId))

    local cfg = {}
    cfg.id = heroId
    cfg.modelPath = modulCfg.modelPath
    cfg.faction = faction[heroCfg.type]
    cfg.level = level or heroCfg.monsterLevel
    cfg.atk = GetCfgField(atkcfg, "strPath", "art/skill/common/common_atk.playable")
    cfg.ult = GetCfgField(ultcfg, "strPath", "art/skill/common/common_atk.playable")
    cfg.hit = GetCfgField(nil, "hit", "art/skill/common/common_hit.playable")
    cfg.die = GetCfgField(nil, "die", "art/skill/common/common_die.playable")
    cfg.atkSeg = GetCfgField(atkcfg, "Multistage", 1)
    cfg.ultSeg = GetCfgField(ultcfg, "Multistage", 1)
    cfg.sid = sid
    sid = sid + 1
    cfg.modelPath = model_res.GetResPath(cfg.modelPath)

    return cfg
end

function GetBuffByEffect(strBuffId, strBuffLevel, resList, processedBuffList)
    if string.empty(strBuffId) or string.empty(strBuffLevel) then
        return
    end
    GetBuffFxResource(tonumber(strBuffId), tonumber(strBuffLevel), resList, processedBuffList)
end

-- 具体参数设置见 Effect.csv
local effectGroupBuffInfo = 
{
    [2]  = {"strParam4", "strParam5"},--效果触发增加buff
    [6]  = {"strParam8", "strParam9"},--受到攻击使用技能
    [8]  = {"strParam3", "strParam4"},--死亡增加buff
    [11] = {"strParam5", "strParam6"},--攻击增加BUFF
    [12] = {"strParam5", "strParam6"},--检测目标Buff类型增加Buff
    [13] = {"strParam4", "strParam5"},--检测当前生命增加Buff
    [17] = {"strParam4", "strParam5"},--复活
    [18] = {"strParam2", "strParam3","strParam4","strParam5"},--每回合增加buff
    [22] = {"strParam5", "strParam6"}, --检测攻击目标数量增加buff
    [23] = {"strParam4", "strParam5"}, --击杀获得buff
    [27] = {"strParam2", "strParam3"}, --生命回复增加buff
    [28] = {"strParam2", "strParam3"}, --增加buff
    [31] = {"strParam2", "strParam3"}, --攻击前增加BUFF
    [32] = {"strParam2", "strParam3"}, --优先攻击指定目标
    [33] = {"strParam4", "strParam5"}, --抵挡伤害
    [38] = {"strParam2", "strParam3", "strParam5", "strParam6"}, --随机增加BUFF
}

function GetBuffFxResource(buffId, buffLevel, resList, processedBuffList)
    --防止buff与effect之间循环引用
    if processedBuffList[buffId]  then
        return
    end
    processedBuffList[buffId] = true

    local buffCfg = game_scheme:Buff_0(buffId, buffLevel)
    if buffCfg == nil then
        return
    end
    --根据buffid，取得buff abname
    local particleType, particleRes = battle_config.GetBuffFxData(buffCfg)
    if particleRes then
        resList[particleRes] = 1
    end

    --是否多个特效组成，递归加载全部
    local effectParam = buffCfg.effectParam
    if effectParam.count == 0 then
        return
    end
    local i,effectID,effectCfg,groupID,buffInfo
    local count = effectParam.count - 1
    for i=0,count do
        effectID = effectParam.data[i]
        effectCfg = game_scheme:Effect_0(effectID)
        if effectCfg then
            groupID = effectCfg.nGroupID
            buffInfo = effectGroupBuffInfo[groupID]
            if buffInfo then
                local buffCount = #buffInfo / 2
                local buffBaseIdx
                for i=1,buffCount do
                    buffBaseIdx = (i - 1) * 2
                    GetBuffByEffect(effectCfg[buffInfo[buffBaseIdx + 1]], effectCfg[buffInfo[buffBaseIdx + 2]], resList, processedBuffList)
                end
            end
        end
    end
end

---comment预加载技能/buff
---@param resList any
---@param heroId any
---@param level any
function ParseHeroSkillResPath(resList, heroId, level)

    local device_param_util = require "device_param_util"
    local isLowMem = device_param_util.IslowMem()
    if isLowMem then return end

    local skill_mgr = require "skill_mgr"
    local skillsID = skill_mgr.GetHeroSkillsID(heroId)
    if not skillsID then
        return
    end
    local skillLvs,skillActiveStates,slotPos = skill_mgr.GetHeroSkillsLv(heroId, level)
    local processedBuffList = {}
    for i,data in pairs(skillsID) do
        if data and skillLvs[i] and skillActiveStates[i] then
            --只循环已解锁技能
            ------ print("已解锁技能:",i,data, skillLvs[i],skillActiveStates[i])
            local cfg_skill = game_scheme:HeroSkill_0(data, skillLvs[i])
            if cfg_skill then
                local paramGroup = string.split(cfg_skill.paramGroup, ';')
                for effectIdx, v in pairs(cfg_skill.effectGroup.data) do
                    local property = paramGroup[effectIdx + 1]
                    if property then
                        if v == 1 then -- 1技能2加BUFF3加属性
                            -- 技能id#权重
                            local skillProperty = string.split(property, '#', tonumber)
                            local skillCfg = game_scheme:Skill_0(skillProperty[1])
                            local skillResPath = GetCfgField(skillCfg, "strPath", "art/skill/common/common_atk.playable")
                            --公共攻击
                            resList[skillResPath] = 1
                        elseif v == 2 then
                            -- buff id#等级
                            local buffProperty = string.split(property, '#', tonumber)
                            GetBuffFxResource(buffProperty[1], buffProperty[2], resList, processedBuffList)
                        end
                    end
                end
            end
        end
    end
end

---comment 获取英雄全部资源（技能、buff、模型）
---@param heroData any
---@param res any
---@param modulResList any
---@return any
function GetHeroRes(heroData, res, modulResList)
    if not heroData or not heroData.numProp then
        return
    end
    local heroId = heroData.heroID
    local level = heroData.numProp.lv

    ParseHeroSkillResPath(res, heroId, level)

    local hit = GetCfgField(nil, "hit", "art/skill/common/common_hit.playable")
    local die = GetCfgField(nil, "die", "art/skill/common/common_die.playable")
    local fakeDeath = "art/skill/common/common_fake_die.playable"
    res[hit] = 1
    res[die] = 1
    res[fakeDeath] = 1
    if modulResList then
        local star = heroData.numProp.starLv or heroCfg.starLv
        local starsLv = star or 1
        local currentModel = gw_hero_mgr.ChangeHeroModel(heroId, starsLv, false, heroData.skinID)
        local modulCfg = game_scheme:Modul_0(currentModel)
        if modulCfg then
            local modelPath = model_res.GetResPath(modulCfg.modelPath)
            res[modelPath] = 1
            modulResList[modelPath] = 1
        end
    end
    return res
end

local weaponSkills = {
    "art/skill/0_wuqijineng/2_fuyoupao_skill.playable",
    "art/skill/0_wuqijineng/1_texiusizhijian_skill.playable",
}
---comment预加载计算
---@param count any
---@return table
---@return table
function GetResPreload(count)
	local hook_hero_data = require "hook_hero_data"
    local lineups = {}
    for k,v in pairs(hook_hero_data.GetSaveHeroData()) do
        table.insert(lineups, v)
    end
    local res = {}
    local modulResList = {}

    local lineupsCount = #lineups
    local resCount = lineupsCount < count and lineupsCount or count
    local heroData
    for i = 1, resCount do
        heroData = lineups[i]
        GetHeroRes(heroData, res, modulResList)
    end 

    --取消2-6杀缓存，后期很少一人多杀，加载时间较短
    --local battle_parser_utility = require "battle_parser_utility"
    --local winningStreakResPath
    --for i = 2, 6 do
    --    winningStreakResPath = battle_parser_utility.GetWinningStreakResPath(i)
    --    if winningStreakResPath then
    --        res[winningStreakResPath] = 1
    --    end
    --end
    return res,modulResList
end

function OnBattleReportReceived(stageType, str)
    --Debug.LogError("OnBattleReportReceived") 
  
    if util.IsObjNull(player) then
        return
    end
    player:UnRegisterLoadingBeginHandler(OnLoadingBegin)
    player:UnRegisterLoadingCompleteHandler(OnLoadingCompleted)
    player:UnRegisterLoadingProgressChangedHandler(OnLoadingPorgressChanged)
    player:UnRegisterBattleEventHandler(OnBattleEvent)
    player:UnRegisterRoundEventHandler(OnRoundBegin)

    player:RegisterLoadingBeginHandler(OnLoadingBegin)
    player:RegisterLoadingCompleteHandler(OnLoadingCompleted)
    player:RegisterLoadingProgressChangedHandler(OnLoadingPorgressChanged)
    player:RegisterBattleEventHandler(OnBattleEvent)
    player:RegisterRoundEventHandler(OnRoundBegin)
 
    player:Load()
end

function OnRoundBegin(round)
    ------ print(round)
    if round==1 then
        --util.SetLayer(playerRoot,22)
    end
end

function OnBattleEvent(eventname)
    if eventname == "BattleEnd" then 
        ------ print(eventname)
        player:UnRegisterBattleEventHandler(OnBattleEvent)

        SimulatePreview()
    end
end

function OnLoadingBegin()
    
end

function OnLoadingCompleted()  
    if util.IsObjNull(player) then
        return
    end
    player:UnRegisterLoadingBeginHandler(OnLoadingBegin)
    player:UnRegisterLoadingCompleteHandler(OnLoadingCompleted)
    player:UnRegisterLoadingProgressChangedHandler(OnLoadingPorgressChanged)
    player:Play()
end

function OnLoadingPorgressChanged(progress)
    
end

function Test()
    closed = false

    local util = require "util"
    util.DelayCall(0.2, function()
        -- local file = io.open(Application.dataPath .. "/BattleReport.txt", "rb")
        -- local str = file:read("*a")
        -- io.close(file)

        -- OnBattleReportReceived(nil, str)

        if closed then
            return
        end
        
        if util.IsObjNull(playerRoot) then

            if not baseGameObj then
                baseGameObj = base_game_object("battle_preview_manager")
            end

            baseGameObj:LoadResource(assetBundleName,nil,function (UIPrefab)
                if closed then
                    -- IOSystem.UnloadAssetBundle(assetBundleName, "battle_preview_manager")
                    return
                end
                if util.IsObjNull(playerRoot) then
                    -- playerRoot = GameObject.Instantiate(UIPrefab)
                    UIPrefab.name = "LaySceneView"
            
                    local go = GameObject.Find("/LaySceneView/View/battleView/MainBattlePlayer")
                    player = go:GetComponent(typeof(BattlePlayer))
                    --player:RegisterRequireBubbleTextHandler(OnRequireBubbleText)
                end
                SimulatePreview(true)
            end)
            -- IOSystem.LoadAssetAsync(assetBundleName, nil, function (UIPrefab)
            --     if closed then
            --         IOSystem.UnloadAssetBundle(assetBundleName, "battle_preview_manager")
            --         return
            --     end
            --     if util.IsObjNull(playerRoot) then
            --         playerRoot = GameObject.Instantiate(UIPrefab)
            --         playerRoot.name = "LaySceneView"
            
            --         local go = GameObject.Find("/LaySceneView/View/battleView/MainBattlePlayer")
            --         player = go:GetComponent(typeof(BattlePlayer))
            --         --player:RegisterRequireBubbleTextHandler(OnRequireBubbleText)
            --     end
            --     SimulatePreview(true)
            -- end, "battle_preview_manager")
        else
            SimulatePreview(true)
        end
    end)
end
local loadList = {}


function OnRequireBubbleText(bubbleId)
    local config = game_scheme:BubbleMap_0(bubbleId)
    if config then

        local c = 0
        for i,d in pairs(config.nLangID.data) do
            c = c+1
        end

        if c > 0 then
            local index = player:RandomRange(0, c)
            return lang.Get(config.nLangID.data[index])
        else
            return nil
        end
    end
end

---comment 计算、加载 英雄和包含资源
---@param heroList any
---@param cachedHeroResList any
---@return table
function PreLoadHeroResource(heroList, cachedHeroResList)
    local heroData,i
    local resList = {}
    local heroCount = 5
    for i = 0, heroCount do
        if heroList then
            heroData = heroList[i]
        end
        GetHeroRes(heroData, resList)
    end

    local loadListT = {}
    for h, k1 in pairs(resList) do 
        loadListT[h] = 1
        if not cachedHeroResList[h] then
           assetLoadMgr:LoadAssetAsync(h, nil, nil)
        end
    end
    for k, h in pairs(cachedHeroResList) do
        if not loadListT[k] then
            assetLoadMgr:UnloadAssetBundle(k)
            cachedHeroResList[k] = nil
        end
    end
    return loadListT
end

function PreLoadEnemyResource(heroList)
    if device_level_controller.OpenSystemQualityLevel() > device_param_util.eLowLevel  then
        loadEnemyList = PreLoadHeroResource(heroList, loadEnemyList)
    end
end

function PreLoadSelectHeroResource(heroList)
    loadSelectHeroList = PreLoadHeroResource(heroList, loadSelectHeroList)
end

function DelayPreLoadSelectHeroResource(heroList, delayTime)
    if heroList == nil then
        return
    end

    if delayTime == nil then
        delayTime = 0.1
    end
    util.DelayOneCall("PreLoadSelectHeroResource", function ()
        PreLoadSelectHeroResource(heroList)
    end, delayTime)
end

function PreLoadBattleLayoutResource(layoutList)
    local loadListT = {}
    for h, k1 in pairs(layoutList) do 
        loadListT[h] = 1
        if not loadBattleLayoutList[h] then
            assetLoadMgr:LoadAssetAsync(h, nil, nil)
        end
    end
    for k, h in pairs(loadBattleLayoutList) do
        if not loadListT[k] then
            assetLoadMgr:UnloadAssetBundle(k)
            loadBattleLayoutList[k] = nil
        end
    end
    loadBattleLayoutList = loadListT
end

function DelayPreLoadBattleLayoutResource(layoutList, delayTime)
    if layoutList == nil then
        return
    end
    
    if delayTime == nil then
        delayTime = 0.1
    end
    util.DelayOneCall("PreLoadBattleLayoutResource", function ()
        PreLoadBattleLayoutResource(layoutList)
    end, delayTime)
end

function PreLoadBattleBackground(background)
    if background == nil then
        log.Error("战斗背景不能设置为空")
    end
    if loadBattleBackground == background then
        return
    end

    assetLoadMgr:LoadAssetAsync(background, nil, nil)
    if loadBattleBackground then
        assetLoadMgr:UnloadAssetBundle(loadBattleBackground)
    end
    loadBattleBackground = background
end

function DelayPreLoadBattleBackground()
    util.DelayOneCall("PreLoadBattleBackground", function ()
        local background, Yoffset = battle_config.GetBattleBackground()
            PreLoadBattleBackground(background)
    end, 0.1)
end

function PreLoadPreludus(preludus)
    if preludus == nil then
        log.Error("战斗开场序幕资源不能为空")
    end
    if loadPreludus == preludus then
        return
    end

    assetLoadMgr:LoadAssetAsync(preludus, nil, nil)
    if loadPreludus then
        assetLoadMgr:UnloadAssetBundle(loadPreludus)
    end
    loadPreludus = preludus
end

function DelayPreLoadPrludus()
    util.DelayOneCall("DelayPreLoadPreludus", function ()
        local preludus = battle_config.GetPreludeTimelineRes()
        PreLoadPreludus(preludus)
    end, 0.1)
end

function CallPreload(delayTime)
    -- log.Error("CallPreload")
    if delayTime == nil then
        delayTime = 2
    end
    util.DelayOneCall("PreLoad",PreLoad,delayTime)
end

function PreLoad()
    util.PeekWatch("Login", "开始预加载英雄模型")   

    local reses, modulResList = GetResPreload(6)
    local loadListT = {}
    for h, k1 in pairs(reses) do 
        loadListT[h] = 1
        if not loadSelectHeroList[h] then
            if modulResList[h] then
                assetLoadMgr:LoadAssetAsyncWithCache(h, nil, LoadCallback)
            else
                assetLoadMgr:LoadAssetAsync(h, nil, LoadCallback)
            end
        end
    end
    for k, h in pairs(loadSelectHeroList) do
        if not loadListT[k] then
            assetLoadMgr:UnloadAssetBundle(k)
            loadSelectHeroList[k] = nil
        end
    end
    loadSelectHeroList = loadListT
    util.PeekWatch("Login", "预加载英雄模完成")   
end
function LoadCallback()
    
end

function OnSceneDestroy()
    Close()
    
    if not util.IsObjNull(playerRoot) then 

		GameObject.Destroy  (playerRoot)
        IOSystem.UnloadAssetBundle(assetBundleName, "battle_preview_manager")
 
    end

    
    -- for k, h in pairs(loadList) do 
    --     assetLoadMgr:UnloadAssetBundle(k)
    --     --IOSystem.UnloadAssetBundle(k, "battle_preview_manager") 
    -- end
    -- loadList = {}
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)