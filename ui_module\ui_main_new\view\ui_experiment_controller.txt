local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type
local tonumber = tonumber
local tostring = tostring
local festival_activity_cfg = require "festival_activity_cfg"
local festival_activity_mgr = require "festival_activity_mgr"
local cfg_util = require "cfg_util"
local reward_mgr = require "reward_mgr"
local common_new_pb = require "common_new_pb"
local net_arena_module = require "net_arena_module"
local flow_text = require "flow_text"
local sand_ui_event_define = require "sand_ui_event_define"
local event = require "event"
local main_slg_const = require "main_slg_const"
local main_slg_data = require "main_slg_data"
local util = require "util"
local player_mgr = require "player_mgr"
local module_jumping = require "module_jumping"
local log = require "log"
local game_scheme = require "game_scheme"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local gw_home_building_data = require "gw_home_building_data"
local GWG = GWG
local ReviewingUtil = require "ReviewingUtil"
--region Controller Life
module("ui_experiment_controller")
local controller = nil
local UIController = newClass("ui_experiment_controller", controller_base)

local activityData = {
    [2017] = true,
    [2019] = true
}

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}

    main_slg_data.SetCurExperimentRecord(main_slg_const.MainExperimentType.None)
    
    --请求新手竞技场时间 
    local arena_data = require "arena_data"
    local arena_common_mgr = require "arena_common_mgr"
    local arena_common_const = require "arena_common_const"
    local isArena = arena_data.IsAreaNewOpen()
    if isArena then
        net_arena_module.Send_ARENA_GET_TIME()
        --请求进入新手竞技赛
        net_arena_module.Send_ARENA_ENTER(common_new_pb.CrystalCrown)

    end
    self.clickFuncDic = {
        --城镇复兴
        [1]=function() 
            self:OnBtnCloseClickedProxy()
            local curEventId = GWG.GWHomeMgr.chapterData.GetCurrentEventPoint()
            module_jumping.Jump("slg_aai",tostring(curEventId))
            arena_data.SetArenaIsFirstEnter(main_slg_const.MainExperimentType.CityRecovery)
        end,
        --竞技场
        [2]=function()
            return self:EnterArenaNew()
        end,
        --阵营试炼
        [3]=function()
            log.Warning("阵营试炼")
            --flow_text.Add("暂未开放")
            ui_window_mgr:ShowModule("ui_camp_trial",nil,nil,{
                lastViewName = "ui_experiment",
            })
            arena_data.SetArenaIsFirstEnter(main_slg_const.MainExperimentType.CampTrial)
            self:CloseView()
            return main_slg_const.MainExperimentType.CampTrial
        end,
        --冒险之旅
        [4]=function()
            log.Warning("冒险之旅")
            local arena_data = require "arena_data"
            arena_data.SetArenaIsFirstEnter(main_slg_const.MainExperimentType.Adventure)
            self:CloseView()
            ui_window_mgr:ShowModule("new_hook_scene")
            return main_slg_const.MainExperimentType.Adventure
        end,
        [5]= function()
            log.Warning("巅峰竞技场")
            self:CloseView()
            --ui_window_mgr:ShowModule("ui_weekend_arena")
            arena_data.OpenAreaWeekend(nil, true)
            return main_slg_const.MainExperimentType.Arena_Weekend
        end,
        [6]= function()
            log.Warning("3V3竞技场")
           --[[ 
            ui_window_mgr:ShowModule("ui_guild_trial")]]
            self:CloseView()
            arena_data.OpenArea3v3(nil, true)
            return main_slg_const.MainExperimentType.Arena_3v3
        end,
        [7]= function()
            log.Warning("风暴竞技场")
            self:CloseView()
            arena_common_mgr.OpenArenaUI(arena_common_const.ArenaType.Storm, nil,  festival_activity_cfg.ActivityCodeType.StormArena)
            return main_slg_const.MainExperimentType.Arena_Storm
        end,
    }
    self:InitGameItemList()
end

function UIController:EnterArenaNew()
    local arena_data = require "arena_data"
    log.Warning("新手竞技场")
    --如果时间已经结束,则弹活动已关闭
    local finishTime = net_arena_module.SyncTime and net_arena_module.SyncTime.crownTime and net_arena_module.SyncTime.crownTime  or -1
    local isFinish = finishTime <= 0
    if isFinish then
        --活动已关闭
        flow_text.Add(lang.Get(665033))
        return
    end
    self:CloseView()
    arena_data.OpenAreaNew(function()
        event.Trigger(sand_ui_event_define.GW_EXIT_EXPERIENCE,main_slg_const.MainExperimentType.Arena)
    end,true)
    return main_slg_const.MainExperimentType.Arena_New
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil

    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()
    local gw_event_activity_define = require "gw_event_activity_define"
    self:RegisterEvent(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,function(_,activityId)
        if activityData[activityId] then
            self:InitGameItemList()
        end
    end)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnCloseClickedProxy()
    self:CloseView()
    --util.MainPanel(true)
    --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
end




function UIController:InitAllList()
    local allList = {}
    self.sortFunc = self.sortFunc or function(a,b)
        if a.SortPriority ~= b.SortPriority then
            return a.SortPriority > b.SortPriority
        else
            return a.id < b.id
        end
    end
    local num = game_scheme:DailyGameplay_nums()
    for i = 0 ,num-1 do
        local cfg = game_scheme:DailyGameplay(i)
        local item = {}
        item.icon = cfg.backgroundID
        item.clickFunc = function()
            local func = self.clickFuncDic[cfg.gameplayType]
            local result =func and  func()
            if result then
                main_slg_data.SetCurExperimentRecord(result)
            end
        end
        item.title = lang.Get(cfg.GameplayName)
        
        item.rewardList = cfg_util.GetLuaArrayByCfgList(cfg.RewardDisplay)
        item.rewardList = reward_mgr.GetRewardGoodsMergers(item.rewardList)

        
       
        
        item.SortPriority = cfg.SortPriority
        item.id = cfg.gameplayType
        item.unlockText = lang.Get(cfg.Unlocklang or 0)
        item.cfg = cfg
      
        table.insert(allList,item)
        table.sort(allList,self.sortFunc)
    end
    return allList
end


--判断是否解锁
function UIController:GetIsUnlock(data)
    --return true
    if data.cfg then
        local isUnlock = true
        if data.cfg.UnlockConditions ~= 0 then
            local function_open = require "function_open"
            isUnlock = function_open.CheckFunctionIsOpen(data.cfg.UnlockConditions)
        else
            if data.cfg.UnlockConditions2 ~= 0 then
                local activityId = data.cfg.UnlockConditions2
                --没有解锁的话，设置解锁的时间倒计时
                isUnlock = festival_activity_mgr.GetIsOpenByActivityID(activityId,true)
                if not isUnlock then
                    local requestedData = festival_activity_mgr.GetRequestedActivityData(activityId)
                    if requestedData and requestedData.startTime then
                        local os = require "os"
                        local duration =   requestedData.startTime - os.server_time()
                        if duration <= 0 then
                            data.unlockText = lang.Get(665120)
                        else
                            data.startTime = requestedData.startTime
                        end
                    end
                end
            end
        end

        return isUnlock
    end
    return false
end


--判断是否显示
function UIController:GetIsShow(data)
    if data.cfg then
        if ReviewingUtil.IsReviewing() then
            --审核屏蔽新手竞技场 巅峰竞技场 3v3竞技场
            if data.cfg.gameplayType == 2 or data.cfg.gameplayType == 5 or data.cfg.gameplayType == 6 or data.cfg.gameplayType == 7 then
                return false
            end
        end
        --判断玩家等级
        local isLevelOk = player_mgr.GetPlayerLV() >= data.cfg.DisplayConditions1
        local SpecialConditionsType = data.cfg.SpecialConditionsType
        local SpecialConditionsparameter = data.cfg.SpecialConditionsparameter
        local HideConditionsType = data.cfg.HideConditionsType
        local HideConditionsParam = data.cfg.HideConditionsParam
        --local curEventId = GWG.GWHomeMgr.chapterData.GetCurrentEventPoint()
        local isHide = false
        if SpecialConditionsType == 1 then
            if SpecialConditionsparameter ~= 0 then
                if GWG.GWHomeMgr.chapterData.CheckPassEvent(SpecialConditionsparameter) then
                    isHide = false
                else
                    isHide = true
                end
            end
        end
        if HideConditionsType == 1 then
            if HideConditionsParam ~= 0 then
                if GWG.GWHomeMgr.chapterData.CheckPassEvent(HideConditionsParam) then
                    isHide = true
                else
                    isHide = false
                end
            end
        end
        
        --判断是否结束后隐藏
        local AutoHide = data.cfg.AutoHide == 1
        --如果是用activityMain的逻辑,如果配置AutoHide为1，就判断是否活动结束了
        if AutoHide and data.cfg.UnlockConditions == 0 and data.cfg.UnlockConditions2 ~= 0 then
            local activityId = data.cfg.UnlockConditions2
            local isHide = festival_activity_mgr.IsRequestedActivityEnd(activityId)
            if isHide then
                return false
            end
        end
        if isLevelOk and not isHide then
            if data.cfg.DisplayConditions2 ~= "" then
                --;分割建筑判断，#分割建筑id跟等级
                local buildList = string.split(data.cfg.DisplayConditions2,";")
                if not buildList then
                    return true
                end
                for _,buildStr in ipairs(buildList) do
                    local buildInfo = string.split(buildStr,"#")
                    local buildId = tonumber(buildInfo[1])
                    local buildLv = tonumber(buildInfo[2])
                    if buildId and buildLv then
                        local maxLv = gw_home_building_data.GetBuildingDataMaxLevel(buildId)
                        if maxLv < buildLv then
                            return false
                        end
                    end
                end
            elseif data.cfg.DisplayConditions3 ~= 0 then
                return festival_activity_mgr.GetIsOpenByActivityID(data.cfg.DisplayConditions3,true)
            end
        else
            return false
        end
    end
    return true
end


function UIController:InitGameItemList()
    self.CData.gameDataList = self.CData.gameDataList or self:InitAllList()
    local tmpDataList = {}
    for _,v in ipairs(self.CData.gameDataList) do
        if self:GetIsShow(v) then
            --设置是否显示
            table.insert(tmpDataList,v)
            --设置是否解锁
            v.isUnlock = self:GetIsUnlock(v)
        end
    end
    self:TriggerUIEvent("SetGameItemData",tmpDataList)
    
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
