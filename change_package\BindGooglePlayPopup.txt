--n:陈泳冰
--d:2022年1月28日
--v:1.0.0
--t:绑定账号

local print = print
local require = require
local lang = require "lang"
local event = require "event"
local q1sdk = require "q1sdk"
local class = require "class"
local ui_base = require "ui_base"
local login_pb = require "login_pb"
local ui_window_mgr = require "ui_window_mgr"
local ChangePackgeConfig = require "ChangePackgeConfig"
local goods_item = require "goods_item_new"
local game_scheme 	= require "game_scheme"
local dump = dump


module("BindGooglePlayPopup")
local window = nil
local BindGooglePlayPopup = {}
BindGooglePlayPopup.widget_table =
{
	bgBtn = {path = "closeBtn", type = "Button",backEvent = true},
	title = {path = "Frame/Title", type = "Text"},	
    textMessage = {path = "Frame/TextMessage", type = "Text"},	 
	BtnBind = {path = "Frame/BtnBind", type = "Button"},
	icongroup = {path = "Frame/ItemGroup", type = "RectTransform"},
}
function BindGooglePlayPopup:Init()
    self:SubscribeEvents()
end

function BindGooglePlayPopup:OnShow()
    self.__base:OnShow()
    self:RefreshUI()
end

function BindGooglePlayPopup:RefreshUI()
    self.textMessage.fontSize = 28
    self.textMessage.text = lang.Get(257001)
    self.title.text = lang.Get(257000)
end

function BindGooglePlayPopup:SubscribeEvents()
    --关闭
    self.closeBtnEvent = function()
        ui_window_mgr:UnloadModule("BindGooglePlayPopup")
    end
    self.bgBtn.onClick:AddListener(self.closeBtnEvent)

    --去绑定
    self.goBtnEvent = function()
        event.Trigger(event.GAME_EVENT_REPORT, "Click_bind_Google", {})
        self.bdcallback = function(success, errorCode, msg, jsonStr)
            print("【绑定结果】:",jsonStr)
            if success then
                q1sdk.SetGoogleBindType(jsonStr)
                ui_window_mgr:UnloadModule("BindGooglePlayPopup")
                ui_window_mgr:ShowModule("DownLoadNewpopup")
            end
        end
        print("从换包模块进入绑定【绑定】")
        self:BindAccount(self.bdcallback)
    end
    self.BtnBind.onClick:AddListener(self.goBtnEvent)

    self.itemIcon = self.itemIcon or {}
    if ChangePackgeConfig.ChangeData then
        local Reward =  ChangePackgeConfig.ChangeData.BindGoogleRewardID.data
        dump(ChangePackgeConfig.ChangeData)
        for i = 0 ,#Reward do
            local rewardCfg = game_scheme:Reward_0(Reward[i])
            if  rewardCfg.arrParam[i] ~= nil then
                self.itemIcon[i] = goods_item.CGoodsItem():Init(self.icongroup,nil,1)
                self.itemIcon[i]:SetGoods(nil, rewardCfg.arrParam[0], rewardCfg.arrParam[1],nil)
            end
        end
    end
end

function BindGooglePlayPopup:BindAccount(bindHandler)
    q1sdk.UserBind(login_pb.enLoginPartnerID_Google, bindHandler)
end

function BindGooglePlayPopup:UnsubscribeEvents()
    self.bgBtn.onClick:RemoveListener(self.closeBtnEvent)
    self.BtnBind.onClick:RemoveListener(self.goBtnEvent)
end

function BindGooglePlayPopup:Close()
	if self and self:IsValid() then
		self:UnsubscribeEvents()

        for k,v in pairs(self.itemIcon) do
            if self.itemIcon[k] then
                self.itemIcon[k]:Dispose()
                self.itemIcon[k] = nil
            end
        end
    end
	self.__base:Close()
	window = nil
end

local CM = class(ui_base, nil, BindGooglePlayPopup)
function Show()
	if window == nil then
		window = CM()
		window._NAME = _NAME;
        window:LoadUIResource("ui/prefabs/uimessageboxbindnew.prefab", nil, nil, nil, true)
	else
		window:Show()
	end
	return window
end

function Hide()
	if window ~= nil then
		window:Hide()
	end
end

function Close()
	if window ~= nil then
		window:Close()
		window = nil
	end
end

function OnSceneDestroy()
	Close()
end
event.Register(event.SCENE_DESTROY_NEW, OnSceneDestroy)