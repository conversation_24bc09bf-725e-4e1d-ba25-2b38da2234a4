local string = string
local require = require
local pairs = pairs
local ipairs = ipairs
local math = math
local tostring = tostring
local xpcall = xpcall
local GWG = GWG

local log = require "log"
local typeof = typeof
local GWHomeMgr = GWHomeMgr
local GWAdmin = GWAdmin
local ui_chat_define_gw = require "ui_chat_define_gw"
local radar_define = require "radar_define"
local util = require "util"
local game_scheme = require "game_scheme"
local event_alliance_define = require "event_alliance_define"
local gw_event_activity_define = require "gw_event_activity_define"
local mail_define_gw = require "mail_define_gw"
local mail_data_gw = require "mail_data_gw"
local red_const = require "red_const"
local table = require "table"
local os = require "os"
local sand_ui_event_define = require "sand_ui_event_define"
local event = require "event"
local gw_ed = require "gw_ed"
local module_jumping = require "module_jumping"
local gw_sand_event_define = require "gw_sand_event_define"
local main_slg_const = require("main_slg_const")
local lang = require("lang")
local ui_window_mgr = require "ui_window_mgr"
local GWConst = require "gw_const"
local function_open_mgr = require "function_open_mgr"---@description 主界面按钮组定义
local festival_activity_cfg = require "festival_activity_cfg"
local event_carriage_define = require "event_carriage_define"
local event_allianceTrain_define = require "event_allianceTrain_define"
local event_DesertStrom_define = require "event_DesertStrom_define"
local ReviewingUtil = require "ReviewingUtil"
local Sprite = CS.UnityEngine.Sprite
local Rect = CS.UnityEngine.Rect
local Vector2 = CS.UnityEngine.Vector2
local UIUtil = CS.Common_Util.UIUtil
local Animator = CS.UnityEngine.Animator
local Canvas = CS.UnityEngine.Canvas
local net_module_open = require "net_module_open"
local moduleOpenPro_pb = require "moduleOpenPro_pb"

module("main_slg_common_define")

local function ShowModule(uiName)
    --event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
    ui_window_mgr:ShowModule(uiName)
end

loginNeedUpdateGroupType = {
    main_slg_const.MainButtonGroupType.LeftTop,
    main_slg_const.MainButtonGroupType.LeftBottom,
    main_slg_const.MainButtonGroupType.RightTop,
    main_slg_const.MainButtonGroupType.RightBottom,
    main_slg_const.MainButtonGroupType.LeftBottom2, --左下的第二组按钮,用于治疗等特殊按钮
    main_slg_const.MainButtonGroupType.LeftBottom5
}

--活动快捷入口的buttonType的偏移
ActivityQuickEntranceOffset = 1000000
groupButtonType = {
    [main_slg_const.MainButtonGroupType.LeftTop] = {
        main_slg_const.MainButtonType.buildQueue,
        main_slg_const.MainButtonType.research,
    },
    [main_slg_const.MainButtonGroupType.LeftBottom] = {
        main_slg_const.MainButtonType.buildList,
        main_slg_const.MainButtonType.sandSearch,
        main_slg_const.MainButtonType.info,
        main_slg_const.MainButtonType.bar,
        main_slg_const.MainButtonType.survivor,
        main_slg_const.MainButtonType.dogHouse,
        --沙漠风暴专属
        main_slg_const.MainButtonType.CityRelocation,
        main_slg_const.MainButtonType.FieldHospital,
        main_slg_const.MainButtonType.NormalHospital,
        main_slg_const.MainButtonType.SetSquad,
        main_slg_const.MainButtonType.DefenceQueue,

        ---火车位置与货车位置互斥,货车优先级高

        main_slg_const.MainButtonType.allianceTrain,
        ---马车第二位置
        main_slg_const.MainButtonType.vehicle,
        ---代办应策划需求放在最上面，注意请在前面添加
        main_slg_const.MainButtonType.schedule,
    },
    [main_slg_const.MainButtonGroupType.RightTop] = {
        --main_slg_const.MainButtonType.gift,  
        --main_slg_const.MainButtonType.Benefits,
        --main_slg_const.MainButtonType.daily,    
    },
    [main_slg_const.MainButtonGroupType.RightBottom] = {
        main_slg_const.MainButtonType.bag,
        main_slg_const.MainButtonType.mail,

    },
    [main_slg_const.MainButtonGroupType.LeftBottom2] = {
        main_slg_const.MainButtonType.heal,
        main_slg_const.MainButtonType.generalTrail,
    },
    [main_slg_const.MainButtonGroupType.LeftBottom4] = {
        main_slg_const.MainButtonType.survivor2,
        main_slg_const.MainButtonType.dogHouse2,
    },
    [main_slg_const.MainButtonGroupType.LeftBottom5] = {
        main_slg_const.MainButtonType.ZombieTreasure,
    },
    [main_slg_const.MainButtonGroupType.bottom] = {
        main_slg_const.MainButtonType.miniGame,
        main_slg_const.MainButtonType.trial,
        main_slg_const.MainButtonType.hero,
        main_slg_const.MainButtonType.union
    },
    [main_slg_const.MainButtonGroupType.bubble] = {
        main_slg_const.MainButtonType.unionHelpBubble,
        main_slg_const.MainButtonType.chatAllianceMass,
        main_slg_const.MainButtonType.unionWarBubble,
        main_slg_const.MainButtonType.unionJoinBubble,
        main_slg_const.MainButtonType.arenaChallengeBubble,
        main_slg_const.MainButtonType.citySiegeBubble,
        main_slg_const.MainButtonType.invitationBubble,
    },
    [main_slg_const.MainButtonGroupType.RightTop2] = {
        --main_slg_const.MainButtonType.duelGiftPackage,
    },
    [main_slg_const.MainButtonGroupType.RightTop3] = {
        -- main_slg_const.MainButtonType.resourceDownload,
        -- main_slg_const.MainButtonType.questionnaire,
        -- main_slg_const.MainButtonType.changePackage,
    },
}
groupButtonTypeOpenMap = {
    [main_slg_const.MainButtonGroupType.LeftTop] = {
        --建造队列
        [main_slg_const.MainButtonType.buildQueue] = function_open_mgr.OpenIdEnum.BuildQueueSwitch,
        --科技队列
        [main_slg_const.MainButtonType.research] = function_open_mgr.OpenIdEnum.Scientific
    },
    [main_slg_const.MainButtonGroupType.RightTop] = {
        --礼包
        [main_slg_const.MainButtonType.Benefits] = function_open_mgr.OpenIdEnum.MallSwitch,
    },
    [main_slg_const.MainButtonGroupType.LeftBottom] = {
        --建造按钮
        [main_slg_const.MainButtonType.buildList] = function_open_mgr.OpenIdEnum.Build,
        --雷达
        [main_slg_const.MainButtonType.info] = function_open_mgr.OpenIdEnum.Radar,
        --货车
        [main_slg_const.MainButtonType.vehicle] = function_open_mgr.OpenIdEnum.TradeCaravan,
        --火车
        [main_slg_const.MainButtonType.allianceTrain] = function_open_mgr.OpenIdEnum.AllianceTrain,
        --任务
        [main_slg_const.MainButtonType.bar] = function_open_mgr.OpenIdEnum.ChapterTasks,
    },
    [main_slg_const.MainButtonGroupType.RightBottom] = {
        --邮件
        [main_slg_const.MainButtonType.mail] = function_open_mgr.OpenIdEnum.Mail,
        --背包
        [main_slg_const.MainButtonType.bag] = function_open_mgr.OpenIdEnum.Backpack,
    },
}
--固定场景的按钮
--若没有在此处定义，则默认在所有地方都显示。（沙漠风暴除外）
sceneOnlyData = {
    --[城市独有]
    [main_slg_const.MainButtonType.buildQueue] = {
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },
    [main_slg_const.MainButtonType.research] = {
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },
    [main_slg_const.MainButtonType.buildList] = {
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },
    [main_slg_const.MainButtonType.survivor] = {
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },
    [main_slg_const.MainButtonType.survivor2] = {
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },
    [main_slg_const.MainButtonType.dogHouse] = {
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },
    [main_slg_const.MainButtonType.dogHouse2] = {
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },

    --沙盘独有
    [main_slg_const.MainButtonType.sandSearch] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
    },
    [main_slg_const.MainButtonType.generalTrail] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
    },

    --沙漠风暴独有
    [main_slg_const.MainButtonType.DefenceQueue] = {
        [main_slg_const.MainSceneType.DesertStormSceneOnly] = true,
    },
    [main_slg_const.MainButtonType.SetSquad] = {
        [main_slg_const.MainSceneType.DesertStormSceneOnly] = true,
    },
    [main_slg_const.MainButtonType.NormalHospital] = {
        [main_slg_const.MainSceneType.DesertStormSceneOnly] = true,
    },
    [main_slg_const.MainButtonType.FieldHospital] = {
        [main_slg_const.MainSceneType.DesertStormSceneOnly] = true,
    },
    [main_slg_const.MainButtonType.CityRelocation] = {
        [main_slg_const.MainSceneType.DesertStormSceneOnly] = true,
    },
    [main_slg_const.MainButtonType.RewardChess] = {
        [main_slg_const.MainSceneType.DesertStormSceneOnly] = true,
    },

    --泛用（即只在某个场景不显示，但其他场景显示）
    [main_slg_const.MainButtonType.bag] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
        [main_slg_const.MainSceneType.DesertStormSceneOnly] = true,
    },
    [main_slg_const.MainButtonType.mail] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
        [main_slg_const.MainSceneType.DesertStormSceneOnly] = true,
    },
    [main_slg_const.MainButtonType.vehicle] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },
    [main_slg_const.MainButtonType.allianceTrain] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },
    [main_slg_const.MainButtonType.bar] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },
    [main_slg_const.MainButtonType.info] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },
    [main_slg_const.MainButtonType.schedule] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },
    [festival_activity_cfg.ActivityEntranceType.LoginGift] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
    },
    [main_slg_const.MainButtonType.trial] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
        [main_slg_const.MainSceneType.DesertStormSceneOnly] = true,
    },
    [main_slg_const.MainButtonType.miniGame] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
        [main_slg_const.MainSceneType.DesertStormSceneOnly] = true,
    },
    [main_slg_const.MainButtonType.hero] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
        [main_slg_const.MainSceneType.DesertStormSceneOnly] = true,
    },
    [main_slg_const.MainButtonType.union] = {
        [main_slg_const.MainSceneType.SandSceneOnly] = true,
        [main_slg_const.MainSceneType.CitySceneOnly] = true,
        [main_slg_const.MainSceneType.DesertStormSceneOnly] = true,
    },

}


--[[
    icon, 主界面的icon资源名
    clickFunc, 点击按钮的回调
    getTextFunc, 显示文本的方法
    redData, 红点数据
    conditionShowFunc, 条件是否显示
    refreshMsg,监听刷新消息，string类型或者string列表,不支持参数判断
]]
---@description 按钮配置
buttonConfig = {
    [main_slg_const.MainButtonType.buildQueue] = {
        icon = "zjm_btn_jianzhu",
        clickFunc = function()
            --打开城建建筑
            ShowModule("ui_bs_buildpop", true)
        end,
        getTextFunc = function()
            --获取文本
            local maxQueue = 0;
            local busyQueue = 0;
            local gw_home_building_queue_data = require "gw_home_building_queue_data"
            local buildingQueue = gw_home_building_queue_data.GetAllQueue();
            if buildingQueue ~= nil then
                for i, v in pairs(buildingQueue) do
                    if v.uOverTime == 0 then
                        if v.uFlag ~= 0 then
                            maxQueue = maxQueue + 1
                            if v.uSid ~= 0 then
                                busyQueue = busyQueue + 1
                            end
                        end
                    else
                        local curTimestamp = os.server_time();
                        local timeLeft = v.uOverTime - curTimestamp;
                        if timeLeft > 0 then
                            maxQueue = maxQueue + 1
                            if v.uSid ~= 0 then
                                busyQueue = busyQueue + 1
                            end
                        end
                    end
                end
            end
            local strList = { busyQueue, "/", maxQueue }
            local str = table.concat(strList)
            return str
        end,
        conditionShowFunc = function()
            local function_open_mgr = require "function_open_mgr"
            return function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.BuildQueueSwitch)
        end,
        redData = {
            redType = red_const.Enum.BuildQueue,
            redPathType = red_const.Type.Default --红点类型
        },
        refreshMsg = event.UPDATE_ALL_BUILDING_QUEUE
    },
    [main_slg_const.MainButtonType.research] = {
        icon = "zjm_btn_keji",
        objName = "technologyQueue_btn",
        clickFunc = function()
            --打开科研
            local unforced_guide_event_define = require "unforced_guide_event_define"
            event.Trigger(unforced_guide_event_define.click_technology)
            local technologyData = require "technology_data"
            local technologyQueueLen = technologyData.GetTechnologyQueueLen()
            if technologyQueueLen <= 0 then
                technologyData.JumpToPriortyScientific()
            else
                ShowModule("ui_mini_technology_queue_panel")
            end
            --打开科研打点
            event.EventReport("Scientific_ViewQueue", {  })
        end,
        getTextFunc = function()
            --获取文本
            local maxQueue = 0;
            local technologyData = require "technology_data"
            local technologyQueueLen = technologyData.GetTechnologyQueueLen(true)
            if technologyData.GetFirstQueueUnlock() then
                maxQueue = maxQueue + 1;
            end
            if technologyData.GetSecondQueueUnlock() then
                maxQueue = maxQueue + 1;
            end
            local strList = { technologyQueueLen, "/", maxQueue }
            local str = table.concat(strList)
            return str
        end,
        conditionShowFunc = function()
            local function_open_mgr = require "function_open_mgr"
            if not function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.Scientific) then
                return false
            end
            local gw_home_building_data = require "gw_home_building_data"
            local technologyBuild = gw_home_building_data.GetMaxLevelBuildingDataByBuildingID(GWConst.ScientificBuildingId.Building_1)--获取第一科研中心的数据，为nil
            if technologyBuild == nil or technologyBuild.nLevel < 1 then
                technologyBuild = gw_home_building_data.GetMaxLevelBuildingDataByBuildingID(GWConst.ScientificBuildingId.Building_2)
            end
            return technologyBuild ~= nil and technologyBuild.nLevel >= 1
        end,
        redData = {
            redType = red_const.Enum.TechnologyList,
            redPathType = red_const.Type.Default --红点类型
        },
        refreshMsg = event.UPDATE_QUEUE
    },
    [main_slg_const.MainButtonType.sandSearch] = {
        icon = "zjm_z_cz_sousuo",
        clickFunc = function()
            --打开沙盘搜索
            ShowModule("ui_sand_search", true)
        end
    },
    [main_slg_const.MainButtonType.buildList] = {
        icon = "zjm_z_cz_jianzhu",
        objName = "buildList_btn",
        clickFunc = function()
            --打开建筑列表
            ShowModule("ui_bs_buildbar")
        end,
        conditionShowFunc = function()
            local function_open_mgr = require "function_open_mgr"
            return function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.Build)
        end,
        redData = {
            redType = red_const.Enum.BuildList,
            redPathType = red_const.Type.Num --红点类型
        }
    },
    [main_slg_const.MainButtonType.survivor] = {
        icon = "xinshou_zjm_xcz_icon",
        clickFunc = function()
            local gw_common_util = require "gw_common_util"
            gw_common_util.SwitchToHome(function()
                --关闭弱引导
                local unforced_guide_event_define = require "unforced_guide_event_define"
                event.Trigger(unforced_guide_event_define.click_first_recruit)
                local gw_home_building_data = require "gw_home_building_data"

                local building = gw_home_building_data.GetMaxLevelBuildingDataByBuildingID(GWConst.enBuildingType.enBuildingType_Wall * 1000)
                local buildingData = gw_home_building_data.GetBuildingCompBySid(building.uSid)
                buildingData:ShowArrowAtFirstSurvivor()

            end, true)
        end,
        conditionShowFunc = function()
            --条件额外加一个关卡判断 
            local laymain_data = require "laymain_data"
            local level = laymain_data.GetPassLevel()
            --备注 45是关卡45，
            if level and level >= 45 then
                return false
            end
            local gw_home_survivor_data = require "gw_home_survivor_data"
            return #gw_home_survivor_data.GetOutsideSurvivorList(GWConst.enCityRewardType.enRewardType_Survivor) > 0
            --return function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.Build)
        end,
        redData = {
            redType = red_const.Enum.Survivor,
            redPathType = red_const.Type.Num --红点类型
        },
        refreshMsg = event.GW_HOME_UPDATE_VISTING_SURVIVOR
    },
    [main_slg_const.MainButtonType.DefenceQueue] = { --沙漠风暴编辑城防
        icon = "yjzdz_icon_chengFang",
        clickFunc = function()
            local gw_const = require "gw_const"
            ui_window_mgr:ShowModule("ui_bs_fortified_wall_panel", nil, nil, {
                sandType = gw_const.SandType[gw_const.ESceneType.Storm]
            })
        end
    },
    [main_slg_const.MainButtonType.SetSquad] = { --沙漠风暴编辑队伍
        icon = "yjzdz_icon_bianDui",
        clickFunc = function()
            local new_hero_select_mgr = require "new_hero_select_mgr"
            new_hero_select_mgr.ShowSelectPanel(new_hero_select_mgr.EnumTeamType.DesertStorm, { index = 1, showMainPanel = true })
        end
    },
    [main_slg_const.MainButtonType.NormalHospital] = { --沙漠风暴打开医院
        icon = "yjzdz_icon_yiYuan",
        clickFunc = function()
            --local gw_storm_mgr = require "gw_storm_mgr"
            --local hospitalData = gw_storm_mgr.GetStormData().GetHospitalData();
            --if hospitalData and hospitalData.uDoneTime - os.server_time() <= 0 then
            --    local ui_bs_hospital_panel = require "ui_desert_storm_hospital_panel"
            --    local data = ui_bs_hospital_panel:GetSoldierData()
            --    local gw_storm_mgr = require "gw_storm_mgr"
            --    --net_city_module.MSG_CITY_HOSPITAL_CURE_SOLDIER_REQ(data)
            --    gw_storm_mgr.OnTreatSoldierAtPersonalHospital(data.count)
            --else
            ui_window_mgr:ShowModule("ui_desert_storm_hospital_panel")
            --end

        end,
        ShowEffectList = {
            [1] = "art/effects/effects/effect_ui_desert_kezhiliao/prefabs/effect_ui_desert_kezhiliao.prefab",
            [2] = "art/effects/effects/effect_ui_desert_zhiliaozhong/prefabs/effect_ui_desert_zhiliaozhong.prefab",
        },
        ShowEffectCheckFunc = function()
            local gw_storm_mgr = require "gw_storm_mgr"
            local hospitalData = gw_storm_mgr.GetStormData().GetHospitalData();
            local trainingData = gw_storm_mgr.GetStormData().GetTrainningCenterData();
            if hospitalData.uDoneTime and hospitalData.uDoneTime > 0 then
                return 2
            end
            if trainingData and trainingData.soldiers and trainingData.soldiers[1] and trainingData.soldiers[1].nHurtCount > 0 then
                return 1
            end
            return 0
        end,
        refreshMsg = gw_ed.GW_DESERT_STORM_HOSPITAL_DATA_UPDATE,
    },
    [main_slg_const.MainButtonType.FieldHospital] = { --沙漠风暴打开战地医院
        icon = "yjzdz_icon_zhanDiYiYuan",
        ShowEffectCheckFunc = function()
            local gw_storm_mgr = require "gw_storm_mgr"
            local battleHospitalData = gw_storm_mgr.GetStormData().GetFieldHospitalData();
            if battleHospitalData and battleHospitalData.nTreatFinishCnt then
                return battleHospitalData.nTreatFinishCnt > 0
            end
            return false
        end,
        ShowEffect = "art/effects/effects/effect_ui_desert_keshouqu/prefabs/effect_ui_desert_keshouqu.prefab",
        clickFunc = function()
            local force_guide_system = require "force_guide_system"
            local guideId = force_guide_system.GetCurStep()
            if guideId and (guideId == 897) then
                local force_guide_event = require "force_guide_event"
                force_guide_system.TriComEvent(force_guide_event.cEventDesertTherapy)
            else
                ui_window_mgr:ShowModule("ui_desert_storm_field_hospital_panel")
            end
        end,
        refreshMsg = gw_ed.GW_DESERT_STORM_FIELD_HOSPITAL_DATA_UPDATE,
        --表示每过一段时间刷新一次
        checkDataFunc = function()
            --只有在沙漠风暴时才刷新，否则不执行
            local main_slg_data = require "main_slg_data"
            local gw_const = require "gw_const"
            if main_slg_data.GetCurSceneType() == gw_const.ESceneType.Storm then
                local gw_storm_mgr = require "gw_storm_mgr"
                gw_storm_mgr.OnGetFieldHospitalData()
            end
        end,
        checkInterval = 10,
    },
    [main_slg_const.MainButtonType.CityRelocation] = { --沙漠风暴迁城
        icon = "yjzdz_icon_qianCheng",
        showTimer = true,
        --getTextTimerFunc = function(textUI) --这里会开启一个定时器，每秒执行一次 (下载的特殊处理，要每秒检测)
        --    local gw_storm_mgr = require "gw_storm_mgr"
        --    local cd = gw_storm_mgr.GetMoveCityCD()
        --    local timeLeft = cd and cd - os.server_time() or 0
        --    if not util.IsObjNull(textUI) then
        --        local time_util = require "time_util"
        --        textUI.text = time_util.FormatTime5(timeLeft)
        --    end
        --end,
        ShowEffectCheckFunc = function()
            local main_slg_data = require "main_slg_data"
            local gw_const = require "gw_const"
            if main_slg_data.GetCurSceneType() == gw_const.ESceneType.Storm then
                local gw_storm_mgr = require "gw_storm_mgr"
                local cd = gw_storm_mgr.GetMoveCityCD()
                local timeLeft = cd - os.server_time()
                return timeLeft <= 0
            end
            return true
        end,
        ShowEffect = "art/effects/effects/effect_ui_desert_keqiancheng/prefabs/effect_ui_desert_keqiancheng.prefab",
        countdownTimeCfg = {
            --buttonType = nil,--入口类型，用来做定时器的key  默认规则是只要你不自己定义，就会统一处理，你自己定义了就会取自己的
            timeStamp = function()
                local gw_storm_mgr = require "gw_storm_mgr"
                local cd = gw_storm_mgr.GetMoveCityCD()
                return cd or 0
            end, --结束时间戳 
            buttonType = main_slg_const.MainButtonGroupType.LeftBottom
        },
        timerShowFunc = function()
            local main_slg_data = require "main_slg_data"
            local gw_const = require "gw_const"
            if main_slg_data.GetCurSceneType() == gw_const.ESceneType.Storm then
                local gw_storm_mgr = require "gw_storm_mgr"
                local cd = gw_storm_mgr.GetMoveCityCD()
                local timeLeft = cd - os.server_time()
                return timeLeft > 0
            end
            return false
        end,
        clickFunc = function()
            ui_window_mgr:ShowModule("ui_desert_storm_city_relocation_panel")
        end,
        refreshMsg = event_DesertStrom_define.ON_DESERTSTORM_MOVE_CD_UPDATE
    },
    [main_slg_const.MainButtonType.info] = {
        icon = "zjm_z_cz_leida",
        clickFunc = function()
            --打开情报 雷达
            local radar_mgr = require "radar_mgr"
            radar_mgr.OpenRadarMainWindow()
        end,
        conditionShowFunc = function()
            local function_open_mgr = require "function_open_mgr"
            return function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.Radar)
        end,
        redData = {
            redType = red_const.Enum.RadarMain,
            redPathType = red_const.Type.Num
        }
    },

    [main_slg_const.MainButtonType.allianceTrain] = {
        icon = "zjm_z_cz_feiChuan",
        objName = "allianceTrainBtn",
        conditionShowFunc = function()
            local intercity_alliance_train_mgr = require "intercity_alliance_train_mgr"
            local intercity_trucks_data = require "intercity_trucks_data"
            local rewardCount = intercity_trucks_data.GetCarriagesRewardStatusCount()
            if rewardCount > 0 then
                return false
            end
            return intercity_alliance_train_mgr.CheckMainSlgIsOpenTrain()
        end,
        clickFunc = function()
            local gw_const = require "gw_const"
            local ui_window_mgr = require "ui_window_mgr"
            ui_window_mgr:CloseAll(gw_const.EHomeEnterCullLua)
            local buildTypeId = gw_const.enBuildingType.enBuildingType_AllianceTrainCenter
            local buildId = GWG.GWHomeMgr.cfg.GetBuildId(buildTypeId)
            local gw_home_camera_util = require "gw_home_camera_util"
            gw_home_camera_util.DoCameraToBuildMove(buildId, nil, nil, nil, nil, nil, nil, nil, 4, -7)
        end,
        refreshMsg = { event_carriage_define.TMSG_CARRIAGE_CNETER_DATA_NTF,
                       event_allianceTrain_define.TMSG_ALLIANCE_TRAIN_UPDATE_INFO_NTF,
                       event_alliance_define.EXIT_ALLIANCE },
        redData = {
            redType = red_const.Enum.AllianceTrain,
            redPathType = red_const.Type.Default
        }
    },

    --城际货车
    [main_slg_const.MainButtonType.vehicle] = {
        icon = "zjm_z_cz_mache",
        objName = "vehicle_btn",
        clickFunc = function()
            --打开货车
            local intercity_trucks_mgr = require "intercity_trucks_mgr"
            intercity_trucks_mgr.OpenTrucksScene()
        end,
        conditionShowFunc = function()
            local intercity_alliance_train_mgr = require "intercity_alliance_train_mgr"
            local trainOpen = intercity_alliance_train_mgr.CheckMainSlgIsOpenTrain()
            if trainOpen then
                local intercity_trucks_data = require "intercity_trucks_data"
                local rewardCount = intercity_trucks_data.GetCarriagesRewardStatusCount()
                if rewardCount > 0 then
                    return true
                end
                return false
            end
            local activity_open = require "activity_open"
            local intercity_trucks_data = require "intercity_trucks_data"
            local function_open_mgr = require "function_open_mgr"
            local opened = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.TradeCaravan)
            opened = opened and activity_open.CheckFunctionIsOpen(activity_open.OpenIdEnum.TradeCaravan)
            local needOpen = (intercity_trucks_data.GetHasTimeLeaved and intercity_trucks_data.GetHasTimeLeaved()) or intercity_trucks_data.GetMainCarriageRedCount() > 0
            return needOpen and opened
        end,
        redData = {
            redType = red_const.Enum.CarriageMain,
            redPathType = red_const.Type.Num
        },
        refreshMsg = { event_carriage_define.TMSG_CARRIAGE_CNETER_DATA_NTF,
                       event_allianceTrain_define.TMSG_ALLIANCE_TRAIN_UPDATE_INFO_NTF,
                       event_alliance_define.EXIT_ALLIANCE },
    },
    [main_slg_const.MainButtonType.bar] = {
        icon = "zjm_z_cz_renwu",
        objName = "barBtn",
        conditionShowFunc = function()
            local function_open_mgr = require "function_open_mgr"
            return function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.TavernTask)
        end,
        clickFunc = function()
            --打开酒馆任务 XQ TODO 改为ui_tavern_base
            ui_window_mgr:ShowModule("ui_tavern_base")
        end,
        refreshMsg = gw_ed.GW_HOME_EVENT_UPDATE,
        redData = {
            redType = red_const.Enum.TavernTask,
            redPathType = red_const.Type.Num
        }
    },
    [main_slg_const.MainButtonType.schedule] = {
        icon = "dbrc_icon_ruKou",
        objName = "scheduleBtn",
        conditionShowFunc = function()
            if ReviewingUtil.IsReviewing() then
                return false
            end
            --local function_open_mgr = require "function_open_mgr"
            local gw_common_util = require "gw_common_util"
            if gw_common_util.GetSandBaseCrossServiceState() then
                return false
            end
            return function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.TodoSchedule)
        end,
        clickFunc = function()
            --打开待办日程
            ui_window_mgr:ShowModule("ui_todo_schedule")
            local todo_schedule_data = require "todo_schedule_data"
            todo_schedule_data.ClearMainRedPoint()
        end,
        refreshMsg = gw_sand_event_define.GW_SAND_INIT_SELF_DATA_FINISH,
        redData = {
            redType = red_const.Enum.ScheduleEntrance,
            redPathType = red_const.Type.Default
        }
    },
    [main_slg_const.MainButtonType.heal] = {
        iconFunc = function()
            local GWHomeMgr = require "gw_home_mgr"
            return GWHomeMgr.soldierData.GetMainIconPath()
        end,
        clickFunc = function()
            local GWHomeMgr = require "gw_home_mgr"
            GWHomeMgr.soldierData.ClickMainIconHospital()
        end,
        conditionShowFunc = function()
            local GWHomeMgr = require "gw_home_mgr"
            local GWMgr = require "gw_mgr"
            local gw_const = require "gw_const"
            if GWMgr.curScene ~= gw_const.ESceneType.Sand then
                return false
            end
            local state = GWHomeMgr.soldierData.GetHospitalState()
            return state ~= GWConst.EHomeHospitalState.None
        end,
        refreshMsg = gw_ed.GW_HOSPITAL_STATE_UPDATE,
        maskIconShowFunc = function()
            local GWHomeMgr = require "gw_home_mgr"
            local state = GWHomeMgr.soldierData.GetHospitalState()
            return state == GWConst.EHomeHospitalState.HealingFinish
        end,
        maskIconFunc = function()
            local GWHomeMgr = require "gw_home_mgr"
            return GWHomeMgr.soldierData.GetMainMaskIconPath()
        end,
        maskType = main_slg_const.MainMaskIconType.soldier,
    },
    [main_slg_const.MainButtonType.generalTrail] = {
        icon = "zjm_z_cz_anyeBG",
        clickFunc = function()
            local ui_general_trials_mgr = require "ui_general_trials_mgr"
            ui_general_trials_mgr.MainClickUIEnter()
        end,
        conditionShowFunc = function()
            --根据条件显示
            local ui_general_trials_mgr = require "ui_general_trials_mgr"
            return ui_general_trials_mgr.CheckMainIconState()
        end,
        refreshMsg = event.TMSG_GENERALTRIAL_PERSONALDATA_NTF
    },
    [main_slg_const.MainButtonType.ZombieTreasure] = {
        clickFunc = function()
            ui_window_mgr:ShowModulePure("ui_zombie_treasure")
        end,
        conditionShowFunc = function()
            if ReviewingUtil.IsReviewing() then
                return false
            end
            --检测是否在主城            
            local GWMgr = require "gw_mgr"
            if GWMgr.curScene ~= GWConst.ESceneType.Home then
                return false
            end
            local function_open = require "function_open_mgr"
            --功能开放id
            local isOpen = function_open.CheckFunctionIsOpen(1530)
            if not isOpen then
                return false
            end
            --通过小于104关则开启主城界面入口
            local gw_zombie_treasure_data = require "gw_zombie_treasure_data"
            local pass = gw_zombie_treasure_data.CheckMainCityEntrance()
            return pass
        end,
        ----刷新回调函数 flyAnim,setValue,setValueTime,updateUIOrder,setActive
        onRefreshFunc = function(data, flyAnim, setValue, setValueTime, updateUIOrder, setActive)
            --按钮通过事件流程调用的话，只会传入data，其他参数自定义即可
            local item = data and data.item
            if util.IsObjNull(item) then
                return
            end
            if setActive ~= nil then
                --为减少消耗，setActive只纯粹设置显影
                UIUtil.SetChildrenActive(item, setActive)
                return
            end
            ----先检测一下是否有奖励
            local gw_zombie_treasure_data = require "gw_zombie_treasure_data"
            local hasRed = gw_zombie_treasure_data.GetCurRedCount() > 0
            local redEffect = "custom_node/ZombieTreasureEntranceButton/node/effect_node/Effect_UI_ZombiesTreasure_getReward2"
            local boxPlayAnimFunc = function(animName)
                if util.IsObjNull(item) then
                    return
                end
                local effectAnim = UIUtil.GetComponent(item, typeof(Animator),
                        "custom_node/ZombieTreasureEntranceButton/node/effect_node/Effect_UI_ZombiesTreasure_box/Effect")
                if effectAnim then
                    effectAnim:Play(animName, 0, 0)
                end
            end
            if flyAnim then
                local flyAnimPath = "custom_node/ZombieTreasureEntranceButton/node/effect_node/Effect_UI_guankajiemian_sanxin"
                UIUtil.SetActive(item, false, flyAnimPath)
                UIUtil.SetActive(item, true, flyAnimPath)
                if data.flyAnimFlow then
                    data.flyAnimFlow:Dispose()
                    data.flyAnimFlow = nil
                end
                local ani_flow = require "ani_flow"
                data.flyAnimFlow = ani_flow()
                local flow = {
                    {
                        "Step1", -- event name
                        0, -- 开始时间点
                        1, -- 持续时间
                        after_flow = {
                            {
                                "Step2", -- event name
                                0, -- 开始时间点
                                0.25, -- 持续时间
                                after_flow = {
                                    {
                                        "Step3", -- event name
                                        0, -- 开始时间点
                                        0.25, -- 持续时间
                                        after_flow = {
                                            {
                                                "Step4", -- event name
                                                0, -- 开始时间点
                                                0.4, -- 持续时间                                                
                                            },
                                        }
                                    },
                                }
                            },
                        }
                    },
                }
                local register_func = function(event, state)
                    if event ~= "finish_ani" then
                        return
                    end
                    local eventFunc = state[1][1]
                    if eventFunc == "Step1" then
                        boxPlayAnimFunc("UI_ZombiesTreasure_box_ani")
                    elseif eventFunc == "Step2" then
                        boxPlayAnimFunc("UI_ZombiesTreasure_box_ani")
                    elseif eventFunc == "Step3" then
                        boxPlayAnimFunc("UI_ZombiesTreasure_box_ani")
                        gw_zombie_treasure_data.RefreshMainEntranceButton(nil, true)
                    elseif eventFunc == "Step4" then
                        if not util.IsObjNull(item) then
                            UIUtil.SetActive(item, false, flyAnimPath)
                        end
                        if data.flyAnimFlow then
                            data.flyAnimFlow:Dispose()
                            data.flyAnimFlow = nil
                        end
                        if hasRed then
                            boxPlayAnimFunc("UI_ZombiesTreasure_box_ani2")
                        end
                    end
                end
                data.flyAnimFlow:InitFlow(flow, register_func)
                data.flyAnimFlow:Play()
            end
            if hasRed then
                boxPlayAnimFunc("UI_ZombiesTreasure_box_ani2")
            end
            UIUtil.SetActive(item, hasRed or false, redEffect)
            local needSetValue = gw_zombie_treasure_data.GetNeedSetEntranceValue()
            if needSetValue or setValue then
                --这里需要额外设置一下当前的进度
                local sliderImage = UIUtil.GetComponent(item, "Image", "custom_node/ZombieTreasureEntranceButton/node/process/icon_circle")
                local text = UIUtil.GetComponent(item, "Text", "custom_node/ZombieTreasureEntranceButton/node/processText")
                if util.IsObjNull(sliderImage) or util.IsObjNull(text) then
                    return
                end
                local taskList = gw_zombie_treasure_data.GetTaskList()
                local rate = gw_zombie_treasure_data.GetCurRate(taskList)
                local range1, range2, nextId, range0 = gw_zombie_treasure_data.GetCurRateRange(taskList, rate)
                local targetValue = 1
                local div = range2
                local showFenZi = rate - range1
                --超过了最大值
                if rate >= range2 then
                    div = range2
                    targetValue = rate / div
                    showFenZi = rate
                else
                    div = (range2 - range1)
                    if div == 0 then
                        div = range2
                    end
                    targetValue = (rate - range1) / div
                    showFenZi = rate - range1
                end
                --添加规则：1，如果有未领取的奖励，则将进度条设置为满，且进度文本是    (rate- rang1)/rang2-rang1 
                local redCount = gw_zombie_treasure_data.GetCurRedCount()
                --有奖励未领取
                if redCount > 0 then
                    targetValue = 1
                    showFenZi = rate - range0 --分子分母顶格上满
                    div = range1 - range0
                end
                if math.abs(sliderImage.fillAmount - targetValue) > 0.001 then
                    local gw_animation_util = require "gw_animation_util"
                    --未设置，默认3秒走完100%
                    gw_animation_util.DoImageSliderAnim(sliderImage, targetValue, setValueTime or 3, 0, 1,
                            function()
                                if not util.IsObjNull(text) then
                                    UIUtil.SetText(text, string.format("%s/%s", showFenZi, div))
                                end
                            end
                    )
                else
                    UIUtil.SetText(text, string.format("%s/%s", showFenZi, div))
                end
            end
            return true
        end,
        redData = {
            redType = red_const.Enum.ZombieTreasure,
            redPathType = red_const.Type.Default, --红点类型
            pos = { x = 73, y = 46 },
        },
        --待优化成动态加载的
        --ShowEffect = "ui/prefabs/gw/gw_main/mainbuttons/zombietreasureentrancebutton.prefab",
        --ShowEffect = "art/effects/effects/effect_ui_zombiestreasure_box/prefabs/effect_ui_zombiestreasure_box.prefab",
        --ShowEffectParentPath = "custom_node", --特效的父节点路径
        refreshMsg = gw_event_activity_define.GW_ZOMBIE_TREASURE_ENTRANCE_UPDATE,
    },
    [main_slg_const.MainButtonType.gift] = {
        icon = "zjm_icon_thsc",
        getTextFunc = function()
            --获取文本,特惠商城
            return lang.Get(653001)
        end,
        clickFunc = function()
            --打开礼包
            module_jumping.Jump("MenuType.Gift")
        end
    },
    [main_slg_const.MainButtonType.Benefits] = {
        icon = "zjm_icon_czhd",
        getTextFunc = function()
            --获取文本,超值活动
            return lang.Get(653002)
        end,
        conditionShowFunc = function()
            local function_open_mgr = require "function_open_mgr"
            return function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.MallSwitch)
        end,
        clickFunc = function()
            --打开福利
            module_jumping.Jump("MenuType.valueGift")
        end
    },
    [main_slg_const.MainButtonType.daily] = {
        icon = "zjm_icon_tshd",
        getTextFunc = function()
            --获取文本,特殊活动
            return lang.Get(653003)
        end,
        clickFunc = function()
            --打开日常活动
            module_jumping.Jump("ActivityType.dailyGift")

        end
    },
    [main_slg_const.MainButtonType.bag] = {
        icon = "zjm_bag_icon",
        getTextFunc = function()
            --获取文本
            return lang.Get(653004)
        end,
        clickFunc = function()
            --打开背包
            module_jumping.Jump("MenuType.Package")
        end,
        conditionShowFunc = function()
            local function_open_mgr = require "function_open_mgr"
            return function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.Backpack)
        end,
        redData = {
            redType = red_const.Enum.BagMain,
            redPathType = red_const.Type.Num
        }
    },
    [main_slg_const.MainButtonType.mail] = {
        icon = "zjm_mail_icon",
        getTextFunc = function()
            --获取文本
            return lang.Get(653005)
        end,
        clickFunc = function()
            --打开邮件
            module_jumping.Jump("MenuType.Mail")
        end,
        conditionShowFunc = function()
            return function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.Mail)
        end,
        bubbleData = {
            main_slg_const.MainButtonType.mailPresidentBubble,
            main_slg_const.MainButtonType.mailR5Bubble,
            main_slg_const.MainButtonType.mailSystemImportantBubble,
            main_slg_const.MainButtonType.mailStrategyBubble
        },
        redData = {
            redType = red_const.Enum.MailMain,
            redPathType = red_const.Type.Default
        }
    },
    [main_slg_const.MainButtonType.union] = {
        icon = "zjm_menu_union",
        getTextFunc = function()
            --获取文本
            return lang.Get(653008)
        end,
        clickFunc = function()
            local isUnlock = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.Alliance)

            if isUnlock then

                local alliance_mgr_extend = require "alliance_mgr_extend"
                alliance_mgr_extend.ClickMainUnionBtn()

                --local alliance_data = require("alliance_data")
                --local joinTime = alliance_data.GetAllianceFirstJoinTime()
                ----打开联盟
                --local userAllianceData = alliance_data.GetUserAllianceData()
                --if not userAllianceData or userAllianceData.allianceId == nil then
                --    if joinTime and joinTime > 0 then
                --        ShowModule("ui_alliance_list")
                --        event.EventReport("league_enter_RecommendedLeague", { })
                --    else
                --        --玩家首次加入联盟特殊处理
                --        ui_window_mgr:ShowModule("ui_alliance_first_join")
                --    end
                --else
                --    ShowModule("ui_alliance_main")
                --end
            else
                local flow_text = require "flow_text"
                flow_text.Add(lang.Get(600541))
                --flow_text.Add("建造同盟中心后解锁")
            end
        end,
        redData = {
            redType = red_const.Enum.AllianceMain,
            redPathType = red_const.Type.Default
        },
        bubbleData = {
            main_slg_const.MainButtonType.invitationBubble,
            main_slg_const.MainButtonType.unionHelpBubble,
            main_slg_const.MainButtonType.chatAllianceMass,
            main_slg_const.MainButtonType.unionWarBubble,
            main_slg_const.MainButtonType.unionJoinBubble,
            main_slg_const.MainButtonType.citySiegeBubble,
        },
        --是否上锁 ，return false为上锁 true 为不上锁
        isUnLockFun = function()
            return function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.Alliance)
        end,
        isLast = true
    },
    [main_slg_const.MainButtonType.hero] = {
        icon = "zjm_menu_hero",
        getTextFunc = function()
            --获取文本
            return lang.Get(653007)
        end,
        clickFunc = function()
            local unforced_guide_mgr = require "unforced_guide_mgr"
            local unforced_guide_const = require "unforced_guide_const"
            local forceId = unforced_guide_mgr.GetCurGuide()
            if forceId == unforced_guide_const.UnForceGuideType.TenInaRowOne
                    or forceId == unforced_guide_const.UnForceGuideType.TenInaRowTwo
                    or forceId == unforced_guide_const.UnForceGuideType.TenInaRowThree
                    or forceId == unforced_guide_const.UnForceGuideType.TenInaRowFour
                    or forceId == unforced_guide_const.UnForceGuideType.TenInaRowFive
            then
                local unforced_guide_event_define = require "unforced_guide_event_define"
                event.Trigger(unforced_guide_event_define.click_hero_main_view)
            end
            --打开英雄
            local gw_hero_mgr = require "gw_hero_mgr"
            gw_hero_mgr.OpenHeroBagWindow()
        end,
        redData = {
            redType = red_const.Enum.HeroAllMain,
            redPathType = red_const.Type.Default
        }
    },
    [main_slg_const.MainButtonType.miniGame] = {
        getIconSpriteFunc = function(icon)
            local const = require "const"
            if not const.OPEN_MINIGAME then
                return nil
            end
            local function xpcallFun()
                local main_slg_mgr = require "main_slg_mgr"
                local gw_ab_test_mgr = require "gw_ab_test_mgr"
                local type, iconStr = gw_ab_test_mgr.GetMiniGameIconOrType()
                if type == 1 then
                    main_slg_mgr.LoadMainIcon(icon, iconStr)
                else
                    if not util.IsObjNull(icon) and not util.IsObjNull(iconStr) then
                        icon.sprite = iconStr
                        icon:SetNativeSize()
                    end
                end
            end
            local f, res = xpcall(xpcallFun, function(err)
                log.Error("getIconSpriteFunc ", err)
            end)
        end,
        getTextFunc = function()
            local gw_ab_test_mgr = require "gw_ab_test_mgr"
            local nameLangId = gw_ab_test_mgr.GetMiniGameName()
            --获取文本
            local miniGameNameStr = lang.Get(nameLangId)
            return miniGameNameStr
        end,
        clickFunc = function()
            ui_window_mgr:UnloadModule("ui_bs_operate")
            local unforced_guide_mgr = require "unforced_guide_mgr"
            if unforced_guide_mgr.GetCurGuide() == 9 and unforced_guide_mgr.GetCurStep() then
                local unforced_guide_event_define = require "unforced_guide_event_define"
                event.Trigger(unforced_guide_event_define.click_miniGame_main_view)
            end
            ui_window_mgr:ShowModule("ui_common_mini_game_level")
            local isOpen = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.MainMiniGame, true)
            if not isOpen then
                local gw_jump_util = require "gw_jump_util"
                gw_jump_util.JumpToBuild(tostring(GWConst.enBuildingType.enBuildingType_Garden))
                return
            end
            ----打开小游戏
            --local puzzlegame_mgr = require "puzzlegame_mgr"
            --if puzzlegame_mgr.getIsOpenMiniGame() then
            --    local unforced_guide_mgr = require "unforced_guide_mgr"
            --    if unforced_guide_mgr.GetCurGuide() == 9 and unforced_guide_mgr.GetCurStep() then
            --        local unforced_guide_event_define = require "unforced_guide_event_define"
            --        event.Trigger(unforced_guide_event_define.click_miniGame_main_view)
            --    end
            --
            --    local curGuide = unforced_guide_mgr.GetCurGuide()
            --    if curGuide then
            --        local cfg = unforced_guide_mgr.GetCurGuideConfig()
            --        if cfg and cfg.guideType == 1 and cfg.lobbyPage == 2 then
            --            local force_guide_system = require "force_guide_system"
            --            if ui_window_mgr:IsModuleShown("ui_pointing_target") then
            --                if force_guide_system.GetState() ~= 2 then
            --                    ui_window_mgr:UnloadModuleImmediate("ui_pointing_target")
            --                    ui_window_mgr:UnloadModuleImmediate("ui_guide_assistant_new")
            --                end
            --                unforced_guide_mgr.SetSceneGuide(false)
            --            end
            --        end
            --    end
            --    local force_guide_data = require "force_guide_data"
            --    local topLevel, levelIndex = puzzlegame_mgr.GetOpenTopLevel()
            --    local quickConfig = game_scheme:MiniGameQuickChallenge_1(levelIndex, force_guide_data.GetGuideGroupId())
            --    if quickConfig and quickConfig.QuickChallenge and quickConfig.QuickChallenge > 0 then
            --        puzzlegame_mgr.OpenMiniGame(topLevel)
            --    else
            --        --    menu_bot_data.OpenMiniGamePage()
            --        ShowModule("ui_puzzle_game_level")
            --    end
            --
            --    --self:SetBtnTxtVisible(false)
            --
            --    --force_guide_system.TriComEvent(force_guide_event.cEventClickPuzzleGame)
            --
            --    local unforced_guide_mgr = require "unforced_guide_mgr"
            --    if unforced_guide_mgr.GetCurGuide() == 45 and unforced_guide_mgr.GetCurStep() then
            --        local cfg = unforced_guide_mgr.GetCurStepConfig()
            --        if cfg and cfg.stepId == 300 then
            --            event.Trigger(event.CLICK_MINIGAME)
            --        end
            --    end
            --    -- print("unforced_guide_mgr.GetCurGuide():",unforced_guide_mgr.GetCurGuide())
            --    if unforced_guide_mgr.GetCurGuide() == 65 or unforced_guide_mgr.GetCurGuide() == 66 or unforced_guide_mgr.GetCurGuide() == 67
            --            or unforced_guide_mgr.GetCurGuide() == 68 or unforced_guide_mgr.GetCurGuide() == 69 or unforced_guide_mgr.GetCurGuide() == 70
            --            or unforced_guide_mgr.GetCurGuide() == 64 and unforced_guide_mgr.GetCurStep() then
            --        local cfg = unforced_guide_mgr.GetCurStepConfig()
            --        -- print("cfg.stepId:",cfg.stepId)
            --        if cfg and cfg.stepId == 490 then
            --            event.Trigger(event.CLICK_MINIGAME)
            --        end
            --    end
            --
            --else
            --    --log.Warning("小游戏功能关闭")
            --end
        end,
        --是否上锁 ，return false为上锁 true 为不上锁
        isUnLockFun = function()
            local isOpen = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.MainMiniGame)
            --local files_version_mgr = require "files_version_mgr"
            --local buildType = files_version_mgr.GetSelectedGameForBuildType(function_open_mgr.OpenIdEnum.MainMiniGame)
            local buildType = 1
            if not isOpen and buildType == 1 then
                return false
            end
            return true
        end,
        conditionShowFunc = function()
            local const = require "const"
            --并且 需要判断 功能是否解锁
            return const.OPEN_MINIGAME
        end,
        redData = {
            redType = red_const.Enum.MiniGame_Main_Icon,
            redPathType = red_const.Type.New
        },
        refreshMsg = sand_ui_event_define.MINIGAME_TYPE_CHANGE,
        BgImageShow = true,
        isLast = true,
        LevelBg = true,
    },
    [main_slg_const.MainButtonType.trial] = {
        icon = "zjm_menu_xWa",
        objName = "trial_btn",
        getRedPathType = function()
            local arena_data = require "arena_data"
            local showNew = arena_data.GetArenaNewRed()
            return showNew and red_const.Type.New or red_const.Type.Default
        end,
        getTextFunc = function()
            --获取文本
            return lang.Get(653006)
        end,
        clickFunc = function()
            local isOpen = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.MainTrial, true)
            if not isOpen then
                return
            end
            --打开试炼
            ShowModule("ui_experiment")
        end,
        isUnLockFun = function()
            local isOpen = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.MainTrial)
            if not isOpen then
                return false
            end
            return true
        end,
        redData = {
            redType = red_const.Enum.CampTrial_Main_Icon,
            redPathType = red_const.Type.Default
        },
        bubbleData = {
            main_slg_const.MainButtonType.arenaChallengeBubble,
        },
    },
    [main_slg_const.MainButtonType.unionHelpBubble] = {
        icon = "zjm_qipao_bangzhu",

        clickFunc = function()
            --打开联盟帮助
            local net_alliance_module = require "net_alliance_module"
            net_alliance_module.MSG_ALLIANCE_HELP_CLICK_REQ()
        end,
        conditionShowFunc = function()
            local AllianceMgr = require "alliance_mgr"
            return AllianceMgr.IsShowHelpBubble()
        end,
        refreshMsg = event_alliance_define.UPDATE_ALLIANCE_HELP_BUBBLE
    },
    [main_slg_const.MainButtonType.chatAllianceMass] = {
        bubbleType = main_slg_const.MainButtonType.chatAllianceMass,
        icon = "lianMeng_qipao_cq",
        clickFunc = function()
            local alliance_mgr_extend = require "alliance_mgr_extend"
            alliance_mgr_extend.SetCancelClickMassBubble()
            --打开集结界面
            ui_window_mgr:ShowModule("ui_alliance_mass")
        end,
        isAnimation = true, --是否播放动画
        conditionShowFunc = function()
            local alliance_mgr_extend = require "alliance_mgr_extend"
            local isShow = alliance_mgr_extend.JudgeMainMassBubble()
            return isShow
        end,
        refreshMsg = event_alliance_define.MAIN_MASS_BUBBLE_REFRESH,
    },

    [main_slg_const.MainButtonType.unionWarBubble] = {
        icon = "zjm_qipao_zhanzheng",
        clickFunc = function()
            --打开联盟战争,并切换到单人情报
            local AllianceMgr = require "alliance_mgr"
            AllianceMgr.OpenAllianceWarUI(true)
        end,
        conditionShowFunc = function()
            local alliance_gen_data = require "alliance_gen_data"
            local isShow = alliance_gen_data.JudgeIsShowPvpIcon()
            return isShow
        end,
        refreshMsg = event_alliance_define.ALLIANCE_INTELLIGENCE_UPDATE
    },
    [main_slg_const.MainButtonType.unionJoinBubble] = {
        icon = "zjm_qipao_jiarulm",
        clickFunc = function()
            local alliance_mgr_extend = require "alliance_mgr_extend"
            alliance_mgr_extend.ClickMainUnionBtn()
            --local log = require "log"
            --log.Error("点击加入联盟气泡")
        end,
        isAnimation = true, --是否播放动画
        conditionShowFunc = function()
            local isOpen = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.Alliance)
            if not isOpen then
                return false
            end
            local alliance_mgr = require "alliance_mgr"
            local allianceID = alliance_mgr.GetUserAllianceId()
            if allianceID > 0 then
                return false
            end
            return true
        end,
        refreshMsg = event_alliance_define.UPDATE_JOIN_ALLIANCE_BUBBLE
    },
    [main_slg_const.MainButtonType.citySiegeBubble] = {
        icon = "csjs_img_zybubble",
        clickFunc = function()
            local city_siege_activity_data = require "city_siege_activity_data"
            local temp = city_siege_activity_data.GetNextOpenCity()
            ui_window_mgr:ShowModule("ui_city_siege_popup", nil, nil, {
                openTime = temp.openTime
            })
            local player_prefs = require "player_prefs"
            player_prefs.SetCacheData("citySiegeBubbleShowTime", os.server_time()) --缓存当前的打开时间
            event.Trigger(gw_event_activity_define.GW_UPDATE_CITY_SIEGE_BUBBLE_STATE)
        end,
        isAnimation = true, --是否播放动画
        conditionShowFunc = function()
            local player_prefs = require "player_prefs"
            local showTime = player_prefs.GetCacheData("citySiegeBubbleShowTime", 0) --拿自己缓存的开启时间
            if showTime ~= 0 then
                local cutTime = os.server_time()
                local date1 = os.date("*t", cutTime)
                local date2 = os.date("*t", showTime)
                --如果是同一天，返回false
                if date1.year == date2.year and date1.month == date2.month and date1.day == date2.day then
                    return false
                end
            end
            local festival_activity_mgr = require "festival_activity_mgr"
            local isUnlocked = festival_activity_mgr.GetIsOpenByHeadingCode(festival_activity_cfg.ActivityCodeType.CitySiege, true)
            if not isUnlocked then
                return false
            end
            local city_siege_activity_data = require "city_siege_activity_data"
            local temp = city_siege_activity_data.GetNextOpenCity()
            if not temp then
                return false
            end
            return true
        end,
        refreshMsg = gw_event_activity_define.GW_UPDATE_CITY_SIEGE_BUBBLE_STATE
    },
    [main_slg_const.MainButtonType.invitationBubble] = {
        icon = "zjm_qipao_yaoQingHan",
        clickFunc = function()
            local net_alliance_module = require "net_alliance_module"
            net_alliance_module.MSG_ALLIANCE_INVITATION_REQ()
            ui_window_mgr:ShowModule("ui_alliance_invitation")
        end,
        isAnimation = true, --是否播放动画
        conditionShowFunc = function()
            local alliance_mgr = require "alliance_mgr"
            return alliance_mgr.CheckIsShowInvitationBubble()
        end,
        refreshMsg = event_alliance_define.REFRESH_ALLIANCE_INVITATE_BUBBLE
    },
    [main_slg_const.MainButtonType.mailPresidentBubble] = {--总统邮件气泡
        icon = "youxiang_icon_hg",

        clickFunc = function()
            local mail_gw_mgr = require "mail_gw_mgr"

            mail_gw_mgr.Jump2MailDetail(mail_define_gw.E_SubMailType.President)
        end,
        conditionShowFunc = function()
            return mail_data_gw.IsHaveUnreadTipBySubType(mail_define_gw.E_SubMailType.President)
        end,
        -- 指定刷新事件
        refreshMsg = event.RefreshMainMailBubble_President,
    },
    [main_slg_const.MainButtonType.mailR5Bubble] = {--盟主邮件气泡
        icon = "youxiang_img_rj",

        clickFunc = function()
            local mail_gw_mgr = require "mail_gw_mgr"

            mail_gw_mgr.Jump2MailDetail(mail_define_gw.E_SubMailType.AllianceAuthority)
        end,
        conditionShowFunc = function()
            return mail_data_gw.IsHaveUnreadTipBySubType(mail_define_gw.E_SubMailType.AllianceAuthority)
        end,
        -- 指定刷新事件
        refreshMsg = event.RefreshMainMailBubble_R5,
    },
    [main_slg_const.MainButtonType.mailSystemImportantBubble] = {--系统重要邮件气泡
        icon = "youxiang_img_rwxl",

        clickFunc = function()
            local mail_gw_mgr = require "mail_gw_mgr"
            mail_gw_mgr.Jump2MailDetail(mail_define_gw.E_SubMailType.SystemImportant)
        end,
        conditionShowFunc = function()

            return mail_data_gw.IsHaveUnreadTipBySubType(mail_define_gw.E_SubMailType.SystemImportant)
        end,
        -- 指定刷新事件
        refreshMsg = event.RefreshMainMailBubble_SystemImportant,
    },
    [main_slg_const.MainButtonType.mailStrategyBubble] = {--攻略邮件气泡
        icon = "youxiang_img_hd1",

        clickFunc = function()
            local mail_gw_mgr = require "mail_gw_mgr"
            mail_gw_mgr.Jump2MailDetail(mail_define_gw.E_SubMailType.Strategy)
        end,
        conditionShowFunc = function()

            return mail_data_gw.IsHaveUnreadTipBySubType(mail_define_gw.E_SubMailType.Strategy)
        end,
        -- 指定刷新事件
        refreshMsg = event.RefreshMainMailBubble_Strategy,
    },
    [main_slg_const.MainButtonType.arenaChallengeBubble] = {--竞技场提示气泡
        icon = "zjm_qipao_jingJiChang",
        clickFunc = function()
            local arena_data = require "arena_data"
            arena_data.JumpArenaByBubble()
        end,
        conditionShowFunc = function()
            local arena_data = require "arena_data"
            return arena_data.GetArenaBubbleIsShow() and true or false
        end,
        -- 指定刷新事件
        refreshMsg = event.REFRESH_ARENA_BUBBLE_SHOW,
    },
    --狗屋奖励按钮
    [main_slg_const.MainButtonType.dogHouse] = {
        icon = "zjm_z_cz_xunbao",
        clickFunc = function()
            ui_window_mgr:ShowModule("ui_straydog_house")
        end,
        conditionShowFunc = function()
            --条件额外加一个关卡判断 
            local laymain_data = require "laymain_data"
            local level = laymain_data.GetPassLevel()
            --备注 45是关卡45，
            if level and level >= 45 then
                return false
            end
            --幸存者按钮隐藏，并且有奖励时显示按钮
            local gw_straydog_mgr = require "gw_straydog_mgr"
            return not buttonConfig[main_slg_const.MainButtonType.survivor].conditionShowFunc() and
                    net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_StrayDog) and
                    gw_straydog_mgr.HasAward()
        end,
        redData = {
            redType = red_const.Enum.DogHouseReward,
            redPathType = red_const.Type.Num
        },
        refreshMsg = event.STRAY_DOG_INFO_UPDATE
    },
    -- 第三列的资源下载、问卷改到自定义活动入口customActivityButtonConfig去了
    -- [main_slg_const.MainButtonType.resourceDownload] = { --资源下载按钮
    --     icon = "zjm_icon_xiazai",
    --     getTextFunc = function()
    --         --获取文本
    --         return lang.Get(602087) --资源
    --     end,
    --     getTextTimerFunc = function(textUI) --这里会开启一个定时器，每秒执行一次 (下载的特殊处理，要每秒检测)
    --         local gray_load_mgr = require "gray_load_mgr"
    --         local loadSize, total, start_or_pause = gray_load_mgr.GetLoadParam()
    --         local bGrayLoad = total > 0 and loadSize < total

    --         if not util.IsObjNull(textUI) then
    --             textUI.text = lang.Get(602087)
    --         end
    --         local rate = math.floor(loadSize * 100 / total)
    --         if not util.IsObjNull(textUI) then
    --             local str = lang.Get(602087)
    --             if  tostring(rate):lower() == "nan" then
    --                 str = lang.Get(602087)
    --             else
    --                 str = rate .. "%"
    --             end
    --             textUI.text = str
    --         end
    --         if not bGrayLoad then
    --             ui_window_mgr:UnloadModule("ui_grayload")
    --         end
    --         event.Trigger(event.RefreshMain_Download)
    --     end,
    --     clickFunc = function()  --点击事件
    --         local gray_load_mgr = require "gray_load_mgr"
    --         local param = gray_load_mgr.GetGrayLoadParam()
    --         local grayBtnCallBack = param.grayBtnCallBack

    --         if grayBtnCallBack then
    --             grayBtnCallBack()
    --         else
    --             ui_window_mgr:ShowModule("ui_grayload")
    --         end
    --     end,
    --     conditionShowFunc = function()
    --         if true then
    --             return true
    --         end
    --         if ReviewingUtil.IsReviewing() then
    --             return false
    --         end
    --         local gw_home_building_data = require "gw_home_building_data"
    --         local level = gw_home_building_data.GetBuildingMainLevel()
    --         --return true
    --         local initCfg = game_scheme:InitBattleProp_0(8269)
    --         if initCfg then
    --             local limitLv = initCfg.szParam.data[0]
    --             --大本等级超过一定等级才会显示该按钮
    --             if level < limitLv then
    --                 return false
    --             end
    --         end

    --         local main_slg_mgr = require "main_slg_mgr"
    --         --封装的显示判断
    --         local isShow =  main_slg_mgr.GetDownLoadBtnIsShow()
    --         return isShow --默认显示
    --         --return true
    --     end,
    --     refreshMsg = event.RefreshMain_Download, --刷新事件
    --     redData = {
    --         redType = red_const.Enum.DownLoadRed,
    --         redPathType = red_const.Type.Default
    --     },
    -- },
    -- [main_slg_const.MainButtonType.questionnaire] = { --调查问卷按钮
    --     icon = "zjm_icon_dcwj",
    --     getTextFunc = function()
    --         --获取文本
    --         return lang.Get(1003201) --调查问卷
    --     end,
    --     clickFunc = function() --点击事件
    --         --log.Error("点击了调查问卷")
    --         local net_questionnaire_module = require "net_questionnaire_module"
    --         net_questionnaire_module.RequestQuestionnaire()
    --         ui_window_mgr:ShowModule("ui_questionnaire_investigation")
    --     end,
    --     conditionShowFunc = function()
    --         if true then
    --             return true
    --         end
    --         if ReviewingUtil.IsReviewing() then
    --             return false
    --         end
    --         --local gw_home_building_data = require "gw_home_building_data"
    --         --local level = gw_home_building_data.GetBuildingMainLevel()
    --         --
    --         --local cfg = game_scheme:InitBattleProp_0(8270)--新手问卷入口条件
    --         --local minTargetLevel = 4
    --         --local maxTargetLevel = 10
    --         --if cfg then
    --         --    minTargetLevel = cfg.szParam.data[0]
    --         --    maxTargetLevel = cfg.szParam.data[1]
    --         --end
    --         ----大本等级不足不显示
    --         --if level < minTargetLevel or level > maxTargetLevel then
    --         --    return false
    --         --end

    --         --判断是否显示调查问卷按钮
    --         --return true显示，false 不显示
    --         local questionnaire_investigation_mgr = require "questionnaire_investigation_mgr"
    --         local data =  questionnaire_investigation_mgr.CheckAllQuestionnaireData()
    --         if data then
    --             return true
    --         end
    --         return false
    --     end,
    --     refreshMsg = event.RefreshMain_Questionnaire, --当Trigger这个事件时会在执行一次上面的刷新方法
    --     redData = {
    --         redType = red_const.Enum.Questionnaire,
    --         redPathType = red_const.Type.Default
    --     },
    -- },
    -- [main_slg_const.MainButtonType.changePackage] = { --换包按钮
    --     icon = "zjm_icon_huanbao",
    --     functionBarID = festival_activity_cfg.ActivityEntranceType.ChangePackage,
    --     getTextFunc = function()
    --         --获取文本
    --         return lang.Get(1007201) --换包好礼
    --     end,
    --     clickFunc = function() --点击事件
    --         ui_window_mgr:ShowModule("ui_change_package")
    --     end,
    --     conditionShowFunc = function()
    --         if ReviewingUtil.IsReviewing() then
    --             return false
    --         end
    --         local change_package_mgr = require "change_package_mgr"
    --         return change_package_mgr.CheckIsShowEnter()
    --     end,
    --     refreshMsg = event.RefreshMain_ChangePackage, --当Trigger这个事件时会在执行一次上面的刷新方法
    --     redData = {
    --         redType = red_const.Enum.ChangePackageRed,
    --         redPathType = red_const.Type.Default
    --     },
    -- },
}
--buttonConfig配置的补充定义，目的是新增一个按钮但是用的配置上已有的按钮的配置
buttonConfig[main_slg_const.MainButtonType.survivor2] = {
    icon = buttonConfig[main_slg_const.MainButtonType.survivor].icon,
    clickFunc = buttonConfig[main_slg_const.MainButtonType.survivor].clickFunc,
    conditionShowFunc = function()
        --条件额外加一个关卡判断 
        local laymain_data = require "laymain_data"
        local level = laymain_data.GetPassLevel()
        --备注 45是关卡45，
        if level and level < 45 then
            return false
        end
        local gw_home_survivor_data = require "gw_home_survivor_data"
        return #gw_home_survivor_data.GetOutsideSurvivorList(GWConst.enCityRewardType.enRewardType_Survivor) > 0
    end,
    redData = buttonConfig[main_slg_const.MainButtonType.survivor].redData,
    refreshMsg = buttonConfig[main_slg_const.MainButtonType.survivor].refreshMsg
}
buttonConfig[main_slg_const.MainButtonType.dogHouse2] = {
    icon = buttonConfig[main_slg_const.MainButtonType.dogHouse].icon,
    clickFunc = buttonConfig[main_slg_const.MainButtonType.dogHouse].clickFunc,
    conditionShowFunc = function()
        --条件额外加一个关卡判断 
        local laymain_data = require "laymain_data"
        local level = laymain_data.GetPassLevel()
        --备注 45是关卡45，
        if level and level < 45 then
            return false
        end
        --幸存者按钮隐藏，并且有奖励时显示按钮
        local gw_straydog_mgr = require "gw_straydog_mgr"
        return not buttonConfig[main_slg_const.MainButtonType.survivor2].conditionShowFunc() and
                net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_StrayDog) and
                gw_straydog_mgr.HasAward()
    end,
    redData = buttonConfig[main_slg_const.MainButtonType.dogHouse].redData,
    refreshMsg = buttonConfig[main_slg_const.MainButtonType.dogHouse].refreshMsg
}

activityBubbleCheckFunc = {
    [1] = {
        checkFunc = function()
            local festival_activity_mgr = require "festival_activity_mgr"
            local isUnlocked = festival_activity_mgr.GetIsOpenByHeadingCode(festival_activity_cfg.ActivityCodeType.Doomsday, true)
            if isUnlocked then
                local bubbleCfg = game_scheme:Bubble_0(1)
                return lang.Get(bubbleCfg.Parameter1)
            end
            return nil
        end
    },
    [2] = {
        checkFunc = function()
            local gw_storm_mgr = require "gw_storm_mgr"
            local isOpen = gw_storm_mgr.CheckStormOpen()
            if not isOpen then
                return nil
            end
            local activityData = gw_storm_mgr.GetStormDataActivity()
            local activityState = activityData.GetActivityState()
            local bubbleCfg = game_scheme:Bubble_0(2)
            if activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_Battle then
                local mySignUpInfo = activityData.OnGetMySignInfo()
                if mySignUpInfo.enTeam and mySignUpInfo.enTeam ~= event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_None then
                    local signUpInfo = activityData.OnGetSignUpInfo()
                    local timeList = activityData.OnGetBattleTimeList()[signUpInfo.teamData[event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A].battleTimeIndex]
                    if timeList then
                        local enterTime = timeList.enterTime - os.server_time()
                        if enterTime < 0 then
                            local startTime = timeList.battleTime - os.server_time()
                            if startTime > 0 then
                                return lang.Get(bubbleCfg.Parameter2)
                            else
                                local endTime = timeList.endTime - os.server_time()
                                if endTime > 0 then
                                    return lang.Get(bubbleCfg.Parameter2)
                                end
                            end
                        end
                    end
                end
            elseif activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_SignUp then
                return lang.Get(bubbleCfg.Parameter1)
            end
            return nil
        end
    },

    [3] = {
        checkFunc = function()
            --local alliance_data = require("alliance_data")
            --local userAllianceData = alliance_data.GetUserAllianceData()
            --if not userAllianceData or userAllianceData.allianceId == nil then
            --    return nil
            --end
            local festival_activity_mgr = require "festival_activity_mgr"
            local isUnlocked = festival_activity_mgr.GetIsOpenByHeadingCode(festival_activity_cfg.ActivityCodeType.CitySiege, true)
            if isUnlocked then
                local city_siege_activity_data = require "city_siege_activity_data"
                local temp = city_siege_activity_data.GetNextOpenCity()
                if temp then
                    local bubbleCfg = game_scheme:Bubble_0(3)
                    return string.format2(lang.Get(bubbleCfg.Parameter3), temp.level)
                end
            end
            return nil
        end
    },
}

---自定义活动入口功能 redData不填则使用red_const.Enum.ActivityEntrance..(key or 0)
customActivityButtonConfig = {
    ["ActivityCommon"] = {
        --icon = nil, 未配置统一处理，有值就使用配置的值
        clickFunc = function(configData)
            local ui_festival_activity_mgr = require("festival_activity_mgr")
            if configData.isEntrance then
                ui_festival_activity_mgr.OpenActivityUIByEntranceID(configData.buttonType)
            else
                --这里是快速入口
                ui_festival_activity_mgr.OpenActivityUIByQuickEntranceID(configData.buttonType - ActivityQuickEntranceOffset)
            end
        end,
        --getTextFunc = nil, 未配置统一处理，有值就使用配置的值
    },
    ["ActivityCommonQuickEntrance"] = {
        --icon = nil, 未配置统一处理，有值就使用配置的值
        clickFunc = function(configData)
            local ui_festival_activity_mgr = require("festival_activity_mgr")
            if configData.isEntrance then
                ui_festival_activity_mgr.OpenActivityUIByEntranceID(configData.buttonType)
            else
                --这里是快速入口
                ui_festival_activity_mgr.OpenActivityUIByQuickEntranceID(configData.buttonType - ActivityQuickEntranceOffset)
            end
        end,
        --getTextFunc = nil, 未配置统一处理，有值就使用配置的值
    },
    [festival_activity_cfg.ActivityEntranceType.WarZonePK] = {
        clickFunc = function(configData)
            local war_zone_duel_data = require "war_zone_duel_data"
            local war_zone_duel_define = require "war_zone_duel_define"
            local isCongStart = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.CongressBattleStart)
            local war_zone_duel_const = require "war_zone_duel_const"
            local panelType = war_zone_duel_const.WarZoneDuelPanelType.None
            if isCongStart then
                panelType = war_zone_duel_const.WarZoneDuelPanelType.Race
            end
            
            local war_zone_duel_mgr = require "war_zone_duel_mgr"
            war_zone_duel_mgr.SetWarZoneAttackMainSlgBtnRed()
            
            ui_window_mgr:ShowModule("ui_war_zone_duel_main", nil, nil, panelType)
        end,
        conditionShowFunc = function()
            local war_zone_duel_define = require "war_zone_duel_define"
            local war_zone_duel_data = require "war_zone_duel_data"
            
            local isEnd = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.AtyEnd)
            if isEnd then
                return false
            end
            local isOpen = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.None)
            if not isOpen then
                return true
            end
            return false
        end,
        countdownTimeCfg = {
            timeStamp = function()
                local war_zone_duel_data = require "war_zone_duel_data"
                
                local noticeTime, startTime = war_zone_duel_data.GetServerPreviewTime()
                local osTime = os.server_time()
                if noticeTime > 0 and startTime > 0 and osTime > noticeTime and osTime < startTime  then
                    return startTime
                end
                return 0
            end,
        },
    },
    [festival_activity_cfg.ActivityEntranceType.AlliancePK] = {
        --icon = nil,未配置统一处理，有值就使用配置的值
        clickFunc = function(configData)
            --local alliance_duel_data = require "alliance_duel_data"
            --local themeId = alliance_duel_data.GetThemeDuelID()
            --local showMatch = alliance_duel_data.GetDuelRoundID() or 0
            --if themeId ~= 0 or showMatch ~= 0 then
                ui_window_mgr:ShowModule("ui_alliance_duel_main")
            --else
            --    local alliance_data = require("alliance_data")
            --    local flow_text = require "flow_text"
            --    local userAllianceData = alliance_data.GetUserAllianceData()
            --    local hasAllianceData = true
            --    if not userAllianceData or userAllianceData.allianceId == nil or userAllianceData.allianceId == 0 then
            --        hasAllianceData = false
            --    end
            --    if not hasAllianceData then
            --        flow_text.Add(lang.Get(16494))
            --    else
            --        flow_text.Add(lang.Get(134103))
            --    end
            --
            --end

        end,
        countdownTimeCfg = {
            --buttonType = nil,--入口类型，用来做定时器的key  默认规则是只要你不自己定义，就会统一处理，你自己定义了就会取自己的
            timeStamp = function()
                local activity_preview_mgr = require "activity_preview_mgr"
                local activity_preview_cfg = require "activity_preview_cfg"
                local isPreview = activity_preview_mgr.IsJudgePreview(activity_preview_cfg.ActivityPreviewID.AllianceDuel)
                if not isPreview then
                    local alliance_duel_data = require "alliance_duel_data"
                    local endTime = alliance_duel_data.GetThemeEndTime()
                    return endTime
                else
                    local previewData = activity_preview_mgr.GetPreviewActivityDataByActivityID(activity_preview_cfg.ActivityPreviewID.AllianceDuel)
                    if previewData then
                        return previewData.endTime
                    end
                end
                return 0
            end, --结束时间戳 
        },
        conditionShowFunc = function()
            local alliance_duel_data = require "alliance_duel_data"
            local themeId = alliance_duel_data.GetThemeDuelID()
            local showMatch = alliance_duel_data.GetDuelRoundID() or 0
            return themeId ~= 0 or showMatch ~= 0
        end
    },
    [festival_activity_cfg.ActivityEntranceType.LoginGift] = {
        --icon = nil,未配置统一处理，有值就使用配置的值
        clickFunc = function(configData)
            ui_window_mgr:ShowModule("ui_login_gift_main")
        end,
        countdownTimeCfg = {
            timeStamp = function()
                local login_gift_data = require "login_gift_data"
                local endTime = login_gift_data.GetActivityEndTime()
                return endTime
            end, --结束时间戳 
        },
        showLockState = function()
            local login_gift_data = require "login_gift_data"
            local isShow = login_gift_data.IsShowLockIcon()
            return isShow
        end, --是否显示锁图标
        curIconState = {
            State = function()
                local login_gift_data = require "login_gift_data"
                local isCanGet = login_gift_data.IsCanGetPrice()
                return isCanGet
            end,
            StateName = function()
                local login_gift_data = require "login_gift_data"
                local isCanGet_Txt = login_gift_data.IsCanGetPriceTxt()
                return isCanGet_Txt
            end,
            RewardNum = function()
                local login_gift_data = require "login_gift_data"
                local RewardNum = login_gift_data.GetCurRewardNum()
                return RewardNum
            end
        },
        conditionShowFunc = function()
            local customCondition = festival_activity_cfg.CustomActivityUnlockCondition[festival_activity_cfg.ActivityCodeType.LoginGift]
            local login_gift_define = require "login_gift_define"
            local sucess = customCondition(login_gift_define.ActivityID)
            return sucess
        end,
    },
    [festival_activity_cfg.ActivityEntranceType.FirstRecharge] = {
        clickFunc = function(configData)
            --获取活动id,通过activityType获取当前处于激活状态的活动
            local festival_activity_mgr = require "festival_activity_mgr"
            local activity = festival_activity_mgr.GetActivityDataByActivityCodeType(festival_activity_cfg.ActivityCodeType.FirstRecharge)
            if activity and activity.activityID then
                --这里要用到活动id，所以要先判断是否能解锁
                ui_window_mgr:ShowModule("ui_first_recharge", nil, nil, { activityID = activity.activityID })
            end
        end,
        --此处不需要了，直接先判断活动是不是能解锁了，不满足直接都不生成了 在festival_activity_cfg.CustomActivityUnlockCondition里面去判断
        --conditionShowFunc = function()
        --    local gw_firstrecharge_mgr = require "gw_firstrecharge_mgr"
        --    local isOpen = gw_firstrecharge_mgr.CheckIsOpen(festival_activity_cfg.ActivityEntranceType.FirstRecharge)
        --    return isOpen
        --end,
    },
    [festival_activity_cfg.ActivityEntranceType.LimitGift] = {
        clickFunc = function(configData)
            local limit_gift_data = require "limit_gift_data"
            local curIndex = limit_gift_data.GetCurShowIndex()
            ui_window_mgr:ShowModule("ui_limit_gift_main", nil, nil, { curIndex = curIndex })
        end,
        showIcon = function()
            local limit_gift_data = require "limit_gift_data"
            local giftList = limit_gift_data.GetShowInfo()
            return giftList
        end,
        -- changIconCD = function()
        --     local limit_gift_data = require "limit_gift_data"
        --     local changeCD = limit_gift_data.GetChangeIconCD()
        --     return changeCD
        -- end,
        -- hasAnimate = function()
        --     local limit_gift_data = require "limit_gift_data"
        --     local HasCom = limit_gift_data.GetHasAnimate()
        --     return HasCom
        -- end,
        countdownTimeCfg = {
            timeStamp = function() 
                local limit_gift_data = require "limit_gift_data"
                local IconData = limit_gift_data.GetShowInfo()
                if IconData then 
                    return IconData.MinTime
                else
                    return 0
                end
            end,

            --优化限时礼包入口切换逻辑
            changIconFunc = function(item,spriteAsset) 
                local limit_gift_data = require "limit_gift_data"
                local IconData = limit_gift_data.GetShowInfo()
                local changeCD = limit_gift_data.GetChangeIconCD()
                local curTime = os.server_time()
                local timerIndex = limit_gift_data.GetCurShowIndex()
                if IconData and math.floor(curTime) % changeCD == 0 then 
                    --延时刷新 Icon
                    util.DelayCallOnce(0.2,function() 
                        local IconList = IconData.IconList
                        if IconList and #IconList > 0 then
                            timerIndex = (timerIndex % #IconList) + 1
                        end

                        if IconList and IconList[timerIndex] and not string.IsNullOrEmpty(IconList[timerIndex]) then
                            local loopIcon = item:Get("icon")
                            --print("Info[timerIndex]",timerIndex,Info[timerIndex].icon)
                            spriteAsset:GetSprite(IconList[timerIndex],function(sp) 
                                if not util.IsObjNull(loopIcon) then
                                    loopIcon.sprite = sp
                                end
                            end)
                            event.Trigger(event.UPDATE_SHOW_INDEX, timerIndex)
                        end
                        -- print("data.loopIcon",#Info,timerIndex)
                    end)
                    -- 先播放渐隐动画再切换Icon 
                    local canShowAni = limit_gift_data.GetHasAnimate()
                    if canShowAni and not util.IsObjNull(item) then
                         -- 开启显示渐隐动画
                        local Ani = item:Get("Ani")
                        Ani.enabled = true
                        Ani:SetTrigger("ShowOff")
                    end
                end
            end
        },
    },
    [festival_activity_cfg.ActivityEntranceType.TotalRecharge] = {
        clickFunc = function(configData)
            local ui_festival_activity_mgr = require("festival_activity_mgr")
            if configData.isEntrance then
                ui_festival_activity_mgr.OpenActivityUIByEntranceID(configData.buttonType)
            else
                --这里是快速入口
                ui_festival_activity_mgr.OpenActivityUIByQuickEntranceID(configData.buttonType - ActivityQuickEntranceOffset)
            end
        end,
        curEnterIcon = function()
            local festival_activity_mgr = require "festival_activity_mgr"
            local activityIDList = festival_activity_mgr.GetAllActivityInEntrance(festival_activity_cfg.ActivityEntranceType.TotalRecharge, true, true)
            if activityIDList then
                local initCfg = game_scheme:InitBattleProp_0(8245)
                local initIconCfg = game_scheme:InitBattleProp_0(8244)
                local cfgStr = game_scheme:GWMapConstant_0(83).szParamString
                if initCfg and initIconCfg then
                    local initIconList = string.split(initIconCfg.szParamString, "#")
                    if initIconList then
                        for k, v in ipairs(activityIDList) do
                            if initCfg.szParam.data[0] == v then
                                return initIconList[1]
                            end
                        end
                        for k, v in ipairs(activityIDList) do
                            for i = 1, initCfg.szParam.count - 1 do
                                local id = initCfg.szParam.data[i]
                                if id and id == v then
                                    return initIconList[i + 1]
                                end
                            end
                        end
                    end
                end
            end
        end
    },
    [festival_activity_cfg.ActivityEntranceType.SevenDayLogin] = {
        clickFunc = function(configData)
            --获取活动id,通过activityType获取当前处于激活状态的活动
            local festival_activity_mgr = require "festival_activity_mgr"
            local activity = festival_activity_mgr.GetActivityDataByActivityCodeType(festival_activity_cfg.ActivityCodeType.SevenDayLogin)
            if activity and activity.activityID then
                --这里要用到活动id，所以要先判断是否能解锁
                ui_window_mgr:ShowModule("ui_seven_day_login", nil, nil, { activityID = activity.activityID })
            end
        end,
        conditionShowFunc = function()
            local gw_seven_day_login_mgr = require "gw_seven_day_login_mgr"
            local isOpen = gw_seven_day_login_mgr.CheckIsOpen()
            return isOpen
        end,
        timerShowFunc = function()
            local festival_activity_mgr = require "festival_activity_mgr"
            local activity = festival_activity_mgr.GetActivityDataByActivityCodeType(festival_activity_cfg.ActivityCodeType.SevenDayLogin)
            if activity and activity.activityID then
                local gw_seven_day_login_data = require "gw_seven_day_login_data"
                local serverData = gw_seven_day_login_data.GetServerData(activity.activityID)
                if serverData then
                    return not serverData.isRed
                end
            end
            return false
        end,
        countdownTimeCfg = {
            --buttonType = nil,--入口类型，用来做定时器的key  默认规则是只要你不自己定义，就会统一处理，你自己定义了就会取自己的
            timeStamp = function()
                local festival_activity_mgr = require "festival_activity_mgr"
                local activity = festival_activity_mgr.GetActivityDataByActivityCodeType(festival_activity_cfg.ActivityCodeType.SevenDayLogin)
                if activity and activity.activityID then
                    local gw_seven_day_login_data = require "gw_seven_day_login_data"
                    local serverData = gw_seven_day_login_data.GetServerData(activity.activityID)
                    if serverData then
                        if serverData.isRed then
                            return 0
                        else
                            return serverData.curDayZeroTime
                        end
                    end
                end
                return 0
            end, --结束时间戳 
            buttonType = main_slg_const.MainButtonGroupType.RightTop2
        },
    },
    [festival_activity_cfg.ActivityEntranceType.ResourceDownload] = { --资源下载按钮
        getTextFunc = function()
            --获取文本
            return lang.Get(602087) --资源
        end,
        getTextTimerFunc = function(textUI)
            --这里会开启一个定时器，每秒执行一次 (下载的特殊处理，要每秒检测)
            local gray_load_mgr = require "gray_load_mgr"
            local loadSize, total, start_or_pause = gray_load_mgr.GetLoadParam()
            local bGrayLoad = total > 0 and loadSize < total
            if not util.IsObjNull(textUI) then
                textUI.text = lang.Get(602087)
            end
            local rate = math.floor(loadSize * 100 / total)
            if not util.IsObjNull(textUI) then
                local str = lang.Get(602087)
                if tostring(rate):lower() == "nan" then
                    str = lang.Get(602087)
                else
                    str = rate .. "%"
                end
                textUI.text = str
            end
            if not bGrayLoad then
                ui_window_mgr:UnloadModule("ui_grayload")
            end
            event.Trigger(event.RefreshMain_Download)
        end,
        clickFunc = function()
            --点击事件
            local gray_load_mgr = require "gray_load_mgr"
            local param = gray_load_mgr.GetGrayLoadParam()
            local grayBtnCallBack = param.grayBtnCallBack

            if grayBtnCallBack then
                grayBtnCallBack()
            else
                ui_window_mgr:ShowModule("ui_grayload")
            end
        end,
        conditionShowFunc = function()
            if ReviewingUtil.IsReviewing() then
                return false
            end
            local gw_home_building_data = require "gw_home_building_data"
            local level = gw_home_building_data.GetBuildingMainLevel()
            --return true
            local initCfg = game_scheme:InitBattleProp_0(8269)
            if initCfg then
                local limitLv = initCfg.szParam.data[0]
                --大本等级超过一定等级才会显示该按钮
                if level < limitLv then
                    return false
                end
            end

            local main_slg_mgr = require "main_slg_mgr"
            --封装的显示判断
            local isShow = main_slg_mgr.GetDownLoadBtnIsShow()
            return isShow --默认显示
            --return true
        end,
        refreshMsg = event.RefreshMain_Download, --刷新事件
        redData = {
            redType = red_const.Enum.DownLoadRed,
            redPathType = red_const.Type.Default
        },
    },
    [festival_activity_cfg.ActivityEntranceType.Questionnaire] = { --调查问卷按钮
        clickFunc = function()
            --点击事件
            --log.Error("点击了调查问卷")
            local net_questionnaire_module = require "net_questionnaire_module"
            net_questionnaire_module.RequestQuestionnaire()
            ui_window_mgr:ShowModule("ui_questionnaire_investigation")
        end,
        conditionShowFunc = function()
            if ReviewingUtil.IsReviewing() then
                return false
            end
            local questionnaire_investigation_mgr = require "questionnaire_investigation_mgr"
            local data = questionnaire_investigation_mgr.CheckAllQuestionnaireData()
            if data then
                return true
            end
            return false
        end,
        refreshMsg = event.RefreshMain_Questionnaire, --当Trigger这个事件时会在执行一次上面的刷新方法
        redData = {
            redType = red_const.Enum.Questionnaire,
            redPathType = red_const.Type.Default
        },
    },
    [festival_activity_cfg.ActivityEntranceType.ChangePackage] = { --换包按钮
        clickFunc = function()
            --点击事件
            ui_window_mgr:ShowModule("ui_change_package")
        end,
        conditionShowFunc = function()
            if ReviewingUtil.IsReviewing() then
                return false
            end
            local change_package_mgr = require "change_package_mgr"
            return change_package_mgr.CheckIsShowEnter()
        end,
        refreshMsg = event.RefreshMain_ChangePackage, --当Trigger这个事件时会在执行一次上面的刷新方法
        redData = {
            redType = red_const.Enum.ChangePackageRed,
            redPathType = red_const.Type.Default
        },
    },
}

---@deprecated 聊天气泡配置
---order 优先级，值越大越高
chatBulletConfig = {
    [main_slg_const.MainButtonType.chatDigTreasureBubble] = {
        bubbleType = main_slg_const.MainButtonType.chatDigTreasureBubble,
        icon = "leida_btn_ditu",
        iconType = main_slg_const.MainChatBubbleIconType.SingleImage,
        order = 1,
        clickFunc = function()
            ----直接打开联盟频道
            local ui_chat_mgr_gw = require "ui_chat_mgr_gw"
            local chat_mgr_new = require "chat_mgr_new"
            ui_chat_mgr_gw.ShowChatMainPanel(chat_mgr_new.enum_pState.guide)
        end,
        conditionShowFunc = function()
            local radar_mgr = require "radar_mgr"
            local isShow = radar_mgr.IsDigTreasureBubbleShow()
            --local log = require "log"
            --log.Error("返回是否显示的结果：",isShow)
            return isShow
        end,
        refreshMsg = radar_define.RADAR_DIG_TREASURE_MSG_BUBBLE_UPDATE,
    },
    [main_slg_const.MainButtonType.chatPrivateBubble] = {
        bubbleType = main_slg_const.MainButtonType.chatPrivateBubble,
        icon = "leida_btn_ditu",
        iconType = main_slg_const.MainChatBubbleIconType.BgAndIcon,
        order = -10,
        clickFunc = function()
            -- 获取一条最新的有红点私聊信息
            local chat_mgr_new = require "chat_mgr_new"
            local chatData = chat_mgr_new.GetNewUnreadData(chat_mgr_new.ENUM_CHANNEL.PRIVATE)
            if chatData then
                local faceStr = chatData[1].faceId
                if chatData[1].faceStr and not string.IsNullOrEmpty(chatData[1].faceStr) then
                    faceStr = chatData[1].faceStr
                end
                local sessionData = { sessionId = chatData[1].roleid,
                                      toWorldid = chatData[1].worldid,
                                      toTitleName = "",
                                      toName = chatData[1].name,
                                      toFaceId = faceStr,
                                      toLv = chatData[1].roleLv,
                                      toAvatarFrame = chatData[1].avatarFrame }
                local ui_chat_mgr_gw = require "ui_chat_mgr_gw"
                ui_chat_mgr_gw.ShowChatMainPanel(chat_mgr_new.enum_pState.privateView, sessionData, false, true)
            end

        end,
        conditionShowFunc = function()
            -- 获取一条最新的有红点私聊消息
            local chat_mgr_new = require "chat_mgr_new"
            return chat_mgr_new.GetNewUnreadData(chat_mgr_new.ENUM_CHANNEL.PRIVATE)
        end,
        refreshMsg = ui_chat_define_gw.Evt_ChatPrivateBubbleRefresh,
    },
    [main_slg_const.MainButtonType.chatAllianceNoticeBubble] = {
        bubbleType = main_slg_const.MainButtonType.chatAllianceNoticeBubble,
        icon = "lt_zjm_icon_ggtx",
        iconType = main_slg_const.MainChatBubbleIconType.BgAndIcon,
        order = 10,
        clickFunc = function()
            local ui_chat_mgr_gw = require "ui_chat_mgr_gw"
            local chat_mgr_new = require "chat_mgr_new"
            ui_chat_mgr_gw.ShowChatMainPanel(chat_mgr_new.enum_pState.allianceNotice)
        end,
        conditionShowFunc = function()
            local chat_mgr_new = require "chat_mgr_new"
            local mq_common_pb = require "mq_common_pb"
            return chat_mgr_new.GetNewUnreadData(chat_mgr_new.ENUM_CHANNEL.GUIDE, mq_common_pb.enSpeak_Announcement) or
                    chat_mgr_new.GetNewUnreadData(chat_mgr_new.ENUM_CHANNEL.GUIDE, mq_common_pb.enSpeak_UrgentAnnouncement)
        end,
        refreshMsg = ui_chat_define_gw.Evt_ChatNoticeBubbleRefresh,
    },
    [main_slg_const.MainButtonType.surpriseBagBubble] = {
        bubbleType = main_slg_const.MainButtonType.surpriseBagBubble,
        icon = "jxh_img_qiPao",
        iconType = main_slg_const.MainChatBubbleIconType.SingleImage,
        order = 11,
        clickFunc = function()
            local ui_chat_mgr_gw = require "ui_chat_mgr_gw"
            local surprise_bag_data = require "surprise_bag_data"
            ui_chat_mgr_gw.ShowChatMainPanel(surprise_bag_data.GetLastChatMsgRedChannel())
        end,
        conditionShowFunc = function()
            local surprise_bag_data = require "surprise_bag_data"
            return surprise_bag_data.IsChatMsgRed()
        end,
        refreshMsg = ui_chat_define_gw.Evt_ChatSurpriseBagBubbleRefresh,
    },

}

---description 打开下列界面的时候会自动关闭主界面
autoCloseMainUIConfig = {
    -- ui_sand_search = true, --沙盘搜索    
    -- ui_gw_hero_list = true, --英雄
    -- ui_alliance_list = true, --联盟推荐
    -- ui_alliance_main = true, --联盟主页
    -- ui_package_gw = true, --新背包
    -- -- ui_tavern_main = true, --酒馆
    -- --ui_chat_main_new = true, --聊天
    -- ui_task_achievement = true, --任务
    -- --new_hook_scene = true, --主线关卡
    -- ui_puzzle_game_level = true, --小游戏关卡
    -- ui_gw_story = true, --对话
    -- ui_casual_game_common = true, --小游戏打开
    -- ui_gwhero_summon_base = true, --招募
    -- ui_common_mini_game_level = true, --小游戏关卡
}
---description 打开下列界面的时候会自动关闭主界面,但保留顶部资源UI
autoCloseMainUINoTopConfig = {
    ui_bs_buildbar = true, --建造
    ui_bs_buildpop = true, --建筑列表
    ui_bs_operate = true, --城建menu界面
    --ui_bs_upgrade = true, --升级
    ui_bs_fortified_wall_panel = true, --城墙
    ui_new_hero_select_panel = true, --编队
}

---description 打开下列界面的时候会自动关闭主界面,但保留顶部资源UI且顶部资源UI层级会被设为最高
autoCloseMainUINoTopSetLayerConfig = {
    ui_bs_upgrade = true, --升级
    ui_speed_up_panel = true, --加速界面
}

---description 关闭下列界面的时候会自动打开主界面
autoOpenMainUIConfig = {
    -- ui_sand_search = true, --沙盘搜索
    -- --ui_bs_operate = true, --城建menu界面
    -- ui_bs_buildbar = true, --建造
    -- ui_bs_buildpop = true, --建筑列表
    -- ui_bs_upgrade = true, --升级
    -- ui_speed_up_panel = true, --加速界面
    -- ui_bs_fortified_wall_panel = true, --城墙
    -- --ui_new_hero_select_panel = true, --编队
    -- ui_gw_hero_list = true, --英雄
    -- ui_alliance_list = true, --联盟推荐
    -- ui_alliance_main = true, --联盟主页
    -- ui_package_gw = true, --新背包 
    -- -- ui_tavern_main = true, --酒馆
    -- ui_chat_main_new = true, --聊天
    -- ui_task_achievement = true, --任务
    -- --new_hook_scene = true, --主线关卡
    -- ui_puzzle_game_level = true, --小游戏关卡
    -- ui_mini_technology_queue_panel = true, --迷你科研队列
    -- ui_gw_story = true, --对话
    -- ui_casual_game_common = true, --小游戏打开
    -- ui_gwhero_summon_base = true, --招募
    -- ui_common_mini_game_level = true, --小游戏关卡
}

autoOpenMainUIConfigWithChecking = {
    --["ui_bs_fortified_wall_panel"] = 
    --{
    --    checkFunc = function()
    --        local gw_mgr = require "gw_mgr"
    --        local gw_const = require "gw_const"
    --        return gw_mgr.GetFsmCurScene() == gw_const.ESceneType.Storm
    --    end
    --},
    --["ui_new_hero_select_panel"] =
    --{
    --    checkFunc = function()
    --        local gw_mgr = require "gw_mgr"
    --        local gw_const = require "gw_const"
    --        return gw_mgr.GetFsmCurScene() ~= gw_const.ESceneType.Sand
    --    end
    --}
}
--所有挂载在主界面下的子界面枚举
mainSlgChildPanelData =
{
    ["ui_desert_storm_activity_tips_panel"] =
    {
        CheckFunc = function()
            local gw_storm_mgr = require "gw_storm_mgr"
            gw_storm_mgr.ShowClockDown()
        end
    },
    ["ui_zombie_storm_warning_timer_panel"] =
    {
        CheckFunc = function()
            local gw_zombie_storm_mgr = require "gw_zombie_storm_mgr"
            gw_zombie_storm_mgr.OnCheckWarningTimer()
        end
    }
}