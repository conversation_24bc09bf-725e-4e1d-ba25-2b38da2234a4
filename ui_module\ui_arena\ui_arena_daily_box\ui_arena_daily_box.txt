local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_arena_daily_box_binding"

--region View Life
module("ui_arena_daily_box")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

local boxNumStr = "txt_BoxItemNum_"
local boxImgStr = "ss_BoxItem_"
local boxBtnStr = "btn_BoxItem_"

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:InitProgressShow(data)
    if not data then
        return
    end
    self.txt_challengeNum.text = string.format("%s%d/%d", lang.Get(1009114),data.curChallengeNum, data.maxTaskFinishNum)
    self.rtf_progress.transform.anchoredPosition = { x = self.rtf_progress.transform.sizeDelta.x * data.sliderValue, y = 0, z = 0 }
    for k, v in ipairs(data.taskCfg) do
        local itemName = string.format("%s%d",boxNumStr, k)
        if self[itemName] then
            self[itemName].text = v.ConditionValue1
        end
        local btnName = string.format("%s%d",boxBtnStr, k)
        if self[btnName] then
            self:AddBtnOnClick(self[btnName],function()
                self:RefreshRewardShow(self[btnName], v, data.activityId)
            end)
        end
    end
end

function UIView:RefreshRewardShow(index, imgIndex)
    local itemName = string.format("%s%d",boxImgStr, index)
    if self[itemName] then
        self[itemName]:Switch(imgIndex)
    end
end


function UIView:RewardButtonClick(tra, taskCfg, activityId)
    local gw_task_mgr = require "gw_task_mgr"
    local state = gw_task_mgr.GetTaskIsReceive(taskCfg.TaskID)
    if state then
        --可领取
        gw_task_mgr.ReceiveTaskReward(taskCfg.TaskID, activityId)
    else
        local ui_util = require "ui_util"
        local reward_mgr = require "reward_mgr"
        ui_util.ShowRewardTips(tra, reward_mgr.GetRewardGoodsList(taskCfg.TaskReward))
    end
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
