--- base_comp.txt -------------------------------
--- author:  fgy
--- date: 2024/4/24 15:13
--- desc:需要绑定得基类
--------------------------------------------------
local newclass = newclass
local checktable = checktable
local ipairs = ipairs

module("base_comp")
local base_comp = newclass("base_comp")

function base_comp:ctor(name, depends)
    self.name_ = name
    self.depends_ = checktable(depends)
end

function base_comp:getName()
    return self.name_
end

function base_comp:getDepends()
    return self.depends_
end

function base_comp:getTarget()
    return self.target_
end

function base_comp:exportMethods_(methods)
    self.exportedMethods_ = methods
    local target = self.target_
    local com = self
    for _, key in ipairs(methods) do
        if not target[key] then
            local m = com[key]
            target[key] = function(__, ...)
                return m(__, ...)
            end
        end
    end
    return self
end

function base_comp:bind_(target)
    self.target_ = target
    for _, name in ipairs(self.depends_) do
        if not target:checkComponent(name) then
            target:addComponent(name)
        end
    end
    self:onBind_(target)
end

function base_comp:unbind_()
    if self.exportedMethods_ then
        local target = self.target_
        for _, key in ipairs(self.exportedMethods_) do
            target[key] = nil
        end
    end
    self:onUnbind_()
end

function base_comp:onBind_()
end

function base_comp:onUnbind_()
end

return base_comp
