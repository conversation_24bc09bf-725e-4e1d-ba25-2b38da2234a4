---简易任务队列
---1，支持任务按队列插入的顺序执行（谁先插入就先执行，eg.A,B C三个任务队列执行，A任务中又含有队列执行任务A1，A1是要等到A执行的时候才会出入队列中的）
---2，支持延迟执行
---3，支持条件检测
---4，默认是插入的队列自动同步进行，会在同一帧执行完所有的连续的AutoComplete类型任务
---5，因为4的原因，可能一帧会执行n多任务，很卡，执行AutoComplete任务时，可以支持自动分帧 查看SetAutoFrame()
local table = table
local setmetatable = setmetatable
local type = type
local ipairs = ipairs
local event = require "event"
local xpcall = xpcall
local tonumber = tonumber
local tostring = tostring
local debug = debug
local unpack = unpack
local DateTime = CS.System.DateTime
local TaskQueue = {}
TaskQueue.__index = TaskQueue
local logger = require("logger").new("sw_simple_task_queue_log", 0)
local Warning = logger.Warning
local Warning0 = logger.Warning0

local  Enum_CompleteType = {
	AutoComplete = 0, --自动完成，即立即
	CheckCondition = 1, -- 检测条件
	Time = 2, --  延迟完成
}
local allTaskQueue = {}
local hasRegisterUpdateTick = false
local _taskId = 0
local _const_max_task_id = 1000000000
local _const_convert_s = 1000

local function _OnLuaErr(err,...)
	Warning0("Func Exec Error:",err)
	local util = require "util"	
	util.AssetBundleManagerTrackEvent("lua_err", {
		type = "simple_task_queue",
		err = err,
		trackback = debug.traceback(),
	})
end



function TaskQueue.new()
	local obj = {
		queue = {},
		running = false,
		currentTask = nil,
		startTime = 0,
	}
	setmetatable(obj, TaskQueue)
	table.insert(allTaskQueue, obj)
	return obj
end

function TaskQueue:_CreateTask(taskFunc,checkFunc, delayTime, onComplete, ...)
	----传入的任务函数不限制是否为nil
	--if taskFunc == nil then
	--	return
	--end
	local cType =  Enum_CompleteType.AutoComplete
	if checkFunc ~= nil then
		cType = Enum_CompleteType.CheckCondition
	elseif type(delayTime) == "number" then
		cType = Enum_CompleteType.Time
	end
	_taskId = _taskId + 1
	if _taskId > _const_max_task_id then
		_taskId = 0
	end
	local task = {
		taskId  = _taskId,
		func = taskFunc,
		completeType = cType,
		checkFunc = checkFunc,
		delayTime = (delayTime or 0)*_const_convert_s , --特别注意，为了能精确到到ms级别
		onComplete = onComplete,
		args = {...},
	}
	table.insert(self.queue, task)
	Warning(1,"创建了taskID =",task.taskId," 任务类型=",cType," 延迟时间=",task.delayTime/_const_convert_s)
	if cType ~= Enum_CompleteType.AutoComplete then
		TaskQueue._RegisterUpdateTick()		
	else 
		--如果是立即执行  
		if not self.waitNextFrame then
			self:Tick()			
		end
	end	
end
---@public 设置队列自动分帧 --限定队列是立即执行的任务时
---@param active boolean 是否开启自动分帧
---@param frameLimitTime number 单位s 当前帧执行时间超过多少s时，自动切换下一帧来执行后续的任务，--特别注意，一个任务不论怎么样也必须一帧执行完，和C#的 await不一样
function TaskQueue:SetAutoFrame(active,frameLimitTime)
	self.autoFrame = active
	self.autoFrameLimitMS = (frameLimitTime or 0.033)*_const_convert_s --如果没设置，自动33ms  30帧率进行分帧
end

---@public Push一个队列任务，，支持检测条件
----@param taskFunc function 任务函数
---@param checkFunc function 检测函数--即完成条件
---@param onComplete function 完成回调函数
---@param ... any 其他参数
function TaskQueue:Push(taskFunc, checkFunc, onComplete, ...)
	self:_CreateTask(taskFunc, checkFunc, nil, onComplete, ...)
end

---@public Push一个队列任务，，支持延迟
----@param taskFunc function 任务函数
---@param delayTime number 延迟时间
---@param onComplete function 完成回调函数,一般情况下不需要，taskFunc就会在完成时候调用；
---@param ... any 其他参数
function TaskQueue:PushDelay(taskFunc, delayTime, onComplete, ...)
	self:_CreateTask(taskFunc, nil, delayTime, onComplete, ...)
end

-- 内部方法，执行当前任务完成处理，自动切换下一任务
function TaskQueue:_DoCompleteCurrent()
	if self.currentTask then
		if self.currentTask.func then
			self:SafeExecuteTask(self.currentTask.func,_OnLuaErr,unpack(self.currentTask.args))			
		end
		if self.currentTask.onComplete then
			self:SafeExecuteTask(self.currentTask.onComplete,_OnLuaErr)
		end
		--完成当前任务
		table.remove(self.queue, 1)
		Warning(1,"完成了taskID =",self.currentTask.taskId," 任务类型=",self.currentTask.completeType," 延迟时间=",self.currentTask.delayTime/_const_convert_s)
	end	
	self.running = false
	local taskId = self.currentTask.taskId
	local completeType = self.currentTask.completeType 
	self.currentTask = nil
	self.waitNextFrame = false
	--完成当前任务时，要判断是否还有其他任务
	if #self.queue == 0 then
		self.frameStartTicks = nil
		TaskQueue._UnregisterUpdateTick()
		return
	end
	--如果开启了自动分帧且当前帧执行时间超过了限制时间，自动切换下一帧执行
	if self.autoFrame and  completeType == Enum_CompleteType.AutoComplete then
		local elapsed = tostring(DateTime.Now.Ticks - self.frameStartTicks)
		elapsed = tonumber(elapsed)
		--Warning0("当前任务id ",taskId,"耗时=",elapsed, " 限制时间=",self.autoFrameLimitMS,DateTime.Now.Ticks,self.frameStartTicks,elapsed > self.autoFrameLimitMS*10000)
		if elapsed > self.autoFrameLimitMS*10000 then
			--启动注册update
			TaskQueue._RegisterUpdateTick()
			self.waitNextFrame = true
			return			
		end
	end
	TaskQueue._CheckUnregisterUpdateTick()
	-- 立即尝试执行下一个任务（可能递归执行多个连续完成任务）--相当于同一帧可能就完成了N个任务
	self:Tick()	
end

function TaskQueue:Tick(isUpdate)
	--log.Error("Ticking  ", tostring(self.disposed),#self.queue)
	if self.disposed then
		return
	end
	if #self.queue == 0 then
		return
	end
	if isUpdate then
		self.frameStartTicks = DateTime.Now.Ticks		
	end
	if not self.currentTask then		
		self.currentTask = self.queue[1]			
		self.startTime = DateTime.Now.Ticks
		if not self.frameStartTicks then
			self.frameStartTicks = self.startTime
		end
		self.running = true
		Warning(1,"启动了taskID =",self.currentTask.taskId," 任务类型=",self.currentTask.completeType," 延迟时间=",self.currentTask.delayTime/_const_convert_s)
		-- 同步完成，马上标记完成
		if self.currentTask.completeType == Enum_CompleteType.AutoComplete then
			self:_DoCompleteCurrent()
		end
	end
	if not self.currentTask then
		return
	end
	if self.currentTask.completeType == Enum_CompleteType.CheckCondition then
		if self.currentTask.checkFunc then
			local complete = self:SafeExecuteTask(self.currentTask.checkFunc,_OnLuaErr)
			if complete then
				self:_DoCompleteCurrent()				
			end
		end
	elseif self.currentTask.completeType == Enum_CompleteType.Time then
		--local elapsed = os.time() - self.startTime
		local elapsed =(DateTime.Now.Ticks - self.startTime)  --100ms
		elapsed = tostring(elapsed) --尼玛，userdata一定要先转成string todo
		elapsed = tonumber(elapsed)
		if elapsed >= self.currentTask.delayTime *10000 then
			self:_DoCompleteCurrent()
		end
	end
end

---@public 任务队列销毁
function TaskQueue:Dispose()
	if self.disposed then
		return
	end
	-- 清空任务队列
	self.queue = {}
	---- 如果当前任务还有回调，
	--if self.currentTask and self.currentTask.onComplete then
	--	-- 这里可以改成专门的清理回调  如果还清理的，强制当前任务完成
	--	self.currentTask.onComplete(true)
	--end
	self.currentTask = nil
	self.running = false
	self.disposed = true
	TaskQueue.RemoveQueue(self)
end

---@public 队列是否空闲
function TaskQueue:IsIdle()
	return not self.running and #self.queue == 0
end

function TaskQueue:IsNeedTick()
	return not self.running and #self.queue == 0
end
-----@public 尝试安全执行任务，
--function TaskQueue:TryExecuteTask(func, param)	
--	local ok,result = xpcall(function()
--		return func(unpack(param))
--	end,function(err)
--		Warning0("Func Exec Error:",err)
--	end)
--	if ok then
--		return result
--	end
--	return false
--end
-----@public 尝试安全执行任务，不带参数
--function TaskQueue:TryExecuteTaskNoParam(func)
--	local ok,result = xpcall(func
--	,function(err) 
--		--这里警告；如果是检测函数，会一直报错
--		Warning0("Func Exec Error:",err)
--	end)
--	if ok then
--		return result
--	end
--	return false
--end

---@public 尝试安全执行任务，
function TaskQueue:SafeExecuteTask(func,err_func,...)
	local ok,result = xpcall(func
	,err_func,...)
	if ok then
		return result
	end
	return false
end


-------静态接口函数-----------------------
function TaskQueue.TaskUpdate()
	for i, v in ipairs(allTaskQueue) do
		v:Tick(true)
	end
end
function TaskQueue.RemoveQueue(queue)
	-- 清空队列类
	for i, v in ipairs(allTaskQueue) do
		if v == queue then
			table.remove(allTaskQueue, i)
			TaskQueue._CheckUnregisterUpdateTick()
			break
		end
	end
end
function TaskQueue.Clear()
	for i, v in ipairs(allTaskQueue) do
		v:Dispose()
	end
	allTaskQueue = {}
	TaskQueue._CheckUnregisterUpdateTick()
end
--有任务的时候，注册update
function TaskQueue._RegisterUpdateTick()
	if not hasRegisterUpdateTick then
		event.Register(event.CSUpdate, TaskQueue.TaskUpdate)
		Warning(1,"开启了监听Update ")
	end
	hasRegisterUpdateTick = true
end
--任务执行完毕之后，关闭update
function TaskQueue._UnregisterUpdateTick()
	if hasRegisterUpdateTick then
		event.Unregister(event.CSUpdate, TaskQueue.TaskUpdate)
		hasRegisterUpdateTick = false
		Warning(1,"监听Update ，取消注册=",hasRegisterUpdateTick)
	end
end
---@public 检测是否要取消注册 tick
function TaskQueue._CheckUnregisterUpdateTick()	
	--判定一下，是否还有条件检测任务/延迟任务
	local hasTickTask = false
	for i, v in ipairs(allTaskQueue) do
		for i1, v1 in ipairs(v.queue) do
			if v1.completeType == Enum_CompleteType.CheckCondition or v1.completeType == Enum_CompleteType.Time then
				hasTickTask = true
				break
			end
		end
		if hasTickTask then
			break
		end
	end
	if not hasTickTask then
		TaskQueue._UnregisterUpdateTick()
	end	
end

return TaskQueue