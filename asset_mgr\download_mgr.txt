-- download_mgr.txt ------------------------------------------
-- author:  李婉璐
-- date:    2019.08.14
-- ver:     1.0
-- desc:    后台下载
--------------------------------------------------------------
local os = os
local print = print
local require = require
local pairs = pairs
local table = table
local ipairs = ipairs
local debug = debug
local string = string
local coroutine = coroutine
local math = math
local typeof = typeof
local xpcall = xpcall
local log = require 'log'
local util = require "util"
local lang = require "lang"
local event = require "event"
local exec_split = require "exec_split"
local dump = dump

local BgDownloadMgr = CS.War.Base.BgDownloadMgr
local AssetBundleManager = CS.War.Base.AssetBundleManager
local hashRemote = CS.War.Base.AssetBundleManager.hashRemote
local Debug = CS.UnityEngine.Debug
local PingCheck = CS.War.Script.PingCheck
local GameObject = CS.UnityEngine.GameObject
local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local Utility = CS.War.Script.Utility
local Input = CS.UnityEngine.Input
local Time = CS.UnityEngine.Time
local CreateInstance = CS.System.Array.CreateInstance
local String = CS.System.String

module("download_mgr")
local inited = false
local maxCount = 10             -- 同时下载的数量限制
local waitMaxTime = 0             -- 下载失败重新下载最大等待时长
local totalCount = 0           -- 需要下载的总资源数量
local totalSize = 0           -- 需要下载的总资源大小(仅代表登陆前下载文件)
local finishSize = 0           -- 下载完成任务资源大小(仅代表登陆前下载文件)
local downloadingList = {}     -- 当前下载列表
local waitingList = {}         -- 等待下载列表
local finishCount = 0          -- 下载完成任务数量
local redownloadTimes = 0      -- 重新下载次数
--local failList = {}            -- 下载失败列表，尝试重新下载3次，如果失败，给出提示
local failListCount = 0        -- 失败列表数量

local failtimes = 0      -- 当前下载失败次数，切换cdn后重置

local beforeDownloadList = {} -- 用于标识登陆前已下载文件

local skipDownList = {
	['gamescp_lang_1']='ind',
	['gamescp_lang_2']='th',
	['gamescp_lang_3']='fr',
	['gamescp_lang_4']='de',
	['gamescp_lang_5']='ma',
	['gamescp_lang_6']='zh',
	['gamescp_lang_7']='en',
	['gamescp_lang_8']='ru',
	['gamescp_lang_9']='es',
}

--排除掉在忽略列表中的资源
function ExcludeSkipDownList(resDiffList)
	if not resDiffList or resDiffList.Length <= 0 then
		return resDiffList
	end

	local diffListTable = {}
	local insListIndex = 1
	for listIndex = 0, resDiffList.Length -1 do
		if not IsInSkipDown(resDiffList[listIndex]) then
			table.insert(diffListTable, insListIndex, resDiffList[listIndex])
			insListIndex = insListIndex + 1
		end
	end

	local array = CreateInstance(typeof(String), #diffListTable)
	for k = 0, array.Length - 1 do
		array[k] = diffListTable[k + 1]
	end
	return array
end

--是否在忽略下载列表中
function IsInSkipDown(localFileName)
	local curLang = lang.USE_LANG

	if not localFileName or localFileName == "" then
		return true
	else
		if skipDownList[localFileName] then
			-- 下载阶段仅下载系统对应语言得lang文件
			if skipDownList[localFileName] ~= curLang then
				return true
			end
		end
	end

	return false
end

function ExecAddSkipDownList()
	if isFirstAddSkipDownList then
		local init_scr = require "init_scr"
		local isUseOldExec = init_scr.CheckCurExecIsOld()
		local execNameConfig = exec_split.GetExecNameConfig()
		if isUseOldExec then
			for i = 1, 5 do
				local asset_name = 'exec' .. (i - 1) .. '.asset'
				skipDownList[asset_name] = 'luassets'
				--log.Warning("exectest01!!!",asset_name)
			end     
		else
			if execNameConfig then
				-- 新版exec列表
				for i = 1, exec_split.GetExecNameLength() do
					local asset_name = execNameConfig[i] .. '.asset'
					--log.Warning("exectest02!!!",asset_name)
					skipDownList[asset_name] = 'luassets'
				end 
			end
		end
		--dump(skipDownList)
		isFirstAddSkipDownList = false
	end
end
ExecAddSkipDownList()

-- 新增 后台下载标识 不清除
local initedBD = false
local enum_state = 
{
	NONE = 0,    -- 无状态
	WAITING = 1, -- 等待下载状态
	FINISH = 2,  -- 下载完成状态
	ERROR = 3,   -- 下载失败状态
}

------------------------ 上报打点相关---------------------
local reportData =
{
["0%"] = 0,
["10%"] = 0,
["20%"] = 0,
["30%"] = 0,
["40%"] = 0,
["50%"] = 0,
["60%"] = 0,
["70%"] = 0,
["80%"] = 0,
["90%"] = 0,
["100%"] = 0,
}

local download_state =
{
	befor_login = "befor_login_downloading",  -- 登录前下载
	background = "background_downloading",   -- 后台下载
	extra_load = "extra_load",   -- 后台下载
}

local startTime = nil   -- 开始下载时间
local lastTime = nil    -- 上一个进度下载完成时间
local downloadType = download_state.befor_login

------------------------ 上报打点相关---------------------
----------------------------------------------------------

local progressChangeFun = nil  -- 下载进入变化回调
local onEvent = nil  -- 事件回调
local downloadFailFun = nil    -- 下载失败回调
local ticker = nil
local isFinished = false
local isDispose = false
local needWifi = false    -- 是否需要检测在WiFi环境下下载
local initMaxCount = 50  -- 初始化下载列表时最大数量
local const_idle_sec = 0  -- 屏幕操作n秒后开始空闲时间
local idle_sec = 3  -- 屏幕操作n秒后开始空闲时间
local idle_time = 0  -- 开始空闲的时间
local idle_ticker
local checkList_ticker

----------------------------------------------------------
------------------------ 超时补偿机制---------------------

local redownloadTicker = nil   -- 检测重试超时
local timeout = 5         -- 5s重试机制
local hasRedownload = false
------------------------ 超时补偿机制---------------------
----------------------------------------------------------

local function Init()
	inited = true
	local q1sdk = require "q1sdk"
end

--[[
启动后台下载管理器
@param difflist       abname的下载列表
@param count          同时下载的最大数量限制，不传默认是5个
@param updateCallback 任务进度变化回调
@param failCallback   多次尝试下载仍然失败的回调处理
@param nType   download_state
@param idle_sec_   屏幕操作n秒后开始空闲时间,默认为const_idle_sec
-- 下面两个参数暂时由内部管理，不直接由外部传入
@param errorCallback  某个任务下载失败回调
@param finishCallback 某个任务下载完成回调
@param nol(No Omission List) 不使用忽略列表
]]
function InitDownloadMgr(difflist, count, updateCallback, failCallback, nType,idle_sec_,_onEvent, nol)
	if inited == false then
		if nType == download_state.background then
			difflist = AssetBundleManager.GetDiffList()
			local preload_resources = require "preload_resources"
			--登录下载需要剔除合集小游戏需要下载的小游戏资源
			difflist = preload_resources.ExcludeCollectionDownloadRes(difflist)
			initedBD = true
		end
		
		Init()
		--idle.PrintMM()
		if not difflist then
			print("difflist nil")
			Dispose()
			return
		end
		Debug.Log("InitDownloadMgr>>>>>>>>>>>>>>>>>>"..maxCount)

		
		if count and count > 0 then
			maxCount = count
		end
		
		if updateCallback then
			progressChangeFun = updateCallback
		end
		
		if failCallback then
			downloadFailFun = failCallback
		end
		onEvent = _onEvent
		if nType then
			downloadType = nType
		end
		
		local url = ""
		local needAdd = true
		totalCount = 0
		totalSize = 0
		beforeDownloadList = {}
		local curLang = lang.USE_LANG
		if checkList_ticker then
			util.RemoveDelayCall(checkList_ticker)
			checkList_ticker = nil
		end
		checkList_ticker = util.DelayCall(0, function()
			-- 下载50条之后
			local curIndex = 0

			print("preload_resources", "开始检测 ",downloadType," 资源列表")
			local localFileName = nil
			local addInd = 0
			local length = difflist.Length or #difflist
			if not difflist.Length then
				addInd = 1
			end

			print(" beforeTime = "..Time.deltaTime)
			for i = addInd, length+ addInd -1 do
				localFileName = difflist[i]

				if curIndex > initMaxCount then
					curIndex = 0
					if downloadType == download_state.background then
						coroutine.yield(0.1)
					end
				end
				curIndex = curIndex + 1
				needAdd = true
				if not localFileName or localFileName == "" then
					needAdd = false
				else
					if not nol and skipDownList[localFileName] then
						-- 下载阶段仅下载系统对应语言得lang文件
						if skipDownList[localFileName] ~= curLang then
							needAdd = false
						end
					end

					url = AssetBundleManager.GetResUrl(localFileName)

					--print(url)
					if not url or url == "" or url==localFileName then
						needAdd = false
					end
				
					if needAdd == true and AssetBundleManager.IsLocalFile(localFileName) == true then
						needAdd = false
					end
				end
				if needAdd == true then
					-- 等待下载状态
					waitingList[localFileName] = enum_state.WAITING
					totalSize = totalSize + hashRemote:GetSize(localFileName)
				else
					if onEvent then
						onEvent("on_task_exist", localFileName)
					end
				end	
			end
			print("preload_resources", "检测 ",downloadType," 资源列表完成")

			print(" afterTime = "..Time.deltaTime)
			totalCount = util.get_len(waitingList)
			
			finishCount = 0
			finishSize = 0
			startTime = os.clock()
			DelayPropressChange()
			print("preload_resources-totalCount:",totalCount, ",totalSize:", totalSize)
			
			if totalCount > 0 then
				isDispose = false
				if downloadType == download_state.background then
					needWifi = true
					waitMaxTime = 30
				end
				BgDownloadMgr.Instance:Start()
				BgDownloadMgr.Instance:RegisterDownloadErrorHandler(OnDownloadTaskError)
				BgDownloadMgr.Instance:RegisterDownloadFinishHandler(OnDownloadTaskFinish)
				BgDownloadMgr.Instance:RegisterDownloadCancleHandler(OnDownloadTaskCancle)
				StartDownload()
			else
				Dispose()
			end	
		end)

		
		idle_sec = idle_sec_ or const_idle_sec
		if idle_ticker then
			util.RemoveDelayCall(idle_ticker)
			idle_ticker = nil
		end

		
		if downloadType == download_state.extra_load then
			if idle_sec > 0 then
				idle_ticker = util.DelayCallOnce(0,function (  )
					if Input.anyKey then
						idle_time = Time.realtimeSinceStartup + idle_sec
					end
					return 0
				end)
			end
		end
	end
end

function GetResTotalSize(difflist)
	if not difflist then
		print("difflist nil")
		return
	end
	local size = 0
	for i,v in pairs(difflist) do
		size = size + hashRemote:GetSize(v)
		print("GetResTotalSize i = " ,i , "    v=",v ,"   cur size",hashRemote:GetSize(v), size)
	end
	return size
end

-- 是否是偶数
function IsEvenNumber(num)
    local num1,num2=math.modf(num/2) --返回整数和小数部分
    if(num2==0)then
        return true
    else
        return false
    end
end

function StartDownload()
	---if true then return end
	isFinished = false
	failListCount = 0
	downloadingList = {}
	local url = ""
	local needAdd = true
	if ticker then
		util.RemoveDelayCall(ticker)
		ticker = nil
	end

	local GetResUrl = AssetBundleManager.GetResUrl
	ticker = util.DelayCall(0, function ()
		for abname, state in pairs(waitingList) do
			if state == enum_state.WAITING or state == enum_state.ERROR then
				if totalCount <= finishCount then
					PropressChange()
					return
				end
				-- 有WiFi的时候开始下载
				while (
					(#downloadingList >= maxCount) 
					or (needWifi 
						and (util.GetNetworkType() ~= util.ENETWORK_TYPE.WIFI) 
						or (util.GetNetworkType() == util.ENETWORK_TYPE.NO_NETWORK)
					)
					or (
						idle_sec > 0 
						and idle_time>Time.realtimeSinceStartup
					)
				)
				do
					if downloadType == download_state.befor_login and util.GetNetworkType() == util.ENETWORK_TYPE.NO_NETWORK then
						event.Trigger(event.CHECK_DOWNLOAD_STATE, false)
					end
					--Debug.Log("downloadingListCount:"..#downloadingList)
					-- if 
					-- idle_sec > 0 
					-- and idle_time>Time.realtimeSinceStartup then
					-- 	-- print("idle skip")
					-- end
					coroutine.yield(0)
				end
				
				if downloadType == download_state.befor_login then
					event.Trigger(event.CHECK_DOWNLOAD_STATE, true)
				end
				
				needAdd = true		
				if not abname or abname == "" then
					needAdd = false
				end
				
				url = GetResUrl(abname)
				if needAdd == true and (not url or url == "") and AssetBundleManager.IsLocalFile(abname) == true then
					waitingList[abname] = nil
					finishCount = finishCount + 1
					finishSize = finishSize + hashRemote:GetSize(abname)
					needAdd = false
					Debug.Log("downloading File is in local:"..abname)
					PropressChange()
				end
				
				if needAdd == true then
					-- 后台下载由于 gray_load_mgr 中有控制并发数，目前 2~3 个。此处先屏蔽
					-- if downloadType ~= download_state.befor_login then
					-- 2021/1/21 此处及前面的 yield(0) 会造成下载停顿，特别是登录前的首包资源下载，会有明显的下载停顿感
					-- 	while (not AssetBundleManager.IsIdle()) do
					-- 		-- Debug.Log("IsNotIdle")
					-- 		coroutine.yield(1)
					-- 	end
					-- end

					table.insert(downloadingList, abname)
					-- 2021/1/21 下载中会在主线程同步创建大量文件夹和文件，会以造成明显的卡顿感，需要继续优化。e.g.后台下载从主线移除创建文件夹操作
					BgDownloadMgr.Instance:Request(abname)

				else
					if onEvent then
						onEvent("on_task_exist", abname)
					end
				end
			end
		end
		
		log.Warning("第"..redownloadTimes.."次下载完成，failCount："..failListCount..",finishCount："..finishCount..",totalCount: "..totalCount)
		isFinished = true
		hasRedownload = false
	end)
end

--[[检查是否有异常下载资源需要重新下载]]
function CheckRedownload()
	if isFinished == false then
		return
	end
	
	if failListCount <= 0 then
		return
	end

	-- 增加超时处理机制,这里c#传过来的状态可能有问题，导致数量对不上 5s之后如果还没有重新下载，跳过下面条件进行重试
	
	-- 1.0.36版本的C#才添加cancleTask接口
	if Application.platform == RuntimePlatform.Android and Utility.VersionIsHigher(Application.version, '1.0.35') then 
		if not redownloadTicker then
			redownloadTicker = util.DelayCall(timeout, function()
				if hasRedownload == false and isDispose == false then
					-- 取消没有回应的任务列表
					for i, assetbundleName in ipairs(downloadingList) do
						BgDownloadMgr.Instance:CancleTask(assetbundleName)
					end
				end
				if redownloadTicker then
					util.RemoveDelayCall(redownloadTicker)
					redownloadTicker = nil
				end
			end)
		end
	end
	if finishCount + failListCount < totalCount  then
		return
	end
	
	hasRedownload = true
	
	if redownloadTicker then
		util.RemoveDelayCall(redownloadTicker)
		redownloadTicker = nil
	end
	
	StartRedownload()
end

function StartRedownload()
	-- 下载完一次之后检测是否有失败，继续尝试下载
	if redownloadTimes < 3 then
		-- 延时重试
		redownloadTimes = redownloadTimes + 1
		--Debug.LogError("尝试重新下载:第"..redownloadTimes.."次, 错误数量："..failListCount)
		-- local properties = string.format("{\"redownloadTimes\":%d,\"failCount\":%d}", redownloadTimes, failListCount)
		local properties = {
			redownloadTimes=redownloadTimes,
			failCount=failListCount
		}
		Report("start_redownload", properties)
		--waitingList = failList
		--failList = {}
		--failListCount = 0
		-- 判断网络连接正常时 重试下载 减少失败率
		util.DelayCall(1, function()
			CheckPingNet()		
		end)			
	else
		if downloadFailFun then
			downloadFailFun()
		end
		if isDispose == false then
			Dispose()
		end		
		Report("downloading_fail")
		--Debug.LogError("后台下载失败")
	end
end

function Report(eName,obj)
	if (not eName) or (not obj) then
		return
	end
	local player_mgr = require "player_mgr"
    local playerProp = player_mgr.GetPlayerProp()

    local game_config 	= require "game_config"
    local q1sdk 	= require "q1sdk"
	local ui_login_main_mgr = require "ui_login_main_mgr"
    obj.pid = game_config.CHANNEL_ID
    obj.uuid = q1sdk.GetUUID()
    obj.downloadType = downloadType
    obj.level = playerProp and playerProp.lv
    obj.server_id = ui_login_main_mgr.GetLoginServerID()
    obj.role_id=player_mgr.GetPlayerRoleID()
    obj.since_start_time=Time.realtimeSinceStartup
    event.Trigger(event.GAME_EVENT_REPORT,eName, obj)
end

--[[检测是否可以ping服务器]]
function CheckPingNet()
	local pingCheck = GameObject.Find("/Engine"):GetComponent(typeof(PingCheck))
	if pingCheck then
		-- 检测30s内如果有网，尝试开始重连
		util.DelayCall(0, function()
			for i = 0, waitMaxTime do
				local pingData = pingCheck:GetPingData()
				if pingData.minPing > 0 and pingData.avgPing > 0 then
					break
				end
				coroutine.yield(1)
			end
			StartDownload()	
		end)
	else
		StartDownload()
	end	
end

--[[启动下载管理器之后要销毁，默认是会在下载完成自动销毁]]
function Dispose()
	print("download_mgr-dispose")

	
	if onEvent then
		-- local nowTime = os.clock()
			
			-- local percent = string.format("%d%%",finishCount/totalCount*100)
			-- local properties = {
			-- 	progress=percent,
			-- 	total_time=nowTime - startTime,
			-- 	finishCount=finishCount,
			-- 	totalCount=totalCount,
			-- }
		onEvent("on_task_dispose",properties)
		onEvent = nil
	end

	isDispose = true
	inited = false
	maxCount = 5
	waitMaxTime = 0
	totalCount = 0
	totalSize = 0
	finishCount = 0
	finishSize = 0
	beforeDownloadList = {}
	redownloadTimes = 0
	downloadingList = {}
	waitingList = {}
	--failList = {}
	failListCount = 0
	progressChangeFun = nil
	downloadFailFun = nil
	needWifi = false
	downloadType = download_state.befor_login
	startTime = nil
	lastTime = nil
	reportData =
	{
	["0%"] = 0,
	["10%"] = 0,
	["20%"] = 0,
	["30%"] = 0,
	["40%"] = 0,
	["50%"] = 0,
	["60%"] = 0,
	["70%"] = 0,
	["80%"] = 0,
	["90%"] = 0,
	["100%"] = 0,
	}
	if ticker then
		util.RemoveDelayCall(ticker)
		ticker = nil
	end
	if redownloadTicker then
		util.RemoveDelayCall(redownloadTicker)
		redownloadTicker = nil
	end

	if idle_ticker then
		util.RemoveDelayCall(idle_ticker)
		idle_ticker = nil
	end

	if checkList_ticker then
		util.RemoveDelayCall(checkList_ticker)
		checkList_ticker = nil
	end
	
	-- BgDownloadMgr.Instance:Dispose()
	BgDownloadMgr.Instance:UnRegisterDownloadErrorHandler(OnDownloadTaskError)
	BgDownloadMgr.Instance:UnRegisterDownloadFinishHandler(OnDownloadTaskFinish)
	BgDownloadMgr.Instance:UnRegisterDownloadCancleHandler(OnDownloadTaskCancle)
	local f,res = xpcall(function ()
        if BgDownloadMgr.ClearHttpSocketRepeatPool ~= nil then
            BgDownloadMgr.ClearHttpSocketRepeatPool()
        end
    end,debug.traceback)
    if not f then
        Debug.LogError(res)
    end   
end

function PropressChange()
	print("PropressChange()")
	util.DelayOneCallNoCoroutine("PropressChange",DelayPropressChange,0.1)
end

function DelayPropressChange()
	if progressChangeFun then
		-- progressChangeFun(finishCount, totalCount)
		progressChangeFun(finishCount,totalCount,finishSize, totalSize)
	else
		--OnDownloadProgressChanged(finishCount, totalCount)
	end
	
	-- 后台下载打点上报 进度百分比打点 每10%上报一次
	if totalCount > 0 then
		local percent = string.format("%d%%",finishCount/totalCount*100)
		if reportData[percent] and reportData[percent] == 0 then
			reportData[percent] = 1
			local nowTime = os.clock()
			local durationTime = lastTime and (nowTime - lastTime) or (startTime and (nowTime - startTime))
			lastTime = nowTime
			-- local properties = string.format("{\"durationTime\":%f,\"progress\":\"%s\",\"total_time\":%f,\"finishCount\":%d,\"totalCount\":%d}", durationTime, percent, nowTime - startTime, finishCount, totalCount)
			local properties = {
				durationTime=durationTime,
				progress=percent,
				total_time=nowTime - startTime,
				finishCount=finishCount,
				totalCount=totalCount,
				finishSize=finishSize,
				totalSize = totalSize,
			}
			--Debug.LogWarning("SetDownloadData:" .. percent.."properties:"..properties.."downloadType:"..downloadType)
			Report( downloadType, properties)
			--idle.PrintMM()
		end
	end
end

--[[后台下载进度变化]]
local window = nil
function OnDownloadProgressChanged(curNum, totalNum)
	local ui_downloading = require "ui_downloading"
	if totalNum > 0 and window == nil then
		window = ui_downloading.Show()
	end
 	
	ui_downloading.SetDownloadData(curNum, totalNum)
	if curNum >= totalNum then
		ui_downloading.Close()
		window = nil
	end
end

--[[后台下载任务失败]]
function OnDownloadTaskError(abname, url, errorCode, errorDes)
	if(downloadType == download_state.befor_login)then
		--登录前下载，失败直接算成功，跳过继续
		OnDownloadTaskFinish(abname)
	else
		local const = require "const"
		local game_config = require "game_config"
		if game_config.ENABLE_HTTPSOCKET_2_REWWW then
			--2021.1.4 赖嘉明：彭总要求全渠道打开
			--if util.GetChannelTag() == const.package_name_set.com_q1_hero_huawei then
			-- 针对俄罗斯地区 manifest 大量下载失败，目前仅华为包在俄罗斯地区放量。
			-- 后台下载使用的是 HttpSocket 方式下载，AssetBundle 将根据开关，来判定在 HttpSocket 失败情况下是否
			-- 使用 UnityWebRequest 方式下载。 后台下载流程目前不支持 WWW 方式，为快速验证，处理先放行，不做错误处理 2021/1/1
			-- log.Warning("url download failed，do not try again on huawei，jump to success")
			OnDownloadTaskFinish(abname)
			--end
		end

		-- if AssetBundleManager.RetryTimes then
		-- 	print("RetryTimes",abname)
		-- 	AssetBundleManager.RetryTimes(abname)
		-- end
		if redownloadTimes == 3 and url and errorCode then
			Debug.LogError("OnDownloadTaskError:abname:"..abname.." url:"..url.." errorCode:"..errorCode.." errorDes:"..errorDes)
		end
		if not waitingList[abname] then
			return
		end
		waitingList[abname] = enum_state.ERROR
		for i, assetbundleName in ipairs(downloadingList) do
			if assetbundleName == abname then
				table.remove(downloadingList, i)
				print("remove downloadingList",i)

				failtimes = failtimes + 1
				if failtimes > 3 then
					failtimes = 0
					if AssetBundleManager.NEXT_DNS then
						AssetBundleManager.NEXT_DNS()
						downloadingList = {}
					end
				end
				break
			end
		end
		--failList[abname] = enum_state.WAITING
		failListCount = failListCount + 1
		CheckRedownload()
	end
end

--[[后台下载任务完成]]
function OnDownloadTaskFinish(abname)
	-- print("OnDownloadTaskFinish",abname)

	if onEvent then
		onEvent("on_task_finish", abname)
	end
	if not waitingList[abname] then
		return
	end
	finishCount = finishCount + 1
	if downloadType == download_state.befor_login then
		-- 登陆前下载
		if not beforeDownloadList[abname] then
			beforeDownloadList[abname] = abname
			finishSize = finishSize + hashRemote:GetSize(abname)
		end
	end
	waitingList[abname] = nil
	for i, assetbundleName in ipairs(downloadingList) do
		if assetbundleName == abname then
			table.remove(downloadingList, i)
			break
		end
	end

	--Debug.Log("OnDownloadTaskFinish:abname"..abname.."finishCount:"..finishCount.."totalCount:"..totalCount)
	--CheckRedownload()
	DelayPropressChange()
	
	if totalCount <= finishCount and #downloadingList <= 0 and isDispose == false then
		--Debug.Log("OnDownloadTaskFinish:Dispose")
		Dispose()
	end
end

--[[取消下载中的任务]]
function OnDownloadTaskCancle(abname)
	-- print("OnDownloadTaskCancle",abname)
	if onEvent then
		onEvent("on_task_cancel", abname)
	end
	OnDownloadTaskFinish(abname)
end

--[[开启后台下载功能，新增，关卡1-5之后开启]]
function InitBackgroundDownload()
	local laymain_data = require "laymain_data"
	local mapLv = laymain_data.GetPassLevel()
	if mapLv < 5 or initedBD then
		return
	end
	-- InitDownloadMgr(nil, nil, nil, nil, download_state.background,5)
end
event.Register(event.CHANGE_PASS_LEVEL, InitBackgroundDownload)