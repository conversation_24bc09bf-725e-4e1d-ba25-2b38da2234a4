local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local time_util = require "time_util"
local common_pb = require "common_pb"
local table_util = require "table_util"
local arena_common_mgr = require "arena_common_mgr"
local event_arena_common_define = require "event_arena_common_define"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_arena_battle_history_controller")
local controller = nil
local UIController = newClass("ui_arena_battle_history_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    self.recordIndex = 20
    self.arenaType = data.arenaType
    --第一次进界面参数一定是 1
    self.pageIndex = 1
    arena_common_mgr.RequestChallengeRecord(self.arenaType, self.pageIndex)
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    self.OnGetBattleRecordsRsp = function(msg)
        --解析字符串
        self:GetBattleRecordsRsp(msg)
    end
    self:RegisterEvent(event_arena_common_define.TMSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP, self.OnGetBattleRecordsRsp)
end

function UIController:AutoUnsubscribeEvents() 
    
end


--endregion

--region Controller Logic
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end

--解析字符串方法
function UIController:GetBattleRecordsRsp(msg)
    if not self.recordData then
        self.recordData = {}
    end
    local ncInfo = common_pb.TBattleReportMailInfo()
    ncInfo:ParseFromString(msg.records)
    for _, v in ipairs(ncInfo.arrBattleReportMailInfo) do
        local info = {
            battleID = v.roundInfo.battleID,
            time = time_util.TicksToTimeStr(v.nTimeStamp),
            score = v.nScoreChange,
            name = v.defInfo.userName,
            frameID = v.defInfo.faceFrameId,
        }
        info.faceStr = v.defInfo.faceId
        if v.defInfo.faceStr and not string.IsNullOrEmpty(v.defInfo.faceStr) then
            info.faceStr = v.defInfo.faceStr
        end
        
        info.ReplayClick = function(index, data)
            local battle_report_ext_helper = require "battle_report_ext_helper"
            battle_report_ext_helper.TryShowBattleReportPanel(data.battleID,battle_report_ext_helper.E_BattleTypeExt.Arena)
        end
        table.insert(self.recordData,info)
    end

    if self.recordIndex ~= 20 then
        self:CustomRecordList(self.recordData)
    end
    self:TriggerUIEvent("SetListData", self.recordData)
end

function UIController:CustomRecordList(data_table)
    if not data_table then
        data_table = {}
    end
    function GetItem(t,k)
        --这里去实现自己对应的数据获取方式和来源      
        --就是dataItem吧
        local info ={}
        return data_table[k]
    end
    --注意 只有你想要动态获取item的值时才需要设置，如果list很短，其实没必要，直接全部传过去
    table_util.SetDynamicGetItem(data_table,GetItem)
    return data_table
end

function UIController:OnScrollRectItemRender(index, dataItem)
    --不能超过500条
    if index == self.recordIndex and self.recordIndex < 500 then
        self.recordIndex = self.recordIndex + 20
        self.pageIndex = self.pageIndex + 1
        arena_common_mgr.RequestChallengeRecord(self.arenaType, self.pageIndex)
    end
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
