local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local game_scheme = require "game_scheme"
local net_recharge_module = require "net_recharge_module"
local gw_recharge_mgr = require "gw_recharge_mgr"
local event = require "event"
local reward_mgr = require "reward_mgr"
local halloween_slot_machine_mgr = require "halloween_slot_machine_mgr"
local halloween_activity_slot_machine_const = require "halloween_activity_slot_machine_const"
local skep_mgr = require "skep_mgr"
local log = require "log"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"


--region Controller Life
module("ui_halloween_daily_gift_controller")
local controller = nil
local UIController = newClass("ui_halloween_daily_gift_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    
    self.rechargeDataArr = {}   -- 充值数组
end

function UIController:OnShow()
    self.__base.OnShow(self)
    event.EventReport("GearSupply_Pack_Enter", {}) -- 进入活动界面打点

    self:SetRechargeData()
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    local UpdateRecharge = function()
        self:UpdateRechargeList()
    end
    
    local UpdateBase = function()
        self:UpdateRechargeList()
    end
    self.onGoodsNumChange = function()
        self:UpdateRechargeList()
    end
    
    self:RegisterEvent(event.GW_REFRESH_RECHARGE_GOODS, UpdateRecharge)     --礼包数据刷新
    self:RegisterEvent(event.GW_GET_RECHARGE_GOODS, UpdateBase)             --充值物品获得通知
    self:RegisterEvent(event.UPDATE_GOODS_NUM_CHANGE, self.onGoodsNumChange)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
-- 排序
function UIController:SortCharegeArray(array)
    table.sort(array, function(a, b)
        local aSpecial = a.isSpecial and 1 or 0
        local bSpecial = b.isSpecial and 1 or 0
        local aCanBuy = a.canBuyNum > 0 and 1 or 0
        local bCanBuy = b.canBuyNum > 0 and 1 or 0
        local aIsFree = a.isFree and 1 or 0
        local bIsFree = b.isFree and 1 or 0

        if aSpecial ~= bSpecial then
            return aSpecial > bSpecial
        else
            if aCanBuy ~= bCanBuy then
                return aCanBuy > bCanBuy
            else
                if aIsFree ~= bIsFree then
                    return aIsFree > bIsFree
                else
                    return a.actContetID < b.actContetID
                end
            end
        end
    end)
end

-- 设置礼包数据
function UIController:SetRechargeData()
    self.rechargeDataArr = {}
    
    local actID = halloween_slot_machine_mgr.GetDailyGiftActivityID()
    local coinID = halloween_slot_machine_mgr.GetCoinID()
    local coinCount = skep_mgr:GetGoodsNum(coinID)
    local iconCfg = game_scheme:Item_0(coinID)
    if not iconCfg then
        log.Warning("iconCfg is nil")
    end
    
    local festivalCfg = game_scheme:festivalActivity_0(actID)
    if festivalCfg and festivalCfg.ctnID1 then
        local contentIDArr = festivalCfg.ctnID1.data
        for i = 0, festivalCfg.ctnID1.count - 1 do
            local actContentCfg = game_scheme:ActivityContent_0(contentIDArr[i])
            if actContentCfg then
                local rechargeData = {}
                rechargeData.actContetID = contentIDArr[i]
                rechargeData.rechargeGoodsID = actContentCfg.rechargeID.data[0]
                rechargeData.limitNum = actContentCfg.LimitNumber
                local rechargeGoodsCfg = game_scheme:RechargeGoods_0(rechargeData.rechargeGoodsID)
                if rechargeGoodsCfg then
                    rechargeData.rechargeGoodsCfg = rechargeGoodsCfg
                    rechargeData.nameLangID = rechargeGoodsCfg.strGoodsNameID.data[0]
                    rechargeData.isFree = rechargeGoodsCfg.iPrice == 0  -- 是否免费
                    rechargeData.priceText = net_recharge_module.GetMoneyStrByGoodsID(rechargeData.rechargeGoodsID)  --充值价格
                    if iconCfg then
                        rechargeData.CoinIcon = iconCfg.icon
                    end
                    rechargeData.canBuyNum = rechargeData.limitNum - gw_recharge_mgr.GetRechargeBuyCount(rechargeData.rechargeGoodsID)        --可购买次数
                    rechargeData.hasGet = rechargeData.canBuyNum == 0 and rechargeData.isFree
                    rechargeData.rewardList = {}
                    for i = 0, rechargeGoodsCfg.rewardNew.count - 1 do
                        local rewardId = rechargeGoodsCfg.rewardNew.data[i]
                        reward_mgr.GetRewardGoodsList(rewardId, rechargeData.rewardList)
                    end

                    -- 购买回调
                    rechargeData.BuyRechargeFun = function(recharge)
                        local canBuyNum = recharge.limitNum - gw_recharge_mgr.GetRechargeBuyCount(recharge.rechargeGoodsID)
                        if canBuyNum > 0 then
                            gw_recharge_mgr.RequestBuyRecharge(recharge.rechargeGoodsID, recharge.actContetID)
                        end
                    end
                    
                    -- 领取回调
                    rechargeData.GetRechargeFun = function(recharge)
                        local canBuyNum = recharge.limitNum - gw_recharge_mgr.GetRechargeBuyCount(recharge.rechargeGoodsID)
                        if canBuyNum > 0 then
                            -- 暂时屏蔽，不知道啥意思
                            --if self.enterModule == "MiracleBox" then
                            --    event.EventReport("MiracleBox_FreePack", {ActivityContent_id = recharge.actContetID})
                            --end
                            gw_recharge_mgr.RequestGetRecharge(recharge.rechargeGoodsID, recharge.actContetID)
                        end
                    end
                    
                    table.insert(self.rechargeDataArr, rechargeData)
                else
                    log.Error("rechargeGoodsCfg is nil, rechargeGoodsId = " .. rechargeData.rechargeGoodsID)
                end
            else
                log.Error("actContentCfg is nil, activityContentId = " .. contentIDArr[i])
            end
        end
    else
        log.Error("festivalCfg is nil, activityId = " .. actID)
    end
    
    -- 排序
    self:SortCharegeArray(self.rechargeDataArr)
    
    -- 添加特殊按钮
    local data = {isSpecial = true}
    self.clickSpecial = function()
        local festival_activity_cfg = require "festival_activity_cfg"

        local actCfg = festival_activity_cfg.GetActivityMainCfgByActivityID(halloween_activity_slot_machine_const.act_id.pass)
        if actCfg then
            local festival_activity_mgr = require "festival_activity_mgr"
            festival_activity_mgr.OpenActivityUIByEntranceID(actCfg.AtyEntrance, actCfg.AtyID)

            self:OnBtnCloseBtnClickedProxy()
        end
    end
    
    self.clickKey = function()
        iui_item_detail.Show(coinID, nil, item_data.Item_Show_Type_Enum.Reward_Interface, coinCount)
    end
    
    table.insert(self.rechargeDataArr, 1, data)
    self:TriggerUIEvent("InitRechargeList", self.rechargeDataArr, self.clickSpecial, self.clickKey)
    self:TriggerUIEvent("UpdateBase", coinCount)
end

-- 刷新礼包数据
function UIController:UpdateRechargeList()
    for i, rechargeData in ipairs(self.rechargeDataArr) do
        if rechargeData and not rechargeData.isSpecial then
            rechargeData.canBuyNum = rechargeData.limitNum - gw_recharge_mgr.GetRechargeBuyCount(rechargeData.rechargeGoodsID)        --可购买次数
            rechargeData.hasGet = rechargeData.canBuyNum == 0 and rechargeData.isFree
        end
    end

    local coinID = halloween_slot_machine_mgr.GetCoinID()
    local coinCount = skep_mgr:GetGoodsNum(coinID)
    
    self:TriggerUIEvent("UpdateRechargeList", self.rechargeDataArr)
    self:TriggerUIEvent("UpdateBase", coinCount)
end


function UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion