local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local net_activity_module = require "net_activity_module"
local player_mgr = require "player_mgr"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_goods_submit_controller")
local controller = nil
local UIController = newClass("ui_goods_submit_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)

    self.submitData = data or {}
    self.submitData.itemID = data.itemID or 8041 --物品ID
    self.submitData.submitMinCount = data.submitMinCount or 10 --提交的最小数量
    self.submitData.preReward  = data.preReward --奖励预览
    
    self.submitData.titleStr = data.titleStr or lang.Get(1009297) --标题
    self.submitData.tipStr = data.tipStr or lang.Get(1009305) --提示
    self.submitData.btnTextStr = data.btnTextStr or lang.Get(7121) --按钮文本
    
    self:InitViewData()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
end

function UIController:AutoUnsubscribeEvents()
end
--endregion

--region Controller Logic
function UIController:OnBtnCloseBtnClickedProxy()
    ui_window_mgr:UnloadModule(self.view_name)
end
function UIController:OnSliderNumSliderValueChange(value)
end
function UIController:OnBtnMinusClickedProxy()
end
function UIController:OnBtnAddClickedProxy()
end
function UIController:OnInputNumInputValueChange(text)
end
function UIController:OnInputNumInputEndEdit(text)
end
function UIController:OnBtnOkClickedProxy()
    if self.submitData.submitMinCount > self.submitData.haveItemCount then
        return
    end
    
    local msgData = {
        nActivityID = 3014,--TODO先写死
        itemId = self.submitData.itemID,
        nCount = 10,--TODO先写死
    }
    net_activity_module.MSG_ACT_RANK_SUBMIT_REQ(msgData)
end

---@public function 获取拥有活动道具数量
function UIController:GetHaveItemNum()
    local sid = player_mgr.GetGoodsSidById(self.submitData.itemID)
    local itemCount = 0
    if sid and sid >0 then
        itemCount = player_mgr.GetGoodsNumberBySid(sid)
    end
    return itemCount
end

function UIController:InitViewData()
    local haveItemCount = self:GetHaveItemNum()
    self.submitData.haveItemCount = haveItemCount
    self:TriggerUIEvent("ShowGoodsSubmitPanel", self.submitData)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
