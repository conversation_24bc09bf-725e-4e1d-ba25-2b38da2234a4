-- battle_event_repot.txt ------------------------------------------
-- author:  梁骐显
-- date:    2022.2.14
-- ver:     1.0
-- desc:    战斗上报功能专用模块
--------------------------------------------------------------
local require = require
local string = string
local tostring = tostring
local pairs = pairs
local ipairs = ipairs
local table = table
local math = math
local print = print
local type = type
local PlayerPrefs         = CS.UnityEngine.PlayerPrefs
module("battle_event_report")
local event                   = require "event"
local battle_data             = require "battle_data"
local json                    = require "dkjson"
local weapon_data             = require "weapon_data"
local game_scheme             = require "game_scheme"
local player_mgr              = require "player_mgr"
-- local space_gap_mgr         = require "space_gap_mgr"
local common_pb               = require "common_new_pb"
local net_arena_module = require "net_arena_module"
-- local domination_mgr          = require "domination_mgr"
--========================Common report==========================
function ReportBossGuildInfo(bossid, power,totalDamage,bosshp,battleID)
    local isWin = battle_data.victory
    -- 这里如果直接跳过战斗，回合数是0
    local heroData = battle_data.GetHeros()
    local roundTimes, heroids, herolevels, herostars, pos, talents = GetBattleInfo(heroData, true)
    
    local weaponID = 0
    local weaponLevel = 0
    
    if heroData[13] then
        -- 是武器
        weaponID = heroData[13].heroID == 80000 and 1 or 2
        local weaponData = weapon_data.GetWeaponByID(weaponID)
        if weaponData and weaponData.part and #weaponData.part >= 1 then
            local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
            weaponLevel = weaponCfg.weaponLv
        end

    end

    local roleName = tostring(player_mgr.GetRoleName()) or ""
    local json_str = json.encode({
        boss_id = bossid,
        power_team = power,
        battle_Damage = totalDamage,
        bosshp_later = bosshp,
        isWin = isWin,
        battle_round = roundTimes,
        hero_id_new = table.concat(heroids, '#'),
        hero_level_new = table.concat(herolevels, '#'),
        hero_star_new = table.concat(herostars, '#'),
        Location_num = table.concat(pos,'#'),
        battle_id = battleID or 0,
        weapon_id = weaponID,
        weapon_level = weaponLevel,
        role_name = roleName,
        hero_talent = talents
    })
    event.Trigger(event.GAME_EVENT_REPORT, "boss_guild", json_str)
end

function ReportInstanceMultiBattleInfo(level,battleList,battleID)
--     local instance_mgr = require "instance_mgr"

--     local isWin = battle_data.victory
--     -- 这里如果直接跳过战斗，回合数是0
--     local roundTimes = battle_data.roundTimes
--     local heroList = {}
--     local weaponList = {}
--     local equipids = {}
--     local equipqualitys = {}
--     local equipsums = {}
--     local Exmaterialdrop= {}
--     local Exmaterialdrop_Num = {}
--    -- local battle_data = require "battle_data"
--     local rewards = battle_data.lastRewards
--     local topLv = instance_mgr.GetServerUnlockLvByType(instance_mgr.instance_type.exclusive)
--     local checkLv = instance_mgr.GetConfigUnlockLvByType(instance_mgr.instance_type.exclusive)
--     for i=1,3 do
--         local v = battleList[2][i]
--         local temp = {}
--         temp.weaponID = 0
--         temp.weaponLevel = 0
--         if v ~= 0 then
--             temp.weaponID = v
--             local weaponData = weapon_data.GetWeaponByID(v)
--             if weaponData.part then
--                 local weaponCfg = game_scheme:MagicWeapon_0(v,1,weaponData.part[1].nLv,0)
--                 temp.weaponLevel = weaponCfg.weaponLv
--             end
--         end
--         table.insert(weaponList,temp)
--     end
--     for i=1,3 do
--         local v = battleList[1][i]
--         local temp = {}
--         temp.heroids = {}
--         temp.herolevels = {}
--         temp.herostars = {}
--         temp.pos = {}
--         temp.teamPower = 0
--         if v then
--             local roundTimes
--             roundTimes, temp.heroids, temp.herolevels, temp.herostars, temp.pos = GetBattleInfo(v)

--         end
--         table.insert(heroList, temp)
--     end
--     if rewards then
--         for i = 1, #rewards do
--             local reward = rewards[i]
--             local id = reward.id
--             local num = reward.num
--             table.insert(Exmaterialdrop, id)
--             table.insert(Exmaterialdrop_Num, num)
--         end
--     end

--     local ranking = net_arena_module.GetArenaRanking()
--     local dungeon_id_max = math.max(topLv, checkLv)
--     local roleName = tostring(player_mgr.GetRoleName()) or ""
--     local json_str = json.encode({
--         dungeon_id = level,
--         isWin = isWin,
--         power_team = tostring(heroList[1].teamPower),
--         power_team2 = tostring(heroList[2].teamPower),
--         power_team3 = tostring(heroList[3].teamPower),
--         battle_round = roundTimes,
--         hero_id_new = table.concat(heroList[1].heroids, '#'),
--         hero_id_new2 = table.concat(heroList[2].heroids, '#'),
--         hero_id_new3 = table.concat(heroList[3].heroids, '#'),
--         hero_level_new = table.concat(heroList[1].herolevels, '#'),
--         hero_level_new2 = table.concat(heroList[2].herolevels, '#'),
--         hero_level_new3 = table.concat(heroList[3].herolevels, '#'),
--         hero_star_new = table.concat(heroList[1].herostars, '#'),
--         hero_star_new2 = table.concat(heroList[2].herostars, '#'),
--         hero_star_new3 = table.concat(heroList[3].herostars, '#'),
--         Location_num = table.concat(heroList[1].pos,'#'),
--         Location_num2 = table.concat(heroList[2].pos,'#'),
--         Location_num3 = table.concat(heroList[3].pos,'#'),
--         battle_id = battleID or 0,
--         weapon_id = weaponList[1].weaponID,
--         weapon_id2 = weaponList[2].weaponID,
--         weapon_id3 = weaponList[3].weaponID,
--         weapon_level = weaponList[1].weaponLevel,
--         weapon_level2 = weaponList[2].weaponLevel,
--         weapon_level3 = weaponList[3].weaponLevel,
--         role_name = roleName,
--         equip_id_new = table.concat(equipids, '#'),
--         equip_quality = table.concat(equipqualitys, '#'),
--         equip_sum = table.concat(equipsums, '#'),
--         dungeon_id_Max = dungeon_id_max,
--         arena_rank = ranking,
--         Exmaterialdrop = Exmaterialdrop,
--         Exmaterialdrop_Num = Exmaterialdrop_Num,
--     })
--     event.Trigger(event.GAME_EVENT_REPORT, "equipDungeon_battle", json_str)
--     print(">>>>>>>>>>打点事件equipDungeon_battle", json_str)
end

function GetBattleInfo(heroData, isNeedTalentSkills)
    local roundTimes = battle_data.roundTimes
    
    local heroids = {}
    local herolevels = {}
    local herostars = {}
    local talents = ""
    local pos = {}

    for i = 0, 5 do
        local data = heroData[i]
        if data then
            table.insert(heroids, data.heroID)
            table.insert(herolevels, data.numProp.lv)
            table.insert(herostars, data.numProp.starLv)
            table.insert(pos, data.pos)
            if isNeedTalentSkills and data.talentSkills then
                local t = {}
                for k,v in pairs(data.talentSkills)do
                    if type(v) == "number" then
                        table.insert(t,'#')
                        table.insert(t,v)
                        -- talents = talents .. '#' .. v
                    elseif type(v) == "table" and v.talentID ~= nil then
                        table.insert(t,'#')
                        table.insert(t,v.talentID)
                        -- talents = talents .. '#' .. v.talentID
                    end
                end
                table.insert(t,';')
                talents = table.concat(t)
            end
        else
            table.insert(heroids, 0)
            table.insert(herolevels, 0)
            table.insert(herostars, 0)
            table.insert(pos,0)
        end
    end

    return roundTimes, heroids, herolevels, herostars, pos, talents
end

function NewHeroActivityTreasurePoltReort(lvID,victory,lastselectedHero)
    -- 这里如果直接跳过战斗，回合数是0
    local roundTimes,heroids = GetBattleInfo(lastselectedHero)
    local json_str = json.encode({
        dungeon = lvID,
        iswin = victory and 1 or 2,
        battle_round = roundTimes,
        hero_id_new = table.concat(heroids, '#'),
    })
    event.Trigger(event.GAME_EVENT_REPORT, "NweHeroActivity_treasure_plot_battle", json_str)
end

function SpaceGapBattleReport(lvID,victory,lastselectedHero)
    -- 这里如果直接跳过战斗，回合数是0
    -- local maxID = space_gap_mgr.GetMaxStageID()
    -- local isPass = space_gap_mgr.IsPassStage(lvID)
    -- local roundTimes,heroids = GetBattleInfo(lastselectedHero)
    -- local json_str = json.encode({
    --     SpaceGap_id = lvID,
    --     SpaceGap_id_Max = maxID,
    --     iswin = victory == 1 or 2,
    --     battle_round = roundTimes,
    --     hero_id_new = table.concat(heroids, '#'),
    --     frist_pass = isPass == nil,
    -- })
    -- event.Trigger(event.GAME_EVENT_REPORT, "SpaceGap_battle", json_str)
end

function HeroPathEnterReport(levelID)
    local roleName = tostring(player_mgr.GetRoleName()) or ""
    local json_str = json.encode({
        level_id = table.concat(levelID,"#"),
        role_name = roleName
    })
    --print("进入关卡时触发json_str",json_str)
    event.Trigger(event.GAME_EVENT_REPORT, "heropath_enter", json_str)
end

function ReportSecretPlaceBattleInfo(teamPower,level,heroData,battleID)
    -- local isWin = battle_data.victory
    -- -- 这里如果直接跳过战斗，回合数是0

    -- local roundTimes, heroids, herolevels, herostars, pos = GetBattleInfo(heroData)

    -- local weaponID = 0
    -- local weaponLevel = 0
    -- local relicIDs = {}
    -- local secretplace_mgr = require "secretplace_mgr"
    -- local arrItemData = secretplace_mgr.GetRelicIDs()
    -- for k,v in pairs(arrItemData) do
    --     table.insert(relicIDs, v)
    -- end

    -- local ranking = net_arena_module.GetArenaRanking()
    -- local roleName = tostring(player_mgr.GetRoleName()) or ""
    -- local levelID = {}
    -- table.insert(levelID,level)
    -- local battle_series_manager = require "battle_series_manager"
    -- local wave = battle_series_manager.GetLastWave()
    -- table.insert(levelID,wave)
    -- local json_str = json.encode({
    --     level_id = level,
    --     level_wave_id = table.concat(levelID,"#"),
    --     iswin = isWin,
    --     power_team = teamPower,
    --     battle_round = roundTimes,
    --     hero_id_new = table.concat(heroids, '#'),
    --     hero_level_new = table.concat(herolevels, '#'),
    --     hero_star_new = table.concat(herostars, '#'),
    --     Location_num = table.concat(pos,'#'),
    --     battle_id = battleID or 0,
    --     weapon_id = weaponID,
    --     weapon_level = weaponLevel,
    --     role_name = roleName,
    --     arena_rank = ranking,
    --     treasureid = table.concat(relicIDs,'#')--选择遗物id
    -- })
    -- --print("关卡战斗结束时触发json_str",json_str)
    -- if common_pb.MultiKillingTower then
    --     local factionID = secretplace_mgr.GetCurCampid()
    --     local mimeData = secretplace_mgr.GetMyRankData(factionID)
    --     local rank= mimeData and mimeData.rank or 0
    --     local new_jeson_str = json.encode({
    --         ranking = rank,
    --         FactionID = factionID,
    --     })
    --     event.Trigger(event.GAME_EVENT_REPORT,"FactionRelam_end",new_jeson_str)
    -- end
    -- event.Trigger(event.GAME_EVENT_REPORT, "heropath_info", json_str)
end

function VoidRingFightAddData(eventName,voidData,enemyData,battleID)
    local void_ring_data = require "void_ring_data"
    --打点

    local heroData = void_ring_data.GetHeroFormation()
    local roundTimes, heroids, herolevels, herostars, pos = GetBattleInfo(heroData)

    local weaponID = 0
    local weaponLevel = 0
    weaponID = weapon_data.GetLocalizedWeaponData(common_pb.VoidArena)
    local weaponData = weapon_data.GetWeaponByID(weaponID)
    if weaponData and weaponData.part then
        local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
        weaponLevel = weaponCfg.weaponLv
    end

    local ranking = void_ring_data.GetAloneRank()
    local enemyStr = ""
    for i=0,5 do
        enemyStr = enemyStr .. enemyData[i].numProp.lv.."#"
    end
    local property = {
        VoidArenaID = voidData.cfg.VoidID,
        hero_id_new = table.concat(heroids, '#'),
        hero_level_new = table.concat(herolevels, '#'),
        hero_star_new = table.concat(herostars, '#'),
        boss_lv = enemyStr,
        weapon_id = weaponID,
        weapon_level = weaponLevel,
        battle_id = battleID or 0,
        Location_num = table.concat(pos,'#'),
        arena_rank = ranking,
    }
    --打点数据上报
    event.Trigger(event.GAME_EVENT_REPORT, eventName, property)
end

function MazeEnterReport(row)
    -- local maze_mgr = require "maze_mgr"
    -- local curId = maze_mgr.GetConfig() and maze_mgr.GetConfig().ID or 0
    -- local roleName = tostring(player_mgr.GetRoleName()) or ""
    -- local json_str = json.encode({
    --     mazerefresh_id_new = curId,
    --     event_type_new = 1,
    --     mazerefresh_row_new = row,
    --     role_name_new = roleName
    -- })
    -- event.Trigger(event.GAME_EVENT_REPORT, "maze_enter", json_str)
end

--类似ReportBossGuildInfo
function MazeCompletedReport(heroData,curId,victory,teamPower,row,roleName,battleID)
    -- 这里如果直接跳过战斗，回合数是0

    local roundTimes, heroids, herolevels, herostars, pos, talents = GetBattleInfo(heroData, true)

    local weaponID = 0
    local weaponLevel = 0
    if heroData[13] then
        -- 是武器
        weaponID = heroData[13].heroID == 80000 and 1 or 2
        local weaponData = weapon_data.GetWeaponByID(weaponID)
        if weaponData and weaponData.part and #weaponData.part >= 1 then
            local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
            weaponLevel = weaponCfg.weaponLv
        end
    end

    local ranking = net_arena_module.GetArenaRanking()
    local json_str = json.encode({
        mazerefresh_id = curId,
        event_type_new = 1,
        parameter_new = victory == true and 1 or 2,
        team_power_new = teamPower,
        mazerefresh_row = row,
        role_name_new = roleName,
        battle_round = roundTimes,
        hero_id_new = table.concat(heroids, '#'),
        hero_level_new = table.concat(herolevels, '#'),
        hero_star_new = table.concat(herostars, '#'),
        weapon_id = weaponID,
        weapon_level = weaponLevel,
        battle_id = battleID or 0,
        arena_rank = ranking,
        hero_talent = talents
    })
    event.Trigger(event.GAME_EVENT_REPORT, "maze_completed", json_str)
end

function ArenaEnterCommonReport(arenaType)
    local str = "{\"arena_type\":%d,\"match_point\":%d}"
    local scores = net_arena_module.GetArenaScores(arenaType)
    local msg = string.format(str,arenaType,scores)
    event.Trigger(event.GAME_EVENT_REPORT, "arena_enter",msg)
end

function ArenaCompletedReportlocal(isWin,arenaType)
    local str = "{\"arena_type\":%d,\"match_point\":%d}"
    local scores = net_arena_module.GetArenaScores(arenaType)
    if arenaType ~= 1 then
        scores = net_arena_module.MineGradeId[arenaType] or 0
    end
    local msg = string.format(str,arenaType,scores)
    if isWin then
        event.Trigger(event.GAME_EVENT_REPORT, "arena_win", msg)
    else
        event.Trigger(event.GAME_EVENT_REPORT, "arena_lost", msg)
    end
end

function ReportInstanceBattleInfo(_type, level, teamPower,battleID)
    -- local isWin = battle_data.victory
    -- -- 这里如果直接跳过战斗，回合数是0
    -- local heroData = battle_data.GetHeros()
    -- local roundTimes, heroids, herolevels, herostars, pos = GetBattleInfo(heroData)

    -- local equipids = {}
    -- local equipqualitys = {}
    -- local equipsums = {}
    -- local runes= {}
    -- local weaponID = 0
    -- local weaponLevel = 0
    -- local rewards = battle_data.rewards
    -- local instance_mgr = require "instance_mgr"
    -- if rewards then
    --     for i = 1, #rewards do
    --         local reward = rewards[i]
    --         local id = reward.id
    --         if _type == instance_mgr.instance_type.equipment then 
    --             local equip_cfg = game_scheme:Equipment_0(id)
    --             if equip_cfg then
    --                 local num = reward.num
    --                 local grade = equip_cfg.grade
    --                 table.insert(equipids, id)
    --                 table.insert(equipqualitys, grade)
    --                 table.insert(equipsums, num)
    --             end
    --         elseif instance_mgr.IsRuneInstance(_type) then
    --             --符文材料判断
    --             if id >= 62001 and id <= 69900 then
    --                 table.insert(runes, id)
    --             end
    --         end
    --     end
    -- end
    -- if heroData[13] then
    --     -- 是武器
    --     weaponID = heroData[13].heroID == 80000 and 1 or 2
    --     local weaponData = weapon_data.GetWeaponByID(weaponID)
    --     if weaponData and weaponData.part and #weaponData.part >= 1 then
    --         local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
    --         weaponLevel = weaponCfg.weaponLv
    --     end
    -- end
    
    -- local ranking = net_arena_module.GetArenaRanking()
    -- local instance_mgr = require "instance_mgr"
    -- local dungeon_id = instance_mgr.GetPassLvByType(_type)
    -- local roleName = tostring(player_mgr.GetRoleName()) or ""
    -- local json_str = json.encode({
    --     dungeon_id = level,
    --     isWin = isWin,
    --     power_team = teamPower,
    --     battle_round = roundTimes,
    --     hero_id_new = table.concat(heroids, '#'),
    --     hero_level_new = table.concat(herolevels, '#'),
    --     hero_star_new = table.concat(herostars, '#'),
    --     Location_num = table.concat(pos,'#'),
    --     battle_id = battleID or 0,
    --     weapon_id = weaponID,
    --     weapon_level = weaponLevel,
    --     role_name = roleName,
    --     equip_id_new = table.concat(equipids, '#'),
    --     equip_quality = table.concat(equipqualitys, '#'),
    --     equip_sum = table.concat(equipsums, '#'),
    --     dungeon_id_Max = dungeon_id,
    --     arena_rank = ranking,
    --     sigil_id = table.concat(runes, '#'),
    -- })
    -- event.Trigger(event.GAME_EVENT_REPORT, "equipDungeon_battle", json_str)
    -- --print("打点事件equipDungeon_battle", json_str)
end

function ReportillusionBattleInfo(teamPower,level,heroData,battleID)
    local isWin = battle_data.victory
    -- 这里如果直接跳过战斗，回合数是0
    local roundTimes, heroids, herolevels, herostars, pos, talents = GetBattleInfo(heroData, true)

    local weaponID = 0
    local weaponLevel = 0

    if heroData[13] then
        -- 是武器
        weaponID = heroData[13].heroID == 80000 and 1 or 2
        local weaponData = weapon_data.GetWeaponByID(weaponID)
        if weaponData and weaponData.part and #weaponData.part >= 1 then
            local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
            weaponLevel = weaponCfg.weaponLv
        end
    end

    local ranking = net_arena_module.GetArenaRanking()
    local roleName = tostring(player_mgr.GetRoleName()) or ""
    local json_str = json.encode({
        dungeon_id = level,
        isWin = isWin,
        power_team = teamPower,
        battle_round = roundTimes,
        hero_id_new = table.concat(heroids, '#'),
        hero_level_new = table.concat(herolevels, '#'),
        hero_star_new = table.concat(herostars, '#'),
        Location_num = table.concat(pos,'#'),
        battle_id = battleID or 0,
        weapon_id = weaponID,
        weapon_level = weaponLevel,
        role_name = roleName,
        arena_rank = ranking,
        hero_talent = talents
    })
    event.Trigger(event.GAME_EVENT_REPORT, "illusion_info", json_str)
end

function ReportMainStoryBattleInfo(grind,lv,battleID)
    local isWin = (grind and grind == 1) and true or battle_data.victory
    -- 这里如果直接跳过战斗，回合数是0
    local hook_hero_data = require "hook_hero_data"
    local heroData = hook_hero_data.GetSaveHeroData()
    local roundTimes, heroids, herolevels, herostars, pos, talents = GetBattleInfo(heroData, true)
    roundTimes = (grind and grind == 1) and 0 or roundTimes

    local weaponID = 0
    local weaponLevel = 0
    local gw_hero_mgr = require "gw_hero_mgr"
    local teamPower = gw_hero_mgr.GetHeroesPower(heroData)
    
    if heroData[13] then
        -- 是武器
        weaponID = heroData[13].heroID == 80000 and 1 or 2
        local weaponData = weapon_data.GetWeaponByID(weaponID)
        if weaponData and weaponData.part and #weaponData.part >= 1 then
            local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
            weaponLevel = weaponCfg.weaponLv
        end
    end

    local ranking = net_arena_module.GetArenaRanking()
    local roleName = tostring(player_mgr.GetRoleName()) or ""
    local isGrind = grind or 0
    local json_str = json.encode({
        dungeon_id = (grind and grind == 1) and lv or battle_data.stageLv,
        power_team = teamPower,
        isWin = isWin,
        battle_round = roundTimes,
        hero_id_new = table.concat(heroids, '#'),
        hero_level_new = table.concat(herolevels, '#'),
        hero_star_new = table.concat(herostars, '#'),
        Location_num = table.concat(pos,'#'),
        battle_id = battleID or 0,
        weapon_id = weaponID,
        weapon_level = weaponLevel,
        role_name = roleName,
        arena_rank = ranking,
        battle_type = tostring(isGrind),
        hero_talent = talents
    })
    event.Trigger(event.GAME_EVENT_REPORT, "hook_info", json_str)
end

function ReportPeakBattleInfo(heroData,battleID)
    -- 这里如果直接跳过战斗，回合数是0
    -- local roundTimes, heroids, herolevels, herostars, pos, talents = GetBattleInfo(heroData, true)
    -- local weaponID = 0
    -- local weaponLevel = 0

    
    -- if heroData[13] then
    --     -- 是武器
    --     weaponID = heroData[13].heroID == 80000 and 1 or 2
    --     local weaponData = weapon_data.GetWeaponByID(weaponID)
    --     if weaponData and weaponData.part and #weaponData.part >= 1 then
    --         local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
    --         weaponLevel = weaponCfg.weaponLv
    --     end
    -- end
    -- local peak_mgr = require "peak_mgr"
    -- local _, levelid = peak_mgr.GetCurBe()
    
    -- local ranking = net_arena_module.GetArenaRanking()
    -- local json_str = json.encode({
    --     Level_id = levelid,
    --     power_team = teamPower,
    --     battle_round = roundTimes,
    --     hero_id_new = table.concat(heroids, '#'),
    --     hero_level_new = table.concat(herolevels, '#'),
    --     hero_star_new = table.concat(herostars, '#'),
    --     battle_id = battleID or 0,
    --     weapon_id = weaponID,
    --     weapon_level = weaponLevel,
    --     arena_rank = ranking,
    --     hero_talent = talents
    -- })
    -- event.Trigger(event.GAME_EVENT_REPORT, "PeakOfTime_battle", json_str)
end

--[[上报战斗的相关数据]]
function ReportBattleInfo(battleType, arenaType)
	local isWin = battle_data.victory
	-- 这里如果直接跳过战斗，回合数是0
	local heroData = battle_data.GetHeros()
    local roundTimes, heroids, herolevels, herostars = GetBattleInfo(heroData)
	
	local weaponID = 0
	local weaponLevel = 0
	
	if heroData[13] then
		-- 是武器
        weaponID = heroData[13].heroID == 80000 and 1 or 2
        local weaponData = weapon_data.GetWeaponByID(weaponID)
        if weaponData and weaponData.part and #weaponData.part >= 1 then
            local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
            weaponLevel = weaponCfg.weaponLv
        end
	end

	local json_str = {
		battle_type = battleType,
		arenaType = arenaType or 0,
		isWin = isWin,
		battle_round = roundTimes,
		hero_id_new = table.concat(heroids, '#'),
		hero_level_new = table.concat(herolevels, '#'),
		hero_star_new = table.concat(herostars, '#'),
		weapon_id = weaponID,
		weapon_level = weaponLevel
	}
	event.Trigger(event.GAME_EVENT_REPORT, "battle_info", json_str)
end

function ReportArenaBattleInfo(arenaType,battleID)
    local ui_select_hero = require "ui_select_hero"
    local currentPower = ui_select_hero.GetTempPower()
    local isWin = battle_data.victory
    local weaponID = 0
    local weaponLevel = 0
    local heroData = battle_data.GetHeros()
    -- 这里如果直接跳过战斗，回合数是0
    local roundTimes, heroids, herolevels, herostars, pos, talents = GetBattleInfo(heroData, true)

    if heroData[13] then
        -- 是武器
        weaponID = heroData[13].heroID == 80000 and 1 or 2
        local weaponData = weapon_data.GetWeaponByID(weaponID)
        if weaponData and weaponData.part and #weaponData.part >= 1 then
            local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
            weaponLevel = weaponCfg.weaponLv
        end
    end

    local roleName = tostring(player_mgr.GetRoleName()) or ""
    
    local rankNum = net_arena_module.MineRank[arenaType] or 0
    local scores = 0
    if arenaType == 1 then
        scores = net_arena_module.GetArenaScores(1)
    else
        scores = net_arena_module.MineGradeId[arenaType] or 0
    end
    local json_str = json.encode({
        arenaType = arenaType,
        isWin = isWin,
        battle_round = roundTimes,
        power_team = currentPower and currentPower.mine or 0,
        def_power = currentPower and currentPower.enemy or 0,
        hero_id_new = table.concat(heroids, '#'),
        hero_level_new = table.concat(herolevels, '#'),
        hero_star_new = table.concat(herostars, '#'),
        Location_num = table.concat(pos,'#'),
        battle_id = battleID or 0,
        isEnemy = false,
        weapon_id = weaponID,
        weapon_level = weaponLevel,
        ranking = rankNum,
        match_point = scores,
        role_name = roleName,
        hero_talent = talents
    })
    event.Trigger(event.GAME_EVENT_REPORT, "arena_battleinfo", json_str)
end

function ReportAshBattleInfo(level,power,victory,heroData,battleID)
    local isWin = victory
    -- 这里如果直接跳过战斗，回合数是0
    local roundTimes, heroids, herolevels, herostars, pos, talents = GetBattleInfo(heroData, true)

    local weaponID = 0
    local weaponLevel = 0
    
    if heroData[13] then
        -- 是武器
        weaponID = heroData[13].heroID == 80000 and 1 or 2
        local weaponData = weapon_data.GetWeaponByID(weaponID)
        if weaponData and weaponData.part and #weaponData.part >= 1 then
            local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
            weaponLevel = weaponCfg.weaponLv
        end
    end

    
    local ranking = net_arena_module.GetArenaRanking()
    local roleName = tostring(player_mgr.GetRoleName()) or ""
    local json_str = json.encode({
        power_team = power,
        dungeon_id = level,
        isWin = isWin,
        battle_round = roundTimes,
        hero_id_new = table.concat(heroids, '#'),
        hero_level_new = table.concat(herolevels, '#'),
        hero_star_new = table.concat(herostars, '#'),
        weapon_id = weaponID,
        weapon_level = weaponLevel,
        Location_num = table.concat(pos,'#'),
        battle_id = battleID or 0,
        role_name = roleName,
        arena_rank = ranking,
        hero_talent = talents
    })
    event.Trigger(event.GAME_EVENT_REPORT, "ashdungeon_info", json_str)

    if isWin and isWin == true then
        local json_str = json.encode({
            ashdungeon_id = level,
            role_name = roleName
        })
        event.Trigger(event.GAME_EVENT_REPORT, "ashdungeon_completed", json_str)
    else
        local json_str = json.encode({
            ashdungeon_id = level,
            role_name = roleName
        })
        event.Trigger(event.GAME_EVENT_REPORT, "ashdungeon_fail", json_str)
    end
end

function ReportSpaceDominationBattleInfo(teamPower, isActiveBoss,battleID)
    -- local isWin = battle_data.victory
    -- -- 这里如果直接跳过战斗，回合数是0
    -- local heroData = battle_data.GetHeros()
    -- local roundTimes, heroids, herolevels, herostars, pos, talents = GetBattleInfo(heroData, true)

    -- local weaponID = 0
    -- local weaponLevel = 0
    -- local dmg = 0
    
    -- if heroData[13] then
    --     dmg = dmg + heroData[13].statistics.damage
    --     -- 是武器
    --     weaponID = heroData[13].heroID == 80000 and 1 or 2
    --     local weaponData = weapon_data.GetWeaponByID(weaponID)
    --     if weaponData and weaponData.part and #weaponData.part >= 1 then
    --         local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
    --         weaponLevel = weaponCfg.weaponLv
    --     end
    -- end

    -- local stageID = 0
    -- local bossHpInfo = domination_mgr.CalBossHpInfo()
    -- for k,v in ipairs(bossHpInfo) do
    --     if dmg < v.endHp then
    --         stageID = k
    --         break
    --     end
    -- end

    -- local buffLayer = 0
    -- local buffInfo = domination_mgr.CalBossBuffInfo()
    -- for k,v in ipairs(buffInfo) do
    --     if dmg < v.endHp then
    --         buffLayer = k
    --         break
    --     end
    -- end


    -- local roleName = tostring(player_mgr.GetRoleName()) or ""
    -- local groupid = domination_mgr.GetTeamID() or 0
    -- local powerID = domination_mgr.GetPowerID() or 0
    -- local monsterTeamId = domination_mgr.GetMonsterTeamID() or 0
    
    
    -- local ranking = net_arena_module.GetArenaRanking()
    -- local property = {
    --     power_team = teamPower,
    --     team_id = groupid,
    --     power_id = powerID,
    --     monsterteam_id = monsterTeamId,
    --     damage = dmg,
    --     Location_num = table.concat(pos,'#'),
    --     battle_id = battleID or 0,
    --     isWin = isWin,
    --     battle_round = roundTimes,
    --     hero_id_new = table.concat(heroids, '#'),
    --     hero_level_new = table.concat(herolevels, '#'),
    --     hero_star_new = table.concat(herostars, '#'),
    --     weapon_id = weaponID,
    --     weapon_level = weaponLevel,
    --     stage_id = stageID - 1,
    --     buff_layer = buffLayer,
    --     role_name = roleName,
    --     arena_rank = ranking,
    --     hero_talent = talents
    -- }

    -- if isActiveBoss then 
    --     local sociaty_data = require "sociaty_data"
    --     property.league_id = sociaty_data.GetLeagueData() and sociaty_data.GetLeagueData().id or 0
    --     event.Trigger(event.GAME_EVENT_REPORT, "ActivityBoss_info", property)
    -- else
    --     event.Trigger(event.GAME_EVENT_REPORT, "spaceDominator_info", property)
    -- end
end

function OnRoundBeginReport(maxspeed,minspeed,lastRound,stageTypeStr,levelID)
    local speed = GetPlaySpeed()
    local isSpeed = ( maxspeed-minspeed>0.1 and speed > 0.5 * (minspeed + maxspeed) ) and 2 or 1
    local properties = string.format("{\"round\":%d,\"is_speed\":%d,\"battle_type\":\"%s\",\"dungeon_id\":%d}",lastRound,isSpeed,stageTypeStr,levelID )
    event.Trigger(event.GAME_EVENT_REPORT, "battle_round", properties)
end

function OnBattleSkipReport(lastRound,stageTypeStr,levelID)
    local properties = string.format("{\"round\":%d,\"battle_type\":\"%s\",\"dungeon_id\":%d}",lastRound,stageTypeStr,levelID)
    event.Trigger(event.GAME_EVENT_REPORT, "battle_skip", properties)
end
--进入关卡打点
function HookEnterReport(level,ceRate)
    --local properties = string.format("{\"dungeon_id\":%d,\"ceRate\":%d}", level, ceRate)
    --event.Trigger(event.GAME_EVENT_REPORT, "    hook_enter", properties)

    local reportMsg = {
        Level_id = level, --关卡ID
    }
    event.EventReport("Hook_enter", reportMsg)
end

--完成关卡打点
function HookCompletedReport(eventname,level, teamPower, ceRate)
    --local properties = string.format("{\"dungeon_id\":%d,\"team_power\":%d,\"ceRate\":%d}", level, teamPower, ceRate)
    --event.Trigger(event.GAME_EVENT_REPORT,eventname , properties)

    local reportMsg = {
        Level_id = level, --关卡ID
        Hook_type=battle_data.stageType, --关卡类型
        Team_power=teamPower, --队伍总战斗力
        --Cost_time=battle_data.stageType==common_pb.GameGoal and battle_data. or nil, --耗时
        battle_round=battle_data.stageType==common_pb.GameGoal and battle_data.roundTimes or nil, --回合数(跳过战斗回合数是0)
    }
    event.EventReport(eventname, reportMsg)
end

--完成关卡打点
function HookCompletedReport_GW(eventname,level, teamPower,stageType)
    local reportMsg = {
        Level_id = level, --关卡ID
        Hook_type=stageType, --关卡类型
        Team_power=teamPower, --队伍总战斗力
        --Cost_time=battle_data.stageType==common_pb.GameGoal and battle_data. or nil, --耗时
        battle_round=battle_data.stageType==common_pb.GameGoal and battle_data.roundTimes or nil, --回合数(跳过战斗回合数是0)
    }
    event.EventReport(eventname, reportMsg)
end

function Report_Odyssey_BattleInfo(level,teamPower,battleID)
    -- local isWin = battle_data.victory
    -- -- 这里如果直接跳过战斗，回合数是0
    -- local heroData = battle_data.GetHeros()
    -- local roundTimes, heroids, herolevels, herostars = GetBattleInfo(heroData)
    -- local weaponID = 0
    -- local weaponLevel = 0
    
    -- if heroData[13] then
    --     -- 是武器
    --     weaponID = weapon_data.GetLocalizedWeaponData(common_pb.ChinaRed,0)
    --     local weaponData = weapon_data.GetWeaponByID(weaponID)
    --     if weaponData and weaponData.part and #weaponData.part >= 1 then
    --         local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
    --         weaponLevel = weaponCfg.weaponLv
    --     end
    -- end

    -- local fleet_expedition_data = require "fleet_expedition_data"
    -- local  starCount = fleet_expedition_data.GetBattleStar() or 0

    -- local json_str = {
    --     dungeon_id = level,
    --     power_team = teamPower,
    --     isWin = isWin,
    --     battle_round = roundTimes,
    --     hero_id_new = table.concat(heroids, '#'),
    --     hero_level_new = table.concat(herolevels, '#'),
    --     hero_star_new = table.concat(herostars, '#'),
    --     weapon_id = weaponID,
    --     weapon_level = weaponLevel,
    --     battle_id = battleID or 0,--战斗ID
    --     StarNum = starCount--本次战斗星级评价（0、1、2、3）
    -- }
    -- event.Trigger(event.GAME_EVENT_REPORT, "Odyssey_Battleinfo", json_str)
end

function FactionTowerReport(_type,level,iTCfg)
    local towerID = _type*1000+level
    local roleName = tostring(player_mgr.GetRoleName()) or ""
    local json_str = json.encode({
        factionTower_id = towerID,
        camp_id = iTCfg.type,
        role_name = roleName
    })
    event.Trigger(event.GAME_EVENT_REPORT, "factionTower_enter", json_str)
end

function IllusionEnterReport(level)
    event.Trigger(event.GAME_EVENT_REPORT, "illusion_enter", "{\"dungeon_id\":"..level.."}")
end

function IllusionComplitReport(isFail,level, teamPower)
    local properties = string.format("{\"dungeon_id\":%d,\"team_power\":%d}", level, teamPower)
    if isFail then
        event.Trigger(event.GAME_EVENT_REPORT, "illusion_fail", properties)
    else
        event.Trigger(event.GAME_EVENT_REPORT, "illusion_completed", properties)
    end
end

function FactionTowerCompletedReport(isWin,_type,level,iTCfg,teamPower,victory,heroData,battleID)
    local roleName = tostring(player_mgr.GetRoleName()) or ""

    local roundTimes, heroids, herolevels, herostars, pos, talents = GetBattleInfo(heroData, true)

    local weaponID = 0
    local weaponLevel = 0
    if heroData[13] then
        -- 是武器
        weaponID = heroData[13].heroID == 80000 and 1 or 2
        local weaponData = weapon_data.GetWeaponByID(weaponID)
        if weaponData and weaponData.part and #weaponData.part >= 1 then
            local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
            weaponLevel = weaponCfg.weaponLv
        end
    end

    local factor_properties = {
        factionTower_id = _type*1000+level,
        camp_id = iTCfg.type or 0,
        team_power = teamPower,
        role_name = roleName,
        iswin = victory,
        battle_round = roundTimes,
        hero_id_new = table.concat(heroids, '#'),
        hero_level_new = table.concat(herolevels, '#'),
        hero_star_new = table.concat(herostars, '#'),
        weapon_id = weaponID,
        weapon_level = weaponLevel,
        battle_id = battleID or 0,
        hero_talent = talents
    }
    if isWin then
        event.Trigger(event.GAME_EVENT_REPORT, "factionTower_completed", factor_properties)
    else
        event.Trigger(event.GAME_EVENT_REPORT, "factionTower_fail", factor_properties)
    end
end

function AshdungeonEnterReport(nowCfg)
    local roleName = tostring(player_mgr.GetRoleName()) or ""
    local json_str = json.encode({
        ashdungeon_id = nowCfg.AshDungeonId,
        role_name = roleName
    })
    event.Trigger(event.GAME_EVENT_REPORT, "ashdungeon_enter", json_str)
end

--联盟boss战战斗结果上报
function ReportSociatyBossWarBattleInfo(teamPower,battleID)
    local isWin = battle_data.victory
    -- 这里如果直接跳过战斗，回合数是0

    local heroData = battle_data.GetHeros()
    local roundTimes, heroids, herolevels, herostars, pos = GetBattleInfo(heroData)

    local weaponID = 0
    local weaponLevel = 0
    local dmg = 0

    if heroData[13] then
        dmg = dmg + heroData[13].statistics.damage
        -- 是武器
        weaponID = heroData[13].heroID == 80000 and 1 or 2
        local weaponData = weapon_data.GetWeaponByID(weaponID)
        if weaponData and weaponData.part and #weaponData.part >= 1 then
            local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
            weaponLevel = weaponCfg.weaponLv
        end
    end

    
    local ranking = net_arena_module.GetArenaRanking()
    local property = {
        Un_battle = dmg,
        power_team = teamPower,
        battle_round = roundTimes,
        hero_id_new = table.concat(heroids, '#'),
        hero_level_new = table.concat(herolevels, '#'),
        hero_star_new = table.concat(herostars, '#'),
        weapon_id = weaponID,
        weapon_level = weaponLevel,
        battle_id = battleID or 0,
        Location_num = table.concat(pos,'#'),
    }

    local sociaty_data = require "sociaty_data"
    if sociaty_data.GetLeagueData() then
        property.league_id = sociaty_data.GetLeagueData().id or 0
        property.league_name = sociaty_data.GetLeagueData().strName or ""
    end
    
    event.Trigger(event.GAME_EVENT_REPORT, "Un_battle_gobattle", property)
end

function SpaceDominatiorEnterReport()
    -- local roleName = tostring(player_mgr.GetRoleName()) or ""
    -- local powerid = domination_mgr.GetPowerID()
    -- local monsterTeamId = domination_mgr.GetMonsterTeamID()
    -- local groupid = domination_mgr.GetTeamID()
    -- local json_str = json.encode({
    --     power_id = powerid,
    --     team_id = groupid,
    --     monsterteam_id = monsterTeamId,
    --     role_name = roleName
    -- })
    -- event.Trigger(event.GAME_EVENT_REPORT, "spaceDominator_enter", json_str)
end

function ReportLeagueChallengeInfo(power,battleID)

    local isWin = battle_data.victory
    -- 这里如果直接跳过战斗，回合数是0
    local heroData = battle_data.GetHeros()
    local roundTimes, heroids, herolevels, herostars, pos = GetBattleInfo(heroData)
    local weaponID = 0
    local weaponLevel = 0

    if heroData[13] then
        -- 是武器
        weaponID = heroData[13].heroID == 80000 and 1 or 2
        local weaponData = weapon_data.GetWeaponByID(weaponID)
        if weaponData and weaponData.part and #weaponData.part >= 1 then
            local weaponCfg = game_scheme:MagicWeapon_0(weaponID,1,weaponData.part[1].nLv,0)
            weaponLevel = weaponCfg.weaponLv
        end
    end
    local sociaty_data = require "sociaty_data"
    local roleName = tostring(player_mgr.GetRoleName()) or ""
    local json_str = json.encode({
        role_name = roleName,--角色名
        guild_id = sociaty_data.GetLeagueData().id,--公会ID
        guild_name = sociaty_data.GetLeagueData().strName,--公会名称
        power_team = power,--出战队伍战力
        iswin = isWin,--是否胜利
        battle_round = roundTimes,--战斗回合
        hero_id = table.concat(heroids, ','),--英雄ID 
        hero_level = table.concat(herolevels, ','),--英雄等级
        hero_star = table.concat(herostars,','),--英雄星级
        weapon_id = weaponID,--武器id
        weapon_level = weaponLevel,--武器等级
        battle_id = battleID or 0,--战斗ID
        Location_num = table.concat(pos,','),--位置序号（1~6号位）
        guild_level = sociaty_data.GetLeagueData().iLevel
    })
    event.Trigger(event.GAME_EVENT_REPORT, "Unbase_Unchallenge", json_str)
end

--上报盟战布阵信息
function ReportSociatyWarLineUpInfo(heroData, weaponId, power)
    
    local roundTimes, heroids, herolevels, herostars, pos, talents = GetBattleInfo(heroData, true)
    local property = {}
    property.power_team = power    --出战队伍战力
    property.hero_id_new = table.concat(heroids, '#')
    property.hero_level_new = table.concat(herolevels, '#')
    property.hero_star_new = table.concat(herostars, '#')
    local sociaty_data = require "sociaty_data"
    local sociatyBaseData = sociaty_data.GetLeagueData()
    property.guild_id = sociatyBaseData.id
    property.guild_name = sociatyBaseData.strName
    property.weapon_id = weaponId
    property.hero_talent = talents

    event.Trigger(event.GAME_EVENT_REPORT, "UnionB_Defense", property)
end

--上报盟战战报信息
function ReportSociatyWarInfo(heroData, weaponId, power,battleID)
    -- local isWin = battle_data.victory

    -- local roundTimes, heroids, herolevels, herostars, pos, talents = GetBattleInfo(heroData, true)

    -- local sociaty_war_mgr = require "sociaty_war_mgr"
    
    -- local property = {}

    -- property.UnionB_GetIntegral = sociaty_war_mgr.GetLastBattleScore()  --获得积分 TODO

    -- property.isRob = sociaty_war_mgr.GetLastBattleIsRobot() and 1 or 0    --对象是否为电脑（0,1
    -- property.iswin = isWin and 1 or 0    --是否胜利（0,1）
    -- property.battle_id = battleID or 0    --战斗ID
    -- property.power_team = power    --出战队伍战力
    -- property.hero_id_new = table.concat(heroids, '#') 
    -- property.hero_level_new = table.concat(herolevels, '#')
    -- property.hero_star_new = table.concat(herostars, '#')
    -- property.weapon_id = weaponId
    -- local sociaty_data = require "sociaty_data"
    -- local sociatyBaseData = sociaty_data.GetLeagueData()
    -- property.guild_id = sociatyBaseData.id
    -- property.guild_name = sociatyBaseData.strName
    -- property.hero_talent = talents

    -- event.Trigger(event.GAME_EVENT_REPORT, "UnionB_Expend", property)
end

function OnBattleNarration(langID)
    local properties = string.format("{\"shou_type\":%d,\"show_content\":%d}", 3, langID)
    event.Trigger(event.GAME_EVENT_REPORT, "show_step", properties)
end

function OnBattleDialog(langID)
    local properties = string.format("{\"shou_type\":%d,\"show_content\":%d}", 1, langID)
    event.Trigger(event.GAME_EVENT_REPORT, "show_step", properties)
end

function OnBattleSuspend(heroSID)
    local hero = battle_data.GetHeroByID(heroSID)
    local properties = string.format("{\"shou_type\":%d,\"show_content\":%d}", 2, hero.heroID)

    event.Trigger(event.GAME_EVENT_REPORT, "show_step", properties)
end

function NewPlayerLoadedReport(curTime,passTime)
    event.Trigger(event.GAME_EVENT_REPORT, string.format("new_player_loaded", "{\"sinceStartTime\":%d,\"consume\":%d}",curTime,passTime))
end

function GetPlaySpeedKey()
    --将PlaySpeed修改为PlaySpeedEx,速度配置被修改，不能再使用PlaySpeed数据
    return "PlaySpeedEx" .. player_mgr.GetPlayerRoleID()
end

function GetPlaySpeed()
    return PlayerPrefs.GetFloat(GetPlaySpeedKey(), 1)
end