--- base_obj.txt -------------------------------
--- author:  fgy
--- date: 2024/4/24 15:13
--- desc:绑定实现
--------------------------------------------------
local require = require
local Registry = require "base_registry"
module("base_obj")
local base_obj = {}

function base_obj.extend(target)
    target.components_ = {}

    function target:checkComponent(name)
        return self.components_[name] ~= nil
    end

    function target:addComponent(name,...)
        local component = Registry.newObject(name,...)
        self.components_[name] = component
        component:bind_(self)
        return component
    end

    function target:removeComponent(name)
        local component = self.components_[name]
        if component then component:unbind_() end
        self.components_[name] = nil
    end

    function target:getComponent(name)
        return self.components_[name]
    end

    return target
end

return base_obj
