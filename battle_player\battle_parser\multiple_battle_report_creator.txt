--multiple_battle_report_creator
--多战斗战报构建器
--基于播放起始信息(战报index)和原始战报；创建新起始点的战报：
--流程：
--1.根据切入战斗的战报index，将战报分割成两部分；
--2.将前半部分按顺序运算执行，生成这部分战报的最终状态
--3.将目标战报之前的状态和后半部分战报组合，生成新战报
local require = require
local table    = table
local pairs    = pairs
local math    = math
local tostring = tostring
local dump    = dump
local print    = print
local setmetatable = setmetatable
local type = type
local tonumber = tonumber
local descriptor = require "descriptor"
local battle_parser_utility   = require "battle_parser_utility"

local game_scheme = require "game_scheme"
local tbs_pb    = require "tbs_pb"
local prop_pb    = require "prop_pb"

local battle_data            = require "battle_data"
local common_pb            = require "common_new_pb"
local json                    = require "dkjson"
local util = require "util"
local log = require "log"

local battle_config = require "battle_config"

local Debug = CS.UnityEngine.Debug

module("multiple_battle_report_creator")

--原始的build初始化战报
local sourceBuildReport = nil

local descripts = nil
local reportIndex = 0

--英雄身上挂的buff
local buffDatas = {}


--最后设置召唤物信息的战报
local lastSummonedNumberReport

local lastRegisterLeftSummonedSid
local lastRegisterRightSummonedSid

local summonedPowerVlaues

function CreateNewReportBy(msg, startReportIndex)
    descripts = {}
    buffDatas = {}
    lastSummonedNumberReport = {}
    lastRegisterLeftSummonedSid = 0
    lastRegisterRightSummonedSid = 0
    summonedPowerVlaues = {}
    battle_data.ClearBattleInfo()
    battle_data.ClearStatisticData()
    local startIndex = 1

    if startReportIndex and startReportIndex > 0 then
        startIndex = startReportIndex
    end
    local newMsg = util.cp_pb_data(msg)
    --print("<color=#ffffff>开始构建新战报！！</color>  startIndex:", startIndex)
    -- dump(MultipleReportTable)
    --local playedReports = {}
    for i = 1, #newMsg.reports do
        if i < startIndex then
            reportIndex = i
            local report = newMsg.reports[i]
            local rt = report.reportType
            local handler = MultipleReportTable[rt]
            -- print("<color=#ffffff>播放戰鬥！！！！！！！！！！！！！</color> reportType:", rt, " index:", i, "handler:", handler)
            if handler ~= nil then
                handler(report)
            end
            -- for key, value in pairs(battle_data.GetHeros()) do
            --     print("<color=#55ffff>阵容属性：</color>"," index:", i, "pos:", key, " info:", json.encode(value, { indent = true }))
            -- end
            --table.insert(playedReports, report)
        end
    end

    --print("<color=#ffffff>已播放战报执行完成！！！！！！！！！！！！！</color>已执行战报", json.encode(playedReports, { indent = true }))

    local newReport = {}
    --添加武器能量战报
    if battle_data.preLoadWeaponValue ~= 0 then
        table.insert(newReport, GetWeaponReport(battle_data.euiptWeapon.left, battle_data.preLoadWeaponValue))
    end
    if battle_data.rightPreLoadWeaponValue ~= 0 then
        table.insert(newReport, GetWeaponReport(battle_data.euiptWeapon.right, battle_data.rightPreLoadWeaponValue))
    end
    


    --添加build战报
    local newbuildRport = GetNewBuildRport()
    -- local newbuildRportJson = json.encode(newbuildRport,{indent = true})
    table.insert(newReport, newbuildRport) --添加build战报

    --添加战斗初始信息战报
    local setInitBatlleDataReport = GetInitBatlleDataReport()
    table.insert(newReport, setInitBatlleDataReport)

    --添加我方召唤物信息
    local summonedReport, powerReport = GetSummonedReport(lastRegisterLeftSummonedSid)
    if summonedReport then
        table.insert(newReport, summonedReport)
    end
    if powerReport then
        table.insert(newReport, powerReport)
    end
    --添加敌方召唤物信息
    summonedReport, powerReport = GetSummonedReport(lastRegisterRightSummonedSid)
    if summonedReport then
        table.insert(newReport, summonedReport)
    end
    if powerReport then
        table.insert(newReport, powerReport)
    end

    --在战斗开始前添加回合开始战报(可以将buff作为子节点进行处理)
    table.insert(newReport, {["roundReport"] = { ["round"] = battle_data.roundTimes }, ["reportType"]=3}) 
    
    --添加buff战报
    --print("<color=#55ffff>需要添加的buff：</color>", json.encode(buffDatas, { indent = true }))
    for palID, buffs in pairs(buffDatas) do
        local palIDNum = tonumber(palID)
        local roleData = battle_data.GetHeroByID(palIDNum)
        --已死亡英雄不添加buff
        if not roleData.statistics.isDead and buffs then
            for key, buff in pairs(buffs) do
                table.insert(newReport, {["addBuffReport"] = { ["target"] = tonumber(palIDNum),
                ["buffID"] = buff[1],
                ["palID"] = tonumber(palIDNum),
                ["buffLevel"] = buff[2],}, ["reportType"]=5}) 
            end
        end
    end
   
    --起始战报在新生成战报中的战报序列 （需要根据该数据，将心湛保序列转换成原始战报序列）
    local newReportStartIndex = 0
    --新老战报序列号偏移差值，新战报加上差值，可以转换为原始战报序列号
    local reportIndexOffset = 0
    --添加后半部分战报
    for i = startIndex, #newMsg.reports do
        local report = newMsg.reports[i]
        table.insert(newReport, report)
        if newReportStartIndex == 0 then 
            newReportStartIndex = #newReport
            --print("<color=#ffffff>开始节点！！！</color>", json.encode(report, { indent = true }), " newReportStartIndex:", newReportStartIndex, " sourceStartIndex:", startIndex)
            reportIndexOffset = startIndex - newReportStartIndex
        end
    end
    --print("<color=#55ffff>新构建完整战报：</color>", json.encode(newReport, { indent = true }))
    --newReport = AddHasFieldMethod(newReport)
    --添加原表函数HasField()，适配解析战报逻辑
    AddHasFieldMethodToAllNode(newReport)
    -- for i = 1, #newReport do
    --         local report = newReport[i]
    --         if report then 
    --             -- local rt = report.reportType
    --             -- if type(report) == "table" then 
    --             --     report = AddHasFieldMethod(report)    
    --             -- end
    --         print("<color=#55ffff>原始战报：</color>", json.encode(report, { indent = true }))
    --         local has = report:HasField("buildReport")
    --         print("<color=#5f5f5f>检测是否可用:HasField</color> reportType:", rt, " hasBuildReport:", has)
    --         has = report:HasField("reportType")
    --         print("<color=#5f5f5f>检测是否可用:HasField</color> reportType:", rt, " has reportType:", has)
    --         has = report:HasField("battleid")
    --         print("<color=#5f5f5f>检测是否可用:HasField</color> reportType:", rt, " has battleid:", has)
    --         end
    --         -- if rt == 1 then
    --         -- end
    -- end

    return newReport, reportIndexOffset
end


function GetSummonedReport(summonedSid)
    local summonedInfo = battle_data.GetHeroByID(summonedSid)
    -- local summonedInfo
    -- for masterId, value in pairs(summonedList) do
    --     if value.statistics.isDead == false then
    --         summonedInfo = value
    --         break   --目前召唤物只有一个
    --     end
    -- end
    -- summonedInfo = summonedList[summonedSid]

    if not summonedInfo then
        return
    end
    -- local prop = battle_data.GetSummonedPropByType(summonedInfo.palID, )
    -- local len = #prop
    --将召唤物最后一次的数值信息，作为切入战斗需要显示的数据
    local lastInfo = lastSummonedNumberReport[summonedInfo.palID] --prop[len]

    if not lastInfo then
        return 
    end

    local report = {["summonedReport"] = {  ["masterSid"] = summonedInfo.masterID,
                                            ["palID"] = summonedInfo.palID,
                                            ["dir"] = summonedInfo.dir,
                                            ["data"] = {["numtype"] = lastInfo.data.numtype, ["val"] = lastInfo.data.val},
                                            ["heroid"] = summonedInfo.heroID,
                                            ["deadRound"] = summonedInfo.deadRound, --死亡的回合
                                            ["isInitData"] = true, --是否为召唤物初始信息
                                            }, 
                    ["reportType"] = tbs_pb.ReportType_SummonedReport}
	--添加召唤物当前能量战报
    local powerValue = summonedPowerVlaues[summonedInfo.palID]
    local powerReport
    if powerValue then 
        powerReport = GetWeaponReport(summonedInfo.palID, powerValue, summonedInfo.masterID)
    end
    return report,powerReport
end

function GetInitBatlleDataReport()
    local palsData = {}
    local pals = battle_data.GetHeros()
    for key, value in pairs(pals) do
        local palData = {["palID"] = value.palID, 
        ["heroID"] = value.heroID,
        ["lv"] = value.lv,
        ["starLv"] = value.numProp.starLv,
        ["sp"] = value.statistics.sp,
        ["damage"] = value.statistics.damage,
        ["hurt"] = value.statistics.hurt,
        ["heal"] = value.statistics.heal,
        ["pos"] = value.pos,}
        table.insert(palsData, palData)
    end
    
    --print("<color=#55ffff>战斗信息战报：</color>", json.encode(palsData, { indent = true }))
    return {["setInitBatlleDataReport"] = { ["round"] = battle_data.roundTimes,
            ["pals"] = palsData }, 
            ["reportType"]=battle_data.ReportType_SetInitBatlleData}
end

function GetWeaponReport(palId, energyValue, masterId)
    local reportContent = { ["palID"] = palId, 
                            ["value"] = energyValue,
                            ["masterId"] = masterId or 0,}--无效参数，设置为0

    local weaponReport = {["wpEnergyReport"] = reportContent, 
                            ["reportType"]=15}
    return weaponReport
end

function AddHasFieldMethodToAllNode(sourceTable)
    for key, value in pairs(sourceTable) do
        if value then 
                if type(value) == "table" then 
                sourceTable[key] = AddHasFieldMethod(value)    
                --递归所有子节点
                AddHasFieldMethodToAllNode(sourceTable[key])
            end
        end
    end
end

function AddHasFieldMethod(sourceTable)
    local newTable = setmetatable(sourceTable, {
        __index = function (self, methodName)
            if methodName == "HasField" then
                return function(self, field_name) 
                    local field = self[field_name]
                    return field ~= nil
                end
            end
        end
    })
    return newTable
end

function GetNewBuildRport()
    -- util.pb2str(sourceBuildReport)
    local newBuildReport = sourceBuildReport--util.cp_pb_data(sourceBuildReport)
    --print("<color=#55ffff>原始build战报：</color>", json.encode(newBuildReport, { indent = true }))--json.encode(sourceBuildReport,{indent = true}))
    --重置双方阵容数据
    newBuildReport.rightPals = UpdateHeroData(newBuildReport.rightPals)
    newBuildReport.leftPals = UpdateHeroData(newBuildReport.leftPals)
    newBuildReport.isCutOverBattle = true
    --print("<color=#55ffff>新构建build战报：</color>", json.encode(newBuildReport, { indent = true }))--json.encode(sourceBuildReport,{indent = true}))
    newBuildReport = {["buildReport"] = newBuildReport, ["reportType"] = 1}
    return newBuildReport
end

function UpdateHeroData(pals)
    if not pals then
        return nil
    end
    local palsCount = #pals
    local item
    local roleData
    local newPals = {}
    for index = palsCount, 1, -1 do
        item = pals[index]
        --print("UpdateHeroData!!!!!", index)
        if item then
            roleData = battle_data.GetHeroByID(item.palID)
            if roleData.statistics.isDead then
                --已死亡英雄，清空数据
                pals[index] = nil
            else
                --重置存活的英雄
                item.props = {}
                --属性序列:1.hp 2.mp 3.lv 4.maxhp 5.maxmp 6.starLv 7.lowhp 8.lowmaxhp 9.realmaxhp 10~15.skill1~skill6 16.skillId 17.DefenceVal
                table.insert(item.props, roleData.statistics.hp)
                table.insert(item.props, roleData.mp)
                table.insert(item.props, roleData.lv)
                table.insert(item.props, roleData.maxhp)
                table.insert(item.props, roleData.maxmp)
                table.insert(item.props, roleData.numProp.starLv)
                table.insert(item.props, 0)
                table.insert(item.props, 0)
                local realmaxhp = roleData.realmaxhp == false and 0 or roleData.realmaxhp
                table.insert(item.props, realmaxhp)
                table.insert(item.props, roleData.talentSkills[1])
                table.insert(item.props, roleData.talentSkills[2])
                table.insert(item.props, roleData.talentSkills[3])
                table.insert(item.props, roleData.talentSkills[4])
                table.insert(item.props, roleData.talentSkills[5])
                table.insert(item.props, roleData.talentSkills[6])
                table.insert(item.props, roleData.skinID)
                table.insert(item.props, roleData.defence)
                table.insert(newPals, item)
            end

            --身上buff获取：
            local des = AcquireDescriptor(item.palID)
            buffDatas[tostring(item.palID)] = des.buff
        end
    end
    return newPals
end




function OnBuildReport(msg)
    sourceBuildReport = msg.buildReport
    --print("<color=#ff0000ff>!!!!!!!!!!OnBuildReport</color>")
    battle_data.stageType = sourceBuildReport.stageType
    battle_data.stageLv = sourceBuildReport.stageLv
    battle_data.attacker = sourceBuildReport.leftID
    battle_data.defender = sourceBuildReport.rightID
    battle_data.leftPals = sourceBuildReport.leftPals
    battle_data.rightPals = sourceBuildReport.rightPals

    --print("<color=#ff0000ff>!!!!!!!!!!leftPals count:", #sourceBuildReport.leftPals, "rightPals count:</color>", #sourceBuildReport.rightPals)
    if sourceBuildReport.leftPals then
        for i = 1, #sourceBuildReport.leftPals do
            local context = sourceBuildReport.leftPals[i]
            BuildActor(context, 0)
        end
    end

    if sourceBuildReport.rightPals then
        for i = 1, #sourceBuildReport.rightPals do
            local context = sourceBuildReport.rightPals[i]
            BuildActor(context, 6)  
        end
    end

    if sourceBuildReport.leftAnimal and sourceBuildReport.leftAnimal.palID ~= 0 then
        BuildWeapon(sourceBuildReport.leftAnimal, 13)
    end

    if sourceBuildReport.rightAnimal and sourceBuildReport.rightAnimal.palID ~= 0 then
        BuildWeapon(sourceBuildReport.rightAnimal, 14)
    end
end

function BuildActor(context, posOffset)
    local heroCfg = game_scheme:Hero_0(context.cfgID)
    if heroCfg == nil then
        Debug.LogError("No Hero config with id = " .. context.cfgID)
        return
    end

    local starLv = context.props[tbs_pb.EnBuildProp_Star_Lv + 1]
    local heroStarLv = (starLv == 0 and heroCfg.starLv or starLv)

    local hp = context.props[tbs_pb.EnBuildProp_Hp + 1]
    local mp = context.props[tbs_pb.EnBuildProp_Mp + 1]
    local lv = context.props[tbs_pb.EnBuildProp_Lv + 1] or heroCfg.monsterLevel
    local skinID = context.props[tbs_pb.EnBuildProp_SkinID + 1]
    local maxhp = context.props[tbs_pb.EnBuildProp_Max_Hp + 1]
    local maxhp_v = context.props[tbs_pb.EnBuildProp_Cure_Max_Hp + 1] -- or maxhp
    local defence = context.props[tbs_pb.EnBuildProp_DefenceVal + 1] --防御值
    local isTrialHero = context.props[tbs_pb.EnBuildProp_IsTrialHero + 1] --是试用英雄:1; 不是试用英雄:0
    if maxhp_v then
        maxhp_v = maxhp_v > 0 and maxhp_v -- or maxhp
    end

    ------ print("maxhp_v",context.palID,maxhp,maxhp_v)
    local maxmp = context.props[tbs_pb.EnBuildProp_Max_Mp + 1]
    local pos = context.Row * 3 + context.Col + posOffset
    local isLargeHp = context.IsEnLarge and context.IsEnLarge
    local talents = {}

    local talent_index_first = tbs_pb.EnBuildProp_Skill1 + 1
    local talent_index_end = tbs_pb.EnBuildProp_Skill6 + 1
    for i = talent_index_first, talent_index_end do
        local talentID = context.props[i]
        if talentID then
            table.insert(talents, talentID)
        else
            table.insert(talents, 0)
        end
    end

    battle_data.BuildActorData(context)

    if isLargeHp then
        if battle_data.stageType == common_pb.BrokenSpaceTime or battle_data.stageType == common_pb.LeaActivityBoss then

        else
            local lowhp = context.props[tbs_pb.EnBuildProp_Low_Hp + 1] or 0
            local lowmaxhp = context.props[tbs_pb.EnBuildProp_Low_Max_Hp + 1] or 0

            -- isLargeHp 时，客户端缩小 1000倍显示?
            hp = hp + lowhp * 0.001
            maxhp = maxhp + lowmaxhp * 0.001
        end
    end
    battle_data.RegisterHero(context.palID, context.cfgID, pos, hp, mp, maxhp, maxmp, lv, heroCfg, starLv, modulCfg, maxhp_v, talents, skinID, defence, isLargeHp, isTrialHero)

end

function BuildWeapon(context, posOffset)
--[[    local weapon_data = require("weapon_data")

    local weaponID = weapon_data.GetWeaponIDByHeroID(context.cfgID)
    if not weaponID then
        log.Error("BuildWeapon weaponID is nil>>>>>>>>cfgID:", context.cfgID)
        weaponID = 1
    end
    local weaponData = weapon_data.GetWeaponByID(weaponID)]]
    local droneData = require "gw_home_drone_data"
    local weaponCfg = droneData.OnGetDroneCfgData()
    local lv =  0--等级默认值
    local starLv = 0
    local mp = 0
    if posOffset == 13 then
        mp = context and context.props and context.props[1] or 0
        lv = context and context.props and context.props[2] or 0--实际武器等级
    end

    --TODO：测试,参数hp,maxhp,maxmp,lv均未定义，语法错误
    local hp = context and context.props and context.props[tbs_pb.EnBuildProp_Hp + 1] or 0
    local maxhp = context and context.props and context.props[tbs_pb.EnBuildProp_Max_Hp + 1] or 0
    local maxmp = context and context.props and context.props[tbs_pb.EnBuildProp_Max_Mp + 1] or 0
    battle_data.RegisterWeapon(context.palID, context.cfgID, posOffset, hp, mp, maxhp, maxmp, lv, weaponCfg)
end

--hp，mp，sp等数值改变
function OnNumericReport(msg)
    local report = msg.numericReport
    --print("<color=#ff0000ff>!!!!!!!!!!OnNumericReport report.numType:</color>", "index", reportIndex, " type:",
         --report.numType, " report.palID:", report.targets, " report.numbers:", report.numbers)

    local isDead = report.Dead and report.Dead or false
    if isDead then 
        --召唤物死亡标记
        if battle_data.IsSummoned(report.targets) then
            local summoned = battle_data.GetHeroByID(report.targets)
            if summoned then
                summoned.statistics.isDead = true
            end
            summoned.deadRound = battle_data.GetBattleRound() --记录召唤物死亡时的回合数
        else
            battle_data.count_dead(report.targets, isDead)
        end
    end
    if report.numType == tbs_pb.NumericType_HP then
        local casterID = report.palID
        if battle_data.IsSummoned(report.palID) then
            local summoned = battle_data.GetHeroByID(report.palID)
            if summoned then
                casterID = summoned.masterID
            end
        end
        if report.numbers > 0 then
            --descript.healHp = descript.healHp + report.numbers
            battle_data.count_heal(casterID, report.numbers)
        else
            --descript.damageHp = descript.damageHp + report.numbers
            -- descript.hurtSource[report.palID] = descript.hurtSource[report.palID] or 0 
            -- descript.hurtSource[report.palID] = descript.hurtSource[report.palID] + report.numbers
            -- report.palID 造成的伤害
            battle_data.count_damage(casterID, math.abs(report.numbers))
            -- report.targets 承受的伤害
            battle_data.count_hurt(report.targets, math.abs(report.numbers))
        end

        local deltaHp = report.numbers
        deltaHp = battle_data.handleLargeHp(report.targets, deltaHp)
        deltaHp = battle_data.clamp_hp(report.targets, deltaHp)
        --设置血量
        battle_data.count_hp(report.targets, deltaHp)

    elseif report.numType == tbs_pb.NumericType_MP then
        local heroData = battle_data.GetHeroByID(report.targets)
        if heroData then
            heroData.mp = heroData.mp + report.numbers
        end
    elseif report.numType == tbs_pb.NumericType_SP then
        battle_data.count_sp(report.targets, report.numbers)
    end

    battle_data.OnNumericReport(report)
end


--更新武器能量值
function OnWeaponEnergyReport(msg)
    local report = msg.wpEnergyReport

    if report.palID == battle_data.euiptWeapon.left then
        battle_data.preLoadWeaponValue = report.value
    elseif report.palID == battle_data.euiptWeapon.right then
        battle_data.rightPreLoadWeaponValue = report.value
    elseif battle_data.IsSummoned(report.palID) then
        --保存召唤物能量值
        summonedPowerVlaues[report.palID] = report.value
    end
end

--添加buff
function OnAddBuffReport(msg)
    --print("<color=#ff0000ff>!!!!!!!!!!OnAddBuffReport</color>")
    local report = msg.addBuffReport
    -- 技能击中时挂buff
    local descript = AcquireDescriptor(report.target)
    if descript.buff == nil then
        descript.buff = {}
    end

    local buffCfg = game_scheme:Buff_0(report.buffID, report.buffLevel)
    local icon = battle_config.GetBuffIconData(buffCfg)
    local particleType, particleRes = battle_config.GetBuffFxData(buffCfg)
    ------ print("AppendAddBuffPackage",palID, buffId,style, text, buffLevel, statisticBuff, particleRes)
    --只添加iconbuff和可持续buff
    if icon or particleType == 2 then
        table.insert(descript.buff, { report.buffID, report.buffLevel })
    end

end
--移除buff
function OnRemoveBuffReport(msg)
    --print("<color=#ff0000ff>!!!!!!!!!!OnRemoveBuffReport</color>")
    local report = msg.removeBuffReport
    -- 技能击中时删buff
    local descript = AcquireDescriptor(report.target)
    if descript.buff == nil then
        return
    end
    local buff
    local length = #descript.buff
    for index = 1, length do
        buff = descript.buff[index]
        if buff and buff[1] == report.buffID and buff[2] == report.buffLevel then
            descript.buff[index] = nil
            break
        end
    end
end


function AcquireDescriptor(target)
    local descript = descripts[target]
    if descript == nil then
        descript = {}
        descript.damageHp = 0
        descript.healHp = 0
        descript.consumeMp = 0
        descript.generateMp = 0
        descript.useSp = 0
        descript.generateSp = 0
        descript.shieldChanged = 0
        descript.removeShield = false
        descript.hurtSource = {}
        --数值的变化并不一定由当前技能的释放者引起，此处保存谁改变了数值，例如谁释放的技能
        --tbs_pb -> NumericReport -> palID --谁改变了数值，例如谁释放的技能
        --tbs_pb -> NumericReport -> targets --谁的数值被改变了
        descript.casterID = nil

        descripts[target] = descript
    end
    return descript
end

--属性修改：血量上限，血量，防御值
function OnUpdatePropReport(msg)
    local report = msg.updatePropReport
    --print("<color=#ff0000ff>!!!!!!!!!!OnUpdatePropReport</color>")

    if report.propID ~= prop_pb.PAL_PROP_HP and
    report.propID ~= prop_pb.PAL_PROP_REMAIN_HP and
    report.propID ~= prop_pb.PAL_PROP_DEFENCE then
        return
    end
    --log.Error("属性值修改!!!!!!" .. report.palID .. "-" .. report.propValue)

    -- 修改属性
    if report.propID == prop_pb.PAL_PROP_HP then
        battle_data.UpdateHeroMaxHp(report.palID, report.propValue)
    elseif report.propID == prop_pb.PAL_PROP_REMAIN_HP then
        battle_data.UpdateHeroHp(report.palID, report.propValue)
    end
    --防御值改变
    if report.propID == prop_pb.PAL_PROP_DEFENCE then
        battle_data.UpdateHeroDefence(report.palID, report.propValue)
    end
end


function OnRoundReport(msg)
    local report = msg.roundReport
    battle_data.SetRoundTimes(report.round)
end

--召唤物报告
function OnSummonedReport(msg, player)
    local report = msg.summonedReport
    local battle_data = require "battle_data"
    local summoned = battle_data.GetHeroByID(report.palID)
    lastSummonedNumberReport[report.palID] = report
    if summoned and summoned.register then
        return
    end
    if report.dir == 1 then 
        lastRegisterLeftSummonedSid = report.palID
    elseif report.dir == 2 then
        lastRegisterRightSummonedSid = report.palID
    end
    local summonedCfg = game_scheme:Summoned_0(report.heroid)
    local heroCfg = game_scheme:Hero_0(report.heroid)
    local moduleCfg = game_scheme:Modul_0()   
    
    battle_data.RegisterSummoned(report.palID, report.masterSid, report.heroid, 0, 0, 0, 0, 0, summonedCfg, moduleCfg, heroCfg and heroCfg.faceID.data[0], 0, 0, report.dir, -1)
end
--召唤物属性改变报告
function OnSummonedNumberReport(msg, player)
    local report = msg.summonedNumReport
    lastSummonedNumberReport[report.palID] = report
end

function OnGameOverReport(msg, player)
    local report = msg.gameOverReport
    battle_parser_utility.SetRewards(report.winner, report.loser, report.winnerRewards, report.loserRewards)
end


MultipleReportTable = {
    [tbs_pb.ReportType_BuildReport] = OnBuildReport,
    [tbs_pb.ReportType_AddBuffReport] = OnAddBuffReport,
    [tbs_pb.ReportType_RemoveBuffReport] = OnRemoveBuffReport,
    [tbs_pb.ReportType_NumericReport] = OnNumericReport,
    [tbs_pb.ReportType_WeaponEnergyReport] = OnWeaponEnergyReport,
    [tbs_pb.ReportType_UpdatePropReport] = OnUpdatePropReport,
    [tbs_pb.ReportType_RoundReport] = OnRoundReport,
    [tbs_pb.ReportType_SummonedReport] = OnSummonedReport,
    [tbs_pb.ReportType_summonedNumberReport] = OnSummonedNumberReport,
    [tbs_pb.ReportType_GameOverReport] = OnGameOverReport,
}