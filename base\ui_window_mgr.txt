-- ui_window_mgr.txt -------------------------------------------------
-- author:  郑秀程
-- date:    2017.10.17
-- ver:     1.0
-- desc:    UI模块管理类
-------------------------------------------------------------------

local Edump         = Edump
local debug          = debug
local print          = print
local xpcall         = xpcall
local ipairs         = ipairs
local table          = table
local pairs          = pairs
local tostring       = tostring
local require        = require
local coroutine      = coroutine
local type           = type
local unpack         = unpack
local typeof = typeof
local collectgarbage = collectgarbage
local handleFunc     = handleFunc
-- local dump           = dump
local math           = math


local log          = require "log"
local idle         = require "idle"
local util         = require "util"
local game         = require "game"
local event        = require "event"
local const        = require "const"
local laymain_data = require "laymain_data"
local effect_load_mgr = require "effect_load_mgr"
local enum_define           = require "enum_define"


local GraphicRaycaster      = CS.War.UI.GRaycaster
local CanvasType            = typeof(CS.UnityEngine.Canvas)
local Application           = CS.UnityEngine.Application
local GameObject            = CS.UnityEngine.GameObject
local IOSystem              = require "iosystem_load"
local DateTime              = CS.System.DateTime
local SortingGroupType      = typeof(CS.UnityEngine.Rendering.SortingGroup)
local OPEN_CACHE_WINDOW_RES = const.OPEN_CACHE_WINDOW_RES
local GC                    = CS.System.GC
local Camera                = CS.UnityEngine.Camera
local LeanTween             = CS.LeanTween
local LeanTweenType         = CS.LeanTweenType
local RectTransform         = CS.UnityEngine.RectTransform
local UIUtil                = CS.Common_Util.UIUtil


module("ui_window_mgr")

local IsLogLevel
local Warning
local Warning0


--- 调试模块打开异常:打开WindowMgr:ShowModuleImediate,ModifyCurrent,checkstate(dump 部分)的日志
local IsStepGC = false

local logOpenClose = false
local logOrder = false
local logCheckState = false
local logOpenExistUI = false


local LAYER_LOW = 1         --最底层次
local LAYER_NORMAL = 2      --正常层次
local LAYER_HIGHT = 3       --高级层次
local LAYER_TOP = 4         --最顶层次

local WIN_NO_SHOW = 1         --
local WIN_SHOW = 2         -- 

local update_interval = 0


local MAX_LAYER_IND = 200
local START_LAYER_IND = 30

if Application.isEditor then
    MAX_LAYER_IND = 40
end


local WindowMgr = {}
local WindowObjs = {}
-- local WindowCacheObjs = {
--     ui_package = {},
--     ui_soul_link_select_hero = {},
--     ui_soul_link = {},
--     ui_hero_impl = {},
--     ui_create_array = {},
--     ui_maze_hero = {},
-- }
--对应ui的controller数据层集合
local Controllers = {}
local WindowDestroy = {}

-- 经常使用Canvas的列表
local UsedCanvasNameList = {
    CanvasWithMesh = 1,
    CanvasLoading=1,
    CanvasScreenEffect=1,
    CanvasBelowHUD=1,
    Canvas=1,
}
-- 等待隐藏的列表
local WaitHideModuleDic = {}
local ticker = nil
-- 等待处理的类型
local WindowWaitType =
{
    Position = 1,
    Destory = 2,
}


local WindowConfig = 
{
	
	 --[[
    ["这里填UI窗口模块名称"] =             {
        interfaceModule = nil,      --定义接口的模块
        showFunc = nil,             --显示接口，默认为"Show"
        hideFunc = nil,             --隐藏接口，默认为"Hide"
        closeFunc = nil,            --关闭接口，默认为"Close"
        unloadWhenHide = true,      --在隐藏的时候是否需要卸载窗口(模式为true)
        enableStateHandle = false,  --是否激活状态处理
		closeOnSwitch = true,		-- 切换状态Close所有窗口
		mutexHallModel = false,		-- 是否需要支持大厅模型互斥
    },
    ]]
     
}


--[[ 为避免state 和showmodule 配置混乱导致不可控,GameStateWindowConfig里面只配置主窗口模块,子窗口的显示关闭在WindowRelationConfig里面配置 ]]
local GameStateWindowConfig = 
{
    --[[[状态的ID] = {
        {"进入状态时同步开启的窗口"},
        {"退出状态时要关闭的窗口(默认就是打开的窗口)，可以不设置"},
        {"退出时额外要关闭的窗口(可以设置)"}, 
        {"进入时要额外关闭的窗口(可以设置)"}
    }]]
    [game.STATE_LOGIN_TYPE] = {{"ui_login_main"},{},{'ui_account_login', 'ui_create_phone_account', 'ui_create_mail_account', 'ui_phone_account_confirm', 'ui_reset_pwd'}},
	[game.STATE_PHONE_LOGIN] = {{"ui_phone_login"}},
    -- [game.STATE_REGISTER_LOGIN] = {{"ui_register_login"}},
    [game.STATE_ACCOUNT_LOGIN] = {{"ui_login"},{}},
	
    [game.STATE_LOGINED] = {{},{"ui_login"}},
    
	-- 创建角色由服务器直接创建，不用客户端请求
	[game.STATE_CREATE_ACTOR] = {{}, {"ui_select_servers"}},

    -- [game.STATE_MAKE_MATCH_V] = {
    --     {"ui_time_loading","ui_lobby"},
    --     {"ui_lobby"},
    --     {},
    --      {"ui_scene_loading"}
    --     },
	
    --场景加载状态
    [game.STATE_SCENE_LOADING] = {{"ui_scene_loading"}, {"ui_select_servers"}, {}, {}},
    
    --游戏状态
    [game.STATE_GAME] = {{}, {"ui_select_servers"}, {}, {"ui_scene_loading"}},	
}

--UI模块关联
--[[ 为避免state 和showmodule 配置混乱导致不可控,GameStateWindowConfig里面只配置主窗口模块,子窗口的显示关闭在WindowRelationConfig里面配置 ]]
local WindowRelationConfig = 
{
    --[[
    --窗口模块名称
    ["这里填模块的名称"] = {
        {"这里填显示时要关闭的窗口列表"},                     --显示时，需要关闭的模块
        {"这里填模块关闭时要打开的模块列表"}                  --关闭时，需要打开的模块(nil时表示与显示列表一致)
        {"这里填要跟随这个模块一起打开的模块列表"},           --显示时，要打开的列表
        {"这里填要跟随这个模块一起关闭的模块列表"},           --关闭时，要关闭的列表
        {"这里填禁止该模块打开的模块"}                        --禁止改模块打开的模块(可以不填)
    }
    ]]
	 
}
---- allow2show 附带界面
---- layer 互斥层为  默认为, 无关层
-- local WindowConfig = 
-- {
-- } 

----+++++

-- UI_LAYER_TOP = 4           --4顶层(loading、需要显示在顶层的ui）
-- UI_LAYER_MODULE = 3        --3模块层（业务模块）
-- UI_LAYER_SCENE_UI = 2      --2场景UI层（uilobby，top，bottom...）
-- UI_LAYER_SCENE = 1         --1最低层：场景层(基础场景，狩猎、冒险、联盟、主城)

local uiLayerOffset = 50    --每层可容纳主动打开的ui数量

local uiLayerStartIndex =   --ui大层起始位置
{
    [const.UI_LAYER_SCENE] = 0,
    [const.UI_LAYER_SCENE_UI] = 70,
    [const.UI_LAYER_MODULE] = 140,
    [const.UI_LAYER_TOP] = 210,
}

--- state list current  -open
--- state attach list current  -attach
local WIN_MAIN_TAG = 1
local WIN_ATTACH_TAG = 2
local WinCurrentList = 
{
    ind=1,
    [const.UI_LAYER_SCENE] = uiLayerStartIndex[const.UI_LAYER_SCENE],
    [const.UI_LAYER_SCENE_UI] = uiLayerStartIndex[const.UI_LAYER_SCENE_UI],
    [const.UI_LAYER_MODULE] = uiLayerStartIndex[const.UI_LAYER_MODULE],
    [const.UI_LAYER_TOP] = uiLayerStartIndex[const.UI_LAYER_TOP],
}
local WinAttachList = 
{
    
}

local moduleDefaultLayer = 
{
    --场景层
    ["new_hook_scene"] = const.UI_LAYER_SCENE,

    --场景ui层
    ["ui_union_list"] = const.UI_LAYER_SCENE_UI, 
    ["ui_union_scene"] = const.UI_LAYER_SCENE_UI,
    ["ui_hero_impl"] = const.UI_LAYER_SCENE_UI,
    ["ui_lobby"] = const.UI_LAYER_SCENE_UI,
    ["ui_menu_bot"] = const.UI_LAYER_SCENE_UI,
    ["ui_menu_top"] = const.UI_LAYER_SCENE_UI,
    
    
    --["ui_chat_main_new"] = const.UI_LAYER_SCENE_UI,
    -- ["ui_sociaty_medal_entrance"] = const.UI_LAYER_SCENE_UI,
    --["ui_alliance_invite_main"] = const.UI_LAYER_SCENE_UI,

    ["ui_hub_city"] = const.UI_LAYER_SCENE_UI, 
    ["ui_hub_adventure"] = const.UI_LAYER_SCENE_UI, 
    ["ui_hub_hunt"] = const.UI_LAYER_SCENE_UI, 
    ["ui_hub_hero"] = const.UI_LAYER_SCENE_UI,

    ["ui_sand_back_to_base"] = const.UI_LAYER_SCENE_UI,
    ["ui_sand_move_server_tip"] = const.UI_LAYER_SCENE_UI,
    
    --业务模块层
    ["ui_chat_main_module"] = const.UI_LAYER_MODULE,  --功能模块聊天，在模块中打开的聊天界面使用业务模块层
    ["ui_sand_box_back_tips"] = const.UI_LAYER_MODULE,
    
    --顶层：loading
    ["ui_loading"] = const.UI_LAYER_TOP,
    ["ui_mini_game_loading"] = const.UI_LAYER_TOP, --小游戏loading面板
    ["ui_static_load"] = const.UI_LAYER_TOP, --静态loading面板，无进度条

    ["ui_ling_shi_new"] = const.UI_LAYER_TOP, -- 灵石
    ["ui_lang_test"] = const.UI_LAYER_TOP, -- 切换语言提示开关
    ["ui_point_slider"] = const.UI_LAYER_TOP,-- 通用进度条提示
    
    ['ui_bs_operate'] = const.UI_LAYER_SCENE, --城建菜单栏
    ['ui_bs_pitchon'] = const.UI_LAYER_SCENE, --城建确定栏


    -- 沙漠风暴
    ['ui_desert_storm_slg'] = const.UI_LAYER_MODULE, --城建菜单栏
}
---@description 设置默认UI层级
function WindowMgr.SetDefaultUILayer(moduleName,layer)
    if  moduleName and  layer and not moduleDefaultLayer[moduleName]  then
        moduleDefaultLayer[moduleName] =layer
    end
end
--缺省全屏ui界面
local defaultFullScreenModules = 
{
    -- ui_login_main 不能为全屏，否则会提前隐藏 ui_lobby 界面，导致 checkCloseTicker 中 IsUIVisible 为 false,不能进入大厅界面
    -- ["ui_login_main"] = true,
    ["ui_loading"] = true,
    ["ui_select_hero"] = true,
    --英雄
    ["ui_hero_team_recommend"] = true,--阵容推荐
    ["ui_hero_base"] = true,
    ["ui_hero_strategy"] = true,--英雄攻略
    ["ui_hero_talent_skill"] = true,--英雄天赋
    ["ui_hero_skin"] = true,--英雄皮肤更换界面
    ["ui_hero_addition_info"] = true,--英雄加成面板
    ["ui_hero_rank"] = true,--英雄排名
    ["ui_archive_detail"] = true,
    ["ui_activity_new_player"] = true,
    ["new_hook_scene"] = true,
    ["ui_select_hero"] = true,
    ["ui_show_hero_card"] = true,
    --大厅礼包活动
    ["ui_gift_new"] = true,
    -- ["ui_business_activity_base"] = true,
    ["ui_new_week_activity"] = true,
    ["ui_bag_bg"] = true,
    --联盟
    -- ["ui_sociaty_war_rank"] = true,--盟战排行
    ["ui_union_technology_base"] = true,--联盟科技
    -- ["ui_sociaty_active_boss_main"] = true,--枢纽试炼
    --["ui_union_list"] = true, -- 选择联盟列表
    --主城
    ["ui_rank_base"] = true,--英雄杯
    ["ui_rank_list_new"] = true,
    ["ui_hero_decompose"] = true,--重构仓
    ["ui_homeland_base"] = true,--家园
    ["ui_weapon_prop"] = true,--家园/战舰
    ["ui_lineup_call"] = true,--召唤之塔/阵容抽召唤/卡片召唤
    ["ui_create_array"] = true,--觉醒研究所
    --冒险
    ["ui_new_area_top_game"] = true,--竞技场巅峰对决
    ["ui_top_game_schedule"] = true,--竞技场巅峰对决小组赛录像
    -- ["ui_maze"] = true,--星际迷航
    -- ["ui_illusion_tower"] = true,--星际通缉
    -- ["ui_tavern_main"] = true,--赛博酒吧
    -- ["ui_dungeon_levels"] = true,--遗落之境levels面板
    ["ui_commander_trial"] = true,--主界面左下角勇者试炼
    ["ui_void_ring"] = true,--英杰擂台
    --小游戏 (全屏界面)
    ["ui_casual_game"] = true,--小游戏levels面板
    ["ui_mini_game_loading"] = true, --小游戏loading面板
    ["ui_mini_game_info"] = true, --小游戏info面板
    --部分界面如小游戏不能添加原因说明
    --["ui_hero_viewing"] = true,--英雄观赏模式模块
    --["ui_market_new"] = true,--购物中心(会被嵌套在竞技场的传奇商城子界面，导致添加该模块时打开竞技场/传奇锦标赛/传奇商城时，出现黑屏) --这种情况需要增加虚拟界面标签，通过虚拟标签区分是否全屏

    --试炼
    ui_experiment = true,
    --新手竞技场
    ui_match_single_rank = true,
    --阵营试炼
    ui_camp_trial = true,
    ui_trial_tower = true,
    ui_trial_level_select = true,
    
    -- ["ui_force_base"] = true, --原力武器
    ["ui_festival_activity_center"] = true, --节日活动
    ["ui_alliance_main"] = true, --联盟主界面
    ["ui_gw_hero_list"] = true, --英雄界面
    ["ui_gwhero_base"] = true, --英雄模型界面
    -- ["ui_rebirth_space_team_create"] = true, --轮回空间备战
    ["ui_gwhero_call"]=true, --抽卡结果界面
    ["ui_gwhero_summon_base"] = true, --抽卡基类页面
    ["ui_chat_main_new"] = true, --聊天界面
    ["ui_bomberman"] = true, --解救人质
    ["ui_alliance_duel_main"] = true, --联盟对决主界面
    ["ui_radar_main"] = true, --雷达主界面
    ["ui_war_zone_main"] = true, --战区对决主界面
    ["ui_theater_position_panel"] = true, --国会管理官职页面
    ["ui_congress_main_panel"] = true, --国会管理主界面
    ["ui_award_main_panel"] = true, --国会嘉奖界面
    ["ui_tavern_base"] = true, --酒馆界面
    ["ui_new_hero_select_panel"] = true, --英雄编队
    ["ui_vip_main_new"] = true, --特权界面
    ["ui_intercity_trucks_main"] = true, --马车界面
    ["ui_new_magic_weapon_panel"] = true, --神兽界面
    ["ui_alliance_train_main"] = true, --火车界面
    ["ui_mail_detail_battle_report"] = true, --战报界面
    ["ui_in_battle"] = true, --战斗界面
    ["ui_all_rank_base"] = true, --排行榜
    ["ui_mail_detail_train_info"] = true, --火车邮件
    ["ui_technology_main_panel"] = true, --联盟第二科技
    ["ui_technology_tree_panel"] = true, --科技
    ["ui_bs_fortified_wall_panel"] = true, --城防配置
    -- ["ui_city_siege_reward_panel"] = true, --占领城市界面
    
    --
    ["ui_storm_battle_map"] = true, -- 战斗地图界面
    
    ["ui_common_mini_game_level"] = true, --前线突围
    ["ui_gw_shop"] = true, --商店
    ["ui_survivor_list"] = true, -- 幸存者列表
    ["ui_gwhero_equip_build_base"] = true, -- 装备工坊
    ["ui_decorate_collect_panel"] = true, -- 饰品收藏馆
    ["ui_war_zone_duel_main"] = true, -- 王国对决
}

function WindowMgr.SetDefaultFullScreenModules(moduleName,isCancel)
    defaultFullScreenModules[moduleName]= not isCancel
end

local fullUIHideList = {}
local lingshiUIList=
{
    ["ui_shield"]=true,
    ["ui_ling_shi_new"] = true,
    ["ui_lang_test"] = true,
    ["ui_gw_scene_enter"]=true,
    ["ui_common_item_anim"] = true
}
local subWndUIMap = {}
function RegisterConsole()
    util.RegisterConsole("WindowMgr-OpenClose",0,function ( st )
        logOpenClose = st == 1
    end)
    util.RegisterConsole("WindowMgr-logOrder",0,function ( st )
        logOrder = st == 1
    end)
    util.RegisterConsole("WindowMgr-logCheckState",0,function ( st )
        logCheckState = st == 1
    end)
    util.RegisterConsole("WindowMgr-logOpenExistUI",0,function ( st )
        logOpenExistUI = st == 1
    end)
end



function WindowMgr:AddSubWndUI(wndName)
    subWndUIMap[wndName] = true
end

function WindowMgr:SetUpdateInterval(intvl)
    update_interval = intvl or 0.1
     --print("SetUpdateInterval",update_interval)
end

function WindowMgr:IsInCurrents(uiModuleName)
    local list = WinCurrentList
    local b = list[uiModuleName]
    return b
end
function WindowMgr:ModifyCurrent(uiModuleName,bAddOrRemove)
    if logOpenClose then
        log.Warning("ModifyCurrent",uiModuleName,bAddOrRemove)
    end
    Warning(1, "ModifyCurrent",uiModuleName,bAddOrRemove)

    local list = WinCurrentList
    -- bAddOrRemove = bAddOrRemove or (WindowConfig[uiModuleName] and WindowConfig[uiModuleName]==WIN_SHOW)
    if bAddOrRemove then
        
        -- if CertainOrderWndDic[uiModuleName] then
        --     WinCurrentList[uiModuleName] = CertainOrderWndDic[uiModuleName]
        -- else 
        local uiLayer = GetUILayerBy(uiModuleName)
        local layerUiCount = list[uiLayer]  --当前层级内ui的数量

        list[uiModuleName] = layerUiCount
        list[uiLayer] = layerUiCount + 1
        --list[uiModuleName] = list.ind
        -- end
        --list.ind = list.ind + 1
    else
        list[uiModuleName] = nil
    end
end
function WindowMgr:CurrentInd(uiModuleName)
    local list = WinCurrentList
    if uiModuleName then
        if CertainOrderWndDic[uiModuleName] then
            return CertainOrderWndDic[uiModuleName]
        else 
            return list[uiModuleName] or 1
        end
        
        -- return list[uiModuleName] or 1
    else
        return list.ind
    end
end

--获取ui所属层级
function GetUILayerBy(uiModuleName)
   --  --print("WindowMgr.GetUILayerBy>> name="..tostring(uiModuleName))

    --无实体层处理
    if NoEnityWndDic[uiModuleName] then 
        if moduleDefaultLayer[uiModuleName] then 
            return moduleDefaultLayer[uiModuleName]
        else
            return const.UI_LAYER_MODULE
        end
    end

    local uiModile = require(uiModuleName)
    if uiModile == nil then
        log.Warning("WindowMgr.GetUILayerBy>> 尝试显示一个不存在的UI模块, name="..tostring(uiModuleName))
        return const.UI_LAYER_MODULE --缺省返回模块层
    end
    local layerType = nil
    if uiModile.GetUILayerType then 
        layerType = uiModile.GetUILayerType() --获取所属层级类型
    end
    if layerType then
        -- --print("获取到模块 layer:", layerType, " uiModuleName:", uiModuleName)
        return layerType
    elseif moduleDefaultLayer[uiModuleName] then 
        --ui没有设置uilayer时，使用缺省配置
        return moduleDefaultLayer[uiModuleName]
    else
        --缺省返回模块层
        return const.UI_LAYER_MODULE 
    end
end

--获取ui是否为全屏界面
function IsFullUIBy(uiModuleName)
    -- --print("WindowMgr.IsFullUIBy获取ui是否为全屏界面>> name="..tostring(uiModuleName))
 
     --无实体层处理
     if NoEnityWndDic[uiModuleName] then 
         if defaultFullScreenModules[uiModuleName] then 
             return true
         else
             return false
         end
     end
 
     local uiModile = require(uiModuleName)
     if uiModile == nil then
         --log.Warning("WindowMgr.GetUILayerBy>> 尝试显示一个不存在的UI模块, name="..tostring(uiModuleName))
         return false --缺省返回false
     end
     local isFullUI = nil
     if uiModile.IsFullUI then 
        return uiModile.IsFullUI() --获取所属层级类型
     end
     
     if defaultFullScreenModules[uiModuleName] then 
        return true
    else
        return false
    end
 end
---@public 获取ui是否为全屏界面 外部接口
function WindowMgr.IsFullUIBy(uiModuleName)
    return IsFullUIBy(uiModuleName)
end

function WindowMgr:ResetCurrentInd()

    ---- --print("resetscurrentind")
    self.OpenIndList = {}

    -- --print("ResetCurrentInd befor:", WinCurrentList[const.UI_LAYER_SCENE], WinCurrentList[const.UI_LAYER_SCENE_UI], 
    --   WinCurrentList[const.UI_LAYER_MODULE], WinCurrentList[const.UI_LAYER_TOP])

    --重置所有layer队列长度
    WinCurrentList[const.UI_LAYER_SCENE] = uiLayerStartIndex[const.UI_LAYER_SCENE]
    WinCurrentList[const.UI_LAYER_SCENE_UI] = uiLayerStartIndex[const.UI_LAYER_SCENE_UI]
    WinCurrentList[const.UI_LAYER_MODULE] = uiLayerStartIndex[const.UI_LAYER_MODULE]
    WinCurrentList[const.UI_LAYER_TOP] = uiLayerStartIndex[const.UI_LAYER_TOP]

    self.dealCurrentInd = 0
    local list =self:CurrentList()
    local layerType
    local sourceIndex
    
    for i, v in ipairs(list) do
        -- if CertainOrderWndDic[v] then
        --     WinCurrentList[v] = CertainOrderWndDic[v]
        -- else
        -- sourceIndex = WinCurrentList[v]
        -- layerType = GetUILayerTypeByIndex(sourceIndex)
        layerType = GetUILayerBy(v)
        --重置ui层数
        WinCurrentList[v] = WinCurrentList[layerType]
        WinCurrentList[layerType] = WinCurrentList[layerType] + 1
        -- end
    end

    -- --print("ResetCurrentInd:", WinCurrentList[const.UI_LAYER_SCENE], WinCurrentList[const.UI_LAYER_SCENE_UI], 
     --  WinCurrentList[const.UI_LAYER_MODULE], WinCurrentList[const.UI_LAYER_TOP])
    --WinCurrentList.ind = #list + 1 + START_LAYER_IND

    --- todo   遍历调整成根据ind顺序来,保持显示上的一致性
    for key, value in pairs(WindowObjs) do
        -- statements
        -- local ind = self:CurrentInd(key)
        -- local order = ind and ind * ATTACH_PANEL_ITL * ORDER_ITL
        self:SetOpenInd(key)
        self:SortModule(key)
    end
end

--通过ui层数获取层类型
function GetUILayerTypeByIndex(index)
    local prevLayerType = const.UI_LAYER_MODULE
    if index >= uiLayerStartIndex[const.UI_LAYER_TOP] then 
        prevLayerType = const.UI_LAYER_TOP
    elseif index >= uiLayerStartIndex[const.UI_LAYER_MODULE] then 
        prevLayerType = const.UI_LAYER_MODULE
    elseif index >= uiLayerStartIndex[const.UI_LAYER_SCENE_UI] then 
        prevLayerType = const.UI_LAYER_SCENE_UI
    elseif index >= uiLayerStartIndex[const.UI_LAYER_SCENE] then 
        prevLayerType = const.UI_LAYER_SCENE
    end
    return prevLayerType
end

--获取CurrentList中所有ui累加的数量
function GetCurrentListAllIndex()
    local count = WinCurrentList[const.UI_LAYER_SCENE] - uiLayerStartIndex[const.UI_LAYER_SCENE]
    count = count + WinCurrentList[const.UI_LAYER_SCENE_UI] - uiLayerStartIndex[const.UI_LAYER_SCENE_UI]
    count = count + WinCurrentList[const.UI_LAYER_MODULE] - uiLayerStartIndex[const.UI_LAYER_MODULE]
    count = count + WinCurrentList[const.UI_LAYER_TOP] - uiLayerStartIndex[const.UI_LAYER_TOP]
    return count
end

--检测主动打开的ui层队列中是否有达到上限
--如果达到上限则重置所有ui的层数
function CheckCurrentListLayerMaxIndex()
    if WinCurrentList[const.UI_LAYER_SCENE] >= uiLayerStartIndex[const.UI_LAYER_SCENE] + uiLayerOffset or 
        WinCurrentList[const.UI_LAYER_SCENE_UI] >= uiLayerStartIndex[const.UI_LAYER_SCENE_UI] + uiLayerOffset or 
        WinCurrentList[const.UI_LAYER_MODULE] >= uiLayerStartIndex[const.UI_LAYER_MODULE] + uiLayerOffset or 
        WinCurrentList[const.UI_LAYER_TOP] >= uiLayerStartIndex[const.UI_LAYER_TOP] + uiLayerOffset
    then 
        return true
    end
    return false
end
local list2 = {}
function WindowMgr:CurrentList()
    local list = WinCurrentList

    for key, value in pairs(list2) do
        list2[key] = nil
    end
    
    local ind = 1
    --local len = list.ind
    for key, value in pairs(list) do
        if type(key) == "string" and key ~= "ind" then
            list2[ind] = key
            ind = ind + 1
        end
        --if ind >= len then break end
    end
	table.sort( list2	, function ( a,b )
		if not a or not b then return false end
        local del =  list[b] - list[a]
        if del ~= 0 then
            return del > 0
        end
		return false
    end )
    return list2
end

--- state open list  -open -attach
openlist = {
    ["ui_lobby"] = { 
        -- "ui_lobby",
        -- "ui_menu_cg",
        "ui_menu_top",
        "ui_menu_bot",
        -- "ui_menu_side",
   },
    --    ["ui_package"] = {
    --     --    "ui_bag_bg",
    --         "ui_package",
    --         -- "ui_menu_top",
    --         -- "ui_menu_bot",
    --    },
   ["ui_chat_main_new"] = {
    --    "ui_bag_bg",
        -- "ui_chat_main_new",
        -- "ui_menu_top",
        "ui_menu_bot",
   },
   ["ui_hero_impl"] = {
        -- "ui_hero_impl",
        "ui_menu_top",
        "ui_menu_bot",
   },
--    ["ui_task_achievement"] = {
--         "ui_task_achievement",
--         -- "ui_menu_top",
--         -- "ui_menu_bot",
--    }, 
--    ["ui_market_new"] = {
--        --"ui_bag_bg",
--        "ui_market_new",
--    },     
--    ["ui_sociaty_main"] = {
--         -- "ui_sociaty_main",
--         "ui_menu_top",
--         "ui_menu_bot",
--    }, 
	["ui_hero_viewing"] = {
        "ui_hero_viewing",
   }, 
   ["ui_union_scene"] = {
        "ui_menu_top",
        "ui_menu_bot",
        -- "ui_sociaty_medal_entrance",
   },
   ["ui_union_list"] = {
    --    "ui_sociaty_list",
        "ui_menu_bot",
   },
   ["ui_market_scene"] = {
    "ui_bag_bg",
    "ui_market_new",
},
   --模块内聊天虚拟界面 不显示主界面底部框
   ["ui_chat_main_module"] = {
        "ui_chat_main_new",
    },

    ["ui_hub_city"] = {
        "ui_lobby",
        "ui_menu_bot",
        "ui_menu_top",
    },
    ["ui_hub_adventure"] = {
        "ui_lobby",
        "ui_menu_bot",
        "ui_menu_top",
    },
    ["ui_hub_hunt"] = {
        "ui_lobby",
        "ui_menu_bot",
        "ui_menu_top",
    },
    ["ui_hub_hero"] = {
        "ui_hero_impl",
        "ui_menu_bot",
        "ui_menu_top",
    },
    -- ["ui_dimension_war_main"] = 
    -- {
    --     "ui_menu_top",
    -- },
}

function WindowMgr:AddOpenList(uiModuleName,subModuleName)
    local isAdd = true
    local olist = openlist[uiModuleName]
    if olist then
        for kk, vv in pairs(olist) do
            if vv == subModuleName then
                isAdd = false
                break
            end
        end
        if isAdd then
            table.insert( openlist[uiModuleName], subModuleName)
        end
    end
end

function WindowMgr:RemoveOpenList(uiModuleName,subModuleName)
    local isRemove = false
    local pos = nil
    local olist = openlist[uiModuleName]
    if olist then
        for kk, vv in pairs(olist) do
            if vv == subModuleName then
                isRemove = true
                pos = kk
                break
            end
        end
        if self:IsModuleShown(subModuleName) then
            local st = self:GetState(subModuleName)
            if st.state == WIN_SHOW then
                local vo = self:GetState(subModuleName)
                vo.state = WIN_NO_SHOW
            end
            self:DelayCheckState()
        end
        if isRemove then
            table.remove(openlist[uiModuleName], pos )
        end
    end
end

function WindowMgr.GetOpenList()
    return openlist
end

function WindowMgr:OpenList(uiModuleName)
    return openlist[uiModuleName]
end
--- state contain list  -open
    -- PARREL_NONE = 100000
    -- PARREL_ALL = 200000
    STATE_1 = 2 
    STATE_2 = 4 
    STATE_ITL = 1000 
    ORDER_ITL = 30
    ATTACH_PANEL_ITL = 5
statelayerlist = {
    2,
    4,
    8,
    16,
}
local Max_UI_MODULE_LAYER = ATTACH_PANEL_ITL * ORDER_ITL * uiLayerStartIndex[const.UI_LAYER_TOP] -1
statelist = {
    ["ui_time_loading"] = STATE_1 + STATE_2,
    ["net_reloading"] = STATE_1 + STATE_2,
    ["ui_hero_impl"] = STATE_1,
    -- ["ui_package"] = STATE_1  ,
    ["ui_dungeon_pose"] = STATE_1  ,
    -- ["ui_select_hero"] = STATE_1  ,
    ["ui_lobby"] = STATE_1  ,
    ["ui_create_array"] = STATE_1  ,
    -- ["ui_illusion_tower"] = STATE_1  ,
    -- ["ui_dungeon"] = STATE_1  ,
    ["ui_skill_package"] = STATE_1  ,
    -- ["ui_brave_practice"] = STATE_1  ,
    ["ui_hero_decompose"] = STATE_1  ,
    ["ui_timearea_main"] = STATE_1  ,
    -- ["ui_task_achievement"] = STATE_1  ,
    --["ui_sociaty_main"] = STATE_1  ,
    ["ui_union_scene"] = STATE_1  ,--联盟界面和大厅界面互斥
    ["ui_chat_main_new"] = STATE_1  ,
    ["ui_novice_tournament"] = STATE_2,
    ["ui_in_battle"] = STATE_1,--战斗界面和大厅界面互斥
} 

--- todo 支持多层区分互斥 目前以state值互斥
function CheckStateLayer(statename,statelayer)
    local state = statelist[statename] or 0
    local cState = statelayerlist[statelayer] or 1
    local bInLayer = state % (cState*2 -1) >= cState

    return bInLayer

end

-- 界面打开超时不处理列表
timeoutExcludelist = {
    ["ui_lobby"] = 1,
    ["ui_login"] = 1,
    ["ui_login_main"] = 1,
    ["ui_iuser_terms"] = 1,
    ["new_hook_scene"] = 1, --处理手机打开狩猎界面 超时黑屏问题
    ["flow_tips"] = 1, 
    
} 

-- 无实体层
NoEnityWndDic = {
    ["ui_union_list"] = 1, 
    ["ui_union_scene"] = 1, 
    ["ui_chat_main_module"] = 1,
    ["ui_market_scene"] = 1,
    

    ["ui_hub_city"] = 1,
    ["ui_hub_adventure"] = 1,
    ["ui_hub_hunt"] = 1,
    ["ui_hub_hero"] = 1,
    ["ui_casual_game"] = 1,
} 
-- 特定排序层
CertainOrderWndDic = {
    --["new_hook_scene"] = 1, 
    -- ["ui_lobby"] = 2, 
} 
-- 检测界面显示个数忽略列表,个数为0会触发跳转主界面或者登录界面
CheckCountExcludelist = {
    ["ui_time_loading"] = 1,
    ["flow_tips"] = 1,
    ["ui_shield"] = 1,
    ui_ling_shi_new = 1
} 
CacheUiList = {
    -- ["ui_time_loading"] = 1 ,
    ["net_reloading"] = 1 ,
    ["ui_hero_impl"] = 1,
    ui_package_gw = 3,
    ["ui_package"] = 3  ,
    ["ui_dungeon_pose"] = 5  ,
    ["ui_select_hero"] = 1  ,
    ["ui_lobby"] = 1  ,
    ["ui_create_array"] = 1  ,
    -- ["ui_illusion_tower"] = 5  ,
    -- ["ui_dungeon"] = 5  ,
    ["ui_skill_package"] = 5  ,
    ["ui_hero_decompose"] = 5  ,
    ["ui_task_achievement"] = 1  ,
    -- ["ui_sociaty_main"] = 3  ,
    ["ui_chat_main_new"] = 5  ,
    ["ui_menu_top"] = 1  ,
    ["ui_menu_bot"] = 1  ,
    ["ui_menu_side"] = 1  ,
    ["new_hook_scene"] = 1  ,
    ui_gw_hero_list = 3,
    ui_sand_marching_team = 3,
    ui_bindprompt_impl  		=5,
    ui_special_gift_bag         =3,
    flow_notice                 =1,
    flow_tips                   =1,
    ui_frist_rechage            =4,
    ui_lost_weapon              =5,
    ui_target_task              =5,
    ui_festival_activity_base   =3,
    ui_online_reward            =3,
    ui_select_diamond           =4,
    ui_gift_new                 =3,
    -- ui_faction_wanted_entrance =4,
    ui_soul_link               =5,
    -- ui_dungeon_levels          =4,
    ui_rank_base               =5,
    ui_market_new              =3,
    ui_matchplace_entrance     =5,
    ui_halo                    =1,
    ui_soul_link_select_hero   =3,
    -- ui_maze_hero   =4,
    ui_activity_broken_time    =5,
    ui_common_mini_game_level  =3,
}

---@decription 设置缓存UI等级
function WindowMgr.SetCacheUI(moduleName,level)
    if moduleName then
        CacheUiList[moduleName] = level or 1
    end
end
 

function WindowMgr:IsCacheUI(name)
    if not name then return end

    return OPEN_CACHE_WINDOW_RES and CacheUiList[name] and CacheUiList[name] <= idle.MEM_LEVEL
end


function WindowMgr:State(uiModuleName)
    return statelist[uiModuleName]
end
--- state exclude list -open
 
--- state active  -close nostate list

--- main state list -open 
----- public 
--- open 
--- attach 
--- pop 
--- push 

------+---



function WindowMgr:Create()
    -- log.Warning("WindowMgr Create")
    self.wndsHideOnShow = {}

    self.disableCheckNoWindow = true

    local logger = require("logger").new("sw_window_mgr_log")
    IsLogLevel = logger.IsLogLevel
    Warning = logger.Warning
    Warning0 = logger.Warning0


    
    event.Register(event.STATE_ENTER, _OnEventStateEnter)
    event.Register(event.STATE_LEAVE, _OnEventStateLeave)
    
    event.Register(event.UI_MODULE_INIT_BASE, _OnEventInitBase)
    event.Register(event.UI_MODULE_INIT, _OnEventUIModuleInit)
    event.Register(event.UI_MODULE_SHOW, _OnEventUIModuleShow)
    --event.Register(event.UI_MODULE_HIDE, _OnEventUIModuleHide) Hide流程不维护
    event.Register(event.UI_MODULE_CLOSE, _OnEventUIModuleClose)
    event.Register(event.UI_MODULE_REMOVE_OBJ, _OnEventUIModuleRemoveObj)
    event.Register(event.UI_MODULE_PREPARE_CLOSE, _CheckIsMainUI)
    event.IsCacheUI = handleFunc(self,self.IsCacheUI)

    RegisterConsole()

    if self.updateOpen then
        util.RemoveDelayCall(self.updateOpen)
        self.updateOpen = nil
    end
    self.updateOpen = util.DelayCallOnce(0, update_window)
    
    self.openind = 1
    local time_checker = require "time_checker" 
    self.tChecker = time_checker()
    self.canvasMesh = GameObject.Find("/UIRoot/CanvasWithMesh")
    self.canvasMeshT = self.canvasMesh.transform
    self.meshCanvas = self.canvasMesh:GetComponent(CanvasType)
    self.curFullState = false
    local g = self.canvasMesh
    local gCache = GameObject.Instantiate(g,self.canvasMeshT.parent,false)
    gCache.name = "gCache"
    gCache:SetActive(false)
    self.gCache = gCache.transform


    self.tChecker.onTick = function (key,time)
        if self.disableCheckNoWindow then
            return
        end

        if key and timeoutExcludelist[key] then 
            -- --print("timeoutExcludelist:",key)
            return
        end
        if key == "NO_WINDOW" then 
            if (time - 2000) >util.TICK_ZERO then
                -- --print("OnTimeTick2",key)
                self.tChecker:Remove(key)
                self.tChecker.isLoading = false
                self:UnloadModuleImmediate("ui_time_loading")
                -- --print("onTick",game.GetCurState() )
                if game.GetCurState() == game.STATE_LOGINED or game.GetCurState() == game.STATE_MAKE_MATCH_V then
                    --- close all 
                    self:CloseAllUIAndScene()
                    --马甲包小游戏关闭判断
                    local puzzlegame_mgr = require "puzzlegame_mgr"
	                if puzzlegame_mgr.IsSockPackage() then
                        if  not puzzlegame_mgr.getIsOpenMiniGame() then
                            if not self:IsModuleShown("ui_sock_package_menu") then
                                self:ShowModule("ui_sock_package_menu")
                            end
                        end
                    else
                        --self:ShowModule("ui_lobby")
                        --event.Trigger(event.GW_OPEN_SAND_MAIN)
                    end
                    
                else 
                    local ui_login_main = require "ui_login_main"
                    ui_login_main.ReturnLogin()
                end
                return
            end
            if (time - 1000) >util.TICK_ZERO then
                -- --print("OnTimeTick1",key)
                self.tChecker.isLoading = true
                self:ShowModuleImmediate("ui_time_loading")
                return
            end
            return
        end
        if (time - 5000) >util.TICK_ZERO then
             --print("OnTimeTick5",key)
            log.Error("Load UI Timeout! >>>>>> ",key,time)
            
            local ub = self:GetWindowObj(key)
            event.Trigger(event.GAME_EVENT_REPORT, "ui_timeout", {
                ui_name=key,
                desc=ub and ub.loader and ub.loader:Des()
            })

            self.tChecker:Remove(key)
            self:UnloadModuleImmediate(key)
            self.tChecker.isLoading = false
            self:UnloadModuleImmediate("ui_time_loading")
            return
        end
        if (time - 2000) >util.TICK_ZERO then
            --  --print("OnTimeTick1",key)
            self.tChecker.isLoading = true
            if not self:IsModuleShown("ui_time_loading") then
                self:ShowModuleImmediate("ui_time_loading")
            end
            return
        end
    end 

end

function WindowMgr:DisableCheckNoWindow()
    self.disableCheckNoWindow = true
end

function WindowMgr:EnableCheckNoWindow()
    self.disableCheckNoWindow = nil
end


function WindowMgr:SetActive(gameObject, bActive, parent, position,uiLogic)
    if not gameObject or gameObject:IsNull() then return end

    if const.OPEN_EXIST_UI then
        if bActive then
            position = position or const.UI_SHOW_POSITION
        else
            position = const.UI_HIDE_POSITION
        end

        if uiLogic then
            if (position == const.UI_HIDE_POSITION or position == const.UI_HIDE_POSITION_OF_FULL_SCENE) and not bActive then
                -- 添加到列表
                -- 是否再列表中
                -- gameObject 是否相等           
                local isWait = self:IsWaitUIHideOrDestory()
                local waitData  = WaitHideModuleDic[uiLogic.UIModuleName]
                if logOpenExistUI then
                    log.Warning("window state SetActive position:",uiLogic.UIModuleName,"isWait",isWait)
                end
                -- Warning(2,"window state SetActive position:",uiLogic.UIModuleName,"isWait",isWait)
                -- 添加到列表
                if isWait then
                    local gameObjectData
                    -- 是否再列表中
                    if not waitData then
                        gameObjectData = {}
                        waitData = {
                            uiModuleName = uiLogic.UIModuleName,
                            gameObjectData = gameObjectData
                        }
                        self:AddWaitHideList(waitData)
                    else
                        gameObjectData = waitData.gameObjectData
                    end

                    local gameObjTable = gameObjectData[gameObject]
                    -- gameObject 是否相等
                    if not gameObjTable then
                        gameObjTable = {}
                        gameObjectData[gameObject] = gameObjTable
                    end
                    gameObjTable.windowWaitType = WindowWaitType.Position
                    gameObjTable.gameObject = gameObject
                    gameObjTable.parent = parent
                    gameObjTable.bActive = bActive
                    gameObjTable.position = position

                    self:CheckWaitListHideOrDestory()
                else
                    self:RemoveWaitHideList(uiLogic.UIModuleName)
                    self:SetPosition(gameObject, bActive, parent, position,uiLogic.UIModuleName)
                end
            else
                -- 从列表移除
                self:RemoveWaitHideList(uiLogic.UIModuleName)
                self:SetPosition(gameObject, bActive, parent, position,uiLogic.UIModuleName)
            end
        else
            self:SetPosition(gameObject, bActive, parent, position)
        end
        return
    end
    if bActive then
        --gameObject.transform:SetParent(parent or self.canvasMeshT)
        gameObject.transform.anchoredPosition = position or const.UI_SHOW_POSITION
    else
        gameObject.transform.anchoredPosition = const.UI_HIDE_POSITION
        --gameObject.transform:SetParent(self.gCache)
    end
    --  --print("++++++++SetActive",bActive, gameObject.name)
    if logOpenClose then
        log.Warning("window state,WindowMgr:SetActive", gameObject.name, bActive)
    end
    -- Warning(1, "window state,WindowMgr:SetActive", gameObject.name, bActive)

end

function WindowMgr:WaitDestoryGameObject(UIModuleName,gameObject)
    if not gameObject or gameObject:IsNull() then return end
    local isWait = self:IsWaitUIHideOrDestory()
    local waitData  = WaitHideModuleDic[UIModuleName]
    if isWait then
        if logOpenExistUI then
            log.Warning("window state WaitDestoryGameObject :",UIModuleName,"isWait",isWait)
        end
        -- Warning(1, "window state WaitDestoryGameObject :",UIModuleName,"isWait",isWait)

        local gameObjectData
        if not waitData then
            gameObjectData = {}
            waitData = {
                uiModuleName = UIModuleName,
                gameObjectData = gameObjectData
            }
            self:AddWaitHideList(waitData)
        else
            gameObjectData =  waitData.gameObjectData
        end

        local gameObjTable = gameObjectData[gameObject]
        -- gameObject 是否相等
        if not gameObjTable then
            gameObjTable = {}
            gameObjectData[gameObject] = gameObjTable
        end
        gameObjTable.windowWaitType = WindowWaitType.Destory
        gameObjTable.gameObject = gameObject
        self:CheckWaitListHideOrDestory()
    else
        -- 从列表移除
        self:RemoveWaitHideList(UIModuleName)
        self:DestoryGameObject(gameObject)
    end
end

function WindowMgr:DestoryGameObject(gameObject)
    if not util.IsObjNull(gameObject) then
        if logOpenExistUI then
            log.Warning("window state,DestoryGameObject release", gameObject.name)
        end
        -- Warning(2, "window state,DestoryGameObject release", gameObject.name)

        GameObject.DestroyImmediate(gameObject)
    end
end

function WindowMgr:SetPosition(gameObject, bActive, parent, position,uiModuleName)
    if not gameObject or gameObject:IsNull() then return end
    if bActive then
        --gameObject.transform:SetParent(parent or self.canvasMeshT)
        gameObject.transform.anchoredPosition = position or const.UI_SHOW_POSITION
    else
        gameObject.transform.anchoredPosition = const.UI_HIDE_POSITION
        --gameObject.transform:SetParent(self.gCache)
        --if uiModuleName and WindowObjs[uiModuleName] then
        --    WindowObjs[uiModuleName]:SetCanvasGroup(0)
        --end
    end
    --  --print("++++++++SetActive",bActive, gameObject.name)
    if logOpenClose then
        log.Warning("window state,WindowMgr:SetPosition", gameObject.name, bActive,gameObject.transform.anchoredPosition.x)
    end
    -- Warning(1, "window state,WindowMgr:SetPosition", gameObject.name, bActive,gameObject.transform.anchoredPosition.x)

end


function update_window()
    local self = WindowMgr
    -- while true do
    if self.BDelayCheckState then 
        self.BDelayCheckState = false
        self:CheckState()
    end
    return update_interval
        -- coroutine.yield(0)
    -- end
end

function WindowMgr:Release()
    -- event.Unregister(event.UI_MODULE_INIT_BASE)
    event.Unregister(event.STATE_ENTER, _OnEventStateEnter)
    event.Unregister(event.STATE_LEAVE, _OnEventStateLeave)
    event.Unregister(event.UI_MODULE_INIT, _OnEventUIModuleInit)
    event.Unregister(event.UI_MODULE_SHOW, _OnEventUIModuleShow)
    --event.Unregister(event.UI_MODULE_HIDE, _OnEventUIModuleHide)
    event.Unregister(event.UI_MODULE_CLOSE, _OnEventUIModuleClose)
    event.Unregister(event.UI_MODULE_REMOVE_OBJ, _OnEventUIModuleRemoveObj)
    
    if self.updateOpen then
        util.RemoveDelayCall(self.updateOpen)
        self.updateOpen = nil
    end

    
end

function WindowMgr:GetAllWindowConfig()
    return WindowConfig
end

-- 以下界面存在时，不等待界面显示再关闭界面，
local ignoreWaitVisiblelist = {
    "ui_login_main",
}
function WindowMgr:IsWaitVisible()
    for k,m in pairs(ignoreWaitVisiblelist) do
        local ub = self:GetWindowObj(m)
        if ub and ub:IsUIExist() then
            -- log.Warning("IsWaitVisible",false,m)
            return
        end
    end
    -- log.Warning("IsWaitVisible",true)
    return true
end


function WindowMgr:CheckState()
    -- -- --print("CheckState")
    local currentListCount = GetCurrentListAllIndex()
    local isResetCurrentInd = CheckCurrentListLayerMaxIndex()
    --if WinCurrentList.ind > MAX_LAYER_IND then
    if currentListCount > MAX_LAYER_IND or isResetCurrentInd then
        -- --print("<color=#ffffff>ResetCurrentInd currentListCount：</color>", currentListCount, "isResetCurrentInd:", isResetCurrentInd)
        self:ResetCurrentInd()
    end


    local win2close = {}
    local winState = {}
    ----++ close ban state from currentlist according state
    local cList = self:CurrentList()
    
    -- dump(cList)
    -- dump(WinCurrentList)
    -- dump(WindowConfig)
    -- for key, value in pairs(WindowObjs) do
        
    --      --print("isShow",key)
    -- end
    for i, name in ipairs(cList) do
        local vo = self:GetState(name)
        if  vo.state == WIN_SHOW then
            local state = self:State(name)
            if state then
                winState[state] = name
            end
        end
    end

    local ind = 1
    for i, name in ipairs(cList) do
        local state = self:State(name)
        if winState[state] and winState[state]~= name then
            
            if logOpenClose then
                log.Warning("logOpenClose-state-close",state,winState[state],name)
            end
            -- Warning(1, "logOpenClose-state-close",state,winState[state],name)
            
            win2close[ind] = name
            ind = ind + 1
        end
    end
    
    for i, name in pairs(win2close) do
            -- --print("win2close-state",name)
        local vo = self:GetState(name)
        vo.state = WIN_NO_SHOW

        -- self:ModifyCurrent(name, false)
        -- self:UnloadModule(name)
    end
    ------ close ban state from currentlist


    -----++ open attach window via state ,遍历Open列表,子窗口对应父窗口设置显示ind,或添加关闭窗口
    WinAttachList = {}
    local win2Open = WinAttachList
    win2close = {}
    local win_check_m = {} -- 保持子模块与父模块的隐藏状态一致
    local openIndex = 0
    local maxFullUi = 0
    for k, v in pairs(WindowConfig) do
        
        local olist = self:OpenList(k)
        local cInd = self:CurrentInd(k)

        local m = nil
        -- 对外部打开界面更新层级
        if self:IsInCurrents(k) and  v.state == WIN_SHOW then
            win2Open[k] =math.max(win2Open[k] or 1,cInd * ATTACH_PANEL_ITL)
            openIndex = win2Open[k] * ORDER_ITL
            self:SetOpenInd(k, openIndex)
            if IsFullUIBy(k) then
                maxFullUi = openIndex > maxFullUi and openIndex or maxFullUi
            end
            if logCheckState then
                log.Warning("temp",k,WinCurrentList[k],win2Open[k] * ORDER_ITL)
            end
            -- Warning(1, "openlist",k,WinCurrentList[k],openIndex)
        end

        if  v.state == WIN_SHOW and olist then
            m = self:IsUIExist(k) and (self:IsUIVisible(k) and 1 or 2) or 3
            -- m = self:IsUIVisible(k) and "IsUIVisible" or "IsUIExist"
            -- --print("olist",k,self:IsUIExist(k),self:IsUIVisible(k),m)
            for kk, vv in ipairs(olist) do
                -- local ccInd = self:CurrentInd(k)
                -- if ccInd > 0 then
                --     win2Open[vv] = ccInd * 5
                -- else
                --     win2Open[vv] = cInd * 5 + kk  
                -- end
                local order = cInd * ATTACH_PANEL_ITL + kk  

                win2Open[vv] = win2Open[vv] or 1
                win2Open[vv] = math.max(order,win2Open[vv])
                if logCheckState then
                    log.Warning("WinAttachList",k,vv,cInd,ATTACH_PANEL_ITL,kk,win2Open[vv])
                end
                -- Warning(3, "WinAttachList",k,vv,cInd,ATTACH_PANEL_ITL,kk,win2Open[vv])

                openIndex = win2Open[vv] * ORDER_ITL
                self:SetOpenInd(vv, openIndex)
                if IsFullUIBy(k) then
                    maxFullUi = openIndex > maxFullUi and openIndex or maxFullUi
                end

                if win_check_m[vv] ~= 1 then
                    win_check_m[vv] = m
                end
               --  --print("olist---",k,vv,win_check_m[vv],m,self:IsUIExist(vv),self:IsUIVisible(vv))
            end
        end
        
        if  v.state ~= WIN_SHOW and olist then
            for kk, vv in pairs(olist) do
                win2close[vv] = true
            end
        end
    end

    -- local bDealCurrentInd = false
    -- for k, v in pairs(win2Open) do
    --     if not self.dealCurrentInd or self.dealCurrentInd <= v then
    --         local st = self:GetState(k)
    --         self:AddOpenInd(k)

    --         -- --print("win2Open",k,st.state)
    --         if st.state ~= WIN_SHOW then
    --             self:ShowModuleInternal(k)
    --         end
    --         self:SortModule(k)
    --         self.dealCurrentInd = v
    --         bDealCurrentInd = true
    --     end
    -- end
    -- if bDealCurrentInd then
    --     self.dealCurrentInd = self.dealCurrentInd+1
    -- end

    for k, v in pairs(win2Open) do
        local st = self:GetState(k)
        st.state = WIN_SHOW
        --  --print("win2Open",k,st.state,v)
    end
 

    -- set close state exclude who in open list
    for k, v in pairs(win2close) do
        if not win2Open[k] then
            local st = self:GetState(k)
            --  --print("win2close",k,st.state)
            if st.state == WIN_SHOW then
                -- self:UnloadModule(v)
                local vo = self:GetState(k)
                vo.state = WIN_NO_SHOW
            end
        end
    end

    self.CheckMethod = self.CheckMethod or {
        "IsUIVisible",
        "IsUIsExist",
        "IsUIsExist",
    }
    --- 汇总的子窗口打开列表 如果没有对应uibase则showmodule, v
    for k, v in pairs(win2Open) do

        local checkM = win_check_m[k] or 1
        local checkMthd =self[self.CheckMethod[checkM]]

        -- --print("ssss",k,checkM,checkMthd)
        
        local st = self:GetState(k)
        -- 对外部打开界面检测是否层级刷新
        if self:IsInCurrents(k) and  st.state == WIN_SHOW then
            if not self:IsNowOpenInd(k) then
                if logOrder then
                    log.Warning("IsNowOpenInd!!",k)
                end
                -- Warning(3, "IsNowOpenInd!!",k)

                self:SortModule(k)
                -- if self:IsUIsExist(k) then
                --     local wobj = WindowObjs[k]
                --     if wobj and (wobj.UpdateUI or wobj.OnValueChange) then
                --     else
                --         self:ShowModuleInternal(k)
                --     end
                -- else
                --     self:ShowModuleInternal(k)
                -- end not self:IsUIExist(k) then --
            end
        elseif st.state == WIN_SHOW and   not checkMthd(self,k) then -- self:GetWindowObj(k) then
            self:ShowModuleInternal(k)
        elseif st.state == WIN_SHOW and not self:IsNowOpenInd(k) then
            -- --print("ind miss:",k)
            if self:IsUIsExist(k) then
                local wobj = WindowObjs[k]
                if wobj and (wobj.UpdateUI or wobj.OnValueChange) then
                    self:SortModule(k)
                else
                    self:ShowModuleInternal(k)
                end
            else
                self:ShowModuleInternal(k)
            end
        elseif st.state == WIN_SHOW and checkM == 2 and self:IsUIVisible(k) then
            self:HideModule(k)
        end
        -- self:ApplyOpenInd(k)
        -- -- --print("win2Open",k,st.state)
    end
    ------open attach window


    -----++ current state control noshow win
    
    local bVisible = true
    for i, name in ipairs(cList) do
        local st = self:GetState(name)
        if st.state == WIN_SHOW then
            local allow = self:OpenList(name)
            bVisible = bVisible and self:IsUIsExist(name, allow)
            if not bVisible then 
                -- --print("bVisible check false:",name)
                self.tChecker:Check(name)
                break
            end
        end
    end

    local showCount = 0
    if not self:IsWaitVisible() or bVisible then
        for k, v in pairs(WindowConfig) do
            if v.state ~= WIN_SHOW then
                if WindowObjs[k] then
                    -- --print("win2close WIN_NO_SHOW",k)
                    self:UnloadModuleImmediate(k)
                end
            end

            
            -- if v.state == WIN_SHOW then
            --     if not CheckCountExcludelist[k] then
            --         showCount = showCount + 1
            --     end
            --     if WindowObjs[k] then
            --         local winObj = self:GetWindowObj(k)
            --         winObj:ActivateCanvas()
            --     end
            -- end
        end
        self:UnloadModuleUI()
        local nextFullState = false
        for k, v in pairs(WindowConfig) do
            if v.state == WIN_SHOW then
                
                -- if WindowObjs[k] then
                --     local winObj = self:GetWindowObj(k)
                --     winObj:ActivateCanvas()
                -- end

                --     showCount = showCount + 1
                -- else
                if WindowObjs[k] then
                    local winObj = self:GetWindowObj(k)
                    if winObj:IsUIVisible() then
                        -- --print("showcount",k,showCount)
                        if not CheckCountExcludelist[k] then
                            showCount = showCount + 1
                        end
                    end
                    -- if k == "ui_monsters_approaching_task_base" then
                    --     log.Warning("ui_monsters_approaching_task_base  CheckState1", Edump(subWndUIMap))
                    --     log.Warning("ui_monsters_approaching_task_base  CheckState2", k, winObj:IsUIExist() , not lingshiUIList[k] , not subWndUIMap[k])
                    -- end
                    --subWndUIMap,打开界面时，自己设置父物体的，不被全屏UI隐藏（后面可以继续拓展，目前只要设置父物体就不全屏隐藏，走父物体的隐藏）
                    if winObj:IsUIExist() and not lingshiUIList[k] and not subWndUIMap[k] then --使灵石不被全屏UI隐藏
                        if winObj.curOrder < maxFullUi then
                            if winObj:IsVisible() then 
                                -- --print("隱藏低层级ui！！！！", k, winObj.curOrder)
                                self:ActiveUIOfLowerThanFullScene(k, false)                                
                                -- self:HideModule(k)
                                -- fullUIHideList[k] = 1
                            end
                        elseif fullUIHideList[k] and winObj:IsVisible() then
                            -- --print("打开ui！！！！", k, winObj.curOrder)
                            self:ActiveUIOfLowerThanFullScene(k, true)
                            -- fullUIHideList[k] = nil
                            -- self:ShowModuleImmediate(k)
                        end
                        if IsFullUIBy(k) then
                            nextFullState = true
                            local screen_util = require "screen_util"
                            if screen_util.IsFoldableScreen() and not self:IsModuleShown("ui_shield") then
                                self:ShowModule("ui_shield")
                            end
                            --print("BBBBXZ1 当前存在全屏ui", k, winObj.curOrder,maxFullUi)
                        end
                    elseif k == "ui_in_battle" then -- 战斗特殊处理 虽然是自己设置父物体的 但是也做双侧遮罩和全屏隐藏
                        nextFullState = true
                        local screen_util = require "screen_util"
                        if screen_util.IsFoldableScreen() and not self:IsModuleShown("ui_shield") then
                            self:ShowModule("ui_shield")
                        end
                    end
                end

                if WindowObjs[k] then
                    local winObj = self:GetWindowObj(k)
                    winObj:ActivateCanvas()
                end
            end
        end
        --防止self.curFullState ==nil时判断错误 
        ---只有状态不同时才触发
        if not self.curFullState == nextFullState then        
            -- Warning(2, "全屏状态改变了：", self.curFullState, nextFullState)
            self.curFullState = nextFullState
            event.Trigger(event.UI_FULL_SCREEN_STATE_CHANGE, nextFullState)
            if not nextFullState then
                if self:IsModuleShown("ui_shield") then
                    self:UnloadModule("ui_shield")
                end
            end
        end
        --- 
        if showCount ==0 then 
            self.tChecker:Check("NO_WINDOW")
        else 
            
            if self.tChecker.isLoading then 
                self.tChecker.isLoading = false
                self:UnloadModuleImmediate("ui_time_loading")
            end
            self.tChecker:RemoveAll()
        end
    end
    -- --print("!fullUIHideList!")
    --dump(fullUIHideList)
    if not bVisible or showCount ==0  then 
        self:DelayCheckState()
    end
    --- manager raycaster

    -- GraphicRaycaster.BlockAll =  not bVisible or showCount ==0
    GraphicRaycaster.BlockAll =  util.GetUseBlockAll() and (not bVisible or showCount ==0)
--     --print("GraphicRaycaster.BlockAll",showCount,GraphicRaycaster.BlockAll)
    ------
 
    
end

function WindowMgr:GetCurFullState()
    return self.curFullState
end

--隐藏低于全屏界面层级的ui
function WindowMgr:ActiveUIOfLowerThanFullScene(uimodulename, isShow)
    local winObj = self:GetWindowObj(uimodulename)
    if not winObj:IsUIExist() then
        return
    end
    local uiRoot = winObj.UIRoot
    if util.IsObjNull(uiRoot) then
        return
    end
    
    if isShow then
        local originalPosition = fullUIHideList[uimodulename]
        --uiRoot.transform.anchoredPosition = originalPosition or const.UI_SHOW_POSITION
        WindowMgr:SetActive(uiRoot, isShow, nil, originalPosition or const.UI_SHOW_POSITION,winObj)
        fullUIHideList[uimodulename] = nil
        if winObj.OnFullUIShow then
            winObj:OnFullUIShow(true)
        end
        --print("显示全屏隐藏的ui：", uimodulename, originalPosition)
    else
        if not fullUIHideList[uimodulename] then 
            fullUIHideList[uimodulename] = uiRoot.transform.anchoredPosition
        end
        --uiRoot.transform.anchoredPosition = const.UI_HIDE_POSITION_OF_FULL_SCENE
        WindowMgr:SetActive(uiRoot, isShow, nil, const.UI_HIDE_POSITION_OF_FULL_SCENE,winObj)
        if winObj.OnFullUIShow then
            winObj:OnFullUIShow(false)
        end
        -- local json = require "dkjson"
        if logOpenExistUI then
            log.Warning("window state, 隐藏全屏隐藏的ui：", uimodulename, uiRoot.transform.anchoredPosition)
        end
        -- Warning(2, "window state, 隐藏全屏隐藏的ui：", uimodulename, uiRoot.transform.anchoredPosition)
        --local json = require "dkjson"
        --print("隐藏全屏隐藏的ui：", uimodulename, fullUIHideList[uimodulename],json.encode(const.UI_HIDE_POSITION_OF_FULL_SCENE))
    end
end

function WindowMgr:IsUIBlockBYFull(uimodulename)
    local obj = self:GetState(uimodulename)
    if obj.state == WIN_SHOW then
        return fullUIHideList[uimodulename]
    else
        return
    end
end

function WindowMgr:BlockScene()
    return GraphicRaycaster.BlockAll
end

function WindowMgr:IsUIsExist(uimodulename,allow)
    local bVisible = self:IsUIExist(uimodulename)
        
    if bVisible and allow then
        for k, v in pairs(allow) do
            bVisible = bVisible and self:IsUIExist(v)
            if not bVisible then 
                --  --print("IsUIsExist false:",v)
                break
            end
        end
    end
    
    return bVisible
end
function WindowMgr:IsUIExist(uimodulename)
    
    if NoEnityWndDic[uimodulename] then
        return true
    end

    local mainBase = self:GetWindowObj(uimodulename)
    local bVisible = mainBase and mainBase:IsUIExist()
    return bVisible
end

--function WindowMgr:IsUIsVisible(uimodulename,allow)
--    local bVisible = self:IsUIExist(uimodulename)
--        
--    if bVisible and allow then
--        for k, v in pairs(allow) do
--            bVisible = bVisible and self:IsUIVisible(v)
--            if not bVisible then 
--                --  --print("IsUIsExist false:",v)
--                break
--            end
--        end
--    end
--    
--    return bVisible
--end
function WindowMgr:IsUIVisible(uimodulename)
    
    if NoEnityWndDic[uimodulename] then
        return true
    end

    local mainBase = self:GetWindowObj(uimodulename)
    local bVisible = mainBase and mainBase:IsUIVisible()
    return bVisible
end

function WindowMgr:GetState(uiModuleName)
    local st = WindowConfig[uiModuleName] or {}
    WindowConfig[uiModuleName] = st
    return st
end
local function nil2Def(v,def)
    if nil == v then
        return def
    end
    return v
end

function WindowMgr:OnModuleShow(uiModuleName)
    -- -- --print("OnModuleShow",uiModuleName)
    local cfg = WindowRelationConfig[uiModuleName] 
    if cfg == nil then
        return 
    end
    
    local curState = game.GetCurState()
    
    --隐藏互斥窗口
    local closeWnds = cfg[1]
    if closeWnds ~= nil and table.getn(closeWnds) > 0 then
        self.wndsHideOnShow[uiModuleName] = {}
        for _,name in pairs(closeWnds) do
            if self:IsModuleShown(name) then
                self:HideModule(name)
                
                --记住打开时关闭的列表，用于在关闭的时候恢复
                self.wndsHideOnShow[uiModuleName][name] = curState
                
            end
            
        end
    end
    
    --打开连带窗口
    local openWnds = cfg[3]
    if openWnds ~= nil and table.getn(openWnds) > 0 then
        for _,name in pairs(openWnds) do
            if not self:IsModuleShown(name) then
                self:ShowModule(name)
            end
        end
    end
end

function WindowMgr:OnModuleHide(uiModuleName)
    local cfg = WindowRelationConfig[uiModuleName] 
    if cfg == nil then
        return 
    end

    local curState = game.GetCurState()

    --打开恢复窗口
    local openWnds = cfg[2]
    if openWnds == nil then
        openWnds = cfg[1]
    end
    if openWnds ~= nil and table.getn(openWnds) > 0 then
        local hideWndWhenShow = self.wndsHideOnShow[uiModuleName]
        if hideWndWhenShow ~= nil then
            for _,name in pairs(openWnds) do
                if self:IsModuleShown(name) == false and hideWndWhenShow[name] ~= nil  then
                    if hideWndWhenShow[name] == curState then   --必须状态也匹配
                        self:ShowModule(name)
                    else
                        --log.Log("WindowMgr:OnModuleHide>> 状态不正确，不能显示！name="..name)
                    end
                end
            end
        end
    end
    
    --连带关闭窗口
    local closeWnds = cfg[4]
    if closeWnds ~= nil and table.getn(closeWnds) > 0 then
        for _,name in pairs(closeWnds) do
            if self:IsModuleShown(name) == true then
                self:HideModule(name)
            end
        end
    end
    
    self.wndsHideOnShow[uiModuleName] = nil
    
end

function WindowMgr:OnModuleUnload(uiModuleName)

    WindowMgr.onUnloadCallback = WindowMgr.onUnloadCallback or {}
    local onhide = WindowMgr.onUnloadCallback[uiModuleName]

    if onhide then
        WindowMgr.onUnloadCallback[uiModuleName] = nil
        if type(onhide) == "function" then
            onhide(uiModuleName)
        else
            log.Error(uiModuleName,"传入参数有误 onHie 必须是函数function")
        end     
    end
    -- if self.onUnloadCallback[uiModuleName] then
	-- 	self.onUnloadCallback[uiModuleName]()
	-- end
end

function WindowMgr:IsModuleExist(uiModuleName)
    if const.USE_MAIN_SLG and uiModuleName == "ui_lobby" then
        
        uiModuleName = const.uiMainName
    end
    return WindowObjs[uiModuleName]
end
--[[显示一个UI模块_New]]
function WindowMgr:ShowModulePure(uiModuleName, data)
    local onShow = data and data.onShow
    local onHide = data and data.onHide --注意 hide目前不建议使用，但沿用保留
    self:ShowModule(uiModuleName, onShow, onHide,data)
end
--[[显示一个UI模块_New]]
function WindowMgr:ShowModuleImmediatePure(uiModuleName,data)
    local onShow = data and data.onShow
    local onHide = data and data.onHide --注意 hide目前不建议使用，但沿用保留
    self:ShowModuleImmediate(uiModuleName, onShow, onHide,data)
end
--[[显示一个UI模块]]
function WindowMgr:ShowModule(uiModuleName, onshow, onHide,data)
   
    if const.IsOldMainUI(uiModuleName) then
        return
    end
    --if data ~= nil then
    --    local str = ""
    --    for k,v in pairs(data) do
    --        str = str .. tostring(k) .. "=" .. tostring(v) .. "\n "
    --    end
    --    log.Error("ShowModule:", uiModuleName, "data=",str)
    --end
    -- local net_route = require "net_route"
    -- net_route.SaveUIData(uiModuleName,uiModuleName,"ShowModule")   
    -- --print("ShowModule",uiModuleName)
    local st = self:GetState(uiModuleName)
    st.state = WIN_SHOW
    GraphicRaycaster.BlockAll = true
    self:RemoveWaitHideList(uiModuleName)
    self:ModifyCurrent(uiModuleName, true)
    local win = self:ShowModuleImmediate(uiModuleName, onshow, onHide,data)
    -- if st.allow2show then 
    --     for i, mN in ipairs(st.allow2show) do
    --         self:ShowModule(mN)
    --     end
    -- end

    
    if uiModuleName ~= "ui_time_loading" and uiModuleName ~= "ui_select_hero" then--预防打开英雄选择面板时清空了打开结算面板设置
        event.Trigger(event.SHOW_RESULT_MODULE, uiModuleName) 
    end

    self:DelayCheckState()


    --界面点击打点（包含主动弹出）
    local mapLv = laymain_data.GetPassLevel()
    if mapLv < 60 then
        local player_mgr = require "player_mgr"
        local roleName = tostring(player_mgr.GetRoleName()) or ""
        local json = require "dkjson"
        local json_str = json.encode({
            interface_id = uiModuleName,
            role_name = roleName
        })
        event.Trigger(event.GAME_EVENT_REPORT, "click_interface_id", json_str)
    end

    --自动开关主界面
    if win and win.delayCloseMain then
        local main_slg_mgr = require "main_slg_mgr"
        main_slg_mgr.DelayCloseMainUI(uiModuleName)
    end
    return win
end

--[[显示一个UI模块]]
function WindowMgr:ShowModuleInternal(uiModuleName, onshow, onHide,data)
    self:ShowModuleImmediate(uiModuleName, onshow, onHide,data)
end
--相对showmodule不会保存打开状态,适用于隐藏界面的重新显示
--[[显示一个UI模块]]
function WindowMgr:ShowModuleImmediate(uiModuleName, onshow, onHide,data)

    if const.IsOldMainUI(uiModuleName) then
        return
    end
    --View层直接自己传，不缓存
    --self:SetUIViewData(uiModuleName,data,0)
    if logOpenClose then
        log.Warning("ShowModuleImmediate",uiModuleName)
    end
    -- Warning(2, "ShowModuleImmediate",uiModuleName)

    if NoEnityWndDic[uiModuleName] then
        local st = self:GetState(uiModuleName)
        return st
    end

    event.Trigger(event.CHECK_FORCE_GUIDE_SYSTEM,uiModuleName) 
 
    --获得模块
    local m = require(uiModuleName)
    if m == nil then
        log.Warning("WindowMgr:ShowModule>> 尝试显示一个不存在的UI模块, name="..tostring(uiModuleName))
        return
    end 
    local st = self:GetState(uiModuleName)
    st.state = WIN_SHOW
    self:RemoveWaitHideList(uiModuleName)
    -- if st.allow2show or not self.lastMainState  then 
    --     self.lastMainState = uiModuleName
    -- end

    -- self:AddOpenInd(uiModuleName)
    

    --假如存在禁止该模块打开的模块，则不打开了
 
    self.onShowCallback = self.onShowCallback or {}
    if onshow then
        self.onShowCallback[uiModuleName] = onshow
	end
    self.onUnloadCallback = self.onUnloadCallback or {}
    if onHide then
        self.onUnloadCallback[uiModuleName] = onHide
	end
    local cfg = WindowConfig[uiModuleName]
    
    --显示模块
    local window = nil
     
    -- if WindowCacheObjs[uiModuleName] then
    --     m.window = WindowCacheObjs[uiModuleName][1]
    -- end

    -- 目前uibase创建实例 和 开始显示 生命周期没有拆分, 无法在show之前,初始化curOrder给uibase使用,当前使用windowmgr.curOrder 做同步中转,在show函数内事件触发绑定该数据
    self:SetOpenInd(uiModuleName)
    self.curOrder = self.OpenIndList[uiModuleName]
    if m.Show then
        --设置静态数据
        self:SetUIParaData(m,data,0)
        --这里Show了俩次，没有去修改以前的设定
        window = m["Show"](data)        
        if window and window.__ui_occupied then
            -- local env = getfenv(m)
            -- env.window = nil
            -- window = m["Show"]()
            -- local _uiModuleName = uiModuleName
            -- util.DelayCallOnce(0,function (  )
            --      --print("WinMGR delay Show()",_uiModuleName)
            -- end)
            self:UnloadModule(uiModuleName)
            self:DestoryGameObject(window.UIRoot)

            if m["SetWindow"] then
                m["SetWindow"](nil)
            end
            window = m["Show"](data)
            if window and window.__ui_occupied then
                self:UnloadModule(uiModuleName)
                self:DestoryGameObject(window.UIRoot)
            end
             --print("WinMGR Show()",uiModuleName)
            return
        end
    end
         
    -- if WindowCacheObjs[uiModuleName] then
    --     WindowCacheObjs[uiModuleName][1] = window
    --     window.recycle_ui = true
    -- end

    --保存窗口对象
    WindowObjs[uiModuleName] = window

    -- window.curOrder = self.OpenIndList[uiModuleName]

    --绑定模块
    if window == nil then
        log.Warning("WindowMgr:ShowModule>> 显示UI模块失败, name="..tostring(uiModuleName))
        
    else
        --绑定模块名称
        window.UIModuleName = uiModuleName
    end

    self:ApplyOpenInd(uiModuleName)
    
    self:OnModuleShow(uiModuleName) 
    -- if uiModuleName ~= "ui_time_loading" and uiModuleName ~= "ui_select_hero" then--预防打开英雄选择面板时清空了打开结算面板设置
    --     event.Trigger(event.SHOW_RESULT_MODULE, uiModuleName) 
    -- end
    return window
    
end

function Destroy(go)
    --UIRoot可能为空
    if not util.IsObjNull(go) then
        GameObject.DestroyImmediate(go)
    end
end

function OnErrCall(err,uiModuleName,...)    
    log.Error( "UIWindowMgr ERROR:",uiModuleName,err,... ) 
    util.AssetBundleManagerTrackEvent("lua_err",{
        type = "window_on_error",
        err = err,
        ui_name = uiModuleName,
        trackback = debug.traceback(),
    })
end
function safeCallClose(funcShow,uiModuleName,data)
    -- -- --print("safeCallClose",uiModuleName)
    function func()
        funcShow(data)
    end
    local check,result = xpcall(func,
            function( err)
                OnErrCall(err,uiModuleName)
            end )
    if check then
        return result
    else
        WindowMgr:HideModule(uiModuleName)
    end
end

-- 是否展示的ui
function WindowMgr:IsUIVisibleAndZeroPos(uimodulename)
    if NoEnityWndDic[uimodulename] then
        local st = self:GetState(uimodulename)
        if st then
            return true
        end
    end
    local mainBase = self:GetWindowObj(uimodulename)
    local bVisible = false
    if not self:IsTopUI(uimodulename) then
        return bVisible
    end
    bVisible = mainBase and mainBase:IsUIVisible() and mainBase:IsUIVisibleAndZeroPos()
    return bVisible
end

-- 忽略无实体的窗口
local checkIgnoreList = {
    ["ui_menu_top"] = 1,
    ["ui_menu_bot"] = 1,
    ["ui_lobby"] = 1,
    -- ["ui_login"] = 1,
    -- ["ui_login_main"] = 1,
    ["ui_button_tips"] = 1,
    ["ui_pointing_target"] = 1,
    ["ui_guide_assistant_new"] = 1,
    ["ui_guide_bubble"] = 1,
    ["ui_guide_mask"] = 1,
    -- ["ui_peak_talk"] = 1,
    ["ui_move_finger"] = 1,
}
-- uilist列表中 show的个数
function WindowMgr:GetShowWindowCount()
    -- local new_scene_mgr = require "new_scene_mgr"
    local showCount = 0
    local showUIModuleName = nil
    for k, v in pairs(WindowConfig) do
        -- if NoEnityWndDic[k] then
        --     print("============== : k1 ",k ,"state:","obj==",self:IsUIVisible(k))
        --     if self:IsUIVisible(k) then
        --         return false
        --     end
        -- end
        if v.state == WIN_SHOW then
            if WindowObjs[k] and not checkIgnoreList[k] then
                -- print("============== : k1 ",k ,"state:","obj==",self:IsUIVisible(k))
                if self:IsUIVisibleAndZeroPos(k) then
                    showCount = showCount + 1
                    if logOpenExistUI then
                        log.Warning("WIN_SHOW k :",k,showCount)
                    end
                    -- Warning(2, "WIN_SHOW k :",k,showCount)

                    showUIModuleName = k
                end
            end
        end
    end
    -- if logOpenExistUI then
    --     log.Warning("showCount :",showCount,"showuiName:",showUIModuleName,"/scene isEmpty :",new_scene_mgr.GetSceneIsEmpty())
    -- end
    -- 无windows  与 场景空
    if showUIModuleName == nil  then
        showCount = 0
        -- log.Warning("unload scene isSceneIsEmpty",showUIModuleName)
    end

    -- 如果windows 为空,场景切换不为空 可以=1 默认场景为window底 todo: add new scene empty check
    if showCount == 0  then
        -- showCount = 1
    end

    return showCount,showUIModuleName,showCount == 0
end

-- 是否等待隐藏HideMuodule
function WindowMgr:IsWaitUIHideOrDestory()
    local showCount,showUIModuleName = self:GetShowWindowCount()
    return showCount == 0
end

-- 检测等待隐藏与删除物体
function WindowMgr:CheckWaitListHideOrDestory()
    if ticker then
        return
    end
    ticker = util.IntervalCall(0, function ()
        local waitCount = 0
        for k, v in pairs(WaitHideModuleDic) do
            waitCount = waitCount + 1
            break
        end
        if waitCount == 0 then
            util.RemoveDelayCall(ticker)
            ticker = nil
            if logOpenExistUI then
                log.Warning("window state, ClearWaitHideList ",util.get_len(WaitHideModuleDic))
            end
            -- Warning(2, "window state, ClearWaitHideList ",util.get_len(WaitHideModuleDic))

            return
        end
        -- local len = #WaitRemoveList
        local showCount,showUIModuleName = self:GetShowWindowCount()
        if showCount > 0 then
            for k_uiModuleName, dicObject in pairs(WaitHideModuleDic) do
                -- 隐藏和删除真正的物体
                for gameObject,gameObjectData in pairs(dicObject.gameObjectData)  do
                    if gameObjectData and gameObjectData.gameObject then
                        if gameObjectData.windowWaitType == WindowWaitType.Position then
                            self:SetPosition(gameObjectData.gameObject, gameObjectData.bActive, gameObjectData.parent, gameObjectData.position,dicObject.UIModuleName)
                        elseif gameObjectData.windowWaitType == WindowWaitType.Destory then
                            self:DestoryGameObject(gameObjectData.gameObject)
                        else
                            log.Error("not support",gameObjectData.gameObject,"k_uiModuleName:",k_uiModuleName)
                        end
                    else
                        log.Error("not support no data",gameObjectData.gameObject,"k_uiModuleName:",k_uiModuleName)
                    end
                end
            end

            -- 移除列表
            WaitHideModuleDic = {}
        end
    end)
end
-- 添加
function WindowMgr:AddWaitHideList(waitData)
    if not const.OPEN_EXIST_UI then
        return
    end
    if not waitData then
        return
    end
    WaitHideModuleDic[waitData.uiModuleName] = waitData

    if logOpenExistUI then
        log.Warning("window state, AddWaitHideList===uiModuleName: ",waitData.uiModuleName,"/count : ",util.get_len(WaitHideModuleDic))
    end

    -- Warning(2, "window state, AddWaitHideList===uiModuleName: ",waitData.uiModuleName,"/count : ",IsLogLevel(2) and util.get_len(WaitHideModuleDic))

end
-- 移除隐藏的
function WindowMgr:RemoveWaitHideList(uiModuleName)
    if not const.OPEN_EXIST_UI then
        return
    end
    if not WaitHideModuleDic[uiModuleName] then
        return
    end
    local waitData = WaitHideModuleDic[uiModuleName]
    if waitData then
        for gameObject,gameObjectData in pairs(waitData.gameObjectData)  do
            if gameObjectData and gameObjectData.gameObject then
                if gameObjectData.windowWaitType == WindowWaitType.Destory then
                    self:DestoryGameObject(gameObjectData.gameObject)
                    if logOpenExistUI then
                        log.Warning("window state, RemoveWaitHideList===uiModuleName: ",uiModuleName,"/count ::: ",util.get_len(WaitHideModuleDic))
                    end
                    -- Warning(2, "RemoveWaitHideList===uiModuleName: ",uiModuleName,"/count ::: ",IsLogLevel(2) and util.get_len(WaitHideModuleDic))

                end
            end
        end
    end
    WaitHideModuleDic[uiModuleName] = nil
    if logOpenExistUI then
        log.Warning("window state, Remove===uiModuleName: ",uiModuleName,"/count : ",util.get_len(WaitHideModuleDic))
    end
    -- Warning(2, "window state, Remove===uiModuleName: ",uiModuleName,"/count : ", IsLogLevel(2) and util.get_len(WaitHideModuleDic))

end

--[[隐藏UI模块]]
function WindowMgr:HideModule(uiModuleName, bDealUnload)
    if logOpenClose then
        log.Warning("HideModule",uiModuleName)
    end
    -- Warning(1, "HideModule",uiModuleName)

    bDealUnload = nil2Def(bDealUnload, true)
    --窗口并没有被打开
    if not WindowObjs[uiModuleName] then
        return
    end
    local m = require(uiModuleName)
    if nil == m then
        log.Warning("WindowMgr:HideModule>> 尝试关闭一个不存在的UI模块, name="..tostring(uiModuleName))
        return
    end
    

    -- local ol = self:OpenList(uiModuleName)
    -- if ol then 
    --     for i, mN in ipairs(ol) do
    --         self:HideModule(mN)
    --     end
    -- end
     
    if   m.Hide then
        m["Hide"]()
    end
    
    -- --print("隐藏UI模块，name="..uiModuleName)

    --窗口隐藏处理
    self:OnModuleHide(uiModuleName) 
   
    self:DelayCheckState()
end
 
--[[关闭UI模块]]
function WindowMgr:DelayCheckState()
    GraphicRaycaster.BlockAll = util.GetUseBlockAll() and true
    --  --print("DelayCheckState")
    self.BDelayCheckState = true
    -- util.DelayOneCall("CheckState",function ()
    --    self:CheckState() 
    -- end,0.3)
end

-- 是否是顶层UI
function WindowMgr:IsTopUI(uiModuleName)
    if (not WindowConfig[uiModuleName]) then
        return false
    end
    local moduleObj = WindowObjs[uiModuleName]
    if (not moduleObj) or util.IsObjNull(moduleObj.UIRoot) then
        return false
    end
    local moduleRootTrans = moduleObj.UIRoot.transform
    if moduleRootTrans.parent then
        local parentName = moduleRootTrans.parent.name
        if UsedCanvasNameList[parentName] then
            if logOpenExistUI then
                log.Warning("UnloadModuleNoCanvasParent uiModuleName==",uiModuleName,'p=',parentName)
            end
            -- Warning(2, "UnloadModuleNoCanvasParent uiModuleName==",uiModuleName,'p=',parentName)

            return true
        end
    end
    return false
end
--- 获取TopUI的排除列表excludeCheckTopUI
local excludeCheckTopUI = {
    ["ui_bomberman_enter"] = true,
}
---@public 获取最上层的UI --排除掉灵石等
function WindowMgr:GetTopUI()     
    --不排序；降低时间复杂度
    local targetUIModule
    local sortOrder = 0
    for i, v in pairs(WindowObjs) do
        if not lingshiUIList[i]  and not excludeCheckTopUI[i]  and v.curOrder and v.curOrder < Max_UI_MODULE_LAYER then
            --注意；这里没有判断ui自己被隐藏的情况,消耗大（如有需要再判断）
            --if sortOrder < v.curOrder   and not util.IsObjNull(v.rootTrans) and  v.rootTrans.gameObject.activeInHierarchy then
            if sortOrder < v.curOrder  then
                sortOrder = v.curOrder
                targetUIModule = i
            end
        end
    end
    if not targetUIModule then
        return
    end
    return self:GetWindowObj(targetUIModule),targetUIModule
end

---@public 更新ui至顶层UI --只处理module层
function WindowMgr:UpdateUIToTop(uiModuleName)
    if not WindowObjs[uiModuleName] then
        return
    end
    WinAttachList[uiModuleName] = nil
    self:RemoveWaitHideList(uiModuleName)
    self:ModifyCurrent(uiModuleName, true)
    self:SetOpenInd(uiModuleName)
    self.curOrder = self.OpenIndList[uiModuleName]
    self:CheckState()
end

--[[关闭UI模块-New]]
function WindowMgr:UnloadModulePure(uiModuleName, data)
    local bDealHide = data and data.bDealHide
    self:UnloadModule(uiModuleName, bDealHide,data)
end
function WindowMgr:UnloadModuleImmediatePure(uiModuleName, data)
    local bDealHide = data and data.bDealHide
    self:UnloadModuleImmediate(uiModuleName, bDealHide,data)
end
--[[关闭UI模块]]
function WindowMgr:UnloadModule(uiModuleName, bDealHide,data)
    --log.Error("UnloadModule:", uiModuleName)

    if const.IsOldMainUI(uiModuleName) then
        return
    end
    -- Warning(1, "UnloadModule",uiModuleName)

    if logOpenClose then
        log.Warning("UnloadModule",uiModuleName)
    end 
    local wnd = WindowObjs[uiModuleName]
    if wnd then
        --自动开主界面
        if wnd.delayOpenMain then
            local main_slg_mgr = require "main_slg_mgr"
            main_slg_mgr.DelayOpenMainUI(uiModuleName)
        end
    end
    local popups_cache = require "popups_cache"
    popups_cache.RemovePopWin(uiModuleName)
    local st = self:GetState(uiModuleName)
    st.state = WIN_NO_SHOW
    st.param = nil
    -- local ol = self:OpenList(uiModuleName)
    -- if ol then 
    --     for i, mN in ipairs(ol) do
    --         local v = self:GetState(mN)
    --         v.state = WIN_NO_SHOW
    --         v.param = nil
    --     end
    -- end
    self:ModifyCurrent(uiModuleName, false)
    self:UnloadModuleInternal(uiModuleName, bDealHide,data)
    self:DelayCheckState()
end

function WindowMgr:UnloadModuleUI(uiModuleName, bDealHide)
    -- log.Error("UnloadModuleUI:", uiModuleName)

    -- local ub = WindowObjs[uiModuleName]
    -- if ub then 
    --     ub:Destroy()
    -- end

    local needStepGC = false
    for key, value in pairs(WindowDestroy) do
        if not needStepGC then
            needStepGC = true
        end
        if not util.IsObjNull(value[2]) then 
            GameObject.DestroyImmediate(value[2])

            -- --print("WindowDestroy",value[1],value[2])
        end
        if not self:IsCacheUI(value[3]) then
            if not value[1] then
                log.Error("abName字段不能为空",value[3])
            end
            -- IOSystem.UnloadAssetBundle(value[1], "ui_base", true)
            local ub = value[4]

            if ub.closed  and ub.loader and value[3] then
                local st = self:GetState(value[3])
                if st and st.state ~= WIN_SHOW then
                --  --print("UnloadModuleUI",value[3],ub.closed,ub.loader and ub.loader.isDisposed)
                    ub.loader:Dispose()
                end
            end
            --卸载ui特效补充（如果ui关闭界面直接执行的close(),如果没走正常的UnloadModuleInternal流程，会导致卸载特效没执行到；）
            --如果ui特效已卸载，再次调用没有消耗
            WindowMgr:UnloadEffectsOfModuleByName(value[3])
            --  --print("UnloadModuleUI",value[3])
        end
    end
    WindowDestroy = {}

    if needStepGC and const.OPEN_STEP_GC then
        util.DelayOneCall("memory step collect", self.MemoryStepGC, 2.5)
    end
end

function MemoryStepGC()
    local logSet = {}
    if const.OPEN_STEP_GC_LOG then
        logSet[1] = "MemoryStepGC before stepGC:"..collectgarbage("count").." kb"
    end
    local lastTime
    local number = 1000
    while true do
        if const.OPEN_STEP_GC_LOG then
            lastTime = DateTime.UtcNow
        end
        if collectgarbage("step", number) then
            break
        else
            -- if const.OPEN_STEP_GC_LOG then
            --     logSet[#logSet + 1] = "step time:"..util.DateTimeDiff(DateTime.UtcNow, lastTime).." s"
            -- end
            coroutine.yield(0)
        end  
    end
    if const.OPEN_STEP_GC_LOG then
        logSet[#logSet + 1] = "MemoryStepGC finished,memory:"..collectgarbage("count").." kb"
        print(table.concat(logSet, "\n"))
    end
    IsStepGC = false

    -- local monitor_object    = require "monitor_object"
    -- monitor_object.Dump_Object()
end

function WindowMgr.MemoryStepGC()
    if IsStepGC then
        return
    end

    IsStepGC = true
    util.DelayCall(0, MemoryStepGC)

    -- local herocount = 0
    -- herocount = herocount + 1
    -- if herocount <= 3 then
        -- return
    -- end
    -- util.DelayOneCall("memory collect",function ()
        -- uihero = nil
        -- GC.Collect()
        -- print(collectgarbage("collect"))
        -- print(collectgarbage("count"))
    -- 
        -- local memory = require "perf.memory"
        -- print('total memory:',memory.total())
    -- 
        -- -- local memorySnap = require "memory_referenceInfo.lua"
        -- -- memorySnap.m_cMethods.DumpMemorySnapshot("./", "ui_hero_impl all", -1)
        -- -- memorySnap.m_cMethods.DumpMemorySnapshotSingleObject("./", "ui_hero_impl_close", -1, "UIHero", uihero)
    -- end,2)
end

function WindowMgr:UnloadModuleImmediate(uiModuleName, bDealHide,data)
    
    local ub = WindowObjs[uiModuleName]
    if ub and not ub.closed then 
        self:UnloadModuleInternal(uiModuleName, bDealHide,data)
    end
    -- self:UnloadModuleUI(uiModuleName, bDealHide)
end
function WindowMgr:UnloadModuleInternal(uiModuleName, bDealHide,data)
    if logOpenClose then
        log.Warning("UnloadModuleInternal",uiModuleName)
    end  
    -- Warning(1,"UnloadModuleInternal",uiModuleName)

    bDealHide = nil2Def(bDealHide, true)
    
    self:ModifyCurrent(uiModuleName, false)

    local st = self:GetState(uiModuleName)
    st.state = WIN_NO_SHOW

    if NoEnityWndDic[uiModuleName] then return end

    -- if self.lastMainState == uiModuleName then
    --     self.lastMainState = nil
    -- end    
    local isRecycleUI = WindowObjs[uiModuleName] and WindowObjs[uiModuleName].recycle_ui
    --如果窗口没有打开，或者 （窗口已打开，但不是缓存ui）；则卸载ui特效
    if not WindowObjs[uiModuleName] or not isRecycleUI then
        --卸载ui特效
        WindowMgr:UnloadEffectsOfModuleByName(uiModuleName)
    end
    --窗口并没有被打开
    if not WindowObjs[uiModuleName] then
        return
    end
    --获得模块
    local m = require(uiModuleName)
    if m == nil then
        log.Warning("WindowMgr:UnloadModule>> 尝试关闭一个不存在的UI模块, name="..tostring(uiModuleName))
        return
    end
    --需要处理隐藏逻辑
    if bDealHide == true then
    --    self:HideModule(uiModuleName, false) 
    end
    
 

    --模块定制属性
    local cfg = WindowConfig[uiModuleName]
    local bUnload = false
    if  m.Close then
        local win = WindowObjs[uiModuleName]
        if win then
            win.__ui_occupied = true
            win:CloseBefore()
            local ctrName = uiModuleName.."_controller"
            --设置静态数据
            self:SetUIParaData(m,data,1)
            local ctr = self:GetController(ctrName)
            if ctr then               
                ctr:CloseBefore()
            end
        end       
        safeCallClose(m["Close"],uiModuleName,data)
        if win then
            win.__ui_occupied = nil
        end
        -- m["Close"]()
    end
    
    --删除窗口对象
    if WindowObjs[uiModuleName] and not WindowObjs[uiModuleName].recycle_ui then
        -- --print("remove recycle_ui",uiModuleName)
        WindowObjs[uiModuleName] = nil
    end

    -- --print("关闭UI模块，name="..uiModuleName)
    
    --窗口关闭处理
    self:OnModuleUnload(uiModuleName)
end

--[[窗口模块是否显示]]
function WindowMgr:IsModuleShown(uiModuleName)
    if not uiModuleName then
        for k, v in pairs(WindowObjs) do
            if uiModuleName and not NoEnityWndDic[uiModuleName] then 
                if v and v:IsVisible() then
                    return false
                end
            end
        end
        return true
    end 

    if nil == WindowObjs[uiModuleName] then
        return false
    end
    
    local vo = self:GetState(uiModuleName)
    return vo.state == WIN_SHOW
        
    --     local window = WindowObjs[uiModuleName]
    -- return window:IsVisible()
end

--[[获得window object]]
function WindowMgr:GetWindowObj(uiModuleName)
    return WindowObjs[uiModuleName]
end
--[[获得window controller]]
function WindowMgr:GetController(uiModuleName)
    return Controllers[uiModuleName]
end

--[[进入某个状态]]
function WindowMgr:OnStateEnter(curState)

    --通知那些需要对模块处理的模块进行处理
    for name,cfg in pairs(WindowConfig) do
        if cfg ~= nil and cfg.enableStateHandle == true then
            local m = require(name)
            if m and m["OnStateEnter"] ~= nil then
                m["OnStateEnter"](curState)
            end
        end
    end
    
    local stateWndConfig = GameStateWindowConfig[curState]
    if nil == stateWndConfig then
        return
    end
    
    local openWnds = stateWndConfig[1]
    if openWnds == nil then
        return 
    end
    
    if table.getn(openWnds) > 0 then
        for _,name in pairs(openWnds) do
            self:ShowModule(name)
        end
    end
    
    local extraCloseWnds = stateWndConfig[4]
    if extraCloseWnds then
        for _,name in pairs(extraCloseWnds) do
            self:HideModule(name)
        end
    end
    
end


--[[离开某个状态]]
function WindowMgr:OnStateLeave(curState)
    
    --通知那些需要对模块处理的模块进行处理
    for name,cfg in pairs(WindowConfig) do
        if cfg ~= nil and cfg.enableStateHandle == true then
            local m = require(name)
            if m and m["OnStateLeave"] ~= nil then
                m["OnStateLeave"](curState)
            end
        end
    end
	
	for name ,cfg in pairs(WindowConfig) do
		if cfg ~= nil and cfg.closeOnSwitch ~= nil and cfg.closeOnSwitch == true then
            local m = require(name)
            if m then
               self:UnloadModule(name)
            end
        end
	end
    
    local stateWndConfig = GameStateWindowConfig[curState]
    if nil == stateWndConfig then
        return
    end
    
    local closeWnds = stateWndConfig[2]
    if closeWnds == nil then
        closeWnds = stateWndConfig[1]
    end
    
    if closeWnds ~= nil and table.getn(closeWnds) > 0 then
        for _,name in pairs(closeWnds) do
            self:UnloadModule(name)
            -- self:HideModule(name)
        end
    end
    
    local extraCloseWnds = stateWndConfig[3]
    if extraCloseWnds ~= nil and table.getn(extraCloseWnds) > 0 then
        for _,name in pairs(extraCloseWnds) do
            self:HideModule(name)
        end
    end
    
end 

function WindowMgr:GetAllWindowObjs()
    return  WindowObjs
    -- body
end

--[[关闭所有ui]]
function WindowMgr:CloseAll(exclude,closeRecyle)
     
    for modulename, obj in pairs(WindowObjs) do
        if modulename and (not exclude or not exclude[modulename]) and not lingshiUIList[modulename] then
            if closeRecyle then 
                local obj = self:GetWindowObj(modulename)
                if obj then
                    obj.recycle_ui = nil
                end
            end
            self:UnloadModule(modulename)  
        end
    end

    
    -- event.Trigger(event.CLOSE_ALL_SCENE)
end
function WindowMgr:HideAll(exclude)
     
    for modulename, obj in pairs(WindowObjs) do
        if modulename and (not exclude or not exclude[modulename]) and not lingshiUIList[modulename] then
            if self:IsInCurrents(modulename)  then
                self:HideModule(modulename)  
            end
        end
    end

    
    -- event.Trigger(event.CLOSE_ALL_SCENE)
end

-- 关闭所有缓存界面以释放资源
function WindowMgr:ClearAllCachedUI(single)
    for modulename, obj in pairs(WindowObjs) do
        if modulename and obj and obj.recycle_ui and (not obj:IsVisible()) and not lingshiUIList[modulename] then
            obj.recycle_ui = nil
            self:UnloadModule(modulename)
            obj.recycle_ui = single
        end
    end
    event.Trigger(event.CLOSE_ALL_CACHE_UI)
end

--[[关闭所有ui和场景]]
function WindowMgr:CloseAllUIAndScene(exclude, closeRecyle)
    for modulename, obj in pairs(WindowObjs) do
        if modulename and (not exclude or not exclude[modulename]) and not lingshiUIList[modulename] then
            if closeRecyle and obj then 
                    obj.recycle_ui = nil
            end
            self:UnloadModule(modulename) 
        end
    end
    
    event.Trigger(event.CLOSE_ALL_SCENE)
end


 
function WindowMgr:SetOpenInd(uiModuleName,ind)
    self.OpenIndList = self.OpenIndList or {}
    --- 窗口为子窗口时跳过同步层次
    if WinAttachList[uiModuleName]  then
        if ind then
            self.OpenIndList[uiModuleName] = ind 
            self.curOrder = ind
            -- local window = WindowObjs[uiModuleName]
            -- if window then
            --     window.curOrder = ind
            -- end
        end
        if logOrder then
            log.Warning("SetOpenInd",uiModuleName,self.OpenIndList[uiModuleName])
        end
        -- Warning(2, "SetOpenInd",uiModuleName,self.OpenIndList[uiModuleName])

        return
    end
    if not ind then 
        ind = self:CurrentInd(uiModuleName)
        if ind then 
            ind = ind * ATTACH_PANEL_ITL * ORDER_ITL
        end
    end
    if ind and ind >0 then
        self.OpenIndList[uiModuleName] = ind
        self.curOrder = ind
            -- local window = WindowObjs[uiModuleName]
        -- if window then
        --     window.curOrder = ind
        -- end
    end
    if logOrder then
        log.Warning("SetOpenInd",uiModuleName, "-",self.OpenIndList[uiModuleName], "-", self:CurrentInd(uiModuleName))
    end
    -- Warning(2, "SetOpenInd",uiModuleName, "-",self.OpenIndList[uiModuleName], "-", self:CurrentInd(uiModuleName))

end 
function WindowMgr:IsNowOpenInd(uiModuleName)
    self.OpenIndList = self.OpenIndList or {}

    if NoEnityWndDic[uiModuleName] then return true end

   local uiOrder = (self.OpenIndList[uiModuleName] or 1) 
   local uibase = self:GetWindowObj(uiModuleName)

   if logOrder then
    log.Warning("IsNowOpenInd",uiModuleName,uibase and uibase.curOrder,uiOrder)
   end
--    Warning(2, "IsNowOpenInd",uiModuleName,uibase and uibase.curOrder,uiOrder)

   return uibase and uibase.curOrder and uibase.curOrder == uiOrder
end
function WindowMgr:GetNextOrder()
    self.OpenIndList = self.OpenIndList or {}
    local nOrder = 1
    for _name, _order in pairs(self.OpenIndList) do
        -- statements
        nOrder = _order > nOrder and _order or nOrder
    end
    return nOrder + ORDER_ITL - 1
end
-- function WindowMgr:AddOpenInd(uiModuleName)
--     self:SetOpenInd(uiModuleName,self:IncreaseInd())
    
-- end
function WindowMgr:ApplyOpenInd(uiModuleName)
    self:SortModule(uiModuleName)
    -- -- --print("AddOpenInd",uiModuleName,self.OpenIndList[uiModuleName])
end

--[[对窗口进行排序 同步窗口顺序]]
function WindowMgr:SortModule(uiModuleName)
    self.OpenIndList = self.OpenIndList or {}

    local wobj = WindowObjs[uiModuleName]
    if wobj then
        local openOrder = (self.OpenIndList[uiModuleName] or 1)
        if openOrder ~= wobj.curOrder then
            wobj.curOrder = openOrder
            wobj.EffOrder = wobj.curOrder + 1

            if logOrder then
                log.Warning("SortModule",uiModuleName,wobj.curOrder)
            end
            -- Warning(2, "SortModule",uiModuleName,wobj.curOrder)

            --if wobj.ResetOrder then
            --    wobj:ResetOrder()
            --end
        end
    end

    if wobj and wobj.UIRoot and not util.IsObjNull(wobj.UIRoot) then
        local canvas = wobj.UIRoot:GetComponent(CanvasType)
        local sGroup = wobj.UIRoot:GetComponent(SortingGroupType)
        if canvas and not util.IsObjNull(canvas) then
            local bDirty = canvas.sortingOrder ~= wobj.curOrder
            canvas.sortingOrder = wobj.curOrder
            canvas.overrideSorting = true
            if not util.IsObjNull(sGroup) then
                local offset = 1
                if wobj.CustomEffectOrderOffset then--有些界面需要更高的oder值
                    offset = offset + wobj.CustomEffectOrderOffset
                end
                if wobj.EffectMaskOn then
                    offset = 2
                end
                sGroup.sortingOrder = wobj.curOrder + offset
            end
        end
        if wobj.OnValueChange then
            wobj:OnValueChange("curOrder",wobj.curOrder)
        end
        if wobj.UpdateUI then
            wobj:UpdateUI()
        end
        event.Trigger(event.WINDOW_MGR_SORT_MODULE,wobj)
    end
end

--[[加载ui上的异步特效资源]]
function WindowMgr:LoadEffectsOfModule(uiRoot, uiModuleName) 
    -- local wobj = WindowObjs[uiModuleName]

    if not util.IsObjNull(uiRoot) then
        effect_load_mgr.LoadUIEffects(uiRoot, uiModuleName)
    end
end

--[[卸载ui上的异步特效资源]]
function WindowMgr:UnloadEffectsOfModule(uiRoot)
    -- local wobj = WindowObjs[uiModuleName]
    --  --print("UnloadEffectsOfModule 特效：", uiModuleName, wobj and wobj.UIRoot.name)
    if not util.IsObjNull(uiRoot) then
        effect_load_mgr.UnloadUIEffects(uiRoot)
    end
end

--[[通过模块名字卸载ui上的异步特效资源]]
function WindowMgr:UnloadEffectsOfModuleByName(uiModuleName) 
    -- local wobj = WindowObjs[uiModuleName]
    --  --print("UnloadEffectsOfModule 特效：", uiModuleName, wobj and wobj.UIRoot.name)
    effect_load_mgr.UnloadUIEffectsByName(uiModuleName)
    -- if not util.IsObjNull(uiRoot) then
    -- end
end

--[[
    @desc: 会修改目标锚点,目前没有做还原操作
    author:{author}
    time:2020-07-20 16:14:52
    --@scaleCenterWorldPos:
	--@uiRootTrans:--@uiRootTrans:(这个节点布局应该铺满全屏，可以准确的设置锚点位置)
	--@closecallback: 
    @return:
]]
function WindowMgr:AnimateScaleClose(scaleCenterWorldPos, uiRootTrans, closecallback)
    if scaleCenterWorldPos == nil or uiRootTrans == nil or closecallback == nil then
        log.Error("AnimateScaleClose 参数未设置")
        return
    end

    local UICamera = GameObject.Find("/UIRoot/UICamera"):GetComponent(typeof(Camera))
    local localPos = util.SyncW2UI(scaleCenterWorldPos, UICamera, self.canvasMesh:GetComponent(CanvasType), uiRootTrans)
    local parentRectTrans = self.canvasMesh:GetComponent(typeof(RectTransform))
    local selfRectTrans = uiRootTrans:GetComponent(typeof(RectTransform))
    local width = parentRectTrans.sizeDelta.x * selfRectTrans.pivot.x
    local height = parentRectTrans.sizeDelta.y * selfRectTrans.pivot.y
    local targetPivot = {x = (width + localPos.x) / parentRectTrans.sizeDelta.x, y = (height + localPos.y) / parentRectTrans.sizeDelta.y}
    selfRectTrans.pivot = targetPivot
    local ltAnimation = LeanTween.scale(uiRootTrans.gameObject, {0.01,0.01,0.01}, 0.5):setEase(LeanTweenType.easeInOutSine)
    ltAnimation:setOnComplete(closecallback)
end

--  function print()
    
--  end

--[[状态进入消息]]
function _OnEventStateEnter(eventName, curState)
	WindowMgr:OnStateEnter(curState)
end


--[[状态离开消息]]
function _OnEventStateLeave(eventName, curState)
	WindowMgr:OnStateLeave(curState)
end

--[[[模块初始化事件]]
function _OnEventInitBase(eventName, base)

    -- -- --print("OnCreateBase",base,WindowMgr.curOrder)
    if base then
        base.curOrder = WindowMgr.curOrder
    end
end

--[[[模块UI_xxx实例初始化事件]]
function _OnEventUIModuleInit(eventName, uiModuleName,ub)
    WindowMgr.onInitCallback = WindowMgr.onInitCallback or {}
    if uiModuleName then
        local onInit = WindowMgr.onInitCallback[uiModuleName]
        --此时gameobject已经好了
        if not WindowObjs[uiModuleName] then
            WindowObjs[uiModuleName] = ub
        end
        if onInit and type(onInit) ~= "string" then
            WindowMgr.onInitCallback[uiModuleName] = nil
            onInit(uiModuleName)
        end
        --after view
        local  vc_type =  WindowObjs[uiModuleName]:GetVCTypeUI()
        local mock_cfg = require "mock_cfg"
        if vc_type and vc_type ==  enum_define.enum_ui_vc_type.vc and not mock_cfg.OPEN_MOCK then
            local controller_name = uiModuleName.."_controller"
            local m = require(controller_name);
            if m  then
                -- 通过静态方法先构造一个controller 同步Init()周期
                local controller =   m["Show"]()
                Controllers[controller_name] = controller;              
                local data = WindowMgr:GetUIParaData(uiModuleName,0)
                WindowMgr:SafeCallFunc("UIController.Init",uiModuleName,controller.Init,controller,uiModuleName,controller_name,data)               
            end

        end

        require("lpat_helper").execute()
    end
end

--[[模块显示事件]]
function _OnEventUIModuleShow(eventName, uiModuleName,ub)
    
    WindowMgr.onShowCallback = WindowMgr.onShowCallback or {}
    if uiModuleName then
        local onshow = WindowMgr.onShowCallback[uiModuleName]
        -- -- --print("_OnEventUIModuleShow",uiModuleName,onshow)
        if not WindowObjs[uiModuleName] then
            WindowObjs[uiModuleName] = ub
        end
        if  onshow  then
            WindowMgr.onShowCallback[uiModuleName] = nil
            if type(onshow) == "function" then
                onshow(uiModuleName)
            else
                log.Error(uiModuleName,"传入参数有误 onShow 必须是函数function")
            end       
        end
        WindowMgr:SortModule(uiModuleName)
		if ub then
	        WindowMgr:LoadEffectsOfModule(ub.UIRoot, uiModuleName)
		end
        --@desc controllerShow after view
        local  vc_type =  WindowObjs[uiModuleName]:GetVCTypeUI()
        if  vc_type and vc_type ==  enum_define.enum_ui_vc_type.vc then
            local controller_name = ub  and ub.controller_name or uiModuleName.."controller"
            local controller = Controllers[controller_name]
            if  controller then
                WindowMgr:SafeCallFunc("UIController.OnShow",uiModuleName,controller.OnShow,controller)
                --Controllers[controller_name]:OnShow()
            end
        end
    end
    WindowMgr:DelayCheckState()
    -- WindowMgr:CheckState() 
end

function _OnEventUIModuleHide(eventName, uiModuleName,ub)
    if WindowObjs[uiModuleName] then
        local  vc_type =  WindowObjs[uiModuleName]:GetVCTypeUI()
        if vc_type and vc_type ==  enum_define.enum_ui_vc_type.vc then
            local controller_name = ub  and ub.controller_name or uiModuleName.."controller"
            local controller = Controllers[controller_name]
            if controller  then
                WindowMgr:SafeCallFunc("UIController.OnHide",uiModuleName,controller.OnHide,controller)
                --Controllers[controller_name]:OnHide()
            end
        end
    end
end

--[[窗口关闭之后，则删除缓存]]
function _OnEventUIModuleClose(eventName, uiModuleName, _assetBundleName, UIRoot,dontdestroy,ub)

    --  --print("_OnEventUIModuleClose",uiModuleName,dontdestroy,WindowMgr:IsCacheUI(uiModuleName) ,idle.MEM_LEVEL)
    --@desc controllerClose befor view
    if WindowObjs[uiModuleName] then
        local  vc_type =  WindowObjs[uiModuleName]:GetVCTypeUI()
        local isRecycleUI = WindowObjs[uiModuleName] and WindowObjs[uiModuleName].recycle_ui
        if vc_type and vc_type ==  enum_define.enum_ui_vc_type.vc  then
            local controller_name = ub  and ub.controller_name or uiModuleName.."controller"
            local controller = Controllers[controller_name]
            if controller  then
                local ctrStaticClass = require(controller_name)
                local data = WindowMgr:GetUIParaData(uiModuleName,1)
                if isRecycleUI then
                    if not data then
                        data = {}
                    end
                    data.isRecycleUI = isRecycleUI
                end
                WindowMgr:SafeCallFunc("UIController.Close",uiModuleName,controller.Close,controller,data)
                --Controllers[controller_name]:Close(data)
                if not isRecycleUI then
                    Controllers[controller_name] = nil
                end
            end
        end
    end
    if dontdestroy and WindowMgr:IsCacheUI(uiModuleName) then 
        if not util.IsObjNull(UIRoot) then
            WindowMgr:SetActive(UIRoot,false,nil,nil,ub)
        end
    else

        if WindowObjs[uiModuleName] then
            local st = WindowMgr:GetState(uiModuleName)
            if st.state == WIN_NO_SHOW then--预防关闭面板又再次打开时窗口对象被销毁
                WindowObjs[uiModuleName] = nil
            end
        end

    -- WindowMgr:ModifyCurrent(uiModuleName)
    --  --print("_OnEventUIModuleClose",uiModuleName,_assetBundleName,ub)
        if not util.IsObjNull(UIRoot) then
            if const.OPEN_EXIST_UI then
                WindowMgr:SetActive(UIRoot,false,nil,nil,ub)
            else
                UIRoot:SetActive(false)
            end
            table.insert(WindowDestroy,{_assetBundleName,UIRoot,uiModuleName,ub})
        end
    end 
    WindowMgr:DelayCheckState()
end

local canCheck = true
--一直存在的UI,主要用于判断是否是主界面，且没有弹窗之类的场景
local AlwaysExistingUIList = {
    ["UIMainSlg(Clone)"] = "ui_main_slg" ,
    ["UISandBackToBase(Clone)"]=  "ui_sand_back_to_base" --从沙盘切回来一直存在，不隐藏，位置也在屏幕内
}
function _CheckIsMainUI(e,uiModuleName)
    if canCheck then
        util.DelayCallOnce(0.5,function ()
            local canvasMesh = GameObject.Find("/UIRoot/CanvasWithMesh").transform
            if not util.IsObjNull(canvasMesh) then
                if canvasMesh.childCount > 0 then
                    local go = canvasMesh:GetChild(canvasMesh.childCount - 1)
                    if not util.IsObjNull(go) then
                        local isOuside = go.localPosition.x >80000 or go.localPosition.y >80000
                        local isInAlwaysExistingUIList = AlwaysExistingUIList[go.name]
                        local isInScreen = WindowMgr:IsUIVisibleAndZeroPos(AlwaysExistingUIList[go.name])
                        if (isOuside) or (isInAlwaysExistingUIList and isInScreen)then
                            local GWMgr = require "gw_mgr"
                            event.Trigger(event.EVENT_ENTER_MAIN_CITY,GWMgr.curScene)
                        end
                    end
                end
            end
            canCheck = true
        end )
        canCheck = false
    end
end


--[[窗口关闭之后，则删除缓存]]
function _OnEventUIModuleRemoveObj(eventName, uiModuleName, _assetBundleName, UIRoot)
    if uiModuleName then
        local st = WindowMgr:GetState(uiModuleName)
        st.state = WIN_NO_SHOW
    end
end

---------
---UI的Show  和 close数据传递； 
----当前原理；因为当前的UI每次只可能同一个module只有一个ui，所以只需要传递参数时直接设置成静态的
---相关的require module 等可以先用_G 判断一下

----@public 设置ui的 Controller Show/Close数据
-----@param staticView string uiView的require的模块 
------@param data table ui的Show数据
------@param dataType number 0:show  1:close
function WindowMgr:SetUIParaData(staticView,data,dataType)
    if not staticView then
        return
    end
    if dataType == 0 then
        staticView.setShowUIData  = data
    elseif dataType == 1 then
        staticView.setCloseUIData = data
    end
end
---@public 获取ui的 Controller Show/Close数据
------@param viewName string 注意这里传入的是viewName
function WindowMgr:GetUIParaData(viewName,dataType)
    local staticClass = require(viewName)
    if staticClass then
        if dataType == 0 then
            return staticClass.setShowUIData
        elseif dataType == 1 then
            return staticClass.setCloseUIData
        end
    end
end


--获取ui摄像机，代码里面很多getUIcamera的魔法代码，以后获取ui摄像机可以使用该函数
function WindowMgr:GetUICamera()
    if self.UICamera then
        return self.UICamera
    end
    local UICamera = GameObject.Find("UIRoot/UICamera")
    if not UICamera then
        return nil
    end
    local cam = UICamera:GetComponent(typeof(Camera))
    self.UICamera = cam
    return cam
end

---@public 暴力清理掉UI相关的数据，只有对应ui已经出现异常时才处理
function WindowMgr:ForceClearWindowData(uiModuleName)
    if uiModuleName then
        local st = self:GetState(uiModuleName)
        if st then
            st.state = WIN_NO_SHOW
        end
    end
    --这里是错误异常情况下才会强制
    WindowObjs[uiModuleName] = nil
    WindowConfig[uiModuleName] = nil
    Controllers[uiModuleName.."controller"] = nil
end
---@public xpcall 安全保护调用
function WindowMgr:SafeCallFunc(tag,uiModuleName, targetFunc,...)
    local param = {...}
    local func = function ()
        if targetFunc then
            targetFunc(unpack(param))
        end
    end
    local check,result = xpcall(func, function( err)       
            OnErrCall(err,uiModuleName,tag)
    end)
    if check then
        return result
    else
        if uiModuleName then
            local isCache = self:IsCacheUI(uiModuleName)
            if isCache then
                -- 取消界面缓存，界面销毁
                self:SetCacheUI(uiModuleName,false)
            end
            -- 销毁界面
            self:UnloadModule(uiModuleName)
            WindowMgr:ForceClearWindowData(uiModuleName)
            --local showCount,moduleName,isCloseAll = windowMgr:GetShowWindowCount()
            --if isCloseAll or not moduleName then
            --    --todo bxz;如果出现异常了，且没有打开的ui  需要怎么处理
            --end
        end
    end
end
















function OnSceneDestroy()
    canCheck = false
    WindowMgr.curFullState = false
    WindowMgr.onInitCallback = {}
    WindowMgr.onShowCallback = {}
    WindowMgr.onUnloadCallback = {}
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)
event.Register(event.SCENE_DESTROY_NEW, OnSceneDestroy)
-- function --print(...)
-- end
--创建
WindowMgr:Create()

--获取实例
return WindowMgr

