local print = print
local require = require
local pairs = pairs
local table = table
local select = select
local unpack = unpack
local type = type
local math = math
local typeof = typeof
local string = string
local debug = debug
local tostring = tostring
local next = next
local xpcall = xpcall
local _G = _G
local ipairs = ipairs

local Time = CS.UnityEngine.Time
local os = os
local IOSystem = require "iosystem_load"
local asset_loader = require "asset_loader"
local GameObject = CS.UnityEngine.GameObject
local UI = CS.UnityEngine.UI
local RectTransform = CS.UnityEngine.RectTransform
local CanvasGroup = CS.UnityEngine.CanvasGroup
local Canvas = CS.UnityEngine.Canvas
local LeanTween = CS.LeanTween
local DateTime = CS.System.DateTime
local GC = CS.System.GC
local Application = CS.UnityEngine.Application
local UIHelper = CS.UIHelper

local WriteTextFile = CS.War.Script.Utility.WriteTextFile
local GetPersistentDataPath = CS.War.Script.Utility.GetPersistentDataPath
local persistentDataPath = GetPersistentDataPath()
local CanvasType = typeof(CS.UnityEngine.Canvas)
local TextMeshProUGUIType = typeof(CS.TMPro.TextMeshProUGUI)
local TextType = typeof(CS.UnityEngine.UI.Text)
local HorizontalWrapMode = CS.UnityEngine.HorizontalWrapMode
local VerticalWrapMode = CS.UnityEngine.VerticalWrapMode
local TextAnchor = CS.UnityEngine.TextAnchor
local TextAlignmentOptions = CS.TMPro.TextAlignmentOptions
local AssetBundleManager = CS.War.Base.AssetBundleManager

local Sprite = CS.UnityEngine.Sprite
local Rect = CS.UnityEngine.Rect
local Vector2 = CS.UnityEngine.Vector2

local class = require "class"
--国际化对象
local monitor_object = require "monitor_object"
local event = require "event"
local sort_order = require "sort_order"
local const = require "const"
local lang = require "lang"
local util = require "util"
local map = require "map"
local log = require "log"
local enum_define = require "enum_define"
local cc = cc

local fix_spritemask = require "fix_spritemask"
local idle = require "idle"
local screen_util = require "screen_util"
local table_util = require "table_util"
local e_handler_mgr = require "e_handler_mgr"
local val = require "val"

module("ui_base")

local UIBase = {}
UIBase.widget_table = {}
UI["RectTransform"] = RectTransform
UI["CanvasGroup"] = CanvasGroup

local str = ""

local canvasMRoot = GameObject.Find("/UIRoot/CanvasWithMesh")
canvasMRoot = canvasMRoot.transform

local canvasRoot = GameObject.Find("/UIRoot/Canvas")
canvasRoot = canvasRoot.transform

LogLoad = false
local stack = {}
_G["ui_base|stack"] = stack
--- 缓存加载过的界面ui Assets  {assetBundleName:go}
local uiProtos = {}
--local closeCallback = nil
local ticker = nil

local windowMgr = nil

local gameObjectType = typeof(GameObject)

local bgPool = nil
--显示ui使用的位置(记录的位置可能是以下之一：界面初始位置或init中设置的ui位置（init中有设置的话）,隐藏ui前的位置）
local originalPosition = nil
local fixMultiLangParam = {
    TextAnchor = TextAnchor,
    TextAlignmentOptions = TextAlignmentOptions,
    HorizontalWrapMode = HorizontalWrapMode,
    VerticalWrapMode = VerticalWrapMode,
}
table_util.ReadOnly(fixMultiLangParam)
function UIBase:ctor(selfType)
    self.UIRoot_canvasObj = nil
    self.UIRoot = false
    self.closed = false
    self._assetBundleName = nil

    self.childWindows = {}
    self._onCloseEvent = false

    self.isShow = true
    self.needAni = false
    self.preShowOnce = false  --只调用一次,在onShow之前的调用,把没必要放在Init的耗时操作放到OnPreShowOnce
        
    self.seconds = nil
    self.millSeconds = nil
    self.curOrder = self.curOrder or 1
    self.addOnClickMap = {}
    self.isOpenOrginIsStretch = true -- 是否打开限制 开启时横屏会固定720*1280 默认打开 
    -- self.__self = self.__self or {id=self}

    windowMgr = windowMgr or require "ui_window_mgr"

    self.event_list = self.event_list or {}
    self.value_changed_event_list = self.value_changed_event_list or {}
    self.end_edit_event_list = self.end_edit_event_list or {}
    self.interval_event_list = self.interval_event_list or {}


    -- 不使用窗口动画情况下才设置此 canvasGroupInfo 值，用于解决窗口在没有 Canvas 组件情况下，窗口在屏幕外仍然绘制情况
    self.canvasGroupInfo = nil
    if const.OPEN_NEW_WINDOW_MGR then
        self.enableSortingGroup = true
    else

    end

    self.scene_name = nil
    if not self._InitCreate then
        InitBaseComponents(self)
    end
    self:_InitCreate()
    self:_InitSubSet()

    self.__ui_occupied = false
    self.redDataMap = {}--{[tra] = id}
    self.closeCallback = nil
    --延迟打开主界面(从一个界面打开另一个界面，会先开主界面，再关闭)
    --打开界面跟关闭界面的延时时间可能会不同，如果打开的时候不希望延迟，关闭希望延迟
    self.delayOpenMain = nil--(延迟打开，一般关闭界面得时候延迟打开)
    self.delayCloseMain = nil--（延迟关闭，打开界面弄的时候延迟关闭）
    self.delayMainType = nil
end

function InitBaseComponents(tab)
    tab = tab or UIBase
    cc(tab):addComponent("BaseSubset"):exportMethods()
    cc(tab):addComponent("base_create"):exportMethods()
    --cc(tab):addComponent("base_cache_child"):exportMethods()
    cc(tab):addComponent("base_ui_util"):exportMethods()
end

function UIBase:AddOrRemoveIntervalListener(list, add)
    for i, v in pairs(list) do
        local ui_name = v
        local btn_proxy_name = nil
        local time = nil
        if type(v) == "table" then
            ui_name = v[1]
            btn_proxy_name = v[2]
            time = v[3]
        end
        if ui_name then
            btn_proxy_name = btn_proxy_name or "On" .. ui_name .. "ClickedProxy"
            if self[ui_name] and not self[ui_name]:IsNull() and self[btn_proxy_name] then
                if add then
                    if self[ui_name].onClick == nil then
                        local log = require "log"
                        log.Error("UI名:", ui_name, "事件名:onClick")
                    end
                    self[ui_name].onClick:AddIntervalListener(self[btn_proxy_name], time or 0.5)
                else
                    self[ui_name].onClick:RemoveIntervalListener(self[btn_proxy_name], time or 0.5)
                end
            end
        end
    end
end

function UIBase:AddOrRemoveListener(list, delegate, add)
    for i, v in pairs(list) do
        local ui_name = v
        local btn_proxy_name = nil
        if type(v) == "table" then
            ui_name = v[1]
            btn_proxy_name = v[2]
        end
        if ui_name then
            if self[ui_name] and not self[ui_name]:IsNull() then
                local vc_type = self:GetVCTypeUI()
                if not vc_type or vc_type == enum_define.enum_ui_vc_type.none then
                    btn_proxy_name = btn_proxy_name or "On" .. ui_name .. "ClickedProxy"
                    if add and self[btn_proxy_name] then
                        if self[ui_name][delegate] == nil then
                            local log = require "log"
                            log.Error("UI名:", ui_name, "事件名:", delegate)
                        end
                        self[ui_name][delegate]:AddListener(self[btn_proxy_name])
                    else
                        self[ui_name][delegate]:RemoveAllListeners()
                    end
                else
                    --btn_proxy_name = btn_proxy_name or "On"..ui_name..delegate
                    if add then
                        local handle_module_name = self._NAME .. "_controller";
                        --根据widget_table.event_ui_vc_type 默认发送到controller
                        if self.widget_table[ui_name] and self.widget_table[ui_name].event_bind_vc_type then
                            if self.widget_table[ui_name].event_bind_vc_type == enum_define.enum_bind_vc_type.v then
                                handle_module_name = self._NAME
                            end
                        end
                        self:BindAddlistener(self[ui_name][delegate], delegate, handle_module_name, ui_name, btn_proxy_name)
                    else
                        self[ui_name][delegate]:RemoveAllListeners()
                    end
                end
            end
        end
        --  --print("ManageEvent for",ui_name,btn_proxy_name)

    end
end

function UIBase:BindAddlistener(unity_componet, delegate, handle_module_name, ui_name, proxy_name)
    if delegate == "onClick" then
        unity_componet:AddListener(
                function()
                    e_handler_mgr.TriggerHandler(handle_module_name, proxy_name)
                end
        )
    elseif delegate == "onValueChanged" then
        unity_componet:AddListener(
                function(value)
                    e_handler_mgr.TriggerHandler(handle_module_name, proxy_name, value)
                end
        )
    elseif delegate == "onEndEdit" then
        unity_componet:AddListener(
                function(value)
                    e_handler_mgr.TriggerHandler(handle_module_name, proxy_name, value)
                end
        )
    end
end

function UIBase:ManageEvent(registerOrDispose)


    if not self.event_list then
        return
    end
    if not self.value_changed_event_list then
        return
    end
    if not self.end_edit_event_list then
        return
    end
    if not self.interval_event_list then
        return
    end
    if not self:IsValid() then
        return
    end

    if registerOrDispose then

        for k, v in pairs(self.widget_table) do
            if v.event_name and v.event_name ~= "" then
                self.event_list[k] = { k, v.event_name }
            end
            if v.value_changed_event and v.value_changed_event ~= "" then
                self.value_changed_event_list[k] = { k, v.value_changed_event }
            end
            if v.end_edit_event and v.end_edit_event ~= "" then
                self.end_edit_event_list[k] = { k, v.end_edit_event }
            end
            if v.interval_event and type(v.interval_event) == "table" and v.interval_event[1] ~= "" then
                local name = v.interval_event[1]
                local time = v.interval_event[2]
                self.interval_event_list[k] = { k, name, time }
            end
            if v.backEvent and (v.type == "Button" or self[k]:GetComponent(typeof(UI.Button))) then
                local closeBtnObj = self[k]
                if not self.escapeHandle then
                    self.escapeHandle = function()
                        --print(self.UIModuleName,self.UIRoot,#stack,stack[#stack])
                        if self:IsUIVisible() then
                            closeBtnObj.onClick:Invoke()
                            return true
                        end


                    end
                end
            end
        end
        self:AddOrRemoveListener(self.event_list, "onClick", true)
        self:AddOrRemoveListener(self.value_changed_event_list, "onValueChanged", true)
        self:AddOrRemoveListener(self.end_edit_event_list, "onEndEdit", true)
        self:AddOrRemoveIntervalListener(self.interval_event_list, true)
        -- end
    else
        self:AddOrRemoveListener(self.event_list, "onClick")
        self:AddOrRemoveListener(self.value_changed_event_list, "onValueChanged")
        self:AddOrRemoveListener(self.end_edit_event_list, "onEndEdit")
        self:AddOrRemoveIntervalListener(self.interval_event_list)

    end
end
function UIBase:IsArabicEnvironment()
    return lang.USE_LANG == lang.AR
end
function UIBase:IsRTLEnvironment()
    return lang.IsRTLEnvironment()
end
function UIBase:FixMultiLang(fixMultiLangParam)
    --print("test")
end
function UIBase:LoadUIResource(assetBundleName, assetName, parentTrans, callback, needAni, needSetOffset, needGuide, needBtnScale, matchHomeIndicator, loadABSyn)
    if self.UIRoot and not util.IsObjNull(self.UIRoot) then
        log.Error("duplicate load resource!", self._NAME)
        return
    end
    event.Trigger(event.UI_MODULE_BEGIN_REPORT, self._NAME)
    -- if self._NAME and not self.UIModuleName then 
    -- 	self.UIModuleName = self._NAME
    -- end
    --  --print("LoadUIResource",self._NAME)

    if loadABSyn then  --如果同步方式加载AB
        AssetBundleManager.AddLoadAbSynSet(assetBundleName)
    else
        AssetBundleManager.RemoveLoadAbSynSet(assetBundleName)
    end
    
    -- todo 目前动画会导致C层的Dispose方法异常,暂时屏蔽; 后续处理人：刘梓孝
    needAni = false

    self.__params = { assetBundleName, assetName, parentTrans, callback, needAni, needSetOffset, needGuide, needBtnScale, matchHomeIndicator }

    event.Trigger(event.UI_MODULE_INIT_BASE, self)

    if needGuide ~= false then
        --新手引导下页面切换时屏蔽操作一段时间
        -- local force_guide_system=require"force_guide_system"
        -- force_guide_system.MaskForGuide()

        event.Trigger(event.CHECK_FORCE_GUIDE_SYSTEM)
    end

    if parentTrans == nil then
        parentTrans = map.vertical and canvasMRoot or canvasRoot
    else
        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:AddSubWndUI(self._NAME)
    end

    self._parent = parentTrans

    local isLocalFile = AssetBundleManager.IsLocalFile(assetBundleName)
	event.Trigger(event.UI_MODULE_LOAD_BEGIN_REPORT, self._NAME, isLocalFile)

    local loadcallback = function(loader, UIPrefab)
        event.Trigger(event.UI_MODULE_LOAD_END_REPORT, self._NAME)
        -- SaveUIData(self._NAME, self._NAME, "LoadedCallback")
        UIPrefab = UIPrefab or loader.asset
        if self.closed then
            if not self:IsCacheUI() then
                -- IOSystem.UnloadAssetBundle(assetBundleName, "ui_base")
            end
            log.Warning("UIBase closed skip loadcallback", self._NAME)
            return
        end

        self._assetBundleName = assetBundleName
        if not util.IsObjNull(self.UIRoot) then
            log.Warning("duplicate load resource!", self._NAME)
            return
        end

        if util.IsObjNull(UIPrefab) then
            log.Warning("UIPrefab load resource null!")
            return
        end

        self:CachePrefab(UIPrefab)

        if util.IsObjNull(self._parent) then
            self._parent = nil
        end

        if self._parent ~= nil then
            self.UIRoot = GameObject.Instantiate(UIPrefab, self._parent, false)
        else
            self.UIRoot = GameObject.Instantiate(UIPrefab)
        end

        event.Trigger(event.UI_MODULE_CREATE_REPORT, self._NAME)

        self.__ui_occupied = true

        parentTrans = self._parent
        self.rootTrans = self.UIRoot.transform

        --初始是否是拉伸模式
        local rt = self.rootTrans:GetComponent(typeof(RectTransform))
        self.orginIsStretch = self.isOpenOrginIsStretch and screen_util.IsStretched(rt) or false
        --if self.orginIsStretch then
        --折叠屏全屏对齐高度，其他情况拉伸
        matchHomeIndicator = matchHomeIndicator or self.matchHomeIndicator
        self.FixLayOut = function()
            ------ --print("matchHomeIndicator",matchHomeIndicator,self._NAME)
            local AndroidScreenUtil = require "AndroidScreenUtil"
            if (needSetOffset or matchHomeIndicator) and not screen_util.IsFoldableScreen() and not AndroidScreenUtil.IsFoldable() then
                -- 处理界面自适应 
                local fix_notch_ui = require "fix_notch_ui"
                fix_notch_ui.FixLayout(self.UIRoot, needSetOffset, matchHomeIndicator)
            end
        end
        
        self.ResetWidget = function()
            self:OnResetWidget()
            --重置蒙版尺寸
            util.DelayCallOnce(0.6, function()
                if self and self:IsValid() then
                    self:ResetSpriteMask()
                end
            end)
           self.FixLayOut()
        end
        event.Register(event.SCREEN_SIZE_CHANGED, self.ResetWidget)
        self:OnResetWidget()
        --end
        self.FixLayOut()
        ------  --print("fix home indicator, assetBundleName:",assetBundleName, ",needSetOffset:", needSetOffset, ",matchHomeIndicator:", matchHomeIndicator)

    

        fix_spritemask.FixSpriteMask(self._assetBundleName, self.UIRoot.transform)
        -- 显示UI的时候如果需要动画效果
        if needAni then
            self.needAni = true

            local canvasGroupObj = self.UIRoot:GetComponent(typeof(CanvasGroup))
            if util.IsObjNull(canvasGroupObj) then
                self.UIRoot:AddComponent(typeof(CanvasGroup))
            end
        end

        if not self.canvasGroupInfo and not self.needAni then
            self.canvasGroupInfo = self.UIRoot:GetComponent(typeof(CanvasGroup))
        end

        self.UIRoot_canvasObj = self.UIRoot:GetComponent(typeof(Canvas))

        --排序处理
        if const.OPEN_NEW_WINDOW_MGR then
            sort_order.OnAddChild(self.UIRoot.transform, parentTrans, assetBundleName, self.enableSortingGroup, self)
            if self.curOrder then
                self.EffOrder = self.curOrder + 1
            end
        else
            self.curOrder = sort_order.OnAddChild(self.UIRoot.transform, parentTrans, assetBundleName) or 0
            if self.curOrder then
                self.EffOrder = self.curOrder + 1
            end
        end

        self.btnScale = needBtnScale == nil and true or needBtnScale
        self:SafeCallFunc("OnLoadComplete",callback)
        --self:OnLoadComplete(callback)
        event.Trigger(event.UI_MODULE_INIT_REPORT, self._NAME)
        self:_SetVisible(self.isShow)
        event.Trigger(event.UI_MODULE_SHOW_REPORT, self._NAME)
        --触发事件
        local event = require "event"
        event.Trigger(event.UI_MODULE_LOAD_COMPLETE, assetBundleName)

        self.__ui_occupied = nil
        event.Trigger(event.UI_MODULE_END_REPORT, self._NAME)
    end

    ------  --print("LoadUIR",self._NAME)
    if self:IsCacheUI() and not util.IsObjNull(uiProtos[self._NAME]) then
        local cProto = uiProtos[self._NAME]
        --print("UIBASE:USE CACHE PROTO UI",self._NAME,cProto)
        loadcallback(self.loader, cProto)
    else

        local load_report_tool = require "load_report_tool"
        load_report_tool.tryUiLoad = false
        load_report_tool.RegisterUILoadHandler(true)
        --window 保证了只加载一次，但是卸载操作没有真正与window产生关联，需优化

        windowMgr.loaders = windowMgr.loaders or {}

        if self._NAME then
            self.loader = self.loader or windowMgr.loaders[self._NAME] or asset_loader(assetBundleName, "ui_base")

            windowMgr.loaders[self._NAME] = self.loader
        else
            self.loader = self.loader or asset_loader(assetBundleName, "ui_base")
            log.Error("UIBASE error _NAME hasnt been set!!!!!")
        end
        self.loader.assetPath = assetBundleName
        self.loader:load(loadcallback)

        -- IOSystem.LoadAssetAsync(assetBundleName, assetName, loadcallback, "ui_base")

        --ui依赖图集，贴图信息log 编辑器模式下生效
        if Application.isEditor and val.IsTrue("sw_ui_dependencies_log", 1) then
            UIHelper.GetPrefabeDependenciesBy("Assets/" .. assetBundleName)
        end

        load_report_tool.RegisterUILoadHandler(false)
        if load_report_tool.tryUiLoad then
            event.Trigger(event.GAME_EVENT_REPORT, "ui_try_load", "{\"asset_name\":\"" .. assetBundleName .. "\"}")
        end
    end
end

--重叠对齐布局 原始拉伸模式的预制体 在全屏外屏切换时修改对齐模式
function UIBase:OnResetWidget()
    local rt = self.rootTrans:GetComponent(typeof(RectTransform))
    if self:IsValid() and rt and self.orginIsStretch then
        if (screen_util.IsFoldableScreen()) then
            rt.anchorMin = { x = 0.5, y = 0.5 }
            rt.anchorMax = { x = 0.5, y = 0.5 }
            rt.sizeDelta = { x = 720, y = 1280 }
        else
            local prePos = rt.anchoredPosition
            rt.anchorMin = { x = 0, y = 0 }
            rt.anchorMax = { x = 1, y = 1 }
            rt.offsetMin = { x = 0, y = 0 }
            rt.offsetMax = { x = 0, y = 0 }
            rt.anchoredPosition = prePos
        end
    end
end
function UIBase:SetFullScreen(obj)
    obj.sizeDelta = { x = screen_util.width, y = screen_util.height }
end
function UIBase:ResetSpriteMask()
    if self and self:IsValid() then
        fix_spritemask.ResetSpriteMask(self._assetBundleName, self.rootTrans)
    end
end

function UIBase:OnLoadComplete(callback)
    local closeBtn = nil
    local rootTrans = self.UIRoot.transform
	self.effectMaskGeneratedContent = {}
	self.effectMaskCanvas = {}

    self.effectMaskGeneratedContent = {}
    for k, v in pairs(self.widget_table) do
        --assign widget to class member
        local child = nil
        if v.path == "" then
            child = rootTrans
        else
            child = rootTrans:Find(v.path)
        end
        local isMultiLang = v.USEMULTILANG and not string.empty(lang.GetUseLang())
        if isMultiLang then
            if v.paths then
                --非默认的组件需要设置为不激活
                local curLang = lang.GetUseLang()
                for key, value in pairs(v.paths) do
                    if key and value and key == curLang then
                        if child then
                            --把默认的设为false
                            child.gameObject:SetActive(false)
                        end
                        local child2 = rootTrans:Find(value)
                        if child2 then
                            --把当前语言的设为true
                            child2.gameObject:SetActive(true)
                            child = child2
                        end
                    end
                end
            end
        end
        -- assert(child, rootTrans.name .. v.path .. " not found!")
        if child then
            local childType
            if type(v.type) == "string" then
                if v.type == "TextMeshProUGUI" then
                    childType = TextMeshProUGUIType
                else
                    childType = typeof(UI[v.type])
                end
            else
                childType = typeof(v.type)
            end
            if childType == gameObjectType then
                self[k] = child.gameObject
            else
                self[k] = child:GetComponent(childType)
            end
            if self:IsRTLEnvironment() then
                local component = self[k]
                if component and childType then
                    if v.fitArabic then
                        if TextType:IsAssignableFrom(childType) then
                            util.SetTextLeft2Right(component)
                        elseif TextMeshProUGUIType:IsAssignableFrom(childType) then
                            util.SetTextMeshProUGUILeft2Right(component)
                        end
                    end
                    if TextType:IsAssignableFrom(childType) then
                        util.SetTextTruncate2OverFlowIfNotBestFit(component)
                    end
                end
            end
        else
            log.Error(rootTrans.name .. v.path .. " not found!")
        end
        if isMultiLang then
            if v.multiFontSize then
                local curLang = lang.GetUseLang()
                if curLang then
                    local fontSize = v.multiFontSize[curLang]
                    if fontSize then
                        if self[k] then
                            self[k].fontSize = fontSize
                        end
                    end
                end
            end
        end
        if string.match(v.path, 'closeBtn$') then
            closeBtn = self[k]
            self.__closeBtn = closeBtn
        end
        if v.effect_mask then
            self:EffectMaskProcess(child, v, self.curOrder)
        end
    end

    self.initSecond = DateTime.Now.Ticks
    local ui_name = self.UIModuleName or self._NAME;
    self.controller_name = ui_name and ui_name .. "_controller" or ""
    self.recycle_ui = self.recycle_ui and windowMgr:IsCacheUI(self.UIModuleName or self._NAME)
    if self:CanUseBlurBg() then
        self:_CreateBlurBg(self.__closeBtn, self.UIRoot.transform)
    else
        if not self._noBg then
            self:_CreateBg(self.__closeBtn, self.UIRoot.transform, canvasMRoot)
        end
    end
    self:FixMultiLang(fixMultiLangParam)

    self:InitComponents()
    self.ResetLanguage = function()
        self:InitComponents()
    end
    event.Register(event.LANGUAGE_SETTING_CHANGED, self.ResetLanguage)
    -- 子类完成初始化
    --self:Init()
    self:SafeCallFunc("Init")
    event.Trigger(event.UI_MODULE_INIT, self._NAME or self.UIModuleName, self)
    -- 注册按钮事件
    if not self.manageEvent then
        self:ManageEvent(true)
        self.manageEvent = true
    end

    --注册多语言


    --记录界面初始位置或init中设置的ui位置（init中有设置的话），展示ui时设置， 
    self.originalPosition = rootTrans.anchoredPosition

    self.initSecond = DateTime.Now.Ticks - self.initSecond


    --监控本对象生命周期
    monitor_object.Monitor_Object(self._assetBundleName, self)
    if callback ~= nil then
        callback()
    end
    --添加初始化开始打点
    -- SaveUIData(self._NAME, self._NAME, "Init")
    -- table.insert(stack, self.UIRoot.name)
    -- if closeBtn then
    -- 	self.escapeHandle = function()
    -- 		if self.UIRoot and self.UIRoot.name == stack[#stack] then	-- 栈顶UI
    -- 			closeBtn.onClick:Invoke()
    -- 		end
    -- 	end
    -- 	event.Register(event.ANDROID_BACK, self.escapeHandle)
    -- end
end

--特效遮罩功能
function UIBase:EffectMaskProcess(transform , param)

	--所有关键字
	--effect_mask
	--effect_mask_skip_clear
	--effect_mask_order
	--effect_mask_clear_order

	--开启特效遮罩标记
	self.EffectMaskOn = true

	--IMG处理
	local imgCom = transform:GetComponent(typeof(UI.Image))
	if util.IsObjNull(imgCom) then
		imgCom = transform.gameObject:AddComponent(typeof(UI.Image))
	end
	imgCom.color = {r=1,g=1,b=1,a=1}

	--Canvas处理
	local canvasCom = transform:GetComponent(typeof(Canvas))
	if util.IsObjNull(canvasCom) then
		canvasCom = transform.gameObject:AddComponent(typeof(Canvas))
	end
    canvasCom.overrideSorting = true
	if param.effect_mask_order then
		canvasCom.sortingOrder = self.curOrder + param.effect_mask_order
	else
		canvasCom.sortingOrder = self.curOrder + 1
	end
	self:AppendEffectMaskCanvas(canvasCom , canvasCom.sortingOrder - self.curOrder)
	--材质球模板值设置
	local mtr = GameObject.Instantiate(imgCom.material)
	local warmup_shaders = require "warmup_shaders"
	mtr.shader = warmup_shaders.Find("UI/DefaultForUIMask")
	imgCom.material = mtr
	mtr:SetInt("_Stencil" , 1)
	mtr:SetInt("_StencilOp" , 2)

	table.insert( self.effectMaskGeneratedContent , mtr)

	--MaskClear生成
	if not param.effect_mask_skip_clear then
		local stencilClearGobj = GameObject.Instantiate(transform.gameObject , transform)
		--RectTransform 跟随父级变化
		local clearRect = stencilClearGobj:GetComponent(typeof(RectTransform))
		clearRect.anchorMin = {x = 0 , y = 0}
		clearRect.anchorMax = {x = 1 , y = 1}
		clearRect.offsetMax = {x = 0 , y = 0}
		clearRect.offsetMin = {x = 0 , y = 0}

		--Img材质球 stencil调整
		local cleareImg = stencilClearGobj:GetComponent(typeof(UI.Image))	
		local clearMtr = GameObject.Instantiate(cleareImg.material)
		cleareImg.material = clearMtr
		clearMtr:SetInt("_Stencil" , 0)
		clearMtr:SetInt("_StencilOp" , 2)

		--Canvas调整
		local clearCanvas = stencilClearGobj:GetComponent(typeof(Canvas))
		if param.effect_mask_clear_order then
			clearCanvas.sortingOrder = self.curOrder +  param.effect_mask_clear_order
		else
			local clearOrder = self.curOrder + 3
			if param.effect_mask_order then
				clearOrder = self.curOrder +  param.effect_mask_order + 3
			end
			clearCanvas.sortingOrder = clearOrder --mask和clear之间夹对应特效的SortingGroup
		end

		self:AppendEffectMaskCanvas(clearCanvas , clearCanvas.sortingOrder - self.curOrder)

		table.insert( self.effectMaskGeneratedContent , clearMtr)
		table.insert( self.effectMaskGeneratedContent , stencilClearGobj)
	end
end
--由于order有一定几率刷新 ， 增加一套刷新effectMask Canvas的机制
--增加EffectMask的Canvas
function UIBase:AppendEffectMaskCanvas(_Canvas , _offset)
	if not _Canvas then
		return
	end
	table.insert( self.effectMaskCanvas , { canvas = _Canvas , offset = _offset} )
end
--EffectMaskCanvas处理
function UIBase:EffectMaskCanvasProcess()
	if not self.effectMaskCanvas or #self.effectMaskCanvas <= 0 then
		return
	end
	for index, value in ipairs(self.effectMaskCanvas) do
		if value.canvas and not util.IsObjNull(value.canvas) then
			value.canvas.sortingOrder = self.curOrder + value.offset
		end
	end
end

function UIBase:InitComponents()
    --国际化处理 
    local Lang = require "lang_util"
    -- 首次资源下载完成前，未初始化多语言模块，此时强更安装弹窗不能进行基于 lang 表的转换
    -- 同时也让不需要进行语言转换的界面可手动选择不转换以减少组件查找
    if not self.disableTranslate then
        Lang.TranslateGameObject(self.UIRoot)
    end
    Lang.SetFontSize(self.UIRoot)
    Lang.SetFont(self.UIRoot)
    --音频初始化
    if not self.btnScale and self.UIRoot and not util.IsObjNull(self.UIRoot) then
        local ui_sound  = require "ui_sound"
        local button_scale = require "button_scale"
        local transform = self.UIRoot.transform
        ui_sound.AddFilter(transform)
        button_scale.AddFilter(transform)
    end
end

function UIBase:_CreateBlurBg(closeBtn, rootTrans)
    if closeBtn and not self._noBg and util.IsObjNull(self.bgGameObject) then
        self._startShowTime = Time.realtimeSinceStartup
        self._clickBgTimes = 0

        if newBgPool == nil then
            newBgPool = util.GetBlurBgPool()
        end
        self:SetBgElement(closeBtn, rootTrans)
    end
end

function UIBase:SetBgElement(closeBtn, rootTrans)
    local bg = newBgPool:GetElement(rootTrans.parent)
    bg.transform.localPosition = { x = 0, y = 0, z = 0 }
    self.bgGameObject = bg.transform:Find("@@@AutoAddedBg@@@")
    -- 根节点的canvas位置及大小可能不一致，需要重设
    self.bgGameObject.transform.sizeDelta = { x = 5000, y = 5000 }
    self.bgGameObject.transform.localPosition = { x = 0, y = 0, z = 0 }
    self.bgGameObject.transform.localScale = { x = 1, y = 1, z = 1 }
    --根据节点的cancas重置大小
    local blurBgObj = bg.transform:Find("@@@AutoAddedBlurBg@@@")
    if (canvasRoot ~= nil) then
        local rootSize = canvasRoot.sizeDelta
        blurBgObj.transform.sizeDelta = { x = rootSize.x, y = rootSize.y }
        blurBgObj.transform.localPosition = { x = 0, y = 0, z = 0 }
    end

    bg.transform:SetAsFirstSibling()
    self._bgBtn = self.bgGameObject:GetComponent(typeof(UI.Button))

    local _moduleName = self.UIModuleName
    self._clickBgHandler = function()
        if Time.realtimeSinceStartup - self._startShowTime < const.ClickBgCloseWndProtectTime and self._clickBgTimes < 1 then
            self._clickBgTimes = self._clickBgTimes + 1
            return
        end
        ------  --print("bgpool bg onClick")
        if not util.IsObjNull(closeBtn) and closeBtn.onClick then
            closeBtn.onClick:Invoke()
        else
            log.Error("closeBtn 已被销毁 背景关闭事件触发异常", _moduleName)
        end
    end
    self._bgBtn.onClick:RemoveAllListeners()
    self._bgBtn.onClick:AddListener(self._clickBgHandler)
end

function UIBase:GetCloseBtn()
    return self.__closeBtn
end

function UIBase:Init()
    ------ --print("UIBase:Init")
end

function UIBase:Show()
    -- --print("UIBase:Show()",self._NAME,tostring(self.__ui_occupied))
    if self.__ui_occupied then
        return
    end

    event.Trigger(event.UI_MODULE_INIT_BASE, self)
    -- --print("UIBase:Show-",self._NAME,self.loader and self.loader:Des(),self:IsUIVisible(),self:IsUIExist(),self:IsValid())


    self:StopAni(true)
    self:_SetVisible(true)
    self.seconds = DateTime.Now.Second
    self.millSeconds = DateTime.Now.Millisecond
    self.initSecond = DateTime.Now.Ticks

    if (self.loader and not self.loader:IsValid()) or (self.UIRoot and util.IsObjNull(self.UIRoot)) then
        log.Warning("UIBase Show loader not valid", self.UIModuleName)
        local util_ui = require "util_ui"
        self:LoadUIResource(util_ui.UnpackWithNil(self.__params))
    end

    if LogLoad then
        self.collect = GC.GetTotalMemory()
    end
end

function UIBase:IsLowMem()
    return idle.MEM_LEVEL <= 1
end

function UIBase:Hide(callback)
    if self.needAni then
        self:StopAni(false)
        self:OnPlayHideAni()
        ticker = util.DelayCall(const.playHideAniTime, function()
            if self.UIRoot ~= false then
                self.UIRoot.transform.localScale = { x = 1, y = 1, z = 1 }
            end

            self:_SetVisible(false)
            if ticker then
                util.RemoveDelayCall(ticker)
                ticker = nil
            end
        end)
    else
        self:_SetVisible(false)
    end
end

function UIBase:aniClose(callback)
    self:StopAni(false)
    self.seconds = nil
    self.millSeconds = nil
    self.closeCallback = callback
    if self:IsValid() and self.needAni == true and not self:IsLowMem() then
        self.isShow = false
        self:OnPlayHideAni()
        ticker = util.DelayCall(const.playHideAniTime, function()
            if not util.IsObjNull(self.UIRoot) then
                self.UIRoot.transform.localScale = { x = 1, y = 1, z = 1 }
            end
            --canvasGroupObj.alpha = 1
            self:_SetVisible(false)
            if self.closeCallback then
                self.closeCallback()
                self.closeCallback = nil
            end
            if ticker then
                util.RemoveDelayCall(ticker)
                ticker = nil
            end
        end)
    else
        self:_SetVisible(false)
        if self.closeCallback then
            self.closeCallback()
            self.closeCallback = nil
        end
    end
end

function UIBase:IsVisible()
    return self.isShow
end

function UIBase:IsUIBlockBYFull()
    return windowMgr and windowMgr:IsUIBlockBYFull(self.UIModuleName)
end

function UIBase:IsUIExist()
    return self:IsValid() and not self.closed
end
function UIBase:IsUIVisible()
    return self:IsUIExist() and self.isShow
end

local xHidePos = const.UI_HIDE_POSITION.x / 2

-- ui是否已经打开 显示中与坐标归零
function UIBase:IsUIVisibleAndZeroPos()
    local visible = false
    if not util.IsObjNull(self.UIRoot_canvasObj) then
        visible = self:IsUIVisible() and self.UIRoot
                and self.UIRoot_canvasObj.enabled
                and math.abs(self.UIRoot.transform.anchoredPosition.x) < xHidePos
    else
        visible = self:IsUIVisible() and self.UIRoot
                and math.abs(self.UIRoot.transform.anchoredPosition.x) < xHidePos
    end
    return visible
end

function UIBase:_SetVisible(show)
    -- --print("_SetVisible",show,self._NAME,self.__ui_occupied)
    self.isShow = show
    if show then
        self.closed = false
    end

    if Application.isEditor and self._NAME == nil then
        log.Error("_NAME 未赋值，使用 window:LoadUIResource 时请对 window._NAME 进行赋值", self.UIModuleName)
    end

    if self.UIModuleName == nil and self._NAME then
        -- 如果 ui_window_mgr 缓存了界面 Prefab，且界面未使用 recycle_ui 缓存方式，第二次打开则为空
        -- 必须保证 self.UIModuleName 不为空，否则其它模块监听处理异常，如 render_tool 可以打开渲染像机失败
        self.UIModuleName = self._NAME
    end
    -- --print("_SetVisible",self.UIModuleName,show)

    if self.isShow then
        if self.escapeHandle then
            -- --print("Register",self.__self,self.UIRoot,self.UIModuleName,show)
            table.remove_value(stack, self.__self, true)
            table.insert(stack, self.__self)
            -- event.Register(event.ANDROID_BACK, self.escapeHandle)
        end
    else
        if self.escapeHandle then
            -- --print("Unregister",self.__self,self.UIRoot,self.UIModuleName,show)
            table.remove_value(stack, self.__self, true)
            -- event.Unregister(event.ANDROID_BACK, self.escapeHandle)
        end
    end

    if self.UIRoot and not util.IsObjNull(self.UIRoot) then
        if show == true then
            if self:CanUseBlurBg() then
                self:_CreateBlurBg(self.__closeBtn, self.UIRoot.transform)
            else
                if not self._noBg then
                    self:_CreateBg(self.__closeBtn, self.UIRoot.transform)
                end
            end

            self:SetCanvasGroup(1)

            --self:SetCanvasGroup(1)
            --展示时使用之前记录好的位置(记录的位置可能是以下之一：界面初始位置或init中设置的ui位置（init中有设置的话）,隐藏ui前的位置）
            if self.recycle_ui and windowMgr then
                windowMgr:SetActive(self.UIRoot, show, self.__parent, self.originalPosition, self)
            else
                self.UIRoot.transform.anchoredPosition = self.originalPosition
                --self.UIRoot:SetActive(show)
            end

            -- 移动到 Init 函数中做初始化
            -- self:InitComponents()
            
            if not self.preShowOnce then
                self.preShowOnce = true
                self:OnPreShowOnce()
            end
            
            --self:OnShow()
            self:SafeCallFunc("OnShow")
            event.Trigger(event.UI_MODULE_SHOW, self.UIModuleName, self)
            -- local net_route = require "net_route"
            -- net_route.SaveUIData(self._NAME, self._NAME, "OnShow")
            if self.scene_name then
                local scene_manager = require "scene_manager"
                scene_manager.instance():LoadSceneComplete(self.scene_name)
            end
        else
            --记录隐藏ui前的位置，显示时使用
            if self.UIRoot.transform.anchoredPosition.x ~= const.UI_HIDE_POSITION.x then
                self.originalPosition = self.UIRoot.transform.anchoredPosition
            end
            --self:SetCanvasGroup(0)

            if self.recycle_ui and windowMgr then
                windowMgr:SetActive(self.UIRoot, show, self.__parent)
            else
                if const.OPEN_EXIST_UI then
                    windowMgr:SetActive(self.UIRoot, show, self.__parent, nil, self)
                else
                    self.UIRoot.transform.anchoredPosition = const.UI_HIDE_POSITION
                end
                --self.UIRoot:SetActive(show)
            end

            self:OnHide()
            event.Trigger(event.UI_MODULE_HIDE, self.UIModuleName)
        end
    end

end

function UIBase:SetCanvasGroup(alpha, isBlockRaycasts)
    if not util.IsObjNull(self.canvasGroupInfo) then
        self.canvasGroupInfo.alpha = alpha
        if isBlockRaycasts ~= nil then
            self.canvasGroupInfo.blocksRaycasts = isBlockRaycasts
        end
    end
end

--只调用一次,在onShow之前的调用,把没必要放在Init的操作放到OnPreShowOnce
function UIBase:OnPreShowOnce()

end

function UIBase:OnShow()
    if self.needAni == true then
        self:OnPlayAShowAni()
    end
    if self.seconds == nil or self.millSeconds == nil then
        return
    end

    if LogLoad then
        local start = self.seconds
        self.seconds = DateTime.Now.Second - self.seconds
        self.millSeconds = DateTime.Now.Millisecond - self.millSeconds
        log.Warning("Init>> 显示UI模块, name=" .. self.UIRoot.gameObject.name .. ", time=" .. tostring(self.seconds * 1000 + self.millSeconds) .. "ms, init="
                .. (self.initSecond and tostring(self.initSecond) or 0)
                .. ", gc="
                .. tostring(GC.GetTotalMemory() - (self.collect or 0))
                .. "\n")

        if self.seconds * 1000 + self.millSeconds > 100 then
            str = str
                    .. "Init>> 显示UI模块, name="
                    .. self.UIRoot.gameObject.name
                    .. ", start="
                    .. tostring(start)
                    .. ", time="
                    .. tostring(self.seconds * 1000 + self.millSeconds)
                    .. "ms, init="
                    .. (self.initSecond and tostring(self.initSecond) or 0)
                    .. ", gc="
                    .. tostring(GC.GetTotalMemory() - (self.collect or 0))
                    .. "\n"

            WriteTextFile(persistentDataPath .. "/timeTest.txt", str)
        end
    end
    self.seconds = nil
    self.millSeconds = nil
    -- 调用一次层级刷新
    if self.UpdateUI then
        self:UpdateUI()
    end
end

function UIBase:OnHide()
end

function UIBase:OnPlayAShowAni(onComplete, onCompleteParam)
    if not self.UIRoot or util.IsObjNull(self.UIRoot) then
        return
    end
    windowMgr:SetActive(self.UIRoot, true, self.__parent, self.originalPosition, self)
    local canvasGroupObj = self.UIRoot:GetComponent(typeof(CanvasGroup))
    if canvasGroupObj and not canvasGroupObj:IsNull() then
        self.UIRoot.transform.localScale = { x = 0.8, y = 0.8, z = 1 };
        canvasGroupObj.alpha = 0.5

        local duration = const.playShowAniTime
        local startTime = os.clock()  -- 记录开始时间
        local initialScale = 0.8       -- 初始缩放
        local targetScale = 1          -- 目标缩放
        if self.scaleOpenAnimationTimer then
            self:RemoveTimer(self.scaleOpenAnimationTimer)
            self.scaleOpenAnimationTimer = nil
        end
        -- 定义更新函数
        local function Update()
            local elapsedTime = os.clock() - startTime
            if elapsedTime < duration then
                local easing = require "easing"
                local scale = easing.outBack(elapsedTime, initialScale, targetScale - initialScale, duration)
                self.UIRoot.transform.localScale = { x = scale, y = scale, z = 1 }
                local alpha = elapsedTime / duration * 0.5 + 0.5  -- 从0.5渐变到1
                canvasGroupObj.alpha = alpha
            else
                -- 动画结束，设置最终状态
                self.UIRoot.transform.localScale = { x = targetScale, y = targetScale, z = 1 }
                canvasGroupObj.alpha = 1
                if onComplete ~= nil then
                    onComplete(onCompleteParam)
                end
                self:RemoveTimer(self.scaleOpenAnimationTimer)
                self.scaleOpenAnimationTimer = nil
            end
        end
        -- 启动更新循环，替换为你的引擎机制，比如使用协程
        self.scaleOpenAnimationTimer = self:CreateTimer(0, Update)  -- 假设有一个创建定时器的函数


        -- self.scaleOpenAnimationTimer = self:Tween(0, 1, 5, function(v)
        --     if self:IsUIVisible() then
        --         local easing = require "easing"
        --         local rate = easing.outBack(v, 0, 1, 1)
        --         local scale = math.lerp(initialScale, targetScale, rate)
        --         local alpha = math.lerp(0.5, 1, rate)
        --         self.UIRoot.transform.localScale = { x = scale, y = scale, z = 1 }
        --         canvasGroupObj.alpha = alpha
        --     end
        -- end,function()
        --     if onComplete ~= nil then
        --         onComplete(onCompleteParam)
        --     end

        -- end)
    else
        if onComplete then
            onComplete(onCompleteParam)
        end
    end
end

function UIBase:OnPlayHideAni()
    if not self.UIRoot or util.IsObjNull(self.UIRoot) then
        return
    end
    local canvasGroupObj = self.UIRoot:GetComponent(typeof(CanvasGroup))
    if util.IsObjNull(canvasGroupObj) then
        if self:IsValid() then
            windowMgr:SetActive(self.UIRoot, false, self.__parent, self.originalPosition, self)
            --self.UIRoot:SetActive(false)
        end
        return
    end
    local duration = const.playShowAniTime
    local startTime = os.clock()  -- 记录开始时间
    local initialScale = 1       -- 初始缩放
    local targetScale = 0.8          -- 目标缩放
    if self.scaleHideAnimationTimer then
        self:RemoveTimer(self.scaleHideAnimationTimer)
        self.scaleHideAnimationTimer = nil
    end
    -- 定义更新函数
    local function Update()
        local elapsedTime = os.clock() - startTime
        if elapsedTime < duration then
            -- 计算新的缩放值
            local easing = require "easing"
            local scale = easing.outBack(elapsedTime, initialScale, targetScale - initialScale, duration)
            self.UIRoot.transform.localScale = { x = scale, y = scale, z = 1 }
            -- 计算新的透明度值
            local alpha = 1 - (elapsedTime / duration)  -- 从1渐变到0
            canvasGroupObj.alpha = alpha
        else
            -- 动画结束，设置最终状态
            self.UIRoot.transform.localScale = { x = targetScale, y = targetScale, z = 1 }
            canvasGroupObj.alpha = 0
            self:RemoveTimer(self.scaleHideAnimationTimer)
            self.scaleHideAnimationTimer = nil
        end
    end
    -- 启动更新循环，替换为你的引擎机制，比如使用协程
    self.scaleHideAnimationTimer = self:CreateTimer(0, Update)  -- 假设有一个创建定时器的函数
    util.DelayCallOnce(const.playHideAniTime, function()
        if self:IsValid() then
            windowMgr:SetActive(self.UIRoot, false, self.__parent, self.originalPosition, self)
            --self.UIRoot:SetActive(false)
        end
    end)
end

function UIBase:StopAni(isShow)
    if ticker then
        util.RemoveDelayCall(ticker)
        ticker = nil
    end
    if self.needAni == true and self.UIRoot and not util.IsObjNull(self.UIRoot) then
        self.UIRoot.transform.localScale = { x = 1, y = 1, z = 1 }
        local canvasGroupObj = self.UIRoot:GetComponent(typeof(CanvasGroup))
        if canvasGroupObj and not canvasGroupObj:IsNull() then
            -- if canvasGroupObj then
            canvasGroupObj.alpha = 1
        end
    end
    if self.closeCallback then
        self.closeCallback()
        self.closeCallback = nil
    end
end

------------------------------------------------单独读取图片------------------------------------------------------------
-- 获得图集，可以统一管理
function UIBase:GetSpriteAssets(atlasName)
    if self.spriteAssetsLs == nil then
        self.spriteAssetsLs = {}
    end
    local targetSpriteAssets = self.spriteAssetsLs[atlasName]
    if targetSpriteAssets == nil then
        local card_sprite_asset = require "card_sprite_asset"
        targetSpriteAssets = card_sprite_asset.CreateCardSpriteAssetWithPath(atlasName)
        self.spriteAssetsLs[atlasName] = targetSpriteAssets
    end
    return targetSpriteAssets
end

-- 传入image图片，图集，图片名字
function UIBase:SetSpriteAssets(imgObj, atlasName, spriteName)
    local targetSpriteAssets = self:GetSpriteAssets(atlasName)
    targetSpriteAssets:GetSprite(spriteName, function(sp)
        if sp == nil then
            return
        end
        if self:IsValid() and not util.IsObjNull(imgObj) then
            imgObj.sprite = sp
        end
    end)
end

-- 设置图片，传入Image,图片ab路径  方法暂不可用
function UIBase:SetSpriteByAb(imgObj, assetPath)
    if not self:IsValid() or util.IsObjNull(imgObj) or assetPath == nil then
        return
    end
    if self.assetLs == nil then
        self.assetLs = {}
    end
    -- 防止重复加载
    local asset = self.assetLs[assetPath] or nil
    if asset ~= nil then
        local spRes = asset
        log.Warning("SetSpriteByAb :: spRes :: spRes.width :: spRes.height",type(spRes),spRes.width,spRes.height,type(spRes.width),type(spRes.height))
        imgObj.sprite = Sprite.Create(spRes, Rect(0, 0, spRes.width, spRes.height), Vector2(0.5, 0.5))
        return
    end
    -- 如果不存在asset，则加载
    if self.assetLoaderLs == nil then
        self.assetLoaderLs = {}
    end
    local assetLoader = self.assetLoaderLs[assetPath] or nil
    if assetLoader == nil then
        assetLoader = asset_loader(assetPath, self.UIModuleName)
        self.assetLoaderLs[assetPath] = assetLoader
    end
    assetLoader:load(function(obj)
        if obj == nil then
            return
        end
        local spRes = obj.asset
        self.assetLs[assetPath] = spRes
        if self:IsValid() and not util.IsObjNull(imgObj) then
            log.Warning("SetSpriteByAb :: spRes :: spRes.width :: spRes.height",type(spRes),spRes.width,spRes.height,type(spRes.width),type(spRes.height))
            imgObj.sprite = Sprite.Create(spRes, Rect(0, 0, spRes.width, spRes.height), Vector2(0.5, 0.5))
        end
    end)
end
-- 自动销毁
function UIBase:UnloadSprite()
    -- 如果有缓存，则不销毁
    if self.assetLoaderLs ~= nil then
        for k, v in pairs(self.assetLoaderLs) do
            v:Dispose()
        end
        self.assetLoaderLs = nil
    end

    if self.spriteAssetsLs ~= nil then
        for k, v in pairs(self.spriteAssetsLs) do
            v:Dispose()
        end
        self.spriteAssetsLs = nil
    end
    if self.assetLs ~= nil then
        self.assetLs = nil
    end
end

function UIBase:ScreenAdaptation()
    local flag = util.Flag_ExternScreen()
    --test
    flag = true
    if flag == true then
        local rt = self.UIRoot.transform:GetComponent(typeof(RectTransform))
        rt.offsetMin = { x = 60, y = 0 }
        rt.offsetMax = { x = -60, y = 0 }

        local bg = self.UIRoot.transform:Find("bg")
        if bg then
            bg.transform.localScale = { x = 1.1, y = 1.1, z = 1 }
        end
    end
end

function UIBase:ShowChildWindow(childWindow)
    childWindow:_SetCloseEvent(self._OnChildWindowClose, self)
    table.insert(self.childWindows, childWindow)
end

function UIBase:ActivateCanvas()
    if self.UIRoot and not util.IsObjNull(self.UIRoot) then
        local canvas = self.UIRoot:GetComponent(CanvasType)
        if canvas and not canvas:IsNull() then
            canvas.enabled = true
            canvas.overrideSorting = true
            --canvas.gameObject:SetActive(true)
        end
    end
end

function UIBase:_OnChildWindowClose(childWindow)
    for k, v in pairs(self.childWindows) do
        if v == childWindow then
            table.remove(self.childWindows, k)
            break
        end
    end
end
function UIBase:IsValid()
    if self and self.UIRoot and not self.UIRoot:IsNull() then
        return true
    else
        return false
    end
end
function UIBase:_SetCloseEvent(func, ...)
    self._onCloseEvent = { func = func, args = { ... } }
end

function UIBase:_ClearCloseEvent()
    self._onCloseEvent = false
end

function UIBase:SetAsLastSibling()
    if self.UIRoot then
        self.UIRoot.transform:SetAsLastSibling()
    end
end
function UIBase:ApplyIndexs(transform, count)
    return self.curOrder, self.curOrder and (self.curOrder + count)
end

function UIBase:IsCacheUI()
    --  --print("UIBase IsCacheUI",const.OPEN_CACHE_WINDOW_RES , self._NAME , event.IsCacheUI(self._NAME))
    return const.OPEN_CACHE_WINDOW_RES and self._NAME and event.IsCacheUI(self._NAME)
end

function UIBase:CachePrefab(UIPrefab)

    if self:IsCacheUI() then
        --  --print("CachePrefab IsCacheUI",self:IsCacheUI(),self._NAME) --and util.IsObjNull(uiProtos[self._NAME])

        if self._NAME and not util.IsObjNull(UIPrefab) then
            ------  --print("CachePrefab",self._NAME)
            uiProtos[self._NAME] = UIPrefab
        end
    end
end

function UIBase:GetOccupied()
    return self.__ui_occupied
end

--@function 获取View-Controller模式的UI
function UIBase:GetVCTypeUI()
    return self.enum_ui_vc_type
end
function UIBase:SetVCTypeUI(type)
    self.enum_ui_vc_type = type
end

function UIBase:OnValueChange(name,order)
    if name == "curOrder"  then
        self:EffectMaskCanvasProcess(order)
    end
end
------红点相关-------
--绑定单个
function UIBase:BindUIRed(tra, id, funcParam, param)
    self.redDataMap[tra] = id
    local red_system = require "red_system"
    red_system.BindRed(tra, id, funcParam, param)
end
--解绑单个
function UIBase:UnBindUIRed(tra, id)
    self.redDataMap[tra] = nil
    local red_system = require "red_system"
    red_system.UnBindRed(tra, id)
end
--Close自动解绑
function UIBase:ClearBindUIRed()
    if not self.recycle_ui then
        --真正关闭的时候解绑
        if next(self.redDataMap) then
            local red_system = require "red_system"
            for tra, id in pairs(self.redDataMap) do
                red_system.UnBindRed(tra, id)
            end
            self.redDataMap = {}
        end
    end
end
--Close之前调用
function UIBase:CloseBefore()
    self:ClearBindUIRed()
    self:RemoveAllOnClick()
end
function UIBase:Close()
    self.__ui_occupied = nil

    event.Trigger(event.UI_MODULE_PREPARE_CLOSE, self.UIModuleName, self._assetBundleName, self.UIRoot, self.recycle_ui, self)
    
    if not self.recycle_ui then
        self:DisposeAllSubset()
        -- 反注册按钮事件
        self:ManageEvent(false)
        self.manageEvent = false
        event.Unregister(event.SCREEN_SIZE_CHANGED, self.ResetWidget)
        self:ClearCreateObj()
        if self.ResetLanguage then
            event.Unregister(event.LANGUAGE_SETTING_CHANGED, self.ResetLanguage)
        end
        -- 不缓存则不注销图片
        self:UnloadSprite()
    end

    if self.effectMaskCanvas then
        self.effectMaskCanvas = {}
    end
    self.closed = true
    --if self.UIRoot and not util.IsObjNull(self.UIRoot) and LeanTween.isTweening(self.UIRoot) then -- 已移动到OnPlayHideAni()函数里
    --	LeanTween.cancel(self.UIRoot)
    --end
    local UIModuleName = self.UIModuleName

    ------  --print("Close:",UIModuleName)
    local aniCallback = function()
        if self._onCloseEvent then
            self._onCloseEvent.func(unpack(self._onCloseEvent.args), self)
        end

        if self.UIRoot and not util.IsObjNull(self.UIRoot) then
            if (self:CanUseBlurBg() and newBgPool) then
                newBgPool:PushElement(self.bgGameObject.transform.parent)
            end
            self:ClearCreateBg()
            event.Trigger(event.UI_MODULE_CLOSE, self.UIModuleName, self._assetBundleName, self.UIRoot, self.recycle_ui, self)

            local childCount = #self.childWindows
            for i = childCount, 1, -1 do
                self.childWindows[i]:_ClearCloseEvent()
                self.childWindows[i]:Close()
            end
            self.childWindows = {}

            if not self.recycle_ui then
                self.UIRoot = false
                self.UIRoot_canvasObj = nil
            end
        else
            if self.loader then

                local st = windowMgr:GetState(self.UIModuleName)
                if st and st.state ~= 2 then
                    --  --print("UnloadModuleUI",value[3],ub.closed,ub.loader and ub.loader.isDisposed)
                    self.loader:Dispose()
                end
            end
            ------ --print("UIBase:Close", self.UIRoot)
            -- GameObject.Destroy(self.UIRoot)
            -- self.UIRoot = false
        end
        -- self.needAni = false
    end

    self:aniClose(aniCallback)
    event.Trigger(event.UI_MODULE_REMOVE_OBJ, UIModuleName, self._assetBundleName)

end

---是否能使用截图做背景的功能
---@return boolean true表示可以
function UIBase:CanUseBlurBg()
    return self.isBlurBg and util.CanUseBlurBg()
end
function UIBase:AddOnClick(onClick, func)
    if onClick and func then
        onClick:RemoveAllListeners()
        onClick:AddListener(func)
        self.addOnClickMap[onClick] = true
    end
end
function UIBase:RemoveAllOnClick()
    for onClick, v in pairs(self.addOnClickMap or {}) do
        onClick:RemoveAllListeners()
    end
    self.addOnClickMap = {}
end
function OnANDROID_BACK()

    local len = #stack
    local cur = nil
    for i = len, 1, -1 do
        local m = stack[i]
        -- m = m and m.id
        if m and m:IsUIVisible() then
            if m.escapeHandle and m.escapeHandle() then
                event.Trigger(event.ON_ANDROID_BACK_RESULT, true)
                break
            end
        end
    end

end
event.Register(event.ANDROID_BACK, OnANDROID_BACK)
--获取所属大层级类型(如果所属层级不是默认层（业务模块层const.UI_LAYER_MODULE），需覆盖此方法)
function GetUILayerType()
    return nil
end
--是否为全屏界面(全屏ui按需覆盖（如果在ui_window_mgr.defaultFullScreenModules中可以不覆盖此方法，但是覆盖此方法后，该方法返回的值优先级最高）)
function IsFullUI()
    return false
end

---xpcall 异常捕获处理
-- 异常捕获
function OnErrCall(err,uiModuleName)
    log.Error( "ERROR:",uiModuleName, err )
    util.AssetBundleManagerTrackEvent("lua_err",{
        type = "window_on_error",
        err = err,
        ui_name = uiModuleName,
        trackback = debug.traceback(),
    })
end
---@public xpcall 安全保护调用
function UIBase:SafeCallFunc(funcname,...)  
    local func = function (...)
        if funcname and self and self[funcname] then
            self[funcname](self,...)
        end
    end
    local check,result = xpcall(func, function( err)
        if self and windowMgr then
            OnErrCall(err,self.UIModuleName)
        end
    end,...)
    if check then
        return result
    else
        if windowMgr and self.UIModuleName then
            local isCache = windowMgr:IsCacheUI(self.UIModuleName)
            if isCache then
                -- 取消界面缓存，界面销毁
                windowMgr:SetCacheUI(self.UIModuleName,false)
            end
            -- 销毁界面
            windowMgr:UnloadModule(self.UIModuleName)           
        end
    end
end

--region 闭包绑定

--- <AUTHOR>
--- @created 2025-09-01
--- @version 1.0
--- @desc 闭包绑定
function UIBase.Bind(self, func, ...)
    if self ~= nil and type(self) ~= "table" then
        debug.LogError("Bind self is not table")
        return
    end

    if func == nil or type(func) ~= "function" then
        debug.LogError("Bind func is not function or nil")
        return
    end
    
    local params = nil
    if self == nil then
        params = SafePack(...)
    else
        params = SafePack(self, ...)
    end
    return function(...)
        local args = ConcatSafePack(params, SafePack(...))
        return func(SafeUnpack(args))
    end
end

-- 对两个SafePack的表执行连接
function ConcatSafePack(safe_pack_l, safe_pack_r)
    local concat = {}
    for i = 1,safe_pack_l.n do
        concat[i] = safe_pack_l[i]
    end
    for i = 1,safe_pack_r.n do
        concat[safe_pack_l.n + i] = safe_pack_r[i]
    end
    concat.n = safe_pack_l.n + safe_pack_r.n
    return concat
end

-- 解决原生pack的nil截断问题，SafePack与SafeUnpack要成对使用
function SafePack(...)
    local params = {...}
    params.n = select('#', ...)
    return params
end

-- 解决原生unpack的nil截断问题，SafePack与SafeUnpack要成对使用
function SafeUnpack(safe_pack_tb)
    return unpack(safe_pack_tb, 1, safe_pack_tb.n)
end
--endregion
--------------------------


local object = require "object"
object.__child = 1
local CUIBase = class(object, nil, UIBase)

return CUIBase

