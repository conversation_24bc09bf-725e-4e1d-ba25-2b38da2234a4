local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local e_handler_mgr = require "e_handler_mgr"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_arena_battle_history_binding"
local face_item = require "face_item_new"

--region View Life
module("ui_arena_battle_history")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.onHistoryRenderFunc = function(...)
        self:OnHistoryRenderFunc(...)
    end
    self.srt_historyList.onItemRender = self.onHistoryRenderFunc
    self.srt_historyList.onItemDispose = function(scroll_rect_item,index)
        if scroll_rect_item.data and scroll_rect_item.data["faceItem"] then
            scroll_rect_item.data["faceItem"]:Dispose()
            scroll_rect_item.data["faceItem"] = nil
        end
    end
    self.VData = {}
end


function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
function UIView:SetListData(data)
    if not data then
        return
    end
    self:SetActive(self.rtf_nothingShow, #data == 0)
    self.srt_historyList:SetData(data, #data)
    self.srt_historyList:Refresh(-1, -1)
    self.srt_historyList.renderPerFrames = 10
end

function UIView:OnHistoryRenderFunc(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    scroll_rect_item.data[3] = scroll_rect_item.data[3] or {}

    local PlayerName = scroll_rect_item:Get("PlayerName")
    local FaceItem = scroll_rect_item:Get("FaceItem")
    local TimeText = scroll_rect_item:Get("TimeText")
    local ScoreText = scroll_rect_item:Get("ScoreText")
    local ScoreTextOutline = scroll_rect_item:Get("ScoreTextOutline")
    local ReplayBtn = scroll_rect_item:Get("ReplayBtn")
    local rankingDown = scroll_rect_item:Get("rankingDown")
    local rankingUp = scroll_rect_item:Get("rankingUp")

    PlayerName.text = string.format("%s %s", dataItem.worldStr, util.SplicingUnionShortName(dataItem.unionShortName, dataItem.name, true))

    TimeText.text = dataItem.time

    ScoreText.text = string.format('<color=#%s>%+d</color>', dataItem.score >= 0 and '87F425' or 'FF8E62', dataItem.score)
    local outColor = dataItem.score >= 0 and { r = 57 / 255, g = 143 / 255, b = 34 / 255, a = 1 } or { r = 147 / 255, g = 77 / 255, b = 49 / 255, z = 1 }
    ScoreTextOutline.effectColor = outColor
    self:SetActive(rankingUp, dataItem.score >= 0)
    self:SetActive(rankingDown, dataItem.score < 0)
    scroll_rect_item.InvokeFunc = scroll_rect_item.InvokeFunc or function(funcname,obj)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index,dataItem)
        end
    end
    local item = scroll_rect_item.data["faceItem"] or face_item.CFaceItem():Init(FaceItem, nil, 1)
    item:SetFaceInfo(dataItem.faceStr, function()
        local player_mgr = require "player_mgr"
        if dataItem.roleID ~= player_mgr.GetPlayerRoleID() then
            local mgr_personalInfo = require "mgr_personalInfo"
            mgr_personalInfo.ShowRoleInfoView(dataItem.roleID)
        end
    end)
    item:SetNewBg(true)
    item:SetFrameID(dataItem.frameID, true)
    item:FrameEffectEnable(true, self.curOrder+1)
    scroll_rect_item.data["faceItem"] = item
    e_handler_mgr.TriggerHandler(window.controller_name, "OnScrollRectItemRender", index, dataItem)
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
