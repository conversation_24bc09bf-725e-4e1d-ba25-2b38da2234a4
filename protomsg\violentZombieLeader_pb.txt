-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local error_code_pb=require("error_code_pb")
local common_new_pb=require("common_new_pb")
module('violentZombieLeader_pb')


V1M=V(4,"violentBossStatus_normal",0,0)
V2M=V(4,"violentBossStatus_weak",1,1)
E1M=E(3,"ViolentBossStatus",".CSMsg.ViolentBossStatus")
V3M=V(4,"violentBossType_normal",0,0)
V4M=V(4,"violentBossType_darkDescendants",1,1)
V5M=V(4,"violentBossType_humanBeings",2,2)
V6M=V(4,"violentBossType_Forest",3,3)
V7M=V(4,"violentBossType_Max",4,4)
E2M=E(3,"ViolentBossType",".CSMsg.ViolentBossType")
F1D=F(2,"sid",".CSMsg.ViolentData.sid",1,0,1,false,0,5,1)
F2D=F(2,"configId",".CSMsg.ViolentData.configId",2,1,1,false,0,5,1)
F3D=F(2,"createTime",".CSMsg.ViolentData.createTime",3,2,1,false,0,5,1)
F4D=F(2,"aliveTime",".CSMsg.ViolentData.aliveTime",4,3,1,false,0,5,1)
F5D=F(2,"configHeath",".CSMsg.ViolentData.configHeath",5,4,1,false,0,5,1)
F6D=F(2,"curHeath",".CSMsg.ViolentData.curHeath",6,5,1,false,0,5,1)
F7D=F(2,"weekStatus",".CSMsg.ViolentData.weekStatus",7,6,1,false,0,5,1)
F8D=F(2,"bossLevel",".CSMsg.ViolentData.bossLevel",8,7,1,false,0,5,1)
F9D=F(2,"tPosX",".CSMsg.ViolentData.tPosX",9,8,1,false,0,5,1)
F10D=F(2,"tPosY",".CSMsg.ViolentData.tPosY",10,9,1,false,0,5,1)
M1G=D(1,"ViolentData",".CSMsg.ViolentData",false,{},{},nil,{})
F11D=F(2,"sandboxSid",".CSMsg.TMSG_VIOLENT_ZOMBIE_DATA_REQ.sandboxSid",1,0,2,false,0,5,1)
F12D=F(2,"atyId",".CSMsg.TMSG_VIOLENT_ZOMBIE_DATA_REQ.atyId",2,1,1,false,0,5,1)
M2G=D(1,"TMSG_VIOLENT_ZOMBIE_DATA_REQ",".CSMsg.TMSG_VIOLENT_ZOMBIE_DATA_REQ",false,{},{},nil,{})
F13D=F(2,"errCode",".CSMsg.TMSG_VIOLENT_ZOMBIE_DATA_RSP.errCode",1,0,2,false,nil,14,8)
F14D=F(2,"data",".CSMsg.TMSG_VIOLENT_ZOMBIE_DATA_RSP.data",2,1,3,false,{},11,10)
M3G=D(1,"TMSG_VIOLENT_ZOMBIE_DATA_RSP",".CSMsg.TMSG_VIOLENT_ZOMBIE_DATA_RSP",false,{},{},nil,{})
F15D=F(2,"sandboxSid",".CSMsg.TMSG_VIOLENT_ZOMBIE_BATTLE_REQ.sandboxSid",1,0,2,false,0,5,1)
F16D=F(2,"atyId",".CSMsg.TMSG_VIOLENT_ZOMBIE_BATTLE_REQ.atyId",2,1,1,false,0,5,1)
F17D=F(2,"bossSid",".CSMsg.TMSG_VIOLENT_ZOMBIE_BATTLE_REQ.bossSid",3,2,1,false,0,5,1)
M5G=D(1,"TMSG_VIOLENT_ZOMBIE_BATTLE_REQ",".CSMsg.TMSG_VIOLENT_ZOMBIE_BATTLE_REQ",false,{},{},nil,{})
F18D=F(2,"errCode",".CSMsg.TMSG_VIOLENT_ZOMBIE_BATTLE_RSP.errCode",1,0,2,false,nil,14,8)
M6G=D(1,"TMSG_VIOLENT_ZOMBIE_BATTLE_RSP",".CSMsg.TMSG_VIOLENT_ZOMBIE_BATTLE_RSP",false,{},{},nil,{})
F19D=F(2,"sandboxSid",".CSMsg.TMSG_VIOLENT_ZOMBIE_GET_REWARD_REQ.sandboxSid",1,0,2,false,0,5,1)
F20D=F(2,"atyId",".CSMsg.TMSG_VIOLENT_ZOMBIE_GET_REWARD_REQ.atyId",2,1,1,false,0,5,1)
F21D=F(2,"bossSid",".CSMsg.TMSG_VIOLENT_ZOMBIE_GET_REWARD_REQ.bossSid",3,2,1,false,0,5,1)
M7G=D(1,"TMSG_VIOLENT_ZOMBIE_GET_REWARD_REQ",".CSMsg.TMSG_VIOLENT_ZOMBIE_GET_REWARD_REQ",false,{},{},nil,{})
F22D=F(2,"errCode",".CSMsg.TMSG_VIOLENT_ZOMBIE_GET_REWARD_RSP.errCode",1,0,2,false,nil,14,8)
M8G=D(1,"TMSG_VIOLENT_ZOMBIE_GET_REWARD_RSP",".CSMsg.TMSG_VIOLENT_ZOMBIE_GET_REWARD_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M}
E2M.values = {V3M,V4M,V5M,V6M,V7M}
M1G.fields={F1D, F2D, F3D, F4D, F5D, F6D, F7D, F8D, F9D, F10D}
M2G.fields={F11D, F12D}
F13D.enum_type=error_code_pb.E1M
F14D.message_type=M1G
M3G.fields={F13D, F14D}
M5G.fields={F15D, F16D, F17D}
F18D.enum_type=error_code_pb.E1M
M6G.fields={F18D}
M7G.fields={F19D, F20D, F21D}
F22D.enum_type=error_code_pb.E1M
M8G.fields={F22D}

TMSG_VIOLENT_ZOMBIE_BATTLE_REQ =M(M5G)
TMSG_VIOLENT_ZOMBIE_BATTLE_RSP =M(M6G)
TMSG_VIOLENT_ZOMBIE_DATA_REQ =M(M2G)
TMSG_VIOLENT_ZOMBIE_DATA_RSP =M(M3G)
TMSG_VIOLENT_ZOMBIE_GET_REWARD_REQ =M(M7G)
TMSG_VIOLENT_ZOMBIE_GET_REWARD_RSP =M(M8G)
ViolentData =M(M1G)
violentBossStatus_normal = 0
violentBossStatus_weak = 1
violentBossType_Forest = 3
violentBossType_Max = 4
violentBossType_darkDescendants = 1
violentBossType_humanBeings = 2
violentBossType_normal = 0

