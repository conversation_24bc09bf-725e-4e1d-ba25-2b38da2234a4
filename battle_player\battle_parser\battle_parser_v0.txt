local require = require
local table = table
local pairs = pairs
local math = math
local string = string
local print = print
local ipairs = ipairs

local game_scheme = require "game_scheme"
local event = require "event"
local tbs_pb = require "tbs_pb"
local gw_hero_mgr = require "gw_hero_mgr"

local battle_data = require "battle_data"
local battle_config = require "battle_config"
local battle_halo = require "battle_halo"
local battle_parser_utility = require "battle_parser_utility"
local model_res = require "model_res"
local lang = require "lang"
local halo_mgr = require "halo_mgr"
-- local domination_mgr = require "domination_mgr"
-- local sociaty_active_boss_mgr = require "sociaty_active_boss_mgr"
-- local activity_boss_challenge = require "activity_boss_challenge"
local common_pb = require "common_new_pb"
local battle_manager = require "battle_manager"
local battle_weapon = require "battle_weapon"
local log = require "log"
local color_palette 		= require"color_palette"

local Utility = CS.War.Script.Utility
local Debug = CS.UnityEngine.Debug
local Application = CS.UnityEngine.Application

module("battle_parser_v0")

local SkillContext = nil

function ParseReport(player, msg)
    battle_data.Clear()

    SkillContext = nil
    WinningStreakContext = {}

    for i = 1, #msg.reports do
        local report = msg.reports[i]
        local rt = report.reportType
        local handler = ReportTable[rt]
        if handler ~= nil then
            if not battle_data.skipBattle 
            or rt == tbs_pb.ReportType_GameOverReport 
            or rt == tbs_pb.ReportType_BuildReport 
            or rt == tbs_pb.ReportType_NumericReport 
            then
                handler(report, player)
            end
        else
            -- 测试情况另外打开
            -- Debug.LogWarning("Error report type " .. rt)
        end
    end
end

function BuildActor(context, player, posOffset)
    local heroCfg = game_scheme:Hero_0(context.cfgID)
    if heroCfg == nil then
        Debug.LogError("No Hero config with id = " .. context.cfgID)
        return
    end

    local starLv = context.props[tbs_pb.EnBuildProp_Star_Lv + 1]
    local heroStarLv = (starLv == 0 and heroCfg.starLv or starLv)

    -- 获取英雄模型路径（包括获取机甲女神开战前变形，及其它开战后技能中变形模型）
    -- 对于开战前变形模型，目前优化成将变形模型作为默认模型加载，而不是加载两种。其它战斗中变形，则在战斗中实例化变形后模型
    local res,transformerPath = gw_hero_mgr.GetHeroModelAssetPath(heroCfg.heroID, heroStarLv, context.props[tbs_pb.EnBuildProp_SkinID + 1] or 0)
    -- TODO:区分战斗中变形和战斗前变形，先实现战斗前变形
    if not string.empty(transformerPath) then
        res = transformerPath
    end


    -- local modulCfg = game_scheme:Modul_0(modulID)
    -- local res = modulCfg.modelPath
    -- res = model_res.GetResPath(res)

    local hp = context.props[tbs_pb.EnBuildProp_Hp + 1]
    local mp = context.props[tbs_pb.EnBuildProp_Mp + 1]
    local lv = context.props[tbs_pb.EnBuildProp_Lv + 1] or heroCfg.monsterLevel
    local skinID = context.props[tbs_pb.EnBuildProp_SkinID + 1]
    local maxhp = context.props[tbs_pb.EnBuildProp_Max_Hp + 1]
    local maxhp_v = context.props[tbs_pb.EnBuildProp_Cure_Max_Hp + 1] -- or maxhp
    local defence = context.props[tbs_pb.EnBuildProp_DefenceVal + 1] --防御值
    local artifactID = context.props[tbs_pb.EnBuildProp_ArtifactID + 1] --神器
    local isTrialHero = context.props[tbs_pb.EnBuildProp_IsTrialHero + 1] --是试用英雄:1; 不是试用英雄:0
    if maxhp_v then
        maxhp_v =  maxhp_v > 0 and maxhp_v -- or maxhp
    end

    ------ print("maxhp_v",context.palID,maxhp,maxhp_v)
    local maxmp = context.props[tbs_pb.EnBuildProp_Max_Mp + 1]
    local pos =  context.Row * 3 + context.Col + posOffset
    if posOffset ~= 0 then --临时处理，敌方英雄使用这个站位，我方使用原版
        pos =  battle_data.GetIndexByRowAndCol(context.Row, context.Col) + posOffset
    end
    --
    local isLargeHp = context:HasField("IsEnLarge") and context.IsEnLarge
    local talents = {}

    local talent_index_first = tbs_pb.EnBuildProp_Skill1 + 1
    local talent_index_end = tbs_pb.EnBuildProp_Skill6 + 1
    for i=talent_index_first,talent_index_end do
        local talentID = context.props[i]
        if talentID then
            table.insert( talents,talentID)
        else
            table.insert( talents,0)
        end
    end

    battle_data.BuildActorData(context)

    if isLargeHp then
        if battle_data.stageType == common_pb.BrokenSpaceTime or battle_data.stageType == common_pb.LeaActivityBoss then

        else
            -- lowhp和lowmaxhp目前都是0，不清楚这里的规则
            local lowhp = context.props[tbs_pb.EnBuildProp_Low_Hp + 1] or 0
            local lowmaxhp = context.props[tbs_pb.EnBuildProp_Low_Max_Hp + 1] or 0

            -- isLargeHp 时，客户端缩小 1000倍显示?
            hp = hp + lowhp * 0.001
            maxhp = maxhp + lowmaxhp * 0.001
        end
    end
    battle_data.RegisterHero(context.palID, context.cfgID, pos, hp, mp, maxhp, maxmp, lv, heroCfg, starLv, modulCfg,maxhp_v,talents,skinID, defence, isLargeHp,isTrialHero, artifactID)
    
    --连续战斗时，不初始化左侧阵容显示对象
    if battle_data.isInSeriesBattle and posOffset == 0 then
        return
    end
    --!!!!!!! isLargeHp 特殊处理，传给C#值固定为false，在lua端，当isLargeHp为true时，所有hp，maxHp，和伤害恢复都应该乘0.001，保证显示正确

    if not battle_data.skipBattle then
        local bubbleCfg = game_scheme:BubbleHeroTips_0(heroCfg.nameID)
        if player.SetUseMeshSimplify then
            player:SetUseMeshSimplify(true)
        end
        
        -- log.Error("RegisterActor context palID:", context.palID, ",pos:", pos, ",res:", res)
        player:RegisterActor(context.palID, pos, res)
        
        --变身英雄特殊处理
        --print("!!!!!!!!!!!!!!!!!模型id：", context.cfgID)
        local replaceHeroDatas = battle_data.GetReplaceHeroCfgDatas()
        local replaceBodyModelId = replaceHeroDatas[context.cfgID]
        if replaceBodyModelId then 
            --初始化变身英雄的替身模型
            local replaceModelPalId = context.palID + 100 --虚拟替身模型palID
            local modelCfg = game_scheme:Modul_0(replaceBodyModelId)
            local replaceBodyPath = model_res.GetResPath(modelCfg.modelPath)
            player:RegisterActor(replaceModelPalId, pos, replaceBodyPath)
            --保存变身英雄和替身id，后续使用
            battle_data.SetReplaceHeroPalIdData(context.palID, replaceModelPalId)
            --print("初始化英雄替身模型:", replaceCfg.heroID, replaceBodyPath, replaceModelPalId, pos)
        end
        if battle_data.stageType == common_pb.BrokenSpaceTime or battle_data.stageType == common_pb.LeaActivityBoss or battle_data.stageType == common_pb.BossTrial then
            if  pos > 5 then
                --log.Error("Boss")
                if (Utility.IsInEditor() or Utility.VersionIsHigher(Application.version, '1.0.48')) and player.SetActorHudData then
                    player:SetActorHudData(context.palID, hp, mp, maxhp, maxmp, lv, battle_config.faction[heroCfg.type], false, heroCfg.HpLayer == 0 and 1 or heroCfg.HpLayer,true)
                    local hpInfo = {}
                    local powerID = nil

                    if battle_manager.GetIsReplay() then
                        powerID = battle_manager.GetPowerID()
                    end
                    if battle_data.stageType == common_pb.BrokenSpaceTime then 
                        -- hpInfo = domination_mgr.CalBossHpInfo(powerID)
                    elseif battle_data.stageType == common_pb.LeaActivityBoss then
                        -- hpInfo = sociaty_active_boss_mgr.CalBossHpInfo(powerID)
                   elseif battle_data.stageType == common_pb.BossTrial then
                        -- hpInfo = activity_boss_challenge.CalBossHpInfo(powerID)
                    end

                    if hpInfo then
                        for _, v in ipairs(hpInfo) do
                            if player.SetActorBossHpData then
                                player:SetActorBossHpData(context.palID, v.startHp, v.endHp)
                            end
                        end
                    end
                else
                    player:SetActorHudData(context.palID, hp, mp, maxhp, maxmp, lv, battle_config.faction[heroCfg.type], false, heroCfg.HpLayer == 0 and 1 or heroCfg.HpLayer,false)
                end
            else
                player:SetActorHudData(context.palID, hp, mp, maxhp, maxmp, lv, battle_config.faction[heroCfg.type], false, heroCfg.HpLayer == 0 and 1 or heroCfg.HpLayer,false)
            end
        else
            player:SetActorHudData(context.palID, hp, mp, maxhp, maxmp, lv, battle_config.faction[heroCfg.type], false, heroCfg.HpLayer == 0 and 1 or heroCfg.HpLayer,false)
        end

        if bubbleCfg then
            player:SetActorBubbleData(context.palID, lang.Get(heroCfg.nameID), starLv, lang.Get(bubbleCfg.clickBubble.data[0]), lang.Get(bubbleCfg.clickBubble.data[1]), heroCfg.type, lang.Get(bubbleCfg.dialogue.data[0]))
        end
    end
end

function BuildWeapon(context, player, posOffset)

    
    local droneData = require "gw_home_drone_data"

    local weaponCfg = droneData.OnGetDroneCfgData()
    local mp = 0
    --local icon = weaponCfg.icon
    local lv =  0--等级默认值

    if posOffset == 13 or posOffset == 14 then
        mp = context and context.props and context.props[1] or 0
        lv = context and context.props and context.props[2] or 0--实际武器等级
    end

    --TODO：测试,参数hp,maxhp,maxmp,lv均未定义，语法错误
    local hp = context and context.props and context.props[tbs_pb.EnBuildProp_Hp + 1] or 0
    local maxhp = context and context.props and context.props[tbs_pb.EnBuildProp_Max_Hp + 1] or 0
    local maxmp = context and context.props and context.props[tbs_pb.EnBuildProp_Max_Mp + 1] or 0
--[[    local adornId = context.adornId
    local skinId = context and context.props and context.props[tbs_pb.EnBuildProp_SkinID + 1] or 0
    local star = context and context.props and context.props[tbs_pb.EnBuildProp_Star_Lv + 1] or 0]]
    
    battle_data.RegisterWeapon(context.palID, context.cfgID, posOffset, hp, mp, maxhp, maxmp, lv, weaponCfg)

    battle_data.SetWeaponAdorn(context.palID,context.adornId)
    
    --连续战斗时，不初始化左侧武器
    if battle_data.isInSeriesBattle and posOffset == 13 then
        return
    end
    if not battle_data.skipBattle then
        --[[local weapon_data = require "weapon_data"
        local weaponData = weapon_data.GetWeaponByID(weaponID)
        local mp = 0
        if posOffset == 13 then
            mp = context and context.props and context.props[1] or 0
        end
        --local weaponCfg = game_scheme:MagicWeapon_0(weaponID, 1, weaponData.part[1].nLv,0)
        local hp = context and context.props and context.props[tbs_pb.EnBuildProp_Hp + 1] or 0
        local maxhp = context and context.props and context.props[tbs_pb.EnBuildProp_Max_Hp + 1] or 0
        local maxmp = context and context.props and context.props[tbs_pb.EnBuildProp_Max_Mp + 1] or 0
        local lv = weaponData and weaponData.part and weaponData.part[1].nLv
        battle_data.RegisterWeapon(context.palID, context.cfgID, posOffset, hp, mp, maxhp, maxmp, lv)]]
        player:RegisterWeapon(context.palID)
    end
end

function BuildSummoned(context, player, posOffset)
    local battle_data = require "battle_data"
    if not battle_data.skipBattle then
        player:RegisterWeapon(context.palID)
    end
end

function OnBuildReport(msg, player)
    local report = msg.buildReport

    battle_data.stageType = report.stageType
    battle_data.stageLv = report.stageLv
    battle_data.attacker = report.leftID
    battle_data.defender = report.rightID
    battle_data.leftPals = report.leftPals
    battle_data.rightPals = report.rightPals

    -- 这是啥 为什么在这里
    -- local lobby_mgr = require("lobby_mgr")
    -- lobby_mgr.SetSceneRootActive(false)

    --print("OnBuildReport mapID:", report.mapID)
    --log.Error(report.mapID)
    --根据实验，mapID传入C#方法中对于编队的选择几乎没有，唯一的作用就是判定是否为BOSS，从而可能可以变为5V1，但这需要排期，所以临时处理。
    --if report.mapID > 0 then
    --    player:BattleBegin(report.mapID, report.stageType)
    --else
        player:BattleBegin(report.stageType)
    --end

    battle_config.SelectBattleBackground()

    if report:HasField("nTime") and report.nTime ~= 0 then
        player:InitializeRandom(report.nTime)
    else
        local data = msg:SerializeToString()
        local hash = Utility.BKDRHash(data)
        player:InitializeRandom(hash)
    end

    local leftPalId = nil
    local rightPalId = nil
    
    --print("<color=#ff0000ff>!!!!!!!!!!leftPals count:", #report.leftPals, "rightPals count:</color>", #report.rightPals)
    local battle_series_manager = require "battle_series_manager"
    if battle_series_manager.IsSeriesBattle(battle_data.stageType) then
        for i = 6, 12 do
            --连续战斗对手阵容强制显示为6个槽位（策划需求）
            if player.SetLayoutOccupiedPosition then
                player:SetLayoutOccupiedPosition(i)
            end
        end
    end
    if report.leftPals then
        for i = 1, #report.leftPals do
            local context = report.leftPals[i]
            BuildActor(context, player, 0)
    
            if leftPalId == nil then
                leftPalId = report.leftPals[i].palID --赋值的意义？
            end
        end
    end
    if report.rightPals then
        for i = 1, #report.rightPals do
            local context = report.rightPals[i]
            BuildActor(context, player, 6)
    
            if rightPalId == nil then
                rightPalId = report.rightPals[i].palID
            end
        end
    end

    battle_weapon.Clear()
    
    --连续战斗时，不初始化左侧武器
    
        if report.leftAnimal and report.leftAnimal.palID ~= 0 then
            BuildWeapon(report.leftAnimal, player, 13)
            battle_weapon.showWeaponEnter = true
            battle_weapon.leftWeaponID = report.leftAnimal.adornId
        end

    if report.rightAnimal and report.rightAnimal.palID ~= 0 then
        BuildWeapon(report.rightAnimal, player, 14)
        battle_weapon.showWeaponEnter = true
        battle_weapon.rightWeaponID = report.rightAnimal.adornId
    end

    if battle_weapon.showWeaponEnter then
        player:SetupPrelude(battle_config.GetWeaponEnterRes())
    end
    --中途切入战斗不显示开战动画
    if not report.isCutOverBattle and not battle_data.GetIsCancelOpenBattleAni() then 
        player:SetupPrelude(battle_config.GetPreludeTimelineRes())
    end
    
    local playerHeroType = {}
    for i, v in ipairs(report.leftPals) do
        local type = game_scheme:Hero_0(v.cfgID).type
        table.insert(playerHeroType,type)
    end
    battle_halo.leftHaloData =
    {
        heroType = playerHeroType,
        isEnemy = false
    }
    
    --客户端计算敌方光环，很怪。
    if report ~= nil then
        local tempHalos = halo_mgr.CalPotentialActivateHalo(report.rightPals,true)
        local tempHalo = halo_mgr.CalActivateHalo(tempHalos)
        if tempHalo ~= nil then
            battle_halo.rightHaloId = tempHalo
        else
            battle_data.rightHaloId = 0
            battle_halo.rightHaloId = 0
        end
        
        local tempMyHalos = halo_mgr.CalPotentialActivateHalo(report.leftPals,true)
        local tempMyHalo = halo_mgr.CalActivateHalo(tempMyHalos)
        if tempMyHalo then
            battle_halo.leftHaloId = tempMyHalo
        else
            battle_halo.leftHaloId = report.leftHaloid
        end
        
        local enemyHeroType = {}
        for i, v in ipairs(report.rightPals) do
            local type = game_scheme:Hero_0(v.cfgID).type
            table.insert(enemyHeroType,type)
        end
        battle_halo.rightHaloData =
        {
            heroType = enemyHeroType,
            isEnemy = true
        }
    end

    player:SetBubbleFontSize(battle_config.GetBubbleFontSize())

    event.Trigger(event.BATTLE_START)
end



function OnPalDieReport(msg, player)
    local report = msg.OnPalDieReport
end

function OnRoundReport(msg, player)
    if SkillContext ~= nil then
        FlushSkillContext(player)
    end
    local report = msg.roundReport
    battle_data.SetRoundTimes(report.round)
    if report:HasField("bEnd") and report.bEnd then
        player:RoundEnd()
    else
        player:RoundStart(report.round)
        battle_config.CheckIsExcuteGuide(player, report.round)
    end
end

function OnUseSkillReport(msg, player)
    local report = msg.useSkillReport

    if SkillContext ~= nil then
        FlushSkillContext(player)
    end

    SkillContext = {}
    SkillContext.casterID = report.palID
    SkillContext.skillID = report.skillID
    SkillContext.descriptor = {}
    SkillContext.targets = report.targets
end

function OnAddBuffReport(msg, player)
    local report = msg.addBuffReport

    if SkillContext == nil then
        battle_parser_utility.AppendAddBuffPackage(player, report.target, report.buffID, report.buffLevel)
    else
        local descript = AcquireDescriptor(report.target)
        if descript.addbuff == nil then
            descript.addbuff = {}
        end
        table.insert(descript.addbuff, {report.buffID, report.buffLevel})
    end
end

function OnRemoveBuffReport(msg, player)
    local report = msg.removeBuffReport

    if SkillContext == nil then
        battle_parser_utility.AppendRemoveBuffPackage(player, report.target, report.buffID, report.buffLevel)
    else
        local descript = AcquireDescriptor(report.target)
        if descript.removeBuff == nil then
            descript.removeBuff = {}
        end
        table.insert(descript.removeBuff, {report.buffID, report.buffLevel})
    end
end

function OnNumericReport(msg, player)
    local report = msg.numericReport

    if SkillContext == nil then
        SkillContext = {}
        SkillContext.descriptor = {}
    end

    local descript = AcquireDescriptor(report.targets)
    descript.dead = report:HasField("Dead") and report.Dead or descript.dead
    descript.critical = report:HasField("Critical") and report.Critical or descript.critical
    descript.breakTrend = report:HasField("breakTrend") and report.breakTrend or false  --原力压制
    descript.needHit = report.actionID == SkillContext.skillID or descript.needHit
    descript.actionID = report.actionID

    if report.actionID == SkillContext.skillID then
        descript.needHit = descript and descript.needHit or true
    else
        if descript.buffHit == nil then
            descript.buffHit = {}
        end
        table.insert(descript.buffHit, report.actionID)
    end

    if report.numType == tbs_pb.NumericType_HP then
        if report.numbers > 0 then
            descript.healHp = descript.healHp + report.numbers
            battle_data.count_heal(report.palID, report.numbers)
        else
            descript.damageHp = descript.damageHp + report.numbers
            battle_data.count_damage(report.palID, math.abs(report.numbers))
            battle_data.count_hurt(report.targets, math.abs(report.numbers))
        end
    elseif report.numberType == tbs_pb.NumbericType_Mp then
        if report.numbers > 0 then
            descript.generateMp = descript.generateMp + report.numbers
        else
            descript.consumeMp = descript.consumeMp + report.numbers
        end
    end
end

function OnGameOverReport(msg, player)
    local report = msg.gameOverReport

    if SkillContext ~= nil then
        FlushSkillContext(player)
    end

    battle_parser_utility.SetRewards(report.winner, report.loser, report.winnerRewards, report.loserRewards)
    player:BattleEnd()
end

function OnStartActionReport(msg, player)
    local report  = msg.startActionReport
end

function OnDestroyReport(msg, player)
    local report = msg.destroyReport
    player:BattleEnd()
end

function OnLogReport(msg, player)
end

function OnWeaponEnergyReport(msg, player)
    local report = msg.wpEnergyReport
    player:WeaponPowerChange(report.palID,report.masterId,report.value)
end

function OnCutsceneReport(msg, player)
    player:SetMaskEnable(true)
    local report = msg.cutsceneReport

    if SkillContext ~= nil then
        FlushSkillContext(player)
    end

    local cutsceneResrouce = battle_config.GetCutsceneRes(report.cutsceneID)
    if report.cutsceneID == 0 or report.cutsceneID == 9 then
        player:SetupPreludeCutscene(cutsceneResrouce)
    elseif report.cutsceneID == 8 or report.cutsceneID == 10 then
        player:SetupPostludeCutscene(cutsceneResrouce)
    else
        player:SetupCutscene(cutsceneResrouce)
    end
end

function OnSuspendReport(msg, player)
    local report = msg.suspendReport

    if SkillContext ~= nil then
        FlushSkillContext(player)
    end

    player:SetupSuspend(report.palID, battle_config.defaultSuspend)
end

function OnSdDamageReport(msg, player)
    local report = msg.sddmgincrReport
    if report then
        local common_new_pb = require "common_new_pb"
		if report.stageType == common_new_pb.LeaActivityBoss then
			-- local sociaty_active_boss_mgr = require "sociaty_active_boss_mgr"
			-- sociaty_active_boss_mgr.UpdateBattleOverData(report)
		else
			-- 虚空主宰数据
			-- local domination_mgr = require "domination_mgr"
			-- domination_mgr.UpdateBattleOverData(report)
		end
    end
end


ReportTable = {
    [tbs_pb.ReportType_BuildReport] = OnBuildReport,
    [tbs_pb.ReportType_PalDieReport] = OnPalDieReport,
    [tbs_pb.ReportType_RoundReport] = OnRoundReport,
    [tbs_pb.ReportType_UseSkillReport] = OnUseSkillReport,
    [tbs_pb.ReportType_AddBuffReport] = OnAddBuffReport,
    [tbs_pb.ReportType_RemoveBuffReport] = OnRemoveBuffReport,
    [tbs_pb.ReportType_NumericReport] = OnNumericReport,
    [tbs_pb.ReportType_GameOverReport] = OnGameOverReport,
    [tbs_pb.ReportType_StartActionReport] = OnStartActionReport,
    [tbs_pb.ReportType_DestroyReport] = OnDestroyReport,
    [tbs_pb.ReportType_LogReport] = OnLogReport,
    [tbs_pb.ReportType_WeaponEnergyReport] = OnWeaponEnergyReport,
    [tbs_pb.ReportType_CutsceneReport] = OnCutsceneReport,
    [tbs_pb.ReportType_SuspendReport] = OnSuspendReport,
    [tbs_pb.ReportType_SdDmgIncrReport] = OnSdDamageReport,
}

function AcquireDescriptor(target)
    local descript = SkillContext.descriptor[target]
    if descript == nil then
        descript = {}
        descript.damageHp = 0
        descript.healHp = 0
        descript.consumeMp = 0
        descript.generateMp = 0
        descript.shieldChanged = 0
        SkillContext.descriptor[target] = descript
    end
    return descript
end

function FlushSkillContext(player)
    local mainSkill = SkillContext.skillID ~= nil
    local segment = 1

    if mainSkill then
        local skillCfg = game_scheme:Skill_0(SkillContext.skillID)
        local skillRes = battle_config.GetConfigField(skillCfg, "strPath", battle_config.defaultAtk)
        segment = battle_config.GetConfigField(skillCfg, "Multistage", 1)

        local data = battle_data.GetHeroByID(SkillContext.casterID)
        if data and data.skinID then
            local skinCfg = game_scheme:Skin_0(data.skinID)
            if skinCfg then
                local SkillNum = battle_config.GetConfigField(skinCfg, "SkillNum")
                if SkillNum then
                    local skinSkillCfg = game_scheme:SkinSkill_0(SkillContext.skillID)
                    skillRes = battle_config.GetConfigField(skinSkillCfg, "strPath"..SkillNum, skillRes)
                    segment = battle_config.GetConfigField(skinSkillCfg, "Multistage"..SkillNum, 1)
                end
            end
        end
        segment = math.max(segment, 1)

        local suspend = battle_config.GetSuspendResBeforeSkill(SkillContext.casterID, skillRes)
        if suspend then
            player:SetupSuspend(SkillContext.casterID, suspend)
        end
        
        --如果有场外大招，在这里插入到队列
        -- local outsideskillRes = battle_config.GetConfigField(skillCfg, "OutsideSceneSkill", "")
        -- if outsideskillRes ~= "" then
            -- local skillInfo = battle_config.GetBubbleSillDetail(SkillContext.skillID)
            -- local skillname = lang.Get(skillCfg.strName)
            -- local skilldesc = lang.Get(skillCfg.strDesc)
            -- player:InsertSkillDetail(SkillContext.casterID,skillInfo.name,skillname,skilldesc,skillInfo.image)
            -- player:InsertOutsideSkill(SkillContext.casterID, outsideskillRes)
        -- end
        -- who cast what skill
        player:BeginSkill(SkillContext.casterID, SkillContext.targets, skillRes)

        -- skill bubble
        battle_parser_utility.AppendBubbleSkillPackage(player, SkillContext.casterID, SkillContext.skillID)
        -- if outsideskillRes ~= "" then
        --     player:InsertHightLight(SkillContext.casterID,false)
        -- end

        --2022-4-7加入技能名字
        if data then
            local cfg_skillName = battle_config.GetBattleSkillName(data)
            if cfg_skillName then
                local heroCfg = game_scheme:Hero_0(data.heroID)
                local cfg_skill = heroCfg and game_scheme:HeroSkill_0(heroCfg.heroSkillId.data[0], 1)
                local cfg_skinheroskill = data.skinID and data.skinID > 0 and game_scheme:SkinHeroSkill_0(data.skinID, cfg_skill.heroSkillID, cfg_skill.Lv)
                local skillName = lang.Get(cfg_skill and cfg_skill.nameID or lang.KEY_ACTOR_ROLE_NAMENILL)
                if cfg_skinheroskill and cfg_skinheroskill.nameID then
                    skillName = lang.Get(cfg_skinheroskill.nameI)
                else
                    skillName = lang.Get(cfg_skill and cfg_skill.nameID or lang.KEY_ACTOR_ROLE_NAMENILL)
                end
                local keyColors = {}

                if cfg_skillName.colorOutline ~= nil and cfg_skillName.colorUp ~= nil or cfg_skillName.colorDown ~= nil then
                    keyColors[1] = color_palette.HexToColor(cfg_skillName.colorOutline)
                    keyColors[2] = color_palette.HexToColor(cfg_skillName.colorUp)
                    keyColors[3] = color_palette.HexToColor(cfg_skillName.colorDown)
                    local imageName = cfg_skillName.imageName or ""
                    player:InsertSkillDetail(SkillContext.casterID,skillName,keyColors,imageName)

                else
                    log.Error("[buff]SkillName load color data Error, please check BattleSkillName.csv")
                end
            end
        end
    end

    local bubbleDead = false

    -- who suffered the skill and
    local deadCount = 0
    local defaultDieRes = battle_config.GetDefaultDieBundleName()
    local winningstreakDelayTime = 1

    for palID, descript in pairs(SkillContext.descriptor) do
        --Debug.LogWarning("palID："..palID..",descript:"..descript)
        local modulCfg = battle_data.GetHeroModuleCfg(palID)

        if descript.dead then
            deadCount = deadCount + 1
            if deadCount > 1 then
                defaultDieRes = battle_config.GetDefaultDieBundleName(true)
            end
            --Debug.LogWarning("play dead audio:"..defaultDieRes)
        end

        if palID ~= SkillContext.casterID and descript.damageHp ~= 0 then
            if descript.dead then
                -- 计算连胜次数
                local roleWinningStreak = WinningStreakContext[SkillContext.casterID]
                 local tmpWinningStreak = roleWinningStreak
                 if tmpWinningStreak == nil then
                     tmpWinningStreak = 0
                 end
                 tmpWinningStreak = tmpWinningStreak + 1

                battle_parser_utility.AppendHitAndDieForSegments(player, palID, segment, modulCfg, defaultDieRes)
                bubbleDead = battle_parser_utility.AppendBubbleDiePackage(player, palID, bubbleDead)

                if deadCount > 1 then
                    battle_parser_utility.AppendWinningStreak(player, SkillContext.casterID, palID,
                        tmpWinningStreak, segment, (deadCount - 1) * winningstreakDelayTime)
                end

                if tmpWinningStreak ~= roleWinningStreak and SkillContext.casterID ~= nil then
                    WinningStreakContext[SkillContext.casterID] = tmpWinningStreak
                end

            elseif SkillContext.casterID ~= nil and descript.needHit then
                battle_parser_utility.AppendHitForSegments(player, palID, segment, modulCfg)
            end

        elseif palID == SkillContext.casterID and descript.damageHp ~= 0 and descript.dead then
            -- 自己发的技能，触发了别人的buff，把自己反弹死了，要播死亡效果
            local dieRes = battle_config.GetConfigField(modulCfg, "die", defaultDieRes)
            battle_parser_utility.AppendDieForSegment(player, palID, 0, dieRes)
        end

        battle_data.count_dead(palID, descript.dead)

        local deltaHp = descript.damageHp + descript.healHp
        local deltaMp = descript.consumeMp + descript.generateMp
        local deltaSp = 0
        battle_data.count_hp(palID, deltaHp)
        battle_parser_utility.AppendHudAverage(player, palID, segment, deltaHp, deltaMp, deltaSp, SkillContext.casterID or 0, descript.damageHp)
        battle_parser_utility.ApppendFloatTextAverage(player, palID, segment,battle_parser_utility.GetFloatTextStyle(SkillContext.casterID, palID, descript.critical,descript.breakTrend),descript.damageHp, 0)
        battle_parser_utility.ApppendFloatTextAverageWithPrefix(player, palID, segment, "heal", descript.healHp, "+")

        if descript.addbuff ~= nil then
            for _, t in pairs(descript.addbuff) do
                battle_parser_utility.AppendAddBuffPackage(player, palID, t[1], t[2])
            end
        end

        if descript.removeBuff ~= nil then
            for _, t in pairs(descript.removeBuff) do
                battle_parser_utility.AppendRemoveBuffPackage(player, palID, t[1], t[2])
            end
        end

        if descript.buffHit ~= nil then
            for _, buffId in pairs(descript.buffHit) do
                battle_parser_utility.AppendBuffHitPackage(player, palID, buffId, 1)
            end
        end
    end

    if mainSkill then
        player:EndSkill()
    end
    SkillContext = nil
end