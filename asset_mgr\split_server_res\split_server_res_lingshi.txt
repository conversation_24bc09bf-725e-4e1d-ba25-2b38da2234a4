local require = require
local util = require "util"
local math = math
local string = string
local tonumber = tonumber
local CS = CS
local File = CS.System.IO.File
local Directory = CS.System.IO.Directory
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local Path = CS.System.IO.Path
local SearchOption = CS.System.IO.SearchOption
local SplitServerResVerMgr = CS.War.Base.SplitServerResVerMgr
local AssetBundleManager = CS.War.Base.AssetBundleManager
local SplitServerFinishFileRecorder = CS.War.Res.SplitServerFinishFileRecorder
local split_server_res_ver_mgr = require"split_server_res_ver_mgr"
local split_define = require"split_server_res_define"
local Warning = split_define.logger.Warning

module("split_server_res_lingshi")

--备份当前版本(备份ncfiles.txt), 用于版本还原方便测试
function BackupVersion()
    local sourcePath = string.format("%s%s", AssetBundleManager.HttpDownloadingPath, "ncfiles.txt")
    local backupPath = string.format("%s%s", sourcePath, ".bak")
    Warning(0, "BackupCurVersion, sourcePath: ", sourcePath)
    
    if File.Exists(backupPath) then
        Warning(0, "BackupCurVersion, backup is exist, backupPath: ", backupPath)
        return 
    end

    if File.Exists(sourcePath) then
        File.Copy(sourcePath, backupPath, true)
        PlayerPrefs.SetInt("split_server_res_lingshi_curVer", split_server_res_ver_mgr.GetCurUseFilesVer())
        Warning(0, "BackupCurVersion, backup ncfiles.txt, backupPath: ", backupPath)
    else
        Warning(0, "BackupCurVersion, sourcePath is not exist, sourcePath: ", sourcePath)
    end
end

--删除某个版本，不要在下载中删除
function DeleteVersion(version)
    local finishRecorderFilePath = string.format("%s%s%d.txt", AssetBundleManager.HttpDownloadingPath, "finishFileRecorder", version)
    local sourcePath = string.format("%s%s", AssetBundleManager.HttpDownloadingPath, "ncfiles.txt")
    local backupPath = string.format("%s%s", sourcePath, ".bak")
    if not File.Exists(finishRecorderFilePath) then
        Warning(0, "DeleteVersion, version is not exist: ", finishRecorderFilePath)
        return 
    end

    if not File.Exists(sourcePath) then
        Warning(0, "DeleteVersion, ncfiles is not exist: ", sourcePath)
        return
    end

    if not File.Exists(backupPath) then
        Warning(0, "DeleteVersion, ncfiles backup file is not exist: ", backupPath)
        return
    end

    local lines = File.ReadAllLines(finishRecorderFilePath)
    if not util.IsObjNull(lines) then
        for i = 0, lines.Length - 1 do
            local path = string.format("%s%s", AssetBundleManager.HttpDownloadingPath, lines[i])
            Warning(0, "DeleteVersion, delete file, path: ", path)
            if File.Exists(path) then
                File.Delete(path)
                Warning(0, "DeleteVersion, delete file success, path: ", path)
            end
        end
    end
    File.Delete(finishRecorderFilePath)
    if version ~= split_server_res_ver_mgr.GetCurUseFilesVer() then
        local hashRemoteVirtualFilePath = string.format("%s%s%d.txt", AssetBundleManager.HttpDownloadingPath, "hashRemoteVirtualFile", version)
        if File.Exists(hashRemoteVirtualFilePath) then
            File.Delete(hashRemoteVirtualFilePath)
            Warning(0, "DeleteVersion, delete hashRemoteVirtualFile success, path: ", hashRemoteVirtualFilePath)
        end 
    end
    
    Warning(0, "DeleteVersion, delete record file success, path: ", finishRecorderFilePath)
    File.Copy(backupPath, sourcePath, true)
    SplitServerFinishFileRecorder.ClearVersion(version)
end

--删除所有版本，不要在下载中删除 (会把和其他版本共用的文件删掉)
function DeleteAllVersion()
    local files = Directory.GetFiles(AssetBundleManager.HttpDownloadingPath, "finishFileRecorder*.txt", SearchOption.TopDirectoryOnly)
    local pattern = "finishFileRecorder(%d+)%.txt"
    for i = 0, files.Length - 1 do
        local filePath = files[i]
        local fileName = Path.GetFileName(filePath)
        local match = string.match(fileName, pattern)
        if match then
            local version = tonumber(match)
            Warning(0, "DeleteAllVersion, start delete version: ", version)
            DeleteVersion(version)
        end
    end
end

--自动增加当前版本-到远端最大版本中间的所有版本（包含当前版本和最大版本, 不基于服务器资源版本配置）
function AutoAddVersionTasks()
    local setting_server_data = require"setting_server_data"
    local curServerId = setting_server_data.GetLoginWorldID()
    local downloadResVer = split_server_res_ver_mgr.GetRemoteFilesVer()
    local curUseResVersion = split_server_res_ver_mgr.GetCurUseFilesVer()
    local split_server_res_download_mgr = require"split_server_res_download_mgr"
    
    local serverId = curServerId + 1
    local verTaskType = split_define.VerTaskType.PreDownload
    local downloadVersion
    for i = curUseResVersion + 1, downloadResVer do
        downloadVersion = i
        if i == downloadResVer then
            serverId = curServerId
            verTaskType = split_define.VerTaskType.PreDownload
        else
            serverId = serverId + 1
            verTaskType = split_define.VerTaskType.SwitchServerResDownload
        end
        
        split_server_res_download_mgr.AddVersionTaskToList(downloadVersion, serverId, verTaskType)
        Warning(0, "AutoAddVersion, version: ", downloadVersion, "serverId:", serverId, "verTaskType:", verTaskType)
        if i ~= downloadResVer then
            serverId = serverId + 1
            split_server_res_download_mgr.AddVersionTaskToList(downloadVersion, serverId, verTaskType)
            Warning(0, "AutoAddVersion, version: ", downloadVersion, "serverId:", serverId, "verTaskType:", verTaskType)
        end
    end

    downloadVersion = curUseResVersion
    serverId = curServerId
    verTaskType = split_define.VerTaskType.BgDownload
    split_server_res_download_mgr.AddVersionTaskToList(downloadVersion, serverId, verTaskType)
    Warning(0, "AutoAddVersion, version: ", downloadVersion, "serverId:", serverId, "verTaskType:", verTaskType)
end

--自动增加版本1-版本2中间的所有版本（不基于服务器资源版本配置）
function AutoAddVersionTasksEx(version1, version2)
    local setting_server_data = require"setting_server_data"
    local curServerId = setting_server_data.GetLoginWorldID()
    local remoteFilesVer = split_server_res_ver_mgr.GetRemoteFilesVer()
    local curUseResVersion = split_server_res_ver_mgr.GetCurUseFilesVer()
    local split_server_res_download_mgr = require"split_server_res_download_mgr"
    version2 = math.min(version2, remoteFilesVer)
    
    local serverId = curServerId + 1
    local verTaskType = split_define.VerTaskType.PreDownload
    local preDownloadVer = version2 > curUseResVersion and version2 or -1  --预下载版本
    local downloadVersion
    for i = version1, version2 do
        if i ~= curUseResVersion then
            if i == preDownloadVer then
                serverId = curServerId
                verTaskType = split_define.VerTaskType.PreDownload
            else
                serverId = serverId + 1
                verTaskType = split_define.VerTaskType.SwitchServerResDownload
            end
            
            downloadVersion = i
            split_server_res_download_mgr.AddVersionTaskToList(downloadVersion, serverId, verTaskType)
            Warning(0, "AutoAddVersion, version: ", downloadVersion, "serverId:", serverId, "verTaskType:", verTaskType)
            if i ~= preDownloadVer then
                serverId = serverId + 1
                split_server_res_download_mgr.AddVersionTaskToList(downloadVersion, serverId, verTaskType)
                Warning(0, "AutoAddVersion, version: ", downloadVersion, "serverId:", serverId, "verTaskType:", verTaskType)
            end 
        end
    end

    --当前版本下载放最后
    if version1 <= curUseResVersion and curUseResVersion <= version2 then
        downloadVersion = curUseResVersion
    else
        return
    end
    serverId = curServerId
    verTaskType = split_define.VerTaskType.BgDownload
    split_server_res_download_mgr.AddVersionTaskToList(downloadVersion, serverId, verTaskType)
    Warning(0, "AutoAddVersion, version: ", downloadVersion, "serverId:", serverId, "verTaskType:", verTaskType)
end

--增加版本下载任务
function AddVersionTask(version, serverId, taskType)
    local split_server_res_download_mgr = require"split_server_res_download_mgr"
    Warning(0, "AddVersionTask, version: ", version, "serverId:", serverId, "verTaskType:", taskType)
    split_server_res_download_mgr.AddVersionTaskToList(version, serverId, taskType)
end

--切换下载版本
function SwitchVersion(version)
    local split_server_res_download_mgr = require"split_server_res_download_mgr"
    Warning(0, "SwitchDownloadVersion, version: ", version)
    split_server_res_download_mgr.SwitchDownloadVersion(version)
end

function PauseDownload()
    local split_server_res_download_mgr = require"split_server_res_download_mgr"
    Warning(0, "PauseDownload")
    split_server_res_download_mgr.PauseDownload()
end

function ResumeDownload()
    local split_server_res_download_mgr = require"split_server_res_download_mgr"
    Warning(0, "ResumeDownload")
    split_server_res_download_mgr.ResumeDownload()
end 

--增加目标服务器所配版本下载任务
function AddVersionTaskForServer(serverId)
    local serverVersion = SplitServerResVerMgr.GetServerCanUseResVer(serverId)
    local remoteResVersion = split_server_res_ver_mgr.GetRemoteFilesVer()
    if serverVersion > remoteResVersion then
        Warning(0,"serverVersion greater than remoteResVersion, serverVersion:", serverVersion, remoteResVersion)
    end
    local downloadResVer = math.min(remoteResVersion, serverVersion)

    local  streamingLocalFilesVer = split_server_res_ver_mgr.GetStreamingLocalFilesVer()
    if downloadResVer == streamingLocalFilesVer then
        Warning(0,"not need download, equal streamingLocalFilesVer", downloadResVer, curUseResVersion)
        return
    end

    local curUseResVersion = split_server_res_ver_mgr.GetCurUseFilesVer()
    if downloadResVer <= curUseResVersion then  --预下载比当前使用版本低
        Warning(0,"not need download", downloadResVer, curUseResVersion)
        return
    end

    local isExist = split_server_res_ver_mgr.IsExistInRemoteVirtual(downloadResVer)
    if isExist then
        Warning(0,"not need download, is exist in hashRemoteVirVerList", downloadResVer, curUseResVersion)
        return
    end

    Warning(0,"add download to task list", downloadResVer, curUseResVersion)
    split_server_res_ver_mgr.DownloadSwitchServerRes(downloadResVer)
end