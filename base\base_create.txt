--- base_create.lua -------------------------------
--- author:  fgy
--- date: 2024/4/11 17:19
--- desc:ui_base创建得bg 跟effectmask 设置
--------------------------------------------------
local require = require
local newclass = newclass
local ipairs = ipairs
local print = print
local table = table
local util = require "util"
local typeof = typeof
local Component = require "base_comp"
local const				= require "const"
local Time          = CS.UnityEngine.Time
local UI            = CS.UnityEngine.UI
local GameObject    = CS.UnityEngine.GameObject
local Canvas        = CS.UnityEngine.Canvas
local RectTransform = CS.UnityEngine.RectTransform
local UIUtil        = CS.Common_Util.UIUtil


module("base_create")

local Subset = newclass("base_create",Component,true)
local bgPool = nil

function Subset:ctor()
    Subset.__base.ctor(self, "base_create")
end
function Subset:_InitCreate()
    self.effectMaskGeneratedContent = {}
    self.effectMaskCanvas = {}
end

function Subset:_CreateBg(closeBtn, rootTrans,canvasMRoot)
    if closeBtn  and util.IsObjNull(self.bgGameObject) then
        self._startShowTime = Time.realtimeSinceStartup
        self._clickBgTimes = 0

        if not util.IsDisableViewBackground() then
            if bgPool == nil then
                local bgPrefab = GameObject()
                bgPrefab.name = '@@@AutoAddedBg@@@'
                bgPrefab:AddComponent(typeof(RectTransform))
                local image = bgPrefab:AddComponent(typeof(UI.Image))
                image.color = {r = 0, g = 0, b = 0, a = 160/255}
                bgPrefab:AddComponent(typeof(UI.Button))
                if util.IsObjNull(canvasMRoot) then
                    local log = require "log"
                    log.Error("canvasMRoot is null")
                    return
                end
                local prefab_pool = require "prefab_pool"
                bgPool = prefab_pool.CreatePrefabPool(canvasMRoot, bgPrefab, "background_pool")
            end
            self.bgGameObject = bgPool:GetElement(rootTrans)
        else
            local bgPrefab = rootTrans:Find('@@@AutoAddedBg@@@')
            if util.IsObjNull(bgPrefab) then
                bgPrefab = GameObject()
                bgPrefab.name = '@@@AutoAddedBg@@@'
                bgPrefab:AddComponent(typeof(RectTransform))
                local image = bgPrefab:AddComponent(typeof(UI.Image))
                image.color = {r = 0, g = 0, b = 0, a = 160/255}
                bgPrefab:AddComponent(typeof(UI.Button))
                bgPrefab.transform:SetParent(rootTrans)
            else
                bgPrefab = bgPrefab.gameObject
            end

            self.bgGameObject = bgPrefab
        end

        -- 根节点的canvas位置及大小可能不一致，需要重设
        self.bgGameObject.transform.sizeDelta = {x = 5000, y = 5000}
        self.bgGameObject.transform.localPosition = {x = 0, y = 0, z = 0}
        self.bgGameObject.transform.localScale = {x = 1, y = 1, z = 1}
        self.bgGameObject.transform:SetAsFirstSibling()
        self._bgBtn = self.bgGameObject:GetComponent(typeof(UI.Button))

        self._clickBgHandler = function()
            if Time.realtimeSinceStartup - self._startShowTime < const.ClickBgCloseWndProtectTime and self._clickBgTimes < 1 then
                self._clickBgTimes = self._clickBgTimes + 1
                return
            end
            ------  --print("bgpool bg onClick")
            closeBtn.onClick:Invoke()
        end
        self._bgBtn.onClick:AddListener(self._clickBgHandler)
    end
end
---@public 设置背景颜色
----@param colorStr table    颜色值 
---@param alpha number      透明度
function Subset:SetBgColor(colorStr,alpha)
    if not alpha then
        alpha = 1
    end 
    if self._bgBtn and colorStr  then     
        UIUtil.SetColor(self._bgBtn,colorStr,nil,alpha)
    end
end

--特效遮罩功能
function Subset:EffectMaskProcess(transform , param,order)

    --所有关键字
    --effect_mask
    --effect_mask_skip_clear
    --effect_mask_order
    --effect_mask_clear_order

    --开启特效遮罩标记

    --IMG处理
    local imgCom = transform:GetComponent(typeof(UI.Image))
    if util.IsObjNull(imgCom) then
        imgCom = transform.gameObject:AddComponent(typeof(UI.Image))
    end
    imgCom.color = {r=1,g=1,b=1,a=1}

    --Canvas处理
    local canvasCom = transform:GetComponent(typeof(Canvas))
    if util.IsObjNull(canvasCom) then
        canvasCom = transform.gameObject:AddComponent(typeof(Canvas))
    end
    canvasCom.overrideSorting = true
    if param.effect_mask_order then
        canvasCom.sortingOrder =order + param.effect_mask_order
    else
        canvasCom.sortingOrder = order 
    end
	self:AppendEffectMaskCanvas(canvasCom , canvasCom.sortingOrder - order)
    --材质球模板值设置
    local mtr = GameObject.Instantiate(imgCom.material)
    local warmup_shaders = require "warmup_shaders"
    mtr.shader = warmup_shaders.Find("UI/DefaultForUIMask")
    imgCom.material = mtr
    mtr:SetInt("_Stencil" , 1)
    mtr:SetInt("_StencilOp" , 2)

    table.insert( self.effectMaskGeneratedContent , mtr)

    --MaskClear生成
    if not param.effect_mask_skip_clear then
        local stencilClearGobj = GameObject.Instantiate(transform.gameObject , transform)
        --RectTransform 跟随父级变化
        local clearRect = stencilClearGobj:GetComponent(typeof(RectTransform))
        clearRect.anchorMin = {x = 0 , y = 0}
        clearRect.anchorMax = {x = 1 , y = 1}
        clearRect.offsetMax = {x = 0 , y = 0}
        clearRect.offsetMin = {x = 0 , y = 0}

        --Img材质球 stencil调整
        local cleareImg = stencilClearGobj:GetComponent(typeof(UI.Image))
        local clearMtr = GameObject.Instantiate(cleareImg.material)
        cleareImg.material = clearMtr
        clearMtr:SetInt("_Stencil" , 0)
        clearMtr:SetInt("_StencilOp" , 2)

        --Canvas调整
        local clearCanvas = stencilClearGobj:GetComponent(typeof(Canvas))
        if param.effect_mask_clear_order then
            clearCanvas.sortingOrder = order +  param.effect_mask_clear_order
        else
            local clearOrder = order + 3
            if param.effect_mask_order then
                clearOrder = order +  param.effect_mask_order + 3
            end
            clearCanvas.sortingOrder = clearOrder --mask和clear之间夹对应特效的SortingGroup
        end

		self:AppendEffectMaskCanvas(clearCanvas , clearCanvas.sortingOrder - order)

        table.insert( self.effectMaskGeneratedContent , clearMtr)
        table.insert( self.effectMaskGeneratedContent , stencilClearGobj)
    end
end

--由于order有一定几率刷新 ， 增加一套刷新effectMask Canvas的机制
--增加EffectMask的Canvas
function Subset:AppendEffectMaskCanvas(_Canvas , _offset)
	if not _Canvas then
		return
	end
	table.insert( self.effectMaskCanvas , { canvas = _Canvas , offset = _offset} )
end

--EffectMaskCanvas处理
function Subset:EffectMaskCanvasProcess(order)
	if not self.effectMaskCanvas or #self.effectMaskCanvas <= 0 then
		return
	end
	for index, value in ipairs(self.effectMaskCanvas) do
		if value.canvas and not util.IsObjNull(value.canvas) then
			value.canvas.sortingOrder = order + value.offset
		end
	end
end

function Subset:ClearCreateObj()
    if self.effectMaskGeneratedContent then
        for index, value in ipairs(self.effectMaskGeneratedContent) do
            GameObject.Destroy(value)
        end
        self.effectMaskGeneratedContent = {}
    end
    
    if self.effectMaskCanvas then
        self.effectMaskCanvas = {}
    end
end

function Subset:ClearCreateBg()
    if not util.IsDisableViewBackground() then
        if self.bgGameObject and bgPool then
            bgPool:PushElement(self.bgGameObject)
            self.bgGameObject = nil
        end
    else
        self.bgGameObject = nil
    end

    if self._bgBtn then
        -- self._bgBtn.onClick:RemoveListener(self._clickBgHandler)
        self._bgBtn.onClick:RemoveAllListeners()
        self._clickBgHandler = nil
        self._bgBtn = nil
    end
end

function Subset:exportMethods()
    self:exportMethods_({
        "_InitCreate",
        "_CreateBg",
        "SetBgColor",
        "ClearCreateObj",
        "ClearCreateBg",
        "EffectMaskProcess",
        "AppendEffectMaskCanvas",
        "EffectMaskCanvasProcess",
    })
    return self.target_
end
return Subset
