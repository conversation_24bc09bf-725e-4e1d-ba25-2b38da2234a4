-- huawei_ads.txt -------------------------------------------------
-- author:  李婉璐
-- date:    2021.2.25
-- ver:     1.0
-- desc:    华为广告sdk，适用于华为渠道包
--------------------------------------------------------------
local require = require
local os      = os

local log         = require "log"
local event       = require "event"
local ad_config   = require "ad_config"

local HuaWeiAds = CS.HuaWeiAds.Ads

module("huawei_ads")

local AD_UUID = 0
function GetNextUUID()
	AD_UUID = AD_UUID + 1
	return AD_UUID
end

local HuaWeiAd = {}

function HuaWeiAd:ctor(selfType)
	self.adUnitId = nil
	self.rewardedAd = nil
	self.index = 0  --第几个广告位
	self.startTime = 0
	self.endTime = 0
	self.uuid = GetNextUUID()
	self.isVideoFinished = false -- 观看完视频
end

function HuaWeiAd:RequestAndLoadRewardedAd(adUnitId, index)
	log.Warning("huaweiAd, RequestAndLoadRewardedAd:", index, ",adUnitId:", adUnitId)
	event.Trigger(event.GAME_EVENT_REPORT, "ads_start_load", {iType = index})
	self.index = index
    self.adUnitId = adUnitId

	self.rewardedAd = HuaWeiAds.Instance:CreateRewardedAd(adUnitId,function(value, tag1, msg, adid)
        self:AdCallBack(value, tag1, 0, msg, ad_config.GetHuaWeiRewardIndex(adid) or 1)
    end)

	HuaWeiAds.Instance:LoadRewardedAd(self.adUnitId)
	self.startTime = os.time()
end

function HuaWeiAd:ShowRewardedAd()
	if self.rewardedAd then
		HuaWeiAds.Instance:ShowRewardedAd(self.adUnitId)
    else
        log.Error("Rewarded ad is not ready yet.")
	end
end

function HuaWeiAd:IsLoaded()
	return self.rewardedAd and HuaWeiAds.Instance:IsLoaded(self.adUnitId)
end

-- 广告加载回调
function HuaWeiAd:AdCallBack(value, tag1, tag2, msg, index)
	if tag1 == 1 then
		-- 广告加载完成
		log.Warning("HuaWeiAd, OnAdLoaded:", index)
		self.endTime = os.time()
		local properties = {iType = self.index, loadTime = self.endTime - self.startTime}
		event.Trigger(event.GAME_EVENT_REPORT, "ads_loaded", properties)
	end
	if tag1 == 2 then
		-- 广告加载失败
		log.Error("HuaWeiAd, OnAdFailedToLoad:", index, msg)
		self.endTime = os.time()
		local properties = {iType = self.index, loadTime = self.endTime - self.startTime, errorMsg = msg}
		event.Trigger(event.GAME_EVENT_REPORT, "ads_loaded_failed", properties)
	end
	if tag1 == 3 then
		-- 广告开始展示
		log.Warning("HuaWeiAd, OnAdOpening:", index)
	end
	if tag1 == 4 then
		-- 广告展示失败
		log.Error("HuaWeiAd, OnAdFailedToShow:", index, msg)
	end
	if tag1 == 5 then
		-- 广告关闭时
		log.Warning("HuaWeiAd, OnAdClosed:", index)
		event.Trigger(event.EVENT_REWAEDEDAD_CLOSED, index)
	end
	if tag1 == 6 then
		-- 在用户因观看视频而应获得奖励时
		self.isVideoFinished = true
		log.Warning("HuaWeiAd, OnAdEarnedReward:", index)
	end
	
	event.Trigger(event.EVENT_REWARDEDAD_CALLBACK, tag1, index, msg)
end

function HuaWeiAd:Dispose()
	self.adUnitId = nil
	self.rewardedAd = nil
	self.index = 0
	self.startTime = 0
	self.endTime = 0
	self.isVideoFinished = false
end

local class = require "class"
local object = require "object"

return class(object, nil, HuaWeiAd)