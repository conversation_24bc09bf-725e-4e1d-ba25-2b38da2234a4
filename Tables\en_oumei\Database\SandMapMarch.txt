return {
['fieldNames']={['marchID']=1,['remark']=2,['type']=3,['attack']=4,['group']=5,['MehrereTeams']=6,['advance']=7,['back']=8,['work']=9,['speedtype']=10,['joinspeed']=11,['groupspeed']=12,['advancespeed']=13,['backspeed']=14,['speedandtime']=15,['leavemodel']=16,['teamTechnology']=17,['fightshow']=18,['home']=19,['groupicon']=20,['advanceicon']=21,['doingicon']=22,['backicon']=23,['shareShow']=24,['SeparateFight']=25,['DeadTransferAtt']=26,['DeadTransferDef']=27,['PVPDeadTransferAtt']=28,['PVPDeadTransferDef']=29,['ModelResource']=30,['StopDistance']=31,['mailTempletID']=32,['accelerate']=33,['changedirection']=34,['Gatheringinvitation']=35,['BeAtBase']=36},
['addInfos']={{1,42,1,0,2,0,19,7,7,1,0,0,1,6,2,0,0,1,0,3,14,35,9,5,0,11,28,11,28,12,0,36,5},{5,43,1,0,2,0,44,10,10,1,0,0,13,13,2,0,0,1,0,3,14,3,9,5,0,2,2,2,2,45,0,46},{13,47,1,0,2,0,19,7,7,1,0,0,6,6,2,0,0,1,0,3,14,37,9,5,0,2,2,2,2,12,0,2,23},{15,48,1,1,2,0,19,7,10,1,0,0,6,6,2,0,0,1,0,3,4,4,9,5,0,11,8,11,8,12,1,24,13},{23,49,1,1,2,0,19,7,10,1,0,0,6,6,2,0,0,1,0,3,4,4,9,5,0,21,16,2,2,12,1,38,1,1},{50,51,1,1,2,0,20,17,10,5,0,0,29,29,2,0,0,1,1,3,4,4,9,5,0,39,25,2,2,12,1,40,1,1},{52,53,1,1,2,0,19,7,10,1,0,0,6,6,2,0,0,1,1,3,4,4,9,5,0,41,16,11,8,12,1,22,15},{54,55,1,1,2,0,19,7,10,1,0,0,33,30,2,0,0,1,1,3,4,4,9,5,0,11,8,11,8,12,0,22,15},{56,57,1,1,2,0,19,7,10,1,0,0,6,6,2,0,0,1,0,3,4,4,9,5,0,25,25,25,25,12,1,24,13},{58,59,1,1,2,0,26,17,17,1,0,0,6,6,2,0,0,1,0,3,4,4,9,5,0,8,8,8,8,12,1,24,13},{60,61,1,1,2,0,26,17,17,1,0,0,6,6,2,0,0,1,0,3,4,4,9,5,0,8,8,8,8,12,1,24,15},{62,63,5,1,2,0,26,17,17,1,6,6,0,6,2,0,0,1,0,14,4,4,9,5,0,8,8,8,8,18,1,24,15},{64,65,5,1,2,0,26,17,17,1,13,6,0,1,2,0,0,1,0,14,4,4,9,5,0,11,8,11,8,18,1,24},{66,67,1,0,2,0,26,17,17,1,0,0,1,6,2,0,0,0,0,3,14,35,9,5,0,11,28,11,28,12,0,36,5},{68,69,5,1,7,0,20,7,10,1,13,6,0,1,2,1,0,1,1,14,4,4,9,5,0,11,8,11,8,18,1,24,0,1},{70,71,5,1,7,0,20,7,10,1,15,15,0,1,2,1,1,1,1,14,4,4,9,5,0,21,16,2,2,18,1,34,0,1},{72,73,5,1,7,0,20,7,10,1,15,15,0,5,2,1,0,1,1,14,4,4,9,1,0,21,16,2,2,18,1,34,0,1,1},{74,75,5,1,7,0,20,7,10,1,6,6,0,1,2,5,0,1,1,14,4,4,9,5,0,76,25,2,2,18,1,77},{78,79,5,1,7,0,20,7,10,1,13,6,0,6,2,1,1,1,1,14,4,4,9,5,0,41,16,11,8,18,1,22},{80,81,5,1,7,0,20,7,10,1,13,33,0,30,2,1,1,1,1,14,4,4,9,5,0,11,8,11,8,18,0,22},{82,83,1,1,2,0,19,7,10,1,0,0,6,6,2,0,0,1,1,3,4,4,9,5,0,11,8,11,8,12,0,22,15},{84,85,5,1,7,0,20,7,10,1,13,6,0,1,2,1,1,1,1,14,4,4,9,5,0,11,8,11,8,18,0,22},{86,87,1,1,2,0,19,7,10,1,0,0,6,6,2,0,0,1,1,3,4,4,9,5,0,11,8,11,8,12,0,22,15},{88,89,5,1,7,0,20,7,10,1,13,6,0,1,2,1,1,1,1,14,4,4,9,5,0,11,8,11,8,18,0,22},{90,91,1,1,2,0,19,7,10,1,0,0,6,6,2,0,0,1,0,3,4,4,9,5,0,21,16,2,2,12,1,38,1,1},{92,93,5,1,7,0,20,7,10,1,15,15,0,1,2,1,1,1,1,14,4,4,9,5,0,21,16,2,2,18,1,34,0,1,1},{94,95,1,0,2,0,19,7,7,1,0,0,15,15,2,0,0,1,0,3,14,37,9,5,0,2,2,2,2,12,0,2,23},{96,97,1,1,2,0,20,17,10,5,0,0,29,29,2,0,0,1,1,3,4,4,9,5,0,39,25,2,2,12,1,40,1,1},{98,99,13,1,2,0,2,2,10,1,0,0,33,0,2,0,0,1,0,3,3,3,3,0,0,16,21,2,2,100,0,27},{101,102,1,0,2,0,2,2,2,1,0,0,23,0,2,0,0,0,0,3,3,3,3,0,0,2,2,2,2,31,0,2},{103,104,1,0,2,0,2,2,2,1,0,0,23,0,2,0,0,0,0,3,3,3,3,0,0,2,2,2,2,31,0,2},{105,106,1,0,2,0,2,2,2,1,0,0,23,0,2,0,0,0,0,3,3,3,3,0,0,2,2,2,2,31,0,2},{107,108,1,0,2,0,2,2,2,1,0,0,23,0,2,0,0,0,0,3,3,3,3,0,0,2,2,2,2,31,0,2},{12,109,1,0,2,0,2,2,2,1,0,0,30,1,2,0,0,0,0,3,3,3,3,0,0,2,2,2,2,110,0,2},{111,112,1,0,2,0,2,2,2,1,0,0,30,1,2,0,0,0,0,3,3,3,3,0,0,2,2,2,2,113,0,2},{18,114,1,1,2,0,2,2,10,13,0,0,0,0,32,0,0,1,0,3,3,3,3,0,0,16,21,2,2,115,0,27},{116,117,1,1,2,0,2,2,10,13,0,0,0,0,32,0,0,1,0,3,3,3,3,0,0,16,21,2,2,118,0,27},{119,120,1,1,2,0,2,2,10,13,0,0,0,0,32,0,0,1,0,3,3,3,3,0,0,16,21,2,2,121,0,27},{122,123,1,1,2,0,2,2,10,13,0,0,0,0,32,0,0,1,0,3,3,3,3,0,0,16,21,2,2,124,0,27}},
['num']=39,['vEnums']={0,1,{},'','chengJian_icon_zhanDou',2,0.5,{1,1},{0,6000,4000},'chengJiu_icon_fanhui',{0,0},{2000,6000,2000},201,3,'chengJian_icon_xingJun',4,{10000,0,0},{1,0},301,{1,1,1},{1,0,0},{1000,500,8500},{100012,100020,100015,100014},5,{100004,100013,100015,100014},{0,0,0},{1,0,1},{0,0,100018,100003},{500,6000,3500},15,0.3,10007,{15000,15},0.4,{100003,100018,0,0},'chengJian_icon_caiJi',{100008},'chengJiu_icon_zengyuan',{100001,100016,0,0},{2500,2500,5000},{100007,0,0,0},{2000,0,8000},[[采集]],[[侦查]],{0,1,1},401,{100005,100006,90001,90002,90003,90004},[[增援]],[[单人打玩家]],[[单人打小怪]],6,[[单人打世界BOSS]],7,[[单人打中立城]],8,[[单人打国会]],9,[[贸易马车打玩家（已废弃）]],10,[[沙漠风暴-单人打玩家]],11,[[沙漠风暴-单人打建筑]],12,[[沙漠风暴-集结打建筑]],13,[[沙漠风暴-集结打玩家]],14,[[沙漠风暴-前往采集]],21,[[集结攻打玩家]],22,[[集结打末日精英]],23,[[集结攻打游荡怪]],24,[[集结打联盟BOSS]],{500,500,9000},{100002,100017,0,0},25,[[集结攻打中立城池]],26,[[集结攻打国会]],27,[[战区对决-单人打国会]],28,[[战区对决-集结攻打国会]],29,[[战区对决-单人打巨炮]],30,[[战区对决-集结攻打巨炮]],31,[[单人打亡灵宝藏]],32,[[集结打亡灵首领]],33,[[单人前往攻城营寨]],34,[[单人打狂暴首领]],100,[[游荡怪行军（玩家无法操作）]],2105,101,[[雷达通用特殊行军]],103,[[雷达环境勘探行军]],104,[[雷达帮助盟友行军]],105,[[橡果酒馆行军]],[[贸易马车行军]],30001,202,[[飞船行军]],30006,[[【丧尸灾变】小怪1]],80101,302,[[【丧尸灾变】剧毒丧尸]],80201,303,[[【丧尸灾变】丧尸卡车]],80301,304,[[【丧尸灾变】变异卡车]],80401},
['keyFuncs']={{1,2,3,4,5,6,7,8,9,10,11,12,13,14,[21]=15,[22]=16,[23]=17,[24]=18,[25]=19,[26]=20,[27]=21,[28]=22,[29]=23,[30]=24,[31]=25,[32]=26,[33]=27,[34]=28,[100]=29,[101]=30,[103]=31,[104]=32,[105]=33,[201]=34,[202]=35,[301]=36,[302]=37,[303]=38,[304]=39}},
['md5']='8a4d8f3a4ae4ae26789f076a20f092fd',
['keys']={{1}}}