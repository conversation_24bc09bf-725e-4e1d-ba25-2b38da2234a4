---
--- Created by: yuannan.
--- DateTime: 2024/10/14.
--- Desc: 预览管理
---

local require = require
local pairs = pairs
local math = math

local gw_sand_flow_const = require "gw_sand_flow_const"
local sand_effect_fly_flow = require "sand_effect_fly_flow"
local sand_get_score_flow = require "sand_get_score_flow"
local sand_kill_soldier_flow = require "sand_kill_soldier_flow"
local sand_get_box_flow = require "sand_get_box_flow"
local gw_sand_data = require "gw_sand_data"
local gw_admin = require "gw_admin"
local gw_comp_name = require "gw_comp_name"
local gw_sand_preview_helper = require "gw_sand_preview_helper"
local sand_preview_line = require "sand_preview_line"
local sand_click_like_flow = require "sand_click_like_flow"

---@class GWSandPreviewMgr  特效管理
local GWSandPreviewMgr = {}
local mc = {}

--- 构造器
function GWSandPreviewMgr.InitSandScene()
    mc.enterSandScene = true
    mc.uniqueId = 0
    mc.teamPreviewList = {}
    mc.previewObjectList = {}
    mc.previewHelper = gw_sand_preview_helper.new()
    mc.previewHelper:Init()
end

--- 棄用
function GWSandPreviewMgr.Dispose()
    if mc.previewHelper then
        mc.previewHelper:Dispose()
    end
    if mc.previewObjectList then
        for k, v in pairs(mc.previewObjectList) do
            if v then
                v:Dispose()
            end
        end
    end
    mc = {}
end

---@public 创建预览线
---@param targetSid string 目标对象的Sid
---@param parent table 特效的基础属性参数
---@param callback table 特效的配置参数
function GWSandPreviewMgr.CreatePreviewLineBySid(targetSid, parent, callback,color)
    if not mc.enterSandScene then
        return
    end

    local entity = gw_admin.MapUtil.GetCompBySid(targetSid)
    if not entity then
        return
    end

    local startPosition = gw_sand_data.selfData.GetSelfVisualSandBoxPosition()
    local endPosition = entity.serData.pos
    return GWSandPreviewMgr.CreatePreviewLine(startPosition, endPosition, parent, callback,color)
end

---@public 创建预览线
---@param startPosition 起始位置(x,y)
---@param endPosition 结束位置(x,y)
---@param parent table 特效的基础属性参数
---@param callback table 特效的配置参数
function GWSandPreviewMgr.CreatePreviewLine(startPosition, endPosition, parent, callback,color)
    if not mc.enterSandScene then
        return
    end

    if not startPosition or not endPosition then
        return
    end

    local gw_sand_node = require "gw_sand_node"
    parent = parent or gw_sand_node.top3Node()

    local id = GWSandPreviewMgr.CreateUniqueID()
    local data = {
        resPath = "art/greatworld/sand/scenenew/items/march/gwsandpreviewline.prefab",
        startPosition = startPosition,
        endPosition = endPosition,
        parent = parent,
        color = color,
    }

    local objectPreview = sand_preview_line.CM():Init(id, data, callback)
    mc.previewObjectList[id] = objectPreview
    return id
end

---@public 沙盘怪物被攻击的预览行为
---@param monsterSid 沙盘对象的Sid
function GWSandPreviewMgr.CreateMonsterAttackPreview(monsterSid)
    if not mc.enterSandScene then
        return
    end

    if not monsterSid then
        return
    end
    local entity = gw_admin.MapUtil.GetCompBySid(monsterSid)
    if not entity then
        return
    end
    local startPosition = gw_sand_data.selfData.GetSelfVisualSandBoxPosition()
    local direction = gw_admin.VectorUtility.Vec2Sub(startPosition, entity.serData.pos)
    -- todo 处理沙盘怪物的预览行为
end

---@public 全军出击头像预览行为
function GWSandPreviewMgr.CreateNCOfflineAttackPreview(targetSid, faceList)
    if not mc.enterSandScene then
        return
    end

    if not targetSid then
        return
    end

    local gw_sand_hud_node = require "gw_sand_hud_node"
    local entity = gw_admin.MapUtil.GetCompBySid(targetSid)
    if not entity then
        return
    end

    if not entity:GetComponent(gw_comp_name.comp_offline_attack_hud) then
        local hudData = { parent = gw_sand_hud_node.UITopNode(), targetPos = { x = entity.serData.pos.x, y = 0, z = entity.serData.pos.y }, minLod = 5, maxLod = 6 }
        local serData = entity.serData
        local curId, curComp = gw_admin.HudUtil.InitEntityHudComponent(gw_comp_name.comp_offline_attack_hud, hudData, serData)
        entity:AddComponent(gw_comp_name.comp_offline_attack_hud, curComp)
    end
    local offline_attack_comp = entity:GetComponent(gw_comp_name.comp_offline_attack_hud)
    offline_attack_comp:RefreshFaceList(faceList)
end

---@public 移除头像预览行为
function GWSandPreviewMgr.RemoveNCOfflineAttackPreview(targetSid)
    if not mc.enterSandScene then
        return
    end

    if not targetSid then
        return
    end

    local entity = gw_admin.MapUtil.GetCompBySid(targetSid)
    entity:RemoveComponent(gw_comp_name.comp_offline_attack_hud)
end

---@public 创建创建沙盘气泡弹窗
function GWSandPreviewMgr.CreateSandFlowPop(flowEnum, pos, popInfo, parent)
    if not mc.enterSandScene or not flowEnum then
        return
    end

    local flowDefine = gw_sand_flow_const.GetFlowDefine(flowEnum)
    if not flowDefine then
        return
    end

    local id = GWSandPreviewMgr.CreateUniqueID()
    local data = {
        resPath = flowDefine.resPath,
        showTime = flowDefine.showTime,
        pos = pos,
        parent = parent,
        popInfo = popInfo,
    }

    local flowModule = require(flowDefine.flowModule)
    local objectPreview = flowModule.CM():Init(id, data)
    mc.previewObjectList[id] = objectPreview
    return id
end

---@public 创建沙盘飞行特效
function GWSandPreviewMgr.CreateSandFlyEffect(effectName, startPos, targetPos, flyTime, parent)
    if not mc.enterSandScene or not effectName or not startPos or not targetPos then
        return
    end

    flyTime = flyTime or 1
    local id = GWSandPreviewMgr.CreateUniqueID()
    local data = {
        resPath = "art/greatworld/sand/scenenew/hud/gwsandeffectflyhud.prefab",
        showTime = flyTime + 0.5,
        effectName = effectName,
        startPos = startPos,
        targetPos = targetPos,
        flyTime = flyTime,
        parent = parent,
    }
    local objectPreview = sand_effect_fly_flow.CM():Init(id, data)
    mc.previewObjectList[id] = objectPreview
end


function GWSandPreviewMgr.GetPreviewObject(id)
    if not mc.enterSandScene then
        return
    end
    return mc.previewObjectList and mc.previewObjectList[id]
end

function GWSandPreviewMgr.RemovePreviewObject(id)
    if not mc.enterSandScene then
        return
    end

    if mc.previewObjectList and mc.previewObjectList[id] then
        mc.previewObjectList[id]:Dispose()
        mc.previewObjectList[id] = nil
    end
end

function GWSandPreviewMgr.CallEffectFunc(id, funcName, ...)
    if not mc.enterSandScene then
        return
    end

    if mc.previewObjectList and mc.previewObjectList[id] then
        mc.previewObjectList[id].CallFuncName(mc.previewObjectList[id], funcName, ...)
    else
        gw_admin.SwitchUtility.Error("CallEffectFunc id error:", id, funcName)
    end
end

--生成唯一ID
function GWSandPreviewMgr.CreateUniqueID()
    if not mc.uniqueId then
        mc.uniqueId = 1
    else
        mc.uniqueId = mc.uniqueId + 1
    end
    return mc.uniqueId
end

return GWSandPreviewMgr