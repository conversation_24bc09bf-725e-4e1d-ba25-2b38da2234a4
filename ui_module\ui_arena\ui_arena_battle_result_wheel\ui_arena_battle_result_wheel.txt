local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local typeof = typeof
local type = type
local UIUtil = CS.Common_Util.UIUtil

local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
local Common_Util = CS.Common_Util.UIUtil
local ui_util = require "ui_util"
local face_item_new = require "face_item_new"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_arena_battle_result_wheel_binding"

--region View Life
module("ui_arena_battle_result_wheel")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}

    self.team_hero_l = {}
    self.team_hero_r = {}

    self:SetWinUIActive()
    self:SetDefaultUIActive()
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    if self.faceIteml then
        self.faceIteml:Dispose()
        self.faceIteml = nil
    end
    if self.faceItemr then
        self.faceItemr:Dispose()
        self.faceItemr = nil
    end
    self.teamLeftItem = nil
    self.teamRightItem = nil
    self.attack_team = nil
    self.defend_team = nil
    self.team_hero_l = nil
    self.team_hero_r = nil
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:SetWinUIActive()
    local isZH = lang.USE_LANG == lang.ZH

    self:SetActive(self.rtf_winText, isZH)
    self:SetActive(self.rtf_winLeftBG, isZH)
    self:SetActive(self.rtf_winRightBG, isZH)
    self:SetActive(self.rtf_victoryEffect, isZH)
    self:SetActive(self.rtf_victoryEffect1, isZH)
    self:SetActive(self.rtf_winTextEn, not isZH)
    self:ChangeLangRes("victory_shengli",self.img_winTextEn)
end

function UIView:SetDefaultUIActive()
    local isZH = lang.USE_LANG == lang.ZH
    self:SetActive(self.rtf_loseText, isZH)
    self:SetActive(self.rtf_loseLeftBG, isZH)
    self:SetActive(self.rtf_loseRightBG, isZH)
    self:SetActive(self.rtf_defeatEffect, isZH)
    self:SetActive(self.rtf_loseEnText, not isZH)
    self:ChangeLangRes("lose_shibai",self.img_loseEnText)
end

function UIView:ChangeLangRes(ImgPath,Img)
    local oversea_res = require "oversea_res"
    local res = ImgPath
    local callBack = function(asset)
        if self:IsValid() then
            if asset then
                --加载多语言图片成功后的回调
                Img.sprite = asset
            end
        end
    end
    return oversea_res.LoadSprite(res,"ui_arena_battle_result_wheel",callBack)
end

---初始化队伍的预设Obj信息
function UIView:InitTeamObjInfo(teamNum)
    if not self:IsValid() then
        return
    end
    self.teamLeftItem = {}
    self.teamLeftItem_Btn = {}
    self.teamRightItem = {}
    self.maxTeamNum = teamNum
    for i = 1, self.maxTeamNum do
        local obj1 = self.rtf_teaminfoLeft.transform:GetChild(i-1)
        local obj2 = self.rtf_teaminfoRight.transform:GetChild(i-1)
        if(obj1 and obj2)then
            local item1 = Common_Util.GetComponent(obj1, typeof(ScrollRectItem), "")
            local item2 = Common_Util.GetComponent(obj2, typeof(ScrollRectItem), "")
            local btn = Common_Util.GetComponent(obj1, typeof(Button), "")

            table.insert(self.teamLeftItem,item1)
            table.insert(self.teamLeftItem_Btn,btn)
            table.insert(self.teamRightItem,item2)
        else
            local log = require "log"
            log.Error("获取到RectTransform失败,队伍界面应该与配置一致:",i)
        end
    end
end

function UIView:ShowPlayerInfo()
    local player_mgr = require "player_mgr"
    local arena_battle_mgr = require "arena_battle_mgr"
    self.faceIteml = self.faceIteml or face_item_new.CFaceItem():Init(self.rtf_rectFace, nil, 1.24)
    --适配faceID 2025.4.2 将faceID 转换为 faceStr
    local custom_avatar_data = require "custom_avatar_data"
    local customHeadData = custom_avatar_data.GetMyAvatar()
    local faceStr = arena_battle_mgr.GetMyFaceStr(player_mgr.GetRoleFaceID())
    if customHeadData then
        faceStr = customHeadData.remoteUrl
    end
    self.faceIteml:SetFaceInfo(faceStr,nil)
    self.faceIteml:SetFrameID(player_mgr.GetPlayerFrameID(),true)
    local alliance_mgr = require "alliance_mgr"
    local worldStr = ui_util.GetWorldIDToShowWorldID(player_mgr.GetCreateRoleWorldID(),nil,ui_util.WorldIDRangeType.Normal)
    self.txt_name.text = string.format("#%s %s",worldStr, util.SplicingUnionShortName(alliance_mgr.GetUserAllianceShortName(),player_mgr.GetRoleName()))
    self:ShowEnemyPlayerInfo()
end

function UIView:ShowEnemyPlayerInfo()
    if(self.enemyInfo)then
        ---设置玩家头像
        self.faceItemr = self.faceItemr or face_item_new.CFaceItem():Init(self.rtf_rectFacer, nil, 1.24)
        local faceStr = self.entity:GetFaceId()
        if self.entity:GetFaceStr() and string.IsNullOrEmpty(self.entity:GetFaceStr()) then
            faceStr = string.IsNullOrEmpty(self.entity:GetFaceStr()) and self.entity:GetFaceId() or self.entity:GetFaceStr()
        end
        self.faceItemr:SetFaceInfo(self.enemyInfo.faceStr)
        self.faceItemr:SetFrameID(self.enemyInfo.frameID, true)
        self.txt_namer.text = string.format("#%s %s",self.enemyInfo.worldStr,util.SplicingUnionShortName(self.enemyInfo.unionShortName, self.enemyInfo.name))
    end
end

function UIView:OnRefreshBattleResultView(data)
    local msg = data.msg
    self.attack_team = data.attack_team
    self.defend_team = data.defend_team
    self.data.enemyInfo = data.enemyInfo
    self:ShowPlayerInfo()
    self:SetActive(self.rtf_Win, msg.bWinFlag)
    self:SetActive(self.rtf_Lose, not msg.bWinFlag)
    self:LoadResultEffect(msg.bWinFlag)
    self:OnRefreshLineupData(msg.arrBattleInfo,msg.bWinFlag)
    self:OnShowHpBarInfo(msg.bWinFlag)
end

--[[加载结算特效]]
function UIView:LoadResultEffect(result)
    local modelPath = ""
    if result then
        modelPath = "art/effects/prefabs/ui/ui_zhandoushenglixin.prefab"
        if lang.USE_LANG ~= lang.ZH then
            modelPath = "art/effects/prefabs/ui/ui_zhandoushenglixin_en.prefab"
        end
    else
        modelPath = "art/effects/prefabs/ui/ui_zhandoushibaixin.prefab"
        if lang.USE_LANG ~= lang.ZH then
            modelPath = "art/effects/prefabs/ui/ui_zhandoushibaixin_en.prefab"
        end
    end
    if self.effectModelViewer then
        self.effectModelViewer:Dispose()
        self.effectModelViewer = nil
    end

    if self.effectModelViewer == nil then
        self.effectModelViewer = CModelViewer()
        self.effectModelViewer:Init(self.rtf_effect, function()
            self.effectModelViewer:ShowGameObject(modelPath, function(goEffect)
                local sortingGroup = goEffect:GetComponent(typeof(SortingGroup))
                if not sortingGroup or util.IsObjNull(sortingGroup) then
                    sortingGroup = goEffect:AddComponent(typeof(SortingGroup))
                end
                sortingGroup.sortingOrder = self.curOrder + 1
            end)
        end)
    end
end

---整理进攻阵容和防守阵容的数据
function UIView:OnRefreshLineupData(attackResultList,iswin)
    if(not self.teamLeftItem or not self.teamRightItem)then
        self:InitTeamObjInfo()
    end
    ---攻击方的最大连胜次数
    self.maxWinTimes_atk = 0
    ---防守方的最大连胜次数
    self.maxWinTimes_def = 0
    ---胜利时攻击方的最后对战的队伍下标(最后一场对局)
    self.curWinTeamIndex_atk = 0
    ---失败时防守方的最后对战的队伍下标(最后一场对局)
    self.curWinTeamIndex_def = 0
    for k,v in ipairs(attackResultList) do
        if v.defendWinTimes > self.maxWinTimes_def then
            self.maxWinTimes_def = v.defendWinTimes
        end
        if v.attackWinTimes > self.maxWinTimes_atk then
            self.maxWinTimes_atk = v.attackWinTimes
        end
        self.curWinTeamIndex_atk = v.attackTeamIndex
        self.curWinTeamIndex_def = v.defendTeamIndex
    end
    self:OnRefreshAttackLineUp(iswin)
    self:OnRefreshDefenseLineUp(iswin)
end
---显示条件：连续击败2队或2队以上是，显示该标签
local minShowWinTimes = 2
function UIView:OnRefreshAttackLineUp(iswin)
    ---获取当前的进攻阵容
    local arena_battle_mgr = require "arena_battle_mgr"
    local totalPower = 0
    for i = 1, #self.teamLeftItem do
        local attackItem = self.teamLeftItem[i]
        local isgray = false
        if(iswin)then
            isgray = false
            if(i < self.curWinTeamIndex_atk)then
                isgray = true
            end
        else
            isgray = true
        end
        ---金色得胜利方得当前阵容
        local isShowGold = nil
        if(iswin)then
            isShowGold = i == self.curWinTeamIndex_atk
        end
        if(self.team_hero_l and self.team_hero_l[i])then
            local heroList = arena_battle_mgr.SetTeamHeroItemInfo(attackItem,i, self.attack_team[i],self.team_hero_l[i],isShowGold,0.42,isgray )
            self.team_hero_l[i] = heroList
        else
            local heroList = arena_battle_mgr.SetTeamHeroItemInfo(attackItem,i,self.attack_team[i],nil,isShowGold,0.42,isgray)
            table.insert(self.team_hero_l,heroList)
        end
        local p = self.attack_team[i].power or 0
        totalPower = totalPower + p
    end
    self.txt_power.text = util.PriceConvert(totalPower or 0)
    self.rtf_wintimebg_l.gameObject:SetActive(self.maxWinTimes_atk >= minShowWinTimes)
    if(self.maxWinTimes_atk >= minShowWinTimes)then
        self.txt_winTimes.text = "x"..self.maxWinTimes_atk
    end
end

function UIView:OnRefreshDefenseLineUp(iswin)
    local totalPower = 0
    local arena_battle_mgr = require "arena_battle_mgr"
    for i = 1, #self.teamRightItem do
        local enemyItem = self.teamRightItem[i]

        local isgray = false
        if(iswin)then
            isgray = true

        else
            if(i < self.curWinTeamIndex_def)then
                isgray = true
            end
            isgray = false
        end
        ---金色得胜利方得当前阵容
        local isShowGold = nil
        if(not iswin)then
            isShowGold = i == self.curWinTeamIndex_def
        end
        if(self.team_hero_r and self.team_hero_r[i])then
            local heroList = arena_battle_mgr.SetTeamHeroItemInfo(enemyItem,i,self.defend_team[i],self.team_hero_r[i],isShowGold,0.42,iswin)
            self.team_hero_r[i] = heroList
        else
            local heroList = arena_battle_mgr.SetTeamHeroItemInfo(enemyItem,i,self.defend_team[i],nil,isShowGold,0.42,iswin)
            table.insert(self.team_hero_r,heroList)
        end
        local p = self.defend_team[i].power or 0
        totalPower = totalPower + p
    end
    self.txt_powerr.text = util.PriceConvert(totalPower or 0)
    self.rtf_wintimebg_r.gameObject:SetActive(self.maxWinTimes_def >= minShowWinTimes)
    if(self.maxWinTimes_def >= minShowWinTimes)then
        self.txt_winTimesr.text = "x"..self.maxWinTimes_def
    end
end

function UIView:OnShowHpBarInfo(iswin)
    for i = 1, self.maxTeamNum do
        local obj1 = self.rtf_hpl.transform:GetChild(i-1)
        obj1.gameObject:SetActive(iswin and i >= self.curWinTeamIndex_atk)
    end
    for i = 1, self.maxTeamNum do
        local obj1 = self.rtf_hprr.transform:GetChild(i-1)
        obj1.gameObject:SetActive((not iswin) and (i >= self.curWinTeamIndex_def))
    end
end




--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
