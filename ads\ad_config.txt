local require = require
local game_config = require "game_config"
local version_mgr = require "version_mgr"
local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
module("ad_config")

----------------------------- 谷歌移动广告sdk--------------------------------
-- 安卓测试版本
local google_android_test_adid = "ca-app-pub-3940256099942544/5224354917"
-- iOS测试版本
local google_ios_test_adid = "ca-app-pub-3940256099942544/1712485313"

-- 安卓正式版本
local ids_Android = {
	[1] = "ca-app-pub-2892001563789649/8839538117", 
	[2] = "ca-app-pub-2892001563789649/6213374771", 
	[3]= "ca-app-pub-2892001563789649/9961048096", 
	[4] = "ca-app-pub-2892001563789649/3615560190",
	[5] = "ca-app-pub-2892001563789649/3905219671",
	[6] = "ca-app-pub-2892001563789649/4044820477"
}

-- iOS正式版本
local ids_iOS = {
	[1] = "ca-app-pub-2892001563789649/2082558079", 
	[2] = "ca-app-pub-2892001563789649/3800137880", 
	[3] = "ca-app-pub-2892001563789649/5886426936", 
	[4] = "ca-app-pub-2892001563789649/7623087967",
	[5] = "ca-app-pub-2892001563789649/8182186667",
	[6] = "ca-app-pub-2892001563789649/1616778314"
}

----------------------------- 华为移动广告sdk--------------------------------
local huawei_test_adid = "testx9dtjwj8hp"
local ids_huawei = {
	[1] = "i2099szxbu", 
	[2] = "o1rav83ss3", 
	[3]= "r8ov0pr1xb", 
	[4] = "y4m3e4dnec",
	[5] = "q0z7ce9wuv",
	[6] = "m1kqbunhv3"
}
local ids_huawei_index = {
    ["i2099szxbu"] = 1, 
    ["o1rav83ss3"] = 2, 
    ["r8ov0pr1xb"]= 3, 
    ["y4m3e4dnec"] = 4,
    ["q0z7ce9wuv"] = 5,
    ["m1kqbunhv3"] = 6
}

-- 内网版本用测试广告ID
function GetTestAdid()
    local testAdid = nil
    if game_config.ENABLE_Q1_DEBUG_MODE  == true then
        if game_config.ENABLE_ADMOB then
            if Application.platform == RuntimePlatform.IPhonePlayer then
                testAdid = google_ios_test_adid
            else
                testAdid = google_android_test_adid
            end
        elseif game_config.ENABLE_HUAWEI then
            -- 如果是华为渠道 华为广告
            testAdid = huawei_test_adid
        end
		
	end
    return testAdid
end

-- 获取正式版本的广告位ID列表
function GetRewardedAdids()
    if game_config.ENABLE_ADMOB then
        return Application.platform == RuntimePlatform.IPhonePlayer and ids_iOS or ids_Android
    elseif game_config.ENABLE_HUAWEI then
        -- 如果是华为渠道 华为广告
        return ids_huawei
    end
    return {}
end

-- 判断当前版本，当前包是否有广告
function IsEnableAd()
    if game_config.ENABLE_ADMOB and version_mgr.CheckSvnTrunkVersion(42927) then
        return true
    end
    if game_config.ENABLE_HUAWEI and version_mgr.CheckSvnTrunkVersion(53338) then
        return true
    end
    return false
end

-- 根据广告ID取对应的index
function GetHuaWeiRewardIndex(adid)
    return ids_huawei_index[adid]
end