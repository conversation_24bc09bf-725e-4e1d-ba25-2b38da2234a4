
local require = require
local typeof = typeof
local table = table
local print = print
local dump = dump
local pairs = pairs
local math = math
local type = type
local string = string
local ipairs = ipairs
local class = require "class"
local ui_base = require "ui_base"

local player_mgr = require "player_mgr"
local event = require "event"
local item_data = require "item_data"
local game_scheme = require "game_scheme"
local util = require "util"
local goods_item = require "goods_item_new"
local windowMgr = require "ui_window_mgr"
local lang = require "lang"
local sort_order = require "sort_order"
local show_animation = require "show_animation"
local log = require "log"

local Button = CS.UnityEngine.UI.Button
local RectTransform = CS.UnityEngine.RectTransform
local Text = CS.UnityEngine.UI.Text
local GameObject = CS.UnityEngine.GameObject
local SpriteMask = CS.UnityEngine.SpriteMask
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local face_item_new = require "face_item_new"
local val = require("val")
local effect_item = require "effect_item"

module("ui_halloween_reward_result")
local window = nil
local UIRewardResult = {}
local data = nil
local closeCallBack = nil
local titleStr = ""
local newTitleStr = ""--前面的titleStr不使用先，因为之前统一不改这个名字，现在有地方需要改，所以新加一个
local blockClickItem = false
local dontDisposeModel = false
local effShowType = false

local rewardArray = {}
local flyEffectList = {}
local itemList = nil
local detailListData = nil
local showAdsBtn = false
local langKey = 184010

local isBigReward = nil
local isTavernTask = nil
local canvasGroup = {}
local goodsScale = 0.5

local showShareBtn = false
local shareCallBack = nil

local isNotSortList = false     --是否不需要排序并合并
local maxSize = { x = 0, y = 0 }
local curSize = { x = 0, y = 0 }
local maxHighNum = 3.5
local newGoodHigh = 170

BIGRWARDSTATE = {
    only_one = 1,
    lots_of_reward = 2,
    need_to_close_win = 3,
}

local staticLineEffect = "art/effects/prefabs/ui/ui_yinji_biankuang.prefab"
local cacheParentTra = nil

UIRewardResult.widget_table = {
    closeBtn = { path = "closeBtn", type = Button, backEvent = true },
    TextTitle = { path = "RewardList/Title/name", type = Text },
    TextSubTitle = { path = "RewardList/Title/subTitle", type = Text },
    scroll_table = { path = "RewardList/Content", type = ScrollRectTable },
    itemListRect = { path = "RewardList", type = RectTransform },
    emptyRect = { path = "RewardList/empty", type = RectTransform },
    spriteMask = { path = "SpriteMask", type = SpriteMask },

    draw_result_item = { path = "imgContent/draw_result_item", type = "RectTransform" },
    detail = { path = "detail", type = "RectTransform" },
    detailList = { path = "detail/detailList/Viewport/Content", type = ScrollRectTable },
    OperateMask = { path = "detail/OperateMask", type = "RectTransform" },
    adsBtn = { path = "adsBtn", type = "Button", event_name = "OnBtn_VideoBtnClickedProxy" },
    adsText = { path = "adsBtn/Text", type = "Text" },
    bigRewardRoot = { path = "bigReward/root", type = "RectTransform" },
    bigRewardRect = { path = "bigReward", type = "RectTransform" },
    bigRewardTitle = { path = "bigReward/TextTitle", type = Text },
    bigRewardName = { path = "bigReward/root/name/Text", type = Text },
    imgBgContent = { path = "imgContent", type = RectTransform },
    tavern_report = { path = "tavern_report", type = RectTransform },
    tavern_report_view = { path = "tavern_report/Personal_UnSelected/ViewPort", type = RectTransform },
    Img_ListItemPerson = { path = "tavern_report/Personal_UnSelected/ViewPort/Auto_PresonListContent/Auto_ListItemPerson", type = RectTransform },
    ShareBtn = { path = "share", type = "Button", event_name = "OnBtn_ShareClickedProxy" },
    CloseText = { path = "imgContent/BG/CloseText", type = RectTransform },
    --丧尸的宝藏 --不好扩展，需统一安排支持灵活扩展才行，目前直接插入
    -- zombie_treasure = { path = "zombie_treasure", type = "RectTransform" },
    -- tf_zom_reward_content = { path = "zombie_treasure/tf_zom_viewPort/tf_zom_reward_content", type = "RectTransform" },
    -- zom_sld_x_green = { path = "zombie_treasure/zom_sld_x_green", type = "Slider" },
    -- txt_star_title = { path = "zombie_treasure/Title/txt_star_title", type = "Text" },
    --minigame
    miniGameTextTitle = { path = "MiniGameRewardList/Title/name", type = Text },
    mini_game_scroll_table = { path = "MiniGameRewardList/Content", type = ScrollRectTable },
    miniGameItemListRect = { path = "MiniGameRewardList", type = RectTransform },
}

function OnRenderItem(scroll_rect_item, index, dataItem)
    local hide_reward_effect = val.IsTrue("sw_hide_reward_effect", 0)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    

    if scroll_rect_item.data["itemUI"] then
        for i, v in pairs(scroll_rect_item.data["itemUI"]) do
            if v then
                v:Dispose()
                v = nil
            end
        end
    end

    if scroll_rect_item.data["heroUI"] then
        for i, v in pairs(scroll_rect_item.data["heroUI"]) do
            if v then
                v:Dispose()
                v = nil
            end
        end
    end

    --for i, v in pairs(flyEffectList) do
    --    if v then
    --        v:Dispose()
    --        v = nil
    --    end
    --end
    --flyEffectList = {}

    if scroll_rect_item.data["itemNode"] then
        for i = #scroll_rect_item.data["itemNode"], 1, -1 do
            if scroll_rect_item.data["itemNode"][i] then
                GameObject.Destroy(scroll_rect_item.data["itemNode"][i])
                scroll_rect_item.data["itemNode"][i] = nil
            end
        end
    end

    scroll_rect_item.data["itemNode"] = {}
    scroll_rect_item.data["heroUI"] = {}
    scroll_rect_item.data["itemUI"] = {}
    for k = 1, #dataItem.dataList do
        local item = dataItem.dataList[k]
        -- dump(item)
        local node = GameObject.Instantiate(scroll_rect_item:Get("itemTemp"))
        node.transform:SetParent(scroll_rect_item:Get("itemRect"))
        node.transform.localScale = { x = 1, y = 1, z = 1 }
        table.insert(scroll_rect_item.data["itemNode"], node)
        local heroIcon = node.transform:Find("HeroIcon/root")
        local heroCount = node.transform:Find("HeroIcon/HeroCount")
        local goodsIcon = node.transform:Find("GoodsIcon/root")
        local bigRewardEffect = node.transform:Find("GoodsIcon/effect")
        local multiple = node.transform:Find("GoodsIcon/multiple")
        local multiple_Text = node.transform:Find("GoodsIcon/multiple/multipleText")
        local nameText = node.transform:Find("GoodsIcon/NameText"):GetComponent(typeof(Text))

        goodsIcon:GetComponent(typeof(SortingGroup)).sortingOrder = window.curOrder + 1
        nameText.text = lang.Get(item.dec)
        if item.nType == item_data.Reward_Type_Enum.Hero then
            heroCount.gameObject:SetActive(false)
            heroCount:GetComponent(typeof(Text)).text = "x" .. (item.count or 1)
            local reward_mgr = require "reward_mgr"
            local heroItem = reward_mgr.GetRewardItemData(item, heroIcon, true, goodsScale)
            if not hide_reward_effect then
                if not effShowType then
                    CreateEffect(heroIcon, k)
                else
                    CreateEffect(heroIcon, (index - 1) * 5 + k)--针对智能觉醒奖励弹窗：一行5个物品/英雄，每行都要播放特效
                end
            end
            scroll_rect_item.data["heroUI"][k] = heroItem
            -- table.insert( itemList, heroItem)
        else
            heroCount.gameObject:SetActive(false)
            local reward_mgr = require "reward_mgr"
            local goodsItem = reward_mgr.GetRewardItemData(item, goodsIcon, true, goodsScale)
            if item.nType == item_data.Reward_Type_Enum.Item then
                --region 设置奖励倍数
                if item.multiplier and item.multiplier > 1 then
                    multiple.gameObject:SetActive(true)
                    multiple_Text:GetComponent(typeof(Text)).text = string.format("x%s", item.multiplier)
                end
            end
            
            scroll_rect_item.data["itemUI"][k] = goodsItem
            if not hide_reward_effect then
                if not effShowType then
                    CreateEffect(goodsIcon, k)
                else
                    CreateEffect(goodsIcon, (index - 1) * 5 + k)--针对智能觉醒奖励弹窗：一行5个物品/英雄，每行都要播放特效
                end
            end

            -- 展示物品特效

            -- local childs = effects.gameObject.transform:GetComponentsInChildren(typeof(ParticleSystem))
            -- for i = 0, childs.Length - 1 do
            -- 	childs[i].sortingOrder = window.curOrder+2
            -- end

            if item.showEffect ~= nil then
                -- if not window.staticLineEffect then
                -- 	window.staticLineEffect = {}
                -- end
                bigRewardEffect.gameObject:SetActive(true)
                -- window.staticLineEffect[k]  = window.staticLineEffect[k] or 
                -- effect_item.CEffectItem():Init(staticLineEffect, goodsIcon,window.curOrder+5,nil,12)
            end

            -- table.insert( itemList, goodsItem)
        end
    end
    
    local ItemList = scroll_rect_item:Get("ItemList")
    ItemList.sizeDelta = { x = ItemList.sizeDelta.x, y = curSize - 156}
    
end


function OnMiniGameRenderItem(scroll_rect_item, index, dataItem)
    local hide_reward_effect = val.IsTrue("sw_hide_reward_effect", 0)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem


    if scroll_rect_item.data["itemUI"] then
        for i, v in pairs(scroll_rect_item.data["itemUI"]) do
            if v then
                v:Dispose()
                v = nil
            end
        end
    end

    if scroll_rect_item.data["heroUI"] then
        for i, v in pairs(scroll_rect_item.data["heroUI"]) do
            if v then
                v:Dispose()
                v = nil
            end
        end
    end


    if scroll_rect_item.data["itemNode"] then
        for i = #scroll_rect_item.data["itemNode"], 1, -1 do
            if scroll_rect_item.data["itemNode"][i] then
                GameObject.Destroy(scroll_rect_item.data["itemNode"][i])
                scroll_rect_item.data["itemNode"][i] = nil
            end
        end
    end

    scroll_rect_item.data["itemNode"] = {}
    scroll_rect_item.data["heroUI"] = {}
    scroll_rect_item.data["itemUI"] = {}
    for k = 1, #dataItem.dataList do
        local item = dataItem.dataList[k]
        local node = GameObject.Instantiate(scroll_rect_item:Get("itemTemp"))
        node.transform:SetParent(scroll_rect_item:Get("itemRect"))
        node.transform.localScale = { x = 1, y = 1, z = 1 }
        table.insert(scroll_rect_item.data["itemNode"], node)
        local heroIcon = node.transform:Find("HeroIcon/root")
        local heroCount = node.transform:Find("HeroIcon/HeroCount")
        local goodsIcon = node.transform:Find("GoodsIcon/root")
        local bigRewardEffect = node.transform:Find("GoodsIcon/effect")
        local multiple = node.transform:Find("GoodsIcon/multiple")
        local multiple_Text = node.transform:Find("GoodsIcon/multiple/multipleText")
        local nameText = node.transform:Find("GoodsIcon/NameText"):GetComponent(typeof(Text))

        goodsIcon:GetComponent(typeof(SortingGroup)).sortingOrder = window.curOrder + 1

        if item.dec then
            nameText.text = lang.Get(item.dec)
        else
            nameText.text=""
        end

        if item.nType == item_data.Reward_Type_Enum.Hero then
            heroCount.gameObject:SetActive(false)
            heroCount:GetComponent(typeof(Text)).text = "x" .. (item.count or 1)
            local reward_mgr = require "reward_mgr"
            local heroItem = reward_mgr.GetRewardItemData(item, heroIcon, true, goodsScale)
            if not hide_reward_effect then
                if not effShowType then
                    CreateEffect(heroIcon, k)
                else
                    CreateEffect(heroIcon, (index - 1) * 5 + k)--针对智能觉醒奖励弹窗：一行5个物品/英雄，每行都要播放特效
                end
            end
            scroll_rect_item.data["heroUI"][k] = heroItem
        else
            heroCount.gameObject:SetActive(false)
            local reward_mgr = require "reward_mgr"
            local goodsItem = reward_mgr.GetRewardItemData(item, goodsIcon, true, goodsScale)
            if item.nType == item_data.Reward_Type_Enum.Item then
                --region 设置奖励倍数
                if item.multiplier and item.multiplier > 1 then
                    multiple.gameObject:SetActive(true)
                    multiple_Text:GetComponent(typeof(Text)).text = string.format("x%s", item.multiplier)
                end
            end

            scroll_rect_item.data["itemUI"][k] = goodsItem
            if not hide_reward_effect then
                if not effShowType then
                    CreateEffect(goodsIcon, k)
                else
                    CreateEffect(goodsIcon, (index - 1) * 5 + k)--针对智能觉醒奖励弹窗：一行5个物品/英雄，每行都要播放特效
                end
            end


            if item.showEffect ~= nil then
                bigRewardEffect.gameObject:SetActive(true)
            end

        end
    end

  --  local ItemList = scroll_rect_item:Get("ItemList")
   --ItemList.sizeDelta = { x = ItemList.sizeDelta.x, y = curSize - 156}

end


function UIRewardResult:ctor(selfType)
    self.iDatalist = nil
end

function UIRewardResult:Init()
    event.Trigger(event.ITEM_OPERATION_DONE)
    -- if titleStr and titleStr ~= "" then
    -- 	self.TextTitle.text = titleStr
    -- else
    -- end
    if newTitleStr and newTitleStr ~= "" then
        self.TextTitle.text = newTitleStr
    else
        self.TextTitle.text = lang.Get(602035)
    end   

    --self:RefreshUI()
    -- --print("dataLength",dataLength,self.itemListRect.sizeDelta.y)
    -- self.curOrder = sort_order.ApplyBaseIndexs(self,nil, 2)
    -- self.spriteMask.isCustomRangeActive = true
    -- self.spriteMask.frontSortingOrder = self.curOrder + 1
    -- self.spriteMask.backSortingOrder = self.curOrder -1

    self:SubscribeEvents()
    self:InitList()
    --self:SetZombieTreasure()
    self:SetHalloweenExtraInfo()
end

function UIRewardResult:OnShow()
    self:RefreshUI()
    
    self.detail.gameObject:SetActive(detailListData ~= nil and #detailListData > 0)
    self.adsBtn.gameObject:SetActive(showAdsBtn)
    self.adsText.text = lang.Get(langKey)
    self.imgBgContent.sizeDelta = { x = 100, y = curSize - 156 }
    self.itemListRect.sizeDelta = { x = self.itemListRect.sizeDelta.x, y = curSize - 156 }
    self.ShareBtn.gameObject:SetActive(showShareBtn)
end

function UIRewardResult:RefreshUI()
    self:UpdateList()
    local dataLength = (data == nil) and 1 or #data
    if dataLength == 1 and data ~= nil then
        --针对类似召唤之塔的奖励弹窗
        --dataLength = math.ceil(#data[1].dataList / 4)
        dataLength = math.ceil(#self.iDatalist[1].dataList / 4)
        self.itemListRect.sizeDelta = { x = self.itemListRect.sizeDelta.x, y = 400 + (dataLength - 1) * newGoodHigh }
        self.emptyRect.gameObject:SetActive(false)
    else
        --针对类似觉醒研究所之类的的奖励弹窗
        --self.itemListRect.sizeDelta = { x = self.itemListRect.sizeDelta.x, y = 280.9 + (dataLength) * newGoodHigh }
        self.emptyRect.gameObject:SetActive(true)
    end
    maxSize = { x = self.itemListRect.sizeDelta.x, y = maxHighNum * newGoodHigh + 60 + 156 }
    curSize = math.min(maxSize.y, self.itemListRect.sizeDelta.y + 120)
    if isTavernTask then
        --RefreshUI Transform
        self.tavern_report:SetActive(isTavernTask)
        self.itemListRect.transform.localPosition = { x = self.itemListRect.transform.localPosition.x, y = 399, z = self.itemListRect.transform.localPosition.z }
        self.imgBgContent.transform.localPosition = { x = self.imgBgContent.transform.localPosition.x, y = 492, z = self.imgBgContent.transform.localPosition.z }
        local tempPosY = self.imgBgContent.transform.localPosition.y - (curSize - 156) - 230
        local oldPosY = self.tavern_report.localPosition.y
        self.tavern_report.localPosition = { x = self.tavern_report.localPosition.x, y = tempPosY, z = self.tavern_report.localPosition.z }
        local newViewPosY = math.abs(oldPosY) - math.abs(self.tavern_report.localPosition.y)
        self.tavern_report_view.sizeDelta = { x = self.tavern_report_view.sizeDelta.x, y = self.tavern_report_view.sizeDelta.y + math.abs(newViewPosY) / 2 }
        --RefreshUI Item
        self:FreshItemUI()
        self.CloseText.anchoredPosition = { x = self.CloseText.anchoredPosition.x, y = -48 - self.tavern_report_view.sizeDelta.y, z = self.CloseText.anchoredPosition.z }
    else
        self.CloseText.anchoredPosition = { x = self.CloseText.anchoredPosition.x, y = -48, z = self.CloseText.anchoredPosition.z }
    end
    --self:UpdateList()
end

function UIRewardResult:FreshItemUI()
    local ui_tavern_mgr = require "ui_tavern_mgr"
    local reward_mgr            = require "reward_mgr"
    local data = ui_tavern_mgr.GetCurTaskHelpData()
    if not data then
        return
    end
    self.itemList = self.itemList or {}
    self.itemList.goodList = self.itemList.goodList or {}
    self.faceItem = self.faceItem or {}

    for i = 1, #data do
        local tmp = self.itemList[i] or GameObject.Instantiate(self.Img_ListItemPerson.gameObject, self.Img_ListItemPerson.transform.parent)
        local scroll_rect_item = tmp:GetComponent(typeof(ScrollRectItem))
        scroll_rect_item.gameObject:SetActive(true)
        local isRob = data[i].isRob
        local cfgID = data[i].taskID

        --Title
        local titleText = scroll_rect_item:Get("titleText")
        local allianceShortName = data[i].allianceShortName ~= "" and "[" .. data[i].allianceShortName.."]" or data[i].allianceShortName
        if isRob then
            titleText.text =  "<color=#FF4D2A>"..allianceShortName..data[i].name.."</color>"
        else
            titleText.text =  "<color=#3A6AA9>"..allianceShortName..data[i].name.."</color>"
        end
        
        --contentText
        local helpText = scroll_rect_item:Get("helpText")
        local robText = scroll_rect_item:Get("robText")
        helpText:SetActive(not isRob)
        robText:SetActive(isRob)

        --GoodList
        local Container = scroll_rect_item:Get("Container")
        Container:SetActive(isRob)
        if isRob then
            local goodList = self.itemList.goodList[i] or {}
            local GoodsList = scroll_rect_item:Get("GoodsList")
            local rewardList = reward_mgr.GetRewardGoodsList(game_scheme:SecretTask_0(cfgID).SnatchRewards)
            goodList = UIRewardResult.SetTaskRewardListShow(goodList, rewardList, GoodsList,0.48)
            self.itemList.goodList[i] = goodList
        end

        --bg
        local bg = scroll_rect_item:Get("bg")
        bg.color = isRob and {r=255/255, g=143/255, b=135/255, a=0.5} or {r=190/255, g=233/255, b=255/255, a=0.5}

        --time
        local timeText = scroll_rect_item:Get("timeText")
        local time_util = require "time_util"
        timeText.text = time_util.ConvertStamp3Time(data[i].occurTime)

        --face
        local faceTrans = scroll_rect_item:Get("faceTrans")
        --适配faceID 2025.4.2 新增faceStr替代原本的faceID
        local faceStr = data[i].faceID
        if data[i].faceStr and not string.IsNullOrEmpty(data[i].faceStr) then
            faceStr = data[i].faceStr
        end
        local faceItem = self.faceItem[i] or face_item_new.CFaceItem():Init(faceTrans, nil, 1)
        faceItem:SetFaceInfo(faceStr)
        faceItem:SetFrameID(data[i].frameID, true)
        self.faceItem[i] = faceItem

        self.itemList[i] = scroll_rect_item
    end
end

--设置任务奖励列表
function UIRewardResult.SetTaskRewardListShow(goodList, data, goodListTransform,scale)
    --先隐藏
    if goodList then
        for k, v in pairs(goodList) do
            if v.gameObject then
                v.gameObject:SetActive(false)
            end
        end
    end
    for k,v in pairs(data) do
        local item = goodList[k] or goods_item.CGoodsItem("ui_tavern")
        item:SetMultiGain(v.ItemFlag)
        item:Init(goodListTransform, nil, scale)
        item:SetGoods(nil,v.id,v.num, function()
            local iui_item_detail =  require "iui_item_detail"
            iui_item_detail.Show(v.id,nil,  item_data.Item_Show_Type_Enum.Reward_Interface, v.num,nil, nil, nil)
        end)
        --item.gameObject:SetActive(true)
        goodList[k] = item
    end
    return goodList
end

function UIRewardResult:InitList()
    -- self.listInstance = CScrollList:CreateInstance({})
    -- self.listInstance:Create(self.itemList, CListItem)
    self.scroll_table.onItemRender = OnRenderItem
    self.scroll_table.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item.data then
            -- if scroll_rect_item.data["itemUI"] then
            -- 	scroll_rect_item.data["itemUI"]:Dispose()
            -- 	scroll_rect_item.data["itemUI"] = nil
            -- end

            for i, v in pairs(scroll_rect_item.data["itemUI"]) do
                if v then
                    v:Dispose()
                    v = nil
                end
            end

            for i, v in pairs(scroll_rect_item.data["heroUI"]) do
                if v then
                    v:Dispose()
                    v = nil
                end
            end

            --for i, v in pairs(flyEffectList) do
            --    if v then
            --        v:Dispose()
            --        v = nil
            --    end
            --end
            if scroll_rect_item.data["itemNode"] then
                for i = #scroll_rect_item.data["itemNode"], 1, -1 do
                    if scroll_rect_item.data["itemNode"][i] then
                        GameObject.Destroy(scroll_rect_item.data["itemNode"][i])
                        scroll_rect_item.data["itemNode"][i] = nil
                    end
                end
            end
        end
    end


    self.mini_game_scroll_table.onItemRender = OnMiniGameRenderItem
    self.mini_game_scroll_table.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item.data then
            -- if scroll_rect_item.data["itemUI"] then
            -- 	scroll_rect_item.data["itemUI"]:Dispose()
            -- 	scroll_rect_item.data["itemUI"] = nil
            -- end

            for i, v in pairs(scroll_rect_item.data["itemUI"]) do
                if v then
                    v:Dispose()
                    v = nil
                end
            end

            for i, v in pairs(scroll_rect_item.data["heroUI"]) do
                if v then
                    v:Dispose()
                    v = nil
                end
            end

            --for i, v in pairs(flyEffectList) do
            --    if v then
            --        v:Dispose()
            --        v = nil
            --    end
            --end
            if scroll_rect_item.data["itemNode"] then
                for i = #scroll_rect_item.data["itemNode"], 1, -1 do
                    if scroll_rect_item.data["itemNode"][i] then
                        GameObject.Destroy(scroll_rect_item.data["itemNode"][i])
                        scroll_rect_item.data["itemNode"][i] = nil
                    end
                end
            end
        end
    end



    self.detailList.onItemRender = function(scroll_rect_item, index, dataItem)
        local critical = scroll_rect_item:Get("critical")
        critical.gameObject:SetActive(dataItem.critical and dataItem.critical > 1)

        critical.color = { r = 1, g = 1, b = 1, a = 1 }

        if not scroll_rect_item.data then
            scroll_rect_item.data = {}
            scroll_rect_item.data.root = scroll_rect_item:Get("root")

        end
        scroll_rect_item.data.itemIcon = scroll_rect_item.data.itemIcon or goods_item.CGoodsItem():Init(scroll_rect_item.data.root.transform, nil, goodsScale)
        scroll_rect_item.data.itemIcon:SetGoods(nil, dataItem.id, dataItem.num, function()
            local iui_item_detail = require "iui_item_detail"
            local item_data = require "item_data"
            iui_item_detail.Show(dataItem.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, dataItem.num, nil, nil, dataItem.rewardid)
        end)
        -- scroll_rect_item.data.itemIcon:SetFrameBgEnable(true)
        -- scroll_rect_item.data.itemIcon:SetEquipEnable(false)

    end
    self.detailList.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item.data then
            if scroll_rect_item.data.itemIcon then
                scroll_rect_item.data.itemIcon:Dispose()
                scroll_rect_item.data.itemIcon = nil
            end
            scroll_rect_item.data = nil
        end
    end

end


function UIRewardResult:BuildMiniGameListData()
    local minigame_mgr = require "minigame_mgr"--关卡奖励额外奖励

    local reward_mgr = require "reward_mgr"
    local rewardId=minigame_mgr.GetRewardId()
    if rewardId and rewardId>0 then
        local dataList = reward_mgr.GetRewardGoodsList2(rewardId)
        minigame_mgr.SetPlayReward(false)
        if dataList and #dataList>0 then
            local miniData={}
            local itemData={}
            itemData.dataList=dataList
            table.insert(miniData,itemData)
            return miniData
        end
    end

    return nil

end
function UIRewardResult:BuildListData()
    local dataEx = {}
    if not data then
        return dataEx
    end
    local mail_pb = require "mail_pb"
    local reward_mgr = require "reward_mgr"
    for i = 1, #data do
        local listData = {}
        local itemLists = {}
        local goodListByNum = {}
        local goodIndexList = {}
        listData.title = data[i].title
        listData.dataList = itemLists

        local index = 1
        for j = 1, #data[i].dataList do
            local item = {}
            local itemData = data[i].dataList[j]
            local sid = itemData.sid
            local nType = itemData.nType
            local enType = itemData.enType
            local entity = nType == item_data.Reward_Type_Enum.Hero and player_mgr.GetPalPartDataBySid(sid) or player_mgr.GetPacketPartDataBySid(sid)
            local id = itemData.id
            if id == nil and entity then
                id = nType == item_data.Reward_Type_Enum.Hero and entity.heroID or entity.goodsID
            end
            local num = itemData.num
            item.nType = nType
            item.multiplier = data[i].multiplier and data[i].multiplier or 0
            if nType == item_data.Reward_Type_Enum.Hero then
                --英雄
                local cfg = game_scheme:Hero_0(id)
                item.dec = 0
                if cfg then
                    item.dec = cfg.nameID
                end
                item.id = id
                item.sid = sid
                --item.type = item_data.Item_Type_Enum.Hero--该类型和物品中的类型6英雄头像重复，会导致显示异常
                item.num = num

                if enType == mail_pb.AttachmentType_Reward then
                    -- 奖励类型
                    local rewardID = itemData.rewardId
                    local rewardData = reward_mgr.GetRewardGoodsList(rewardID)
                    if rewardData then
                        item.lv = rewardData.lv
                        item.starLevel = rewardData.starLevel
                    end
                else
                    item.lv = entity and entity.numProp.lv or itemData.lv or 1
                    item.starLevel = itemData.starLevel and itemData.starLevel or (entity and entity.numProp.starLv or (cfg and cfg.starLv))
                end
                item.skinID = itemData.skinID
                item.showEffect = itemData.showEffect
                itemLists[index] = item
                index = index + 1
            elseif nType == item_data.Reward_Type_Enum.Survivor then
                --幸存者
                local cfg = game_scheme:BuildSurvivor_0(id)
                item.dec = 0
                if cfg then
                    item.dec = cfg.NameID
                end
                item.id = id
                item.sid = sid
                --item.type = item_data.Item_Type_Enum.Hero--该类型和物品中的类型6英雄头像重复，会导致显示异常
                item.num = num or 1
                item.showEffect = itemData.showEffect
                --table.insert(itemLists, item)
                itemLists[index] = item
                index = index + 1
            else
                --物品和士兵
                if isNotSortList then
                    --列表不需要进行排序
                    if id then
                        local cfg = game_scheme:Item_0(id)
                        item.dec = 0
                        if cfg then
                            --item.type = cfg.type
                            item.dec = cfg.nameKey
                        end
                        item.num = num
                        item.sid = sid
                        item.id = id
                        item.soliderId = itemData.soliderId or 0
                        item.rewardid = itemData.rewardid
                    end
                    item.showEffect = itemData.showEffect
                    table.insert(itemLists, item)
                else
                    if id then
                        local good = goodListByNum[id] or {}
                        if not good.index then
                            good.index = index
                            index = index + 1
                            local cfg = game_scheme:Item_0(id)
                            good.dec = 0
                            if cfg then
                                --good.type = cfg.type
                                good.dec = cfg.nameKey
                            end
                            good.sid = sid
                            good.id = id
                            good.rewardid = itemData.rewardid
                            good.showEffect = itemData.showEffect
                            good.multiplier = data[i].multiplier and data[i].multiplier or 0
                            good.num = num
                            good.nType = nType
                            item.soliderId = itemData.soliderId or 0
                        else
                            good.num = good.num + num
                        end
                        goodListByNum[id] = good
                    end
                end
            end
        end
        --itemLists = listData.dataList
        for k, v in pairs(goodListByNum) do
            if v.num and v.num ~= 0 then
                itemLists[v.index] = v
                --table.insert(itemLists, v)
            end
        end
        table.insert(dataEx, listData)
    end
    return dataEx
end

function UIRewardResult:UpdateUI()
    self.curOrder = sort_order.ApplyBaseIndexs(self, nil, 2)    --重新获得层级
    self.spriteMask.isCustomRangeActive = true
    self.spriteMask.frontSortingOrder = self.curOrder + 1
    self.spriteMask.backSortingOrder = self.curOrder - 1
    -- if canvasGroup then
    -- 	for k,v in pairs(canvasGroup)do
    -- 		if v then
    -- 			v.sortingOrder = self.curOrder+1
    -- 		end
    -- 	end
    -- end
end

function UIRewardResult:UpdateList()
    ------  --print("UIRewardResult >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
    -- if self.listInstance == nil then
    -- 	return
    -- end
    self.bigRewardRect.gameObject:SetActive(isBigReward == BIGRWARDSTATE.only_one)
    self.imgBgContent.gameObject:SetActive(isBigReward ~= BIGRWARDSTATE.only_one)
    self.itemListRect.gameObject:SetActive(not isBigReward or isBigReward == BIGRWARDSTATE.lots_of_reward)
    self.miniGameItemListRect.gameObject:SetActive(false)


    if not isBigReward or isBigReward == BIGRWARDSTATE.lots_of_reward then
        if not self.curRewardRender then
            self.curRewardRender = true
            self.iDatalist = self:BuildListData()
            -- self.listInstance:SetListData(listData)
            self.scroll_table.pageSize = #self.iDatalist
            self.scroll_table.data = self.iDatalist
            --self.scroll_table:Refresh(0, -1)

            self.MiniGameDatalist=self:BuildMiniGameListData()
            if  self.MiniGameDatalist then
                self.miniGameTextTitle.text = lang.Get(673018)
                self.CloseText.gameObject:SetActive(false)
                self.miniGameItemListRect.gameObject:SetActive(true)
                self.mini_game_scroll_table.pageSize = #self.MiniGameDatalist
                self.mini_game_scroll_table.data = self.MiniGameDatalist
                self.mini_game_scroll_table:Refresh(0, -1)
            end

        end

        self.spriteMask.isCustomRangeActive = true
        self.spriteMask.frontSortingOrder = self.curOrder + 1
        self.spriteMask.backSortingOrder = self.curOrder - 1

        if detailListData then
            self.detailList.data = detailListData
            self.detailList.pageSize = #detailListData
            self.detailList:Refresh(-1, -1)

            local perHandlerFun = function(count)
                local h = math.ceil(math.max(count - 15, 0) / 5)
                local mValue = 120 * h
                local oldPos = self.detailList.transform.localPosition
                self.detailList.transform.localPosition = { x = oldPos.x, y = mValue, z = oldPos.z }

                local len = #detailListData

                if count == len then
                    self.OperateMask.gameObject:SetActive(false)
                    if stopCallback then
                        stopCallback()
                        stopCallback = nil
                    end
                end
                ------print(count,mValue)
            end

            local len = #detailListData
            self.OperateMask.gameObject:SetActive(true)
            local modelPath = "art/effects/prefabs/ui/effect_ui_huodedaoju_02.prefab"
            local maxSpeed = 0.03
            local minSpeed = 0.1
            local speed = minSpeed + (maxSpeed - minSpeed) * math.min(math.max(len - 14, 0) / 50, 1)
            ------ --print("speed>>>>>>>>>>>>",speed)
            show_animation.PlayAni("dungeon_map_reward", self.detailList.transform, modelPath, self.curOrder + 1, len, nil, nil, nil, nil, nil, nil, speed, perHandlerFun)
        end
        --self.imgBgContent.sizeDelta = { x = 100, y = self.itemListRect.sizeDelta.y - 156 } --刷新背景大小
    else
        self.bigRewardTitle.text = titleStr
        local goodsItem = self.bigRewardItem or goods_item.CGoodsItem()
        goodsItem:Init(self.bigRewardRoot, nil, goodsScale)
        goodsItem:SetFrameBg(3)
        -- 加载物品图标
        goodsItem:SetGoods(nil, data.id, data.count, function()
            if blockClickItem then
                return
            end
            local iui_item_detail = require "iui_item_detail"
            iui_item_detail.Show(data.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, data.count, nil, nil, data.rewardid)
        end, 0.8)
        goodsItem:SetCountEnable(true)
        self.bigRewardItem = goodsItem

        local itemCfg = game_scheme:Item_0(data.id)
        if itemCfg then
            self.bigRewardName.text = lang.Get(itemCfg.nameKey)
        else
            self.bigRewardName.text = ""
            log.Error("Item.csv not find", data.id)
        end

    end
end

function UIRewardResult:SubscribeEvents()
    self.closeEvent = function()
        --英雄展示
        local hero_mgr = require "hero_mgr"
        local tArrHero = {}
        local showNewSign = false
        if data then
            for i = 1, #data do
                if data[i].nType == item_data.Reward_Type_Enum.Hero then
                    local item = player_mgr.GetPalPartDataBySid(data[i].sid)
                    if item and item:GetHeroNumProps().starLv >= 4 then
                        if not hero_mgr.HaveOwnedHero(item:GetHeroID()) then
                            showNewSign = true
                            table.insert(tArrHero, item)
                            hero_mgr.MarkOwnedHero(item:GetHeroID())
                        end
                    end
                end
            end
        end
        if #tArrHero ~= 0 then
            local flow_text = require "flow_text"
            flow_text.Add(lang.Get(9363))
            local ui_show_hero_card = require "ui_show_hero_card"
            event.Trigger(event.SUPPER_EFFECT_ENABLE, false)
            ui_show_hero_card.ShowWithData(ui_show_hero_card.ParseArrHeroEntity(tArrHero), function()
                event.Trigger(event.SUPPER_EFFECT_ENABLE, true)
            end, dontDisposeModel, showNewSign)
        end

        if isBigReward == BIGRWARDSTATE.lots_of_reward then
            isBigReward = BIGRWARDSTATE.need_to_close_win
        end

        if closeCallBack then
            closeCallBack()
            closeCallBack = nil
        end

        if util.get_len(rewardArray) > 0 then
            ------ --print("读取队列，重新赋值")
            local newArr = {}
            for key, value in pairs(rewardArray) do
                ------ --print("赋值：key=",key)
                if key == 1 then
                    data = value.data
                    titleStr = value.titleStr
                    closeCallBack = value.closeCallBack
                    blockClickItem = value.blockClickItem
                    dontDisposeModel = value.dontDisposeModel
                else
                    table.insert(newArr, value)
                end
            end
            self.curRewardRender = false
            window:RefreshUI()
            window.imgBgContent.sizeDelta = { x = 100, y = curSize - 156 }
            window.itemListRect.sizeDelta = { x = window.itemListRect.sizeDelta.x, y = curSize - 156 }
            window.scroll_table:Refresh(0, -1)
            data = newArr
            rewardArray = newArr
        else
            ------ --print("队列为空，关闭")
            if not isBigReward or isBigReward == BIGRWARDSTATE.need_to_close_win then
                -- 大奖展示时  不需要关闭  因为有二次弹窗
                windowMgr:UnloadModule("ui_halloween_reward_result")
            end
        end

    end
    self.closeBtn.onClick:AddListener(self.closeEvent)

    self.OnBtn_VideoBtnClickedProxy = function()
        windowMgr:UnloadModule("ui_halloween_reward_result")
        event.Trigger(event.CLICK_NEXT_VIDEO_BTN)
    end

    self.OnBtn_ShareClickedProxy = function()
        if shareCallBack then
            shareCallBack()
        end
    end
end

--资源获取动画展示接口
function UIRewardResult:DoDonateAnim()
    local gw_common_item_animation_mgr = require "gw_common_item_animation_mgr"
    if data then
        for k, v in pairs(data) do
            if v.useItemAnim then
                for k, rewardData in pairs(v.dataList) do
                    gw_common_item_animation_mgr.DoItemAnimationTransformToTransform(rewardData.id, rewardData.num, self.itemListRect.transform)
                end
            end
        end
    end
end

function UIRewardResult:UnsubscribeEvents()
    self.closeBtn.onClick:RemoveListener(self.closeEvent)
end
--region 丧尸的宝藏
function UIRewardResult:SetZombieTreasure()
    if not data or  #data == 0 then
        return 
    end
    if not data[1] then
        return
    end
    local otherParam = data[1].otherParam
    if not otherParam or  not otherParam.isZombieTreasure then
        return
    end
    local commonRewardList = data[1].dataList and #data[1].dataList or 1
    local line = math.ceil(commonRewardList / 4)
    if line > 1 then
        self:SetLocalPos(self.zombie_treasure, 0,-220 - (line - 1) * 120, 0)
    end
    self:SetActive(self.zombie_treasure, true)
    self:SetActive(self.CloseText, false)
    local zom_data = otherParam
    --开始设置表现
    local div = zom_data.nextLevelValue - zom_data.curValue
    self:SetText(self.txt_star_title,lang.GetFormat(1005506,div))
    local fenMu = (zom_data.nextLevelValue - zom_data.curLevelValue)
    if fenMu == 0 then
        fenMu = 1
    end
    self.zom_sld_x_green.value = (zom_data.curValue - zom_data.curLevelValue) / fenMu
    self:SetText(self.zom_sld_x_green,string.format("%s/%s",(zom_data.curValue - zom_data.curLevelValue),fenMu),"Text_1")
    --设置奖励
    if zom_data.ZomRewardId then
        local reward_mgr = require "reward_mgr"
        self.zomGoodsItems = reward_mgr.GetRewardItemList(zom_data.ZomRewardId, self.tf_zom_reward_content, function(id, number)
            local iui_item_detail =  require "iui_item_detail"
            --local item_data = require "item_data"
            iui_item_detail.Show(id,nil,  item_data.Item_Show_Type_Enum.Reward_Interface, number,nil, nil, nil)
        end, 0.45)
    end
end
--endregion

function UIRewardResult:SetHalloweenExtraInfo()
    if not data or  #data == 0 then
        return 
    end
    if not data[1] then
        return
    end
    local otherParam = data[1].otherParam
    if not otherParam or  not otherParam.halloween_extra_data then
        return
    end

    local halloween_extra_data = otherParam.halloween_extra_data
    local draw_result_item = require "draw_result_item"
    draw_result_item.RenderByData(self.draw_result_item, halloween_extra_data.act_id, halloween_extra_data)
end


function UIRewardResult:Close()
    if self.UIRoot and self:IsValid() then
        self:DoDonateAnim()
        self:UnsubscribeEvents()
    end

    -- if self.staticLineEffect then
    -- 	for k,v in pairs(self.staticLineEffect)do
    -- 		v:Dispose()
    -- 	end
    -- end

    if self.bigRewardItem then
        self.bigRewardItem:Dispose()
        self.bigRewardItem = nil
    end
    if self.zomGoodsItems then
        for i, v in ipairs(self.zomGoodsItems) do
            v:Dispose()
        end
        self.zomGoodsItems = nil
    end
    -- if itemList and #itemList > 0 then
    -- 	for k,v in pairs(itemList)do
    -- 		if v then
    -- 			v:Dispose()
    -- 			v = nil
    -- 		end
    -- 	end
    -- 	itemList = nil
    -- end

    for i, v in pairs(flyEffectList) do
        if v then
            --v:Dispose()
            --v = nil
            --缓存这个特效到RedParent上，反正RedParent也没存啥东西。
            if cacheParentTra == nil then
                cacheParentTra = GameObject.Find("UIRoot/RedParent").transform
            end
            v:SetParent(cacheParentTra)
        end
    end

    if self.detailList then
        self.detailList:ItemsDispose()
    end

    if self.scroll_table then
        self.scroll_table:ItemsDispose()
    end
    if self.mini_game_scroll_table then
        self.mini_game_scroll_table:ItemsDispose()
    end




    if self.itemList and self.itemList.goodList then
        for i = 1, #self.itemList.goodList do
            if self.itemList.goodList[i] then
                for k,item in pairs(self.itemList.goodList[i]) do
                    item:Dispose()
                end
            end
        end
        self.itemList.goodList = nil
        self.itemList = nil
    end

    -- self.listInstance = nil
    window = nil
    data = nil
    titleStr = ""
    blockClickItem = false
    dontDisposeModel = false
    effShowType = false
    rewardArray = {}
    detailListData = nil
    closeCallBack = nil
    show_animation.ClearData()
    --flyEffectList = {}
    canvasGroup = {}
    showAdsBtn = false
    langKey = 184010
    isBigReward = nil
    showShareBtn = nil
    shareCallBack = nil
    newTitleStr = nil
    self.curRewardRender = false
    self.__base:Close()
    
    event.Trigger(event.REWARD_RESULT_NEW_CLOSE)
end

--local CModelViewer = require "modelviewer"
function CreateEffect(parentTrans, pos)
    if not window then
        return
    end
    local modelPath = "art/effects/prefabs/ui/effect_ui_huodedaoju_02.prefab"
    if flyEffectList[pos] then
        flyEffectList[pos]:SetParent(parentTrans)
        flyEffectList[pos]:Replay()
        return
    end
    if not flyEffectList[pos] then
        --flyEffectList[pos] = CModelViewer()
        --flyEffectList[pos]:Init(parentTrans, function()
        --    if pos and flyEffectList[pos] and modelPath then
        --        flyEffectList[pos]:ShowGameObject(modelPath)
        --    end
        --end)
        ----设置光效的渲染顺序
        --if window then
        --    flyEffectList[pos]:SetRenderOrder(window.curOrder + 1)
        --end
        --缓存effect_item来实现，更方便做缓存。
        flyEffectList[pos] = effect_item.CEffectItem():Init(modelPath, parentTrans, window.curOrder + 1, nil, 1, false, false, false)
        
    end
    
end

local CUIRewardResult = class(ui_base, nil, UIRewardResult)

---设置获得奖励数据信息, 其中nType暂时只传item_data.Reward_Type_Enum.Item类型
---@param datalist 物品或英雄数据，具体结构如下
---{{
---        title = "普通英雄自动分解",
---        dataList = {{sid = 0, id = 0, num = 0, nType(见item_data.Reward_Type_Enum) = 0, starLevel=0, rewardid(是物品就加) = 0}}
---}}
---@param CloseCallback 获得奖励界面关闭回调
---@param title 顶部标题，不传则使用默认值：获得奖励
---@param block 奖励图标是否可以接受点击
---@param effShowType 特效显示类型(1、智能觉醒奖励弹窗：一行5个物品/英雄，每行都要播放特效)
---@param detailListData 详情列表
---@param isBigReward 是否仅展示一个
---@param rewardMultiple number 奖励倍数
---@param newTitle 新标题
---@param NotSortList 是否不需要排序
function SetInputParam(datalist, CloseCallback, title, block, _dontDisposeModel, _effShowType, _detailListData, _isBigReward, rewardMultiple, newTitle, NotSortList, _isTavernTask)
    if window and window.UIRoot and window:IsValid() then
        isBigReward = _isBigReward
        isNotSortList = NotSortList
        if not isBigReward then
            table.insert(rewardArray, {
                data = datalist, --奖励列表
                titleStr = title, --内容
                closeCallBack = CloseCallback,
                blockClickItem = block,
                dontDisposeModel = _dontDisposeModel,
            })
        else
            data = datalist--奖励列表
            titleStr = title--内容
            closeCallBack = CloseCallback
            blockClickItem = block
            dontDisposeModel = _dontDisposeModel
            effShowType = _effShowType
            detailListData = _detailListData
        end
        ------ --print("已经打开窗口，插入新数据>>>>>")
    else
        ------ --print("新窗口")
        data = datalist--奖励列表
        titleStr = title--内容
        closeCallBack = CloseCallback
        blockClickItem = block
        dontDisposeModel = _dontDisposeModel
        effShowType = _effShowType
        detailListData = _detailListData
        isBigReward = _isBigReward
        isTavernTask = _isTavernTask
        isNotSortList = NotSortList
        SetTitle(newTitle)
    end
end

function SetTitle(title)
    newTitleStr = title
end

function SetShowAdsBtnEnable(_showAdsBtn, _langKey)
    -- showAdsBtn = _showAdsBtn and util.ShouldUseCustomSDKUI() --国内版本不显示广告按钮
    if _langKey then
        langKey = _langKey
    end

    if window and window.UIRoot and window:IsValid() then
        window.adsBtn.gameObject:SetActive(showAdsBtn)
        window.adsText.text = lang.Get(langKey)
    end
end

--特殊需求，竞技场挑战第一名要弹出一个这个窗口，显示牛逼
function SetShowTextOnly()
    window.detail.gameObject:SetActive(false)
    window.TextTitle.text = ""
    window.TextDetail.text = lang.Get(8848)
    window.itemListRect.gameObject:SetActive(false)
end
---这是分享按钮是否显示
---@param activity boolean true表示显示
---@param callBack function 点分享后的回到
function SetShareBtnActive(activity, callBack)
    showShareBtn = activity
    shareCallBack = callBack
    if window and window.UIRoot and window:IsValid() then
        window.ShareBtn.gameObject:SetActive(activity)
    end
end

function Show()
    -- local heroCount = 0
    -- if data then
    --     for i = 1, #data do
    --         heroCount = heroCount + 1
    --     end
    -- end
    local path = "ui/prefabs/gw/activity/halloweenactivity/slotmachine/uihalloweenslotrewardpop.prefab"
    if window == nil then
        window = CUIRewardResult()
        window._NAME = _NAME;
        window:LoadUIResource(path, nil, nil, nil, true)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function OnSceneDestroy()
    Close()
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)