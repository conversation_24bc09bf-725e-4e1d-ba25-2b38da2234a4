--- Created by fgy.
--- Changed by connan.
--- DateTime: 2024/10/14
--- Des:行军预览线

local require = require
local Utility = CS.War.Script.Utility
local UIUtil = UIUtil

local gw_admin = require "gw_admin"
local game_scheme = require "game_scheme"

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
module("sand_preview_line")

---@class SandPreviewLine : GWDisposableObject 
local SandPreviewLine = {  }

-- 默认速度配置ID
local defaultSpeedID = 80

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
function SandPreviewLine:Init(id, data, callback)
    self.__base.Init(self, id, data, callback)

    self.marchLineSpeed = game_scheme:GWMapConstant_0(defaultSpeedID).szParam.data[0]    -- 默认速度
    return self
end

function SandPreviewLine:OnBind()
    self.__base.OnBind(self)
    self.from = UIUtil.GetSpriteRender(self.transform, "trans_from")
    self.to = UIUtil.GetSpriteRender(self.transform, "trans_to")
    self.arrow = UIUtil.GetSpriteRender(self.transform, "sp_arrow")
end

function SandPreviewLine:OnUpdate()
    self.__base.OnUpdate(self)

    Utility.SetMaterialBlockProperty(0, self.arrow, "_AniSpeed", { x = self.marchLineSpeed, y = 0, z = 1, w = 1 })
    if self.previewData.color then
        local color_palette = require "color_palette"
        local color = color_palette.HexToColor(self.previewData.color)
        Utility.SetMaterialBlockProperty(2, self.arrow, "_Color", { x = color.r, y = color.g, z = color.b, w = 1 })
    else
        Utility.SetMaterialBlockProperty(2, self.arrow, "_Color", { x = 1, y = 1, z = 1, w = 1 })
    end

    self:UpdateLine()
end

function SandPreviewLine:UpdateLine()
    self:ShowMarchLineSP(self.from.transform, self.previewData.startPosition)
    self:ShowMarchLineSP(self.to.transform, self.previewData.endPosition)
    self:Refresh(self.arrow, self.previewData.startPosition, self.previewData.endPosition)
end

function SandPreviewLine:Refresh(sp, from, to)
    local forward = gw_admin.VectorUtility.Vec2Sub(to, from)
    forward.z = 0
    local height = gw_admin.VectorUtility.Magnitude(forward)
    local angle = self:GetAngle(forward)
    sp.size = { x = height, y = 0.13 }
    self:ShowMarchLineSP(sp, { x = (to.x + from.x) / 2, y = (to.y + from.y) / 2 }, angle)
end

function SandPreviewLine:GetAngle(dir)
    local angle = gw_admin.VectorUtility.Angle(dir, { x = 1, y = 0, z = 0 })
    if dir.y < 0 then
        angle = -angle
    end
    return angle
end

function SandPreviewLine:ShowMarchLineSP(sp, position, angle)
    UIUtil.SetActive(sp, true)
    if position then
        UIUtil.SetLocalPos(sp.transform, position.x, 0, position.y)
    end
    if angle then
        UIUtil.SetRotation(sp.transform, 90, 0, angle)
    end
end

local class = require "class"
local sand_preview_base = require "sand_preview_base"
CM = class(sand_preview_base, nil, SandPreviewLine)
