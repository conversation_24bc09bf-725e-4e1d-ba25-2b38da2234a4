-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
module('error_code_pb')


V1M=V(4,"enErr_NoError",0,0)
V2M=V(4,"enErr_Unknown",1,1)
V3M=V(4,"enErr_Abnormal",2,2)
V4M=V(4,"enErr_Busy",3,3)
V5M=V(4,"enErr_GoldOverflow",4,4)
V6M=V(4,"enErr_InventoryFull",5,5)
V7M=V(4,"enErr_NotEnoughMoney",6,6)
V8M=V(4,"enErr_NotEnoughGold",7,7)
V9M=V(4,"enErr_HeroFull",8,8)
V10M=V(4,"enErr_NotEnoughGoods",9,9)
V11M=V(4,"enErr_NotEnoughCond",10,10)
V12M=V(4,"enErr_HeroNotFound",11,11)
V13M=V(4,"enErr_Freezing",12,12)
V14M=V(4,"enErr_IllegalStr",13,13)
V15M=V(4,"enErr_ConsumeItemFail",14,14)
V16M=V(4,"enErr_ConsumeMoneyFail",15,15)
V17M=V(4,"enErr_ConsumeGoldFail",16,16)
V18M=V(4,"enErr_PalPlaceIllegal",17,17)
V19M=V(4,"enErr_SameHeroID",18,18)
V20M=V(4,"enErr_FewHero",19,19)
V21M=V(4,"enErr_HeroLowLevel",20,20)
V22M=V(4,"enErr_HeroLowStar",21,21)
V23M=V(4,"enErr_PlayerVipLv",22,22)
V24M=V(4,"enErr_ModuleNoOpen",23,23)
V25M=V(4,"enErr_DataSyncing",24,24)
V26M=V(4,"enErr_ClickFrequently",25,25)
V27M=V(4,"enErr_HeroCount_Max",26,26)
V28M=V(4,"enErr_Common_NoCount",27,27)
V29M=V(4,"enErr_Common_ConfigError",28,28)
V30M=V(4,"enErr_Common_ActNotOpen",29,29)
V31M=V(4,"enErr_Mail_SendReqFailed",30,30)
V32M=V(4,"enErr_Mail_Invalid",31,31)
V33M=V(4,"enErr_Mail_NotExist",32,32)
V34M=V(4,"enErr_Mail_AttachmentNotExist",33,33)
V35M=V(4,"enErr_Mail_AttachmentAlreadyGot",34,34)
V36M=V(4,"enErr_Mail_AttachmentGetFailed",35,35)
V37M=V(4,"enErr_Mail_InventoryFull",36,36)
V38M=V(4,"enErr_Mail_GetListFailed",37,37)
V39M=V(4,"enErr_Mail_SendMailFailed",38,38)
V40M=V(4,"enErr_Mail_AddresseeNotExist",39,39)
V41M=V(4,"enErr_Mail_GetMailDetailFail",40,40)
V42M=V(4,"enErr_BindAccount_NotVisitor",41,50)
V43M=V(4,"enErr_BindAccount_BindAlready",42,51)
V44M=V(4,"enErr_BindAccount_AccountHasRole",43,52)
V45M=V(4,"enErr_BindAccount_AccountVerifyFail",44,53)
V46M=V(4,"enErr_BindAccount_CodeErr1",45,54)
V47M=V(4,"enErr_BindAccount_BCUserID_Empty",46,55)
V48M=V(4,"enErr_Login_ServerMaintenance",47,60)
V49M=V(4,"enErr_Login_IOS_Forbidden",48,61)
V50M=V(4,"enErr_Login_Interior_illegal_Dev",49,62)
V51M=V(4,"enErr_Login_White_List_Audit",50,63)
V52M=V(4,"enErr_Login_Http_Fail",51,64)
V53M=V(4,"enErr_Match_Failed",52,70)
V54M=V(4,"enErr_Match_RegionUnopened",53,71)
V55M=V(4,"enErr_LobbyTeam_CaptainOnly",54,80)
V56M=V(4,"enErr_LobbyTeam_NotReady",55,81)
V57M=V(4,"enErr_LoginSvr_Login",56,90)
V58M=V(4,"enErr_LoginSvr_AutoLogin",57,91)
V59M=V(4,"enErr_LoginSvr_AutoLoginChkAls",58,92)
V60M=V(4,"enErr_LobbySvr_BuildInfo",59,110)
V61M=V(4,"enErr_LobbySvr_Login",60,111)
V62M=V(4,"enErr_LobbySvr_CheckALS",61,112)
V63M=V(4,"enErr_LobbySvr_Verify",62,113)
V64M=V(4,"enErr_LobbySvr_CreateActorFail",63,114)
V65M=V(4,"enErr_LobbySvr_ExportActorFail",64,115)
V66M=V(4,"enErr_LobbySvr_Create",65,116)
V67M=V(4,"enErr_LobbySvr_RoleList",66,117)
V68M=V(4,"enErr_LobbySvr_Shoot",67,118)
V69M=V(4,"enErr_LobbySvr_Select",68,119)
V70M=V(4,"enErr_LobbySvr_Version",69,120)
V71M=V(4,"enErr_LobbySvr_Create_TooMany",70,121)
V72M=V(4,"enErr_LobbySvr_WorldID_Error",71,122)
V73M=V(4,"enErr_Recharge_PayNumNotMatch",72,140)
V74M=V(4,"enErr_Recharge_IllegalPartnerID",73,141)
V75M=V(4,"enErr_Recharge_ActivityLimite",74,142)
V76M=V(4,"enErr_Recharge_MonthCardLimit",75,143)
V77M=V(4,"enErr_Recharge_EnterCityLimit",76,144)
V78M=V(4,"enErr_Recharge_LifetimeCardLimite",77,145)
V79M=V(4,"enErr_Recharge_OffShelves",78,146)
V80M=V(4,"enErr_Recharge_Max",79,160)
V81M=V(4,"enErr_GoldenHand_HasGet",80,161)
V82M=V(4,"enErr_GoldenHand_Max",81,170)
V83M=V(4,"enErr_Hero_Coins_NotEnough",82,200)
V84M=V(4,"enErr_Hero_Exp_NotEnough",83,201)
V85M=V(4,"enErr_Hero_Advance_NotEnough",84,202)
V86M=V(4,"enErr_Hero_Update_Equip_Fail",85,203)
V87M=V(4,"enErr_Hero_Star_NotEnough",86,204)
V88M=V(4,"enErr_Hero_Advance_Lv",87,205)
V89M=V(4,"enErr_Hero_Compose_Fail",88,206)
V90M=V(4,"enErr_Hero_List_Buy_Max",89,207)
V91M=V(4,"enErr_Hero_Upgrade_Lv_Max",90,208)
V92M=V(4,"enErr_Hero_Advance_Max_Lv",91,209)
V93M=V(4,"enErr_Hero_Change_Goods_NotEnough",92,210)
V94M=V(4,"enErr_Hero_Change_Star_Err",93,211)
V95M=V(4,"enErr_Hero_Change_Cost_Hero_NotEnough",94,212)
V96M=V(4,"enErr_Hero_Change_Same_Hero",95,213)
V97M=V(4,"enErr_Hero_Compose_Fail1",96,214)
V98M=V(4,"enErr_Hero_Advance_Fail1",97,215)
V99M=V(4,"enErr_Hero_Awaken_Fail1",98,216)
V100M=V(4,"enErr_Hero_Upgrade_Fail1",99,217)
V101M=V(4,"enErr_Change_Cost_Hero_Star_Lv_NotEnough",100,218)
V102M=V(4,"enErr_Hero_HandBook_Unactived",101,219)
V103M=V(4,"enErr_Hero_HandBook_MaxLv",102,220)
V104M=V(4,"enErr_Hero_Update_Decorate_Fail",103,221)
V105M=V(4,"enErr_Hero_Rand_Decorate_Fail",104,222)
V106M=V(4,"enErr_Hero_Create_Decorate_Fail",105,223)
V107M=V(4,"enErr_Hero_Scheme_DecorateID_Fail",106,224)
V108M=V(4,"enErr_Hero_Pos_Decorate_Fail",107,225)
V109M=V(4,"enErr_Hero_Decorate_Exchange_Num0_Fail",108,226)
V110M=V(4,"enErr_Hero_Rand_Coin_NoEnough_Fail",109,227)
V111M=V(4,"enErr_Hero_Scheme_DecorateID_Quality_Fail",110,228)
V112M=V(4,"enErr_Hero_Scheme_DecorateID_Not_Exist",111,229)
V113M=V(4,"enErr_Skill_Points_NotEnough",112,230)
V114M=V(4,"enErr_Skill_UseNum_NotEnough",113,231)
V115M=V(4,"enErr_Skill_Slot_Lock",114,232)
V116M=V(4,"enErr_Skill_Same_Skill",115,233)
V117M=V(4,"enErr_Skill_Born_Skill",116,234)
V118M=V(4,"enErr_Skill_Pos_Have_Skill",117,235)
V119M=V(4,"enErr_Skill_Pos_Not_Skill",118,236)
V120M=V(4,"enErr_Skill_Have_Same_Skill",119,237)
V121M=V(4,"enErr_Equip_5_Slot_Lock",120,240)
V122M=V(4,"enErr_Equip_6_Slot_Lock",121,241)
V123M=V(4,"enErr_Equip_Type_Err",122,242)
V124M=V(4,"enErr_Equip_Exclusive_Cant_Takeoff",123,243)
V125M=V(4,"enErr_Update_Gemstone_Fail",124,244)
V126M=V(4,"enErr_Update_Gemstone_Had_Activated",125,245)
V127M=V(4,"enErr_Update_Not_Exist",126,246)
V128M=V(4,"enErr_Equip_Max_Lv",127,250)
V129M=V(4,"enErr_Goods_Num_NotEnough",128,300)
V130M=V(4,"enErr_Goods_Sell_Fail",129,301)
V131M=V(4,"enErr_Goods_Compose_Fail",130,302)
V132M=V(4,"enErr_Goods_Skill_Update_Fail",131,303)
V133M=V(4,"enErr_Goods_Skill_Upgrade_Fail",132,304)
V134M=V(4,"enErr_Goods_Equip_Upgrade_Fail",133,305)
V135M=V(4,"enErr_Goods_Equip_Decompose_Fail",134,306)
V136M=V(4,"enErr_Goods_Equip_Locked",135,307)
V137M=V(4,"enErr_Idle_Res_Get_Fail",136,400)
V138M=V(4,"enErr_Idle_Goods_Get_Fail",137,401)
V139M=V(4,"enErr_Idle_Stage_ChestReward_Get_Fail",138,402)
V140M=V(4,"enErr_Idle_Star_ChestReward_Get_Fail",139,403)
V141M=V(4,"enErr_Idle_Stage_ChestReward_Miss",140,404)
V142M=V(4,"enErr_Idle_Star_ChestReward_Miss",141,405)
V143M=V(4,"enErr_Idle_Stage_ChestReward_Order",142,406)
V144M=V(4,"enErr_Idle_Star_ChestReward_Order",143,407)
V145M=V(4,"enErr_Idle_Stage_DidNotBuyGift",144,408)
V146M=V(4,"enErr_Idle_Star_DidNotBuyGift",145,409)
V147M=V(4,"enErr_Idle_Stage_NoReward",146,410)
V148M=V(4,"enErr_Idle_Star_NoReward",147,411)
V149M=V(4,"enErr_Idle_Max",148,449)
V150M=V(4,"enErr_Chat_Lv_NotEnough1",149,450)
V151M=V(4,"enErr_Chat_Lv_NotEnough2",150,451)
V152M=V(4,"enErr_Chat_Lv_NotEnough3",151,452)
V153M=V(4,"enErr_Chat_Not_Guild",152,453)
V154M=V(4,"enErr_Chat_Empty_Context",153,454)
V155M=V(4,"enErr_Chat_Max_Len",154,455)
V156M=V(4,"enErr_Chat_Blocked_Person",155,456)
V157M=V(4,"enErr_Chat_unBlocked_Person",156,457)
V158M=V(4,"enErr_Chat_Mute",157,458)
V159M=V(4,"enErr_Not_Territory",158,459)
V160M=V(4,"enErr_Chat_Be_Blocked",159,460)
V161M=V(4,"enErr_Chat_Lv_BattleLv_Not_Enough",160,461)
V162M=V(4,"enErr_Chat_Gm_Close_Conversation",161,462)
V163M=V(4,"enErr_Chat_GoldenEggs_NotEnough",162,463)
V164M=V(4,"enErr_Lottery_NoFree",163,480)
V165M=V(4,"enErr_Lottery_NoTicket",164,481)
V166M=V(4,"enErr_Lottery_NoMoney",165,482)
V167M=V(4,"enErr_Lottery_ReachMax",166,483)
V168M=V(4,"enErr_Lottery_NotOpen",167,484)
V169M=V(4,"enErr_Lottery_Fail",168,485)
V170M=V(4,"enErr_Lottery_NewHero_SGained",169,486)
V171M=V(4,"enErr_Lottery_NewHero_SPGained",170,487)
V172M=V(4,"enErr_LotteryWish_NotOpen",171,490)
V173M=V(4,"enErr_LotteryWish_Fail",172,491)
V174M=V(4,"enErr_LotteryWish_NoFreeRefresh",173,492)
V175M=V(4,"enErr_LotteryWish_RefreshFail",174,493)
V176M=V(4,"enErr_Shop_NoRefreshCount",175,500)
V177M=V(4,"enErr_Shop_NoRefreshGoods",176,501)
V178M=V(4,"enErr_Shop_NoGoodItem",177,502)
V179M=V(4,"enErr_Shop_MaxLimit",178,503)
V180M=V(4,"enErr_Shop_NeedlessRefreshGoods",179,504)
V181M=V(4,"enErr_Shop_BuyFail",180,505)
V182M=V(4,"enErr_Shop_BuyZero",181,506)
V183M=V(4,"enErr_Shop_RefreshFail",182,507)
V184M=V(4,"enErr_TreasureRareShop_Awaken_Limit",183,508)
V185M=V(4,"enErr_Shop_BuyLimited",184,509)
V186M=V(4,"enErr_Arena_NotOpen",185,550)
V187M=V(4,"enErr_Arena_Level_NotEnough",186,551)
V188M=V(4,"enErr_Arena_Ticket_NotEnough",187,552)
V189M=V(4,"enErr_Arena_Already_InTeam",188,553)
V190M=V(4,"enErr_Arena_NotCaptain",189,554)
V191M=V(4,"enErr_Arena_HasEnrolled",190,555)
V192M=V(4,"enErr_Arena_IsCaptain",191,556)
V193M=V(4,"enErr_Arena_ReachTeamMemberUL",192,557)
V194M=V(4,"enErr_Arena_TeamMember_NotEnough",193,558)
V195M=V(4,"enErr_Arena_VitalityPoint_NotEnough",194,559)
V196M=V(4,"enErr_Arena_TeamName_Exist",195,560)
V197M=V(4,"enErr_Arena_TeamDismissed",196,561)
V198M=V(4,"enErr_Arena_NotMeetCELL",197,562)
V199M=V(4,"enErr_Arena_PassStage_NotSatisfy",198,563)
V200M=V(4,"enErr_Arena_ChallengeFail",199,564)
V201M=V(4,"enErr_Arena_LoadBattleRecordFail",200,565)
V202M=V(4,"enErr_Arena_LoadRankingListFail",201,566)
V203M=V(4,"enErr_Arena_LoadDefenceLineupFail",202,567)
V204M=V(4,"enErr_Arena_EnterArenaFail",203,568)
V205M=V(4,"enErr_Arena_LoadRivalListFail",204,569)
V206M=V(4,"enErr_Arena_Not_InTeam",205,570)
V207M=V(4,"enErr_Arena_NoTeam",206,571)
V208M=V(4,"enErr_Arena_Got_Level_Reward",207,572)
V209M=V(4,"enErr_Arena_Btn_Still_Cooling",208,573)
V210M=V(4,"enErr_Arena_Btn_Not_Cooling",209,574)
V211M=V(4,"enErr_Arena_Victory_Num_NotEnough",210,575)
V212M=V(4,"enErr_Arena_Have_NO5_Victory_Reward",211,576)
V213M=V(4,"enErr_Arena_Stage_NotEnough",212,577)
V214M=V(4,"enErr_Arena_Buy_Ticket_Num_Max",213,578)
V215M=V(4,"enErr_Tavern_TaskNotFound",214,600)
V216M=V(4,"enErr_Tavern_NotFinished",215,601)
V217M=V(4,"enErr_Tavern_NotProcessing",216,602)
V218M=V(4,"enErr_Tavern_NoNeedRefresh",217,603)
V219M=V(4,"enErr_Tavern_AlterStarted",218,604)
V220M=V(4,"enErr_Tavern_HeroNotFound",219,605)
V221M=V(4,"enErr_Tavern_HeroConditionMismatch",220,606)
V222M=V(4,"enErr_Tavern_TaskMaxLimit",221,607)
V223M=V(4,"enErr_Tavern_HeroNotEnough",222,608)
V224M=V(4,"enErr_Tavern_HeroLowStar",223,609)
V225M=V(4,"enErr_Tavern_HeroTypeNotFit",224,610)
V226M=V(4,"enErr_Tavern_HeroProfessionNotFit",225,611)
V227M=V(4,"enErr_Tavern_HeroUsed",226,612)
V228M=V(4,"enErr_Tavern_CreateTaskFail",227,613)
V229M=V(4,"enErr_Tavern_RefreshFail",228,614)
V230M=V(4,"enErr_Tavern_RewardFail",229,615)
V231M=V(4,"enErr_Tavern_SpeedupFail",230,616)
V232M=V(4,"enErr_Tavern_StartFail",231,617)
V233M=V(4,"enErr_Tavern_Max",232,620)
V234M=V(4,"enErr_Hero_Transform_Forbiddent",233,630)
V235M=V(4,"enErr_Hero_Transform_InArena",234,631)
V236M=V(4,"enErr_Hero_Transform_Locked",235,632)
V237M=V(4,"enErr_Hero_Transform_Unprocessed",236,633)
V238M=V(4,"enErr_Hero_Transform_InAdvanceArena",237,634)
V239M=V(4,"enErr_Hero_Transform_DiffSid",238,640)
V240M=V(4,"enErr_Hero_Transform_NoProcess",239,641)
V241M=V(4,"enErr_Hero_Transform_DiffType",240,642)
V242M=V(4,"enErr_Hero_Transform_CreateHero",241,643)
V243M=V(4,"enErr_Hero_Transform_Fail",242,644)
V244M=V(4,"enErr_Face_Same",243,700)
V245M=V(4,"enErr_Role_Name_Exist",244,701)
V246M=V(4,"enErr_Role_FaceProp_NotWear",245,702)
V247M=V(4,"enErr_Role_Key_Words",246,703)
V248M=V(4,"enErr_Role_Unload_Face_Prop_Fail",247,704)
V249M=V(4,"enErr_Role_Update_Face_Prop_Fail",248,705)
V250M=V(4,"enErr_Role_Update_Face_Fail",249,706)
V251M=V(4,"enErr_Role_Update_Name_Fail",250,707)
V252M=V(4,"enErr_Role_Face_Image_Verify",251,708)
V253M=V(4,"enErr_Role_Face_Image_Verify_Fail",252,709)
V254M=V(4,"enErr_Face_Max",253,800)
V255M=V(4,"enErr_Trial_Team_Locked",254,801)
V256M=V(4,"enErr_Trial_NotEnoughEnergy",255,802)
V257M=V(4,"enErr_Trial_NoHero",256,803)
V258M=V(4,"enErr_Trial_LevelNotPass",257,804)
V259M=V(4,"enErr_Trial_MaxLevel",258,805)
V260M=V(4,"enErr_Trial_MoveOutOfVision",259,806)
V261M=V(4,"enErr_Trial_NoTeam",260,807)
V262M=V(4,"enErr_Trial_TeamNotFound",261,808)
V263M=V(4,"enErr_Trial_NoLevel",262,809)
V264M=V(4,"enErr_Trial_SetTeam_HeroMaxLimit",263,820)
V265M=V(4,"enErr_Trial_SetTeam_HeroUsed",264,821)
V266M=V(4,"enErr_Trial_SetTeam_PlaceUsed",265,822)
V267M=V(4,"enErr_Trial_SetTeam_PlaceIllegal",266,823)
V268M=V(4,"enErr_Trial_SetTeam_Locked",267,824)
V269M=V(4,"enErr_Trial_SetTeam_LowLevel",268,825)
V270M=V(4,"enErr_Trial_SetTeam_LowStar",269,826)
V271M=V(4,"enErr_Trial_Move_Block",270,829)
V272M=V(4,"enErr_Trial_Move_PosOccupy",271,830)
V273M=V(4,"enErr_Trial_Event_NotExist",272,831)
V274M=V(4,"enErr_Trial_Event_NotActive",273,832)
V275M=V(4,"enErr_Trial_Event_TooFar",274,833)
V276M=V(4,"enErr_Trial_Move_PathError",275,834)
V277M=V(4,"enErr_Trial_Event_ErrorType",276,835)
V278M=V(4,"enErr_Trial_Max",277,900)
V279M=V(4,"enErr_AshDungeon_NoReachOpenLevel",278,901)
V280M=V(4,"enErr_AshDungeon_haveSaved",279,902)
V281M=V(4,"enErr_AshDungeon_NoFindMedicineID",280,903)
V282M=V(4,"enErr_AshDungeon_NoEnoughMedicineNum",281,904)
V283M=V(4,"enErr_AshDungeon_NoFindHero",282,905)
V284M=V(4,"enErr_AshDungeon_HeroDie",283,906)
V285M=V(4,"enErr_AshDungeon_errShopSubType",284,907)
V286M=V(4,"enErr_AshDungeon_NoFindGoodsID",285,908)
V287M=V(4,"enErr_AshDungeon_NoEnoughGoodsNum",286,910)
V288M=V(4,"enErr_AshDungeon_NoOpen",287,911)
V289M=V(4,"enErr_AshDungeon_NoSweepChance",288,912)
V290M=V(4,"enErr_AshDungeon_NoSweepMap",289,913)
V291M=V(4,"enErr_AshDungeon_AllPass",290,914)
V292M=V(4,"enErr_AshDungeon_SelMedicine",291,915)
V293M=V(4,"enErr_AshDungeon_MapRewardLock",292,916)
V294M=V(4,"enErr_AshDungeon_NoReset",293,917)
V295M=V(4,"enErr_AshDungeon_Max",294,966)
V296M=V(4,"enErr_LeagueComp_NoOpen",295,970)
V297M=V(4,"enErr_LeagueComp_NoJoinLeague",296,971)
V298M=V(4,"enErr_LeagueComp_NoFindPlayBackData",297,972)
V299M=V(4,"enErr_LeagueComp_NoFindPlayer",298,973)
V300M=V(4,"enErr_LeagueComp_NoEnoughHp",299,974)
V301M=V(4,"enErr_LeagueComp_NoEnoughEnergy",300,975)
V302M=V(4,"enErr_LeagueComp_NoFindRecord",301,976)
V303M=V(4,"enErr_LeagueComp_IsMoving",302,977)
V304M=V(4,"enErr_LeagueComp_NoAdjacentCell",303,978)
V305M=V(4,"enErr_LeagueComp_NoEnoughCellDefendNum",304,979)
V306M=V(4,"enErr_LeagueComp_NoJoinScene",305,980)
V307M=V(4,"enErr_LeagueComp_ForbidBattle",306,981)
V308M=V(4,"enErr_LeagueComp_NoOpenSpecial",307,982)
V309M=V(4,"enErr_LeagueComp_NoSaveLineUp",308,983)
V310M=V(4,"enErr_LeagueComp_NoLeagueOfficial",309,984)
V311M=V(4,"enErr_LeagueComp_InvalidPos",310,985)
V312M=V(4,"enErr_LeagueComp_InvalidCellType",311,986)
V313M=V(4,"enErr_LeagueComp_NoFindLeague",312,987)
V314M=V(4,"enErr_LeagueComp_FailAddScene",313,988)
V315M=V(4,"enErr_LeagueComp_NotFortressNearby",314,989)
V316M=V(4,"enErr_LeagueComp_CellBattleing",315,990)
V317M=V(4,"enErr_LeagueComp_InvalidTaskID",316,991)
V318M=V(4,"enErr_LeagueComp_HavingGain",317,992)
V319M=V(4,"enErr_LeagueComp_GainConditionFail",318,993)
V320M=V(4,"enErr_LeagueComp_Max",319,1020)
V321M=V(4,"enErr_League_NotExist",320,1100)
V322M=V(4,"enErr_League_InLeague",321,1101)
V323M=V(4,"enErr_League_ApplyFull",322,1102)
V324M=V(4,"enErr_League_Outnumber",323,1103)
V325M=V(4,"enErr_League_NotInLeague",324,1104)
V326M=V(4,"enErr_League_NoMember",325,1105)
V327M=V(4,"enErr_League_NotOfficer",326,1106)
V328M=V(4,"enErr_League_ExpelOfficer",327,1107)
V329M=V(4,"enErr_League_ExpelCountMax",328,1108)
V330M=V(4,"enErr_League_NoApply",329,1109)
V331M=V(4,"enErr_League_ApplyInLeague",330,1110)
V332M=V(4,"enErr_League_MemberFull",331,1112)
V333M=V(4,"enErr_League_MailCountMax",332,1113)
V334M=V(4,"enErr_League_MailNoTarget",333,1114)
V335M=V(4,"enErr_League_ModifyType",334,1115)
V336M=V(4,"enErr_League_AlreadyOfficer",335,1116)
V337M=V(4,"enErr_League_TransferCountMax",336,1117)
V338M=V(4,"enErr_League_ModifyNameTooLong",337,1118)
V339M=V(4,"enErr_League_ModifyNameTooShort",338,1119)
V340M=V(4,"enErr_League_TransferNoLeague",339,1120)
V341M=V(4,"enErr_League_AnnounceTooLong",340,1121)
V342M=V(4,"enErr_League_TransferTimeOut",341,1122)
V343M=V(4,"enErr_LeagueBoss_noCount",342,1123)
V344M=V(4,"enErr_LeagueBoss_BossDead",343,1124)
V345M=V(4,"enErr_LeagueBoss_ReqDuplicate",344,1125)
V346M=V(4,"enErr_LeagueBoss_NotFound",345,1126)
V347M=V(4,"enErr_LeagueBoss_TimeOut",346,1127)
V348M=V(4,"enErr_League_SignCountDown",347,1128)
V349M=V(4,"enErr_LeagueBoss_LvLimit",348,1129)
V350M=V(4,"enErr_League_Name_Duplicate",349,1130)
V351M=V(4,"enErr_League_Name_CenterError",350,1131)
V352M=V(4,"enErr_League_Name_GameError",351,1132)
V353M=V(4,"enErr_League_Name_Wait",352,1133)
V354M=V(4,"enErr_League_Name_NoFree",353,1134)
V355M=V(4,"enErr_LeagueBoss_Error",354,1135)
V356M=V(4,"enErr_League_NoRoleInfo",355,1136)
V357M=V(4,"enErr_League_TransferOnMatch",356,1137)
V358M=V(4,"enErr_League_TransferFail",357,1138)
V359M=V(4,"enErr_LeagueBoss_GainFail",358,1139)
V360M=V(4,"enErr_League_Apply_Reject",359,1140)
V361M=V(4,"enErr_League_Apply_LvLimit",360,1141)
V362M=V(4,"enErr_League_Apply_CeLimit",361,1142)
V363M=V(4,"enErr_League_Apply_CD",362,1143)
V364M=V(4,"enErr_League_Apply_AlreadyIn",363,1144)
V365M=V(4,"enErr_League_Create_CD",364,1145)
V366M=V(4,"enErr_League_CEO_Exit",365,1146)
V367M=V(4,"enErr_League_Officier_Limit",366,1147)
V368M=V(4,"enErr_League_CEO_Offline_Intime",367,1148)
V369M=V(4,"enErr_League_Impeach_CD",368,1149)
V370M=V(4,"enErr_BuyChallengeBadgesUseUp",369,1150)
V371M=V(4,"enErr_ActivityParamAbnormal",370,1151)
V372M=V(4,"enErr_ActivityCfgNoExist",371,1152)
V373M=V(4,"enErr_ActivityTypeAbnormal",372,1153)
V374M=V(4,"enErr_ActivityValueAbnormal",373,1154)
V375M=V(4,"enErr_ActivityCompleteOverstep",374,1155)
V376M=V(4,"enErr_ActivityHeroNumError",375,1156)
V377M=V(4,"enErr_ActivityHeroPropError",376,1157)
V378M=V(4,"enErr_ActivityHeroInArena",377,1158)
V379M=V(4,"enErr_ActivityCfgError",378,1159)
V380M=V(4,"enErr_ActrivityEquipNumError",379,1160)
V381M=V(4,"enErr_ActrivityEquipPropErr",380,1161)
V382M=V(4,"enErr_ActrivityEquipDesFail",381,1162)
V383M=V(4,"enErr_ActivityNoNeedReceive",382,1163)
V384M=V(4,"enErr_ActivityHaveReceive",383,1164)
V385M=V(4,"enErr_ActivityHeroInAdvanceArena",384,1165)
V386M=V(4,"enErr_AwakenRoadConfirm",385,1166)
V387M=V(4,"enErr_AwakenRoadNotConfirm",386,1167)
V388M=V(4,"enErr_ActivityNotOpen",387,1168)
V389M=V(4,"enErr_LeagueTreasure_FlowerNotEnough",388,1200)
V390M=V(4,"enErr_LeagueTreasure_OutSendFlowerLimit",389,1201)
V391M=V(4,"enErr_LeagueTreasure_OutSendPlayerLimit",390,1202)
V392M=V(4,"enErr_leagueTreasure_BossHaveDied",391,1203)
V393M=V(4,"enErr_League_Impeach_OverTime",392,1230)
V394M=V(4,"enErr_League_Dismiss_Member_TooMuch",393,1231)
V395M=V(4,"enErr_League_Apply_TooMuch",394,1232)
V396M=V(4,"enErr_League_Impeaching",395,1233)
V397M=V(4,"enErr_League_Max",396,1300)
V398M=V(4,"enErr_Halo_Retuen_Gold_Fail",397,1301)
V399M=V(4,"enErr_Halo_Retuen_Goods_Fail",398,1302)
V400M=V(4,"enErr_Halo_Restore_Fail",399,1303)
V401M=V(4,"enErr_Halo_Upgrade_Fail",400,1304)
V402M=V(4,"enErr_Halo_UseGoods_Fail",401,1305)
V403M=V(4,"enErr_Halo_Max",402,1400)
V404M=V(4,"enErr_CompeteBattle_RivalNotExist",403,1401)
V405M=V(4,"enErr_CompeteBattle_RivalNoTroop",404,1402)
V406M=V(4,"enErr_CompeteBattle_ExportTroop",405,1403)
V407M=V(4,"enErr_CompeteBattle_ExportTroopOther",406,1404)
V408M=V(4,"enErr_CompeteBattle_Self",407,1405)
V409M=V(4,"enErr_CompeteBattle_RequireHero",408,1406)
V410M=V(4,"enErr_CompeteBattle_Max",409,1450)
V411M=V(4,"enErr_Tower_Video_Nonentiry",410,1451)
V412M=V(4,"enErr_Tower_SweepFail",411,1452)
V413M=V(4,"enErr_Tower_NoEnoughSweeps",412,1453)
V414M=V(4,"enErr_Tower_SweepStageErr",413,1454)
V415M=V(4,"enErr_Tower_SweepRewardFail",414,1456)
V416M=V(4,"enErr_Farm_Soil_Lock",415,1500)
V417M=V(4,"enErr_Farm_Soil_Not_Botany",416,1501)
V418M=V(4,"enErr_Farm_Soil_Has_Botany",417,1502)
V419M=V(4,"enErr_Farm_Soil_MainLv_NotEnough",418,1503)
V420M=V(4,"enErr_Farm_Soil_Botany_Plant_Max",419,1504)
V421M=V(4,"enErr_Weapon_Pre_Cond_NotEnough",420,1505)
V422M=V(4,"enErr_Weapon_Was_Used",421,1506)
V423M=V(4,"enErr_Weapon_No_Wear",422,1507)
V424M=V(4,"enErr_Weapon_No_Active",423,1508)
V425M=V(4,"enErr_Farm_Plant_Fail",424,1509)
V426M=V(4,"enErr_Farm_Collect_Fail",425,1510)
V427M=V(4,"enErr_Farm_Dismantle_Fail",426,1511)
V428M=V(4,"enErr_Farm_Main_Upgrade_Fail",427,1512)
V429M=V(4,"enErr_Farm_Botany_Upgrade_Fail",428,1513)
V430M=V(4,"enErr_Weapon_Active_Fail",429,1514)
V431M=V(4,"enErr_Weapon_Choose_Fail",430,1515)
V432M=V(4,"enErr_Weapon_Dismantle_Fail",431,1516)
V433M=V(4,"enErr_Weapon_Upgrade_Fail",432,1517)
V434M=V(4,"enErr_Farm_Cond_NotEnough",433,1550)
V435M=V(4,"enErr_Mate_SelfHasMate",434,1600)
V436M=V(4,"enErr_Mate_TargetHasMate",435,1601)
V437M=V(4,"enErr_Mate_TargetApplyMax",436,1602)
V438M=V(4,"enErr_Mate_ApplySelf",437,1603)
V439M=V(4,"enErr_Mate_LoadTargetRole",438,1604)
V440M=V(4,"enErr_Mate_CenterCreate",439,1605)
V441M=V(4,"enErr_Mate_GameCreate",440,1606)
V442M=V(4,"enErr_Mate_NoMate",441,1607)
V443M=V(4,"enErr_Mate_NoFoundMate",442,1608)
V444M=V(4,"enErr_Mate_NoFoundTarget",443,1609)
V445M=V(4,"enErr_Mate_NoFoundSelf",444,1610)
V446M=V(4,"enErr_Mate_RoleNotExist",445,1611)
V447M=V(4,"enErr_Mate_NoApply",446,1612)
V448M=V(4,"enErr_Mate_LogReward_NotExist",447,1613)
V449M=V(4,"enErr_Mate_LogReward_RoleNotMatch",448,1614)
V450M=V(4,"enErr_Mate_LogReward_Already",449,1615)
V451M=V(4,"enErr_Mate_NotReady",450,1616)
V452M=V(4,"enErr_Mate_Water_ErrorPos",451,1617)
V453M=V(4,"enErr_Mate_Water_NoTree",452,1618)
V454M=V(4,"enErr_Mate_Monster_NotTarget",453,1619)
V455M=V(4,"enErr_Mate_Monster_NotExist",454,1620)
V456M=V(4,"enErr_Mate_Monster_NoEnergy",455,1621)
V457M=V(4,"enErr_Mate_GameModifyRelation",456,1622)
V458M=V(4,"enErr_Mate_GameDB_EndFail",457,1623)
V459M=V(4,"enErr_Mate_HasTargetApply",458,1624)
V460M=V(4,"enErr_Mate_Max",459,1690)
V461M=V(4,"enErr_Achievement_NoCondition",460,1700)
V462M=V(4,"enErr_Achievement_HaveAward",461,1701)
V463M=V(4,"enErr_Achievement_StageNoCondition",462,1702)
V464M=V(4,"enErr_ExistsInArenaLineUp",463,1750)
V465M=V(4,"enErr_HeroNoExists",464,1751)
V466M=V(4,"enErr_HeroStarOverTop",465,1752)
V467M=V(4,"enErr_HeroResetNoDiamond",466,1753)
V468M=V(4,"enErr_HeroResetFail",467,1754)
V469M=V(4,"enErr_HeroGoBackNoEnoughNum",468,1755)
V470M=V(4,"enErr_HeroGoBackForbidMaxStar",469,1756)
V471M=V(4,"enErr_HeroGoBackNoDiamond",470,1757)
V472M=V(4,"enErr_HeroGoBackNoConfig",471,1758)
V473M=V(4,"enErr_HeroGoBackFail",472,1759)
V474M=V(4,"enErr_HeroGoBackNoSpace",473,1760)
V475M=V(4,"enErr_HeroGoBackLowStar",474,1761)
V476M=V(4,"enErr_ExistsInAdvanceArenaLineUp",475,1762)
V477M=V(4,"enErr_HeroDestoryFail",476,1763)
V478M=V(4,"enErr_HeroDestoryStar",477,1764)
V479M=V(4,"enErr_HeroDestoryRarity",478,1765)
V480M=V(4,"enErr_HeroDestoryNoHigherStar",479,1766)
V481M=V(4,"enErr_HeroDestoryLowIdleStage",480,1767)
V482M=V(4,"enErr_HeroInLeagueWarBattle",481,1768)
V483M=V(4,"enErr_TargetTask_NotFound",482,1780)
V484M=V(4,"enErr_TargetTask_Close",483,1781)
V485M=V(4,"enErr_TargetTask_NotFinished",484,1782)
V486M=V(4,"enErr_TargetTask_AlreadyReward",485,1783)
V487M=V(4,"enErr_TargetTask_NotOpen",486,1784)
V488M=V(4,"enErr_SevenDayActivity_InvalidTime",487,1801)
V489M=V(4,"enErr_SevenDayActivity_HavingGain",488,1802)
V490M=V(4,"enErr_SevenDayActivity_Max",489,1820)
V491M=V(4,"enErr_SubsequentReward_TaskNonentity",490,1821)
V492M=V(4,"enErr_SubsequentReward_StageError",491,1822)
V493M=V(4,"enErr_SubsequentReward_CfgBbnormal",492,1823)
V494M=V(4,"enErr_SubsequentReward_RewardCfgErr",493,1824)
V495M=V(4,"enErr_SubsequentReward_RewardHasGet",494,1825)
V496M=V(4,"enErr_SubsequentReward_Max",495,1840)
V497M=V(4,"enErr_SevenDayChallenge_InvalidTime",496,1841)
V498M=V(4,"enErr_SevenDayChallenge_HavingGain",497,1842)
V499M=V(4,"enErr_SevenDayChallenge_InvalidTaskID",498,1843)
V500M=V(4,"enErr_SevenDayChallenge_NotEnoughIntergal",499,1844)
V501M=V(4,"enErr_SevenDayChallenge_BuyLimit",500,1845)
V502M=V(4,"enErr_SevenDayChallenge_NoSpace",501,1846)
V503M=V(4,"enErr_SevenDayChallenge_Max",502,1860)
V504M=V(4,"enErr_RedeemCode_NotEligibleForActivation",503,1861)
V505M=V(4,"enErr_RedeemCode_CardIsInvalidOrBanned",504,1862)
V506M=V(4,"enErr_RedeemCode_CardIsInvalidOrHaveUsed",505,1863)
V507M=V(4,"enErr_RedeemCode_CardIsInvalidOrObsolete",506,1864)
V508M=V(4,"enErr_RedeemCode_GameWorldBanThisCard",507,1865)
V509M=V(4,"enErr_RedeemCode_ActivationCodeNotEnabled",508,1866)
V510M=V(4,"enErr_RedeemCode_CardHavePassDue",509,1867)
V511M=V(4,"enErr_RedeemCode_CardTimeNotOpen",510,1868)
V512M=V(4,"enErr_RedeemCode_ChannelErrors",511,1869)
V513M=V(4,"enErr_RedeemCode_QuantityUpToCeiling",512,1870)
V514M=V(4,"enErr_RedeemCode_HaveBeenActivated",513,1871)
V515M=V(4,"enErr_RedeemCode_Max",514,1880)
V516M=V(4,"enErr_QIdle_LVLessOpenLv",515,1881)
V517M=V(4,"enErr_QIdle_NoRemainBuyTimes",516,1882)
V518M=V(4,"enErr_QIdle_NoQBatleTimes",517,1883)
V519M=V(4,"enErr_QIdle_BuyBattleFail",518,1884)
V520M=V(4,"enErr_QIdle_MAX",519,1900)
V521M=V(4,"enErr_Mill_OrderTooMuch",520,1901)
V522M=V(4,"enErr_Mill_CanNotFindOrder",521,1902)
V523M=V(4,"enErr_Mill_OrderHasBegun",522,1903)
V524M=V(4,"enErr_Mill_CfgAbnormal",523,1904)
V525M=V(4,"enErr_Mill_MaxLv",524,1905)
V526M=V(4,"enErr_Mill_GetRewardFail",525,1906)
V527M=V(4,"enErr_Mill_Max",526,1910)
V528M=V(4,"enErr_Tech_SkillUpgradeFail",527,1911)
V529M=V(4,"enErr_Tech_SkillCfgErr",528,1912)
V530M=V(4,"enErr_Tech_FrontSkillLess",529,1913)
V531M=V(4,"enErr_Tech_NoEnoughRes",530,1914)
V532M=V(4,"enErr_Tech_MAx",531,1930)
V533M=V(4,"enErr_Maze_Lock",532,1931)
V534M=V(4,"enErr_RankAchv_InvalidTaskID",533,2001)
V535M=V(4,"enErr_RankAchv_InvalidParam",534,2002)
V536M=V(4,"enErr_RankAchv_NoReachOpenLv",535,2003)
V537M=V(4,"enErr_RankAchv_Max",536,2030)
V538M=V(4,"enErr_Triumphant_RankTypeErr",537,2031)
V539M=V(4,"enErr_Triumphant_CampNoRank",538,2032)
V540M=V(4,"enErr_Passport_InvalidType",539,2050)
V541M=V(4,"enErr_Passport_InvalidID",540,2051)
V542M=V(4,"enErr_Passport_NoReach",541,2052)
V543M=V(4,"enErr_Passport_HavingGain",542,2053)
V544M=V(4,"enErr_Passport_NoOpen",543,2054)
V545M=V(4,"enErr_PassPort_AreadyBuy",544,2055)
V546M=V(4,"enErr_Passport_Max",545,2070)
V547M=V(4,"enErr_KQFriend_toFriendTooMuch",546,2071)
V548M=V(4,"enErr_KQFriend_fromFriendTooMuch",547,2072)
V549M=V(4,"enErr_KQFriend_repeatAdd",548,2073)
V550M=V(4,"enErr_KQFriend_notInBlock",549,2074)
V551M=V(4,"enErr_KQFriend_alreadyInBlock",550,2075)
V552M=V(4,"enErr_KQFriend_gainTooMuch",551,2076)
V553M=V(4,"enErr_KQFriend_noStar",552,2077)
V554M=V(4,"enErr_KQFriend_giveTooMuch",553,2078)
V555M=V(4,"enErr_KQFriend_alreadyGived",554,2079)
V556M=V(4,"enErr_KQFriend_notFriend",555,2080)
V557M=V(4,"enErr_KQFriend_acceptFailed",556,2081)
V558M=V(4,"enErr_KQFriend_dbidNoExist",557,2082)
V559M=V(4,"enErr_KQFriend_addSelf",558,2083)
V560M=V(4,"enErr_KQFriend_starTooMuch",559,2084)
V561M=V(4,"enErr_KQFriend_applyInBlock",560,2085)
V562M=V(4,"enErr_KQFriend_blockLTooMuch",561,2086)
V563M=V(4,"enErr_KQFriend_oppositeInBlock",562,2087)
V564M=V(4,"enErr_KQFriend_selfInBlock",563,2088)
V565M=V(4,"enErr_KQFriend_Max",564,2100)
V566M=V(4,"enErr_PalSelectCard_GoodsNoExist",565,2101)
V567M=V(4,"enErr_PalSelectCard_GoodsNotPalCard",566,2102)
V568M=V(4,"enErr_PalSelectCard_GoodsNotEnough",567,2103)
V569M=V(4,"enErr_PalSelectCard_CfgError",568,2104)
V570M=V(4,"enErr_PalSelectCard_ReduceGoodsFail",569,2105)
V571M=V(4,"enErr_PalSelectCard_RewardIDNotExist",570,2106)
V572M=V(4,"enErr_PalSelectCard_GoodsNotCampCard",571,2107)
V573M=V(4,"enErr_PalSelectCard_CampError",572,2108)
V574M=V(4,"enErr_PalSelectCard_CampCfgError",573,2109)
V575M=V(4,"enErr_PalSelectCard_CampCardUseFail",574,2110)
V576M=V(4,"enErr_PalSelectCard_PalPacketNotEnough",575,2111)
V577M=V(4,"enErr_PalSelectCard_PalPacketHeroLimit",576,2112)
V578M=V(4,"enErr_PalSelectCard_Max",577,2130)
V579M=V(4,"enErr_NewRedeemCode_Inexistence",578,2131)
V580M=V(4,"enErr_NewRedeemCode_HaveBeenUsed",579,2132)
V581M=V(4,"enErr_NewRedeemCode_Invalid",580,2133)
V582M=V(4,"enErr_NewRedeemCode_NoTurnedOn",581,2134)
V583M=V(4,"enErr_NewRedeemCode_HaveVioded",582,2135)
V584M=V(4,"enErr_NewRedeemCode_HaveNotStarted",583,2136)
V585M=V(4,"enErr_NewRedeemCode_DiffAccount",584,2137)
V586M=V(4,"enErr_NewRedeemCode_DiffPhone",585,2138)
V587M=V(4,"enErr_NewRedeemCode_MoneyNotEnough",586,2139)
V588M=V(4,"enErr_NewRedeemCode_LvNotEnough",587,2140)
V589M=V(4,"enErr_NewRedeemCode_ZoneNotAllow",588,2141)
V590M=V(4,"enErr_NewRedeemCode_ChannelNotAllow",589,2142)
V591M=V(4,"enErr_NewRedeemCode_VersonNotAllow",590,2143)
V592M=V(4,"enErr_NewRedeemCode_TimePayNotEnough",591,2144)
V593M=V(4,"enErr_NewRedeemCode_AccountHaveActivity",592,2145)
V594M=V(4,"enErr_NewRedeemCode_RoleHaveActivity",593,2146)
V595M=V(4,"enErr_NewRedeemCode_HaveAtyOrHaveUsed",594,2147)
V596M=V(4,"enErr_NewRedeemCode_Max",595,2170)
V597M=V(4,"enErr_SoulLinkModule_AddSlotFail",596,2171)
V598M=V(4,"enErr_SoulLinkModule_ModuleClose",597,2172)
V599M=V(4,"enErr_SoulLinkModule_SlotExceedTheLimit",598,2173)
V600M=V(4,"enErr_SoulLinkModule_AddSlotLinkFail",599,2174)
V601M=V(4,"enErr_SoulLinkModule_SlotError",600,2175)
V602M=V(4,"enErr_SoulLinkModule_SetInPalFail",601,2176)
V603M=V(4,"enErr_SoulLinkModule_FlamenSetSlot",602,2177)
V604M=V(4,"enErr_SoulLinkModule_PalInSlot",603,2178)
V605M=V(4,"enErr_SoulLinkModule_SlotCool",604,2179)
V606M=V(4,"enErr_SoulLinkModule_SlotHasPal",605,2180)
V607M=V(4,"enErr_SoulLinkModule_PalNotInSlot",606,2181)
V608M=V(4,"enErr_SoulLinkModule_SlotNumError",607,2182)
V609M=V(4,"enErr_SoulLinkModule_LessDiamond",608,2183)
V610M=V(4,"enErr_SoulLinkModule_NoInCool",609,2184)
V611M=V(4,"enErr_SoulLinkModule_OpFail",610,2185)
V612M=V(4,"enErr_SoulLinkModule_LessSoul",611,2186)
V613M=V(4,"enErr_SoulLinkModule_StepFailPalInSlot",612,2187)
V614M=V(4,"enErr_SoulLinkModule_LvFailPalInSlot",613,2188)
V615M=V(4,"enErr_SoulLinkModule_ResetFailInSlot",614,2189)
V616M=V(4,"enErr_SoulLinkModule_SoulMaxLv",615,2190)
V617M=V(4,"enErr_SoulLinkModule_SoulProgressLess",616,2191)
V618M=V(4,"enErr_SoulLinkModule_SoulLowLevel",617,2192)
V619M=V(4,"enErr_SoulLinkModule_SoulMaxUnlocked",618,2193)
V620M=V(4,"enErr_SoulLinkModule_SoulProgressFull",619,2194)
V621M=V(4,"enErr_SoulLinkModule_DestoryFailInSlot",620,2195)
V622M=V(4,"enErr_SoulLinkModule_Max",621,2200)
V623M=V(4,"enErr_ThinksGivingActivity_UseTurkeyErr",622,2201)
V624M=V(4,"enErr_ThinksGivingActivity_NoTurkey",623,2202)
V625M=V(4,"enErr_ThinksGivingActivity_GetSignInRewardFail",624,2203)
V626M=V(4,"enErr_ThinksGivingActivity_CashRewardFail",625,2204)
V627M=V(4,"enErr_ThinksGivingActivity_SignInDaysNotEnough",626,2205)
V628M=V(4,"enErr_ThinksGivingActivity_SignInRewardGetted",627,2206)
V629M=V(4,"enErr_ThinksGivingActivity_Max",628,2219)
V630M=V(4,"enErr_Frame_SelectFail",629,2220)
V631M=V(4,"enErr_Frame_Max",630,2240)
V632M=V(4,"enErr_UseTrialPhysicalAgent_Fail",631,2241)
V633M=V(4,"enErr_UseTrialPhysicalAgent_ItemLack",632,2242)
V634M=V(4,"enErr_UseTrialPhysicalAgent_Max",633,2260)
V635M=V(4,"enErr_Christmas_ActiveNotOpen",634,2261)
V636M=V(4,"enErr_Christmas_TreeLvLimit",635,2262)
V637M=V(4,"enErr_Christmas_NoCandy",636,2263)
V638M=V(4,"enErr_Christmas_RepeatCard",637,2264)
V639M=V(4,"enErr_Christmas_SendLimit",638,2265)
V640M=V(4,"enErr_Christmas_NoCard",639,2266)
V641M=V(4,"enErr_Christmas_NotActiveBlackCard",640,2267)
V642M=V(4,"enErr_Christmas_NoMoreReward",641,2268)
V643M=V(4,"enErr_Christmas_NumberRestriction",642,2269)
V644M=V(4,"enErr_Christmas_RewardGotBefore",643,2270)
V645M=V(4,"enErr_Christmas_Max",644,2280)
V646M=V(4,"enErr_UseSelectCustomBox_Fail",645,2281)
V647M=V(4,"enErr_UseSelectCustomBox_LackOfGoods",646,2282)
V648M=V(4,"enErr_SigilPacket_Full",647,2283)
V649M=V(4,"enErr_EquipPacket_Full",648,2284)
V650M=V(4,"enErr_TreasureRareStage_CannotUse",649,2285)
V651M=V(4,"enErr_DroneStarPacket_Full",650,2286)
V652M=V(4,"enErr_UseSelectCustomBox_Max",651,2300)
V653M=V(4,"enErr_Faction_LevelMax",652,2301)
V654M=V(4,"enErr_Faction_CountMax",653,2302)
V655M=V(4,"enErr_Faction_OffHours",654,2303)
V656M=V(4,"enErr_Faction_HeroType",655,2304)
V657M=V(4,"enErr_Faction_ProficientType",656,2305)
V658M=V(4,"enErr_Faction_ProOrSkillMaxLv",657,2306)
V659M=V(4,"enErr_Faction_LowStage",658,2307)
V660M=V(4,"enErr_Faction_DarkLowLevel",659,2308)
V661M=V(4,"enErr_Faction_Max",660,2400)
V662M=V(4,"enErr_SpaceDominator_Not_Open",661,2401)
V663M=V(4,"enErr_SpaceDominator_Not_End",662,2402)
V664M=V(4,"enErr_SpaceDominator_Not_Reward",663,2403)
V665M=V(4,"enErr_SpaceDominator_Have_Reward",664,2404)
V666M=V(4,"enErr_SpaceDominator_reopening",665,2405)
V667M=V(4,"enErr_SpaceDominator_Max",666,2500)
V668M=V(4,"enErr_ValentinesDay_DonateFail",667,2501)
V669M=V(4,"enErr_ValentinesDay_ShortageOfRoses",668,2502)
V670M=V(4,"enErr_ValentinesDay_Max",669,2520)
V671M=V(4,"enErr_LeagueHelp_CannotSeekHelp",670,2521)
V672M=V(4,"enErr_LeagueHelp_TodayReqIsMax",671,2522)
V673M=V(4,"enErr_LeagueHelp_HelpReqCooling",672,2523)
V674M=V(4,"enErr_LeagueHelp_TaskHaveComplete",673,2524)
V675M=V(4,"enErr_LeagueHelp_HelpFail",674,2525)
V676M=V(4,"enErr_LeagueHelp_DiffLeague",675,2526)
V677M=V(4,"enErr_LeagueHelp_TaskComplete",676,2527)
V678M=V(4,"enErr_LeagueHelp_RepetitiveHelp",677,2528)
V679M=V(4,"enErr_LeagueHelp_GetRewardFail",678,2529)
V680M=V(4,"enErr_LeagueHelp_NotInLeague",679,2530)
V681M=V(4,"enErr_LeagueHelp_ExceedCollLimit",680,2531)
V682M=V(4,"enErr_LeagueHelp_TaskUnfinished",681,2532)
V683M=V(4,"enErr_LeagueHelp_SCardSelNumLimit",682,2533)
V684M=V(4,"enErr_LeagueHelp_HelpIDNotInPool",683,2534)
V685M=V(4,"enErr_LeagueHelp_HelpIDHavingSel",684,2535)
V686M=V(4,"enErr_LeagueHelp_Max",685,2580)
V687M=V(4,"enErr_Peak_Lock",686,2601)
V688M=V(4,"enErr_Peak_LevelNotExist",687,2602)
V689M=V(4,"enErr_Peak_LevelLock",688,2603)
V690M=V(4,"enErr_Peak_EventNotExist",689,2604)
V691M=V(4,"enErr_Peak_EventTypeError",690,2605)
V692M=V(4,"enErr_Peak_HeroNotSel",691,2606)
V693M=V(4,"enErr_Peak_CarriageIndex",692,2607)
V694M=V(4,"enErr_Peak_HeroCreate",693,2608)
V695M=V(4,"enErr_Peak_BattleNoParam",694,2609)
V696M=V(4,"enErr_Peak_BattleNoHero",695,2610)
V697M=V(4,"enErr_Peak_BattleNoLiveHero",696,2611)
V698M=V(4,"enErr_Peak_BattleError",697,2612)
V699M=V(4,"enErr_Peak_SelectRelic",698,2613)
V700M=V(4,"enErr_Peak_NoSelectRelic",699,2614)
V701M=V(4,"enErr_Peak_SelRelicNotExist",700,2615)
V702M=V(4,"enErr_Peak_RelicNotExist",701,2616)
V703M=V(4,"enErr_Peak_AddRelicFail",702,2617)
V704M=V(4,"enErr_Peak_AlreadyGotReward",703,2618)
V705M=V(4,"enErr_Peak_RewardFail",704,2619)
V706M=V(4,"enErr_Peak_TooFar",705,2620)
V707M=V(4,"enErr_Peak_InteractMax",706,2621)
V708M=V(4,"enErr_Peak_InteractDisable",707,2622)
V709M=V(4,"enErr_Peak_LevelNotPlaying",708,2623)
V710M=V(4,"enErr_Peak_MapNotFound",709,2624)
V711M=V(4,"enErr_Peak_GridCantWalk",710,2625)
V712M=V(4,"enErr_Peak_GridHasEvent",711,2626)
V713M=V(4,"enErr_Peak_ErrorPosIndex",712,2627)
V714M=V(4,"enErr_Peak_LevelDiffPlaying",713,2628)
V715M=V(4,"enErr_Peak_FinishedLevel",714,2629)
V716M=V(4,"enErr_Peak_LevelCreateFail",715,2630)
V717M=V(4,"enErr_Peak_LevelResetFail",716,2631)
V718M=V(4,"enErr_Peak_RefreshConfigError",717,2632)
V719M=V(4,"enErr_Peak_GetBoxFail",718,2633)
V720M=V(4,"enErr_Peak_MapError",719,2634)
V721M=V(4,"enErr_Peak_SnowManNotMove",720,2635)
V722M=V(4,"enErr_Peak_SnowManAllArrive",721,2636)
V723M=V(4,"enErr_Peak_SnowManNotMoveTwenty",722,2637)
V724M=V(4,"enErr_Peak_BatteryTransmitFail",723,2638)
V725M=V(4,"enErr_Peak_MoveToPosFail",724,2639)
V726M=V(4,"enErr_Peak_MAX",725,2699)
V727M=V(4,"enErr_Championships_NotJoin",726,2700)
V728M=V(4,"enErr_Championships_GetRankFail",727,2701)
V729M=V(4,"enErr_Championships_RankRem",728,2702)
V730M=V(4,"enErr_Championships_GetRewardFail",729,2703)
V731M=V(4,"enErr_Championships_HaveGain",730,2704)
V732M=V(4,"enErr_Championships_ReqRankFre",731,2705)
V733M=V(4,"enErr_Championships_ReqPromoteFre",732,2706)
V734M=V(4,"enErr_Championships_GetRewardTimeOut",733,2707)
V735M=V(4,"enErr_Championships_Max",734,2750)
V736M=V(4,"enErr_EquipEctype_Lock",735,2751)
V737M=V(4,"enErr_EquipEctype_ScrollNotEnough",736,2752)
V738M=V(4,"enErr_EquipEctype_VIPLv",737,2753)
V739M=V(4,"enErr_IllusionTower_NotPass",738,2761)
V740M=V(4,"enErr_IllusionTower_Prized",739,2762)
V741M=V(4,"enErr_IllusionTower_Max",740,2770)
V742M=V(4,"enErr_EquipEctype_NotPass",741,2771)
V743M=V(4,"enErr_EquipEctype_Prized",742,2772)
V744M=V(4,"enErr_EquipEctype_MAX",743,2780)
V745M=V(4,"enErr_Activation_Not_Enough",744,2781)
V746M=V(4,"enErr_Activation_Prized",745,2782)
V747M=V(4,"enErr_Activation_MAX",746,2790)
V748M=V(4,"enErr_Farm_NoSpinReward",747,2791)
V749M=V(4,"enErr_Farm_NoEnoughRes",748,2792)
V750M=V(4,"enErr_Farm_NoFindFarm",749,2793)
V751M=V(4,"enErr_Farm_NoInAimList",750,2794)
V752M=V(4,"enErr_Farm_NoEnoughHammer",751,2795)
V753M=V(4,"enErr_Farm_AttackBuildFail",752,2796)
V754M=V(4,"enErr_Farm_NoMatchDigRole",753,2797)
V755M=V(4,"enErr_Farm_NoEnoughShovel",754,2798)
V756M=V(4,"enErr_Farm_DigFail",755,2799)
V757M=V(4,"enErr_Farm_NoLoadingFarm",756,2800)
V758M=V(4,"enErr_Farm_NoLoadingBaseInfo",757,2801)
V759M=V(4,"enErr_Farm_GamebleIDNoCfg",758,2802)
V760M=V(4,"enErr_Farm_AimListEmpty",759,2803)
V761M=V(4,"enErr_Farm_HavingGain",760,2804)
V762M=V(4,"enErr_Farm_NoEnoughScore",761,2805)
V763M=V(4,"enErr_Farm_VipLvNoReach",762,2806)
V764M=V(4,"enErr_Farm_Max",763,2820)
V765M=V(4,"enErr_EntertainmentCity_Close",764,2821)
V766M=V(4,"enErr_EntertainmentCity_LvNoCfg",765,2822)
V767M=V(4,"enErr_EntertainmentCity_SelectRewardNoExist",766,2823)
V768M=V(4,"enErr_EntertainmentCity_SelectRewardHasGetted",767,2824)
V769M=V(4,"enErr_EntertainmentCity_SelectRewardNotHero",768,2825)
V770M=V(4,"enErr_EntertainmentCity_SelectRewardRepeat",769,2826)
V771M=V(4,"enErr_EntertainmentCity_SelectRewardHeroLimit",770,2827)
V772M=V(4,"enErr_EntertainmentCity_NextRoundSplNotGet",771,2828)
V773M=V(4,"enErr_EntertainmentCity_NextRoundMaxRound",772,2829)
V774M=V(4,"enErr_EntertainmentCity_DivinationNoCfg",773,2830)
V775M=V(4,"enErr_EntertainmentCity_DivinationTimesErr",774,2831)
V776M=V(4,"enErr_EntertainmentCity_DivinationErr",775,2832)
V777M=V(4,"enErr_EntertainmentCity_DivinationDiamondIsInsufficient",776,2833)
V778M=V(4,"enErr_EntertainmentCity_DivinationComplete",777,2834)
V779M=V(4,"enErr_EntertainmentCity_DivinationCoinsIsInsufficient",778,2835)
V780M=V(4,"enErr_EntertainmentCity_DivinationCoinsBuyLimit",779,2836)
V781M=V(4,"enErr_EntertainmentCity_DivinationCoinsBuyDiamondLack",780,2837)
V782M=V(4,"enErr_EntertainmentCity_DivinationCoinsBuyFail",781,2838)
V783M=V(4,"enErr_EntertainmentCity_Max",782,2850)
V784M=V(4,"enErr_Playback_No_Exist",783,2851)
V785M=V(4,"enErr_Playback_Max",784,2860)
V786M=V(4,"enErr_VIP_PART_Lv_Reward_Fail",785,2861)
V787M=V(4,"enErr_VIP_PART_Lv_Reward_HasGetted",786,2862)
V788M=V(4,"enErr_VIP_PART_Lv_Reward_LvErr",787,2863)
V789M=V(4,"enErr_VIP_PART_BOX_HavingGetted",788,2864)
V790M=V(4,"enErr_VIP_PART_HavingExpired",789,2865)
V791M=V(4,"enErr_VIP_PART_Vip_NotEnough",790,2866)
V792M=V(4,"enErr_VIP_PART_Times_Limit",791,2867)
V793M=V(4,"enErr_VIP_PART_MAX",792,2880)
V794M=V(4,"enErr_LAB_Lack_Activity",793,2900)
V795M=V(4,"enErr_LAB_Is_Open",794,2901)
V796M=V(4,"enErr_LAB_Not_President",795,2902)
V797M=V(4,"enErr_LAB_Not_Join_Activity",796,2903)
V798M=V(4,"enErr_LAB_Activity_Not_End",797,2904)
V799M=V(4,"enErr_LAB_Have_Not_Reward",798,2905)
V800M=V(4,"enErr_LAB_Not_Open",799,2906)
V801M=V(4,"enErr_LAB_Max",800,3000)
V802M=V(4,"enErr_LeagueWar_JoinCDforReward",801,3001)
V803M=V(4,"enErr_LeagueWar_NoReward",802,3002)
V804M=V(4,"enErr_LeagueWar_NotApplyState",803,3003)
V805M=V(4,"enErr_LeagueWar_NotJoinLeague",804,3004)
V806M=V(4,"enErr_LeagueWar_ActivityNotEnough",805,3005)
V807M=V(4,"enErr_LeagueWar_JoinCDforApply",806,3006)
V808M=V(4,"enErr_LeagueWar_NotPrepareState",807,3007)
V809M=V(4,"enErr_LeagueWar_NotAttackState",808,3008)
V810M=V(4,"enErr_LeagueWar_NotApplied",809,3009)
V811M=V(4,"enErr_LeagueWar_AttackExhausted",810,3010)
V812M=V(4,"enErr_LeagueWar_UsedCard",811,3011)
V813M=V(4,"enErr_LeagueWar_NotRevived",812,3012)
V814M=V(4,"enErr_League_to_Apply_CD",813,3013)
V815M=V(4,"enErr_LeagueWar_BattleCD",814,3014)
V816M=V(4,"enErr_League_repeat_change_position",815,3015)
V817M=V(4,"enErr_League_Recruit_CD",816,3016)
V818M=V(4,"enErr_League_Diff_Segment",817,3017)
V819M=V(4,"enErr_LeagueBossWar_NotRewardState",818,3018)
V820M=V(4,"enErr_LeagueWar_Max",819,3100)
V821M=V(4,"enErr_Operative_Email_Invalid",820,3101)
V822M=V(4,"enErr_Operative_NotBindEmail",821,3102)
V823M=V(4,"enErr_Operative_Email_Prized",822,3103)
V824M=V(4,"enErr_Operative_Code_Pirzed",823,3104)
V825M=V(4,"enErr_Operative_Share_Pirzed",824,3105)
V826M=V(4,"enErr_Operative_Condition",825,3106)
V827M=V(4,"enErr_Operative_Max",826,3150)
V828M=V(4,"enErr_GoldHand_Buy_Max",827,3151)
V829M=V(4,"enErr_GoldHand_Max",828,3200)
V830M=V(4,"enErr_ChangePackage_NotBind",829,3201)
V831M=V(4,"enErr_ChangePackage_BindAccountRewardGetted",830,3202)
V832M=V(4,"enErr_ChangePackage_NoExchangeRecord",831,3203)
V833M=V(4,"enErr_ChangePackage_ExchangeRecordError",832,3204)
V834M=V(4,"enErr_ChangePackage_ExchangeRewardGetted",833,3205)
V835M=V(4,"enErr_ChangePackage_Max",834,3210)
V836M=V(4,"enErr_Hook_LowPower",835,3211)
V837M=V(4,"enErr_Hero_NotOpen",836,3221)
V838M=V(4,"enErr_Hero_InvalidParam",837,3222)
V839M=V(4,"enErr_Hero_ConfigError",838,3223)
V840M=V(4,"enErr_Hero_ItemNotEnough",839,3224)
V841M=V(4,"enErr_Hero_CountLimit",840,3225)
V842M=V(4,"enErr_Hero_LvError",841,3226)
V843M=V(4,"enErr_Hero_TaskCondError",842,3227)
V844M=V(4,"enErr_Hero_RemoveItemFailed",843,3228)
V845M=V(4,"enErr_Hero_PrizeGetted",844,3229)
V846M=V(4,"enErr_GlobalEvent_NotOpen",845,3231)
V847M=V(4,"enErr_GlobalEvent_Prized",846,3232)
V848M=V(4,"enErr_Hero_Max",847,3240)
V849M=V(4,"enErr_MergeServer_NotJoin",848,3250)
V850M=V(4,"enErr_MergeServer_Close",849,3251)
V851M=V(4,"enErr_MergeServer_GiftAlreadyGot",850,3252)
V852M=V(4,"enErr_MergeServer_Max",851,3300)
V853M=V(4,"enErr_LineUp_InvalidID",852,3301)
V854M=V(4,"enErr_LineUp_Limit",853,3302)
V855M=V(4,"enErr_LineUp_NoCancle",854,3303)
V856M=V(4,"enErr_LineUp_UpdateSuccess",855,3304)
V857M=V(4,"enErr_LineUp_Max",856,3310)
V858M=V(4,"enErr_ReturnAct_NoReward",857,3311)
V859M=V(4,"enErr_ReturnAct_NotOpen",858,3312)
V860M=V(4,"enErr_ReturnAct_Rewarded",859,3313)
V861M=V(4,"enErr_ReturnAct_Lock",860,3314)
V862M=V(4,"enErr_ReturnAct_Max",861,3350)
V863M=V(4,"enErr_JumpScore_HasGet",862,3351)
V864M=V(4,"enErr_JumpScore_Close",863,3352)
V865M=V(4,"enErr_JumpScore_Max",864,3360)
V866M=V(4,"enErr_VoidArean_Lock",865,3361)
V867M=V(4,"enErr_VoidArean_WrongUpgradeLv",866,3362)
V868M=V(4,"enErr_VoidArean_WrongID",867,3363)
V869M=V(4,"enErr_VoidArean_LowRank",868,3364)
V870M=V(4,"enErr_VoidArean_Max",869,3450)
V871M=V(4,"enErr_Turnable_Sucess",870,3451)
V872M=V(4,"enErr_Turnable_InvalidParam",871,3452)
V873M=V(4,"enErr_Turnable_ConfigError",872,3453)
V874M=V(4,"enErr_Turnable_Prized",873,3454)
V875M=V(4,"enErr_Turnable_NotOpen",874,3455)
V876M=V(4,"enErr_Turnable_CantRandomEffect",875,3456)
V877M=V(4,"enErr_Turnable_ItemNotEnough",876,3457)
V878M=V(4,"enErr_Turnable_Max",877,3460)
V879M=V(4,"enErr_MonthlyFund_Unactive",878,3461)
V880M=V(4,"enErr_MonthlyFund_Not_Reward",879,3462)
V881M=V(4,"enErr_MonthlyFund_Not_Open",880,3463)
V882M=V(4,"enErr_MonthlyFund_Reward_Fail",881,3464)
V883M=V(4,"enErr_MonthlyFund_Not_SuperMonthCard",882,3465)
V884M=V(4,"enErr_MonthlyFund_NormalFund_Activated",883,3466)
V885M=V(4,"enErr_MonthlyFund_LuxuryFund_Activated",884,3467)
V886M=V(4,"enErr_MonthlyFund_UnactiveEx",885,3468)
V887M=V(4,"enErr_MonthlyFund_Max",886,3470)
V888M=V(4,"enErr_TopRace_Close",887,3500)
V889M=V(4,"enErr_TopRace_NoArena",888,3501)
V890M=V(4,"enErr_TopRace_NoRoleInfo",889,3502)
V891M=V(4,"enErr_TopRace_NoTop18Pal",890,3503)
V892M=V(4,"enErr_TopRace_NoCurBattle",891,3504)
V893M=V(4,"enErr_TopRace_NoJoin",892,3505)
V894M=V(4,"enErr_TopRace_SettleNoResult",893,3506)
V895M=V(4,"enErr_TopRace_OutOf64",894,3507)
V896M=V(4,"enErr_TopRace_RankError",895,3508)
V897M=V(4,"enErr_TopRace_RankRewardHasGet",896,3509)
V898M=V(4,"enErr_TopRace_NoRank",897,3510)
V899M=V(4,"enErr_TopRace_VoteAfter23",898,3511)
V900M=V(4,"enErr_TopRace_VoteNoPush",899,3512)
V901M=V(4,"enErr_TopRace_VoteComplete",900,3513)
V902M=V(4,"enErr_TopRace_VoteCoinLess",901,3514)
V903M=V(4,"enErr_TopRace_VotePushDayMore7",902,3515)
V904M=V(4,"enErr_TopRace_OnTopRaceList",903,3516)
V905M=V(4,"enErr_TopRace_VoteOnBattle",904,3517)
V906M=V(4,"enErr_TopRace_OutOfSetLineUpTime",905,3518)
V907M=V(4,"enErr_TopRace_ApplaudFrequently",906,3519)
V908M=V(4,"enErr_TopRace_AlreadyLiked",907,3520)
V909M=V(4,"enErr_TopRace_ApplaudNoArenaID",908,3521)
V910M=V(4,"enErr_TopRace_ApplaudNotTop1",909,3522)
V911M=V(4,"enErr_TopRace_ApplaudNoReward",910,3523)
V912M=V(4,"enErr_TopRace_Pal_InLineup",911,3524)
V913M=V(4,"enErr_TopRace_CannotSaveTroop",912,3525)
V914M=V(4,"enErr_TopRace_Max",913,3550)
V915M=V(4,"enErr_Reforge_CostNotEnough",914,3551)
V916M=V(4,"enErr_Reforge_CostRemoveFaild",915,3552)
V917M=V(4,"enErr_Reforge_EquipNotExist",916,3553)
V918M=V(4,"enErr_Reforge_ConfigFailed",917,3554)
V919M=V(4,"enErr_Reforge_CantReforge",918,3555)
V920M=V(4,"enErr_Reforge_Max",919,3560)
V921M=V(4,"enErr_SpaceGap_NotPassLast",920,3561)
V922M=V(4,"enErr_SpaceGap_BattleFail",921,3562)
V923M=V(4,"enErr_SpaceGap_Set_DialogFlag_Fail",922,3563)
V924M=V(4,"enErr_SpaceGap_Max",923,3570)
V925M=V(4,"enErr_MultiLineUp_MonsterCfgErr",924,3571)
V926M=V(4,"enErr_MultiLineUp_PalsParamErr",925,3572)
V927M=V(4,"enErr_MultiLineUp_BattleError",926,3573)
V928M=V(4,"enErr_MultiLineUp_CheckPointErr",927,3574)
V929M=V(4,"enErr_MultiLineUp_Max",928,3580)
V930M=V(4,"enErr_LeagueMedal_ErrorID",929,3651)
V931M=V(4,"enErr_LeagueMedal_NotCEO",930,3652)
V932M=V(4,"enErr_LeagueMedal_RewardGot",931,3653)
V933M=V(4,"enErr_LeagueMedal_RewardLocked",932,3654)
V934M=V(4,"enErr_LeagueMedal_Max",933,3700)
V935M=V(4,"enErr_MonthTask_UnFinishAll",934,3701)
V936M=V(4,"enErr_MonthTask_HavingGain",935,3702)
V937M=V(4,"enErr_MonthTask_NoSelRewardID",936,3703)
V938M=V(4,"enErr_MonthTask_NoSpace",937,3704)
V939M=V(4,"enErr_MonthTask_Max",938,3720)
V940M=V(4,"enErr_RecomCard_GetCard_Fail",939,3721)
V941M=V(4,"enErr_RecomCard_SetCard_Fail",940,3722)
V942M=V(4,"enErr_RecomCard_3_Fail",941,3723)
V943M=V(4,"enErr_RecomCard_4_Fail",942,3724)
V944M=V(4,"enErr_RecomCard_Max",943,3725)
V945M=V(4,"enErr_HeroSkin_Update_Fail",944,3726)
V946M=V(4,"enErr_HeroSkin_Have_Not_Skin",945,3727)
V947M=V(4,"enErr_HeroSkin_1",946,3728)
V948M=V(4,"enErr_HeroSkin_2",947,3729)
V949M=V(4,"enErr_HeroSkin_Max",948,3730)
V950M=V(4,"enErr_Polt_BattleFail",949,3731)
V951M=V(4,"enErr_Polt_StageLv_Err",950,3732)
V952M=V(4,"enErr_Polt_Time_Err",951,3733)
V953M=V(4,"enErr_Polt_Pass_Last_Stage",952,3734)
V954M=V(4,"enErr_Polt_Max",953,3735)
V955M=V(4,"enErr_RookieTargetAct_Reward_Fail",954,3736)
V956M=V(4,"enErr_RookieTargetAct_Have_Reward",955,3737)
V957M=V(4,"enErr_RookieTargetAct_Not_Reward",956,3738)
V958M=V(4,"enErr_RookieTargetAct_ActEnd",957,3739)
V959M=V(4,"enErr_RookieTargetAct_Max",958,3740)
V960M=V(4,"enErr_EquipResonance_Fail",959,3741)
V961M=V(4,"enErr_EquipResonance_Cost_Insufficient",960,3742)
V962M=V(4,"enErr_EquipResonance_Lv_Insufficient",961,3743)
V963M=V(4,"enErr_EquipResonance_Recasting",962,3744)
V964M=V(4,"enErr_EquipResonance_Max",963,3745)
V965M=V(4,"enErr_LifetimeCard_NoBuy",964,3750)
V966M=V(4,"enErr_LifetimeCard_HaveGetted",965,3751)
V967M=V(4,"enErr_LifetimeCard_Max",966,3760)
V968M=V(4,"enErr_Valentines_NotOpen",967,3761)
V969M=V(4,"enErr_ParamError",968,3762)
V970M=V(4,"enErr_InitmacyConfigNotExist",969,3763)
V971M=V(4,"enErr_InitmacyConfigError",970,3764)
V972M=V(4,"enErr_ValentinesParamError",971,3765)
V973M=V(4,"enErr_ArcheryCountMax",972,3766)
V974M=V(4,"enErr_ArcheryStateError",973,3767)
V975M=V(4,"enErr_ArcheryConfigError",974,3768)
V976M=V(4,"enErr_ArcheryBuyCountMax",975,3769)
V977M=V(4,"enErr_ValentinesErrorCodeMax",976,3780)
V978M=V(4,"enErr_ModuleAct_Close",977,3781)
V979M=V(4,"enErr_ModuleAct_Lock",978,3782)
V980M=V(4,"enErr_Module_Max",979,3880)
V981M=V(4,"enErr_BattleshipTech_GoToUpgrade",980,3901)
V982M=V(4,"enErr_BattleshipTech_TechLvErr",981,3902)
V983M=V(4,"enErr_BattleshipTech_LvError",982,3903)
V984M=V(4,"enErr_BattleshipTech_PreLvNotEnough",983,3904)
V985M=V(4,"enErr_BattleshipTech_UpgradeLackOfGoods",984,3905)
V986M=V(4,"enErr_BattleshipTech_ModuleClose",985,3906)
V987M=V(4,"enErr_BattleshipTech_ResetLackDiamond",986,3907)
V988M=V(4,"enErr_BattleshipTech_ResetLvZero",987,3908)
V989M=V(4,"enErr_BattleshipTech_MAX",988,3920)
V990M=V(4,"enErr_SpaceExplore_OngoingExploreGalaxy",989,3921)
V991M=V(4,"enErr_SpaceExplore_NoGalaxyID",990,3922)
V992M=V(4,"enErr_SpaceExplore_NoGalaxyMapID",991,3923)
V993M=V(4,"enErr_SpaceExplore_NoPlanetID",992,3924)
V994M=V(4,"enErr_SpaceExplore_GalaxyNoActivation",993,3925)
V995M=V(4,"enErr_SpaceExplore_BattleshipTechIDLvErr",994,3926)
V996M=V(4,"enErr_SpaceExplore_BattleshipTechUpLvErr",995,3927)
V997M=V(4,"enErr_SpaceExplore_BattleshipTechUpOldLvErr",996,3928)
V998M=V(4,"enErr_SpaceExplore_UpgradeLackOfGoods",997,3929)
V999M=V(4,"enErr_SpaceExplore_BattleshipUpgradeFail",998,3930)
V1000M=V(4,"enErr_SpaceExplore_PlanetNoExist",999,3931)
V1001M=V(4,"enErr_SpaceExplore_GalaxyNoExplore",1000,3932)
V1002M=V(4,"enErr_SpaceExplore_LinkPlanetNoAty",1001,3933)
V1003M=V(4,"enErr_SpaceExplore_ActivationPlanetFail",1002,3934)
V1004M=V(4,"enErr_SpaceExplore_NoGalaxyMapCfg",1003,3935)
V1005M=V(4,"enErr_SpaceExplore_PlanetNoInGalaxyMay",1004,3936)
V1006M=V(4,"enErr_SpaceExplore_PreciousGetFail",1005,3937)
V1007M=V(4,"enErr_SpaceExplore_NotResoursePlanet",1006,3938)
V1008M=V(4,"enErr_SpaceExplore_DispatchHeroNoExist",1007,3939)
V1009M=V(4,"enErr_SpaceExplore_DispatchHeroStarLvlow",1008,3940)
V1010M=V(4,"enErr_SpaceExplore_NoDispatchPlanet",1009,3941)
V1011M=V(4,"enErr_SpaceExplore_RepeatedHeroSid",1010,3942)
V1012M=V(4,"enErr_SpaceExplore_DispatchHeroTooMore",1011,3943)
V1013M=V(4,"enErr_SpaceExplore_PlanetNoActavation",1012,3944)
V1014M=V(4,"enErr_SpaceExplore_PlanetDispatchRepeated",1013,3945)
V1015M=V(4,"enErr_SpaceExplore_PlanetHavePirates",1014,3946)
V1016M=V(4,"enErr_SpaceExplore_EventIDNoExist",1015,3947)
V1017M=V(4,"enErr_SpaceExplore_EventError",1016,3948)
V1018M=V(4,"enErr_SpaceExplore_ExploreTooLittle",1017,3949)
V1019M=V(4,"enErr_SpaceExplore_GalaxyHasActivation",1018,3950)
V1020M=V(4,"enErr_SpaceExplore_GalaxyUnlockConsume",1019,3951)
V1021M=V(4,"enErr_SpaceExplore_PlanetActivation",1020,3952)
V1022M=V(4,"enErr_SpaceExplore_DiffPeriod",1021,3953)
V1023M=V(4,"enErr_SpaceExplore_ModuleClose",1022,3954)
V1024M=V(4,"enErr_SpaceExplore_BuyPrivilegeCardNotEnough",1023,3955)
V1025M=V(4,"enErr_SpaceExplore_HasBuyPrivilegeCard",1024,3956)
V1026M=V(4,"enErr_SpaceExplore_BuyPrivilegeCardFail",1025,3957)
V1027M=V(4,"enErr_SpaceExplore_NoHavePrivilegeCard",1026,3958)
V1028M=V(4,"enErr_SpaceExplore_AkeyExploreFail",1027,3959)
V1029M=V(4,"enErr_SpaceExplore_NumMaxFail",1028,3960)
V1030M=V(4,"enErr_SpaceExplore_MAX",1029,3980)
V1031M=V(4,"enErr_Sigil_Replace_Fail",1030,3981)
V1032M=V(4,"enErr_Sigil_Have_None",1031,3982)
V1033M=V(4,"enErr_Sigil_Not_Sigil",1032,3983)
V1034M=V(4,"enErr_Sigil_Position_Err",1033,3984)
V1035M=V(4,"enErr_Sigil_Hero_StarLv_Err",1034,3985)
V1036M=V(4,"enErr_Sigil_Wear_Same",1035,3986)
V1037M=V(4,"enErr_Sigil_Takeoff_Same",1036,3987)
V1038M=V(4,"enErr_Sigil_UpgradeLvFail",1037,3990)
V1039M=V(4,"enErr_Sigil_Max",1038,3991)
V1040M=V(4,"enErr_LegendType_Error",1039,4001)
V1041M=V(4,"enErr_LegendNotSign",1040,4002)
V1042M=V(4,"enErr_LegendConfigError",1041,4003)
V1043M=V(4,"enErr_LegendCantPrize",1042,4004)
V1044M=V(4,"enErr_LegendPrized",1043,4005)
V1045M=V(4,"enErr_LengendNotSign",1044,4006)
V1046M=V(4,"enErr_LengendNotGroup",1045,4007)
V1047M=V(4,"enErr_LegendNoData",1046,4008)
V1048M=V(4,"enErr_LegendTodayAtt",1047,4009)
V1049M=V(4,"enErr_LegendTroopError",1048,4010)
V1050M=V(4,"enErr_LegendUpdateDBError",1049,4011)
V1051M=V(4,"enErr_LegendNotOpen",1050,4013)
V1052M=V(4,"enErr_LegendGroupError",1051,4014)
V1053M=V(4,"enErr_LegendHeroStarError",1052,4015)
V1054M=V(4,"enErr_Legend_SelfTroopError",1053,4016)
V1055M=V(4,"enErr_Legend_TargetTroopError",1054,4017)
V1056M=V(4,"enErr_Legend_MaxBattleCount",1055,4018)
V1057M=V(4,"enErr_Legend_SetDefTroopError",1056,4019)
V1058M=V(4,"enErr_Legend_BattledHeroCantCost",1057,4020)
V1059M=V(4,"enErr_Legend_CantSignExperience",1058,4021)
V1060M=V(4,"enErr_Legend_NotBattlePeriod",1059,4022)
V1061M=V(4,"enErr_Legend_SignCooling",1060,4023)
V1062M=V(4,"enErr_Legend_Signed",1061,4024)
V1063M=V(4,"enErr_Legend_MAX",1062,4050)
V1064M=V(4,"enErr_NewSvrTopRace_Close",1063,4051)
V1065M=V(4,"enErr_NewSvrTopRace_NoArena",1064,4052)
V1066M=V(4,"enErr_NewSvrTopRace_NoRoleInfo",1065,4053)
V1067M=V(4,"enErr_NewSvrTopRace_NoTop18Pal",1066,4054)
V1068M=V(4,"enErr_NewSvrTopRace_NoCurBattle",1067,4055)
V1069M=V(4,"enErr_NewSvrTopRace_NoJoin",1068,4056)
V1070M=V(4,"enErr_NewSvrTopRace_SettleNoResult",1069,4057)
V1071M=V(4,"enErr_NewSvrTopRace_OutOf64",1070,4058)
V1072M=V(4,"enErr_NewSvrTopRace_RankError",1071,4059)
V1073M=V(4,"enErr_NewSvrTopRace_RankRewardHasGet",1072,4060)
V1074M=V(4,"enErr_NewSvrTopRace_NoRank",1073,4061)
V1075M=V(4,"enErr_NewSvrTopRace_VoteAfter23",1074,4062)
V1076M=V(4,"enErr_NewSvrTopRace_VoteNoPush",1075,4063)
V1077M=V(4,"enErr_NewSvrTopRace_VoteComplete",1076,4064)
V1078M=V(4,"enErr_NewSvrTopRace_VoteCoinLess",1077,4065)
V1079M=V(4,"enErr_NewSvrTopRace_VotePushDayMore7",1078,4066)
V1080M=V(4,"enErr_NewSvrTopRace_OnTopRaceList",1079,4067)
V1081M=V(4,"enErr_NewSvrTopRace_VoteOnBattle",1080,4068)
V1082M=V(4,"enErr_NewSvrTopRace_OutOfSetLineUpTime",1081,4069)
V1083M=V(4,"enErr_NewSvrTopRace_ApplaudFrequently",1082,4070)
V1084M=V(4,"enErr_NewSvrTopRace_AlreadyLiked",1083,4071)
V1085M=V(4,"enErr_NewSvrTopRace_ApplaudNoArenaID",1084,4072)
V1086M=V(4,"enErr_NewSvrTopRace_ApplaudNotTop1",1085,4073)
V1087M=V(4,"enErr_NewSvrTopRace_ApplaudNoReward",1086,4074)
V1088M=V(4,"enErr_NewSvrTopRace_NotRole",1087,4075)
V1089M=V(4,"enErr_NewSvrTopRace_NotSaveTroop",1088,4076)
V1090M=V(4,"enErr_NewSvrTopRace_Pal_InLineup",1089,4077)
V1091M=V(4,"enErr_NewSvrTopRace_Max",1090,4100)
V1092M=V(4,"enErr_Lottery_NoChoice",1091,4101)
V1093M=V(4,"enErr_Lottery_PoolNoHero",1092,4102)
V1094M=V(4,"enErr_Lottery_NoChoiceHero",1093,4103)
V1095M=V(4,"enErr_Lottery_ChoiceLotID",1094,4104)
V1096M=V(4,"enErr_Lottery_TimeLimit",1095,4105)
V1097M=V(4,"enErr_ExpPassport_ConfigError",1096,4110)
V1098M=V(4,"enErr_ExpPassPort_TypeError",1097,4111)
V1099M=V(4,"enErr_ExpPassPort_LvError",1098,4112)
V1100M=V(4,"enErr_TargetRewardNotExist",1099,4121)
V1101M=V(4,"enErr_NoTargetReward",1100,4122)
V1102M=V(4,"enErr_DynamicBattle_NoData",1101,4130)
V1103M=V(4,"enErr_KillingTower_NoData",1102,4140)
V1104M=V(4,"enErr_KillingTower_CanNotBattle",1103,4141)
V1105M=V(4,"enErr_KillingTower_AlreadyHave",1104,4142)
V1106M=V(4,"enErr_KillingTower_NotGet",1105,4143)
V1107M=V(4,"enErr_KillingTower_CrushOverCount",1106,4144)
V1108M=V(4,"enErr_KillingTower_CampHeroCount",1107,4145)
V1109M=V(4,"enErr_WeekendArena_Rank_Err",1108,4150)
V1110M=V(4,"enErr_WeekendArena_Challenge_Num_Empty",1109,4151)
V1111M=V(4,"enErr_WeekendArena_End",1110,4152)
V1112M=V(4,"enErr_WeekendArena_Battle_Fail",1111,4153)
V1113M=V(4,"enErr_WeekendArena_Like_Fail",1112,4154)
V1114M=V(4,"enErr_WeekendArena_Pal_InLineup",1113,4155)
V1115M=V(4,"enErr_WeekendArena_Like_Today",1114,4156)
V1116M=V(4,"enErr_WeekendArena_OptFreqLimit",1115,4157)
V1117M=V(4,"enErr_Lottery_Choice_Max",1116,4200)
V1118M=V(4,"enErr_XYX_ActiveAgain",1117,4201)
V1119M=V(4,"enErr_XYX_PreLevelBlocked",1118,4202)
V1120M=V(4,"enErr_XYX_PreBuildingBlocked",1119,4203)
V1121M=V(4,"enErr_XYX_PreMainStageBlocked",1120,4204)
V1122M=V(4,"enErr_XYX_TrainningCenterOverflow",1121,4205)
V1123M=V(4,"enErr_XYX_CreateRoleDayBlocked",1122,4206)
V1124M=V(4,"enErr_XYX_TargetPowerNotEnough",1123,4207)
V1125M=V(4,"enErr_XYX_ModuleIsNotOpen",1124,4208)
V1126M=V(4,"enErr_XYX_ActivityOpenDayBlocked",1125,4209)
V1127M=V(4,"enErr_ChinaRed_SweepLimit",1126,4220)
V1128M=V(4,"enErr_ChinaRed_ChapterGained",1127,4221)
V1129M=V(4,"enErr_ChinaRed_StarNumNotEnough",1128,4222)
V1130M=V(4,"enErr_ChinaRed_HavingSweep",1129,4223)
V1131M=V(4,"enErr_ChinaRed_PassLimit",1130,4224)
V1132M=V(4,"enErr_ChinaRed_Max",1131,4240)
V1133M=V(4,"enErr_BindPohone_ArealyGot",1132,4250)
V1134M=V(4,"enErr_ExtrasResourceDownload_ArealyGot",1133,4251)
V1135M=V(4,"enErr_BindEmail_RealyGot",1134,4252)
V1136M=V(4,"enErr_StarCraft_NotSignState",1135,4260)
V1137M=V(4,"enErr_StarCraft_Signed",1136,4261)
V1138M=V(4,"enErr_StarCraft_Caption_Over",1137,4262)
V1139M=V(4,"enErr_StarCraft_Is_Caption",1138,4263)
V1140M=V(4,"enErr_StarCraft_Not_Caption",1139,4264)
V1141M=V(4,"enErr_StarCraft_Max",1140,4300)
V1142M=V(4,"enErr_SubTask_UnFinishAll",1141,4301)
V1143M=V(4,"enErr_SubTask_HavingGain",1142,4302)
V1144M=V(4,"enErr_SubTask_NoSelRewardID",1143,4303)
V1145M=V(4,"enErr_SubTask_NoSpace",1144,4304)
V1146M=V(4,"enErr_SubTask_Max",1145,4305)
V1147M=V(4,"enErr_DecorPassport_ConfigError",1146,4306)
V1148M=V(4,"enErr_DecorPassPort_TypeError",1147,4307)
V1149M=V(4,"enErr_DecorPassPort_LvError",1148,4308)
V1150M=V(4,"enErr_DecorPassPort_InvalidTime",1149,4315)
V1151M=V(4,"enErr_RebirthSpace_RecommandEmpty",1150,4323)
V1152M=V(4,"enErr_RebirthSpace_ChallengeCountEmpty",1151,4324)
V1153M=V(4,"enErr_RebirthSpace_BuyCountEmpty",1152,4325)
V1154M=V(4,"enErr_RebirthSpace_BatttleInSettle",1153,4326)
V1155M=V(4,"enErr_RebirthSpace_UserIsForbidden",1154,4327)
V1156M=V(4,"enErr_RebirthSpace_HeroForbidden",1155,4328)
V1157M=V(4,"enErr_RebirthSpace_BattleLock",1156,4329)
V1158M=V(4,"enErr_RebirthSpace_Max",1157,4335)
V1159M=V(4,"enErr_TreasureRare_Reward_Conf",1158,4336)
V1160M=V(4,"enErr_TreasureRare_ComposeTreasure_System",1159,4337)
V1161M=V(4,"enErr_TreasureRare_ComposeTreasure_Num",1160,4338)
V1162M=V(4,"enErr_TreasureRare_ComposeTreasure_Repeated",1161,4339)
V1163M=V(4,"enErr_TreasureRare_Stage_Failed",1162,4340)
V1164M=V(4,"enErr_TreasureRare_Guild_Failed",1163,4341)
V1165M=V(4,"enErr_TreasureRare_Awaken",1164,4342)
V1166M=V(4,"enErr_TreasureRare_Item",1165,4343)
V1167M=V(4,"enErr_TreasureRare_Unavaliable",1166,4344)
V1168M=V(4,"enErr_TreasureRare_Not_Satisfied",1167,4345)
V1169M=V(4,"enErr_TreasureRare_Remove_Item",1168,4346)
V1170M=V(4,"enErr_TreasureRare_TreasureIDUpdate",1169,4347)
V1171M=V(4,"enErr_TreasureRare_Enhance_Conditions",1170,4348)
V1172M=V(4,"enErr_TreasureRare_Enhance_Limit",1171,4349)
V1173M=V(4,"enErr_TreasureRare_Enhance_Equip",1172,4350)
V1174M=V(4,"enErr_TreasureRare_Purchase_Limit",1173,4351)
V1175M=V(4,"enErr_TreasureRare_Enhance_Failed",1174,4352)
V1176M=V(4,"enErr_TreasureRare_System",1175,4353)
V1177M=V(4,"enErr_TreasureRare_Maker_Failed",1176,4354)
V1178M=V(4,"enErr_TreasureRare_Maker_Proccessing",1177,4355)
V1179M=V(4,"enErr_MonthCardGift_ConfigError",1178,4356)
V1180M=V(4,"enErr_MonthCardGift_ResError",1179,4357)
V1181M=V(4,"enErr_MonthCardGift_NotSuperUser",1180,4358)
V1182M=V(4,"enErr_MonthCardGift_NotStart",1181,4359)
V1183M=V(4,"enErr_MonthCardGift_NotOnline",1182,4360)
V1184M=V(4,"enErr_MonthCardGift_IdleStageError",1183,4361)
V1185M=V(4,"enErr_MonthCardGift_limitTimeError",1184,4362)
V1186M=V(4,"enErr_MonthCardGift_SetCompleteTimes",1185,4363)
V1187M=V(4,"enErr_TreasureRare_Purchase_Limit_ForgeReward",1186,4386)
V1188M=V(4,"enErr_TreasureRare_Advance_Recycling",1187,4391)
V1189M=V(4,"enErr_TreasureRare_Enchance_Recycling",1188,4392)
V1190M=V(4,"enErr_TreasureRare_GM_Param",1189,4393)
V1191M=V(4,"enErr_TreasureRare_GM_PlayerOffLine",1190,4394)
V1192M=V(4,"enErr_TreasureRare_GM_DelTreasureFail",1191,4395)
V1193M=V(4,"enErr_TreasureRare_AwakenColorType_Config",1192,4396)
V1194M=V(4,"enErr_League_IllegalStr",1193,4400)
V1195M=V(4,"enErr_FBguide_AlreadyGot",1194,4401)
V1196M=V(4,"enErr_MonthlyTask_TreasureRare_Closed",1195,4402)
V1197M=V(4,"enErr_MonthlyTask_TreasureRare_Unlocked",1196,4403)
V1198M=V(4,"enErr_MonthlyTask_TreasureRare_Config",1197,4404)
V1199M=V(4,"enErr_MonthlyTask_TreasureRare_LackOfItem",1198,4405)
V1200M=V(4,"enErr_MonthlyTask_TreasureRare_RemoveFail",1199,4406)
V1201M=V(4,"enErr_MonthlyTask_TreasureRare_UnlockFail",1200,4407)
V1202M=V(4,"enErr_MonthlyTask_TreasureRare_ReqParam",1201,4408)
V1203M=V(4,"enErr_MonthlyTask_TreasureRare_NoRewardInconfig",1202,4409)
V1204M=V(4,"enErr_MonthlyTask_TreasureRare_RewardTimeLimit",1203,4410)
V1205M=V(4,"enErr_MonthlyTask_TreasureRare_SetSystem",1204,4411)
V1206M=V(4,"enErr_MonthlyTask_TreasureRare_NoChosenReward",1205,4412)
V1207M=V(4,"enErr_MonthlyTask_TreasureRare_RewardReceived",1206,4413)
V1208M=V(4,"enErr_MonthlyTask_TreasureRare_Max",1207,4414)
V1209M=V(4,"enErr_NewYearAct_User_No_Redpacket",1208,4500)
V1210M=V(4,"enErr_NewYearAct_User_Remove_Redpacket_Fail",1209,4501)
V1211M=V(4,"enErr_NewYearAct_Redpacket_Numbers_Over",1210,4502)
V1212M=V(4,"enErr_NewYearAct_Redpacket_Expire",1211,4503)
V1213M=V(4,"enErr_NewYearAct_Today_Give_Redpacket_Over",1212,4504)
V1214M=V(4,"enErr_NewYearAct_Today_Grab_Redpacket_Over",1213,4505)
V1215M=V(4,"enErr_NewYearAct_Redpacket_Expire_Over",1214,4506)
V1216M=V(4,"enErr_NewYearAct_Redpacket_Had_Get",1215,4507)
V1217M=V(4,"enErr_NewYearAct_Redpacket_Garb_Over",1216,4508)
V1218M=V(4,"enErr_NewYearAct_User_No_This_FuCard",1217,4509)
V1219M=V(4,"enErr_NewYearAct_Repeat_Send_FuCard",1218,4510)
V1220M=V(4,"enErr_NewYearAct_SendLimit",1219,4511)
V1221M=V(4,"enErr_NewYearAct_GetLimit",1220,4512)
V1222M=V(4,"enErr_WeaponDiamond_Composite_param",1221,4513)
V1223M=V(4,"enErr_WeaponDiamond_System",1222,4514)
V1224M=V(4,"enErr_WeaponDiamond_GoodsNotExist",1223,4515)
V1225M=V(4,"enErr_WeaponDiamond_GoodsType",1224,4516)
V1226M=V(4,"enErr_WeaponDiamond_TakeOffDiamond",1225,4517)
V1227M=V(4,"enErr_WeaponDiamond_Composite_Overflow",1226,4518)
V1228M=V(4,"enErr_WeaponDiamond_ConsumeFailed",1227,4519)
V1229M=V(4,"enErr_WeaponDiamond_ReplaceFailed",1228,4523)
V1230M=V(4,"enErr_WeaponDiamond_operation_param",1229,4524)
V1231M=V(4,"enErr_WeaponDiamond_DiamondSlotLocked",1230,4525)
V1232M=V(4,"enErr_WeaponDiamond_DiamondNotExist",1231,4526)
V1233M=V(4,"enErr_WeaponDiamond_RepalcePosNoDia",1232,4527)
V1234M=V(4,"enErr_WeaponDiamond_DiamondSocketType",1233,4528)
V1235M=V(4,"enErr_WeaponDiamond_DiamondSlotPos",1234,4529)
V1236M=V(4,"enErr_WeaponDiamond_StarWeaponConfig",1235,4530)
V1237M=V(4,"enErr_WeaponDiamond_DiamondOpreationFail",1236,4531)
V1238M=V(4,"enErr_WeaponDiamond_WeaponUpgrade_param",1237,4532)
V1239M=V(4,"enErr_WeaponDiamond_WeaponUpgrade_fail",1238,4533)
V1240M=V(4,"enErr_WeaponDiamond_WeaponUpgrade_Config",1239,4534)
V1241M=V(4,"enErr_WeaponDiamond_WeaponUpgrade_MaxLevel",1240,4535)
V1242M=V(4,"enErr_WeaponDiamond_WeaponUpgrade_Item",1241,4536)
V1243M=V(4,"enErr_WeaponDiamond_Upgrade_ItemDelete",1242,4537)
V1244M=V(4,"enErr_WeaponDiamond_WeaponSkill_param",1243,4538)
V1245M=V(4,"enErr_WeaponDiamond_WeaponSkill_Locked",1244,4539)
V1246M=V(4,"enErr_WeaponDiamond_WeaponSkill_NotExist",1245,4540)
V1247M=V(4,"enErr_WeaponDiamond_WeaponSkill_Update",1246,4541)
V1248M=V(4,"enErr_WeaponDiamond_WeaponSkill_PropUpdate",1247,4542)
V1249M=V(4,"enErr_WeaponDiamond_PalIsMercenary",1248,4543)
V1250M=V(4,"enErr_WeaponDiamond_CompositeGold",1249,4544)
V1251M=V(4,"enErr_WeaponDiamond_MountedType",1250,4545)
V1252M=V(4,"enErr_WeaponDiamond_Exchange",1251,4546)
V1253M=V(4,"enErr_WeaponDiamond_HighCompositeClose",1252,4547)
V1254M=V(4,"enErr_WeaponDiamond_NotFirstGuide",1253,4548)
V1255M=V(4,"enErr_WeaponDiamond_GuideInitBattle",1254,4549)
V1256M=V(4,"enErr_WeaponDiamond_GemCompositeCfg",1255,4550)
V1257M=V(4,"enErr_weaponDiamond_GemCompositeUpgrade",1256,4551)
V1258M=V(4,"enErr_WeaponDiamond_Module_Closed",1257,4552)
V1259M=V(4,"enErr_WeaponDiamond_BatchComposit_Closed",1258,4553)
V1260M=V(4,"enErr_WeaponDiamond_Lock_Failed",1259,4554)
V1261M=V(4,"enErr_WeaponDiamond_Lock_Status",1260,4555)
V1262M=V(4,"enErr_WeaponDiamond_Absolutely_Failed",1261,4556)
V1263M=V(4,"enErr_WeaponDiamond_ExchangeDiamondSlotPos",1262,4558)
V1264M=V(4,"enErr_WeaponDiamond_Max",1263,4575)
V1265M=V(4,"enErr_Galaxy_TickNotEnough",1264,4576)
V1266M=V(4,"enErr_Galaxy_AcrossDay",1265,4577)
V1267M=V(4,"enErr_Galaxy_NoRefurbish",1266,4578)
V1268M=V(4,"enErr_Galaxy_NotCoint",1267,4579)
V1269M=V(4,"enErr_Galaxy_GoodsNotExist",1268,4580)
V1270M=V(4,"enErr_Galaxy_GoodsHadSell",1269,4581)
V1271M=V(4,"enErr_Galaxy_PackFull",1270,4582)
V1272M=V(4,"enErr_Galaxy_TimesNotUsed",1271,4583)
V1273M=V(4,"enErr_Galaxy_TimesLimit",1272,4584)
V1274M=V(4,"enErr_Galaxy_NoDiamond",1273,4585)
V1275M=V(4,"enErr_Galaxy_NotCondition",1274,4586)
V1276M=V(4,"enErr_Galaxy_NotOpen",1275,4587)
V1277M=V(4,"enErr_Galaxy_AlreadyReward",1276,4588)
V1278M=V(4,"enErr_Galaxy_NotFinished",1277,4589)
V1279M=V(4,"enErr_Galaxy_StateError",1278,4590)
V1280M=V(4,"enErr_Galaxy_RoundCheckFail",1279,4591)
V1281M=V(4,"enErr_Galaxy_GetRewardFail",1280,4592)
V1282M=V(4,"enErr_Galaxy_RankTimes",1281,4593)
V1283M=V(4,"enErr_Galaxy_ExitFail",1282,4594)
V1284M=V(4,"enErr_Galaxy_Empty",1283,4636)
V1285M=V(4,"enErr_DimensionWar_NotSignState",1284,4760)
V1286M=V(4,"enErr_DimensionWar_Signed",1285,4761)
V1287M=V(4,"enErr_DimensionWar_Caption_Over",1286,4762)
V1288M=V(4,"enErr_DimensionWar_Is_Caption",1287,4763)
V1289M=V(4,"enErr_DimensionWar_Not_Caption",1288,4764)
V1290M=V(4,"enErr_DimensionWar_SetDefTroopError",1289,4765)
V1291M=V(4,"enErr_DimensionWarNotSign",1290,4766)
V1292M=V(4,"enErr_DimensionWar_NotInBattleState",1291,4767)
V1293M=V(4,"enErr_DimensionWar_Captain_Dealing",1292,4768)
V1294M=V(4,"enErr_DimensionWar_StationIsBasePos",1293,4769)
V1295M=V(4,"enErr_DimensionWar_StationCantArrive",1294,4770)
V1296M=V(4,"enErr_DimensionWar_StationInfoError",1295,4771)
V1297M=V(4,"enErr_DimensionWar_ActionCountMax",1296,4772)
V1298M=V(4,"enErr_DimensionWar_HeroUsed",1297,4773)
V1299M=V(4,"enErr_DimensionWar_HeroWanted",1298,4774)
V1300M=V(4,"enErr_DimensionWar_Team_Not_Exist",1299,4775)
V1301M=V(4,"enErr_DimensionWar_CEOCanSignUp",1300,4776)
V1302M=V(4,"enErr_DimensionWar_Station_Hided",1301,4777)
V1303M=V(4,"enErr_DimensionWar_HeroInTroop",1302,4778)
V1304M=V(4,"enErr_DimensionWar_Area_Not_Open",1303,4779)
V1305M=V(4,"enErr_DimensionWar_ExitLeague",1304,4780)
V1306M=V(4,"enErr_DimensionWar_0Score",1305,4781)
V1307M=V(4,"enErr_DimensionWar_AlreadyAssembled",1306,4782)
V1308M=V(4,"enErr_DimensionWar_NotAssembled",1307,4783)
V1309M=V(4,"enErr_DimensionWar_AssembleTooMuch",1308,4784)
V1310M=V(4,"enErr_DimensionWar_NotMatched",1309,4785)
V1311M=V(4,"enErr_DimensionWar_TeamNotTrusted",1310,4786)
V1312M=V(4,"enErr_DimensionWar_StarNotEnough",1311,4787)
V1313M=V(4,"enErr_DimensionWar_PioneerLock",1312,4788)
V1314M=V(4,"enErr_DimensionWar_TeamListInvalid",1313,4789)
V1315M=V(4,"enErr_DimensionWar_TaskRewarded",1314,4790)
V1316M=V(4,"enErr_DimensionWar_NotOwnStation",1315,4791)
V1317M=V(4,"enErr_DimensionWar_NotOfficer",1316,4792)
V1318M=V(4,"enErr_DimensionWar_NotBoss",1317,4793)
V1319M=V(4,"enErr_DimensionWar_Summoned",1318,4794)
V1320M=V(4,"enErr_DimensionWar_BossNoKill",1319,4795)
V1321M=V(4,"enErr_DimensionWar_BossRewarded",1320,4796)
V1322M=V(4,"enErr_DimensionWar_ScoreNotEnough",1321,4797)
V1323M=V(4,"enErr_DimensionWar_AttackSelfStation",1322,4798)
V1324M=V(4,"enErr_DimensionWar_SaveAfterAttack",1323,4799)
V1325M=V(4,"enErr_DimensionWar_StationUnlock",1324,4800)
V1326M=V(4,"enErr_DimensionWar_SummonNotTime",1325,4801)
V1327M=V(4,"enErr_DimensionWar_NotBossAttTime",1326,4802)
V1328M=V(4,"enErr_DimensionWar_BossLeft",1327,4803)
V1329M=V(4,"enErr_DimensionWar_SamePlace",1328,4804)
V1330M=V(4,"enErr_DimensionWar_DefenseTeamLimit",1329,4805)
V1331M=V(4,"enErr_DimensionWar_DefenseNoTeam",1330,4806)
V1332M=V(4,"enErr_DimensionWar_AttackCountMax",1331,4807)
V1333M=V(4,"enErr_DimensionWar_RestTime",1332,4808)
V1334M=V(4,"enErr_DimensionWar_AssembleCDTime",1333,4809)
V1335M=V(4,"enErr_DimensionWar_DropProtectTime",1334,4810)
V1336M=V(4,"enErr_DimensionWar_TeamAddQueue",1335,4811)
V1337M=V(4,"enErr_DimensionWar_AllTeamInQueue",1336,4812)
V1338M=V(4,"enErr_DimensionWar_SomeTeamInQueue",1337,4813)
V1339M=V(4,"enErr_DimensionWar_TeamInBattle",1338,4814)
V1340M=V(4,"enErr_DimensionWar_Teamlock",1339,4815)
V1341M=V(4,"enErr_DimensionWar_WeaponUsed",1340,4816)
V1342M=V(4,"enErr_DimensionWar_0GatherTeamCount",1341,4817)
V1343M=V(4,"enErr_DimensionWar_StationBusy",1342,4818)
V1344M=V(4,"enErr_DimensionWar_DropStationGathe",1343,4819)
V1345M=V(4,"enErr_DimensionWar_AfterGarrisonTeamMax",1344,4820)
V1346M=V(4,"enErr_DimensionWar_DropOcpNotRightTime",1345,4821)
V1347M=V(4,"enErr_DimensionWar_GotDropReward",1346,4822)
V1348M=V(4,"enErr_DimensionWar_SelfScoreEmpty",1347,4823)
V1349M=V(4,"enErr_Dimension_Empty",1348,4840)
V1350M=V(4,"enErr_LabourDayActivity_IsNotSameAty",1349,4849)
V1351M=V(4,"enErr_LabourDayActivity_InLocking",1350,4850)
V1352M=V(4,"enErr_LabourDayActivity_PriceIsIllegal",1351,4851)
V1353M=V(4,"enErr_LabourDayActivity_ItemNotEnough",1352,4852)
V1354M=V(4,"enErr_LabourDayActivity_AddItemFail",1353,4853)
V1355M=V(4,"enErr_LabourDayActivity_StallSellTimeLimit",1354,4854)
V1356M=V(4,"enErr_LabourDay_ShareCD",1355,4855)
V1357M=V(4,"enErr_LabourDay_LackSellTime",1356,4856)
V1358M=V(4,"enErr_LabourDayActivity_TeamMemberLimit",1357,4857)
V1359M=V(4,"enErr_LabourDayActivity_JoinTeamCrowd",1358,4858)
V1360M=V(4,"enErr_LabourDayActivity_StallFail",1359,4859)
V1361M=V(4,"enErr_LabourDayActivity_StallMiss",1360,4860)
V1362M=V(4,"enErr_LabourDayActivity_ServerNotOpen",1361,4861)
V1363M=V(4,"enErr_LabourDayActivity_AccountEmpty",1362,4862)
V1364M=V(4,"enErr_LabourDayActivity_StallLocking",1363,4863)
V1365M=V(4,"enErr_LabourDayActivity_JoinTeamCD",1364,4864)
V1366M=V(4,"enErr_LabourDayActivity_InCurrTeam",1365,4865)
V1367M=V(4,"enErr_LabourDayActivity_InOtherTeam",1366,4866)
V1368M=V(4,"enErr_LabourDayActivity_HaveOneTeam",1367,4867)
V1369M=V(4,"enErr_LabourDayActivity_IsNotMember",1368,4868)
V1370M=V(4,"enErr_LabourDayActivity_IsNotCaptain",1369,4869)
V1371M=V(4,"enErr_LabourDayActivity_IsShowLimit",1370,4872)
V1372M=V(4,"enErr_LabourDayActivity_IsTimeOut",1371,4873)
V1373M=V(4,"enErr_LabourDay_Max",1372,4890)
V1374M=V(4,"enErr_LeagueActivityBoss_leaveLeagueTimeError",1373,4910)
V1375M=V(4,"enErr_SelectArtifactBox_ItemNotExist",1374,4911)
V1376M=V(4,"enErr_SelectArtifactBox_IsNotArtifactBox",1375,4912)
V1377M=V(4,"enErr_SelectArtifactBox_RewardIDNotExist",1376,4913)
V1378M=V(4,"enErr_SelectArtifactBox_ArtifactIsGetted",1377,4914)
V1379M=V(4,"enErr_SelectArtifactBox_RewardNotArtifact",1378,4915)
V1380M=V(4,"enErr_SelectArtifactBox_RewardFail",1379,4916)
V1381M=V(4,"enErr_LeagueActivityBoss_AddLeagueTimeLimit",1380,4955)
V1382M=V(4,"enErr_IsTrialHero",1381,4960)
V1383M=V(4,"enErr_TrialHero_RegionUnopened",1382,4961)
V1384M=V(4,"enErr_TrialHero_NotEnoughCond",1383,4962)
V1385M=V(4,"enErr_TrialHero_Close",1384,4963)
V1386M=V(4,"enErr_TrialHero_AlreadyHasHero",1385,4964)
V1387M=V(4,"enErr_TrialHero_GetTimesLimit",1386,4965)
V1388M=V(4,"enErr_TrialHero_TimeErr",1387,4966)
V1389M=V(4,"enErr_TrialHero_BattleLimit",1388,4967)
V1390M=V(4,"enErr_TrialHero_DecoPacketNoSpace",1389,4968)
V1391M=V(4,"enErr_TrialHero_EquipPacketNoSpace",1390,4969)
V1392M=V(4,"enErr_TrialHero_HeroPacketNoSpace",1391,4970)
V1393M=V(4,"enErr_TrialHero_SigilPacketNoSpace",1392,4971)
V1394M=V(4,"enErr_TrialHero_SigilCannotOpt",1393,4972)
V1395M=V(4,"enErr_TrialHero_DiamondPacketNoSpace",1394,4973)
V1396M=V(4,"enErr_TrialHero_Max",1395,4980)
V1397M=V(4,"enErr_PickTheRoute_Level_Closed",1396,4985)
V1398M=V(4,"enErr_PickTheRoute_Event_Reward_Received",1397,4986)
V1399M=V(4,"enErr_PickTheRoute_Event_SaveArchived_Failed",1398,4987)
V1400M=V(4,"enErr_PickTheRoute_DailyShareBefore",1399,4988)
V1401M=V(4,"enErr_PickTheRoute_Event_NoRewardDesign",1400,4989)
V1402M=V(4,"enErr_PickTheRoute_Event_PathIllegal",1401,4990)
V1403M=V(4,"enErr_ChristmasTeam_NoTeamTicket",1402,4991)
V1404M=V(4,"enErr_ChristmasTeam_LaunchTeamMaxLimit",1403,4992)
V1405M=V(4,"enErr_ChristmasTeam_LaunchTeamOnce",1404,4993)
V1406M=V(4,"enErr_ChristmasTeam_ChristmasTeamExpired",1405,4994)
V1407M=V(4,"enErr_ChristmasTeam_JoinTeamOnce",1406,4995)
V1408M=V(4,"enErr_ChristmasTeam_JoinTeam_MemberFull",1407,4996)
V1409M=V(4,"enErr_ChristmasTeam_TeamRewardUnavaliable",1408,4997)
V1410M=V(4,"enErr_ChristmasTeam_NoTeamInfo",1409,4998)
V1411M=V(4,"enErr_ChristmasTeam_TeamLock",1410,4999)
V1412M=V(4,"enErr_ChristmasTeam_SelfTeam",1411,5000)
V1413M=V(4,"enErr_BossSlave_NoAward",1412,5021)
V1414M=V(4,"enErr_BossSlave_ParamError",1413,5022)
V1415M=V(4,"enErr_BossSlave_NotMySlave",1414,5023)
V1416M=V(4,"enErr_BossSlave_NotMyBoss",1415,5024)
V1417M=V(4,"enErr_BossSlave_BattleInfoError",1416,5025)
V1418M=V(4,"enErr_BossSlave_ConquerSelf",1417,5026)
V1419M=V(4,"enErr_BossSlave_ConquerFailed",1418,5027)
V1420M=V(4,"enErr_BossSlave_CatchSelf",1419,5028)
V1421M=V(4,"enErr_BossSlave_EnergyLess",1420,5029)
V1422M=V(4,"enErr_BossSlave_Abnormal",1421,5030)
V1423M=V(4,"enErr_BossSlave_AlreadyCatched",1422,5031)
V1424M=V(4,"enErr_BossSlave_OperError",1423,5032)
V1425M=V(4,"enErr_BossSlave_MailBusy",1424,5033)
V1426M=V(4,"enErr_BossSlave_MailNotExist",1425,5034)
V1427M=V(4,"enErr_BossSlave_MailAwarded",1426,5035)
V1428M=V(4,"enErr_BossSlave_MailNoItems",1427,5036)
V1429M=V(4,"enErr_BossSlave_MailEmpty",1428,5037)
V1430M=V(4,"enErr_BossSlave_BattleStartError",1429,5038)
V1431M=V(4,"enErr_Slave_AddItemFail",1430,5039)
V1432M=V(4,"enErr_Slave_AddEnergyFail",1431,5040)
V1433M=V(4,"enErr_Slave_OverPerchaseEnergy",1432,5041)
V1434M=V(4,"enErr_Slave_DataProcError",1433,5042)
V1435M=V(4,"enErr_BossSlave_BattleRobot",1434,5043)
V1436M=V(4,"enErr_BossSlave_CatchInvalidSlave",1435,5044)
V1437M=V(4,"enErr_BossSlave_ForcedLeftTeam",1436,5045)
V1438M=V(4,"enErr_BossSlave_SlaveListFull",1437,5046)
V1439M=V(4,"emErr_BossSlave_DuplicatePalId",1438,5047)
V1440M=V(4,"enErr_BossSlave_NotInSameWorldArea",1439,5048)
V1441M=V(4,"enErr_SlaveRecommend_Empty",1440,5049)
V1442M=V(4,"enErr_EmailSub_InitCfgNoExist",1441,5050)
V1443M=V(4,"enErr_EmailSub_NotOpen",1442,5051)
V1444M=V(4,"enErr_EmailSub_ItemNotExist",1443,5052)
V1445M=V(4,"enErr_EmailSub_ParamError",1444,5053)
V1446M=V(4,"enErr_EmailSub_UnBind",1445,5054)
V1447M=V(4,"enErr_EmailSub_RepeteRewards",1446,5055)
V1448M=V(4,"enErr_PickTheRoute_BoxType",1447,5056)
V1449M=V(4,"enErr_PickTheRoute_GoodsNotExsit",1448,5057)
V1450M=V(4,"enErr_PickTheRoute_GoodsNotInConfig",1449,5058)
V1451M=V(4,"enErr_PickTheRoute_UseNumZero",1450,5059)
V1452M=V(4,"enErr_PickTheRoute_PassPreviseLevel",1451,5060)
V1453M=V(4,"enErr_AllSaintsDay_NotSubmit",1452,5101)
V1454M=V(4,"enErr_AllSaintsDay_NotSExist",1453,5102)
V1455M=V(4,"enErr_AllSaintsDay_HasUpvote",1454,5103)
V1456M=V(4,"enErr_AllSaintsDay_UpvoteOver",1455,5104)
V1457M=V(4,"enErr_AllSaintsDay_NotFinished",1456,5105)
V1458M=V(4,"enErr_AllSaintsDay_AlreadyReward",1457,5106)
V1459M=V(4,"enErr_AllSaintsDay_RiddleUpdate",1458,5107)
V1460M=V(4,"enErr_AllSaintsDay_NotOpen",1459,5108)
V1461M=V(4,"enErr_MakeFoodActivity_NotOpen",1460,5109)
V1462M=V(4,"enErr_MakeFoodActivityCfgNoExist",1461,5110)
V1463M=V(4,"enErr_MakeFoodActivityItemNotEnough",1462,5111)
V1464M=V(4,"enErr_MakeFoodActivityTimesLimit",1463,5112)
V1465M=V(4,"enErr_MakeFoodActivityCoolDown",1464,5113)
V1466M=V(4,"enErr_MakeFoodActivityOrderNotExist",1465,5114)
V1467M=V(4,"enErr_MakeFoodActivityOrderExpire",1466,5115)
V1468M=V(4,"enErr_MakeFoodActivityOrderTransacted",1467,5116)
V1469M=V(4,"enErr_MakeFoodActivityOrderTransacting",1468,5117)
V1470M=V(4,"enErr_DrawTheSugarMan_NotOpen",1469,5129)
V1471M=V(4,"enErr_DrawTheSugarMan_ParamError",1470,5130)
V1472M=V(4,"enErr_DrawTheSugarMan_ConfigNotExist",1471,5131)
V1473M=V(4,"enErr_DrawTheSugarMan_ConfigError",1472,5132)
V1474M=V(4,"enErr_DrawTheSugarMan_CountMax",1473,5133)
V1475M=V(4,"enErr_DrawTheSugarMan_BuyCountMax",1474,5134)
V1476M=V(4,"enErr_DrawTheSugarMan_GetDataErr",1475,5135)
V1477M=V(4,"enErr_DrawTheSugarMan_SetDataErr",1476,5136)
V1478M=V(4,"enErr_DrawTheSugarMan_CountNotEnough",1477,5137)
V1479M=V(4,"enErr_DrawTheSugarMan_Max",1478,5150)
V1480M=V(4,"enErr_PiggyBank_NotOpen",1479,5151)
V1481M=V(4,"enErr_PiggyBank_ParamError",1480,5152)
V1482M=V(4,"enErr_PiggyBank_ConfigNotExist",1481,5153)
V1483M=V(4,"enErr_PiggyBank_ConfigError",1482,5154)
V1484M=V(4,"enErr_PiggyBank_GetDataErr",1483,5155)
V1485M=V(4,"enErr_PiggyBank_SetDataErr",1484,5156)
V1486M=V(4,"enErr_PiggyBank_NotSaveMoney",1485,5157)
V1487M=V(4,"enErr_PiggyBank_NotWithdrawMoney",1486,5158)
V1488M=V(4,"enErr_PiggyBank_NotFinishTask",1487,5159)
V1489M=V(4,"enErr_PiggyBank_RepeteRewards",1488,5160)
V1490M=V(4,"enErr_PiggyBank_AbnormalErr",1489,5161)
V1491M=V(4,"enErr_PiggyBank_AccrualErr",1490,5162)
V1492M=V(4,"enErr_AnniversaryGradGame_MaxParticipation",1491,5170)
V1493M=V(4,"enErr_AnniversaryGradGame_MaxPurchase",1492,5171)
V1494M=V(4,"enErr_AnniversaryGradGame_MaxPurchaseFaild",1493,5172)
V1495M=V(4,"enErr_AnniversaryGradGame_NotOpenTime",1494,5173)
V1496M=V(4,"enErr_AnniversaryGradGame_GotRewardsToday",1495,5174)
V1497M=V(4,"enErr_ModuleTempClose",1496,5175)
V1498M=V(4,"enErr_PiggyBank_Max",1497,5200)
V1499M=V(4,"enErr_MiniGameRank_ParamError",1498,5221)
V1500M=V(4,"enErr_MiniGameRank_GetRankFail",1499,5222)
V1501M=V(4,"enErr_MiniGameRank_AddRankFail",1500,5223)
V1502M=V(4,"enErr_MiniGameRank_ClickFrequently",1501,5224)
V1503M=V(4,"enErr_MiniGameRank_ScoreErr",1502,5225)
V1504M=V(4,"enErr_MiniGameRank_Max",1503,5230)
V1505M=V(4,"enErr_WelfareActivity_ParamError",1504,5391)
V1506M=V(4,"enErr_WelfareActivity_ErrTime",1505,5392)
V1507M=V(4,"enErr_WelfareActivity_AlreadyGet",1506,5393)
V1508M=V(4,"enErr_WelfareActivity_Max",1507,5400)
V1509M=V(4,"enErr_LuckFlop_CountNotEnough",1508,5451)
V1510M=V(4,"enErr_LuckFlop_BuyTimeNotEnough",1509,5452)
V1511M=V(4,"enErr_LuckFlop_NotPaly",1510,5453)
V1512M=V(4,"enErr_LuckFlop_CardGetted",1511,5454)
V1513M=V(4,"enErr_LeagueExpel_NotTimes",1512,5455)
V1514M=V(4,"enErr_RemoteBattleSvr_CreateFail",1513,5456)
V1515M=V(4,"enErr_RemoteBattleSvr_MAX",1514,5460)
V1516M=V(4,"enErr_Sandbox_SaveTeam_Failed",1515,30015)
V1517M=V(4,"enErr_leagueTech_NotRecommendPermission",1516,30100)
V1518M=V(4,"enErr_leagueTech_ErrEffectId",1517,30101)
V1519M=V(4,"enErr_leagueTech_NoUnLock",1518,30102)
V1520M=V(4,"enErr_Alliance_ParamsErr",1519,30103)
V1521M=V(4,"enErr_leagueTech_CancelRecommendErr",1520,30104)
V1522M=V(4,"enErr_leagueTech_NotInleague",1521,30105)
V1523M=V(4,"enErr_leagueTech_MaxLevel",1522,30106)
V1524M=V(4,"enErr_leagueTech_Upgrade",1523,30107)
V1525M=V(4,"enErr_leagueTech_NotEnoughExperience",1524,30108)
V1526M=V(4,"enErr_leagueTech_EnoughExperience",1525,30109)
V1527M=V(4,"enErr_leagueTech_NotEnoughCoinsContributeCnt",1526,30110)
V1528M=V(4,"enErr_Alliance_NoAuthority",1527,30111)
V1529M=V(4,"enErr_Alliance_R5NoAuthority",1528,30112)
V1530M=V(4,"enErr_Alliance_NoSuitableIn",1529,30113)
V1531M=V(4,"enErr_leagueTech_NotStudyPermission",1530,30114)
V1532M=V(4,"enErr_leagueTech_ContributeGoodsErr",1531,30115)
V1533M=V(4,"enErr_Alliance_NoUnlockFlagID",1532,30116)
V1534M=V(4,"enErr_Alliance_LanguageErr",1533,30117)
V1535M=V(4,"enErr_Alliance_GiftBoxIdNoExist",1534,30118)
V1536M=V(4,"enErr_Alliance_GiftBoxIdHadGet",1535,30119)
V1537M=V(4,"enErr_Alliance_GiftBoxIdHadExpire",1536,30120)
V1538M=V(4,"enErr_Alliance_ApplyLoseEffect",1537,30121)
V1539M=V(4,"enErr_Alliance_NotEnoughGiftLv",1538,30122)
V1540M=V(4,"enErr_Alliance_NotSendEmail",1539,30123)
V1541M=V(4,"enErr_Alliance_NoAllianceMember",1540,30124)
V1542M=V(4,"enErr_Alliance_HasApply",1541,30125)
V1543M=V(4,"enErr_Alliance_SendEmailInCD",1542,30126)
V1544M=V(4,"enErr_Alliance_AuthorityMemberIsFull",1543,30127)
V1545M=V(4,"enErr_Alliance_ContentIllegal",1544,30128)
V1546M=V(4,"enErr_Alliance_PowerSetExecLimit",1545,30129)
V1547M=V(4,"enErr_Alliance_MainLvSetExecLimit",1546,30130)
V1548M=V(4,"enErr_Alliance_UrgentAnnounce",1547,30131)
V1549M=V(4,"enErr_Alliance_TitleIllegal",1548,30132)
V1550M=V(4,"enErr_Alliance_NotAllianceBuildeLv",1549,30133)
V1551M=V(4,"enErr_AllianceShare_NoAlliance",1550,30134)
V1552M=V(4,"enErr_AllianceShare_InCD",1551,30135)
V1553M=V(4,"enErr_AllianceShare_NoAuthority",1552,30136)
V1554M=V(4,"enErr_ScientificResearch_NotOpenBuilding",1553,30200)
V1555M=V(4,"enErr_ScientificResearch_NotEnoughCondition",1554,30201)
V1556M=V(4,"enErr_ScientificResearch_NotFindInfo",1555,30202)
V1557M=V(4,"enErr_ScientificResearch_NotFindBuilding",1556,30203)
V1558M=V(4,"enErr_ScientificResearch_GetScientificFail",1557,30204)
V1559M=V(4,"enErr_ScientificResearch_ItemsNotEnough",1558,30205)
V1560M=V(4,"enErr_ScientificResearch_QueueIsEnough",1559,30206)
V1561M=V(4,"enErr_ScientificResearch_AlreadyResearch",1560,30207)
V1562M=V(4,"enErr_ScientificResearch_NoEmptyBuild",1561,30208)
V1563M=V(4,"enErr_AllianceHelp_RepeatStart",1562,30216)
V1564M=V(4,"enErr_AllianceHelp_NotInBuilding",1563,30217)
V1565M=V(4,"enErr_AllianceHelp_NoConfig",1564,30218)
V1566M=V(4,"enErr_AllianceHelp_ParamsErr",1565,30219)
V1567M=V(4,"enErr_UseGradeResourceBox_Fail",1566,30236)
V1568M=V(4,"enErr_UseGradeResourceBox_LackOfGoods",1567,30237)
V1569M=V(4,"enErr_UseGradeResourceBox_CentorLevelErr",1568,30238)
V1570M=V(4,"enErr_UseResourceSelectBox_Fail",1569,30239)
V1571M=V(4,"enErr_UseResourceSelectBox_LackOfGoods",1570,30240)
V1572M=V(4,"enErr_UseOtherResourceBox_Fail",1571,30241)
V1573M=V(4,"enErr_UseOtherResourceBox_LackOfGoods",1572,30242)
V1574M=V(4,"enErr_UseItemOpenNormalBox_Fail",1573,30243)
V1575M=V(4,"enErr_UseItemOpenNormalBox_LackOfGoods",1574,30244)
V1576M=V(4,"enErr_UseItemResourceFillBox_Fail",1575,30245)
V1577M=V(4,"enErr_UseItemResourceFillBox_LackOfGood",1576,30246)
V1578M=V(4,"enErr_OpenNormalBox_Fail",1577,30247)
V1579M=V(4,"enErr_OpenRandomBox_Fail",1578,30248)
V1580M=V(4,"enErr_OpenSelectBox_Fail",1579,30249)
V1581M=V(4,"enErr_ResourceFillSelectBox_Fail",1580,30250)
V1582M=V(4,"enErr_ResourceFillLvBox_Fail",1581,30251)
V1583M=V(4,"enErr_City_InvalidBuilding",1582,30300)
V1584M=V(4,"enErr_City_NotBrokenState",1583,30301)
V1585M=V(4,"enErr_City_FixConditionCfgError",1584,30302)
V1586M=V(4,"enErr_City_BuildQueueIsFull",1585,30303)
V1587M=V(4,"enErr_City_NotFoundBuilding",1586,30304)
V1588M=V(4,"enErr_City_NotFoundWorker",1587,30305)
V1589M=V(4,"enErr_City_WorkerHasBuilding",1588,30306)
V1590M=V(4,"enErr_City_NotTargetBuilding",1589,30307)
V1591M=V(4,"enErr_City_BuildingFull",1590,30308)
V1592M=V(4,"enErr_City_NotEnoughReplaceCond",1591,30309)
V1593M=V(4,"enErr_City_AlreadyHaveWorker",1592,30310)
V1594M=V(4,"enErr_City_NotEnoughCond",1593,30311)
V1595M=V(4,"enErr_City_WorkerLevelLimit",1594,30312)
V1596M=V(4,"enErr_City_NotGiftState",1595,30313)
V1597M=V(4,"enErr_City_BuildingTypeIsFull",1596,30314)
V1598M=V(4,"enErr_City_NotAllowPosition",1597,30315)
V1599M=V(4,"enErr_City_NotFoundBuildingCfg",1598,30316)
V1600M=V(4,"enErr_City_PositionNotChange",1599,30317)
V1601M=V(4,"enErr_City_RecruitSoldierOverflow",1600,30318)
V1602M=V(4,"enErr_City_NotFoundCfg",1601,30319)
V1603M=V(4,"enErr_City_InvalidNum",1602,30320)
V1604M=V(4,"enErr_City_TimeNotYet",1603,30321)
V1605M=V(4,"enErr_City_TrainningCenterOverflow",1604,30322)
V1606M=V(4,"enErr_City_HospitalOverflow",1605,30323)
V1607M=V(4,"enErr_City_UpgradeSoldierOverflow",1606,30324)
V1608M=V(4,"enErr_City_InvalidLevel",1607,30325)
V1609M=V(4,"enErr_City_WallNotFire",1608,30326)
V1610M=V(4,"enErr_City_GetRewardQueueFail",1609,30327)
V1611M=V(4,"enErr_City_NoResourceErr",1610,30328)
V1612M=V(4,"enErr_City_CityWarDefenseFull",1611,30329)
V1613M=V(4,"enErr_City_BuyDefenseFreeze",1612,30330)
V1614M=V(4,"enErr_City_DroneStarBuildingNoOpen",1613,30331)
V1615M=V(4,"enErr_Hero_MainCityLvErr",1614,30360)
V1616M=V(4,"enErr_Hero_NEW_HeroUpGradeFail",1615,30361)
V1617M=V(4,"enErr_Hero_NEW_SkillUpGradeFail",1616,30362)
V1618M=V(4,"enErr_Hero_NEW_HeroAwakeFail",1617,30363)
V1619M=V(4,"enErr_Hero_NEW_DebrisComposeFail",1618,30364)
V1620M=V(4,"enErr_Hero_NEW_SkillLevelLimitErr",1619,30365)
V1621M=V(4,"enErr_Hero_NEW_DebrisNotEnough",1620,30366)
V1622M=V(4,"enErr_Hero_NEW_SkillUnlockErr",1621,30367)
V1623M=V(4,"enErr_SpeedUp_NotEnoughGoods",1622,30410)
V1624M=V(4,"enErr_SpeedUp_NotEnoughCondition",1623,30411)
V1625M=V(4,"enErr_Composite_NotEnoughGoods",1624,30421)
V1626M=V(4,"enErr_RadarMission_IdError",1625,30430)
V1627M=V(4,"enErr_RadarMission_NotGet",1626,30431)
V1628M=V(4,"enErr_RadarMission_NotAssist",1627,30432)
V1629M=V(4,"enErr_RadarMission_Expire",1628,30433)
V1630M=V(4,"enErr_RadarMission_NoTarget",1629,30434)
V1631M=V(4,"enErr_RadarMission_AlreadyDigging",1630,30435)
V1632M=V(4,"enErr_Not_Do_Join_Alliance_Less_24Hours",1631,30436)
V1633M=V(4,"enErr_RadarMission_Not_Help_Target",1632,30437)
V1634M=V(4,"enErr_RadarMission_NotDigTreasure",1633,30438)
V1635M=V(4,"enErr_RadarMission_ExecuteTask",1634,30439)
V1636M=V(4,"enErr_RadarMission_TargetUpdate",1635,30440)
V1637M=V(4,"enErr_RadarMission_NotOpen",1636,30441)
V1638M=V(4,"enErr_RadarMission_CityInZone",1637,30442)
V1639M=V(4,"enErr_RadarMission_NotFindPos",1638,30443)
V1640M=V(4,"enErr_RadarMission_NotInNativeSandbox",1639,30444)
V1641M=V(4,"enErr_Sandbox_FuncNotOpen",1640,30501)
V1642M=V(4,"enErr_Stamina_NotEnough",1641,30502)
V1643M=V(4,"enErr_Sandbox_CityNotExist",1642,30509)
V1644M=V(4,"enErr_Sandbox_PosError",1643,30510)
V1645M=V(4,"enErr_Sandbox_ExteriorError",1644,30511)
V1646M=V(4,"enErr_Sandbox_TargetNot",1645,30512)
V1647M=V(4,"enErr_Sandbox_TeamInOutside",1646,30513)
V1648M=V(4,"enErr_Sandbox_SearchNotPos",1647,30514)
V1649M=V(4,"enErr_Sandbox_NotAttack",1648,30515)
V1650M=V(4,"enErr_Sandbox_MoveFail",1649,30516)
V1651M=V(4,"enErr_Sandbox_AlreadyInTeam",1650,30517)
V1652M=V(4,"enErr_Sandbox_NotTeam",1651,30518)
V1653M=V(4,"enErr_Sandbox_DetectNum",1652,30519)
V1654M=V(4,"enErr_Sandbox_DetectAlliance",1653,30520)
V1655M=V(4,"enErr_Sandbox_ParamsError",1654,30521)
V1656M=V(4,"enErr_Sandbox_DetectType",1655,30522)
V1657M=V(4,"enErr_Sandbox_AlreadyGoing",1656,30523)
V1658M=V(4,"enErr_Sandbox_MassMemberFull",1657,30524)
V1659M=V(4,"enErr_Sandbox_MassNotMember",1658,30525)
V1660M=V(4,"enErr_Sandbox_MassNotIsLeader",1659,30526)
V1661M=V(4,"enErr_Sandbox_MassIsLeader",1660,30527)
V1662M=V(4,"enErr_Sandbox_MassParamError",1661,30528)
V1663M=V(4,"enErr_Sandbox_TeamError",1662,30529)
V1664M=V(4,"enErr_Sandbox_NotExist",1663,30530)
V1665M=V(4,"enErr_Sandbox_TargetNotCanMass",1664,30531)
V1666M=V(4,"enErr_Sandbox_MassTeamNoJoin",1665,30532)
V1667M=V(4,"enErr_Sandbox_DetectInSafetyTime",1666,30533)
V1668M=V(4,"enErr_Sandbox_ReinforceFull",1667,30534)
V1669M=V(4,"enErr_Sandbox_ReinforceOneTeam",1668,30535)
V1670M=V(4,"enErr_Sandbox_ReinforceAlliance",1669,30536)
V1671M=V(4,"enErr_Sandbox_HaveBuffNoSafety",1670,30537)
V1672M=V(4,"enErr_Sandbox_MoreBuffNoSafety",1671,30538)
V1673M=V(4,"enErr_Sandbox_MassAlreadyInTeam",1672,30539)
V1674M=V(4,"enErr_Sandbox_TeamNotReturn",1673,30540)
V1675M=V(4,"enErr_Sandbox_MassCreateFail",1674,30541)
V1676M=V(4,"enErr_Sandbox_MassInSafetyTime",1675,30542)
V1677M=V(4,"enErr_Sandbox_MarchSoldierDataError",1676,30543)
V1678M=V(4,"enErr_Sandbox_FixedMonsterAsTarget",1677,30544)
V1679M=V(4,"enErr_March_SpeedError",1678,30545)
V1680M=V(4,"enErr_March_NotChangeTarget",1679,30546)
V1681M=V(4,"enErr_March_ChangeTargetError",1680,30547)
V1682M=V(4,"enErr_March_CreateError",1681,30548)
V1683M=V(4,"enErr_March_LineTypeError",1682,30549)
V1684M=V(4,"enErr_March_AddTeamError",1683,30550)
V1685M=V(4,"enErr_March_TeamNotInCity",1684,30551)
V1686M=V(4,"enErr_March_AllianceNotAttack",1685,30552)
V1687M=V(4,"enErr_March_SafetyTimeNotAttack",1686,30553)
V1688M=V(4,"enErr_March_CondNotMeet",1687,30554)
V1689M=V(4,"enErr_Mass_TeamTimeOut",1688,30555)
V1690M=V(4,"enErr_Sandbox_NotCover",1689,30556)
V1691M=V(4,"enErr_March_HasSameTarget",1690,30557)
V1692M=V(4,"enErr_Sandbox_WonderDie",1691,30558)
V1693M=V(4,"enErr_Sandbox_CrossTypeError",1692,30559)
V1694M=V(4,"enErr_Sandbox_AlreayMassTarget",1693,30560)
V1695M=V(4,"enErr_Sandbox_CityInZoneNotSpeedUp",1694,30561)
V1696M=V(4,"enErr_Sandbox_MarchInZoneNotSpeedUp",1695,30562)
V1697M=V(4,"enErr_Sandbox_CurNotSpeedUp",1696,30563)
V1698M=V(4,"enErr_Mass_TeamTimeOutNotPlay",1697,30564)
V1699M=V(4,"enErr_Sandbox_Detect_NoGointStete",1698,30565)
V1700M=V(4,"enErr_Sandbox_Detect_InIdle",1699,30566)
V1701M=V(4,"enErr_Sandbox_VisualAlready",1700,30567)
V1702M=V(4,"enErr_Sandbox_VisualEnterFail",1701,30568)
V1703M=V(4,"enErr_Sandbox_VisualInEntering",1702,30569)
V1704M=V(4,"enErr_Sandbox_MarchNotExit",1703,30570)
V1705M=V(4,"enErr_Sandbox_EntityNotExit",1704,30571)
V1706M=V(4,"enErr_Sandbox_CastleEffectError",1705,30572)
V1707M=V(4,"enErr_Sandbox_TargetInSafeArea",1706,30573)
V1708M=V(4,"enErr_Sandbox_CrossNotAttackTarget",1707,30574)
V1709M=V(4,"enErr_Sandbox_InsufficientRelocationLv",1708,30575)
V1710M=V(4,"enErr_Sandbox_DetectInCDTime",1709,30576)
V1711M=V(4,"enErr_Sandbox_PlateError",1710,30577)
V1712M=V(4,"enErr_Sandbox_AllianceMove",1711,30578)
V1713M=V(4,"enErr_GeneralTrial_ActivityNotOpen",1712,30600)
V1714M=V(4,"enErr_GeneralTrial_ParamsAbnormal",1713,30601)
V1715M=V(4,"enErr_GeneralTrial_SelectDifficultyErr",1714,30602)
V1716M=V(4,"enErr_GeneralTrial_ProduceGeneralErr",1715,30603)
V1717M=V(4,"enErr_GeneralTrial_GetPersonalPrizeErr",1716,30604)
V1718M=V(4,"enErr_GeneralTrial_NotFindAlliance",1717,30605)
V1719M=V(4,"enErr_GeneralTrial_PersonNotEnoughCondition",1718,30606)
V1720M=V(4,"enErr_GeneralTrial_AlreadyBeginChallenge",1719,30607)
V1721M=V(4,"enErr_GeneralTrial_GeneralNotExist",1720,30608)
V1722M=V(4,"enErr_GeneralTrial_ProduceGeneralTooFast",1721,30609)
V1723M=V(4,"enErr_GeneralTrial_JoinTimeNotEnough",1722,30610)
V1724M=V(4,"enErr_GeneralTrial_NotAllianceMassed",1723,30611)
V1725M=V(4,"enErr_GeneralTrial_MonsterTypeError",1724,30612)
V1726M=V(4,"enErr_Role_Update_Schloss_Effect_Fail",1725,30707)
V1727M=V(4,"enErr_Role_Update_Sex_Fail",1726,30708)
V1728M=V(4,"enErr_Role_Sex_Exist",1727,30709)
V1729M=V(4,"enErr_Role_Update_Schloss_Fail",1728,30710)
V1730M=V(4,"enErr_LikeRole_Fail",1729,30711)
V1731M=V(4,"enErr_LikeRole_NotEnough",1730,30712)
V1732M=V(4,"enErr_LikeRole_GetRecordFailed",1731,30713)
V1733M=V(4,"enErr_LikeRole_Self_Fail",1732,30714)
V1734M=V(4,"enErr_LikeRole_Liked",1733,30715)
V1735M=V(4,"enErr_Hero_Equip_Update_Fail",1734,31713)
V1736M=V(4,"enErr_Hero_Equip_Reward_Fail",1735,31714)
V1737M=V(4,"enErr_Hero_Equip_Strength_Fail",1736,31715)
V1738M=V(4,"enErr_Hero_Equip_Make_Fail",1737,31716)
V1739M=V(4,"enErr_Hero_Equip_Dismantle",1738,31717)
V1740M=V(4,"enErr_Hero_Equip_Goods_Craft_Fail",1739,31718)
V1741M=V(4,"enErr_Hero_Equip_Goods_Salvage_Fail",1740,31719)
V1742M=V(4,"enErr_All_Rewards_Received",1741,31750)
V1743M=V(4,"enErr_Reward_Only_Can_Receive_Once",1742,31751)
V1744M=V(4,"enErr_KastenBox_Is_Only_For_Creator",1743,31752)
V1745M=V(4,"enErr_KastenBox_Is_Only_For_Alliance",1744,31753)
V1746M=V(4,"enErr_KastenBox_Join_New_Alliance",1745,31754)
V1747M=V(4,"enErr_Today_Reward_Received",1746,31755)
V1748M=V(4,"enErr_Today_Reward_Times_Limit",1747,31756)
V1749M=V(4,"enErr_AllianceBoss_NotActivityTime",1748,31800)
V1750M=V(4,"enErr_AllianceBoss_NotPrivilege",1749,31801)
V1751M=V(4,"enErr_AllianceBoss_NotInAlliance",1750,31802)
V1752M=V(4,"enErr_AllianceBoss_TimeNotRight",1751,31803)
V1753M=V(4,"enErr_AllianceBoss_WaitForReady",1752,31804)
V1754M=V(4,"enErr_AllianceBoss_NotFoundCfg",1753,31805)
V1755M=V(4,"enErr_AllianceBoss_ServerOpenTimeNotMatch",1754,31806)
V1756M=V(4,"enErr_AllianceBoss_NotUnlocked",1755,31807)
V1757M=V(4,"enErr_AllianceBoss_NotMatchState",1756,31808)
V1758M=V(4,"enErr_AllianceBoss_NotEnoughCond",1757,31809)
V1759M=V(4,"enErr_AllianceBoss_NotRightEntity",1758,31810)
V1760M=V(4,"enErr_AllianceBoss_InCD",1759,31811)
V1761M=V(4,"enErr_AllianceBoss_NotInNativeSandbox",1760,31812)
V1762M=V(4,"enErr_AllianceBoss_NotHaveAllianceMark",1761,31813)
V1763M=V(4,"enErr_AllianceBoss_NewComersCanNotAttack",1762,31814)
V1764M=V(4,"enErr_AllianceAchievement_NotOpen",1763,31850)
V1765M=V(4,"enErr_AllianceAchievement_NotUnlock",1764,31851)
V1766M=V(4,"enErr_AllianceAchievement_IsOver",1765,31852)
V1767M=V(4,"enErr_AllianceAchievement_NeverGetReward",1766,31853)
V1768M=V(4,"enErr_AllianceAchievement_AlreadyGetReward",1767,31854)
V1769M=V(4,"enErr_AllianceInvite_AlreadyInvite",1768,31855)
V1770M=V(4,"enErr_AllianceInvite_AlreadyExpired",1769,31856)
V1771M=V(4,"enErr_AllianceInvite_NoAlliance",1770,31857)
V1772M=V(4,"enErr_AllianceInvite_AlreadyHasAlliance",1771,31858)
V1773M=V(4,"enErr_Alliance_FreeMoveCityOverFail",1772,31859)
V1774M=V(4,"enErr_TaskMain_RechargeLimit",1773,31861)
V1775M=V(4,"enErr_Activity_NotOpen",1774,31862)
V1776M=V(4,"enErr_HangUp_GetRewardFail",1775,31863)
V1777M=V(4,"enErr_CampTrial_NotData",1776,31901)
V1778M=V(4,"enErr_CampTrial_NotType",1777,31902)
V1779M=V(4,"enErr_CampTrial_NotOpen",1778,31903)
V1780M=V(4,"enErr_CampTrial_NotDifficulty",1779,31904)
V1781M=V(4,"enErr_CampTrial_NotTrialID",1780,31905)
V1782M=V(4,"enErr_CampTrial_UnLocked_Type",1781,31911)
V1783M=V(4,"enErr_CampTrial_UnLock_Quick",1782,31912)
V1784M=V(4,"enErr_CampTrial_UnLock_Difficulty",1783,31913)
V1785M=V(4,"enErr_CampTrial_SelectedDifficulty",1784,31914)
V1786M=V(4,"enErr_CampTrial_Unselected",1785,31915)
V1787M=V(4,"enErr_CampTrial_Only_Difficulty_Max",1786,31921)
V1788M=V(4,"enErr_CampTrial_Only_Chall_SameDifficulty",1787,31922)
V1789M=V(4,"enErr_CampTrial_Only_Next_Layer",1788,31923)
V1790M=V(4,"enErr_CampTrial_Hero_NoOne",1789,31931)
V1791M=V(4,"enErr_CampTrial_Hero_TooMany",1790,31932)
V1792M=V(4,"enErr_CampTrial_Hero_Repeat",1791,31933)
V1793M=V(4,"enErr_CampTrial_Hero_NotExist",1792,31934)
V1794M=V(4,"enErr_CampTrial_Hero_ChallOther",1793,31935)
V1795M=V(4,"enErr_CampTrial_InBattle",1794,31941)
V1796M=V(4,"enErr_CampTrial_MonsterTroop",1795,31942)
V1797M=V(4,"enErr_CampTrial_EnterBattleErr",1796,31943)
V1798M=V(4,"enErr_CampTrial_AsyncBattle_Reset",1797,31944)
V1799M=V(4,"enErr_CampTrial_Chall_Completed",1798,31945)
V1800M=V(4,"enErr_NC_DetectNotInBattle",1799,32001)
V1801M=V(4,"enErr_NC_DW_NotPower",1800,32002)
V1802M=V(4,"enErr_NC_DW_NotEnoughTime",1801,32003)
V1803M=V(4,"enErr_NC_DW_NotEnoughPerson",1802,32004)
V1804M=V(4,"enErr_NC_DW_InProtect",1803,32005)
V1805M=V(4,"enErr_NC_DW_MaxNum",1804,32006)
V1806M=V(4,"enErr_NC_DW_DayMaxNum",1805,32007)
V1807M=V(4,"enErr_NC_DW_OwnMaxNum",1806,32008)
V1808M=V(4,"enErr_NC_DW_NotHaveNear",1807,32009)
V1809M=V(4,"enErr_NC_DW_NotEnoughMaxLv",1808,32010)
V1810M=V(4,"enErr_NC_DW_NotDW",1809,32011)
V1811M=V(4,"enErr_NC_AA_NotPower",1810,32012)
V1812M=V(4,"enErr_NC_AA_NotEnoughTime",1811,32013)
V1813M=V(4,"enErr_NC_AA_InCD",1812,32014)
V1814M=V(4,"enErr_NC_AA_NotInBattle",1813,32015)
V1815M=V(4,"enErr_NC_AA_NotPerson",1814,32016)
V1816M=V(4,"enErr_NC_Reward_NotAlliance",1815,32017)
V1817M=V(4,"enErr_NC_Reward_NotOwn",1816,32018)
V1818M=V(4,"enErr_NC_Reward_Params",1817,32019)
V1819M=V(4,"enErr_NC_Reward_NotHave",1818,32020)
V1820M=V(4,"enErr_NC_Reward_NotFirst",1819,32021)
V1821M=V(4,"enErr_NC_Reward_Already",1820,32022)
V1822M=V(4,"enErr_NC_Abandon_NotAlliance",1821,32023)
V1823M=V(4,"enErr_NC_Abandon_NotPower",1822,32024)
V1824M=V(4,"enErr_NC_Abandon_NotOwner",1823,32025)
V1825M=V(4,"enErr_NC_Abandon_Params",1824,32026)
V1826M=V(4,"enErr_NC_Abandon_NotAbandon",1825,32027)
V1827M=V(4,"enErr_NC_Abandon_InCooling",1826,32028)
V1828M=V(4,"enErr_NC_Abandon_CanNot",1827,32029)
V1829M=V(4,"enErr_NC_DetectNotMine",1828,32030)
V1830M=V(4,"enErr_NC_ATK_NotAlliacne",1829,32031)
V1831M=V(4,"enErr_NC_ATK_CongressProtect",1830,32032)
V1832M=V(4,"enErr_NC_ATK_CongressNear",1831,32033)
V1833M=V(4,"enErr_NC_ATK_CongressMax",1832,32034)
V1834M=V(4,"enErr_NC_ATK_NotDW",1833,32035)
V1835M=V(4,"enErr_NC_ATK_NotDWTime",1834,32036)
V1836M=V(4,"enErr_SX_REINFORCE_NOTTEAM",1835,32037)
V1837M=V(4,"enErr_SX_ZONE_NOTSAFETY",1836,32038)
V1838M=V(4,"enErr_NC_ALLIANCE_NOT_DECLAREWAR",1837,32039)
V1839M=V(4,"enErr_Carriage_TargetNotExit",1838,33900)
V1840M=V(4,"enErr_Carriage_CrossServerNotTruckGo",1839,33901)
V1841M=V(4,"enErr_Carriage_NotInCrossGroups",1840,33902)
V1842M=V(4,"enErr_Carriage_NoDepart",1841,34000)
V1843M=V(4,"enErr_Carriage_NoTrade",1842,34001)
V1844M=V(4,"enErr_Carriage_TeamErr",1843,34002)
V1845M=V(4,"enErr_Carriage_TeamLocked",1844,34003)
V1846M=V(4,"enErr_Carriage_TeamInUse",1845,34004)
V1847M=V(4,"enErr_Carriage_BattleError",1846,34005)
V1848M=V(4,"enErr_Carriage_NoTradeCnt",1847,34006)
V1849M=V(4,"enErr_Carriage_NoLootCnt",1848,34007)
V1850M=V(4,"enErr_Carriage_NoBeLootCnt",1849,34008)
V1851M=V(4,"enErr_Carriage_NoCityInfo",1850,34009)
V1852M=V(4,"enErr_Carriage_AlreadyGoing",1851,34010)
V1853M=V(4,"enErr_Carriage_GoingError",1852,34011)
V1854M=V(4,"enErr_Carriage_NotGetAward",1853,34012)
V1855M=V(4,"enErr_Carriage_NotOtherAward",1854,34013)
V1856M=V(4,"enErr_Carriage_InBattle",1855,34014)
V1857M=V(4,"enErr_Carriage_NotLootSelf",1856,34015)
V1858M=V(4,"enErr_Carriage_NotLootAlliance",1857,34016)
V1859M=V(4,"enErr_Carriage_RoleDataNoLoad",1858,34017)
V1860M=V(4,"enErr_Carriage_NotOpenSystem",1859,34018)
V1861M=V(4,"enErr_FullBattle_NoBuyCnt",1860,34020)
V1862M=V(4,"enErr_FullBattle_NotExist",1861,34021)
V1863M=V(4,"enErr_FullBattle_NotEnoughItem",1862,34022)
V1864M=V(4,"enErr_FullBattle_EnoughLimit",1863,34023)
V1865M=V(4,"enErr_FullBattle_NotEnoughLotteryCnt",1864,34025)
V1866M=V(4,"enErr_AllianceDuel_NoAttend",1865,34100)
V1867M=V(4,"enErr_AllianceDuel_NoRANKTHEMEID",1866,34101)
V1868M=V(4,"enErr_AllianceDuel_RANKTHEMEID_ERROR",1867,34102)
V1869M=V(4,"enErr_AllianceDuel_BattleNoAttend",1868,34103)
V1870M=V(4,"enErr_AllianceDuel_BattleNoUpDown",1869,34104)
V1871M=V(4,"enErr_AllianceDuel_end",1870,34149)
V1872M=V(4,"enErr_AcornPub_NotExistTask",1871,34150)
V1873M=V(4,"enErr_AcornPub_NotInNativeSandbox",1872,34151)
V1874M=V(4,"enErr_AcornPub_NotFoundCfg",1873,34152)
V1875M=V(4,"enErr_AcornPub_NotFoundSandboxBase",1874,34153)
V1876M=V(4,"enErr_AcornPub_WaitForReady",1875,34154)
V1877M=V(4,"enErr_AcornPub_NotExistTaskEntity",1876,34155)
V1878M=V(4,"enErr_AcornPub_TaskDoing",1877,34156)
V1879M=V(4,"enErr_AcornPub_HeroNotExist",1878,34157)
V1880M=V(4,"enErr_AcornPub_ConfigErr",1879,34158)
V1881M=V(4,"enErr_AcornPub_ConditionErr",1880,34159)
V1882M=V(4,"enErr_AcornPub_TaskHelped",1881,34160)
V1883M=V(4,"enErr_AcornPub_HelpLimit",1882,34161)
V1884M=V(4,"enErr_AcornPub_TaskCantNotHelpSelf",1883,34162)
V1885M=V(4,"enErr_AcornPub_TaskSnatchLimit",1884,34163)
V1886M=V(4,"enErr_AcornPub_TaskCantNotSnatchSelf",1885,34164)
V1887M=V(4,"enErr_AcornPub_SnatchLimit",1886,34165)
V1888M=V(4,"enErr_AcornPub_TaskNotFinish",1887,34166)
V1889M=V(4,"enErr_AcornPub_AllianceCantNotSnatch",1888,34167)
V1890M=V(4,"enErr_AcornPub_AlreadySnatchTask",1889,34168)
V1891M=V(4,"enErr_AcornPub_AlreadyReward",1890,34169)
V1892M=V(4,"enErr_AcornPub_SuperRefreshNotOpen",1891,34170)
V1893M=V(4,"enErr_AcornPub_SuperRefreshFailed",1892,34171)
V1894M=V(4,"enErr_AcornPub_NotHaveRewardTask",1893,34172)
V1895M=V(4,"enErr_AcornPub_NotEnoughTaskQueue",1894,34173)
V1896M=V(4,"enErr_AcornPub_NotFoundPosPlzMoveBase",1895,34174)
V1897M=V(4,"enErr_AcornPub_NotAllowSendTaskInZone1",1896,34175)
V1898M=V(4,"enErr_Congress_Official_NotData",1897,34200)
V1899M=V(4,"enErr_Congress_Official_NotConfig",1898,34201)
V1900M=V(4,"enErr_Congress_Official_NotCurSeason",1899,34202)
V1901M=V(4,"enErr_Congress_Official_Repeat",1900,34203)
V1902M=V(4,"enErr_Congress_Official_DiffWorld",1901,34204)
V1903M=V(4,"enErr_Congress_Official_BaseLev",1902,34205)
V1904M=V(4,"enErr_Congress_Appoint_NotAuthority",1903,34211)
V1905M=V(4,"enErr_Congress_Appoint_NoOperate",1904,34212)
V1906M=V(4,"enErr_Congress_Appoint_CD",1905,34213)
V1907M=V(4,"enErr_Congress_Appoint_IsSelf",1906,34214)
V1908M=V(4,"enErr_Congress_Appoint_CannotChangeTarget",1907,34215)
V1909M=V(4,"enErr_Congress_Dismiss_NotAuthority",1908,34221)
V1910M=V(4,"enErr_Congress_Dismiss_NoOperate",1909,34222)
V1911M=V(4,"enErr_Congress_Dismiss_NotRole",1910,34223)
V1912M=V(4,"enErr_Congress_Dismiss_IsSelf",1911,34224)
V1913M=V(4,"enErr_Congress_ApplyFor_NotQueueData",1912,34231)
V1914M=V(4,"enErr_Congress_ApplyFor_NotAuthority",1913,34232)
V1915M=V(4,"enErr_Congress_ApplyFor_QueueFull",1914,34233)
V1916M=V(4,"enErr_Congress_ApplyFor_NotFindRole",1915,34234)
V1917M=V(4,"enErr_Congress_ApplyFor_NoOperate",1916,34241)
V1918M=V(4,"enErr_Congress_ApplyFor_Full",1917,34242)
V1919M=V(4,"enErr_Congress_ApplyFor_CD",1918,34243)
V1920M=V(4,"enErr_Congress_ApplyFor_HasPosition",1919,34244)
V1921M=V(4,"enErr_Congress_ApplyFor_InList",1920,34245)
V1922M=V(4,"enErr_Congress_ApplyFor_InQueueList",1921,34246)
V1923M=V(4,"enErr_Congress_ApplyFor_NotInList",1922,34247)
V1924M=V(4,"enErr_Congress_ApplyFor_NoPresident",1923,34248)
V1925M=V(4,"enErr_Congress_QueueUp_NotData",1924,34251)
V1926M=V(4,"enErr_Congress_QueueUp_Set_NotAuthority",1925,34252)
V1927M=V(4,"enErr_Congress_QueueUp_Set_NotCfg",1926,34253)
V1928M=V(4,"enErr_Congress_QueueUp_NotFindRole",1927,34254)
V1929M=V(4,"enErr_Congress_QueueUp_Kick_NotAuthority",1928,34255)
V1930M=V(4,"enErr_Congress_OfficialRecord_NotData",1929,34261)
V1931M=V(4,"enErr_Congress_OfficialRecord_NotOfficial",1930,34262)
V1932M=V(4,"enErr_Congress_Award_NoAuthority",1931,34271)
V1933M=V(4,"enErr_Congress_Award_DiffWorld",1932,34272)
V1934M=V(4,"enErr_Congress_Award_NotCfg",1933,34273)
V1935M=V(4,"enErr_Congress_Award_BaseLev",1934,34274)
V1936M=V(4,"enErr_Congress_Award_Getted",1935,34275)
V1937M=V(4,"enErr_Congress_Award_NotPres",1936,34276)
V1938M=V(4,"enErr_Congress_Award_Limit",1937,34277)
V1939M=V(4,"enErr_Congress_Award_PresNotGetOther",1938,34278)
V1940M=V(4,"enErr_Congress_President_ManifestoLimit",1939,34281)
V1941M=V(4,"enErr_Congress_President_MailContentLimit",1940,34282)
V1942M=V(4,"enErr_Congress_President_MailContentFail",1941,34283)
V1943M=V(4,"enErr_Congress_President_MailSendCD",1942,34284)
V1944M=V(4,"enErr_Congress_NotOpen",1943,34291)
V1945M=V(4,"enErr_GetReward_SoldiersNotSpace",1944,34300)
V1946M=V(4,"enErr_SpecialGift_NotStart",1945,34301)
V1947M=V(4,"enErr_SpecialGift_IsLimited",1946,34302)
V1948M=V(4,"enErr_SpecialGift_Max",1947,34305)
V1949M=V(4,"enErr_MonthCardGift_NotActived",1948,34306)
V1950M=V(4,"enErr_MonthCardGift_LIMITBUY",1949,34307)
V1951M=V(4,"enErr_MonthCardGift_Max",1950,34310)
V1952M=V(4,"enErr_ShopNotEnough",1951,34400)
V1953M=V(4,"enErr_RechargeGift_ErrParams",1952,34500)
V1954M=V(4,"enErr_RechargeGift_NotFindRechargeInfo",1953,34501)
V1955M=V(4,"enErr_RechargeGift_EndTime",1954,34502)
V1956M=V(4,"enErr_RechargeGift_BuyTimesOverLimit",1955,34503)
V1957M=V(4,"enErr_RechargeGift_NotOpen",1956,34504)
V1958M=V(4,"enErr_RechargeGift_NotSelectGift",1957,34505)
V1959M=V(4,"enErr_RechargeGift_MutuallyExclusive",1958,34506)
V1960M=V(4,"enErr_RechargeGift_CanNotSelect",1959,34507)
V1961M=V(4,"enErr_RechargeGift_StartTime",1960,34508)
V1962M=V(4,"enErr_RechargeGift_NotBuyPrevious",1961,34509)
V1963M=V(4,"enErr_RechargeGift_FunctionOpen",1962,34510)
V1964M=V(4,"enErr_AllianceDuel_MoveCityCD",1963,34600)
V1965M=V(4,"enErr_AllianceDuel_BattleNotNeed",1964,34601)
V1966M=V(4,"enErr_AllianceDuel_ThemeIdError",1965,34602)
V1967M=V(4,"enErr_AllianceDuel_AlliancePosErr",1966,34603)
V1968M=V(4,"enErr_AllianceDuel_ConfigError",1967,34604)
V1969M=V(4,"enErr_AllianceDuel_StatusError",1968,34605)
V1970M=V(4,"enErr_HonorWall_NotOpen",1969,34700)
V1971M=V(4,"enErr_HonorWall_NotHeroConf",1970,34701)
V1972M=V(4,"enErr_HonorWall_HeroNotEnoughStar",1971,34702)
V1973M=V(4,"enErr_HonorWall_Top",1972,34703)
V1974M=V(4,"enErr_HonorWall_ShardNotEnought",1973,34704)
V1975M=V(4,"enErr_Idle_IsNotMiniGame",1974,34900)
V1976M=V(4,"enErr_UseItem_SandboxNotExist",1975,35446)
V1977M=V(4,"enErr_DroneCenter_NotEnoughCondition",1976,35447)
V1978M=V(4,"enErr_DroneCenter_DroneLevelReachMax",1977,35448)
V1979M=V(4,"enErr_DroneCenter_NotEnoughGoods",1978,35449)
V1980M=V(4,"enErr_DroneCenter_PartLevelReachMax",1979,35450)
V1981M=V(4,"enErr_DroneCenter_AdvanceNotNextLv",1980,35451)
V1982M=V(4,"enErr_DroneCenter_AdvanceNotMaxLv",1981,35452)
V1983M=V(4,"enErr_DroneCenter_TeamIndexNotUnlock",1982,35453)
V1984M=V(4,"enErr_DroneCenter_StarSchemeNotUnlock",1983,35454)
V1985M=V(4,"enErr_DroneCenter_NotStar",1984,35455)
V1986M=V(4,"enErr_DroneCenter_StarLock",1985,35456)
V1987M=V(4,"enErr_DroneCenter_NotStarType",1986,35457)
V1988M=V(4,"enErr_DroneCenter_NotStarLvUp",1987,35458)
V1989M=V(4,"enErr_DroneCenter_NotUnLockStar",1988,35459)
V1990M=V(4,"enErr_DroneCenter_UnlockStarErr",1989,35460)
V1991M=V(4,"enErr_DroneCenter_EquipStarErr",1990,35461)
V1992M=V(4,"enErr_DroneCenter_NotStarLvUpCost",1991,35462)
V1993M=V(4,"enErr_DroneCenter_CostStarErr",1992,35463)
V1994M=V(4,"enErr_DroneCenter_ZeroStarNotReset",1993,35464)
V1995M=V(4,"enErr_DroneCenter_TeamIndexErr",1994,35465)
V1996M=V(4,"enErr_DroneCenter_HasTeamNotIdle",1995,35466)
V1997M=V(4,"enErr_DroneCenter_HasTeam",1996,35467)
V1998M=V(4,"enErr_DroneCenter_PlaceHasSid",1997,35468)
V1999M=V(4,"enErr_DroneCenter_UnlockEquipStarErr",1998,35469)
V2000M=V(4,"enErr_DroneCenter_EquipExchangeErr",1999,35470)
V2001M=V(4,"enErr_DroneCenter_EquipTypeErr",2000,35471)
V2002M=V(4,"enErr_DroneCenter_StarTypeErr",2001,35472)
V2003M=V(4,"enErr_DroneCenter_SetTeamSchemeErr",2002,35473)
V2004M=V(4,"enErr_AllianceRecord_NoData",2003,35500)
V2005M=V(4,"enErr_AllianceRecord_RecordIsNull",2004,35501)
V2006M=V(4,"enErr_Award_Claimed",2005,35550)
V2007M=V(4,"enErr_Point_NotEnoughGet",2006,35551)
V2008M=V(4,"enErr_AllianceMassMove_NoEnough",2007,35560)
V2009M=V(4,"enErr_ZoneBattleDuel_NotEnoughScore",2008,35600)
V2010M=V(4,"enErr_ZoneBattleDuel_NotEnd",2009,35601)
V2011M=V(4,"enErr_ZoneBattleDuel_Rewarded",2010,35602)
V2012M=V(4,"enErr_ZoneBattleDuel_Not_Top_Scorer",2011,35603)
V2013M=V(4,"enErr_ZoneBattleDuel_Activity_Not_Open",2012,35604)
V2014M=V(4,"enErr_ZoneBattleDuel_NOT_WINNING_SIDE",2013,35605)
V2015M=V(4,"enErr_ZoneBattleDuel_Not_Enough_Integral",2014,35606)
V2016M=V(4,"enErr_ZoneBattleDuel_HAVE_MATCH_GAME",2015,35607)
V2017M=V(4,"enErr_FunctionOpen_NotServerDays",2016,35650)
V2018M=V(4,"enErr_FunctionOpen_NotRoleCreateDays",2017,35651)
V2019M=V(4,"enErr_FunctionOpen_NotHooklevelID",2018,35652)
V2020M=V(4,"enErr_FunctionOpen_NotBuildingLevel",2019,35653)
V2021M=V(4,"enErr_FunctionOpen_NotEventID",2020,35654)
V2022M=V(4,"enErr_FunctionOpen_NotScientificLevel",2021,35655)
V2023M=V(4,"enErr_FunctionOpen_NotSpecialCondition",2022,35656)
V2024M=V(4,"enErr_FunctionOpen_NotCloseServerDays",2023,35657)
V2025M=V(4,"enErr_FunctionOpen_NotCloseBuildingLevel",2024,35658)
V2026M=V(4,"enErr_Common_Using",2025,35700)
V2027M=V(4,"enErr_Common_Not_Find_Data",2026,35701)
V2028M=V(4,"enErr_Common_Max",2027,35800)
V2029M=V(4,"enErr_AllianceTrain_AllianceIdErr",2028,35801)
V2030M=V(4,"enErr_AllianceTrain_NoAllianceTrain",2029,35802)
V2031M=V(4,"enErr_AllianceTrain_NoTrainHead",2030,35803)
V2032M=V(4,"enErr_AllianceTrain_GetSandBoxSidErr",2031,35804)
V2033M=V(4,"enErr_AllianceTrain_TeamIndexErr",2032,35805)
V2034M=V(4,"enErr_AllianceTrain_TeamLineUpErr",2033,35806)
V2035M=V(4,"enErr_AllianceTrain_SetCacheErr",2034,35807)
V2036M=V(4,"enErr_allianceTrain_SaveTeam_Failed",2035,35808)
V2037M=V(4,"enErr_allianceTrain_GetTeam_Failed",2036,35809)
V2038M=V(4,"enErr_allianceTrain_NoAllianceLineUp",2037,35810)
V2039M=V(4,"enErr_allianceTrain_AttackTooFast",2038,35811)
V2040M=V(4,"enErr_allianceTrain_AllianceProtected",2039,35812)
V2041M=V(4,"enErr_AllianceTrain_GetTrainHeadErr",2040,35813)
V2042M=V(4,"enErr_AllianceTrain_AttackTrainErr",2041,35814)
V2043M=V(4,"enErr_allianceTrain_GetTrainRewardErr",2042,35815)
V2044M=V(4,"enErr_allianceTrain_SetLootTimesErr",2043,35816)
V2045M=V(4,"enErr_allianceTrain_NotOpen",2044,35817)
V2046M=V(4,"enErr_allianceTrain_GetBattleHistoryErr",2045,35818)
V2047M=V(4,"enErr_AllianceTrain_SwapLineUpFail",2046,35819)
V2048M=V(4,"enErr_AllianceTrain_TargetNotExit",2047,35820)
V2049M=V(4,"enErr_AllianceTrain_GetWorldGroupErr",2048,35821)
V2050M=V(4,"enErr_AllianceTrain_GetAllianceTrainErr",2049,35822)
V2051M=V(4,"enErr_AllianceTrain_TrainIsLock",2050,35823)
V2052M=V(4,"enErr_AllianceTrain_JoinTimeNotEnough",2051,35824)
V2053M=V(4,"enErr_DesertStrom_GetAllianceScoreErr",2052,36001)
V2054M=V(4,"enErr_DesertStrom_GetAllianceRankErr",2053,36002)
V2055M=V(4,"enErr_DesertStrom_GetRoleScoreErr",2054,36003)
V2056M=V(4,"enErr_DesertStrom_MoveCityCD",2055,36050)
V2057M=V(4,"enErr_DesertStrom_EnemySafeAreaNotMove",2056,36051)
V2058M=V(4,"enErr_DesertStrom_EnemySafeAreaNotAttackDetect",2057,36052)
V2059M=V(4,"enErr_DesertStrom_Common_ServerNotOpen",2058,36100)
V2060M=V(4,"enErr_DesertStrom_Common_RoleNotOpen",2059,36101)
V2061M=V(4,"enErr_DesertStrom_Common_NotAlliance",2060,36102)
V2062M=V(4,"enErr_DesertStrom_Common_TeamErr",2061,36103)
V2063M=V(4,"enErr_DesertStrom_Common_NotExpel",2062,36104)
V2064M=V(4,"enErr_DesertStrom_SignUp_SetTime_NotInStage",2063,36117)
V2065M=V(4,"enErr_DesertStrom_SignUp_SetMember_NotInStage",2064,36118)
V2066M=V(4,"enErr_DesertStrom_SignUp_SetHope_NotInStage",2065,36119)
V2067M=V(4,"enErr_DesertStrom_SignUp_NotInStage",2066,36120)
V2068M=V(4,"enErr_DesertStrom_SignUp_HashData",2067,36121)
V2069M=V(4,"enErr_DesertStrom_SignUp_CreateAllianceTime",2068,36122)
V2070M=V(4,"enErr_DesertStrom_SignUp_JoinAllianceTime",2069,36123)
V2071M=V(4,"enErr_DesertStrom_SignUp_BaseLevel",2070,36124)
V2072M=V(4,"enErr_DesertStrom_SignUp_AllianceAuthority",2071,36125)
V2073M=V(4,"enErr_DesertStrom_SignUp_AllianceRank",2072,36126)
V2074M=V(4,"enErr_DesertStrom_SignUp_DiffAlliance",2073,36127)
V2075M=V(4,"enErr_DesertStrom_SignUp_Repeat",2074,36129)
V2076M=V(4,"enErr_DesertStrom_SignUp_NotYet",2075,36130)
V2077M=V(4,"enErr_DesertStrom_SignUp_BattleTimeErr",2076,36131)
V2078M=V(4,"enErr_DesertStrom_SignUp_BattleTimeRepeat",2077,36132)
V2079M=V(4,"enErr_DesertStrom_SignUp_SetMemberSame",2078,36133)
V2080M=V(4,"enErr_DesertStrom_SignUp_MemberFull_Main",2079,36134)
V2081M=V(4,"enErr_DesertStrom_SignUp_MemberFull_Sub",2080,36135)
V2082M=V(4,"enErr_DesertStrom_SignUp_SetMemberAbnormal",2081,36136)
V2083M=V(4,"enErr_DesertStrom_SignUp_BattleTimeModifyAgain",2082,36137)
V2084M=V(4,"enErr_DesertStrom_Battle_NotInStage",2083,36140)
V2085M=V(4,"enErr_DesertStrom_Battle_HashData",2084,36141)
V2086M=V(4,"enErr_DesertStrom_Battle_RoleNotJoin",2085,36142)
V2087M=V(4,"enErr_DesertStrom_Battle_TimeCfgErr",2086,36143)
V2088M=V(4,"enErr_DesertStrom_Battle_EnterEarly_Main",2087,36144)
V2089M=V(4,"enErr_DesertStrom_Battle_EnterEarly_Sub",2088,36145)
V2090M=V(4,"enErr_DesertStrom_Battle_EnterEnd",2089,36146)
V2091M=V(4,"enErr_DesertStrom_Battle_OtherSandbox",2090,36147)
V2092M=V(4,"enErr_DesertStrom_Battle_LeaveEvery",2091,36148)
V2093M=V(4,"enErr_DesertStrom_Battle_MemberFull",2092,36149)
V2094M=V(4,"enErr_DesertStrom_Battle_CityInZone1",2093,36150)
V2095M=V(4,"enErr_DesertStrom_Battle_NoSandbox",2094,36151)
V2096M=V(4,"enErr_DesertStrom_Battle_NotInSandbox",2095,36152)
V2097M=V(4,"enErr_DesertStrom_Battle_InOtherSandbox",2096,36153)
V2098M=V(4,"enErr_DesertStrom_CreateCityFail",2097,36154)
V2099M=V(4,"enErr_DesertStrom_Battle_HasAllianceMember",2098,36155)
V2100M=V(4,"enErr_DesertStrom_Battle_ResouceIsEmpty",2099,36156)
V2101M=V(4,"enErr_DesertStrom_Battle_NotOpenTime",2100,36157)
V2102M=V(4,"enErr_DesertStrom_BuildInSafeState",2101,36158)
V2103M=V(4,"enErr_ZombieComing_NotFindGold",2102,36300)
V2104M=V(4,"enErr_ZombieComing_GoalHasDeath",2103,36301)
V2105M=V(4,"enErr_AtyGathering_NotOpen",2104,36350)
V2106M=V(4,"enErr_AtyGathering_BaseLvNoEnough",2105,36351)
V2107M=V(4,"enErr_AtyGathering_SearchLvNoEnough",2106,36352)
V2108M=V(4,"enErr_ShelfGoodsItemErr",2107,36370)
V2109M=V(4,"enErr_ShelfGoodsItemNotEnough",2108,36371)
V2110M=V(4,"enErr_ShelfGoodOrderNotExist",2109,36372)
V2111M=V(4,"enErr_ShelfGoodCreateOrder",2110,36373)
V2112M=V(4,"enErr_ShelfGoodOrderTransacted",2111,36374)
V2113M=V(4,"enErr_ShelfGoodOrderTransacting",2112,36375)
V2114M=V(4,"enErr_ShelfGoodHadOrder",2113,36376)
V2115M=V(4,"enErr_GearSupply_NotHasKey",2114,36390)
V2116M=V(4,"enErr_GearSupply_BigRewardIsEmpty",2115,36391)
V2117M=V(4,"enErr_GearSupply_ActivityNotOpen",2116,36392)
V2118M=V(4,"enErr_ChooseWeekCard_NotData",2117,36401)
V2119M=V(4,"enErr_ChooseWeekCard_Buyed",2118,36402)
V2120M=V(4,"enErr_ChooseWeekCard_NotCfg",2119,36403)
V2121M=V(4,"enErr_ChooseWeekCard_ChooseNum",2120,36404)
V2122M=V(4,"enErr_ChooseWeekCard_ChooseItem",2121,36405)
V2123M=V(4,"enErr_ChooseWeekCard_NotBuy",2122,36406)
V2124M=V(4,"enErr_ChooseWeekCard_Get",2123,36407)
V2125M=V(4,"enErr_GoldenEggs_DataErr",2124,36411)
V2126M=V(4,"enErr_GoldenEggs_NotHaved",2125,36412)
V2127M=V(4,"enErr_GoldenEggs_HavedLike",2126,36413)
V2128M=V(4,"enErr_GoldenEggs_Expira",2127,36414)
V2129M=V(4,"enErr_GoldenEggs_Haved",2128,36415)
V2130M=V(4,"enErr_GoldenEggs_NotRewardTimes",2129,36416)
V2131M=V(4,"enErr_GoldenEggs_limit",2130,36417)
V2132M=V(4,"enErr_GoldenEggs_NotReward",2131,36418)
V2133M=V(4,"enErr_GoldenEggs_SendChatErr",2132,36419)
V2134M=V(4,"enErr_GoldenEggs_ZoneBattleDuel",2133,36420)
V2135M=V(4,"enErr_SevenDayLogin_CannotGot",2134,36451)
V2136M=V(4,"enErr_SevenDayLogin_RepeatGotAward",2135,36452)
V2137M=V(4,"enErr_ZombieApocalypse_ActivityNotOpen",2136,36460)
V2138M=V(4,"enErr_ZombieApocalypse_GetAllianceIdErr",2137,36461)
V2139M=V(4,"enErr_ZombieApocalypse_GetActivityDataErr",2138,36462)
V2140M=V(4,"enErr_ZombieApocalypse_NoPermissions",2139,36463)
V2141M=V(4,"enErr_ZombieApocalypse_NoEnoughCondition",2140,36464)
V2142M=V(4,"enErr_ZombieApocalypse_LoadPlayerInfoFail",2141,36465)
V2143M=V(4,"enErr_ZombieApocalypse_HasAppointmentInfo",2142,36466)
V2144M=V(4,"enErr_ZombieApocalypse_NotHasAppointmentInfo",2143,36467)
V2145M=V(4,"enErr_ZombieApocalypse_GetPassBoxErr",2144,36468)
V2146M=V(4,"enErr_ZombieApocalypse_NotSameAlliance",2145,36469)
V2147M=V(4,"enErr_ZombieApocalypse_NotSameWorldId",2146,36470)
V2148M=V(4,"enErr_ZombieApocalypse_CheckPosionFail",2147,36471)
V2149M=V(4,"enErr_ZombieApocalypse_ActivityNotRunning",2148,36472)
V2150M=V(4,"enErr_MonsterComing_Reward_Already",2149,36480)
V2151M=V(4,"enErr_MonsterComing_Stage_NotOpen",2150,36481)
V2152M=V(4,"enErr_MonsterComing_Task_Unfinished",2151,36482)
V2153M=V(4,"enErr_MonsterComing_Unfinished_MiniGame",2152,36483)
V2154M=V(4,"enErr_MonsterComing_NotOpen",2153,36484)
V2155M=V(4,"enErr_AllianceOutFire_NoLeagueMember",2154,36501)
V2156M=V(4,"enErr_AllianceOutFire_NoFire",2155,36502)
V2157M=V(4,"enErr_AllianceTodo_ModuleNoOpen",2156,36521)
V2158M=V(4,"enErr_AllianceTodo_AlreadyGet",2157,36522)
V2159M=V(4,"enErr_AllianceTodo_ExitTimeDontGet",2158,36523)
V2160M=V(4,"enErr_AllianceTodo_NoAuthority",2159,36524)
V2161M=V(4,"enErr_AllianceTodo_TaskDontGet",2160,36525)
V2162M=V(4,"enErr_StrayDog_NoAward",2161,36541)
V2163M=V(4,"enErr_Role_Plate_Fail",2162,36551)
V2164M=V(4,"enErr_RoleFrame_2",2163,36552)
V2165M=V(4,"enErr_RoleFrame_3",2164,36553)
V2166M=V(4,"enErr_RoleFrame_4",2165,36554)
V2167M=V(4,"enErr_ActorCreateLevelInvalid",2166,36561)
V2168M=V(4,"enErr_ActorCreateLevelChallengeInvalid",2167,36562)
V2169M=V(4,"enErr_ActorCreateCityBuildingInvalid",2168,36563)
V2170M=V(4,"enErr_ActorCreateRepairSidInvalid",2169,36564)
V2171M=V(4,"enErr_ActorCreateOpenGiftSidInvalid",2170,36565)
V2172M=V(4,"enErr_Halloween_ChestHadOpenedMaxToday",2171,36571)
V2173M=V(4,"enErr_Halloween_Item_Recycle_Fail",2172,36572)
V2174M=V(4,"enErr_SiegeTreasureDailyTimesLimit",2173,36601)
V2175M=V(4,"enErr_NationalFlag_NotFlagType",2174,36610)
V2176M=V(4,"enErr_NationalFlag_NotOpen",2175,36611)
V2177M=V(4,"enErr_NationalFlag_NotSKDChannel",2176,36612)
V2178M=V(4,"enErr_NationalFlag_NotCountryID",2177,36613)
V2179M=V(4,"enErr_NationalFlag_NotLanguageId",2178,36614)
V2180M=V(4,"enErr_NationalFlag_NotR5",2179,36615)
V2181M=V(4,"enErr_NationalFlag_NotAlliance",2180,36616)
V2182M=V(4,"enErr_NationalFlag_NotConfig",2181,36617)
V2183M=V(4,"enErr_NationalFlag_R5NotCondition",2182,36618)
V2184M=V(4,"enErr_NationalFlag_IN_CD",2183,36619)
V2185M=V(4,"enErr_AllianceGiftPrivlege_AllGetNoOpen",2184,36625)
V2186M=V(4,"enErr_AllianceGiftPrivlege_TrainMaxDefOpen",2185,36626)
V2187M=V(4,"enErr_VipLvNotReach",2186,36630)
V2188M=V(4,"enErr_VipLvNotActive",2187,36631)
V2189M=V(4,"enErr_Arena_Not_Exist",2188,36651)
V2190M=V(4,"enErr_Arena_Role_Not_In_Arena",2189,36652)
V2191M=V(4,"enErr_Arena_Not_Ready",2190,36653)
V2192M=V(4,"enErr_Arena_Consume_Fail",2191,36654)
E1M=E(3,"EnErrorCode",".CSMsg.EnErrorCode")
V2193M=V(4,"EKickoutClient_Reason_PersonDataInvalid",0,0)
V2194M=V(4,"EKickoutClient_Reason_ShootFailed",1,1)
V2195M=V(4,"EKickoutClient_Reason_UserLocked",2,2)
V2196M=V(4,"EKickoutClient_Reason_StopZoneSvr",3,3)
V2197M=V(4,"EKickoutClient_Reason_GMKickout",4,4)
V2198M=V(4,"EKickoutClient_Reason_StarTrekFailed",5,5)
V2199M=V(4,"EKickoutClient_Reason_SecretiveError",6,6)
V2200M=V(4,"EKickoutClient_Reason_KickoutSelf",7,7)
V2201M=V(4,"EKickoutClient_Reason_Hack",8,8)
V2202M=V(4,"EKickoutClient_Reason_Matrix",9,9)
V2203M=V(4,"EKickoutClient_Reason_LimitLogin",10,10)
V2204M=V(4,"EKickoutClient_Reason_GameGuardTimeout",11,11)
V2205M=V(4,"EKickoutClient_Reason_GameGuardError",12,12)
V2206M=V(4,"EKickoutClient_Reason_Speed",13,13)
V2207M=V(4,"EKickoutClient_Reason_NoPing",14,14)
V2208M=V(4,"EKickoutClient_Reason_RoleVended",15,15)
V2209M=V(4,"EKickoutClient_Reason_TryPlay",16,16)
V2210M=V(4,"EKickoutClient_Reason_RebuildFail",17,17)
V2211M=V(4,"EKickoutClient_Reason_Illegal",18,18)
V2212M=V(4,"EKickoutClient_Reason_Version",19,19)
V2213M=V(4,"EKKickoutClient_Reason_Connect_1",20,50)
V2214M=V(4,"EKKickoutClient_Reason_Connect_2",21,51)
V2215M=V(4,"EKKickoutClient_Reason_Connect_3",22,52)
V2216M=V(4,"EKKickoutClient_Reason_Connect_4",23,53)
V2217M=V(4,"EKKickoutClient_Reason_Connect_5",24,54)
V2218M=V(4,"EKKickoutClient_Reason_Connect_6",25,55)
V2219M=V(4,"EKKickoutClient_Reason_Connect_7",26,56)
V2220M=V(4,"EKKickoutClient_Reason_Connect_8",27,57)
V2221M=V(4,"EKKickoutClient_Reason_Connect_9",28,58)
V2222M=V(4,"EKKickoutClient_Reason_PersonDie",29,59)
V2223M=V(4,"EKKickoutClient_Reason_Reconnect",30,60)
V2224M=V(4,"EKKickoutClient_Reason_ServerNotFound",31,61)
V2225M=V(4,"EKKickoutClient_Reason_RobotExit",32,62)
V2226M=V(4,"EKKickoutClient_Reason_GMKickout",33,63)
V2227M=V(4,"EKKickoutClient_Reason_HasExit",34,64)
V2228M=V(4,"EKKickoutClient_Reason_OnlySingleArea",35,65)
E2M=E(3,"KickReason",".CSMsg.KickReason")
V2229M=V(4,"EDisconnect_Reason_GatewayDisconnect",0,1000000)
V2230M=V(4,"EDisconnect_Reason_GatewayError",1,2000000)
V2231M=V(4,"EDisconnect_Reason_ClientError",2,3000000)
V2232M=V(4,"EDisconnect_Reason_ClientDisconnect",3,4000000)
V2233M=V(4,"EDisconnect_Reason_OfflineWait",4,5000000)
V2234M=V(4,"EDisconnect_Reason_Kickout",5,6000000)
V2235M=V(4,"EDisconnect_Reason_MaxPacket",6,7000000)
V2236M=V(4,"EDisconnect_Reason_GMLimit",7,8000000)
V2237M=V(4,"EDisconnect_Reason_KCPNewError",8,9000000)
V2238M=V(4,"EDisconnect_Reason_KCPRecvError",9,10000000)
V2239M=V(4,"EDisconnect_Reason_KCPClientDisconnect",10,11000000)
V2240M=V(4,"EDisconnect_Reason_KCPUpdateRecvError",11,12000000)
V2241M=V(4,"EDisconnect_Reason_KCPHearBeat",12,13000000)
V2242M=V(4,"EDisconnect_Reason_KCPReconnect",13,14000000)
E3M=E(3,"EnDisconnectReason",".CSMsg.EnDisconnectReason")
F1D=F(2,"errorcode",".CSMsg.TMSG_COMMON_ERR_NTF.errorcode",1,0,1,false,nil,14,8)
M1G=D(1,"TMSG_COMMON_ERR_NTF",".CSMsg.TMSG_COMMON_ERR_NTF",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M,V24M,V25M,V26M,V27M,V28M,V29M,V30M,V31M,V32M,V33M,V34M,V35M,V36M,V37M,V38M,V39M,V40M,V41M,V42M,V43M,V44M,V45M,V46M,V47M,V48M,V49M,V50M,V51M,V52M,V53M,V54M,V55M,V56M,V57M,V58M,V59M,V60M,V61M,V62M,V63M,V64M,V65M,V66M,V67M,V68M,V69M,V70M,V71M,V72M,V73M,V74M,V75M,V76M,V77M,V78M,V79M,V80M,V81M,V82M,V83M,V84M,V85M,V86M,V87M,V88M,V89M,V90M,V91M,V92M,V93M,V94M,V95M,V96M,V97M,V98M,V99M,V100M,V101M,V102M,V103M,V104M,V105M,V106M,V107M,V108M,V109M,V110M,V111M,V112M,V113M,V114M,V115M,V116M,V117M,V118M,V119M,V120M,V121M,V122M,V123M,V124M,V125M,V126M,V127M,V128M,V129M,V130M,V131M,V132M,V133M,V134M,V135M,V136M,V137M,V138M,V139M,V140M,V141M,V142M,V143M,V144M,V145M,V146M,V147M,V148M,V149M,V150M,V151M,V152M,V153M,V154M,V155M,V156M,V157M,V158M,V159M,V160M,V161M,V162M,V163M,V164M,V165M,V166M,V167M,V168M,V169M,V170M,V171M,V172M,V173M,V174M,V175M,V176M,V177M,V178M,V179M,V180M,V181M,V182M,V183M,V184M,V185M,V186M,V187M,V188M,V189M,V190M,V191M,V192M,V193M,V194M,V195M,V196M,V197M,V198M,V199M,V200M,V201M,V202M,V203M,V204M,V205M,V206M,V207M,V208M,V209M,V210M,V211M,V212M,V213M,V214M,V215M,V216M,V217M,V218M,V219M,V220M,V221M,V222M,V223M,V224M,V225M,V226M,V227M,V228M,V229M,V230M,V231M,V232M,V233M,V234M,V235M,V236M,V237M,V238M,V239M,V240M,V241M,V242M,V243M,V244M,V245M,V246M,V247M,V248M,V249M,V250M,V251M,V252M,V253M,V254M,V255M,V256M,V257M,V258M,V259M,V260M,V261M,V262M,V263M,V264M,V265M,V266M,V267M,V268M,V269M,V270M,V271M,V272M,V273M,V274M,V275M,V276M,V277M,V278M,V279M,V280M,V281M,V282M,V283M,V284M,V285M,V286M,V287M,V288M,V289M,V290M,V291M,V292M,V293M,V294M,V295M,V296M,V297M,V298M,V299M,V300M,V301M,V302M,V303M,V304M,V305M,V306M,V307M,V308M,V309M,V310M,V311M,V312M,V313M,V314M,V315M,V316M,V317M,V318M,V319M,V320M,V321M,V322M,V323M,V324M,V325M,V326M,V327M,V328M,V329M,V330M,V331M,V332M,V333M,V334M,V335M,V336M,V337M,V338M,V339M,V340M,V341M,V342M,V343M,V344M,V345M,V346M,V347M,V348M,V349M,V350M,V351M,V352M,V353M,V354M,V355M,V356M,V357M,V358M,V359M,V360M,V361M,V362M,V363M,V364M,V365M,V366M,V367M,V368M,V369M,V370M,V371M,V372M,V373M,V374M,V375M,V376M,V377M,V378M,V379M,V380M,V381M,V382M,V383M,V384M,V385M,V386M,V387M,V388M,V389M,V390M,V391M,V392M,V393M,V394M,V395M,V396M,V397M,V398M,V399M,V400M,V401M,V402M,V403M,V404M,V405M,V406M,V407M,V408M,V409M,V410M,V411M,V412M,V413M,V414M,V415M,V416M,V417M,V418M,V419M,V420M,V421M,V422M,V423M,V424M,V425M,V426M,V427M,V428M,V429M,V430M,V431M,V432M,V433M,V434M,V435M,V436M,V437M,V438M,V439M,V440M,V441M,V442M,V443M,V444M,V445M,V446M,V447M,V448M,V449M,V450M,V451M,V452M,V453M,V454M,V455M,V456M,V457M,V458M,V459M,V460M,V461M,V462M,V463M,V464M,V465M,V466M,V467M,V468M,V469M,V470M,V471M,V472M,V473M,V474M,V475M,V476M,V477M,V478M,V479M,V480M,V481M,V482M,V483M,V484M,V485M,V486M,V487M,V488M,V489M,V490M,V491M,V492M,V493M,V494M,V495M,V496M,V497M,V498M,V499M,V500M,V501M,V502M,V503M,V504M,V505M,V506M,V507M,V508M,V509M,V510M,V511M,V512M,V513M,V514M,V515M,V516M,V517M,V518M,V519M,V520M,V521M,V522M,V523M,V524M,V525M,V526M,V527M,V528M,V529M,V530M,V531M,V532M,V533M,V534M,V535M,V536M,V537M,V538M,V539M,V540M,V541M,V542M,V543M,V544M,V545M,V546M,V547M,V548M,V549M,V550M,V551M,V552M,V553M,V554M,V555M,V556M,V557M,V558M,V559M,V560M,V561M,V562M,V563M,V564M,V565M,V566M,V567M,V568M,V569M,V570M,V571M,V572M,V573M,V574M,V575M,V576M,V577M,V578M,V579M,V580M,V581M,V582M,V583M,V584M,V585M,V586M,V587M,V588M,V589M,V590M,V591M,V592M,V593M,V594M,V595M,V596M,V597M,V598M,V599M,V600M,V601M,V602M,V603M,V604M,V605M,V606M,V607M,V608M,V609M,V610M,V611M,V612M,V613M,V614M,V615M,V616M,V617M,V618M,V619M,V620M,V621M,V622M,V623M,V624M,V625M,V626M,V627M,V628M,V629M,V630M,V631M,V632M,V633M,V634M,V635M,V636M,V637M,V638M,V639M,V640M,V641M,V642M,V643M,V644M,V645M,V646M,V647M,V648M,V649M,V650M,V651M,V652M,V653M,V654M,V655M,V656M,V657M,V658M,V659M,V660M,V661M,V662M,V663M,V664M,V665M,V666M,V667M,V668M,V669M,V670M,V671M,V672M,V673M,V674M,V675M,V676M,V677M,V678M,V679M,V680M,V681M,V682M,V683M,V684M,V685M,V686M,V687M,V688M,V689M,V690M,V691M,V692M,V693M,V694M,V695M,V696M,V697M,V698M,V699M,V700M,V701M,V702M,V703M,V704M,V705M,V706M,V707M,V708M,V709M,V710M,V711M,V712M,V713M,V714M,V715M,V716M,V717M,V718M,V719M,V720M,V721M,V722M,V723M,V724M,V725M,V726M,V727M,V728M,V729M,V730M,V731M,V732M,V733M,V734M,V735M,V736M,V737M,V738M,V739M,V740M,V741M,V742M,V743M,V744M,V745M,V746M,V747M,V748M,V749M,V750M,V751M,V752M,V753M,V754M,V755M,V756M,V757M,V758M,V759M,V760M,V761M,V762M,V763M,V764M,V765M,V766M,V767M,V768M,V769M,V770M,V771M,V772M,V773M,V774M,V775M,V776M,V777M,V778M,V779M,V780M,V781M,V782M,V783M,V784M,V785M,V786M,V787M,V788M,V789M,V790M,V791M,V792M,V793M,V794M,V795M,V796M,V797M,V798M,V799M,V800M,V801M,V802M,V803M,V804M,V805M,V806M,V807M,V808M,V809M,V810M,V811M,V812M,V813M,V814M,V815M,V816M,V817M,V818M,V819M,V820M,V821M,V822M,V823M,V824M,V825M,V826M,V827M,V828M,V829M,V830M,V831M,V832M,V833M,V834M,V835M,V836M,V837M,V838M,V839M,V840M,V841M,V842M,V843M,V844M,V845M,V846M,V847M,V848M,V849M,V850M,V851M,V852M,V853M,V854M,V855M,V856M,V857M,V858M,V859M,V860M,V861M,V862M,V863M,V864M,V865M,V866M,V867M,V868M,V869M,V870M,V871M,V872M,V873M,V874M,V875M,V876M,V877M,V878M,V879M,V880M,V881M,V882M,V883M,V884M,V885M,V886M,V887M,V888M,V889M,V890M,V891M,V892M,V893M,V894M,V895M,V896M,V897M,V898M,V899M,V900M,V901M,V902M,V903M,V904M,V905M,V906M,V907M,V908M,V909M,V910M,V911M,V912M,V913M,V914M,V915M,V916M,V917M,V918M,V919M,V920M,V921M,V922M,V923M,V924M,V925M,V926M,V927M,V928M,V929M,V930M,V931M,V932M,V933M,V934M,V935M,V936M,V937M,V938M,V939M,V940M,V941M,V942M,V943M,V944M,V945M,V946M,V947M,V948M,V949M,V950M,V951M,V952M,V953M,V954M,V955M,V956M,V957M,V958M,V959M,V960M,V961M,V962M,V963M,V964M,V965M,V966M,V967M,V968M,V969M,V970M,V971M,V972M,V973M,V974M,V975M,V976M,V977M,V978M,V979M,V980M,V981M,V982M,V983M,V984M,V985M,V986M,V987M,V988M,V989M,V990M,V991M,V992M,V993M,V994M,V995M,V996M,V997M,V998M,V999M,V1000M,V1001M,V1002M,V1003M,V1004M,V1005M,V1006M,V1007M,V1008M,V1009M,V1010M,V1011M,V1012M,V1013M,V1014M,V1015M,V1016M,V1017M,V1018M,V1019M,V1020M,V1021M,V1022M,V1023M,V1024M,V1025M,V1026M,V1027M,V1028M,V1029M,V1030M,V1031M,V1032M,V1033M,V1034M,V1035M,V1036M,V1037M,V1038M,V1039M,V1040M,V1041M,V1042M,V1043M,V1044M,V1045M,V1046M,V1047M,V1048M,V1049M,V1050M,V1051M,V1052M,V1053M,V1054M,V1055M,V1056M,V1057M,V1058M,V1059M,V1060M,V1061M,V1062M,V1063M,V1064M,V1065M,V1066M,V1067M,V1068M,V1069M,V1070M,V1071M,V1072M,V1073M,V1074M,V1075M,V1076M,V1077M,V1078M,V1079M,V1080M,V1081M,V1082M,V1083M,V1084M,V1085M,V1086M,V1087M,V1088M,V1089M,V1090M,V1091M,V1092M,V1093M,V1094M,V1095M,V1096M,V1097M,V1098M,V1099M,V1100M,V1101M,V1102M,V1103M,V1104M,V1105M,V1106M,V1107M,V1108M,V1109M,V1110M,V1111M,V1112M,V1113M,V1114M,V1115M,V1116M,V1117M,V1118M,V1119M,V1120M,V1121M,V1122M,V1123M,V1124M,V1125M,V1126M,V1127M,V1128M,V1129M,V1130M,V1131M,V1132M,V1133M,V1134M,V1135M,V1136M,V1137M,V1138M,V1139M,V1140M,V1141M,V1142M,V1143M,V1144M,V1145M,V1146M,V1147M,V1148M,V1149M,V1150M,V1151M,V1152M,V1153M,V1154M,V1155M,V1156M,V1157M,V1158M,V1159M,V1160M,V1161M,V1162M,V1163M,V1164M,V1165M,V1166M,V1167M,V1168M,V1169M,V1170M,V1171M,V1172M,V1173M,V1174M,V1175M,V1176M,V1177M,V1178M,V1179M,V1180M,V1181M,V1182M,V1183M,V1184M,V1185M,V1186M,V1187M,V1188M,V1189M,V1190M,V1191M,V1192M,V1193M,V1194M,V1195M,V1196M,V1197M,V1198M,V1199M,V1200M,V1201M,V1202M,V1203M,V1204M,V1205M,V1206M,V1207M,V1208M,V1209M,V1210M,V1211M,V1212M,V1213M,V1214M,V1215M,V1216M,V1217M,V1218M,V1219M,V1220M,V1221M,V1222M,V1223M,V1224M,V1225M,V1226M,V1227M,V1228M,V1229M,V1230M,V1231M,V1232M,V1233M,V1234M,V1235M,V1236M,V1237M,V1238M,V1239M,V1240M,V1241M,V1242M,V1243M,V1244M,V1245M,V1246M,V1247M,V1248M,V1249M,V1250M,V1251M,V1252M,V1253M,V1254M,V1255M,V1256M,V1257M,V1258M,V1259M,V1260M,V1261M,V1262M,V1263M,V1264M,V1265M,V1266M,V1267M,V1268M,V1269M,V1270M,V1271M,V1272M,V1273M,V1274M,V1275M,V1276M,V1277M,V1278M,V1279M,V1280M,V1281M,V1282M,V1283M,V1284M,V1285M,V1286M,V1287M,V1288M,V1289M,V1290M,V1291M,V1292M,V1293M,V1294M,V1295M,V1296M,V1297M,V1298M,V1299M,V1300M,V1301M,V1302M,V1303M,V1304M,V1305M,V1306M,V1307M,V1308M,V1309M,V1310M,V1311M,V1312M,V1313M,V1314M,V1315M,V1316M,V1317M,V1318M,V1319M,V1320M,V1321M,V1322M,V1323M,V1324M,V1325M,V1326M,V1327M,V1328M,V1329M,V1330M,V1331M,V1332M,V1333M,V1334M,V1335M,V1336M,V1337M,V1338M,V1339M,V1340M,V1341M,V1342M,V1343M,V1344M,V1345M,V1346M,V1347M,V1348M,V1349M,V1350M,V1351M,V1352M,V1353M,V1354M,V1355M,V1356M,V1357M,V1358M,V1359M,V1360M,V1361M,V1362M,V1363M,V1364M,V1365M,V1366M,V1367M,V1368M,V1369M,V1370M,V1371M,V1372M,V1373M,V1374M,V1375M,V1376M,V1377M,V1378M,V1379M,V1380M,V1381M,V1382M,V1383M,V1384M,V1385M,V1386M,V1387M,V1388M,V1389M,V1390M,V1391M,V1392M,V1393M,V1394M,V1395M,V1396M,V1397M,V1398M,V1399M,V1400M,V1401M,V1402M,V1403M,V1404M,V1405M,V1406M,V1407M,V1408M,V1409M,V1410M,V1411M,V1412M,V1413M,V1414M,V1415M,V1416M,V1417M,V1418M,V1419M,V1420M,V1421M,V1422M,V1423M,V1424M,V1425M,V1426M,V1427M,V1428M,V1429M,V1430M,V1431M,V1432M,V1433M,V1434M,V1435M,V1436M,V1437M,V1438M,V1439M,V1440M,V1441M,V1442M,V1443M,V1444M,V1445M,V1446M,V1447M,V1448M,V1449M,V1450M,V1451M,V1452M,V1453M,V1454M,V1455M,V1456M,V1457M,V1458M,V1459M,V1460M,V1461M,V1462M,V1463M,V1464M,V1465M,V1466M,V1467M,V1468M,V1469M,V1470M,V1471M,V1472M,V1473M,V1474M,V1475M,V1476M,V1477M,V1478M,V1479M,V1480M,V1481M,V1482M,V1483M,V1484M,V1485M,V1486M,V1487M,V1488M,V1489M,V1490M,V1491M,V1492M,V1493M,V1494M,V1495M,V1496M,V1497M,V1498M,V1499M,V1500M,V1501M,V1502M,V1503M,V1504M,V1505M,V1506M,V1507M,V1508M,V1509M,V1510M,V1511M,V1512M,V1513M,V1514M,V1515M,V1516M,V1517M,V1518M,V1519M,V1520M,V1521M,V1522M,V1523M,V1524M,V1525M,V1526M,V1527M,V1528M,V1529M,V1530M,V1531M,V1532M,V1533M,V1534M,V1535M,V1536M,V1537M,V1538M,V1539M,V1540M,V1541M,V1542M,V1543M,V1544M,V1545M,V1546M,V1547M,V1548M,V1549M,V1550M,V1551M,V1552M,V1553M,V1554M,V1555M,V1556M,V1557M,V1558M,V1559M,V1560M,V1561M,V1562M,V1563M,V1564M,V1565M,V1566M,V1567M,V1568M,V1569M,V1570M,V1571M,V1572M,V1573M,V1574M,V1575M,V1576M,V1577M,V1578M,V1579M,V1580M,V1581M,V1582M,V1583M,V1584M,V1585M,V1586M,V1587M,V1588M,V1589M,V1590M,V1591M,V1592M,V1593M,V1594M,V1595M,V1596M,V1597M,V1598M,V1599M,V1600M,V1601M,V1602M,V1603M,V1604M,V1605M,V1606M,V1607M,V1608M,V1609M,V1610M,V1611M,V1612M,V1613M,V1614M,V1615M,V1616M,V1617M,V1618M,V1619M,V1620M,V1621M,V1622M,V1623M,V1624M,V1625M,V1626M,V1627M,V1628M,V1629M,V1630M,V1631M,V1632M,V1633M,V1634M,V1635M,V1636M,V1637M,V1638M,V1639M,V1640M,V1641M,V1642M,V1643M,V1644M,V1645M,V1646M,V1647M,V1648M,V1649M,V1650M,V1651M,V1652M,V1653M,V1654M,V1655M,V1656M,V1657M,V1658M,V1659M,V1660M,V1661M,V1662M,V1663M,V1664M,V1665M,V1666M,V1667M,V1668M,V1669M,V1670M,V1671M,V1672M,V1673M,V1674M,V1675M,V1676M,V1677M,V1678M,V1679M,V1680M,V1681M,V1682M,V1683M,V1684M,V1685M,V1686M,V1687M,V1688M,V1689M,V1690M,V1691M,V1692M,V1693M,V1694M,V1695M,V1696M,V1697M,V1698M,V1699M,V1700M,V1701M,V1702M,V1703M,V1704M,V1705M,V1706M,V1707M,V1708M,V1709M,V1710M,V1711M,V1712M,V1713M,V1714M,V1715M,V1716M,V1717M,V1718M,V1719M,V1720M,V1721M,V1722M,V1723M,V1724M,V1725M,V1726M,V1727M,V1728M,V1729M,V1730M,V1731M,V1732M,V1733M,V1734M,V1735M,V1736M,V1737M,V1738M,V1739M,V1740M,V1741M,V1742M,V1743M,V1744M,V1745M,V1746M,V1747M,V1748M,V1749M,V1750M,V1751M,V1752M,V1753M,V1754M,V1755M,V1756M,V1757M,V1758M,V1759M,V1760M,V1761M,V1762M,V1763M,V1764M,V1765M,V1766M,V1767M,V1768M,V1769M,V1770M,V1771M,V1772M,V1773M,V1774M,V1775M,V1776M,V1777M,V1778M,V1779M,V1780M,V1781M,V1782M,V1783M,V1784M,V1785M,V1786M,V1787M,V1788M,V1789M,V1790M,V1791M,V1792M,V1793M,V1794M,V1795M,V1796M,V1797M,V1798M,V1799M,V1800M,V1801M,V1802M,V1803M,V1804M,V1805M,V1806M,V1807M,V1808M,V1809M,V1810M,V1811M,V1812M,V1813M,V1814M,V1815M,V1816M,V1817M,V1818M,V1819M,V1820M,V1821M,V1822M,V1823M,V1824M,V1825M,V1826M,V1827M,V1828M,V1829M,V1830M,V1831M,V1832M,V1833M,V1834M,V1835M,V1836M,V1837M,V1838M,V1839M,V1840M,V1841M,V1842M,V1843M,V1844M,V1845M,V1846M,V1847M,V1848M,V1849M,V1850M,V1851M,V1852M,V1853M,V1854M,V1855M,V1856M,V1857M,V1858M,V1859M,V1860M,V1861M,V1862M,V1863M,V1864M,V1865M,V1866M,V1867M,V1868M,V1869M,V1870M,V1871M,V1872M,V1873M,V1874M,V1875M,V1876M,V1877M,V1878M,V1879M,V1880M,V1881M,V1882M,V1883M,V1884M,V1885M,V1886M,V1887M,V1888M,V1889M,V1890M,V1891M,V1892M,V1893M,V1894M,V1895M,V1896M,V1897M,V1898M,V1899M,V1900M,V1901M,V1902M,V1903M,V1904M,V1905M,V1906M,V1907M,V1908M,V1909M,V1910M,V1911M,V1912M,V1913M,V1914M,V1915M,V1916M,V1917M,V1918M,V1919M,V1920M,V1921M,V1922M,V1923M,V1924M,V1925M,V1926M,V1927M,V1928M,V1929M,V1930M,V1931M,V1932M,V1933M,V1934M,V1935M,V1936M,V1937M,V1938M,V1939M,V1940M,V1941M,V1942M,V1943M,V1944M,V1945M,V1946M,V1947M,V1948M,V1949M,V1950M,V1951M,V1952M,V1953M,V1954M,V1955M,V1956M,V1957M,V1958M,V1959M,V1960M,V1961M,V1962M,V1963M,V1964M,V1965M,V1966M,V1967M,V1968M,V1969M,V1970M,V1971M,V1972M,V1973M,V1974M,V1975M,V1976M,V1977M,V1978M,V1979M,V1980M,V1981M,V1982M,V1983M,V1984M,V1985M,V1986M,V1987M,V1988M,V1989M,V1990M,V1991M,V1992M,V1993M,V1994M,V1995M,V1996M,V1997M,V1998M,V1999M,V2000M,V2001M,V2002M,V2003M,V2004M,V2005M,V2006M,V2007M,V2008M,V2009M,V2010M,V2011M,V2012M,V2013M,V2014M,V2015M,V2016M,V2017M,V2018M,V2019M,V2020M,V2021M,V2022M,V2023M,V2024M,V2025M,V2026M,V2027M,V2028M,V2029M,V2030M,V2031M,V2032M,V2033M,V2034M,V2035M,V2036M,V2037M,V2038M,V2039M,V2040M,V2041M,V2042M,V2043M,V2044M,V2045M,V2046M,V2047M,V2048M,V2049M,V2050M,V2051M,V2052M,V2053M,V2054M,V2055M,V2056M,V2057M,V2058M,V2059M,V2060M,V2061M,V2062M,V2063M,V2064M,V2065M,V2066M,V2067M,V2068M,V2069M,V2070M,V2071M,V2072M,V2073M,V2074M,V2075M,V2076M,V2077M,V2078M,V2079M,V2080M,V2081M,V2082M,V2083M,V2084M,V2085M,V2086M,V2087M,V2088M,V2089M,V2090M,V2091M,V2092M,V2093M,V2094M,V2095M,V2096M,V2097M,V2098M,V2099M,V2100M,V2101M,V2102M,V2103M,V2104M,V2105M,V2106M,V2107M,V2108M,V2109M,V2110M,V2111M,V2112M,V2113M,V2114M,V2115M,V2116M,V2117M,V2118M,V2119M,V2120M,V2121M,V2122M,V2123M,V2124M,V2125M,V2126M,V2127M,V2128M,V2129M,V2130M,V2131M,V2132M,V2133M,V2134M,V2135M,V2136M,V2137M,V2138M,V2139M,V2140M,V2141M,V2142M,V2143M,V2144M,V2145M,V2146M,V2147M,V2148M,V2149M,V2150M,V2151M,V2152M,V2153M,V2154M,V2155M,V2156M,V2157M,V2158M,V2159M,V2160M,V2161M,V2162M,V2163M,V2164M,V2165M,V2166M,V2167M,V2168M,V2169M,V2170M,V2171M,V2172M,V2173M,V2174M,V2175M,V2176M,V2177M,V2178M,V2179M,V2180M,V2181M,V2182M,V2183M,V2184M,V2185M,V2186M,V2187M,V2188M,V2189M,V2190M,V2191M,V2192M}
E2M.values = {V2193M,V2194M,V2195M,V2196M,V2197M,V2198M,V2199M,V2200M,V2201M,V2202M,V2203M,V2204M,V2205M,V2206M,V2207M,V2208M,V2209M,V2210M,V2211M,V2212M,V2213M,V2214M,V2215M,V2216M,V2217M,V2218M,V2219M,V2220M,V2221M,V2222M,V2223M,V2224M,V2225M,V2226M,V2227M,V2228M}
E3M.values = {V2229M,V2230M,V2231M,V2232M,V2233M,V2234M,V2235M,V2236M,V2237M,V2238M,V2239M,V2240M,V2241M,V2242M}
F1D.enum_type=M2G
M1G.fields={F1D}

EDisconnect_Reason_ClientDisconnect = 4000000
EDisconnect_Reason_ClientError = 3000000
EDisconnect_Reason_GMLimit = 8000000
EDisconnect_Reason_GatewayDisconnect = 1000000
EDisconnect_Reason_GatewayError = 2000000
EDisconnect_Reason_KCPClientDisconnect = 11000000
EDisconnect_Reason_KCPHearBeat = 13000000
EDisconnect_Reason_KCPNewError = 9000000
EDisconnect_Reason_KCPReconnect = 14000000
EDisconnect_Reason_KCPRecvError = 10000000
EDisconnect_Reason_KCPUpdateRecvError = 12000000
EDisconnect_Reason_Kickout = 6000000
EDisconnect_Reason_MaxPacket = 7000000
EDisconnect_Reason_OfflineWait = 5000000
EKKickoutClient_Reason_Connect_1 = 50
EKKickoutClient_Reason_Connect_2 = 51
EKKickoutClient_Reason_Connect_3 = 52
EKKickoutClient_Reason_Connect_4 = 53
EKKickoutClient_Reason_Connect_5 = 54
EKKickoutClient_Reason_Connect_6 = 55
EKKickoutClient_Reason_Connect_7 = 56
EKKickoutClient_Reason_Connect_8 = 57
EKKickoutClient_Reason_Connect_9 = 58
EKKickoutClient_Reason_GMKickout = 63
EKKickoutClient_Reason_HasExit = 64
EKKickoutClient_Reason_OnlySingleArea = 65
EKKickoutClient_Reason_PersonDie = 59
EKKickoutClient_Reason_Reconnect = 60
EKKickoutClient_Reason_RobotExit = 62
EKKickoutClient_Reason_ServerNotFound = 61
EKickoutClient_Reason_GMKickout = 4
EKickoutClient_Reason_GameGuardError = 12
EKickoutClient_Reason_GameGuardTimeout = 11
EKickoutClient_Reason_Hack = 8
EKickoutClient_Reason_Illegal = 18
EKickoutClient_Reason_KickoutSelf = 7
EKickoutClient_Reason_LimitLogin = 10
EKickoutClient_Reason_Matrix = 9
EKickoutClient_Reason_NoPing = 14
EKickoutClient_Reason_PersonDataInvalid = 0
EKickoutClient_Reason_RebuildFail = 17
EKickoutClient_Reason_RoleVended = 15
EKickoutClient_Reason_SecretiveError = 6
EKickoutClient_Reason_ShootFailed = 1
EKickoutClient_Reason_Speed = 13
EKickoutClient_Reason_StarTrekFailed = 5
EKickoutClient_Reason_StopZoneSvr = 3
EKickoutClient_Reason_TryPlay = 16
EKickoutClient_Reason_UserLocked = 2
EKickoutClient_Reason_Version = 19
TMSG_COMMON_ERR_NTF =M(M1G)
emErr_BossSlave_DuplicatePalId = 5047
enErr_Abnormal = 2
enErr_Achievement_HaveAward = 1701
enErr_Achievement_NoCondition = 1700
enErr_Achievement_StageNoCondition = 1702
enErr_AcornPub_AllianceCantNotSnatch = 34167
enErr_AcornPub_AlreadyReward = 34169
enErr_AcornPub_AlreadySnatchTask = 34168
enErr_AcornPub_ConditionErr = 34159
enErr_AcornPub_ConfigErr = 34158
enErr_AcornPub_HelpLimit = 34161
enErr_AcornPub_HeroNotExist = 34157
enErr_AcornPub_NotAllowSendTaskInZone1 = 34175
enErr_AcornPub_NotEnoughTaskQueue = 34173
enErr_AcornPub_NotExistTask = 34150
enErr_AcornPub_NotExistTaskEntity = 34155
enErr_AcornPub_NotFoundCfg = 34152
enErr_AcornPub_NotFoundPosPlzMoveBase = 34174
enErr_AcornPub_NotFoundSandboxBase = 34153
enErr_AcornPub_NotHaveRewardTask = 34172
enErr_AcornPub_NotInNativeSandbox = 34151
enErr_AcornPub_SnatchLimit = 34165
enErr_AcornPub_SuperRefreshFailed = 34171
enErr_AcornPub_SuperRefreshNotOpen = 34170
enErr_AcornPub_TaskCantNotHelpSelf = 34162
enErr_AcornPub_TaskCantNotSnatchSelf = 34164
enErr_AcornPub_TaskDoing = 34156
enErr_AcornPub_TaskHelped = 34160
enErr_AcornPub_TaskNotFinish = 34166
enErr_AcornPub_TaskSnatchLimit = 34163
enErr_AcornPub_WaitForReady = 34154
enErr_Activation_MAX = 2790
enErr_Activation_Not_Enough = 2781
enErr_Activation_Prized = 2782
enErr_ActivityCfgError = 1159
enErr_ActivityCfgNoExist = 1152
enErr_ActivityCompleteOverstep = 1155
enErr_ActivityHaveReceive = 1164
enErr_ActivityHeroInAdvanceArena = 1165
enErr_ActivityHeroInArena = 1158
enErr_ActivityHeroNumError = 1156
enErr_ActivityHeroPropError = 1157
enErr_ActivityNoNeedReceive = 1163
enErr_ActivityNotOpen = 1168
enErr_ActivityParamAbnormal = 1151
enErr_ActivityTypeAbnormal = 1153
enErr_ActivityValueAbnormal = 1154
enErr_Activity_NotOpen = 31862
enErr_ActorCreateCityBuildingInvalid = 36563
enErr_ActorCreateLevelChallengeInvalid = 36562
enErr_ActorCreateLevelInvalid = 36561
enErr_ActorCreateOpenGiftSidInvalid = 36565
enErr_ActorCreateRepairSidInvalid = 36564
enErr_ActrivityEquipDesFail = 1162
enErr_ActrivityEquipNumError = 1160
enErr_ActrivityEquipPropErr = 1161
enErr_AllSaintsDay_AlreadyReward = 5106
enErr_AllSaintsDay_HasUpvote = 5103
enErr_AllSaintsDay_NotFinished = 5105
enErr_AllSaintsDay_NotOpen = 5108
enErr_AllSaintsDay_NotSExist = 5102
enErr_AllSaintsDay_NotSubmit = 5101
enErr_AllSaintsDay_RiddleUpdate = 5107
enErr_AllSaintsDay_UpvoteOver = 5104
enErr_All_Rewards_Received = 31750
enErr_AllianceAchievement_AlreadyGetReward = 31854
enErr_AllianceAchievement_IsOver = 31852
enErr_AllianceAchievement_NeverGetReward = 31853
enErr_AllianceAchievement_NotOpen = 31850
enErr_AllianceAchievement_NotUnlock = 31851
enErr_AllianceBoss_InCD = 31811
enErr_AllianceBoss_NewComersCanNotAttack = 31814
enErr_AllianceBoss_NotActivityTime = 31800
enErr_AllianceBoss_NotEnoughCond = 31809
enErr_AllianceBoss_NotFoundCfg = 31805
enErr_AllianceBoss_NotHaveAllianceMark = 31813
enErr_AllianceBoss_NotInAlliance = 31802
enErr_AllianceBoss_NotInNativeSandbox = 31812
enErr_AllianceBoss_NotMatchState = 31808
enErr_AllianceBoss_NotPrivilege = 31801
enErr_AllianceBoss_NotRightEntity = 31810
enErr_AllianceBoss_NotUnlocked = 31807
enErr_AllianceBoss_ServerOpenTimeNotMatch = 31806
enErr_AllianceBoss_TimeNotRight = 31803
enErr_AllianceBoss_WaitForReady = 31804
enErr_AllianceDuel_AlliancePosErr = 34603
enErr_AllianceDuel_BattleNoAttend = 34103
enErr_AllianceDuel_BattleNoUpDown = 34104
enErr_AllianceDuel_BattleNotNeed = 34601
enErr_AllianceDuel_ConfigError = 34604
enErr_AllianceDuel_MoveCityCD = 34600
enErr_AllianceDuel_NoAttend = 34100
enErr_AllianceDuel_NoRANKTHEMEID = 34101
enErr_AllianceDuel_RANKTHEMEID_ERROR = 34102
enErr_AllianceDuel_StatusError = 34605
enErr_AllianceDuel_ThemeIdError = 34602
enErr_AllianceDuel_end = 34149
enErr_AllianceGiftPrivlege_AllGetNoOpen = 36625
enErr_AllianceGiftPrivlege_TrainMaxDefOpen = 36626
enErr_AllianceHelp_NoConfig = 30218
enErr_AllianceHelp_NotInBuilding = 30217
enErr_AllianceHelp_ParamsErr = 30219
enErr_AllianceHelp_RepeatStart = 30216
enErr_AllianceInvite_AlreadyExpired = 31856
enErr_AllianceInvite_AlreadyHasAlliance = 31858
enErr_AllianceInvite_AlreadyInvite = 31855
enErr_AllianceInvite_NoAlliance = 31857
enErr_AllianceMassMove_NoEnough = 35560
enErr_AllianceOutFire_NoFire = 36502
enErr_AllianceOutFire_NoLeagueMember = 36501
enErr_AllianceRecord_NoData = 35500
enErr_AllianceRecord_RecordIsNull = 35501
enErr_AllianceShare_InCD = 30135
enErr_AllianceShare_NoAlliance = 30134
enErr_AllianceShare_NoAuthority = 30136
enErr_AllianceTodo_AlreadyGet = 36522
enErr_AllianceTodo_ExitTimeDontGet = 36523
enErr_AllianceTodo_ModuleNoOpen = 36521
enErr_AllianceTodo_NoAuthority = 36524
enErr_AllianceTodo_TaskDontGet = 36525
enErr_AllianceTrain_AllianceIdErr = 35801
enErr_AllianceTrain_AttackTrainErr = 35814
enErr_AllianceTrain_GetAllianceTrainErr = 35822
enErr_AllianceTrain_GetSandBoxSidErr = 35804
enErr_AllianceTrain_GetTrainHeadErr = 35813
enErr_AllianceTrain_GetWorldGroupErr = 35821
enErr_AllianceTrain_JoinTimeNotEnough = 35824
enErr_AllianceTrain_NoAllianceTrain = 35802
enErr_AllianceTrain_NoTrainHead = 35803
enErr_AllianceTrain_SetCacheErr = 35807
enErr_AllianceTrain_SwapLineUpFail = 35819
enErr_AllianceTrain_TargetNotExit = 35820
enErr_AllianceTrain_TeamIndexErr = 35805
enErr_AllianceTrain_TeamLineUpErr = 35806
enErr_AllianceTrain_TrainIsLock = 35823
enErr_Alliance_ApplyLoseEffect = 30121
enErr_Alliance_AuthorityMemberIsFull = 30127
enErr_Alliance_ContentIllegal = 30128
enErr_Alliance_FreeMoveCityOverFail = 31859
enErr_Alliance_GiftBoxIdHadExpire = 30120
enErr_Alliance_GiftBoxIdHadGet = 30119
enErr_Alliance_GiftBoxIdNoExist = 30118
enErr_Alliance_HasApply = 30125
enErr_Alliance_LanguageErr = 30117
enErr_Alliance_MainLvSetExecLimit = 30130
enErr_Alliance_NoAllianceMember = 30124
enErr_Alliance_NoAuthority = 30111
enErr_Alliance_NoSuitableIn = 30113
enErr_Alliance_NoUnlockFlagID = 30116
enErr_Alliance_NotAllianceBuildeLv = 30133
enErr_Alliance_NotEnoughGiftLv = 30122
enErr_Alliance_NotSendEmail = 30123
enErr_Alliance_ParamsErr = 30103
enErr_Alliance_PowerSetExecLimit = 30129
enErr_Alliance_R5NoAuthority = 30112
enErr_Alliance_SendEmailInCD = 30126
enErr_Alliance_TitleIllegal = 30132
enErr_Alliance_UrgentAnnounce = 30131
enErr_AnniversaryGradGame_GotRewardsToday = 5174
enErr_AnniversaryGradGame_MaxParticipation = 5170
enErr_AnniversaryGradGame_MaxPurchase = 5171
enErr_AnniversaryGradGame_MaxPurchaseFaild = 5172
enErr_AnniversaryGradGame_NotOpenTime = 5173
enErr_ArcheryBuyCountMax = 3769
enErr_ArcheryConfigError = 3768
enErr_ArcheryCountMax = 3766
enErr_ArcheryStateError = 3767
enErr_Arena_Already_InTeam = 553
enErr_Arena_Btn_Not_Cooling = 574
enErr_Arena_Btn_Still_Cooling = 573
enErr_Arena_Buy_Ticket_Num_Max = 578
enErr_Arena_ChallengeFail = 564
enErr_Arena_Consume_Fail = 36654
enErr_Arena_EnterArenaFail = 568
enErr_Arena_Got_Level_Reward = 572
enErr_Arena_HasEnrolled = 555
enErr_Arena_Have_NO5_Victory_Reward = 576
enErr_Arena_IsCaptain = 556
enErr_Arena_Level_NotEnough = 551
enErr_Arena_LoadBattleRecordFail = 565
enErr_Arena_LoadDefenceLineupFail = 567
enErr_Arena_LoadRankingListFail = 566
enErr_Arena_LoadRivalListFail = 569
enErr_Arena_NoTeam = 571
enErr_Arena_NotCaptain = 554
enErr_Arena_NotMeetCELL = 562
enErr_Arena_NotOpen = 550
enErr_Arena_Not_Exist = 36651
enErr_Arena_Not_InTeam = 570
enErr_Arena_Not_Ready = 36653
enErr_Arena_PassStage_NotSatisfy = 563
enErr_Arena_ReachTeamMemberUL = 557
enErr_Arena_Role_Not_In_Arena = 36652
enErr_Arena_Stage_NotEnough = 577
enErr_Arena_TeamDismissed = 561
enErr_Arena_TeamMember_NotEnough = 558
enErr_Arena_TeamName_Exist = 560
enErr_Arena_Ticket_NotEnough = 552
enErr_Arena_Victory_Num_NotEnough = 575
enErr_Arena_VitalityPoint_NotEnough = 559
enErr_AshDungeon_AllPass = 914
enErr_AshDungeon_HeroDie = 906
enErr_AshDungeon_MapRewardLock = 916
enErr_AshDungeon_Max = 966
enErr_AshDungeon_NoEnoughGoodsNum = 910
enErr_AshDungeon_NoEnoughMedicineNum = 904
enErr_AshDungeon_NoFindGoodsID = 908
enErr_AshDungeon_NoFindHero = 905
enErr_AshDungeon_NoFindMedicineID = 903
enErr_AshDungeon_NoOpen = 911
enErr_AshDungeon_NoReachOpenLevel = 901
enErr_AshDungeon_NoReset = 917
enErr_AshDungeon_NoSweepChance = 912
enErr_AshDungeon_NoSweepMap = 913
enErr_AshDungeon_SelMedicine = 915
enErr_AshDungeon_errShopSubType = 907
enErr_AshDungeon_haveSaved = 902
enErr_AtyGathering_BaseLvNoEnough = 36351
enErr_AtyGathering_NotOpen = 36350
enErr_AtyGathering_SearchLvNoEnough = 36352
enErr_AwakenRoadConfirm = 1166
enErr_AwakenRoadNotConfirm = 1167
enErr_Award_Claimed = 35550
enErr_BattleshipTech_GoToUpgrade = 3901
enErr_BattleshipTech_LvError = 3903
enErr_BattleshipTech_MAX = 3920
enErr_BattleshipTech_ModuleClose = 3906
enErr_BattleshipTech_PreLvNotEnough = 3904
enErr_BattleshipTech_ResetLackDiamond = 3907
enErr_BattleshipTech_ResetLvZero = 3908
enErr_BattleshipTech_TechLvErr = 3902
enErr_BattleshipTech_UpgradeLackOfGoods = 3905
enErr_BindAccount_AccountHasRole = 52
enErr_BindAccount_AccountVerifyFail = 53
enErr_BindAccount_BCUserID_Empty = 55
enErr_BindAccount_BindAlready = 51
enErr_BindAccount_CodeErr1 = 54
enErr_BindAccount_NotVisitor = 50
enErr_BindEmail_RealyGot = 4252
enErr_BindPohone_ArealyGot = 4250
enErr_BossSlave_Abnormal = 5030
enErr_BossSlave_AlreadyCatched = 5031
enErr_BossSlave_BattleInfoError = 5025
enErr_BossSlave_BattleRobot = 5043
enErr_BossSlave_BattleStartError = 5038
enErr_BossSlave_CatchInvalidSlave = 5044
enErr_BossSlave_CatchSelf = 5028
enErr_BossSlave_ConquerFailed = 5027
enErr_BossSlave_ConquerSelf = 5026
enErr_BossSlave_EnergyLess = 5029
enErr_BossSlave_ForcedLeftTeam = 5045
enErr_BossSlave_MailAwarded = 5035
enErr_BossSlave_MailBusy = 5033
enErr_BossSlave_MailEmpty = 5037
enErr_BossSlave_MailNoItems = 5036
enErr_BossSlave_MailNotExist = 5034
enErr_BossSlave_NoAward = 5021
enErr_BossSlave_NotInSameWorldArea = 5048
enErr_BossSlave_NotMyBoss = 5024
enErr_BossSlave_NotMySlave = 5023
enErr_BossSlave_OperError = 5032
enErr_BossSlave_ParamError = 5022
enErr_BossSlave_SlaveListFull = 5046
enErr_Busy = 3
enErr_BuyChallengeBadgesUseUp = 1150
enErr_CampTrial_AsyncBattle_Reset = 31944
enErr_CampTrial_Chall_Completed = 31945
enErr_CampTrial_EnterBattleErr = 31943
enErr_CampTrial_Hero_ChallOther = 31935
enErr_CampTrial_Hero_NoOne = 31931
enErr_CampTrial_Hero_NotExist = 31934
enErr_CampTrial_Hero_Repeat = 31933
enErr_CampTrial_Hero_TooMany = 31932
enErr_CampTrial_InBattle = 31941
enErr_CampTrial_MonsterTroop = 31942
enErr_CampTrial_NotData = 31901
enErr_CampTrial_NotDifficulty = 31904
enErr_CampTrial_NotOpen = 31903
enErr_CampTrial_NotTrialID = 31905
enErr_CampTrial_NotType = 31902
enErr_CampTrial_Only_Chall_SameDifficulty = 31922
enErr_CampTrial_Only_Difficulty_Max = 31921
enErr_CampTrial_Only_Next_Layer = 31923
enErr_CampTrial_SelectedDifficulty = 31914
enErr_CampTrial_UnLock_Difficulty = 31913
enErr_CampTrial_UnLock_Quick = 31912
enErr_CampTrial_UnLocked_Type = 31911
enErr_CampTrial_Unselected = 31915
enErr_Carriage_AlreadyGoing = 34010
enErr_Carriage_BattleError = 34005
enErr_Carriage_CrossServerNotTruckGo = 33901
enErr_Carriage_GoingError = 34011
enErr_Carriage_InBattle = 34014
enErr_Carriage_NoBeLootCnt = 34008
enErr_Carriage_NoCityInfo = 34009
enErr_Carriage_NoDepart = 34000
enErr_Carriage_NoLootCnt = 34007
enErr_Carriage_NoTrade = 34001
enErr_Carriage_NoTradeCnt = 34006
enErr_Carriage_NotGetAward = 34012
enErr_Carriage_NotInCrossGroups = 33902
enErr_Carriage_NotLootAlliance = 34016
enErr_Carriage_NotLootSelf = 34015
enErr_Carriage_NotOpenSystem = 34018
enErr_Carriage_NotOtherAward = 34013
enErr_Carriage_RoleDataNoLoad = 34017
enErr_Carriage_TargetNotExit = 33900
enErr_Carriage_TeamErr = 34002
enErr_Carriage_TeamInUse = 34004
enErr_Carriage_TeamLocked = 34003
enErr_Championships_GetRankFail = 2701
enErr_Championships_GetRewardFail = 2703
enErr_Championships_GetRewardTimeOut = 2707
enErr_Championships_HaveGain = 2704
enErr_Championships_Max = 2750
enErr_Championships_NotJoin = 2700
enErr_Championships_RankRem = 2702
enErr_Championships_ReqPromoteFre = 2706
enErr_Championships_ReqRankFre = 2705
enErr_ChangePackage_BindAccountRewardGetted = 3202
enErr_ChangePackage_ExchangeRecordError = 3204
enErr_ChangePackage_ExchangeRewardGetted = 3205
enErr_ChangePackage_Max = 3210
enErr_ChangePackage_NoExchangeRecord = 3203
enErr_ChangePackage_NotBind = 3201
enErr_Change_Cost_Hero_Star_Lv_NotEnough = 218
enErr_Chat_Be_Blocked = 460
enErr_Chat_Blocked_Person = 456
enErr_Chat_Empty_Context = 454
enErr_Chat_Gm_Close_Conversation = 462
enErr_Chat_GoldenEggs_NotEnough = 463
enErr_Chat_Lv_BattleLv_Not_Enough = 461
enErr_Chat_Lv_NotEnough1 = 450
enErr_Chat_Lv_NotEnough2 = 451
enErr_Chat_Lv_NotEnough3 = 452
enErr_Chat_Max_Len = 455
enErr_Chat_Mute = 458
enErr_Chat_Not_Guild = 453
enErr_Chat_unBlocked_Person = 457
enErr_ChinaRed_ChapterGained = 4221
enErr_ChinaRed_HavingSweep = 4223
enErr_ChinaRed_Max = 4240
enErr_ChinaRed_PassLimit = 4224
enErr_ChinaRed_StarNumNotEnough = 4222
enErr_ChinaRed_SweepLimit = 4220
enErr_ChooseWeekCard_Buyed = 36402
enErr_ChooseWeekCard_ChooseItem = 36405
enErr_ChooseWeekCard_ChooseNum = 36404
enErr_ChooseWeekCard_Get = 36407
enErr_ChooseWeekCard_NotBuy = 36406
enErr_ChooseWeekCard_NotCfg = 36403
enErr_ChooseWeekCard_NotData = 36401
enErr_ChristmasTeam_ChristmasTeamExpired = 4994
enErr_ChristmasTeam_JoinTeamOnce = 4995
enErr_ChristmasTeam_JoinTeam_MemberFull = 4996
enErr_ChristmasTeam_LaunchTeamMaxLimit = 4992
enErr_ChristmasTeam_LaunchTeamOnce = 4993
enErr_ChristmasTeam_NoTeamInfo = 4998
enErr_ChristmasTeam_NoTeamTicket = 4991
enErr_ChristmasTeam_SelfTeam = 5000
enErr_ChristmasTeam_TeamLock = 4999
enErr_ChristmasTeam_TeamRewardUnavaliable = 4997
enErr_Christmas_ActiveNotOpen = 2261
enErr_Christmas_Max = 2280
enErr_Christmas_NoCandy = 2263
enErr_Christmas_NoCard = 2266
enErr_Christmas_NoMoreReward = 2268
enErr_Christmas_NotActiveBlackCard = 2267
enErr_Christmas_NumberRestriction = 2269
enErr_Christmas_RepeatCard = 2264
enErr_Christmas_RewardGotBefore = 2270
enErr_Christmas_SendLimit = 2265
enErr_Christmas_TreeLvLimit = 2262
enErr_City_AlreadyHaveWorker = 30310
enErr_City_BuildQueueIsFull = 30303
enErr_City_BuildingFull = 30308
enErr_City_BuildingTypeIsFull = 30314
enErr_City_BuyDefenseFreeze = 30330
enErr_City_CityWarDefenseFull = 30329
enErr_City_DroneStarBuildingNoOpen = 30331
enErr_City_FixConditionCfgError = 30302
enErr_City_GetRewardQueueFail = 30327
enErr_City_HospitalOverflow = 30323
enErr_City_InvalidBuilding = 30300
enErr_City_InvalidLevel = 30325
enErr_City_InvalidNum = 30320
enErr_City_NoResourceErr = 30328
enErr_City_NotAllowPosition = 30315
enErr_City_NotBrokenState = 30301
enErr_City_NotEnoughCond = 30311
enErr_City_NotEnoughReplaceCond = 30309
enErr_City_NotFoundBuilding = 30304
enErr_City_NotFoundBuildingCfg = 30316
enErr_City_NotFoundCfg = 30319
enErr_City_NotFoundWorker = 30305
enErr_City_NotGiftState = 30313
enErr_City_NotTargetBuilding = 30307
enErr_City_PositionNotChange = 30317
enErr_City_RecruitSoldierOverflow = 30318
enErr_City_TimeNotYet = 30321
enErr_City_TrainningCenterOverflow = 30322
enErr_City_UpgradeSoldierOverflow = 30324
enErr_City_WallNotFire = 30326
enErr_City_WorkerHasBuilding = 30306
enErr_City_WorkerLevelLimit = 30312
enErr_ClickFrequently = 25
enErr_Common_ActNotOpen = 29
enErr_Common_ConfigError = 28
enErr_Common_Max = 35800
enErr_Common_NoCount = 27
enErr_Common_Not_Find_Data = 35701
enErr_Common_Using = 35700
enErr_CompeteBattle_ExportTroop = 1403
enErr_CompeteBattle_ExportTroopOther = 1404
enErr_CompeteBattle_Max = 1450
enErr_CompeteBattle_RequireHero = 1406
enErr_CompeteBattle_RivalNoTroop = 1402
enErr_CompeteBattle_RivalNotExist = 1401
enErr_CompeteBattle_Self = 1405
enErr_Composite_NotEnoughGoods = 30421
enErr_Congress_ApplyFor_CD = 34243
enErr_Congress_ApplyFor_Full = 34242
enErr_Congress_ApplyFor_HasPosition = 34244
enErr_Congress_ApplyFor_InList = 34245
enErr_Congress_ApplyFor_InQueueList = 34246
enErr_Congress_ApplyFor_NoOperate = 34241
enErr_Congress_ApplyFor_NoPresident = 34248
enErr_Congress_ApplyFor_NotAuthority = 34232
enErr_Congress_ApplyFor_NotFindRole = 34234
enErr_Congress_ApplyFor_NotInList = 34247
enErr_Congress_ApplyFor_NotQueueData = 34231
enErr_Congress_ApplyFor_QueueFull = 34233
enErr_Congress_Appoint_CD = 34213
enErr_Congress_Appoint_CannotChangeTarget = 34215
enErr_Congress_Appoint_IsSelf = 34214
enErr_Congress_Appoint_NoOperate = 34212
enErr_Congress_Appoint_NotAuthority = 34211
enErr_Congress_Award_BaseLev = 34274
enErr_Congress_Award_DiffWorld = 34272
enErr_Congress_Award_Getted = 34275
enErr_Congress_Award_Limit = 34277
enErr_Congress_Award_NoAuthority = 34271
enErr_Congress_Award_NotCfg = 34273
enErr_Congress_Award_NotPres = 34276
enErr_Congress_Award_PresNotGetOther = 34278
enErr_Congress_Dismiss_IsSelf = 34224
enErr_Congress_Dismiss_NoOperate = 34222
enErr_Congress_Dismiss_NotAuthority = 34221
enErr_Congress_Dismiss_NotRole = 34223
enErr_Congress_NotOpen = 34291
enErr_Congress_OfficialRecord_NotData = 34261
enErr_Congress_OfficialRecord_NotOfficial = 34262
enErr_Congress_Official_BaseLev = 34205
enErr_Congress_Official_DiffWorld = 34204
enErr_Congress_Official_NotConfig = 34201
enErr_Congress_Official_NotCurSeason = 34202
enErr_Congress_Official_NotData = 34200
enErr_Congress_Official_Repeat = 34203
enErr_Congress_President_MailContentFail = 34283
enErr_Congress_President_MailContentLimit = 34282
enErr_Congress_President_MailSendCD = 34284
enErr_Congress_President_ManifestoLimit = 34281
enErr_Congress_QueueUp_Kick_NotAuthority = 34255
enErr_Congress_QueueUp_NotData = 34251
enErr_Congress_QueueUp_NotFindRole = 34254
enErr_Congress_QueueUp_Set_NotAuthority = 34252
enErr_Congress_QueueUp_Set_NotCfg = 34253
enErr_ConsumeGoldFail = 16
enErr_ConsumeItemFail = 14
enErr_ConsumeMoneyFail = 15
enErr_DataSyncing = 24
enErr_DecorPassPort_InvalidTime = 4315
enErr_DecorPassPort_LvError = 4308
enErr_DecorPassPort_TypeError = 4307
enErr_DecorPassport_ConfigError = 4306
enErr_DesertStrom_Battle_CityInZone1 = 36150
enErr_DesertStrom_Battle_EnterEarly_Main = 36144
enErr_DesertStrom_Battle_EnterEarly_Sub = 36145
enErr_DesertStrom_Battle_EnterEnd = 36146
enErr_DesertStrom_Battle_HasAllianceMember = 36155
enErr_DesertStrom_Battle_HashData = 36141
enErr_DesertStrom_Battle_InOtherSandbox = 36153
enErr_DesertStrom_Battle_LeaveEvery = 36148
enErr_DesertStrom_Battle_MemberFull = 36149
enErr_DesertStrom_Battle_NoSandbox = 36151
enErr_DesertStrom_Battle_NotInSandbox = 36152
enErr_DesertStrom_Battle_NotInStage = 36140
enErr_DesertStrom_Battle_NotOpenTime = 36157
enErr_DesertStrom_Battle_OtherSandbox = 36147
enErr_DesertStrom_Battle_ResouceIsEmpty = 36156
enErr_DesertStrom_Battle_RoleNotJoin = 36142
enErr_DesertStrom_Battle_TimeCfgErr = 36143
enErr_DesertStrom_BuildInSafeState = 36158
enErr_DesertStrom_Common_NotAlliance = 36102
enErr_DesertStrom_Common_NotExpel = 36104
enErr_DesertStrom_Common_RoleNotOpen = 36101
enErr_DesertStrom_Common_ServerNotOpen = 36100
enErr_DesertStrom_Common_TeamErr = 36103
enErr_DesertStrom_CreateCityFail = 36154
enErr_DesertStrom_EnemySafeAreaNotAttackDetect = 36052
enErr_DesertStrom_EnemySafeAreaNotMove = 36051
enErr_DesertStrom_GetAllianceRankErr = 36002
enErr_DesertStrom_GetAllianceScoreErr = 36001
enErr_DesertStrom_GetRoleScoreErr = 36003
enErr_DesertStrom_MoveCityCD = 36050
enErr_DesertStrom_SignUp_AllianceAuthority = 36125
enErr_DesertStrom_SignUp_AllianceRank = 36126
enErr_DesertStrom_SignUp_BaseLevel = 36124
enErr_DesertStrom_SignUp_BattleTimeErr = 36131
enErr_DesertStrom_SignUp_BattleTimeModifyAgain = 36137
enErr_DesertStrom_SignUp_BattleTimeRepeat = 36132
enErr_DesertStrom_SignUp_CreateAllianceTime = 36122
enErr_DesertStrom_SignUp_DiffAlliance = 36127
enErr_DesertStrom_SignUp_HashData = 36121
enErr_DesertStrom_SignUp_JoinAllianceTime = 36123
enErr_DesertStrom_SignUp_MemberFull_Main = 36134
enErr_DesertStrom_SignUp_MemberFull_Sub = 36135
enErr_DesertStrom_SignUp_NotInStage = 36120
enErr_DesertStrom_SignUp_NotYet = 36130
enErr_DesertStrom_SignUp_Repeat = 36129
enErr_DesertStrom_SignUp_SetHope_NotInStage = 36119
enErr_DesertStrom_SignUp_SetMemberAbnormal = 36136
enErr_DesertStrom_SignUp_SetMemberSame = 36133
enErr_DesertStrom_SignUp_SetMember_NotInStage = 36118
enErr_DesertStrom_SignUp_SetTime_NotInStage = 36117
enErr_DimensionWarNotSign = 4766
enErr_DimensionWar_0GatherTeamCount = 4817
enErr_DimensionWar_0Score = 4781
enErr_DimensionWar_ActionCountMax = 4772
enErr_DimensionWar_AfterGarrisonTeamMax = 4820
enErr_DimensionWar_AllTeamInQueue = 4812
enErr_DimensionWar_AlreadyAssembled = 4782
enErr_DimensionWar_Area_Not_Open = 4779
enErr_DimensionWar_AssembleCDTime = 4809
enErr_DimensionWar_AssembleTooMuch = 4784
enErr_DimensionWar_AttackCountMax = 4807
enErr_DimensionWar_AttackSelfStation = 4798
enErr_DimensionWar_BossLeft = 4803
enErr_DimensionWar_BossNoKill = 4795
enErr_DimensionWar_BossRewarded = 4796
enErr_DimensionWar_CEOCanSignUp = 4776
enErr_DimensionWar_Captain_Dealing = 4768
enErr_DimensionWar_Caption_Over = 4762
enErr_DimensionWar_DefenseNoTeam = 4806
enErr_DimensionWar_DefenseTeamLimit = 4805
enErr_DimensionWar_DropOcpNotRightTime = 4821
enErr_DimensionWar_DropProtectTime = 4810
enErr_DimensionWar_DropStationGathe = 4819
enErr_DimensionWar_ExitLeague = 4780
enErr_DimensionWar_GotDropReward = 4822
enErr_DimensionWar_HeroInTroop = 4778
enErr_DimensionWar_HeroUsed = 4773
enErr_DimensionWar_HeroWanted = 4774
enErr_DimensionWar_Is_Caption = 4763
enErr_DimensionWar_NotAssembled = 4783
enErr_DimensionWar_NotBoss = 4793
enErr_DimensionWar_NotBossAttTime = 4802
enErr_DimensionWar_NotInBattleState = 4767
enErr_DimensionWar_NotMatched = 4785
enErr_DimensionWar_NotOfficer = 4792
enErr_DimensionWar_NotOwnStation = 4791
enErr_DimensionWar_NotSignState = 4760
enErr_DimensionWar_Not_Caption = 4764
enErr_DimensionWar_PioneerLock = 4788
enErr_DimensionWar_RestTime = 4808
enErr_DimensionWar_SamePlace = 4804
enErr_DimensionWar_SaveAfterAttack = 4799
enErr_DimensionWar_ScoreNotEnough = 4797
enErr_DimensionWar_SelfScoreEmpty = 4823
enErr_DimensionWar_SetDefTroopError = 4765
enErr_DimensionWar_Signed = 4761
enErr_DimensionWar_SomeTeamInQueue = 4813
enErr_DimensionWar_StarNotEnough = 4787
enErr_DimensionWar_StationBusy = 4818
enErr_DimensionWar_StationCantArrive = 4770
enErr_DimensionWar_StationInfoError = 4771
enErr_DimensionWar_StationIsBasePos = 4769
enErr_DimensionWar_StationUnlock = 4800
enErr_DimensionWar_Station_Hided = 4777
enErr_DimensionWar_SummonNotTime = 4801
enErr_DimensionWar_Summoned = 4794
enErr_DimensionWar_TaskRewarded = 4790
enErr_DimensionWar_TeamAddQueue = 4811
enErr_DimensionWar_TeamInBattle = 4814
enErr_DimensionWar_TeamListInvalid = 4789
enErr_DimensionWar_TeamNotTrusted = 4786
enErr_DimensionWar_Team_Not_Exist = 4775
enErr_DimensionWar_Teamlock = 4815
enErr_DimensionWar_WeaponUsed = 4816
enErr_Dimension_Empty = 4840
enErr_DrawTheSugarMan_BuyCountMax = 5134
enErr_DrawTheSugarMan_ConfigError = 5132
enErr_DrawTheSugarMan_ConfigNotExist = 5131
enErr_DrawTheSugarMan_CountMax = 5133
enErr_DrawTheSugarMan_CountNotEnough = 5137
enErr_DrawTheSugarMan_GetDataErr = 5135
enErr_DrawTheSugarMan_Max = 5150
enErr_DrawTheSugarMan_NotOpen = 5129
enErr_DrawTheSugarMan_ParamError = 5130
enErr_DrawTheSugarMan_SetDataErr = 5136
enErr_DroneCenter_AdvanceNotMaxLv = 35452
enErr_DroneCenter_AdvanceNotNextLv = 35451
enErr_DroneCenter_CostStarErr = 35463
enErr_DroneCenter_DroneLevelReachMax = 35448
enErr_DroneCenter_EquipExchangeErr = 35470
enErr_DroneCenter_EquipStarErr = 35461
enErr_DroneCenter_EquipTypeErr = 35471
enErr_DroneCenter_HasTeam = 35467
enErr_DroneCenter_HasTeamNotIdle = 35466
enErr_DroneCenter_NotEnoughCondition = 35447
enErr_DroneCenter_NotEnoughGoods = 35449
enErr_DroneCenter_NotStar = 35455
enErr_DroneCenter_NotStarLvUp = 35458
enErr_DroneCenter_NotStarLvUpCost = 35462
enErr_DroneCenter_NotStarType = 35457
enErr_DroneCenter_NotUnLockStar = 35459
enErr_DroneCenter_PartLevelReachMax = 35450
enErr_DroneCenter_PlaceHasSid = 35468
enErr_DroneCenter_SetTeamSchemeErr = 35473
enErr_DroneCenter_StarLock = 35456
enErr_DroneCenter_StarSchemeNotUnlock = 35454
enErr_DroneCenter_StarTypeErr = 35472
enErr_DroneCenter_TeamIndexErr = 35465
enErr_DroneCenter_TeamIndexNotUnlock = 35453
enErr_DroneCenter_UnlockEquipStarErr = 35469
enErr_DroneCenter_UnlockStarErr = 35460
enErr_DroneCenter_ZeroStarNotReset = 35464
enErr_DroneStarPacket_Full = 2286
enErr_DynamicBattle_NoData = 4130
enErr_EmailSub_InitCfgNoExist = 5050
enErr_EmailSub_ItemNotExist = 5052
enErr_EmailSub_NotOpen = 5051
enErr_EmailSub_ParamError = 5053
enErr_EmailSub_RepeteRewards = 5055
enErr_EmailSub_UnBind = 5054
enErr_EntertainmentCity_Close = 2821
enErr_EntertainmentCity_DivinationCoinsBuyDiamondLack = 2837
enErr_EntertainmentCity_DivinationCoinsBuyFail = 2838
enErr_EntertainmentCity_DivinationCoinsBuyLimit = 2836
enErr_EntertainmentCity_DivinationCoinsIsInsufficient = 2835
enErr_EntertainmentCity_DivinationComplete = 2834
enErr_EntertainmentCity_DivinationDiamondIsInsufficient = 2833
enErr_EntertainmentCity_DivinationErr = 2832
enErr_EntertainmentCity_DivinationNoCfg = 2830
enErr_EntertainmentCity_DivinationTimesErr = 2831
enErr_EntertainmentCity_LvNoCfg = 2822
enErr_EntertainmentCity_Max = 2850
enErr_EntertainmentCity_NextRoundMaxRound = 2829
enErr_EntertainmentCity_NextRoundSplNotGet = 2828
enErr_EntertainmentCity_SelectRewardHasGetted = 2824
enErr_EntertainmentCity_SelectRewardHeroLimit = 2827
enErr_EntertainmentCity_SelectRewardNoExist = 2823
enErr_EntertainmentCity_SelectRewardNotHero = 2825
enErr_EntertainmentCity_SelectRewardRepeat = 2826
enErr_EquipEctype_Lock = 2751
enErr_EquipEctype_MAX = 2780
enErr_EquipEctype_NotPass = 2771
enErr_EquipEctype_Prized = 2772
enErr_EquipEctype_ScrollNotEnough = 2752
enErr_EquipEctype_VIPLv = 2753
enErr_EquipPacket_Full = 2284
enErr_EquipResonance_Cost_Insufficient = 3742
enErr_EquipResonance_Fail = 3741
enErr_EquipResonance_Lv_Insufficient = 3743
enErr_EquipResonance_Max = 3745
enErr_EquipResonance_Recasting = 3744
enErr_Equip_5_Slot_Lock = 240
enErr_Equip_6_Slot_Lock = 241
enErr_Equip_Exclusive_Cant_Takeoff = 243
enErr_Equip_Max_Lv = 250
enErr_Equip_Type_Err = 242
enErr_ExistsInAdvanceArenaLineUp = 1762
enErr_ExistsInArenaLineUp = 1750
enErr_ExpPassPort_LvError = 4112
enErr_ExpPassPort_TypeError = 4111
enErr_ExpPassport_ConfigError = 4110
enErr_ExtrasResourceDownload_ArealyGot = 4251
enErr_FBguide_AlreadyGot = 4401
enErr_Face_Max = 800
enErr_Face_Same = 700
enErr_Faction_CountMax = 2302
enErr_Faction_DarkLowLevel = 2308
enErr_Faction_HeroType = 2304
enErr_Faction_LevelMax = 2301
enErr_Faction_LowStage = 2307
enErr_Faction_Max = 2400
enErr_Faction_OffHours = 2303
enErr_Faction_ProOrSkillMaxLv = 2306
enErr_Faction_ProficientType = 2305
enErr_Farm_AimListEmpty = 2803
enErr_Farm_AttackBuildFail = 2796
enErr_Farm_Botany_Upgrade_Fail = 1513
enErr_Farm_Collect_Fail = 1510
enErr_Farm_Cond_NotEnough = 1550
enErr_Farm_DigFail = 2799
enErr_Farm_Dismantle_Fail = 1511
enErr_Farm_GamebleIDNoCfg = 2802
enErr_Farm_HavingGain = 2804
enErr_Farm_Main_Upgrade_Fail = 1512
enErr_Farm_Max = 2820
enErr_Farm_NoEnoughHammer = 2795
enErr_Farm_NoEnoughRes = 2792
enErr_Farm_NoEnoughScore = 2805
enErr_Farm_NoEnoughShovel = 2798
enErr_Farm_NoFindFarm = 2793
enErr_Farm_NoInAimList = 2794
enErr_Farm_NoLoadingBaseInfo = 2801
enErr_Farm_NoLoadingFarm = 2800
enErr_Farm_NoMatchDigRole = 2797
enErr_Farm_NoSpinReward = 2791
enErr_Farm_Plant_Fail = 1509
enErr_Farm_Soil_Botany_Plant_Max = 1504
enErr_Farm_Soil_Has_Botany = 1502
enErr_Farm_Soil_Lock = 1500
enErr_Farm_Soil_MainLv_NotEnough = 1503
enErr_Farm_Soil_Not_Botany = 1501
enErr_Farm_VipLvNoReach = 2806
enErr_FewHero = 19
enErr_Frame_Max = 2240
enErr_Frame_SelectFail = 2220
enErr_Freezing = 12
enErr_FullBattle_EnoughLimit = 34023
enErr_FullBattle_NoBuyCnt = 34020
enErr_FullBattle_NotEnoughItem = 34022
enErr_FullBattle_NotEnoughLotteryCnt = 34025
enErr_FullBattle_NotExist = 34021
enErr_FunctionOpen_NotBuildingLevel = 35653
enErr_FunctionOpen_NotCloseBuildingLevel = 35658
enErr_FunctionOpen_NotCloseServerDays = 35657
enErr_FunctionOpen_NotEventID = 35654
enErr_FunctionOpen_NotHooklevelID = 35652
enErr_FunctionOpen_NotRoleCreateDays = 35651
enErr_FunctionOpen_NotScientificLevel = 35655
enErr_FunctionOpen_NotServerDays = 35650
enErr_FunctionOpen_NotSpecialCondition = 35656
enErr_Galaxy_AcrossDay = 4577
enErr_Galaxy_AlreadyReward = 4588
enErr_Galaxy_Empty = 4636
enErr_Galaxy_ExitFail = 4594
enErr_Galaxy_GetRewardFail = 4592
enErr_Galaxy_GoodsHadSell = 4581
enErr_Galaxy_GoodsNotExist = 4580
enErr_Galaxy_NoDiamond = 4585
enErr_Galaxy_NoRefurbish = 4578
enErr_Galaxy_NotCoint = 4579
enErr_Galaxy_NotCondition = 4586
enErr_Galaxy_NotFinished = 4589
enErr_Galaxy_NotOpen = 4587
enErr_Galaxy_PackFull = 4582
enErr_Galaxy_RankTimes = 4593
enErr_Galaxy_RoundCheckFail = 4591
enErr_Galaxy_StateError = 4590
enErr_Galaxy_TickNotEnough = 4576
enErr_Galaxy_TimesLimit = 4584
enErr_Galaxy_TimesNotUsed = 4583
enErr_GearSupply_ActivityNotOpen = 36392
enErr_GearSupply_BigRewardIsEmpty = 36391
enErr_GearSupply_NotHasKey = 36390
enErr_GeneralTrial_ActivityNotOpen = 30600
enErr_GeneralTrial_AlreadyBeginChallenge = 30607
enErr_GeneralTrial_GeneralNotExist = 30608
enErr_GeneralTrial_GetPersonalPrizeErr = 30604
enErr_GeneralTrial_JoinTimeNotEnough = 30610
enErr_GeneralTrial_MonsterTypeError = 30612
enErr_GeneralTrial_NotAllianceMassed = 30611
enErr_GeneralTrial_NotFindAlliance = 30605
enErr_GeneralTrial_ParamsAbnormal = 30601
enErr_GeneralTrial_PersonNotEnoughCondition = 30606
enErr_GeneralTrial_ProduceGeneralErr = 30603
enErr_GeneralTrial_ProduceGeneralTooFast = 30609
enErr_GeneralTrial_SelectDifficultyErr = 30602
enErr_GetReward_SoldiersNotSpace = 34300
enErr_GlobalEvent_NotOpen = 3231
enErr_GlobalEvent_Prized = 3232
enErr_GoldHand_Buy_Max = 3151
enErr_GoldHand_Max = 3200
enErr_GoldOverflow = 4
enErr_GoldenEggs_DataErr = 36411
enErr_GoldenEggs_Expira = 36414
enErr_GoldenEggs_Haved = 36415
enErr_GoldenEggs_HavedLike = 36413
enErr_GoldenEggs_NotHaved = 36412
enErr_GoldenEggs_NotReward = 36418
enErr_GoldenEggs_NotRewardTimes = 36416
enErr_GoldenEggs_SendChatErr = 36419
enErr_GoldenEggs_ZoneBattleDuel = 36420
enErr_GoldenEggs_limit = 36417
enErr_GoldenHand_HasGet = 161
enErr_GoldenHand_Max = 170
enErr_Goods_Compose_Fail = 302
enErr_Goods_Equip_Decompose_Fail = 306
enErr_Goods_Equip_Locked = 307
enErr_Goods_Equip_Upgrade_Fail = 305
enErr_Goods_Num_NotEnough = 300
enErr_Goods_Sell_Fail = 301
enErr_Goods_Skill_Update_Fail = 303
enErr_Goods_Skill_Upgrade_Fail = 304
enErr_Halloween_ChestHadOpenedMaxToday = 36571
enErr_Halloween_Item_Recycle_Fail = 36572
enErr_Halo_Max = 1400
enErr_Halo_Restore_Fail = 1303
enErr_Halo_Retuen_Gold_Fail = 1301
enErr_Halo_Retuen_Goods_Fail = 1302
enErr_Halo_Upgrade_Fail = 1304
enErr_Halo_UseGoods_Fail = 1305
enErr_HangUp_GetRewardFail = 31863
enErr_HeroCount_Max = 26
enErr_HeroDestoryFail = 1763
enErr_HeroDestoryLowIdleStage = 1767
enErr_HeroDestoryNoHigherStar = 1766
enErr_HeroDestoryRarity = 1765
enErr_HeroDestoryStar = 1764
enErr_HeroFull = 8
enErr_HeroGoBackFail = 1759
enErr_HeroGoBackForbidMaxStar = 1756
enErr_HeroGoBackLowStar = 1761
enErr_HeroGoBackNoConfig = 1758
enErr_HeroGoBackNoDiamond = 1757
enErr_HeroGoBackNoEnoughNum = 1755
enErr_HeroGoBackNoSpace = 1760
enErr_HeroInLeagueWarBattle = 1768
enErr_HeroLowLevel = 20
enErr_HeroLowStar = 21
enErr_HeroNoExists = 1751
enErr_HeroNotFound = 11
enErr_HeroResetFail = 1754
enErr_HeroResetNoDiamond = 1753
enErr_HeroSkin_1 = 3728
enErr_HeroSkin_2 = 3729
enErr_HeroSkin_Have_Not_Skin = 3727
enErr_HeroSkin_Max = 3730
enErr_HeroSkin_Update_Fail = 3726
enErr_HeroStarOverTop = 1752
enErr_Hero_Advance_Fail1 = 215
enErr_Hero_Advance_Lv = 205
enErr_Hero_Advance_Max_Lv = 209
enErr_Hero_Advance_NotEnough = 202
enErr_Hero_Awaken_Fail1 = 216
enErr_Hero_Change_Cost_Hero_NotEnough = 212
enErr_Hero_Change_Goods_NotEnough = 210
enErr_Hero_Change_Same_Hero = 213
enErr_Hero_Change_Star_Err = 211
enErr_Hero_Coins_NotEnough = 200
enErr_Hero_Compose_Fail = 206
enErr_Hero_Compose_Fail1 = 214
enErr_Hero_ConfigError = 3223
enErr_Hero_CountLimit = 3225
enErr_Hero_Create_Decorate_Fail = 223
enErr_Hero_Decorate_Exchange_Num0_Fail = 226
enErr_Hero_Equip_Dismantle = 31717
enErr_Hero_Equip_Goods_Craft_Fail = 31718
enErr_Hero_Equip_Goods_Salvage_Fail = 31719
enErr_Hero_Equip_Make_Fail = 31716
enErr_Hero_Equip_Reward_Fail = 31714
enErr_Hero_Equip_Strength_Fail = 31715
enErr_Hero_Equip_Update_Fail = 31713
enErr_Hero_Exp_NotEnough = 201
enErr_Hero_HandBook_MaxLv = 220
enErr_Hero_HandBook_Unactived = 219
enErr_Hero_InvalidParam = 3222
enErr_Hero_ItemNotEnough = 3224
enErr_Hero_List_Buy_Max = 207
enErr_Hero_LvError = 3226
enErr_Hero_MainCityLvErr = 30360
enErr_Hero_Max = 3240
enErr_Hero_NEW_DebrisComposeFail = 30364
enErr_Hero_NEW_DebrisNotEnough = 30366
enErr_Hero_NEW_HeroAwakeFail = 30363
enErr_Hero_NEW_HeroUpGradeFail = 30361
enErr_Hero_NEW_SkillLevelLimitErr = 30365
enErr_Hero_NEW_SkillUnlockErr = 30367
enErr_Hero_NEW_SkillUpGradeFail = 30362
enErr_Hero_NotOpen = 3221
enErr_Hero_Pos_Decorate_Fail = 225
enErr_Hero_PrizeGetted = 3229
enErr_Hero_Rand_Coin_NoEnough_Fail = 227
enErr_Hero_Rand_Decorate_Fail = 222
enErr_Hero_RemoveItemFailed = 3228
enErr_Hero_Scheme_DecorateID_Fail = 224
enErr_Hero_Scheme_DecorateID_Not_Exist = 229
enErr_Hero_Scheme_DecorateID_Quality_Fail = 228
enErr_Hero_Star_NotEnough = 204
enErr_Hero_TaskCondError = 3227
enErr_Hero_Transform_CreateHero = 643
enErr_Hero_Transform_DiffSid = 640
enErr_Hero_Transform_DiffType = 642
enErr_Hero_Transform_Fail = 644
enErr_Hero_Transform_Forbiddent = 630
enErr_Hero_Transform_InAdvanceArena = 634
enErr_Hero_Transform_InArena = 631
enErr_Hero_Transform_Locked = 632
enErr_Hero_Transform_NoProcess = 641
enErr_Hero_Transform_Unprocessed = 633
enErr_Hero_Update_Decorate_Fail = 221
enErr_Hero_Update_Equip_Fail = 203
enErr_Hero_Upgrade_Fail1 = 217
enErr_Hero_Upgrade_Lv_Max = 208
enErr_HonorWall_HeroNotEnoughStar = 34702
enErr_HonorWall_NotHeroConf = 34701
enErr_HonorWall_NotOpen = 34700
enErr_HonorWall_ShardNotEnought = 34704
enErr_HonorWall_Top = 34703
enErr_Hook_LowPower = 3211
enErr_Idle_Goods_Get_Fail = 401
enErr_Idle_IsNotMiniGame = 34900
enErr_Idle_Max = 449
enErr_Idle_Res_Get_Fail = 400
enErr_Idle_Stage_ChestReward_Get_Fail = 402
enErr_Idle_Stage_ChestReward_Miss = 404
enErr_Idle_Stage_ChestReward_Order = 406
enErr_Idle_Stage_DidNotBuyGift = 408
enErr_Idle_Stage_NoReward = 410
enErr_Idle_Star_ChestReward_Get_Fail = 403
enErr_Idle_Star_ChestReward_Miss = 405
enErr_Idle_Star_ChestReward_Order = 407
enErr_Idle_Star_DidNotBuyGift = 409
enErr_Idle_Star_NoReward = 411
enErr_IllegalStr = 13
enErr_IllusionTower_Max = 2770
enErr_IllusionTower_NotPass = 2761
enErr_IllusionTower_Prized = 2762
enErr_InitmacyConfigError = 3764
enErr_InitmacyConfigNotExist = 3763
enErr_InventoryFull = 5
enErr_IsTrialHero = 4960
enErr_JumpScore_Close = 3352
enErr_JumpScore_HasGet = 3351
enErr_JumpScore_Max = 3360
enErr_KQFriend_Max = 2100
enErr_KQFriend_acceptFailed = 2081
enErr_KQFriend_addSelf = 2083
enErr_KQFriend_alreadyGived = 2079
enErr_KQFriend_alreadyInBlock = 2075
enErr_KQFriend_applyInBlock = 2085
enErr_KQFriend_blockLTooMuch = 2086
enErr_KQFriend_dbidNoExist = 2082
enErr_KQFriend_fromFriendTooMuch = 2072
enErr_KQFriend_gainTooMuch = 2076
enErr_KQFriend_giveTooMuch = 2078
enErr_KQFriend_noStar = 2077
enErr_KQFriend_notFriend = 2080
enErr_KQFriend_notInBlock = 2074
enErr_KQFriend_oppositeInBlock = 2087
enErr_KQFriend_repeatAdd = 2073
enErr_KQFriend_selfInBlock = 2088
enErr_KQFriend_starTooMuch = 2084
enErr_KQFriend_toFriendTooMuch = 2071
enErr_KastenBox_Is_Only_For_Alliance = 31753
enErr_KastenBox_Is_Only_For_Creator = 31752
enErr_KastenBox_Join_New_Alliance = 31754
enErr_KillingTower_AlreadyHave = 4142
enErr_KillingTower_CampHeroCount = 4145
enErr_KillingTower_CanNotBattle = 4141
enErr_KillingTower_CrushOverCount = 4144
enErr_KillingTower_NoData = 4140
enErr_KillingTower_NotGet = 4143
enErr_LAB_Activity_Not_End = 2904
enErr_LAB_Have_Not_Reward = 2905
enErr_LAB_Is_Open = 2901
enErr_LAB_Lack_Activity = 2900
enErr_LAB_Max = 3000
enErr_LAB_Not_Join_Activity = 2903
enErr_LAB_Not_Open = 2906
enErr_LAB_Not_President = 2902
enErr_LabourDayActivity_AccountEmpty = 4862
enErr_LabourDayActivity_AddItemFail = 4853
enErr_LabourDayActivity_HaveOneTeam = 4867
enErr_LabourDayActivity_InCurrTeam = 4865
enErr_LabourDayActivity_InLocking = 4850
enErr_LabourDayActivity_InOtherTeam = 4866
enErr_LabourDayActivity_IsNotCaptain = 4869
enErr_LabourDayActivity_IsNotMember = 4868
enErr_LabourDayActivity_IsNotSameAty = 4849
enErr_LabourDayActivity_IsShowLimit = 4872
enErr_LabourDayActivity_IsTimeOut = 4873
enErr_LabourDayActivity_ItemNotEnough = 4852
enErr_LabourDayActivity_JoinTeamCD = 4864
enErr_LabourDayActivity_JoinTeamCrowd = 4858
enErr_LabourDayActivity_PriceIsIllegal = 4851
enErr_LabourDayActivity_ServerNotOpen = 4861
enErr_LabourDayActivity_StallFail = 4859
enErr_LabourDayActivity_StallLocking = 4863
enErr_LabourDayActivity_StallMiss = 4860
enErr_LabourDayActivity_StallSellTimeLimit = 4854
enErr_LabourDayActivity_TeamMemberLimit = 4857
enErr_LabourDay_LackSellTime = 4856
enErr_LabourDay_Max = 4890
enErr_LabourDay_ShareCD = 4855
enErr_LeagueActivityBoss_AddLeagueTimeLimit = 4955
enErr_LeagueActivityBoss_leaveLeagueTimeError = 4910
enErr_LeagueBossWar_NotRewardState = 3018
enErr_LeagueBoss_BossDead = 1124
enErr_LeagueBoss_Error = 1135
enErr_LeagueBoss_GainFail = 1139
enErr_LeagueBoss_LvLimit = 1129
enErr_LeagueBoss_NotFound = 1126
enErr_LeagueBoss_ReqDuplicate = 1125
enErr_LeagueBoss_TimeOut = 1127
enErr_LeagueBoss_noCount = 1123
enErr_LeagueComp_CellBattleing = 990
enErr_LeagueComp_FailAddScene = 988
enErr_LeagueComp_ForbidBattle = 981
enErr_LeagueComp_GainConditionFail = 993
enErr_LeagueComp_HavingGain = 992
enErr_LeagueComp_InvalidCellType = 986
enErr_LeagueComp_InvalidPos = 985
enErr_LeagueComp_InvalidTaskID = 991
enErr_LeagueComp_IsMoving = 977
enErr_LeagueComp_Max = 1020
enErr_LeagueComp_NoAdjacentCell = 978
enErr_LeagueComp_NoEnoughCellDefendNum = 979
enErr_LeagueComp_NoEnoughEnergy = 975
enErr_LeagueComp_NoEnoughHp = 974
enErr_LeagueComp_NoFindLeague = 987
enErr_LeagueComp_NoFindPlayBackData = 972
enErr_LeagueComp_NoFindPlayer = 973
enErr_LeagueComp_NoFindRecord = 976
enErr_LeagueComp_NoJoinLeague = 971
enErr_LeagueComp_NoJoinScene = 980
enErr_LeagueComp_NoLeagueOfficial = 984
enErr_LeagueComp_NoOpen = 970
enErr_LeagueComp_NoOpenSpecial = 982
enErr_LeagueComp_NoSaveLineUp = 983
enErr_LeagueComp_NotFortressNearby = 989
enErr_LeagueExpel_NotTimes = 5455
enErr_LeagueHelp_CannotSeekHelp = 2521
enErr_LeagueHelp_DiffLeague = 2526
enErr_LeagueHelp_ExceedCollLimit = 2531
enErr_LeagueHelp_GetRewardFail = 2529
enErr_LeagueHelp_HelpFail = 2525
enErr_LeagueHelp_HelpIDHavingSel = 2535
enErr_LeagueHelp_HelpIDNotInPool = 2534
enErr_LeagueHelp_HelpReqCooling = 2523
enErr_LeagueHelp_Max = 2580
enErr_LeagueHelp_NotInLeague = 2530
enErr_LeagueHelp_RepetitiveHelp = 2528
enErr_LeagueHelp_SCardSelNumLimit = 2533
enErr_LeagueHelp_TaskComplete = 2527
enErr_LeagueHelp_TaskHaveComplete = 2524
enErr_LeagueHelp_TaskUnfinished = 2532
enErr_LeagueHelp_TodayReqIsMax = 2522
enErr_LeagueMedal_ErrorID = 3651
enErr_LeagueMedal_Max = 3700
enErr_LeagueMedal_NotCEO = 3652
enErr_LeagueMedal_RewardGot = 3653
enErr_LeagueMedal_RewardLocked = 3654
enErr_LeagueTreasure_FlowerNotEnough = 1200
enErr_LeagueTreasure_OutSendFlowerLimit = 1201
enErr_LeagueTreasure_OutSendPlayerLimit = 1202
enErr_LeagueWar_ActivityNotEnough = 3005
enErr_LeagueWar_AttackExhausted = 3010
enErr_LeagueWar_BattleCD = 3014
enErr_LeagueWar_JoinCDforApply = 3006
enErr_LeagueWar_JoinCDforReward = 3001
enErr_LeagueWar_Max = 3100
enErr_LeagueWar_NoReward = 3002
enErr_LeagueWar_NotApplied = 3009
enErr_LeagueWar_NotApplyState = 3003
enErr_LeagueWar_NotAttackState = 3008
enErr_LeagueWar_NotJoinLeague = 3004
enErr_LeagueWar_NotPrepareState = 3007
enErr_LeagueWar_NotRevived = 3012
enErr_LeagueWar_UsedCard = 3011
enErr_League_AlreadyOfficer = 1116
enErr_League_AnnounceTooLong = 1121
enErr_League_ApplyFull = 1102
enErr_League_ApplyInLeague = 1110
enErr_League_Apply_AlreadyIn = 1144
enErr_League_Apply_CD = 1143
enErr_League_Apply_CeLimit = 1142
enErr_League_Apply_LvLimit = 1141
enErr_League_Apply_Reject = 1140
enErr_League_Apply_TooMuch = 1232
enErr_League_CEO_Exit = 1146
enErr_League_CEO_Offline_Intime = 1148
enErr_League_Create_CD = 1145
enErr_League_Diff_Segment = 3017
enErr_League_Dismiss_Member_TooMuch = 1231
enErr_League_ExpelCountMax = 1108
enErr_League_ExpelOfficer = 1107
enErr_League_IllegalStr = 4400
enErr_League_Impeach_CD = 1149
enErr_League_Impeach_OverTime = 1230
enErr_League_Impeaching = 1233
enErr_League_InLeague = 1101
enErr_League_MailCountMax = 1113
enErr_League_MailNoTarget = 1114
enErr_League_Max = 1300
enErr_League_MemberFull = 1112
enErr_League_ModifyNameTooLong = 1118
enErr_League_ModifyNameTooShort = 1119
enErr_League_ModifyType = 1115
enErr_League_Name_CenterError = 1131
enErr_League_Name_Duplicate = 1130
enErr_League_Name_GameError = 1132
enErr_League_Name_NoFree = 1134
enErr_League_Name_Wait = 1133
enErr_League_NoApply = 1109
enErr_League_NoMember = 1105
enErr_League_NoRoleInfo = 1136
enErr_League_NotExist = 1100
enErr_League_NotInLeague = 1104
enErr_League_NotOfficer = 1106
enErr_League_Officier_Limit = 1147
enErr_League_Outnumber = 1103
enErr_League_Recruit_CD = 3016
enErr_League_SignCountDown = 1128
enErr_League_TransferCountMax = 1117
enErr_League_TransferFail = 1138
enErr_League_TransferNoLeague = 1120
enErr_League_TransferOnMatch = 1137
enErr_League_TransferTimeOut = 1122
enErr_League_repeat_change_position = 3015
enErr_League_to_Apply_CD = 3013
enErr_LegendCantPrize = 4004
enErr_LegendConfigError = 4003
enErr_LegendGroupError = 4014
enErr_LegendHeroStarError = 4015
enErr_LegendNoData = 4008
enErr_LegendNotOpen = 4013
enErr_LegendNotSign = 4002
enErr_LegendPrized = 4005
enErr_LegendTodayAtt = 4009
enErr_LegendTroopError = 4010
enErr_LegendType_Error = 4001
enErr_LegendUpdateDBError = 4011
enErr_Legend_BattledHeroCantCost = 4020
enErr_Legend_CantSignExperience = 4021
enErr_Legend_MAX = 4050
enErr_Legend_MaxBattleCount = 4018
enErr_Legend_NotBattlePeriod = 4022
enErr_Legend_SelfTroopError = 4016
enErr_Legend_SetDefTroopError = 4019
enErr_Legend_SignCooling = 4023
enErr_Legend_Signed = 4024
enErr_Legend_TargetTroopError = 4017
enErr_LengendNotGroup = 4007
enErr_LengendNotSign = 4006
enErr_LifetimeCard_HaveGetted = 3751
enErr_LifetimeCard_Max = 3760
enErr_LifetimeCard_NoBuy = 3750
enErr_LikeRole_Fail = 30711
enErr_LikeRole_GetRecordFailed = 30713
enErr_LikeRole_Liked = 30715
enErr_LikeRole_NotEnough = 30712
enErr_LikeRole_Self_Fail = 30714
enErr_LineUp_InvalidID = 3301
enErr_LineUp_Limit = 3302
enErr_LineUp_Max = 3310
enErr_LineUp_NoCancle = 3303
enErr_LineUp_UpdateSuccess = 3304
enErr_LobbySvr_BuildInfo = 110
enErr_LobbySvr_CheckALS = 112
enErr_LobbySvr_Create = 116
enErr_LobbySvr_CreateActorFail = 114
enErr_LobbySvr_Create_TooMany = 121
enErr_LobbySvr_ExportActorFail = 115
enErr_LobbySvr_Login = 111
enErr_LobbySvr_RoleList = 117
enErr_LobbySvr_Select = 119
enErr_LobbySvr_Shoot = 118
enErr_LobbySvr_Verify = 113
enErr_LobbySvr_Version = 120
enErr_LobbySvr_WorldID_Error = 122
enErr_LobbyTeam_CaptainOnly = 80
enErr_LobbyTeam_NotReady = 81
enErr_LoginSvr_AutoLogin = 91
enErr_LoginSvr_AutoLoginChkAls = 92
enErr_LoginSvr_Login = 90
enErr_Login_Http_Fail = 64
enErr_Login_IOS_Forbidden = 61
enErr_Login_Interior_illegal_Dev = 62
enErr_Login_ServerMaintenance = 60
enErr_Login_White_List_Audit = 63
enErr_LotteryWish_Fail = 491
enErr_LotteryWish_NoFreeRefresh = 492
enErr_LotteryWish_NotOpen = 490
enErr_LotteryWish_RefreshFail = 493
enErr_Lottery_ChoiceLotID = 4104
enErr_Lottery_Choice_Max = 4200
enErr_Lottery_Fail = 485
enErr_Lottery_NewHero_SGained = 486
enErr_Lottery_NewHero_SPGained = 487
enErr_Lottery_NoChoice = 4101
enErr_Lottery_NoChoiceHero = 4103
enErr_Lottery_NoFree = 480
enErr_Lottery_NoMoney = 482
enErr_Lottery_NoTicket = 481
enErr_Lottery_NotOpen = 484
enErr_Lottery_PoolNoHero = 4102
enErr_Lottery_ReachMax = 483
enErr_Lottery_TimeLimit = 4105
enErr_LuckFlop_BuyTimeNotEnough = 5452
enErr_LuckFlop_CardGetted = 5454
enErr_LuckFlop_CountNotEnough = 5451
enErr_LuckFlop_NotPaly = 5453
enErr_Mail_AddresseeNotExist = 39
enErr_Mail_AttachmentAlreadyGot = 34
enErr_Mail_AttachmentGetFailed = 35
enErr_Mail_AttachmentNotExist = 33
enErr_Mail_GetListFailed = 37
enErr_Mail_GetMailDetailFail = 40
enErr_Mail_Invalid = 31
enErr_Mail_InventoryFull = 36
enErr_Mail_NotExist = 32
enErr_Mail_SendMailFailed = 38
enErr_Mail_SendReqFailed = 30
enErr_MakeFoodActivityCfgNoExist = 5110
enErr_MakeFoodActivityCoolDown = 5113
enErr_MakeFoodActivityItemNotEnough = 5111
enErr_MakeFoodActivityOrderExpire = 5115
enErr_MakeFoodActivityOrderNotExist = 5114
enErr_MakeFoodActivityOrderTransacted = 5116
enErr_MakeFoodActivityOrderTransacting = 5117
enErr_MakeFoodActivityTimesLimit = 5112
enErr_MakeFoodActivity_NotOpen = 5109
enErr_March_AddTeamError = 30550
enErr_March_AllianceNotAttack = 30552
enErr_March_ChangeTargetError = 30547
enErr_March_CondNotMeet = 30554
enErr_March_CreateError = 30548
enErr_March_HasSameTarget = 30557
enErr_March_LineTypeError = 30549
enErr_March_NotChangeTarget = 30546
enErr_March_SafetyTimeNotAttack = 30553
enErr_March_SpeedError = 30545
enErr_March_TeamNotInCity = 30551
enErr_Mass_TeamTimeOut = 30555
enErr_Mass_TeamTimeOutNotPlay = 30564
enErr_Match_Failed = 70
enErr_Match_RegionUnopened = 71
enErr_Mate_ApplySelf = 1603
enErr_Mate_CenterCreate = 1605
enErr_Mate_GameCreate = 1606
enErr_Mate_GameDB_EndFail = 1623
enErr_Mate_GameModifyRelation = 1622
enErr_Mate_HasTargetApply = 1624
enErr_Mate_LoadTargetRole = 1604
enErr_Mate_LogReward_Already = 1615
enErr_Mate_LogReward_NotExist = 1613
enErr_Mate_LogReward_RoleNotMatch = 1614
enErr_Mate_Max = 1690
enErr_Mate_Monster_NoEnergy = 1621
enErr_Mate_Monster_NotExist = 1620
enErr_Mate_Monster_NotTarget = 1619
enErr_Mate_NoApply = 1612
enErr_Mate_NoFoundMate = 1608
enErr_Mate_NoFoundSelf = 1610
enErr_Mate_NoFoundTarget = 1609
enErr_Mate_NoMate = 1607
enErr_Mate_NotReady = 1616
enErr_Mate_RoleNotExist = 1611
enErr_Mate_SelfHasMate = 1600
enErr_Mate_TargetApplyMax = 1602
enErr_Mate_TargetHasMate = 1601
enErr_Mate_Water_ErrorPos = 1617
enErr_Mate_Water_NoTree = 1618
enErr_Maze_Lock = 1931
enErr_MergeServer_Close = 3251
enErr_MergeServer_GiftAlreadyGot = 3252
enErr_MergeServer_Max = 3300
enErr_MergeServer_NotJoin = 3250
enErr_Mill_CanNotFindOrder = 1902
enErr_Mill_CfgAbnormal = 1904
enErr_Mill_GetRewardFail = 1906
enErr_Mill_Max = 1910
enErr_Mill_MaxLv = 1905
enErr_Mill_OrderHasBegun = 1903
enErr_Mill_OrderTooMuch = 1901
enErr_MiniGameRank_AddRankFail = 5223
enErr_MiniGameRank_ClickFrequently = 5224
enErr_MiniGameRank_GetRankFail = 5222
enErr_MiniGameRank_Max = 5230
enErr_MiniGameRank_ParamError = 5221
enErr_MiniGameRank_ScoreErr = 5225
enErr_ModuleAct_Close = 3781
enErr_ModuleAct_Lock = 3782
enErr_ModuleNoOpen = 23
enErr_ModuleTempClose = 5175
enErr_Module_Max = 3880
enErr_MonsterComing_NotOpen = 36484
enErr_MonsterComing_Reward_Already = 36480
enErr_MonsterComing_Stage_NotOpen = 36481
enErr_MonsterComing_Task_Unfinished = 36482
enErr_MonsterComing_Unfinished_MiniGame = 36483
enErr_MonthCardGift_ConfigError = 4356
enErr_MonthCardGift_IdleStageError = 4361
enErr_MonthCardGift_LIMITBUY = 34307
enErr_MonthCardGift_Max = 34310
enErr_MonthCardGift_NotActived = 34306
enErr_MonthCardGift_NotOnline = 4360
enErr_MonthCardGift_NotStart = 4359
enErr_MonthCardGift_NotSuperUser = 4358
enErr_MonthCardGift_ResError = 4357
enErr_MonthCardGift_SetCompleteTimes = 4363
enErr_MonthCardGift_limitTimeError = 4362
enErr_MonthTask_HavingGain = 3702
enErr_MonthTask_Max = 3720
enErr_MonthTask_NoSelRewardID = 3703
enErr_MonthTask_NoSpace = 3704
enErr_MonthTask_UnFinishAll = 3701
enErr_MonthlyFund_LuxuryFund_Activated = 3467
enErr_MonthlyFund_Max = 3470
enErr_MonthlyFund_NormalFund_Activated = 3466
enErr_MonthlyFund_Not_Open = 3463
enErr_MonthlyFund_Not_Reward = 3462
enErr_MonthlyFund_Not_SuperMonthCard = 3465
enErr_MonthlyFund_Reward_Fail = 3464
enErr_MonthlyFund_Unactive = 3461
enErr_MonthlyFund_UnactiveEx = 3468
enErr_MonthlyTask_TreasureRare_Closed = 4402
enErr_MonthlyTask_TreasureRare_Config = 4404
enErr_MonthlyTask_TreasureRare_LackOfItem = 4405
enErr_MonthlyTask_TreasureRare_Max = 4414
enErr_MonthlyTask_TreasureRare_NoChosenReward = 4412
enErr_MonthlyTask_TreasureRare_NoRewardInconfig = 4409
enErr_MonthlyTask_TreasureRare_RemoveFail = 4406
enErr_MonthlyTask_TreasureRare_ReqParam = 4408
enErr_MonthlyTask_TreasureRare_RewardReceived = 4413
enErr_MonthlyTask_TreasureRare_RewardTimeLimit = 4410
enErr_MonthlyTask_TreasureRare_SetSystem = 4411
enErr_MonthlyTask_TreasureRare_UnlockFail = 4407
enErr_MonthlyTask_TreasureRare_Unlocked = 4403
enErr_MultiLineUp_BattleError = 3573
enErr_MultiLineUp_CheckPointErr = 3574
enErr_MultiLineUp_Max = 3580
enErr_MultiLineUp_MonsterCfgErr = 3571
enErr_MultiLineUp_PalsParamErr = 3572
enErr_NC_AA_InCD = 32014
enErr_NC_AA_NotEnoughTime = 32013
enErr_NC_AA_NotInBattle = 32015
enErr_NC_AA_NotPerson = 32016
enErr_NC_AA_NotPower = 32012
enErr_NC_ALLIANCE_NOT_DECLAREWAR = 32039
enErr_NC_ATK_CongressMax = 32034
enErr_NC_ATK_CongressNear = 32033
enErr_NC_ATK_CongressProtect = 32032
enErr_NC_ATK_NotAlliacne = 32031
enErr_NC_ATK_NotDW = 32035
enErr_NC_ATK_NotDWTime = 32036
enErr_NC_Abandon_CanNot = 32029
enErr_NC_Abandon_InCooling = 32028
enErr_NC_Abandon_NotAbandon = 32027
enErr_NC_Abandon_NotAlliance = 32023
enErr_NC_Abandon_NotOwner = 32025
enErr_NC_Abandon_NotPower = 32024
enErr_NC_Abandon_Params = 32026
enErr_NC_DW_DayMaxNum = 32007
enErr_NC_DW_InProtect = 32005
enErr_NC_DW_MaxNum = 32006
enErr_NC_DW_NotDW = 32011
enErr_NC_DW_NotEnoughMaxLv = 32010
enErr_NC_DW_NotEnoughPerson = 32004
enErr_NC_DW_NotEnoughTime = 32003
enErr_NC_DW_NotHaveNear = 32009
enErr_NC_DW_NotPower = 32002
enErr_NC_DW_OwnMaxNum = 32008
enErr_NC_DetectNotInBattle = 32001
enErr_NC_DetectNotMine = 32030
enErr_NC_Reward_Already = 32022
enErr_NC_Reward_NotAlliance = 32017
enErr_NC_Reward_NotFirst = 32021
enErr_NC_Reward_NotHave = 32020
enErr_NC_Reward_NotOwn = 32018
enErr_NC_Reward_Params = 32019
enErr_NationalFlag_IN_CD = 36619
enErr_NationalFlag_NotAlliance = 36616
enErr_NationalFlag_NotConfig = 36617
enErr_NationalFlag_NotCountryID = 36613
enErr_NationalFlag_NotFlagType = 36610
enErr_NationalFlag_NotLanguageId = 36614
enErr_NationalFlag_NotOpen = 36611
enErr_NationalFlag_NotR5 = 36615
enErr_NationalFlag_NotSKDChannel = 36612
enErr_NationalFlag_R5NotCondition = 36618
enErr_NewRedeemCode_AccountHaveActivity = 2145
enErr_NewRedeemCode_ChannelNotAllow = 2142
enErr_NewRedeemCode_DiffAccount = 2137
enErr_NewRedeemCode_DiffPhone = 2138
enErr_NewRedeemCode_HaveAtyOrHaveUsed = 2147
enErr_NewRedeemCode_HaveBeenUsed = 2132
enErr_NewRedeemCode_HaveNotStarted = 2136
enErr_NewRedeemCode_HaveVioded = 2135
enErr_NewRedeemCode_Inexistence = 2131
enErr_NewRedeemCode_Invalid = 2133
enErr_NewRedeemCode_LvNotEnough = 2140
enErr_NewRedeemCode_Max = 2170
enErr_NewRedeemCode_MoneyNotEnough = 2139
enErr_NewRedeemCode_NoTurnedOn = 2134
enErr_NewRedeemCode_RoleHaveActivity = 2146
enErr_NewRedeemCode_TimePayNotEnough = 2144
enErr_NewRedeemCode_VersonNotAllow = 2143
enErr_NewRedeemCode_ZoneNotAllow = 2141
enErr_NewSvrTopRace_AlreadyLiked = 4071
enErr_NewSvrTopRace_ApplaudFrequently = 4070
enErr_NewSvrTopRace_ApplaudNoArenaID = 4072
enErr_NewSvrTopRace_ApplaudNoReward = 4074
enErr_NewSvrTopRace_ApplaudNotTop1 = 4073
enErr_NewSvrTopRace_Close = 4051
enErr_NewSvrTopRace_Max = 4100
enErr_NewSvrTopRace_NoArena = 4052
enErr_NewSvrTopRace_NoCurBattle = 4055
enErr_NewSvrTopRace_NoJoin = 4056
enErr_NewSvrTopRace_NoRank = 4061
enErr_NewSvrTopRace_NoRoleInfo = 4053
enErr_NewSvrTopRace_NoTop18Pal = 4054
enErr_NewSvrTopRace_NotRole = 4075
enErr_NewSvrTopRace_NotSaveTroop = 4076
enErr_NewSvrTopRace_OnTopRaceList = 4067
enErr_NewSvrTopRace_OutOf64 = 4058
enErr_NewSvrTopRace_OutOfSetLineUpTime = 4069
enErr_NewSvrTopRace_Pal_InLineup = 4077
enErr_NewSvrTopRace_RankError = 4059
enErr_NewSvrTopRace_RankRewardHasGet = 4060
enErr_NewSvrTopRace_SettleNoResult = 4057
enErr_NewSvrTopRace_VoteAfter23 = 4062
enErr_NewSvrTopRace_VoteCoinLess = 4065
enErr_NewSvrTopRace_VoteComplete = 4064
enErr_NewSvrTopRace_VoteNoPush = 4063
enErr_NewSvrTopRace_VoteOnBattle = 4068
enErr_NewSvrTopRace_VotePushDayMore7 = 4066
enErr_NewYearAct_GetLimit = 4512
enErr_NewYearAct_Redpacket_Expire = 4503
enErr_NewYearAct_Redpacket_Expire_Over = 4506
enErr_NewYearAct_Redpacket_Garb_Over = 4508
enErr_NewYearAct_Redpacket_Had_Get = 4507
enErr_NewYearAct_Redpacket_Numbers_Over = 4502
enErr_NewYearAct_Repeat_Send_FuCard = 4510
enErr_NewYearAct_SendLimit = 4511
enErr_NewYearAct_Today_Give_Redpacket_Over = 4504
enErr_NewYearAct_Today_Grab_Redpacket_Over = 4505
enErr_NewYearAct_User_No_Redpacket = 4500
enErr_NewYearAct_User_No_This_FuCard = 4509
enErr_NewYearAct_User_Remove_Redpacket_Fail = 4501
enErr_NoError = 0
enErr_NoTargetReward = 4122
enErr_NotEnoughCond = 10
enErr_NotEnoughGold = 7
enErr_NotEnoughGoods = 9
enErr_NotEnoughMoney = 6
enErr_Not_Do_Join_Alliance_Less_24Hours = 30436
enErr_Not_Territory = 459
enErr_OpenNormalBox_Fail = 30247
enErr_OpenRandomBox_Fail = 30248
enErr_OpenSelectBox_Fail = 30249
enErr_Operative_Code_Pirzed = 3104
enErr_Operative_Condition = 3106
enErr_Operative_Email_Invalid = 3101
enErr_Operative_Email_Prized = 3103
enErr_Operative_Max = 3150
enErr_Operative_NotBindEmail = 3102
enErr_Operative_Share_Pirzed = 3105
enErr_PalPlaceIllegal = 17
enErr_PalSelectCard_CampCardUseFail = 2110
enErr_PalSelectCard_CampCfgError = 2109
enErr_PalSelectCard_CampError = 2108
enErr_PalSelectCard_CfgError = 2104
enErr_PalSelectCard_GoodsNoExist = 2101
enErr_PalSelectCard_GoodsNotCampCard = 2107
enErr_PalSelectCard_GoodsNotEnough = 2103
enErr_PalSelectCard_GoodsNotPalCard = 2102
enErr_PalSelectCard_Max = 2130
enErr_PalSelectCard_PalPacketHeroLimit = 2112
enErr_PalSelectCard_PalPacketNotEnough = 2111
enErr_PalSelectCard_ReduceGoodsFail = 2105
enErr_PalSelectCard_RewardIDNotExist = 2106
enErr_ParamError = 3762
enErr_PassPort_AreadyBuy = 2055
enErr_Passport_HavingGain = 2053
enErr_Passport_InvalidID = 2051
enErr_Passport_InvalidType = 2050
enErr_Passport_Max = 2070
enErr_Passport_NoOpen = 2054
enErr_Passport_NoReach = 2052
enErr_Peak_AddRelicFail = 2617
enErr_Peak_AlreadyGotReward = 2618
enErr_Peak_BatteryTransmitFail = 2638
enErr_Peak_BattleError = 2612
enErr_Peak_BattleNoHero = 2610
enErr_Peak_BattleNoLiveHero = 2611
enErr_Peak_BattleNoParam = 2609
enErr_Peak_CarriageIndex = 2607
enErr_Peak_ErrorPosIndex = 2627
enErr_Peak_EventNotExist = 2604
enErr_Peak_EventTypeError = 2605
enErr_Peak_FinishedLevel = 2629
enErr_Peak_GetBoxFail = 2633
enErr_Peak_GridCantWalk = 2625
enErr_Peak_GridHasEvent = 2626
enErr_Peak_HeroCreate = 2608
enErr_Peak_HeroNotSel = 2606
enErr_Peak_InteractDisable = 2622
enErr_Peak_InteractMax = 2621
enErr_Peak_LevelCreateFail = 2630
enErr_Peak_LevelDiffPlaying = 2628
enErr_Peak_LevelLock = 2603
enErr_Peak_LevelNotExist = 2602
enErr_Peak_LevelNotPlaying = 2623
enErr_Peak_LevelResetFail = 2631
enErr_Peak_Lock = 2601
enErr_Peak_MAX = 2699
enErr_Peak_MapError = 2634
enErr_Peak_MapNotFound = 2624
enErr_Peak_MoveToPosFail = 2639
enErr_Peak_NoSelectRelic = 2614
enErr_Peak_RefreshConfigError = 2632
enErr_Peak_RelicNotExist = 2616
enErr_Peak_RewardFail = 2619
enErr_Peak_SelRelicNotExist = 2615
enErr_Peak_SelectRelic = 2613
enErr_Peak_SnowManAllArrive = 2636
enErr_Peak_SnowManNotMove = 2635
enErr_Peak_SnowManNotMoveTwenty = 2637
enErr_Peak_TooFar = 2620
enErr_PickTheRoute_BoxType = 5056
enErr_PickTheRoute_DailyShareBefore = 4988
enErr_PickTheRoute_Event_NoRewardDesign = 4989
enErr_PickTheRoute_Event_PathIllegal = 4990
enErr_PickTheRoute_Event_Reward_Received = 4986
enErr_PickTheRoute_Event_SaveArchived_Failed = 4987
enErr_PickTheRoute_GoodsNotExsit = 5057
enErr_PickTheRoute_GoodsNotInConfig = 5058
enErr_PickTheRoute_Level_Closed = 4985
enErr_PickTheRoute_PassPreviseLevel = 5060
enErr_PickTheRoute_UseNumZero = 5059
enErr_PiggyBank_AbnormalErr = 5161
enErr_PiggyBank_AccrualErr = 5162
enErr_PiggyBank_ConfigError = 5154
enErr_PiggyBank_ConfigNotExist = 5153
enErr_PiggyBank_GetDataErr = 5155
enErr_PiggyBank_Max = 5200
enErr_PiggyBank_NotFinishTask = 5159
enErr_PiggyBank_NotOpen = 5151
enErr_PiggyBank_NotSaveMoney = 5157
enErr_PiggyBank_NotWithdrawMoney = 5158
enErr_PiggyBank_ParamError = 5152
enErr_PiggyBank_RepeteRewards = 5160
enErr_PiggyBank_SetDataErr = 5156
enErr_Playback_Max = 2860
enErr_Playback_No_Exist = 2851
enErr_PlayerVipLv = 22
enErr_Point_NotEnoughGet = 35551
enErr_Polt_BattleFail = 3731
enErr_Polt_Max = 3735
enErr_Polt_Pass_Last_Stage = 3734
enErr_Polt_StageLv_Err = 3732
enErr_Polt_Time_Err = 3733
enErr_QIdle_BuyBattleFail = 1884
enErr_QIdle_LVLessOpenLv = 1881
enErr_QIdle_MAX = 1900
enErr_QIdle_NoQBatleTimes = 1883
enErr_QIdle_NoRemainBuyTimes = 1882
enErr_RadarMission_AlreadyDigging = 30435
enErr_RadarMission_CityInZone = 30442
enErr_RadarMission_ExecuteTask = 30439
enErr_RadarMission_Expire = 30433
enErr_RadarMission_IdError = 30430
enErr_RadarMission_NoTarget = 30434
enErr_RadarMission_NotAssist = 30432
enErr_RadarMission_NotDigTreasure = 30438
enErr_RadarMission_NotFindPos = 30443
enErr_RadarMission_NotGet = 30431
enErr_RadarMission_NotInNativeSandbox = 30444
enErr_RadarMission_NotOpen = 30441
enErr_RadarMission_Not_Help_Target = 30437
enErr_RadarMission_TargetUpdate = 30440
enErr_RankAchv_InvalidParam = 2002
enErr_RankAchv_InvalidTaskID = 2001
enErr_RankAchv_Max = 2030
enErr_RankAchv_NoReachOpenLv = 2003
enErr_RebirthSpace_BattleLock = 4329
enErr_RebirthSpace_BatttleInSettle = 4326
enErr_RebirthSpace_BuyCountEmpty = 4325
enErr_RebirthSpace_ChallengeCountEmpty = 4324
enErr_RebirthSpace_HeroForbidden = 4328
enErr_RebirthSpace_Max = 4335
enErr_RebirthSpace_RecommandEmpty = 4323
enErr_RebirthSpace_UserIsForbidden = 4327
enErr_RechargeGift_BuyTimesOverLimit = 34503
enErr_RechargeGift_CanNotSelect = 34507
enErr_RechargeGift_EndTime = 34502
enErr_RechargeGift_ErrParams = 34500
enErr_RechargeGift_FunctionOpen = 34510
enErr_RechargeGift_MutuallyExclusive = 34506
enErr_RechargeGift_NotBuyPrevious = 34509
enErr_RechargeGift_NotFindRechargeInfo = 34501
enErr_RechargeGift_NotOpen = 34504
enErr_RechargeGift_NotSelectGift = 34505
enErr_RechargeGift_StartTime = 34508
enErr_Recharge_ActivityLimite = 142
enErr_Recharge_EnterCityLimit = 144
enErr_Recharge_IllegalPartnerID = 141
enErr_Recharge_LifetimeCardLimite = 145
enErr_Recharge_Max = 160
enErr_Recharge_MonthCardLimit = 143
enErr_Recharge_OffShelves = 146
enErr_Recharge_PayNumNotMatch = 140
enErr_RecomCard_3_Fail = 3723
enErr_RecomCard_4_Fail = 3724
enErr_RecomCard_GetCard_Fail = 3721
enErr_RecomCard_Max = 3725
enErr_RecomCard_SetCard_Fail = 3722
enErr_RedeemCode_ActivationCodeNotEnabled = 1866
enErr_RedeemCode_CardHavePassDue = 1867
enErr_RedeemCode_CardIsInvalidOrBanned = 1862
enErr_RedeemCode_CardIsInvalidOrHaveUsed = 1863
enErr_RedeemCode_CardIsInvalidOrObsolete = 1864
enErr_RedeemCode_CardTimeNotOpen = 1868
enErr_RedeemCode_ChannelErrors = 1869
enErr_RedeemCode_GameWorldBanThisCard = 1865
enErr_RedeemCode_HaveBeenActivated = 1871
enErr_RedeemCode_Max = 1880
enErr_RedeemCode_NotEligibleForActivation = 1861
enErr_RedeemCode_QuantityUpToCeiling = 1870
enErr_Reforge_CantReforge = 3555
enErr_Reforge_ConfigFailed = 3554
enErr_Reforge_CostNotEnough = 3551
enErr_Reforge_CostRemoveFaild = 3552
enErr_Reforge_EquipNotExist = 3553
enErr_Reforge_Max = 3560
enErr_RemoteBattleSvr_CreateFail = 5456
enErr_RemoteBattleSvr_MAX = 5460
enErr_ResourceFillLvBox_Fail = 30251
enErr_ResourceFillSelectBox_Fail = 30250
enErr_ReturnAct_Lock = 3314
enErr_ReturnAct_Max = 3350
enErr_ReturnAct_NoReward = 3311
enErr_ReturnAct_NotOpen = 3312
enErr_ReturnAct_Rewarded = 3313
enErr_Reward_Only_Can_Receive_Once = 31751
enErr_RoleFrame_2 = 36552
enErr_RoleFrame_3 = 36553
enErr_RoleFrame_4 = 36554
enErr_Role_FaceProp_NotWear = 702
enErr_Role_Face_Image_Verify = 708
enErr_Role_Face_Image_Verify_Fail = 709
enErr_Role_Key_Words = 703
enErr_Role_Name_Exist = 701
enErr_Role_Plate_Fail = 36551
enErr_Role_Sex_Exist = 30709
enErr_Role_Unload_Face_Prop_Fail = 704
enErr_Role_Update_Face_Fail = 706
enErr_Role_Update_Face_Prop_Fail = 705
enErr_Role_Update_Name_Fail = 707
enErr_Role_Update_Schloss_Effect_Fail = 30707
enErr_Role_Update_Schloss_Fail = 30710
enErr_Role_Update_Sex_Fail = 30708
enErr_RookieTargetAct_ActEnd = 3739
enErr_RookieTargetAct_Have_Reward = 3737
enErr_RookieTargetAct_Max = 3740
enErr_RookieTargetAct_Not_Reward = 3738
enErr_RookieTargetAct_Reward_Fail = 3736
enErr_SX_REINFORCE_NOTTEAM = 32037
enErr_SX_ZONE_NOTSAFETY = 32038
enErr_SameHeroID = 18
enErr_Sandbox_AllianceMove = 30578
enErr_Sandbox_AlreadyGoing = 30523
enErr_Sandbox_AlreadyInTeam = 30517
enErr_Sandbox_AlreayMassTarget = 30560
enErr_Sandbox_CastleEffectError = 30572
enErr_Sandbox_CityInZoneNotSpeedUp = 30561
enErr_Sandbox_CityNotExist = 30509
enErr_Sandbox_CrossNotAttackTarget = 30574
enErr_Sandbox_CrossTypeError = 30559
enErr_Sandbox_CurNotSpeedUp = 30563
enErr_Sandbox_DetectAlliance = 30520
enErr_Sandbox_DetectInCDTime = 30576
enErr_Sandbox_DetectInSafetyTime = 30533
enErr_Sandbox_DetectNum = 30519
enErr_Sandbox_DetectType = 30522
enErr_Sandbox_Detect_InIdle = 30566
enErr_Sandbox_Detect_NoGointStete = 30565
enErr_Sandbox_EntityNotExit = 30571
enErr_Sandbox_ExteriorError = 30511
enErr_Sandbox_FixedMonsterAsTarget = 30544
enErr_Sandbox_FuncNotOpen = 30501
enErr_Sandbox_HaveBuffNoSafety = 30537
enErr_Sandbox_InsufficientRelocationLv = 30575
enErr_Sandbox_MarchInZoneNotSpeedUp = 30562
enErr_Sandbox_MarchNotExit = 30570
enErr_Sandbox_MarchSoldierDataError = 30543
enErr_Sandbox_MassAlreadyInTeam = 30539
enErr_Sandbox_MassCreateFail = 30541
enErr_Sandbox_MassInSafetyTime = 30542
enErr_Sandbox_MassIsLeader = 30527
enErr_Sandbox_MassMemberFull = 30524
enErr_Sandbox_MassNotIsLeader = 30526
enErr_Sandbox_MassNotMember = 30525
enErr_Sandbox_MassParamError = 30528
enErr_Sandbox_MassTeamNoJoin = 30532
enErr_Sandbox_MoreBuffNoSafety = 30538
enErr_Sandbox_MoveFail = 30516
enErr_Sandbox_NotAttack = 30515
enErr_Sandbox_NotCover = 30556
enErr_Sandbox_NotExist = 30530
enErr_Sandbox_NotTeam = 30518
enErr_Sandbox_ParamsError = 30521
enErr_Sandbox_PlateError = 30577
enErr_Sandbox_PosError = 30510
enErr_Sandbox_ReinforceAlliance = 30536
enErr_Sandbox_ReinforceFull = 30534
enErr_Sandbox_ReinforceOneTeam = 30535
enErr_Sandbox_SaveTeam_Failed = 30015
enErr_Sandbox_SearchNotPos = 30514
enErr_Sandbox_TargetInSafeArea = 30573
enErr_Sandbox_TargetNot = 30512
enErr_Sandbox_TargetNotCanMass = 30531
enErr_Sandbox_TeamError = 30529
enErr_Sandbox_TeamInOutside = 30513
enErr_Sandbox_TeamNotReturn = 30540
enErr_Sandbox_VisualAlready = 30567
enErr_Sandbox_VisualEnterFail = 30568
enErr_Sandbox_VisualInEntering = 30569
enErr_Sandbox_WonderDie = 30558
enErr_ScientificResearch_AlreadyResearch = 30207
enErr_ScientificResearch_GetScientificFail = 30204
enErr_ScientificResearch_ItemsNotEnough = 30205
enErr_ScientificResearch_NoEmptyBuild = 30208
enErr_ScientificResearch_NotEnoughCondition = 30201
enErr_ScientificResearch_NotFindBuilding = 30203
enErr_ScientificResearch_NotFindInfo = 30202
enErr_ScientificResearch_NotOpenBuilding = 30200
enErr_ScientificResearch_QueueIsEnough = 30206
enErr_SelectArtifactBox_ArtifactIsGetted = 4914
enErr_SelectArtifactBox_IsNotArtifactBox = 4912
enErr_SelectArtifactBox_ItemNotExist = 4911
enErr_SelectArtifactBox_RewardFail = 4916
enErr_SelectArtifactBox_RewardIDNotExist = 4913
enErr_SelectArtifactBox_RewardNotArtifact = 4915
enErr_SevenDayActivity_HavingGain = 1802
enErr_SevenDayActivity_InvalidTime = 1801
enErr_SevenDayActivity_Max = 1820
enErr_SevenDayChallenge_BuyLimit = 1845
enErr_SevenDayChallenge_HavingGain = 1842
enErr_SevenDayChallenge_InvalidTaskID = 1843
enErr_SevenDayChallenge_InvalidTime = 1841
enErr_SevenDayChallenge_Max = 1860
enErr_SevenDayChallenge_NoSpace = 1846
enErr_SevenDayChallenge_NotEnoughIntergal = 1844
enErr_SevenDayLogin_CannotGot = 36451
enErr_SevenDayLogin_RepeatGotAward = 36452
enErr_ShelfGoodCreateOrder = 36373
enErr_ShelfGoodHadOrder = 36376
enErr_ShelfGoodOrderNotExist = 36372
enErr_ShelfGoodOrderTransacted = 36374
enErr_ShelfGoodOrderTransacting = 36375
enErr_ShelfGoodsItemErr = 36370
enErr_ShelfGoodsItemNotEnough = 36371
enErr_ShopNotEnough = 34400
enErr_Shop_BuyFail = 505
enErr_Shop_BuyLimited = 509
enErr_Shop_BuyZero = 506
enErr_Shop_MaxLimit = 503
enErr_Shop_NeedlessRefreshGoods = 504
enErr_Shop_NoGoodItem = 502
enErr_Shop_NoRefreshCount = 500
enErr_Shop_NoRefreshGoods = 501
enErr_Shop_RefreshFail = 507
enErr_SiegeTreasureDailyTimesLimit = 36601
enErr_SigilPacket_Full = 2283
enErr_Sigil_Have_None = 3982
enErr_Sigil_Hero_StarLv_Err = 3985
enErr_Sigil_Max = 3991
enErr_Sigil_Not_Sigil = 3983
enErr_Sigil_Position_Err = 3984
enErr_Sigil_Replace_Fail = 3981
enErr_Sigil_Takeoff_Same = 3987
enErr_Sigil_UpgradeLvFail = 3990
enErr_Sigil_Wear_Same = 3986
enErr_Skill_Born_Skill = 234
enErr_Skill_Have_Same_Skill = 237
enErr_Skill_Points_NotEnough = 230
enErr_Skill_Pos_Have_Skill = 235
enErr_Skill_Pos_Not_Skill = 236
enErr_Skill_Same_Skill = 233
enErr_Skill_Slot_Lock = 232
enErr_Skill_UseNum_NotEnough = 231
enErr_SlaveRecommend_Empty = 5049
enErr_Slave_AddEnergyFail = 5040
enErr_Slave_AddItemFail = 5039
enErr_Slave_DataProcError = 5042
enErr_Slave_OverPerchaseEnergy = 5041
enErr_SoulLinkModule_AddSlotFail = 2171
enErr_SoulLinkModule_AddSlotLinkFail = 2174
enErr_SoulLinkModule_DestoryFailInSlot = 2195
enErr_SoulLinkModule_FlamenSetSlot = 2177
enErr_SoulLinkModule_LessDiamond = 2183
enErr_SoulLinkModule_LessSoul = 2186
enErr_SoulLinkModule_LvFailPalInSlot = 2188
enErr_SoulLinkModule_Max = 2200
enErr_SoulLinkModule_ModuleClose = 2172
enErr_SoulLinkModule_NoInCool = 2184
enErr_SoulLinkModule_OpFail = 2185
enErr_SoulLinkModule_PalInSlot = 2178
enErr_SoulLinkModule_PalNotInSlot = 2181
enErr_SoulLinkModule_ResetFailInSlot = 2189
enErr_SoulLinkModule_SetInPalFail = 2176
enErr_SoulLinkModule_SlotCool = 2179
enErr_SoulLinkModule_SlotError = 2175
enErr_SoulLinkModule_SlotExceedTheLimit = 2173
enErr_SoulLinkModule_SlotHasPal = 2180
enErr_SoulLinkModule_SlotNumError = 2182
enErr_SoulLinkModule_SoulLowLevel = 2192
enErr_SoulLinkModule_SoulMaxLv = 2190
enErr_SoulLinkModule_SoulMaxUnlocked = 2193
enErr_SoulLinkModule_SoulProgressFull = 2194
enErr_SoulLinkModule_SoulProgressLess = 2191
enErr_SoulLinkModule_StepFailPalInSlot = 2187
enErr_SpaceDominator_Have_Reward = 2404
enErr_SpaceDominator_Max = 2500
enErr_SpaceDominator_Not_End = 2402
enErr_SpaceDominator_Not_Open = 2401
enErr_SpaceDominator_Not_Reward = 2403
enErr_SpaceDominator_reopening = 2405
enErr_SpaceExplore_ActivationPlanetFail = 3934
enErr_SpaceExplore_AkeyExploreFail = 3959
enErr_SpaceExplore_BattleshipTechIDLvErr = 3926
enErr_SpaceExplore_BattleshipTechUpLvErr = 3927
enErr_SpaceExplore_BattleshipTechUpOldLvErr = 3928
enErr_SpaceExplore_BattleshipUpgradeFail = 3930
enErr_SpaceExplore_BuyPrivilegeCardFail = 3957
enErr_SpaceExplore_BuyPrivilegeCardNotEnough = 3955
enErr_SpaceExplore_DiffPeriod = 3953
enErr_SpaceExplore_DispatchHeroNoExist = 3939
enErr_SpaceExplore_DispatchHeroStarLvlow = 3940
enErr_SpaceExplore_DispatchHeroTooMore = 3943
enErr_SpaceExplore_EventError = 3948
enErr_SpaceExplore_EventIDNoExist = 3947
enErr_SpaceExplore_ExploreTooLittle = 3949
enErr_SpaceExplore_GalaxyHasActivation = 3950
enErr_SpaceExplore_GalaxyNoActivation = 3925
enErr_SpaceExplore_GalaxyNoExplore = 3932
enErr_SpaceExplore_GalaxyUnlockConsume = 3951
enErr_SpaceExplore_HasBuyPrivilegeCard = 3956
enErr_SpaceExplore_LinkPlanetNoAty = 3933
enErr_SpaceExplore_MAX = 3980
enErr_SpaceExplore_ModuleClose = 3954
enErr_SpaceExplore_NoDispatchPlanet = 3941
enErr_SpaceExplore_NoGalaxyID = 3922
enErr_SpaceExplore_NoGalaxyMapCfg = 3935
enErr_SpaceExplore_NoGalaxyMapID = 3923
enErr_SpaceExplore_NoHavePrivilegeCard = 3958
enErr_SpaceExplore_NoPlanetID = 3924
enErr_SpaceExplore_NotResoursePlanet = 3938
enErr_SpaceExplore_NumMaxFail = 3960
enErr_SpaceExplore_OngoingExploreGalaxy = 3921
enErr_SpaceExplore_PlanetActivation = 3952
enErr_SpaceExplore_PlanetDispatchRepeated = 3945
enErr_SpaceExplore_PlanetHavePirates = 3946
enErr_SpaceExplore_PlanetNoActavation = 3944
enErr_SpaceExplore_PlanetNoExist = 3931
enErr_SpaceExplore_PlanetNoInGalaxyMay = 3936
enErr_SpaceExplore_PreciousGetFail = 3937
enErr_SpaceExplore_RepeatedHeroSid = 3942
enErr_SpaceExplore_UpgradeLackOfGoods = 3929
enErr_SpaceGap_BattleFail = 3562
enErr_SpaceGap_Max = 3570
enErr_SpaceGap_NotPassLast = 3561
enErr_SpaceGap_Set_DialogFlag_Fail = 3563
enErr_SpecialGift_IsLimited = 34302
enErr_SpecialGift_Max = 34305
enErr_SpecialGift_NotStart = 34301
enErr_SpeedUp_NotEnoughCondition = 30411
enErr_SpeedUp_NotEnoughGoods = 30410
enErr_Stamina_NotEnough = 30502
enErr_StarCraft_Caption_Over = 4262
enErr_StarCraft_Is_Caption = 4263
enErr_StarCraft_Max = 4300
enErr_StarCraft_NotSignState = 4260
enErr_StarCraft_Not_Caption = 4264
enErr_StarCraft_Signed = 4261
enErr_StrayDog_NoAward = 36541
enErr_SubTask_HavingGain = 4302
enErr_SubTask_Max = 4305
enErr_SubTask_NoSelRewardID = 4303
enErr_SubTask_NoSpace = 4304
enErr_SubTask_UnFinishAll = 4301
enErr_SubsequentReward_CfgBbnormal = 1823
enErr_SubsequentReward_Max = 1840
enErr_SubsequentReward_RewardCfgErr = 1824
enErr_SubsequentReward_RewardHasGet = 1825
enErr_SubsequentReward_StageError = 1822
enErr_SubsequentReward_TaskNonentity = 1821
enErr_TargetRewardNotExist = 4121
enErr_TargetTask_AlreadyReward = 1783
enErr_TargetTask_Close = 1781
enErr_TargetTask_NotFinished = 1782
enErr_TargetTask_NotFound = 1780
enErr_TargetTask_NotOpen = 1784
enErr_TaskMain_RechargeLimit = 31861
enErr_Tavern_AlterStarted = 604
enErr_Tavern_CreateTaskFail = 613
enErr_Tavern_HeroConditionMismatch = 606
enErr_Tavern_HeroLowStar = 609
enErr_Tavern_HeroNotEnough = 608
enErr_Tavern_HeroNotFound = 605
enErr_Tavern_HeroProfessionNotFit = 611
enErr_Tavern_HeroTypeNotFit = 610
enErr_Tavern_HeroUsed = 612
enErr_Tavern_Max = 620
enErr_Tavern_NoNeedRefresh = 603
enErr_Tavern_NotFinished = 601
enErr_Tavern_NotProcessing = 602
enErr_Tavern_RefreshFail = 614
enErr_Tavern_RewardFail = 615
enErr_Tavern_SpeedupFail = 616
enErr_Tavern_StartFail = 617
enErr_Tavern_TaskMaxLimit = 607
enErr_Tavern_TaskNotFound = 600
enErr_Tech_FrontSkillLess = 1913
enErr_Tech_MAx = 1930
enErr_Tech_NoEnoughRes = 1914
enErr_Tech_SkillCfgErr = 1912
enErr_Tech_SkillUpgradeFail = 1911
enErr_ThinksGivingActivity_CashRewardFail = 2204
enErr_ThinksGivingActivity_GetSignInRewardFail = 2203
enErr_ThinksGivingActivity_Max = 2219
enErr_ThinksGivingActivity_NoTurkey = 2202
enErr_ThinksGivingActivity_SignInDaysNotEnough = 2205
enErr_ThinksGivingActivity_SignInRewardGetted = 2206
enErr_ThinksGivingActivity_UseTurkeyErr = 2201
enErr_Today_Reward_Received = 31755
enErr_Today_Reward_Times_Limit = 31756
enErr_TopRace_AlreadyLiked = 3520
enErr_TopRace_ApplaudFrequently = 3519
enErr_TopRace_ApplaudNoArenaID = 3521
enErr_TopRace_ApplaudNoReward = 3523
enErr_TopRace_ApplaudNotTop1 = 3522
enErr_TopRace_CannotSaveTroop = 3525
enErr_TopRace_Close = 3500
enErr_TopRace_Max = 3550
enErr_TopRace_NoArena = 3501
enErr_TopRace_NoCurBattle = 3504
enErr_TopRace_NoJoin = 3505
enErr_TopRace_NoRank = 3510
enErr_TopRace_NoRoleInfo = 3502
enErr_TopRace_NoTop18Pal = 3503
enErr_TopRace_OnTopRaceList = 3516
enErr_TopRace_OutOf64 = 3507
enErr_TopRace_OutOfSetLineUpTime = 3518
enErr_TopRace_Pal_InLineup = 3524
enErr_TopRace_RankError = 3508
enErr_TopRace_RankRewardHasGet = 3509
enErr_TopRace_SettleNoResult = 3506
enErr_TopRace_VoteAfter23 = 3511
enErr_TopRace_VoteCoinLess = 3514
enErr_TopRace_VoteComplete = 3513
enErr_TopRace_VoteNoPush = 3512
enErr_TopRace_VoteOnBattle = 3517
enErr_TopRace_VotePushDayMore7 = 3515
enErr_Tower_NoEnoughSweeps = 1453
enErr_Tower_SweepFail = 1452
enErr_Tower_SweepRewardFail = 1456
enErr_Tower_SweepStageErr = 1454
enErr_Tower_Video_Nonentiry = 1451
enErr_TreasureRareShop_Awaken_Limit = 508
enErr_TreasureRareStage_CannotUse = 2285
enErr_TreasureRare_Advance_Recycling = 4391
enErr_TreasureRare_Awaken = 4342
enErr_TreasureRare_AwakenColorType_Config = 4396
enErr_TreasureRare_ComposeTreasure_Num = 4338
enErr_TreasureRare_ComposeTreasure_Repeated = 4339
enErr_TreasureRare_ComposeTreasure_System = 4337
enErr_TreasureRare_Enchance_Recycling = 4392
enErr_TreasureRare_Enhance_Conditions = 4348
enErr_TreasureRare_Enhance_Equip = 4350
enErr_TreasureRare_Enhance_Failed = 4352
enErr_TreasureRare_Enhance_Limit = 4349
enErr_TreasureRare_GM_DelTreasureFail = 4395
enErr_TreasureRare_GM_Param = 4393
enErr_TreasureRare_GM_PlayerOffLine = 4394
enErr_TreasureRare_Guild_Failed = 4341
enErr_TreasureRare_Item = 4343
enErr_TreasureRare_Maker_Failed = 4354
enErr_TreasureRare_Maker_Proccessing = 4355
enErr_TreasureRare_Not_Satisfied = 4345
enErr_TreasureRare_Purchase_Limit = 4351
enErr_TreasureRare_Purchase_Limit_ForgeReward = 4386
enErr_TreasureRare_Remove_Item = 4346
enErr_TreasureRare_Reward_Conf = 4336
enErr_TreasureRare_Stage_Failed = 4340
enErr_TreasureRare_System = 4353
enErr_TreasureRare_TreasureIDUpdate = 4347
enErr_TreasureRare_Unavaliable = 4344
enErr_TrialHero_AlreadyHasHero = 4964
enErr_TrialHero_BattleLimit = 4967
enErr_TrialHero_Close = 4963
enErr_TrialHero_DecoPacketNoSpace = 4968
enErr_TrialHero_DiamondPacketNoSpace = 4973
enErr_TrialHero_EquipPacketNoSpace = 4969
enErr_TrialHero_GetTimesLimit = 4965
enErr_TrialHero_HeroPacketNoSpace = 4970
enErr_TrialHero_Max = 4980
enErr_TrialHero_NotEnoughCond = 4962
enErr_TrialHero_RegionUnopened = 4961
enErr_TrialHero_SigilCannotOpt = 4972
enErr_TrialHero_SigilPacketNoSpace = 4971
enErr_TrialHero_TimeErr = 4966
enErr_Trial_Event_ErrorType = 835
enErr_Trial_Event_NotActive = 832
enErr_Trial_Event_NotExist = 831
enErr_Trial_Event_TooFar = 833
enErr_Trial_LevelNotPass = 804
enErr_Trial_Max = 900
enErr_Trial_MaxLevel = 805
enErr_Trial_MoveOutOfVision = 806
enErr_Trial_Move_Block = 829
enErr_Trial_Move_PathError = 834
enErr_Trial_Move_PosOccupy = 830
enErr_Trial_NoHero = 803
enErr_Trial_NoLevel = 809
enErr_Trial_NoTeam = 807
enErr_Trial_NotEnoughEnergy = 802
enErr_Trial_SetTeam_HeroMaxLimit = 820
enErr_Trial_SetTeam_HeroUsed = 821
enErr_Trial_SetTeam_Locked = 824
enErr_Trial_SetTeam_LowLevel = 825
enErr_Trial_SetTeam_LowStar = 826
enErr_Trial_SetTeam_PlaceIllegal = 823
enErr_Trial_SetTeam_PlaceUsed = 822
enErr_Trial_TeamNotFound = 808
enErr_Trial_Team_Locked = 801
enErr_Triumphant_CampNoRank = 2032
enErr_Triumphant_RankTypeErr = 2031
enErr_Turnable_CantRandomEffect = 3456
enErr_Turnable_ConfigError = 3453
enErr_Turnable_InvalidParam = 3452
enErr_Turnable_ItemNotEnough = 3457
enErr_Turnable_Max = 3460
enErr_Turnable_NotOpen = 3455
enErr_Turnable_Prized = 3454
enErr_Turnable_Sucess = 3451
enErr_Unknown = 1
enErr_Update_Gemstone_Fail = 244
enErr_Update_Gemstone_Had_Activated = 245
enErr_Update_Not_Exist = 246
enErr_UseGradeResourceBox_CentorLevelErr = 30238
enErr_UseGradeResourceBox_Fail = 30236
enErr_UseGradeResourceBox_LackOfGoods = 30237
enErr_UseItemOpenNormalBox_Fail = 30243
enErr_UseItemOpenNormalBox_LackOfGoods = 30244
enErr_UseItemResourceFillBox_Fail = 30245
enErr_UseItemResourceFillBox_LackOfGood = 30246
enErr_UseItem_SandboxNotExist = 35446
enErr_UseOtherResourceBox_Fail = 30241
enErr_UseOtherResourceBox_LackOfGoods = 30242
enErr_UseResourceSelectBox_Fail = 30239
enErr_UseResourceSelectBox_LackOfGoods = 30240
enErr_UseSelectCustomBox_Fail = 2281
enErr_UseSelectCustomBox_LackOfGoods = 2282
enErr_UseSelectCustomBox_Max = 2300
enErr_UseTrialPhysicalAgent_Fail = 2241
enErr_UseTrialPhysicalAgent_ItemLack = 2242
enErr_UseTrialPhysicalAgent_Max = 2260
enErr_VIP_PART_BOX_HavingGetted = 2864
enErr_VIP_PART_HavingExpired = 2865
enErr_VIP_PART_Lv_Reward_Fail = 2861
enErr_VIP_PART_Lv_Reward_HasGetted = 2862
enErr_VIP_PART_Lv_Reward_LvErr = 2863
enErr_VIP_PART_MAX = 2880
enErr_VIP_PART_Times_Limit = 2867
enErr_VIP_PART_Vip_NotEnough = 2866
enErr_ValentinesDay_DonateFail = 2501
enErr_ValentinesDay_Max = 2520
enErr_ValentinesDay_ShortageOfRoses = 2502
enErr_ValentinesErrorCodeMax = 3780
enErr_ValentinesParamError = 3765
enErr_Valentines_NotOpen = 3761
enErr_VipLvNotActive = 36631
enErr_VipLvNotReach = 36630
enErr_VoidArean_Lock = 3361
enErr_VoidArean_LowRank = 3364
enErr_VoidArean_Max = 3450
enErr_VoidArean_WrongID = 3363
enErr_VoidArean_WrongUpgradeLv = 3362
enErr_WeaponDiamond_Absolutely_Failed = 4556
enErr_WeaponDiamond_BatchComposit_Closed = 4553
enErr_WeaponDiamond_CompositeGold = 4544
enErr_WeaponDiamond_Composite_Overflow = 4518
enErr_WeaponDiamond_Composite_param = 4513
enErr_WeaponDiamond_ConsumeFailed = 4519
enErr_WeaponDiamond_DiamondNotExist = 4526
enErr_WeaponDiamond_DiamondOpreationFail = 4531
enErr_WeaponDiamond_DiamondSlotLocked = 4525
enErr_WeaponDiamond_DiamondSlotPos = 4529
enErr_WeaponDiamond_DiamondSocketType = 4528
enErr_WeaponDiamond_Exchange = 4546
enErr_WeaponDiamond_ExchangeDiamondSlotPos = 4558
enErr_WeaponDiamond_GemCompositeCfg = 4550
enErr_WeaponDiamond_GoodsNotExist = 4515
enErr_WeaponDiamond_GoodsType = 4516
enErr_WeaponDiamond_GuideInitBattle = 4549
enErr_WeaponDiamond_HighCompositeClose = 4547
enErr_WeaponDiamond_Lock_Failed = 4554
enErr_WeaponDiamond_Lock_Status = 4555
enErr_WeaponDiamond_Max = 4575
enErr_WeaponDiamond_Module_Closed = 4552
enErr_WeaponDiamond_MountedType = 4545
enErr_WeaponDiamond_NotFirstGuide = 4548
enErr_WeaponDiamond_PalIsMercenary = 4543
enErr_WeaponDiamond_RepalcePosNoDia = 4527
enErr_WeaponDiamond_ReplaceFailed = 4523
enErr_WeaponDiamond_StarWeaponConfig = 4530
enErr_WeaponDiamond_System = 4514
enErr_WeaponDiamond_TakeOffDiamond = 4517
enErr_WeaponDiamond_Upgrade_ItemDelete = 4537
enErr_WeaponDiamond_WeaponSkill_Locked = 4539
enErr_WeaponDiamond_WeaponSkill_NotExist = 4540
enErr_WeaponDiamond_WeaponSkill_PropUpdate = 4542
enErr_WeaponDiamond_WeaponSkill_Update = 4541
enErr_WeaponDiamond_WeaponSkill_param = 4538
enErr_WeaponDiamond_WeaponUpgrade_Config = 4534
enErr_WeaponDiamond_WeaponUpgrade_Item = 4536
enErr_WeaponDiamond_WeaponUpgrade_MaxLevel = 4535
enErr_WeaponDiamond_WeaponUpgrade_fail = 4533
enErr_WeaponDiamond_WeaponUpgrade_param = 4532
enErr_WeaponDiamond_operation_param = 4524
enErr_Weapon_Active_Fail = 1514
enErr_Weapon_Choose_Fail = 1515
enErr_Weapon_Dismantle_Fail = 1516
enErr_Weapon_No_Active = 1508
enErr_Weapon_No_Wear = 1507
enErr_Weapon_Pre_Cond_NotEnough = 1505
enErr_Weapon_Upgrade_Fail = 1517
enErr_Weapon_Was_Used = 1506
enErr_WeekendArena_Battle_Fail = 4153
enErr_WeekendArena_Challenge_Num_Empty = 4151
enErr_WeekendArena_End = 4152
enErr_WeekendArena_Like_Fail = 4154
enErr_WeekendArena_Like_Today = 4156
enErr_WeekendArena_OptFreqLimit = 4157
enErr_WeekendArena_Pal_InLineup = 4155
enErr_WeekendArena_Rank_Err = 4150
enErr_WelfareActivity_AlreadyGet = 5393
enErr_WelfareActivity_ErrTime = 5392
enErr_WelfareActivity_Max = 5400
enErr_WelfareActivity_ParamError = 5391
enErr_XYX_ActiveAgain = 4201
enErr_XYX_ActivityOpenDayBlocked = 4209
enErr_XYX_CreateRoleDayBlocked = 4206
enErr_XYX_ModuleIsNotOpen = 4208
enErr_XYX_PreBuildingBlocked = 4203
enErr_XYX_PreLevelBlocked = 4202
enErr_XYX_PreMainStageBlocked = 4204
enErr_XYX_TargetPowerNotEnough = 4207
enErr_XYX_TrainningCenterOverflow = 4205
enErr_ZombieApocalypse_ActivityNotOpen = 36460
enErr_ZombieApocalypse_ActivityNotRunning = 36472
enErr_ZombieApocalypse_CheckPosionFail = 36471
enErr_ZombieApocalypse_GetActivityDataErr = 36462
enErr_ZombieApocalypse_GetAllianceIdErr = 36461
enErr_ZombieApocalypse_GetPassBoxErr = 36468
enErr_ZombieApocalypse_HasAppointmentInfo = 36466
enErr_ZombieApocalypse_LoadPlayerInfoFail = 36465
enErr_ZombieApocalypse_NoEnoughCondition = 36464
enErr_ZombieApocalypse_NoPermissions = 36463
enErr_ZombieApocalypse_NotHasAppointmentInfo = 36467
enErr_ZombieApocalypse_NotSameAlliance = 36469
enErr_ZombieApocalypse_NotSameWorldId = 36470
enErr_ZombieComing_GoalHasDeath = 36301
enErr_ZombieComing_NotFindGold = 36300
enErr_ZoneBattleDuel_Activity_Not_Open = 35604
enErr_ZoneBattleDuel_HAVE_MATCH_GAME = 35607
enErr_ZoneBattleDuel_NOT_WINNING_SIDE = 35605
enErr_ZoneBattleDuel_NotEnd = 35601
enErr_ZoneBattleDuel_NotEnoughScore = 35600
enErr_ZoneBattleDuel_Not_Enough_Integral = 35606
enErr_ZoneBattleDuel_Not_Top_Scorer = 35603
enErr_ZoneBattleDuel_Rewarded = 35602
enErr_allianceTrain_AllianceProtected = 35812
enErr_allianceTrain_AttackTooFast = 35811
enErr_allianceTrain_GetBattleHistoryErr = 35818
enErr_allianceTrain_GetTeam_Failed = 35809
enErr_allianceTrain_GetTrainRewardErr = 35815
enErr_allianceTrain_NoAllianceLineUp = 35810
enErr_allianceTrain_NotOpen = 35817
enErr_allianceTrain_SaveTeam_Failed = 35808
enErr_allianceTrain_SetLootTimesErr = 35816
enErr_leagueTech_CancelRecommendErr = 30104
enErr_leagueTech_ContributeGoodsErr = 30115
enErr_leagueTech_EnoughExperience = 30109
enErr_leagueTech_ErrEffectId = 30101
enErr_leagueTech_MaxLevel = 30106
enErr_leagueTech_NoUnLock = 30102
enErr_leagueTech_NotEnoughCoinsContributeCnt = 30110
enErr_leagueTech_NotEnoughExperience = 30108
enErr_leagueTech_NotInleague = 30105
enErr_leagueTech_NotRecommendPermission = 30100
enErr_leagueTech_NotStudyPermission = 30114
enErr_leagueTech_Upgrade = 30107
enErr_leagueTreasure_BossHaveDied = 1203
enErr_weaponDiamond_GemCompositeUpgrade = 4551

