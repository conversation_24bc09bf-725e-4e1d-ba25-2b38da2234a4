-- local print = print
-- local require = require

-- local Application = CS.UnityEngine.Application
-- local RuntimePlatform = CS.UnityEngine.RuntimePlatform

-- module("casualgame_download_mgr")

-- local http_inst = require "http_inst"
-- local dkjson = require "dkjson"	
-- local util = require "util"
-- local ui_login_progress = require "ui_login_progress"
-- local log = require "log"
-- local cs_coroutine = require "cs_coroutine"
-- local casualgame_progress = require "casualgame_progress"
-- local casualgame_global = require "casualgame_global"

-- local downhanderDic = {}
-- local inMiniGameTimer = nil
-- -- 游戏启动检查小游戏的更新
-- local isEnter = false
-- function PreloadDowmMiniGame(cb,reskey)
--     if isEnter == true then
--         log.Warning("enter again")
--         return
--     end
--     isEnter = true
--     local log = require "log"
--     log.Warning("PreloadDowmMiniGame")
--     casualgame_progress.InitUI()
--     local files_version_mgr = require "files_version_mgr"
--     if not reskey then
--         reskey = files_version_mgr.GetStandAloneTinyRes()
--     end
--     if Application.platform == RuntimePlatform.WebGLPlayer or util.IsEnableHCLR() then
--         StartDownloadMiniGame(reskey,nil,nil,cb,function (downloadSize,totalSize,downCount,totalCount)
--             ShowDownloadProgress(downloadSize, totalSize, downCount, totalCount)
--         end,0)
--     else
--         StartDownloadMiniGame(reskey,nil,nil,cb,function (downloadSize,totalSize,downCount,totalCount)
--             ShowDownloadProgress(downloadSize, totalSize, downCount, totalCount)
--         end)
--     end
-- end
-- --下载小游戏
-- function StartDownloadMiniGame(reskey,okCallBack,failCallBack,miniGameEndCb,onProgress,levelid)
--     local downhander = GetOrCreateDownHander(reskey)
--     downhander:StartDownloadMiniGame(okCallBack,failCallBack,miniGameEndCb,onProgress,levelid)
-- end
-- --检测小游戏完整性
-- function CheckMiniGameComplete(reskey,callBack)
--     local downhander = GetOrCreateDownHander(reskey)
--     downhander:CheckMiniGameComplete(callBack)
-- end
-- --显示小游戏进度条
-- function ShowDownloadProgress(downloadSize,totalSize,downCount,totalCount)
--     print("downloadSize,",downloadSize,"totalSize",totalSize,"downCount",downCount,"totalCount",totalCount)
--     casualgame_progress.ShowProgress(downloadSize, totalSize, downCount, totalCount)
-- end
-- --获取小游戏进度数据 downloadSize, totalSize, downCount, totalCount 分别为 当前下载大小,总下载大小,当前下载个数，总下载个数
-- function GetDownLoadProgress(reskey)
--     if downhanderDic[reskey] == nil then
--         log.Warning("downhanderDic[reskey] nil")
--         return nil
--     else
--         return downhanderDic[reskey]:GetProgress()
--     end
-- end
-- -- 加载小游戏lua资源并require 小游戏enter脚本
-- function InitGameLua(reskey,callback)
--     if downhanderDic[reskey] == nil then
--         log.Warning("downhanderDic[reskey] nil")
--         return nil
--     else
--         return downhanderDic[reskey]:InitGameLua(callback)
--     end
-- end
-- -- 加载小游戏lua资源并require 小游戏enter脚本
-- function InitGameLua_Ip(reskey,callback)
--     if downhanderDic[reskey] == nil then
--         log.Warning("downhanderDic[reskey] nil")
--         return nil
--     else
--         return downhanderDic[reskey]:InitGameLua_Ip(callback)
--     end
-- end
-- --同步加载
-- function LoadAllAssetsFromFile(reskey,rAssetbundleName, callback)
--     local downhander = GetOrCreateDownHander(reskey) 
--     downhander:LoadAllAssetsFromFile(rAssetbundleName, callback)
-- end
-- --异步加载
-- function LoadAllAssets(reskey,rAssetbundleName, callback)
--     local downhander = GetOrCreateDownHander(reskey) 
--     downhander:LoadAllAssets(rAssetbundleName, callback)
-- end
-- --卸载资源
-- function UnloadAsset(reskey,rAssetbundleName)
--     if downhanderDic[reskey] == nil then
--         log.Warning("downhanderDic[reskey] nil")
--     else
--         return downhanderDic[reskey]:UnloadAsset(rAssetbundleName)
--     end
-- end
-- function GetEntityPath(reskey,rAssetbundleName)
--     if downhanderDic[reskey] == nil then
--         log.Warning("downhanderDic[reskey] nil")
--     else
--         return downhanderDic[reskey]:GetEntityPath(rAssetbundleName)
--     end
-- end
-- function CheckStreamAssetContainResKey(reskey)
--     local path =  casualgame_global.GetStreamAssetPath()..reskey.."/file.txt"
--     local www =  casualgame_global.DownLoadStreamAssetsRes(path,0.5)
--     if  www then
--         return true
--     else
--         return false
--     end
-- end
-- function ReportDownloadStateEvent(reskey)
--     if downhanderDic[reskey] == nil then
--         log.Warning("downhanderDic[reskey] nil")
--     else
--         return downhanderDic[reskey]:ReportDownloadStateEvent()
--     end
-- end
-- --获取或者创建下载实例
-- function GetOrCreateDownHander(reskey,callback)
--     local downhander = nil
--     if downhanderDic[reskey] == nil then
--         local casualgame_download_read_mgr = require "casualgame_download_read_mgr"
--         downhander = casualgame_download_read_mgr.New(reskey,callback)
--         downhanderDic[reskey] = downhander
--         downhander:Initialize()
--     else
--         downhander = downhanderDic[reskey]
--     end
--     return downhander
-- end

