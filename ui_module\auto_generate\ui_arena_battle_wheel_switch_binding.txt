local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local RectTransform = CS.UnityEngine.RectTransform


module("ui_arena_battle_wheel_switch_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenabattlewheelswitch.prefab"

WidgetTable ={
	btn_LeftBtn = { path = "btn_LeftBtn", type = Button, event_name = "OnBtnLeftBtnClickedProxy"},
	rtf_teaminfoLeft = { path = "main/rtf_teaminfoLeft", type = RectTransform, },
	btn_switch1 = { path = "main/btn_switch1", type = Button, event_name = "OnBtnSwitch1ClickedProxy"},
	btn_switch2 = { path = "main/btn_switch2", type = Button, event_name = "OnBtnSwitch2ClickedProxy"},

}
