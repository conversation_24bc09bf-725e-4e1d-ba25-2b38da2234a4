local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Slider = CS.UnityEngine.UI.Slider
local InputField = CS.UnityEngine.UI.InputField
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local RectTransform = CS.UnityEngine.RectTransform


module("ui_goods_submit_binding")

UIPath = "ui/prefabs/gw/activity/halloweenactivity/wingding/uigoodssubmit.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	txt_title = { path = "Panel/Bg/txt_title", type = Text, },
	sld_numSlider = { path = "Panel/sld_numSlider", type = Slider, value_changed_event = "OnSliderNumSliderValueChange"},
	btn_minus = { path = "Panel/sld_numSlider/btn_minus", type = Button, event_name = "OnBtnMinusClickedProxy"},
	btn_add = { path = "Panel/sld_numSlider/btn_add", type = Button, event_name = "OnBtnAddClickedProxy"},
	inp_numInput = { path = "Panel/sld_numSlider/inp_numInput", type = InputField, value_changed_event = "OnInputNumInputValueChange" , end_edit_event = "OnInputNumInputEndEdit"},
	txt_curNum = { path = "Panel/txt_curNum", type = Text, },
	btn_ok = { path = "Panel/btn&ss_ok", type = Button, event_name = "OnBtnOkClickedProxy"},
	ss_ok = { path = "Panel/btn&ss_ok", type = SpriteSwitcher, },
	txt_okText = { path = "Panel/btn&ss_ok/txt_okText", type = Text, },
	txt_tips = { path = "Panel/txt_tips", type = Text, },
	rtf_submitGoodsParent = { path = "Panel/rtf_submitGoodsParent", type = RectTransform, },
	rtf_rewardParent = { path = "Panel/rewardList/Viewport/rtf_rewardParent", type = RectTransform, },

}
