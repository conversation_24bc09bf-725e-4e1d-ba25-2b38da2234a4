local require = require
local pairs = pairs     
local table = table
local newClass = newclass
local type = type
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"

--region Controller Life
module("ui_common_damage_rank_reward_controller")
local controller = nil
local UIController = newClass("ui_common_damage_rank_reward_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    if  data then
        self.CData.selfRankId = data.selfRankId
        self.CData.rewardtype = data.rewardtype
        self.CData.subtype = data.subtype
    else        
        self.CData.selfRankId = -1
    end
    self.CData.rankingRewards = self:GetRankingRewards(self.CData.rewardtype,self.CData.subtype)
    self:TriggerUIEvent("UpdateScrollList",self.CData.rankingRewards)
end

function UIController:GetRankingRewards(type, subtype)
    local game_scheme = require "game_scheme"
    local num = game_scheme:RankingRewards_nums()
    local rankingRewards = {}

    for i = 0, num - 1 do
        local cfg = game_scheme:RankingRewards(i)
        if cfg and cfg.rewardtype == type then
            if not subtype or cfg.subtype == subtype then
                if cfg.conditionalparameters[1] then
                    local rankRange1 = cfg.conditionalparameters[1][0]
                    local rankRange2 = cfg.conditionalparameters[1][1] or rankRange1
                    table.insert(rankingRewards, {
                        id = cfg.RewardID,
                        rankRange1 = rankRange1,
                        rankRange2 = rankRange2,
                        rewardId = cfg.Reward,
                        titleLangId = cfg.Title
                    })
                end
            end
        end
    end

    return rankingRewards
end

function UIController:OnShow()
    self.__base.OnShow(self)
   
end

function UIController:Close()   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil

    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents() 
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end
function  UIController:OnBtnPop_btn_returnClickedProxy()
    ui_window_mgr:UnloadModule(self.view_name)
end

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
