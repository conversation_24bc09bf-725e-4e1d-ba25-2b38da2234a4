local require = require
local pairs = pairs
local ipairs = ipairs
local print = print
local table = table
local dump = dump
local tonumber = tonumber
local math = math
local msg_pb = require "msg_pb"
local challenge_pb = require "challenge_pb"
local net = require "net"
local common_new_pb = require "common_new_pb"
local event = require "event"
local util = require "util"
local game_config = require "game_config"

local log = require "log"

local Debug = CS.UnityEngine.Debug

module("battle_message")

local tbsPool = {}
local multiTbs = false
local stageType = common_new_pb.GameGoal
local isShowBattleResult = true--跳过战斗时是否需要显示结算面板
local multiTbsPool = {} -- 存放多场战斗数据，播完一场不自动清除
local lastStageTypeVlaueOfMultiTb = {} --存储是请求的战斗类型否为多队战斗（每种战斗类型只保存最后一次请求的信息）
-- function SendBattleChallengeMessage(stageType, stageLevel, heroData, firendId, arenaInfo, trialInfo, skipBattle,brokenTimeInfo)
---下面的方法为请求战斗发送协议
function SendBattleChallengeMessage(data)
    local msg = challenge_pb.TMSG_PLAYER_ENTER_BATTLE_REQ()
	local battle_data = require "battle_data"
	msg.stageType = data.stageType
	if data.stageLevel then
		msg.stageLv = data.stageLevel
	end    

    if data.firendId then
        msg.isFriendHelp = true
        msg.friendId = data.firendId
    else
        msg.isFriendHelp = false
        msg.friendId = 0
    end
	if data.voidData then
		msg.VoidArenaAddLv = data.upLevel
		msg.VoidArenaID = data.voidData.cfg.VoidID
	end
	if data.heroData then
		local t = {}
		 for pos, hero in pairs(data.heroData) do
			table.insert(t,{pos=pos,hero=hero})
		end
		table.sort(t, function(v1, v2)
			return v1.pos < v2.pos
		end)
		
		for index, data in ipairs(t) do
			if data.pos >= 0 and data.hero.heroSid > 0 then--这里是为了防止一些偶然错误导致拖拽英雄位置为-100也存储了进来
				local dataInfo = msg.pals:add()
				dataInfo.palId = data.hero.heroSid
				
				local row,col = battle_data.GetRowAndColByIndex(data.pos + 1)
				dataInfo.row = row
				dataInfo.col = col
				--if data.pos < 3 then
				--	dataInfo.row = 0
				--	dataInfo.col = data.pos
				--else
				--	dataInfo.row = 1
				--	dataInfo.col = data.pos-3
				--end
			end
		end
    end

	if data.multiHeroData then
		for i,v in ipairs(data.multiHeroData) do
			if v then
				local dataInfo = msg.palsLineUp:add()
				dataInfo.weaponId = v.weaponId or 0
				local t = {}
				if v.hero then
					for pos, hero in pairs(v.hero) do
						table.insert(t,{pos=pos,hero=hero})
					end
				end
				table.sort(t, function(v1, v2)
					return v1.pos < v2.pos
				end)
	
				for j,k in ipairs(t) do
					if k.pos >= 0 then--这里是为了防止一些偶然错误导致拖拽英雄位置为-100也存储了进来
						local heroList = dataInfo.palList:add()
						heroList.palId = k.hero.heroSid
						local row,col = battle_data.GetRowAndColByIndex(k.pos + 1)
						heroList.row = row
						heroList.col = col
						--if k.pos < 3 then
						--	heroList.row = 0
						--	heroList.col = k.pos
						--else
						--	heroList.row = 1
						--	heroList.col = k.pos - 3
						--end
					end
				end
			end
		end
	end

    if data.arenaInfo then
        msg.arenaEnterBattle.arenaType = data.arenaInfo.arenaType
        msg.arenaEnterBattle.rivalID = data.arenaInfo.rivalID
        --if data.arenaInfo.weaponId then
        --    msg.weaponId = data.arenaInfo.weaponId
        --end
        
        if data.arenaInfo.lineupList then
            for i, v in ipairs(data.arenaInfo.lineupList) do
                local lineup = msg.arenaEnterBattle.lineupList:add()
				if data.arenaInfo.weaponIds and data.arenaInfo.weaponIds[i] then
					lineup.weaponId = data.arenaInfo.weaponIds[i]
				end
				
                for _i, _v in ipairs(v) do
                    local pal = lineup.palList:add()
                    pal.palId = _v.palId
                    pal.row = _v.row
                    pal.col = _v.col
                end
            end
        end
    end

	--multiTbs = GetIsMultiTbs()
	-- multiTbs = (data.arenaInfo and data.arenaInfo.arenaType ~= common_new_pb.CrystalCrown) or 
	-- (msg.stageType == common_new_pb.Maze and data.multiHeroData and #data.multiHeroData>1) or
	-- 		(msg.stageType == common_new_pb.GameGoal and data.multiHeroData and #data.multiHeroData>1) or
	-- 		(msg.stageType == common_new_pb.EquipEctypeZhuanShu and data.multiHeroData and #data.multiHeroData>1)
	
	if msg.stageType == common_new_pb.Maze or
	   msg.stageType == common_new_pb.GameGoal or 
	   msg.stageType == common_new_pb.EquipEctypeZhuanShu or
	   --msg.stageType == common_new_pb.WeekendArena or
	   msg.stageType == common_new_pb.Arena then
		if msg.stageType == common_new_pb.Arena then 
			multiTbs = data.arenaInfo and data.arenaInfo.arenaType ~= common_new_pb.CrystalCrown
		--elseif msg.stageType == common_new_pb.WeekendArena then
		--	-- 时空擂台 多战报判断
		--	multiTbs = true
		else
			multiTbs = data.multiHeroData and #data.multiHeroData > 1
		end
		lastStageTypeVlaueOfMultiTb[msg.stageType] = multiTbs
	else
		multiTbs = false
	end

	ClearMultiTbs()
	
	if data.trialInfo then
		msg.trialEnterBattle.iTeamId = data.trialInfo.iTeamId
		msg.trialEnterBattle.iEventSid = data.trialInfo.iEventSid
    end
	if data.peakInfo then
		msg.peakBattle.sid = data.peakInfo.iEventSid
    end

    if data.mazeInfo then
        msg.mazeBattle.pos = data.mazeInfo.pos
    end
    
    if data.brokenTimeInfo then
        msg.brokenSpaceTimeBattle.enType = data.brokenTimeInfo.enType              --副本类型
        msg.brokenSpaceTimeBattle.enBattleType = data.brokenTimeInfo.enBattleType  --战斗类型
        msg.brokenSpaceTimeBattle.mopUpTimes = data.brokenTimeInfo.mopUpTimes        --扫荡次数
    end

    --羁绊怪物
    if data.mateBattleRoleId then
        msg.mateBattle.roleID=data.mateBattleRoleId
    end

    if data.stageType == common_new_pb.Ashdungeon then
        msg.ashdungeonBattle.mapType = 	  data.mapType
        msg.ashdungeonBattle.stageLevel = data.stageLevel
        msg.ashdungeonBattle.skipBattle = data.skipBattle
    end
	
	if data.bossInfo then
		msg.leagueBossBattle.sid = data.bossInfo.sid
	end
	
	if data.competeBattle then
		msg.competeBattle.dwRivalRoleId = data.competeBattle.dwRivalRoleId
    end
    
    if data.friendBattle then
        msg.friendBattle.worldid = data.friendBattle.worldid
        msg.friendBattle.dbid = data.friendBattle.dbid
	end

	if data.slaveBattle then
		msg.slaveBattle.bossid = data.slaveBattle.bossid
		msg.slaveBattle.slaveid = data.slaveBattle.slaveid
		msg.slaveBattle.type = data.slaveBattle.type
		if data.slaveBattle.conquerid then
			msg.slaveBattle.conquerid = data.slaveBattle.conquerid
		end
	end
	  
    if data.EquipEctypeInfo then
		msg.equipEctype.ectypeID = data.EquipEctypeInfo.ectypeID
		msg.equipEctype.bBigPrize = data.EquipEctypeInfo.bBigPrize
    end

	if data.SpaceExplorePosition then
		msg.SpaceExplorePosition = data.SpaceExplorePosition
	end

    -- 跳过战斗
    if data.skipBattle then
        msg.isSkipBattle = data.skipBattle
    end

	if data.ActivityID then
		msg.ActivityID = data.ActivityID
	end

	msg.bCrushBlow = data.grind or false
	
	local isSkipBattle = false
	if (data.skipBattle ~= nil) then
		isSkipBattle = data.skipBattle
	else
		local battle_data = require "battle_data"
		isSkipBattle = battle_data.skipBattle
	end
    --print("跳过战斗",data.skipBattle,battle_data.skipBattle,GetBattleResult())
	if (not isSkipBattle) and (not data.grind) then
		local ui_window_mgr = require "ui_window_mgr"
		local ui_loading = require "ui_loading"
		ui_loading.SetBattleStageType(msg.stageType)
		local loadingWindow = ui_window_mgr:ShowModule("ui_loading")
		loadingWindow:SetLoadingProgress(0)
    end
    local zone = (common_new_pb.Arena==data.stageType or common_new_pb.Friend==data.stageType) and net.Endpoint_AccountLogin or net.Endpoint_Zone
    net.SendMessage(net.Endpoint_Client, zone, 0, msg_pb.MSG_PLAYER_ENTER_BATTLE_REQ, msg)
end

function SetBattleResult(isShow)
	isShowBattleResult = isShow
	------print("SetBattleResult",isShowBattleResult)
end

function GetBattleResult()
	------print("GetBattleResult",isShowBattleResult)
	return isShowBattleResult
end

function ChangeState()
    local battle_data = require "battle_data"
	local ui_window_mgr = require "ui_window_mgr"
	local isResultUI = ui_window_mgr:IsModuleShown("ui_battle_defeat") or ui_window_mgr:IsModuleShown("ui_battle_victory_new")
	--print("isResultUI",isResultUI,"1==",ui_window_mgr:IsModuleShown("ui_battle_defeat"),"2==",ui_window_mgr:IsModuleShown("ui_battle_victory"))
	SetBattleResult(true)
	if battle_data.skipBattle and not isResultUI then
		------print("打开新面板")
		SetBattleResult(nil)
	end
end
--event.Register(event.SHOW_RESULT_MODULE, ChangeState)

function SendBattleEndNTF(iype)
	local msg = challenge_pb.TMSG_BATTLE_END_OF_THE_PLAY_NTF()
	msg.stageType = iype or stageType
	net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_BATTLE_END_OF_THE_PLAY_NTF, msg)
end

function MultiTbsNext()
    local count = #tbsPool
    if count > 0 then
        local msg = tbsPool[1]
        table.remove(tbsPool, 1)

        local util = require "util"
        -- 延迟时间设置为 > 0, 0不保证为下一帧执行，会导致 Timeline 未完成清理前发起下一场战斗
        util.DelayCall(0.01, function()
            local battle_manager = require "battle_manager"
            battle_manager.OnBattleReportReceived(msg.stageType, msg.battleReport, true, nil, nil, nil, msg.battleID)
        end)
    end

    -- 等于0表示是最后一场
    return count
end

function HasNextTbs()
    return #tbsPool > 0
end

function GetTbsCount()
	return #tbsPool
end

--通过计算方式获取当前进行战斗队数
function GetCurBattleIndexByBattle()
	return #multiTbsPool - #tbsPool
end

function SetIsMultiTbs(value, battleType)
	multiTbs = value
	if battleType then 
		lastStageTypeVlaueOfMultiTb[battleType] = multiTbs
	end
	--print("设置多队伍信息:", battleType, " value:", value)
end

function ClearMultiTbs()
    tbsPool = {}
	multiTbsPool = {}
end

function GetMultiTbs()
	return multiTbsPool
end

function SetMultiTbs(tbsPool)
	multiTbsPool = tbsPool or {}
end

function SetTbsPool(data)
	tbsPool = data or {}
end

function S2CBattleOver(msg)
	local battle_manager = require "battle_manager"
	local battle_series_manager = require "battle_series_manager"
	--获取当前战斗类型最后一次请求的战斗是否为多队
	multiTbs = lastStageTypeVlaueOfMultiTb[msg.stageType]
	if not msg:HasField("ntfStep") or msg.ntfStep == 1 then
		if game_config.ENABLE_Q1_DEBUG_MODE then
			if msg:HasField("battleTrName") and msg:HasField("battleTimeStr") then
				util.SendToServer(msg.battleTrName, msg.battleTimeStr)	
			end
		else
			if msg:HasField("battleTrName1") and msg:HasField("battleTimeStr1") then
				util.SendToServer(msg.battleTrName1, msg.battleTimeStr1)	
			end
		end
	
		-- 2020-01-08战斗结算中battleReport和battleID改成repeated，支持多战斗数据
		--Debug.LogError("S2CBattleOver")
		for i, report in ipairs(msg.battleReport) do
			battle_manager.SaveBattleRecord(report)
		end
	end
	if #msg.battleID == 1 then
		battle_manager.SetBattleRecordData(msg.battleID[1], msg.battlePartyName)
	end
	if msg.stageType == common_new_pb.Maze or msg.stageType == common_new_pb.GameGoal or msg.stageType == common_new_pb.EquipEctypeZhuanShu then
		local ui_maze_multi_battle_result = require("ui_maze_multi_battle_result")
		for i,v in ipairs(msg.battleID) do
			ui_maze_multi_battle_result.SetBattleRecordData(v,msg.battlePartyName)
		end
	end

	local battle_data = require("battle_data")
	battle_data.stageType = msg.stageType
    battle_manager.SetIsReplay(false)
	--消息携带的奖励和积分信息
	local rewards, scores = GetRewardsAndScoreByMsg(msg)
	--log.Error("BattleOver rewards:", dump(rewards))
	

	if msg:HasField("WeekendArenaWinnerId") then
		local winnerID = msg.WeekendArenaWinnerId
		local weekend_arena_mgr = require "weekend_arena_mgr"
		weekend_arena_mgr.SetWinnerID(winnerID)
	end
	if not msg:HasField("ntfStep") or msg.ntfStep == 1 then
		battle_data.OnGameOverReport(msg)
		if msg.stageType == common_new_pb.Maze then
			--log.Error("!!!!!!!!!!!!!!!!!!获取战报！！！！stageType:", msg.stageType, "index", 0, "battle_data.skipBattle:", battle_data.skipBattle)
		elseif msg.stageType == common_new_pb.Legend then
			--print("Legend=======传奇锦标赛为避免多队弱网累计战斗问题，收到战报清除前一个多队战报数据")
			local ui_legend_championships_battle_result = require "ui_legend_championships_battle_result"
			ui_legend_championships_battle_result.ResetBattleData()
			ClearMultiTbs()
		end
		local cutOutTbsDatas = {} --切出战斗缓存的多队信息
		--进入战斗前设置消息的奖励和积分信息
		battle_data.rewards = rewards
		battle_data.scores = scores
		for i, report in ipairs(msg.battleReport) do
			stageType = msg.stageType
			local dataMsg = {stageType = msg.stageType, battleReport = report, battleID = msg.battleID[i], battlePartyName = msg.battlePartyName}
			--当前战斗为多队战斗时才添加到多队数据中
			if multiTbs then
				table.insert(multiTbsPool, dataMsg)
				table.insert(cutOutTbsDatas, dataMsg)
			end
			if i == 1 then
				--切出多战斗缓存战报
				local battle_switch_manager = require "battle_switch_manager"
				if not battle_series_manager.IsSeriesBattle(msg.stageType) and battle_switch_manager.IsSwitchable(stageType) then
					battle_data.battleReports[stageType] = {battleBits = report, battleID = msg.battleID[i], rewards = rewards, scores = scores}
				end

				if battle_data.skipBattle
						and (msg.stageType == common_new_pb.Maze or msg.stageType == common_new_pb.Legend or msg.stageType == common_new_pb.EquipEctypeZhuanShu) then
					util.DelayCall(0.1,function()
						battle_manager.OnBattleReportReceived(msg.stageType, report, multiTbs, nil, nil, nil, msg.battleID[i])
					end)
				elseif battle_series_manager.IsSeriesBattle(msg.stageType) then
					battle_series_manager.SetSeriesBattleReport(msg.stageType, msg.battleID[i], report, rewards, scores)
				else
					battle_manager.OnBattleReportReceived(msg.stageType, report, multiTbs, nil, nil, nil, msg.battleID[i])
				end
			else
				table.insert(tbsPool, dataMsg)
			end
		end
		--设置切出信息的多队战斗数据
		--战斗默认战报id使用最后一场战斗id
		--print("<color=#ffffff>设置切出信息的多队战斗数据!!!!</color>", msg.stageType, multiTbs, #cutOutTbsDatas)
		-- dump(battle_data.battleReports)
		if multiTbs then
			local reportData = battle_data.battleReports[msg.stageType] or {}
			if reportData then
				reportData.multiTbsDatas = cutOutTbsDatas
				reportData.isMultiTbs = multiTbs
			end
			battle_data.battleReports[msg.stageType] = reportData
		end
	end
	
	------print("battle_data.scores",battle_data.scores,#msg.rewardArr.RewardID)
	

	if msg:HasField("ntfStep") and msg.ntfStep == 2 then
		--如果是连续战斗，且不需要显示结算界面，则退出
		if battle_series_manager.IsSeriesBattle(msg.stageType) and not battle_series_manager.IsShowBattleResult() then
			return
		end
		local scene_manager = require "scene_manager"
		local ui_window_mgr = require "ui_window_mgr"
		local scene_manager_instance = scene_manager.instance()
		local skip = scene_manager_instance:IsCurrentScene("scene_loading") or ui_window_mgr:IsModuleShown("ui_in_battle") -- scene_manager_instance:IsCurrentScene("scene_battle")
		--and msg.stageType == battle_manager.GetLastStageType()
		--在战斗逻辑场景，先跳过不显示结算
		
		local reportCount = #msg.battleID --多队使用最后一场战报最为结算数据
		--print("战报结果下发！！！！！！！", skip, msg.stageType, scene_manager_instance:IsCurrentScene("scene_battle"), "rewards", rewards, "scores", scores, "reportCount", reportCount)
		local report = msg.battleReport[reportCount] --battle_data.battleReports[msg.stageType]
		if not skip then
			--结算前，先设置消息奖励和积分
			battle_data.rewards = rewards
			battle_data.scores = scores
			if report then
				local multiple_battle_report_creator = require "multiple_battle_report_creator"
				local bits = battle_manager.ParseRealBattleReport(report)
				local tbs_pb = require "tbs_pb"
				local pbMsg = tbs_pb.TbsReports()  
				pbMsg:ParseFromString(bits)
				--重新构建battle_data的数据
				multiple_battle_report_creator.CreateNewReportBy(pbMsg, #pbMsg.reports + 1)
			end
			if reportCount == 1 then
				battle_manager.OnBattleEvent("BattleEnd", true, msg.stageType)
			else
				--多队结算剩余战斗设置为空
				SetTbsPool({})
				battle_manager.OnMultiTbsBattleEvent("BattleEnd", true, msg.stageType)
			end
			event.Trigger(event.UPDATE_STAGE_TYPE, msg.stageType)
		else
			--跳过展示结算，不需要设置消息奖励和积分，避免覆盖当前战斗信息
			if battle_manager.GetLastStageType() ~= msg.stageType then
				local battle_switch_manager = require "battle_switch_manager"
				--print("保存跳过战斗战报！！！", msg.battleID)
				battle_switch_manager.SaveBattleData(msg.stageType, report, msg.battleID, rewards, scores, msg.battlePartyName)
			end
		end
	end
	local ishaveBattleID = false
	if msg.stageType == common_new_pb.Arena then
		for i, report in ipairs(msg.battleID) do
			if not  ishaveBattleID then
				ishaveBattleID = true
			end
		end
		if not ishaveBattleID then
			---已跟服务器和策划沟通新手竞技场无战斗ID直接跳过，修改原先30m的逻辑
			SkipArenaBattleISLineNull()
		end
	end
end

--获取消息携带的奖励和积分信息
--(方法重构，将该部分逻辑拆分出来，减少原始方法复杂度)
function GetRewardsAndScoreByMsg(msg)
	local rewards = {}
	local scores = 0
	if msg.stageType == common_new_pb.GameGoal or msg.stageType == common_new_pb.EquipEctype
			or msg.stageType == common_new_pb.EquipEctypeZhuanShu or msg.stageType == common_new_pb.ChinaRed
			 then
		local reward_mgr = require "reward_mgr"
		for i,v in ipairs(msg.rewardArr.itemReward) do--物品奖励的数组
			if v.itemSid and #v.itemSid > 0 then	--数值物品没有sid
				for _i, _v in ipairs(v.itemSid) do
					local num = v.itemNum
					local game_scheme = require "game_scheme"
					if game_scheme:Equipment_0(v.itemID) then
						num = 1
					end
					local item_cfg = game_scheme:Item_0(v.itemID)
					if item_cfg ~= nil then
						local nType = item_cfg.type
						local quality = item_cfg.quality
						table.insert(rewards, {id = v.itemID, num = num, sid = _v, quality = quality, nType = nType,itemFlag = v.itemFlag})
					else
						log.Error("Item.csv not find", v.itemID)
					end
				end
			else
				local game_scheme = require "game_scheme"
				local item_cfg = game_scheme:Item_0(v.itemID)
				if item_cfg ~= nil then
					local nType = item_cfg.type
					local quality = item_cfg.quality
					table.insert(rewards, {id = v.itemID, num = v.itemNum, quality = quality, nType = nType,itemFlag = v.itemFlag})
				else
					log.Error("Item.csv not find", v.itemID)
				end
			end
		end
		table.sort(rewards, function(v1, v2)
			if v1.nType == v2.nType then
				if v1.quality == v2.quality then
					if v1.nType == 1 then
						return v1.id < v2.id
					else
						return v1.id > v2.id
					end
				else
					return v1.quality > v2.quality
				end
			else
				return v1.nType < v2.nType
			end
		end)
		for i,v in ipairs(msg.rewardArr.RewardID) do--英雄奖励的数组
			local reward = reward_mgr.GetRewardGoods(v.rewardID)
			table.insert(rewards, {heroid = reward.id, num = reward.num, rewardID = v.rewardID})
		end
		scores = msg.checkpointScores--关卡评分

	else
	
	end
	if msg.stageType == common_new_pb.EquipEctype and msg.equipEctype then
		if msg.equipEctype.bBigPrize then
			local split = {}
			local id = msg.equipEctype.ectypeID
			if id > 0 then
				local game_scheme = require "game_scheme"
				local cfg = game_scheme:Lnstance_0(id)
				if cfg then
					local arr = util.SplitString(cfg.Separate, ";")
					for k,v in ipairs(arr) do
						local data = util.SplitString(v, "#", tonumber)	
						split[data[1]] = cfg.RandomCount.data[data[2]-1]
						if split[data[1]] then
							split[data[1]] = split[data[1]] + 1
						end
					end
				end
				local append = {}
				local index = 0
				for k,v in ipairs(rewards) do
					if split[v.id] then
						v.num = math.floor(v.num/split[v.id])
						for i=1, split[v.id]-1 do
							table.insert(append, v)
							index = k+1
						end
					end
				end
				for k,v in ipairs(append) do
					table.insert(rewards, index, v)
				end
			end
		end
	end
	
	return rewards, scores
end

---跳过竞技场战斗空阵容
function SkipArenaBattleISLineNull()
	local lang = require "lang"
	local net_arena_module = require "net_arena_module"
	local windowMgr = require "ui_window_mgr"
	local message_box    = require "message_box"
	message_box.Open(lang.Get(665142), message_box.STYLE_YES, function()
		if windowMgr:IsModuleShown("ui_loading")  then
			windowMgr:UnloadModule("ui_loading")
		end
	end, 0, lang.KEY_OK)
	if windowMgr:IsModuleShown("ui_match_single_rank")  then
		local  ui_match_single_rank = require "ui_match_single_rank"
		ui_match_single_rank.SetpreBattle(false)
		net_arena_module.Send_ARENA_ENTER(common_new_pb.CrystalCrown)
		net_arena_module.Send_ARENA_GET_RANKINGLIST(common_new_pb.CrystalCrown)
	end
	if windowMgr:IsModuleShown("ui_loading")  then
		windowMgr:UnloadModule("ui_loading")
	end
end


--[[获取关卡类型]]
function GetStateType()
	return stageType
end

function SetStageType(t)
	stageType = t
end

--[[用于切出战斗，战斗结束时领奖]]
function C2SBattleResult(battleID)
	--print("用于切出战斗，战斗结束时领奖!!!!!!!!!", battleID)
	local msg = challenge_pb.TMSG_PLAYER_BATTLE_RESULT_REQ()
	msg.battleID = battleID
	net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_PLAYER_BATTLE_RESULT_REQ, msg)
end

function S2CBattleResult(msg)
	--msg.battleID //战斗id
	--msg.errCode //错误码
end

local MessageTable = 
{
	{msg_pb.MSG_GAMEOVER_NTF, S2CBattleOver, challenge_pb.TMSG_GAMEOVER_NTF},
	{msg_pb.MSG_PLAYER_BATTLE_RESULT_RSP, S2CBattleResult, challenge_pb.TMSG_PLAYER_BATTLE_RESULT_RSP}
}

local net_route = require "net_route"
net_route.RegisterMsgHandlers(MessageTable)


function OnSceneLoaded()
    
end
event.Register(event.SCENE_LOADED, OnSceneLoaded)