local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local typeof = typeof
local UIUtil = CS.Common_Util.UIUtil

local ui_util = require "ui_util"
local face_item_new = require "face_item_new"
local ui_window_mgr = require "ui_window_mgr"
local effect_item = require "effect_item"
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Common_Util = CS.Common_Util.UIUtil
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_arena_battle_wheel_binding"

local result_ticker
local vs_ticker
local pk_ticker
local win_ticker
local pos_ticker
local pkeffect_ticker

local vsPath = "art/effects/effects/effect_ui_alliance_vs/prefabs/effect_ui_alliance_vs.prefab"
local pkPath = "art/effects/effects/effect_ui_alliance_pk/prefabs/effect_ui_alliance_pk.prefab"

local atk_clips = {"UI_AllianceTrainPlan_02","UI_AllianceTrainPlan_02_1","UI_AllianceTrainPlan_02_2"}
local def_clips = {"UI_AllianceTrainPlan_03","UI_AllianceTrainPlan_03_1","UI_AllianceTrainPlan_03_2"}
local pktime = 1
local pkeffecttime = 1.4
local wintime = 2
local postime = 3

--region View Life
module("ui_arena_battle_wheel")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.team_hero_l = {}
    self.team_hero_r = {}
    self.trainTeamItem = {}
    self.pkEffect = nil
    self.vsEffect = nil
    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.curShowIndex = 0
    self.def_animator.enabled = false
    self.atk_animator.enabled = false
    self.animator.enabled = false
    self.left_win.gameObject:SetActive(false)
    self.right_win.gameObject:SetActive(false)
    if result_ticker then
        util.RemoveDelayCall(result_ticker)
        result_ticker = nil
    end
    if win_ticker then
        util.RemoveDelayCall(win_ticker)
        win_ticker = nil
    end
    if pos_ticker then
        util.RemoveDelayCall(pos_ticker)
        pos_ticker = nil
    end

    if pk_ticker then
        util.RemoveDelayCall(pk_ticker)
        pk_ticker = nil
    end
    if pkeffect_ticker then
        util.RemoveDelayCall(pkeffect_ticker)
        pkeffect_ticker = nil
    end
    if vs_ticker then
        util.RemoveDelayCall(vs_ticker)
        vs_ticker = nil
    end
    if self.vsEffect then
        self.vsEffect:Dispose()
        self.vsEffect = nil
    end
    if self.pkEffect then
        self.pkEffect:Dispose()
        self.pkEffect = nil
    end
    for i = 1, #self.teamLeftItem_Btn do
        self.teamLeftItem_Btn[i].onClick:RemoveAllListeners()
    end
    self.teamLeftItem = nil
    self.teamRightItem = nil
    self.team_hero_l = {}
    self.team_hero_r = {}
    if self.faceIteml then
        self.faceIteml:Dispose()
        self.faceIteml = nil
    end
    if self.faceItemr then
        self.faceItemr:Dispose()
        self.faceItemr = nil
    end
    
    self.VData = nil    
    self.__base.Close(self)
    window = nil
    
    
end
--endregion

--region View Logic

---存在是刷新处理
function UIView:InitTeamObjInfo(teamNum)
    if not self:IsValid() then
        return
    end
    self.teamLeftItem = {}
    self.teamLeftItem_Btn = {}
    self.teamRightItem = {}
    self.maxTeamNum = teamNum
    for i = 1, self.maxTeamNum do
        local obj1 = self.rtf_teaminfoLeft.transform:GetChild(i-1)
        local obj2 = self.rtf_teaminfoRight.transform:GetChild(i-1)
        if(obj1 and obj2)then
            local item1 = Common_Util.GetComponent(obj1, typeof(ScrollRectItem), "")
            local item2 = Common_Util.GetComponent(obj2, typeof(ScrollRectItem), "")
            local btn = Common_Util.GetComponent(obj1, typeof(Button), "")

            table.insert(self.teamLeftItem,item1)
            table.insert(self.teamLeftItem_Btn,btn)
            table.insert(self.teamRightItem,item2)
        else
            local log = require "log"
            log.Error("获取到RectTransform失败,队伍界面应该与配置一致:",i)
        end
    end
end

---刷新敌方防守阵容数据
function UIView:OnRefreshDefendData(rightData, teamNum)
    if not self:IsValid() then
        return
    end
    if not rightData then
        return
    end
    
    self.trainTeamItem = {}
    for k, team in ipairs(rightData) do
        table.insert(self.trainTeamItem,team)
    end
    table.sort(self.trainTeamItem, function(v1, v2)
        return v1.index < v2.index
    end)
    if(not self.teamLeftItem or not self.teamRightItem)then
        self:InitTeamObjInfo(teamNum)
    end
    local totalPower = 0
    local arena_battle_mgr = require "arena_battle_mgr"
    for i = 1, #self.teamRightItem do
        local enemyItem = self.teamRightItem[i]
        if(self.team_hero_r and self.team_hero_r[i])then
            local heroList = arena_battle_mgr.SetTeamHeroItemInfo(enemyItem,i,self.trainTeamItem[i],self.team_hero_r[i])
            self.team_hero_r[i] = heroList
        else
            local heroList = arena_battle_mgr.SetTeamHeroItemInfo(enemyItem,i,self.trainTeamItem[i])
            table.insert(self.team_hero_r,heroList)
        end
        totalPower = totalPower + self.trainTeamItem[i].power
    end
    self.txt_powerr.text = util.PriceConvert(totalPower or 0)
end

--收到战斗信息播放战斗结果
function UIView:OnRefreshBattleProcess(msg)
    self.animator.enabled = false
    local serData = {}
    serData.msg = msg
    serData.attack_team = self.heroData
    serData.defend_team = self.trainTeamItem
    serData.enemyInfo = self.enemyInfo
    self:ShowBattleVSEffect(serData)
    self.noClick.gameObject:SetActive(true)
end

--region 战斗动画
--显示pk动画
function UIView:ShowBattleVSEffect(battleData)
    self.effect.gameObject:SetActive(true)
    self.vsEffect = self.vsEffect or effect_item.CEffectItem():Init(vsPath, self.effect,self.curOrder + 1,function (obj)
        if window:IsValid() then
            obj.gameObject:SetActive(true)
        end
    end)
    if self.vsEffect and self.vsEffect.gameObject then
        self.vsEffect.gameObject:SetActive(true)
    end
    ---1s后显示pk特效
    if battleData and battleData.msg and battleData.msg.attackResultList then
        self.curShowIndex = 1
        if vs_ticker then
            util.RemoveDelayCall(vs_ticker)
            vs_ticker = nil
        end
        vs_ticker = util.IntervalCall(postime + 1,function ()
            if self.curShowIndex > #battleData.msg.attackResultList then
                --战斗结果已经播放完成
                ui_window_mgr:ShowModule("ui_arena_battle_result_wheel",
                        nil,nil, battleData)
                ui_window_mgr:UnloadModule("ui_arena_battle_wheel")
                return true
            end
            local v = self:GetAtkDataByMsg(battleData.msg.attackResultList,self.curShowIndex)
            local nResult = v.nResult
            local atk_index = v.attackTeamIndex
            local def_index = v.defendTeamIndex
            --log.Error("当前展示的index:",self.curShowIndex,atk_index,def_index,v.attackWinTimes,nResult)
            ---1S后展示显示pk动画1和pk动效
            if pk_ticker then
                util.RemoveDelayCall(pk_ticker)
                pk_ticker = nil
            end
            pk_ticker = util.DelayCallOnce(pktime,function ()
                --log.Error("展示显示pk动画1:",self.curShowIndex,atk_index,def_index,nResult)
                if self.vsEffect and self.vsEffect.gameObject and self.vsEffect.gameObject.activeSelf then
                    self.vsEffect.gameObject:SetActive(false)
                end
                if not self.animator.enabled then
                    self.animator.enabled = true
                end
                --根据当前进攻阵容和防守阵容的下标处理动画问题（和火车战斗使用相同动画，不在修改）
                local clipName1 = "UI_AllianceTrainPlan_" .. atk_index.."v"..def_index
                self:ResetAnimation(clipName1)
            end)
            ---1.4S后展示PK特效
            if pkeffect_ticker then
                util.RemoveDelayCall(pkeffect_ticker)
                pkeffect_ticker = nil
            end
            pkeffect_ticker = util.DelayCallOnce(pkeffecttime,function ()
                self.pkEffect = self.pkEffect or effect_item.CEffectItem():Init(pkPath, self.effect,self.curOrder + 1,function (obj)
                    if window:IsValid() then
                        obj.gameObject:SetActive(true)
                    end
                end)
                if self.pkEffect and self.pkEffect.gameObject then
                    self.pkEffect.gameObject:SetActive(true)
                end
            end)
            ---2S后展示PK结果和win特效
            if win_ticker then
                util.RemoveDelayCall(win_ticker)
                win_ticker = nil
            end
            win_ticker = util.DelayCallOnce(wintime,function ()
                --log.Error("展示PK结果和win特效：",k,":curShowIndex:",self.curShowIndex)
                if self.pkEffect and self.pkEffect.gameObject and self.pkEffect.gameObject.activeSelf then
                    self.pkEffect.gameObject:SetActive(false)
                end
                self.left_win.gameObject:SetActive(nResult == 1)
                self.right_win.gameObject:SetActive(nResult == 2)

                --处理血条
                self:OnShowHpBarInfo(nResult,atk_index,def_index)
                self:OnShowHeroGrayInfo(nResult,atk_index,def_index)
            end)

            ---3S后展示失败或者成功的动画
            if pos_ticker then
                util.RemoveDelayCall(pos_ticker)
                pos_ticker = nil
            end
            pos_ticker = util.DelayCallOnce(postime,function ()
                self.left_win.gameObject:SetActive(false)
                self.right_win.gameObject:SetActive(false)
                --log.Error("展示失败者的位移动画：",k,":curShowIndex:",self.curShowIndex,"nResult",nResult)
                self.curShowIndex = self.curShowIndex + 1
                if nResult == 1 then    ---胜利防守方播放位移动画
                self:ResetDefAnimation(def_clips[def_index])
                    self.teamRightItem[def_index].transform:SetAsFirstSibling()
                elseif nResult == 2 then --- 失败攻击方播放位移动画
                self:ResetAtkAnimation(atk_clips[atk_index])
                    self.teamLeftItem[atk_index].transform:SetAsFirstSibling()

                elseif nResult == 3 then  ---平局都播放位移动画
                self:ResetDefAnimation(def_clips[def_index])
                    self:ResetAtkAnimation(atk_clips[atk_index])
                    self.teamLeftItem[atk_index].transform:SetAsFirstSibling()
                    self.teamRightItem[def_index].transform:SetAsFirstSibling()
                end
            end)
            
        end)
    end
end

function UIView:GetAtkDataByMsg(attackResultList,index)
    for k,v in ipairs(attackResultList) do
        if k == index then
            return v
        end
    end
    return nil
end


function UIView:OnShowHpBarInfo(nResult, atkIndex, defIndex)
    if nResult == 1 then
        local obj1 = self.rtf_hprr.transform:GetChild(defIndex-1)
        obj1.gameObject:SetActive(false)
    elseif nResult == 2 then
        local obj1 = self.rtf_hpl.transform:GetChild(atkIndex -1)
        obj1.gameObject:SetActive(false)
    elseif nResult == 3 then
        local obj1 = self.rtf_hpl.transform:GetChild(atkIndex -1)
        obj1.gameObject:SetActive(false)
        local obj2 = self.rtf_hprr.transform:GetChild(defIndex-1)
        obj2.gameObject:SetActive(false)
    end
end

function UIView:OnShowHeroGrayInfo(nResult, atkIndex, defIndex)
    local arena_battle_mgr = require "arena_battle_mgr"
    if nResult == 1 then --胜利的话敌方对应的队伍置灰
        --防守阵容
        local enemyItem = self.teamRightItem[defIndex]
        local heroList = arena_battle_mgr.SetTeamHeroItemInfo(enemyItem, defIndex,self.trainTeamItem[defIndex],self.team_hero_r[defIndex],nil,nil,true,nil)
        self.team_hero_r[defIndex] = heroList
    elseif nResult == 2 then  --- 失败的话自己的队伍置灰
        --进攻阵容
        local attackItem = self.teamLeftItem[atkIndex]
        local heroList = arena_battle_mgr.SetTeamHeroItemInfo(attackItem, atkIndex,self.heroData[atkIndex],self.team_hero_l[atkIndex],nil,nil,true,nil)
        self.team_hero_l[atkIndex] = heroList
    elseif nResult == 3 then   ---平局双方都置灰
        --进攻阵容
        local attackItem = self.teamLeftItem[atkIndex]
        local heroListL = arena_battle_mgr.SetTeamHeroItemInfo(attackItem, atkIndex,self.heroData[atkIndex],self.team_hero_l[atkIndex],nil,nil,true,nil)
        self.team_hero_l[atkIndex]= heroListL
        --防守阵容
        local enemyItem = self.teamRightItem[defIndex]
        local heroListR = arena_battle_mgr.SetTeamHeroItemInfo(enemyItem, defIndex,self.trainTeamItem[defIndex],self.team_hero_r[defIndex],nil,nil,true,nil)
        self.team_hero_r[defIndex] = heroListR
    end
end

function UIView:ResetAnimation(clipName)
    self.animator:Play(clipName, -1, 0)
    self.animator:Update(0)
end

function UIView:ResetAtkAnimation(clipName)
    if not self.atk_animator.enabled then
        self.atk_animator.enabled = true
    end
    self.atk_animator:Play(clipName, -1, 0)
    self.atk_animator:Update(0)
end

function UIView:ResetDefAnimation(clipName)
    if not self.def_animator.enabled then
        self.def_animator.enabled = true
    end
    self.def_animator:Play(clipName, -1, 0)
    self.def_animator:Update(0)
end

function UIView:ShowPlayerInfo()
    local player_mgr = require "player_mgr"
    local arena_battle_mgr = require "arena_battle_mgr"
    self.faceIteml = self.faceIteml or face_item_new.CFaceItem():Init(self.rtf_rectFace, nil, 1.24)
    --适配faceID 2025.4.2 将faceID 转换为 faceStr
    local custom_avatar_data = require "custom_avatar_data"
    local customHeadData = custom_avatar_data.GetMyAvatar()
    local faceStr = arena_battle_mgr.GetMyFaceStr(player_mgr.GetRoleFaceID())
    if customHeadData then
        faceStr = customHeadData.remoteUrl
    end
    self.faceIteml:SetFaceInfo(faceStr,nil)
    self.faceIteml:SetFrameID(player_mgr.GetPlayerFrameID(),true)
    local alliance_mgr = require "alliance_mgr"
    local worldStr = ui_util.GetWorldIDToShowWorldID(player_mgr.GetCreateRoleWorldID(),nil,ui_util.WorldIDRangeType.Normal)
    self.txt_name.text = string.format("#%s %s",worldStr, util.SplicingUnionShortName(alliance_mgr.GetUserAllianceShortName(),player_mgr.GetRoleName()))
    self:ShowEnemyPlayerInfo()
end

function UIView:ShowEnemyPlayerInfo()
    if(self.enemyInfo)then
        ---设置玩家头像
        self.faceItemr = self.faceItemr or face_item_new.CFaceItem():Init(self.rtf_rectFacer, nil, 1.24)
        local faceStr = self.entity:GetFaceId()
        if self.entity:GetFaceStr() and string.IsNullOrEmpty(self.entity:GetFaceStr()) then
            faceStr = string.IsNullOrEmpty(self.entity:GetFaceStr()) and self.entity:GetFaceId() or self.entity:GetFaceStr()
        end
        self.faceItemr:SetFaceInfo(self.enemyInfo.faceStr)
        self.faceItemr:SetFrameID(self.enemyInfo.frameID, true)
        self.txt_namer.text = string.format("#%s %s",self.enemyInfo.worldStr,util.SplicingUnionShortName(self.enemyInfo.unionShortName, self.enemyInfo.name))
    end
end

---刷新己方的进攻英雄数据
function UIView:OnRefreshHeroData(heroData, enemyInfo, OnClickTeamItem)
    self.enemyInfo = enemyInfo
    self:ShowPlayerInfo()
    if(not self.teamLeftItem or not self.teamRightItem)then
        self:InitTeamObjInfo()
    end
    ---获取当前的进攻阵容
    self.heroData = heroData
    local arena_battle_mgr = require "arena_battle_mgr"
    local totalPower = 0
    for i = 1, #self.teamLeftItem do
        local attackItem = self.teamLeftItem[i]
        if(self.team_hero_l and self.team_hero_l[i])then
            local heroList = arena_battle_mgr.SetTeamHeroItemInfo(attackItem,i,self.heroData[i],self.team_hero_l[i],nil,nil,nil,true)
            self.team_hero_l[i] = heroList
        else
            local heroList = arena_battle_mgr.SetTeamHeroItemInfo(attackItem,i,self.heroData[i],nil,nil,nil,nil,true)
            table.insert(self.team_hero_l,heroList)
        end
        local attackItemBtn = self.teamLeftItem_Btn[i]
        attackItemBtn.onClick:RemoveAllListeners()
        attackItemBtn.onClick:AddListener(function()
            OnClickTeamItem(i, self.heroData)
        end)

        totalPower = totalPower + self.heroData[i].power
    end
    self.txt_power.text = util.PriceConvert(totalPower or 0)
end


--endregion




--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
