local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform


module("ui_login_pop_halloween_binding")

UIPath = "ui/prefabs/gw/activity/halloweenactivity/uiloginpop_halloween.prefab"

WidgetTable ={
	btn_closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	txt_Tips = { path = "Bg/selfReward/txt_Tips", type = Text, },
	rtf_content = { path = "Bg/selfReward/ScrollView/Viewport/rtf_content", type = RectTransform, },
	btn_GoTo = { path = "Bg/selfReward/btn_GoTo", type = Button, event_name = "OnBtnGoToClickedProxy"},
	txt_TimeLeft = { path = "Bg/Time/txt_TimeLeft", type = Text, },

}
