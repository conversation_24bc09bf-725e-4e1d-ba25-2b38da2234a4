local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_madness_lord_rank_panel_controller")
local controller = nil
local UIController = newClass("ui_madness_lord_rank_panel_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)    
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end
function  UIController:OnBtnRank1LikeClickedProxy()
end
function  UIController:OnBtnRank2LikeClickedProxy()
end
function  UIController:OnBtnRank3LikeClickedProxy()
end
function  UIController:OnBtnLeftBtnClickedProxy()
end
function  UIController:OnTogSelfRankValueChange(state)
end
function  UIController:OnTogBoss1ValueChange(state)
end
function  UIController:OnTogBoss2ValueChange(state)
end
function  UIController:OnTogBoss3ValueChange(state)
end
function  UIController:OnTogAllianceValueChange(state)
end
function  UIController:OnBtnPlayerDamageMaskClickedProxy()
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
