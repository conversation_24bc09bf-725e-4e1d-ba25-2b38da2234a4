
local require   = require
local print     = print
local ipairs    = ipairs
local pairs     = pairs
local table     = table
local string    = string
local tonumber  = tonumber
local event         = require "event"
local xManMsg_pb        = require "xManMsg_pb"
local violentZombieLeader_pb       = require "violentZombieLeader_pb"
local net           = require "net"
local net_route     = require "net_route"
local util          = require "util"
local log           = require "log"
local lang          = require "lang"
local lua_pb        = require "lua_pb"
local flow_text        = require "flow_text"
local net_leaguepro_module  = require "net_leaguepro_module"
local gw_common_util = require "gw_common_util"
local TransmitLuaFuncReq    = net_leaguepro_module.TransmitLuaFuncReqNew
local event_violentZombieLeader_define = require("event_violentZombieLeader_define")
module("net_violentZombieLeader_module")

-- 请求总的活动页面数据
function MSG_VIOLENT_ZOMBIE_DATA_REQ()
    -- sandboxSid
    -- atyId
    local msg = violentZombieLeader_pb.TMSG_VIOLENT_ZOMBIE_DATA_REQ()
    msg.sandboxSid = gw_common_util.GetSandSandBoxSid()
    local festival_activity_mgr = require "festival_activity_mgr"
    local festival_activity_cfg = require "festival_activity_cfg"
    msg.atyId = festival_activity_mgr.GetActivityIdByCodeType(festival_activity_cfg.ActivityCodeType.MadnessLord)
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_VIOLENT_ZOMBIE_DATA_REQ, msg)
end

function MSG_VIOLENT_ZOMBIE_DATA_RSP(msg)
    -- errCode
    -- data
   if msg.errCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errCode)
    else
        event.Trigger(event_violentZombieLeader_define.TMSG_VIOLENT_ZOMBIE_DATA_RSP,msg)
    end
end

local MessageTable = {
    {xManMsg_pb.MSG_VIOLENT_ZOMBIE_DATA_RSP, MSG_VIOLENT_ZOMBIE_DATA_RSP, violentZombieLeader_pb.TMSG_VIOLENT_ZOMBIE_DATA_RSP},
}
net_route.RegisterMsgHandlers(MessageTable)