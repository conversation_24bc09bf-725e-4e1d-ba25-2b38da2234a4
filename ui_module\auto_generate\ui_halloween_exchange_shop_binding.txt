local require = require
local typeof = typeof

local Image = CS.UnityEngine.UI.Image
local RawImage = CS.UnityEngine.UI.RawImage
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Toggle = CS.UnityEngine.UI.Toggle


module("ui_halloween_exchange_shop_binding")

UIPath = "ui/prefabs/gw/activity/halloweenactivity/uihalloweenexchangeshop.prefab"

WidgetTable ={
	img_banner = { path = "TopShow/img_banner", type = Image, },
	rImg_building = { path = "TopShow/img_banner/schlossInfoMask/rImg&btn_building", type = RawImage, },
	btn_building = { path = "TopShow/img_banner/schlossInfoMask/rImg&btn_building", type = Button, event_name = "OnBtnBuildingClickedProxy"},
	txt_TitleText = { path = "TopShow/txt_TitleText", type = Text, },
	txt_TimeText = { path = "TopShow/Time/txt_TimeText", type = Text, },
	txt_TipText = { path = "TopShow/tipBg/txt_TipText", type = Text, },
	btn_TipBtn = { path = "TopShow/btn_TipBtn", type = Button, event_name = "OnBtnTipBtnClickedProxy"},
	rtf_TopResouce = { path = "TopShow/rtf_TopResouce", type = RectTransform, },
	btn_add = { path = "TopShow/rtf_TopResouce/rf_ReourceItem/btn_add", type = Button, event_name = "OnBtnAddClickedProxy"},
	srt_shopListContent = { path = "GameObject/shopList/Viewport/srt_shopListContent", type = ScrollRectTable, },
	scrItem_shopListItem = { path = "GameObject/shopList/Viewport/srt_shopListContent/scrItem_shopListItem", type = ScrollRectItem, },
	tog_ExchangeTipSwitch = { path = "tog_ExchangeTipSwitch", type = Toggle, value_changed_event = "OnTogExchangeTipSwitchValueChange"},
	btn_closeBtn = { path = "Bottom/btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},

}
