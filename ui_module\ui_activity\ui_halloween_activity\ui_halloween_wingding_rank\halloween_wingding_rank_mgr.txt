local require = require
local event = require "event"
local dump = dump
local util = require "util"
local log = require "log"  -- 提前加载 log 模块
local ui_window_mgr = require"ui_window_mgr"
module("halloween_wingding_rank_mgr")

MainToggleType = {
    Personal = 1,
    Alliance = 2
}

SubToggleType = {
    Rank = 1,
    Reward = 2
}
function GetFirstRewardItemBySubType(subType)
    local gw_activity_ranking_data = require "gw_activity_ranking_data"
    local reward_mgr = require "reward_mgr"
    local rewardListData = gw_activity_ranking_data.GetRankingRewards(13,subType)
    if rewardListData and #rewardListData > 1 then
        local rewardData = reward_mgr.GetRewardGoodsList2(rewardListData[1].rewardId)
        if rewardData and #rewardData > 0 then
            return rewardData[1]
        end
    end
    return nil
end

function Clear()

end

event.Register(event.USER_DATA_RESET, Clear)