local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local os = os
local log = require "log"
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local activity_pb = require "activity_pb"
local face_item_new = require "face_item_new"
local binding = require "ui_halloween_wingding_rank_binding"
local game_scheme = require "game_scheme"
local reward_mgr = require "reward_mgr"
local time_util = require "time_util"
local goods_item_new = require "goods_item_new"
local halloween_wingding_rank_mgr = require "halloween_wingding_rank_mgr"
local MainToggleType = halloween_wingding_rank_mgr.MainToggleType
local SubToggleType = halloween_wingding_rank_mgr.SubToggleType
--region View Life
module("ui_halloween_wingding_rank")

local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self:InitScrollRectTable()
end

function UIView:OnShow()
    self.__base.OnShow(self)
    self.mainToggle = {[MainToggleType.Personal] = self.tog_personToggle,[MainToggleType.Alliance] = self.tog_allianceToggle}
    self.subToggle = {[SubToggleType.Rank] = self.tog_rankToggle,[SubToggleType.Reward] = self.tog_rewardToggle}
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:UpdatePage(allData,mainToggleType,subToggleType)
    self.mainToggle[mainToggleType].isOn = true
    self.subToggle[subToggleType].isOn = true
    if allData and allData[mainToggleType] and allData[mainToggleType][subToggleType] then
        local curData = allData[mainToggleType][subToggleType]
        if subToggleType == SubToggleType.Reward then
            local listData = curData.rewardList
            local selfItemData = curData.curReward
            self.srt_rewardContent:SetData(listData,#listData)
            self.srt_rewardContent:Refresh(0, -1)
            self:OnRewardItemRender(self.scrItem_selfReward,1,selfItemData,true)
        elseif subToggleType == SubToggleType.Rank then
            local listData = curData.rankInfo
            local selfItemData = curData.selfRank
            self.srt_rankContent:SetData(listData,#listData)
            self.srt_rankContent:Refresh(0, -1)
            self:OnRankItemRender(self.scrItem_selfRank,1,selfItemData,true)
        end
        self:SetActive(self.rtf_rewardPage,subToggleType == SubToggleType.Reward)
        self:SetActive(self.rtf_rankPage,subToggleType == SubToggleType.Rank)

        local firstRewardData = allData[mainToggleType][SubToggleType.Reward].firstRewardData
        if firstRewardData then
            self.VData.itemUI = self.VData.itemUI or goods_item_new.CGoodsItem():Init(self.rtf_firstReward,nil,1)
            self.VData.itemUI:SetGoods(nil, firstRewardData.id, firstRewardData.num, function()
                local iui_item_detail = require "iui_item_detail"
                local item_data = require "item_data"
                iui_item_detail.Show(firstRewardData.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface)
            end)
        end
    end
end

function UIView:InitScrollRectTable()
    self.srt_rankContent.onItemRender = function (...)
        self:OnRankItemRender(...)
    end
    self.srt_rankContent.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data  then
            if scroll_rect_item.data["itemUI"] then
                scroll_rect_item.data["itemUI"]:Dispose()
            end
        end
    end   

    self.srt_rewardContent.onItemRender = function (...)
        self:OnRewardItemRender(...)
    end
    self.srt_rewardContent.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data  then
            if scroll_rect_item.data.rewardItemList then
                for i, v in pairs(scroll_rect_item.data.rewardItemList) do
                    v:Dispose()
                end
                scroll_rect_item.data.rewardItemList = nil
            end
        end
    end 
end

function UIView:OnRankItemRender(scroll_rect_item,index,dataItem,isSelf)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local NumText = scroll_rect_item:Get("NumText")
    local NumImageBg = scroll_rect_item:Get("NumImageBg")
    local NumImage = scroll_rect_item:Get("NumImage")
    local FaceIcon = scroll_rect_item:Get("FaceIcon")
    local name = scroll_rect_item:Get("name")
    local score = scroll_rect_item:Get("score")
    local flagIcon = scroll_rect_item:Get("flagIcon")
    local noneTip = scroll_rect_item:Get("noneTip")
    local hasData = scroll_rect_item:Get("hasData")
    local inRank = dataItem.noneTip == nil

    if not dataItem.noneTip then 
        inRank = not (dataItem.rank == 0)
        local isTopThree = dataItem.rank >= 1 and dataItem.rank <= 3
        if isTopThree then
            NumImageBg:Switch(dataItem.rank-1)
            NumImage:Switch(dataItem.rank-1)
        end
        NumText.text = dataItem.rank
        self:SetActive(NumText,not isTopThree or isSelf )
        self:SetActive(NumImageBg,isTopThree and not isSelf)
        self:SetActive(FaceIcon,dataItem.rankType == activity_pb.ACTTYPE_PARTY_PERSON)
        self:SetActive(flagIcon,dataItem.rankType == activity_pb.ACTTYPE_PARTY_ALLIANCE)
        name.text = dataItem.name
        if dataItem.rankType == activity_pb.ACTTYPE_PARTY_PERSON then
            local item = scroll_rect_item.data["itemUI"] or face_item_new.CFaceItem():Init(FaceIcon, nil, 1.05)
            item:SetFaceInfo(dataItem.faceStr)
            item:SetNewBg(true)
            item:SetFrameID(dataItem.frameID, true)
            item:FrameEffectEnable(true, self.curOrder+1)
            scroll_rect_item.data["itemUI"] = item
        end
        if dataItem.rankType == activity_pb.ACTTYPE_PARTY_ALLIANCE then
            self:CreateSubSprite("CreateLeagueAsset",flagIcon,dataItem.flagIconName)
        end
        score.text = dataItem.score
        --绑定上点击等事件，主要是为了抛给controller层进行分离   
        scroll_rect_item.InvokeFunc =  function(funcname,obj)
            --注意  这里的事件是存在多种的clickItemEvent 
            if dataItem[funcname] then
                dataItem[funcname](scroll_rect_item,index,dataItem)
            end
        end
    else
        noneTip.text = dataItem.noneTip
    end

    self:SetActive(noneTip, not inRank)
    self:SetActive(hasData, inRank)
end

function UIView:OnRewardItemRender(scroll_rect_item,index,dataItem,isSelf)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local rewardList = scroll_rect_item:Get("rewardList")
    local NumImageBg = scroll_rect_item:Get("NumImageBg")
    local NumText = scroll_rect_item:Get("NumText")
    local none = scroll_rect_item:Get("none")
    local hasData = scroll_rect_item:Get("hasData")
    local NumImage = scroll_rect_item:Get("NumImage")
    if dataItem then
        self:SetActive(none,dataItem.rewardId == nil)
        self:SetActive(hasData,dataItem.rewardId ~= nil)
        if dataItem.rewardId then
            if scroll_rect_item.data.rewardItemList then
                for i, v in pairs(scroll_rect_item.data.rewardItemList) do
                    v:Dispose()
                end
                scroll_rect_item.data.rewardItemList = nil
            end
            scroll_rect_item.data.rewardItemList = reward_mgr.GetRewardItemList(dataItem.rewardId, rewardList,
                    function(id,number)
                        local iui_item_detail =  require "iui_item_detail"
                        local item_data = require "item_data"
                        iui_item_detail.Show(id,nil,  item_data.Item_Show_Type_Enum.Reward_Interface, number,nil, nil, nil)
                    end,0.5)

            local isTopThree = index > 0 and index <= 3
            local isSingle = dataItem.rankRange1 == dataItem.rankRange2
            if isTopThree and isSingle then
                NumImageBg:Switch(index-1)
                NumImage:Switch(index-1)
            end
            self:SetActive(NumImageBg,not isSelf and isTopThree and isSingle)
            self:SetActive(NumText,not isSingle or isSelf)
            local rank
            if isSingle then
                rank = dataItem.rankRange1
            else
                rank = string.format("%d-%d",dataItem.rankRange1,dataItem.rankRange2)
            end
            NumText.text = rank
        else

        end

        --绑定上点击等事件，主要是为了抛给controller层进行分离   
        scroll_rect_item.InvokeFunc =  function(funcname,obj)
            --注意  这里的事件是存在多种的clickItemEvent 
            if dataItem[funcname] then
                dataItem[funcname](scroll_rect_item,index,dataItem)
            end
        end
    end

end

function UIView:SetTopTime(endTime)
    if self.Timer then 
        self:RemoveTimer(self.Timer)
        self.Timer = nil
    end
    self.Timer=self:CreateTimer(1,function() 
        local curTime = os.server_time()
        if endTime > 0 then 
            local offset = endTime - curTime
            --print("CreateTimer ,offset,endTime,curTime",offset,endTime,curTime)
            if offset > 0 and not util.IsObjNull(self.txt_tip) then
                self.txt_tip.text = string.format2(lang.Get(1009336),time_util.FormatTime5(offset))
            else
                self:RemoveTimer(self.Timer)
                self.Timer = nil
            end    
        else
            self:RemoveTimer(self.Timer)
            self.Timer = nil
        end
    end)
end
function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, true, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil, true, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
