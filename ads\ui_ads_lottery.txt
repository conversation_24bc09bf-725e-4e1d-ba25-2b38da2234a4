--@region FileHead
-- ui_ads_lottery.txt ---------------------------------
-- author:  无名氏
-- date:    11/18/2020 3:36:07 PM
-- ver:     1.0
-- desc:    Description
-------------------------------------------------
--@endregion 

--@region Require
local print     = print
local require   = require
local ipairs    = ipairs
local typeof    = typeof
local string    = string
local table     = table
local math      = math
local os        = os
local coroutine = coroutine

local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local Transform     = CS.UnityEngine.Transform
local UISound 	    = CS.War.Script.UISound
local Time          = CS.UnityEngine.Time
local RectTransform = CS.UnityEngine.RectTransform
local Application   = CS.UnityEngine.Application

local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local log                   = require "log"
local lotterywish_pb        = require "lotterywish_pb"
local goods_item            = require "goods_item_new"
local game_scheme           = require "game_scheme"
local item_data             = require "item_data"
local lottery_data          = require "lottery_data"
local iui_item_detail       = require "iui_item_detail"
local util                  = require "util"
local timer_mgr             = require "timer_mgr"
local ui_window_mgr         = require "ui_window_mgr"
local base_object           = require "base_object"
local lang                  = require "lang"
local flow_text             = require "flow_text"
local ad_mgr                = require "ad_mgr"
local asset_loader          = require "asset_loader"
--@endregion 

--@region ModuleDeclare
module("ui_ads_lottery")

local window = nil
local UIAdsLottery = {}
--@endregion 
local wishType = lotterywish_pb.emLotteryWishType_Advertisement1
local closeCallBack = nil
local rewardCount = 8
local lastIndex = 1
local animCurvePath = "ui/wishpool/animstyle.asset"
local animCurve = nil
local animTimer = nil
local isClickVideo = false -- 是否在播放加载中
local isVideoFinished = false -- 广告是否播放完
-- 如果界面打开预加载时失败，在点击时 IsRewardedAdLoaded 应该一直是 false,导致一直不能打开且无明显提示
local assetLoader = nil
local timeout = false --广告加载超时
local nextType = nil  -- 随机出的下一个可播放视频
--@region WidgetTable
UIAdsLottery.widget_table = {
    Btn_VideoBtn = { path = "Auto_VideoBtn", type = "Button", event_name = "OnBtn_VideoBtnClickedProxy" },
    Btn_closeBtn = { path = "Auto_closeBtn", type = "Button", event_name = "OnBtn_closeBtnClickedProxy" ,backEvent = true},
    Text_RefreshTime = { path = "Auto_RefreshTime", type = "Text", event_name = "" },
	
	--特效
    UI_XYCzhongjiang={path = "rewardPool/UI_XYCzhongjiang", type =Transform},
    lightIcon={path = "rewardPool/lightIcon", type = Transform},
	--交互遮罩
    Mask = {path = "Mask", type = "RectTransform"},
	Auto_bg1 = {path = "Auto_bg1", type = RectTransform},
	Auto_bg2 = {path = "Auto_bg2", type = RectTransform},
	Auto_bg3 = {path = "Auto_bg3", type = RectTransform},
	enableBtn = {path = "Auto_enableBtn", type = "Button", event_name = "OnBtn_disableBtnClickedProxy" },
	tips = {path = "tips", type = "Text"},

	-- toggle相关
	adsToggle1 = {path = "toggle_group/toggle_ads1", type = "Toggle", value_changed_event = "OnToggleAds1Proxy"},
    adsText1 = {path = "toggle_group/toggle_ads1/text", type = "Text"},
    adsSelectText1 = {path = "toggle_group/toggle_ads1/text_s", type = "Text"},
    adsToggle2 = {path = "toggle_group/toggle_ads2", type = "Toggle", value_changed_event = "OnToggleAds2Proxy"},
    adsText2 = {path = "toggle_group/toggle_ads2/text", type = "Text"},
    adsSelectText2 = {path = "toggle_group/toggle_ads2/text_s", type = "Text"},
    adsToggle3 = {path = "toggle_group/toggle_ads3", type = "Toggle", value_changed_event = "OnToggleAds3Proxy"},
    adsText3 = {path = "toggle_group/toggle_ads3/text", type = "Text"},
    adsSelectText3 = {path = "toggle_group/toggle_ads3/text_s", type = "Text"},
    adsRed1 = {path = "toggle_group/toggle_ads1/red", type = RectTransform },
    adsRed2 = {path = "toggle_group/toggle_ads2/red", type = RectTransform },
    adsRed3 = {path = "toggle_group/toggle_ads3/red", type = RectTransform },
--@region User
--@endregion 
}
--@endregion 

--@region WindowCtor
function UIAdsLottery:ctor(selfType)
	self._noBg = true
	self.startVideoTime = 0 -- 开始播放视频的时间
	self.__base:ctor(selfType)
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function UIAdsLottery:Init()
	self:InitRewardItemList()
	self:SubscribeEvents()
	self:UpdateAdsUI()
	event.Trigger(event.GAME_EVENT_REPORT, "ads_lottery_enter", {iType = wishType - 2})
	self.tips.text = lang.Get(184005)
	self:UpdateAdsToggle(wishType)
	self:UpdateAdsReddot(lotterywish_pb.emLotteryWishType_Advertisement1, lottery_data.GetRedDot(lotterywish_pb.emLotteryWishType_Advertisement1))
	self:UpdateAdsReddot(lotterywish_pb.emLotteryWishType_Advertisement2, lottery_data.GetRedDot(lotterywish_pb.emLotteryWishType_Advertisement2))
	self:UpdateAdsReddot(lotterywish_pb.emLotteryWishType_Advertisement3, lottery_data.GetRedDot(lotterywish_pb.emLotteryWishType_Advertisement3))
	
	--动画曲线
	if not assetLoader then
		assetLoader = asset_loader(animCurvePath, "ui_ads_lottery")
	end
	assetLoader:load(function(ld)
		animCurve = ld.asset
	end)
--@region User
--@endregion 
end --///<<< function

function UIAdsLottery:UpdateAdsUI()
	-- 预加载广告
	local isAdLoaded = ad_mgr.IsRewardedAdLoaded(wishType-2)
	if not isAdLoaded then
		ad_mgr.CreateRewardedAd(wishType-2)
	end

	self.Auto_bg1.gameObject:SetActive(wishType == lotterywish_pb.emLotteryWishType_Advertisement1)
	self.Auto_bg2.gameObject:SetActive(wishType == lotterywish_pb.emLotteryWishType_Advertisement2)
	self.Auto_bg3.gameObject:SetActive(wishType == lotterywish_pb.emLotteryWishType_Advertisement3)
	
	self:UpdateRewardItem()
	self:StartCountDown()
	--默认选中
	self:SwitchRewardLight(-1)
end

function UIAdsLottery:UpdateAdsToggle(iType)
	if iType == lotterywish_pb.emLotteryWishType_Advertisement1 then
		self.adsToggle1.isOn = true
	elseif iType == lotterywish_pb.emLotteryWishType_Advertisement2 then
		self.adsToggle2.isOn = true
	else
		self.adsToggle3.isOn = true
	end
end

function UIAdsLottery:UpdateAdsReddot(iType, isShow)
	if iType == lotterywish_pb.emLotteryWishType_Advertisement1 then
		self.adsRed1.gameObject:SetActive(isShow)
	end
	if iType == lotterywish_pb.emLotteryWishType_Advertisement2 then
		self.adsRed2.gameObject:SetActive(isShow)
	end
	if iType == lotterywish_pb.emLotteryWishType_Advertisement3 then
		self.adsRed3.gameObject:SetActive(isShow)
	end
end
--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIAdsLottery:OnShow()
    
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnHide
--[[界面隐藏时调用]]
function UIAdsLottery:OnHide()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowClose
function UIAdsLottery:Close()
    if self:IsValid() then
		self:UnsubscribeEvents()
	end
	wishType = lotterywish_pb.emLotteryWishType_Advertisement1
	closeCallBack = nil
	lastIndex = 1
	if self.itemList then
        for i=1,#self.itemList do
            self.itemList[i].iconUI:Dispose()
            self.itemList[i].iconUI = nil
            self.itemList[i].icon = nil
            self.itemList[i].data = nil
            self.itemList[i] = nil
        end
    end
	if assetLoader then
		assetLoader:Dispose()
		assetLoader = nil
	end
    animCurve = nil
	if animTimer then
        util.RemoveDelayCall(animTimer)
        animTimer = nil
    end
	if self.refreshTimer then
		self.refreshTimer:Dispose()
		self.refreshTimer = nil
	end
	isClickVideo = false
	isVideoFinished = false
	timeout = false
	nextType = nil
	self.__base:Close()
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

function UIAdsLottery:SubscribeEvents()
	--log.Warning("admob, SubscribeEvents")

    self.OnBtn_VideoBtnClickedProxy = function()
        self:OnBtn_VideoBtnClicked()
    end
	
	self.OnBtn_closeBtnClickedProxy = function()
        self:OnBtn_closeBtnClicked()
    end

    self.OnBtn_disableBtnClickedProxy = function ()
    	flow_text.Add(lang.Get(184006))
	end
	
	self.OnToggleAds1Proxy = function (value)
		self:OnAdsToggleChange(lotterywish_pb.emLotteryWishType_Advertisement1, value)
	end

	self.OnToggleAds2Proxy = function (value)
		self:OnAdsToggleChange(lotterywish_pb.emLotteryWishType_Advertisement2, value)
	end

	self.OnToggleAds3Proxy = function (value)
		self:OnAdsToggleChange(lotterywish_pb.emLotteryWishType_Advertisement3, value)
	end
	
	self.lotteryWishRSP = function(eventName, index, rewards)
		self.Mask.gameObject:SetActive(true)
		self:ShowBEffect(false)
		self:SwitchRewardLight(lastIndex)

		local processCallBack=function(turnIndex)
			if window and window:IsValid() then
				window:SwitchRewardLight(turnIndex)
			end
		end
		local completeCallcack=function(turnIndex)
			--print("completeCallcack>>>>>>>>",turnIndex,index)
			-- if window and turnIndex==index then
			-- 不管 turnIndex 是否等于 index,至少需要设置 window.Mask.gameObject:SetActive(false)，否则界面完全不可点击
			-- 先直接使用 index，但逻辑上需要保证返回的 turnIndex == index 才能保证动画正常
			--log.Warning("admob, anim complete")
			if turnIndex ~= index then
				log.Error("turnIndex not equal index:",turnIndex,index)
			end

			if window then
				window:ShowBEffect(true,index)
				window:SwitchRewardLight(-1)
				util.DelayCall(0.5,function()
					if window and window:IsValid() then
						window.Mask.gameObject:SetActive(false)   
					end
					DisplayReward(rewards)
				end)
			end
		end

		local music_contorller = require "music_contorller"
		music_contorller.PlayFxAudio(300006)
		local duration = 5
		if animCurve and animCurve.scale then
			duration = animCurve.scale.keys[animCurve.scale.length - 1].time
		end
        StartLotteryAnim(OneCurve,duration,5,lastIndex,index,processCallBack,completeCallcack)
	end
	event.Register(event.EVENT_ONMSG_LOTTERYWISH_RSP, self.lotteryWishRSP)
	
	self.updatelotteryData = function()
		self:UpdateRewardItem()
		self:StartCountDown()
	end
	event.Register(event.EVENT_UDAPTE_LOTTERY_DATA, self.updatelotteryData)

	self.updateAdsToggleRed = function (evenName, iType, isShow)
		self:UpdateAdsReddot(iType, isShow)
	end
	self:RegisterEvent(event.EVENT_UPDATE_LOTTERY_REDDOT, self.updateAdsToggleRed)

	self.nextVideoEvent = function ()
		if nextType then
			event.Trigger(event.GAME_EVENT_REPORT, "ads_enter_reward")
			self:UpdateAdsToggle(nextType)
		end
	end
	self:RegisterEvent(event.CLICK_NEXT_VIDEO_BTN, self.nextVideoEvent)
	
	self.rewardedAdCallback = function(eventName, tag1, index, msg)
		local iType = wishType - 2
		if index == wishType - 2 then
			if tag1 == 1 then
				-- 广告加载完成
				if ui_window_mgr:IsModuleShown("ui_ads_loading") then
					ui_window_mgr:UnloadModule("ui_ads_loading")
				end
				-- 可以播放
				if self.active and self.active == true and isClickVideo == true and timeout == false then
					isClickVideo = false
					ad_mgr.ShowRewardedAd(index)
				end				
			end
			if tag1 == 2 then
				-- 广告加载失败
				if self.active and self.active == true and isClickVideo == true and timeout == false then
					isClickVideo = false
					flow_text.Add(lang.Get(184004))
				end

				if ui_window_mgr:IsModuleShown("ui_ads_loading") then
					ui_window_mgr:UnloadModule("ui_ads_loading")
				end
			end
		end
		if tag1 == 3 then
			-- 广告开始展示
			event.Trigger(event.GAME_EVENT_REPORT, "ads_opening", {iType = iType})
			self.startVideoTime = os.time()
		end
		if tag1 == 4 then
			-- 广告展示失败
			local properties = {iType = iType, errorMsg = msg}
			event.Trigger(event.GAME_EVENT_REPORT, "ads_opening_failed", properties)
		end
		if tag1 == 5 then
			-- 广告关闭时
			if Application.isEditor then
				isVideoFinished = true
			end
			local isFinished = 0
			if isVideoFinished == true then
				lottery_data.C2SLotteryWishREQ(wishType)
				isVideoFinished = false
				isFinished = 1
			end
			local properties = {iType = iType, videoTime = os.time()-self.startVideoTime, isFinished = isFinished}
			event.Trigger(event.GAME_EVENT_REPORT, "ads_closed", properties)
		end
		if tag1 == 6 then
			-- 在用户因观看视频而应获得奖励时 看完视频
			isVideoFinished = true
		end
	end
	event.Register(event.EVENT_REWARDEDAD_CALLBACK, self.rewardedAdCallback)
end --///<<< function
--@endregion 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIAdsLottery:UnsubscribeEvents()
--@region User
	event.Unregister(event.EVENT_ONMSG_LOTTERYWISH_RSP, self.lotteryWishRSP)
	event.Unregister(event.EVENT_UDAPTE_LOTTERY_DATA, self.updatelotteryData)
	event.Unregister(event.EVENT_REWARDEDAD_CALLBACK, self.rewardedAdCallback)
--@endregion 
end --///<<< function

--@endregion 

function PlayAds()
	event.Trigger(event.GAME_EVENT_REPORT, "ads_lottery_start_video", {iType = wishType - 2})
	-- 播放广告或者请求加载广告 wishType是从3开始，index是从1开始
	local isAdLoaded = ad_mgr.IsRewardedAdLoaded(wishType-2)
	if not isAdLoaded then
		isClickVideo = true
		timeout = false
		ui_window_mgr:ShowModule("ui_ads_loading")
		util.DelayOneCall("ui_ads_loading_close", function ()
			timeout = true
			if ui_window_mgr:IsModuleShown("ui_ads_loading") then
				ui_window_mgr:UnloadModule("ui_ads_loading")
				flow_text.Add(lang.Get(184004))
			end
		end, lottery_data.timeout)
		ad_mgr.CreateRewardedAd(wishType-2)
		return
	end
	ad_mgr.ShowRewardedAd(wishType-2)
end

--@region WindowBtnFunctions
function UIAdsLottery:OnBtn_VideoBtnClicked()
	lottery_data.ShowLotteryJumpAdsTip(LotteryWishREQ, PlayAds)
end

function LotteryWishREQ()
	lottery_data.C2SLotteryWishREQ(wishType)
end

function UIAdsLottery:OnBtn_closeBtnClicked()
	if closeCallBack then
		closeCallBack()
		closeCallBack = nil
	else
		local laymain_mgr = require "laymain_mgr"
		if laymain_mgr.GetCurSceneIndex() ~= laymain_mgr.UNION_SCENE then
			ui_window_mgr:ShowModule("ui_lobby")	
		end
	end
	ui_window_mgr:UnloadModule("ui_ads_lottery")
end

function UIAdsLottery:OnAdsToggleChange(index, value)
	SetLotteryType(index)
	self:UpdateAdsUI()
	self:ShowBEffect(false)
	if value == true then
		event.Trigger(event.GAME_EVENT_REPORT, "ads_lottery_enter", {iType = wishType - 2})
	end
end

--[[刷新倒计时]]
function UIAdsLottery:StartCountDown()
	self.Text_RefreshTime.text = ""
	self:UpdateBtnState(true)
	if self.refreshTimer then
		self.refreshTimer:Dispose()
		self.refreshTimer = nil
	end
	local remainTime = lottery_data.GetLotteryCountdown(wishType)
	if not remainTime then
		return
	end
	local greenColor = "#64C5B4"
	local yellowColor = "#E8D975"
	local purpleColor = "#9FADF4"
	local textColor = greenColor
	if wishType == lotterywish_pb.emLotteryWishType_Advertisement2 then
		textColor = yellowColor
	elseif wishType == lotterywish_pb.emLotteryWishType_Advertisement3 then
		textColor = purpleColor
	end

	remainTime = remainTime - os.server_time()
	self.refreshTimer = base_object()
	self.refreshTimer:CreateTimeTicker(0, function()
		local last = os.server_time()
		while true do
			local now = os.server_time()
			local pass = now - last
			local t = remainTime - pass	
			if window and window:IsValid() then
				local dayNum, timeStr = string.countdown(t)
				local hour, min, sec = string.match(timeStr, '(%d+):(%d+):(%d+)')
				self.Text_RefreshTime.text = string.format("<color=%s>%s</color>".."<color=#F75240>%s</color>" ,textColor, lang.Get(15109), hour..":"..min..":"..sec) --#F75240 #64C5B4绿色 #E8D975黄色 #9FADF4 紫色
				self:UpdateBtnState(false)
			end
			if t <= 0 then
				-- 改变按钮状态为可点击
				self.Text_RefreshTime.text = ""
				self:UpdateBtnState(true)
				break
			end
			coroutine.yield(1)
		end
	end)
end

function UIAdsLottery:UpdateBtnState(isActive)
	self.active = isActive
	if self.Btn_VideoBtn.gameObject.activeSelf ~= isActive then 
		self.Btn_VideoBtn.gameObject:SetActive(isActive)
	end
	
	if self.enableBtn.gameObject.activeSelf ~= not isActive then
		self.enableBtn.gameObject:SetActive(not isActive)
	end	
end

--转盘奖品列表------------------------
function UIAdsLottery:InitRewardItemList()
	self.itemList = {}
    for i=1,rewardCount do
        self.itemList[i] = {}
        self.itemList[i].icon = self.UIRoot.transform:Find("rewardPool/item"..i)
        self.itemList[i].iconUI = goods_item.CGoodsItem()
        self.itemList[i].iconUI:Init(self.itemList[i].icon.transform)
        self.itemList[i].iconUI:SetFrame(false)
    end
end

function UIAdsLottery:UpdateRewardItem()
    local DataList = {}
	local lotteryData = lottery_data.GetLotteryData(wishType)
    local dataList = lotteryData.arrRwItems or {}
    local totalChance = 0
    for i=1,#dataList do
        local rewardid = dataList[i].iRewardID
        local boxid = dataList[i].iFromBoxId
        local cfg
        if rewardid then
            cfg = game_scheme:Reward_0(rewardid)
        end
        if not cfg then
            return {}
        end
        local rewarddata = {}
        if cfg.iRewardType == 2 then
            local heroCfg = game_scheme:Hero_0(cfg.arrParam[0])
            rewarddata.id = cfg.arrParam[0]
            rewarddata.icon = heroCfg and heroCfg.faceID
            rewarddata.type = item_data.Item_Type_Enum.Hero
            rewarddata.starLevel = cfg.arrParam[1]
            rewarddata.num = 1
            rewarddata.iRewardID = cfg.iRewardID
            if dataList[i].bNoSupply == true then
                rewarddata.mask = true
            else
                rewarddata.mask = false
            end

            local level = lotteryData.iLevelCreate
            
            local wishcfg = nil
            for i=1,game_scheme:LotteryWish_nums() do
                local scheme = game_scheme:LotteryWish_0(i)
                if wishType == scheme.iType and level >= scheme.iLevel then
                    wishcfg = scheme
                end
            end
            if wishcfg then
                for i=0,wishcfg.arrBoxID.count  do
                    if wishcfg.arrBoxID.data[i] == boxid then          
                        rewarddata.chance = wishcfg.showArrProbability.data[i]
                        totalChance = totalChance + rewarddata.chance
                        break
                    end
                end
            else
                log.Error("配置为空")
            end
        elseif cfg.iRewardType == 1 then
            local itemCfg = game_scheme:Item_0(cfg.arrParam[0])
            if itemCfg ~= nil then
                rewarddata.type = itemCfg.type
                rewarddata.icon = itemCfg.icon
                rewarddata.quality = itemCfg.quality
            else
                rewarddata.type = 0
                rewarddata.icon = 0
                rewarddata.quality = 1
                log.Error("Item.csv not find", cfg.arrParam[0])
            end
            rewarddata.id = cfg.arrParam[0]
            rewarddata.num = cfg.arrParam[1]
            rewarddata.iRewardID = cfg.iRewardID
            if dataList[i].bNoSupply == true then
                rewarddata.mask = true
            else
                rewarddata.mask = false
            end

            local level = lotteryData.iLevelCreate
            local wishcfg = nil
            for i=1,game_scheme:LotteryWish_nums() do
                local scheme = game_scheme:LotteryWish_0(i)
                if wishType == scheme.iType and level >= scheme.iLevel then
                    wishcfg = scheme
                end
            end
            if wishcfg then
                for i=0,wishcfg.arrBoxID.count  do
                    if wishcfg.arrBoxID.data[i] == boxid then          
                        rewarddata.chance = wishcfg.showArrProbability.data[i]
                        totalChance = totalChance + rewarddata.chance
                        break
                    end
                end
            else
                log.Error("配置为空")
            end
        end
		
        self.itemList[i].data = rewarddata
        self.itemList[i].iconUI:SetGoods(nil, rewarddata.id, rewarddata.num, function()
            iui_item_detail.Show(self.itemList[i].data.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil ,nil ,self.itemList[i].data.iRewardID)
        end)
        self.itemList[i].iconUI:SetEnableState(not rewarddata.mask, true)
    end
end

--切换亮框
function UIAdsLottery:SwitchRewardLight(index)
    --print("SwitchRewardLight>>>>>>",rewardCount)y
    if index > 0 and index <= 8 then
		self.lightIcon.transform.position=self.itemList[index].icon.transform.position
		self.lightIcon.gameObject:SetActive(true)
		lastIndex=index
		-- local music_contorller = require "music_contorller"
		-- music_contorller.PlayFxAudio(300037)
		self.itemList[index].icon.gameObject:GetComponent(typeof(UISound)):Play()
	else
		self.lightIcon.gameObject:SetActive(false)
	end

end
--显示边框特效
function UIAdsLottery:ShowBEffect(isShow,index)
    if not self.UIRoot or window==nil then
        return 
    end

    if isShow then
        self.UI_XYCzhongjiang.transform.position=self.itemList[index].icon.transform.position
        self.UI_XYCzhongjiang.gameObject:SetActive(true)
    else
        self.UI_XYCzhongjiang.gameObject:SetActive(false)
    end
    
end
--转盘奖品列表end------------------------

--easeFun 曲线函数,duration 持续时间,rotateNum 转圈次数 ,startIdx 开始序号,targetIdx 目标序号,processCallBack 过程回调,completeCallcack 完成回调
function StartLotteryAnim(easeFun,duration,rotateNum,startIdx,targetIdx,processCallBack,completeCallcack)
    local startTime=Time.time
    local curDuration=0
    local curIndex=startIdx

    local ease_b=startIdx   --起始步数
    local ease_c=rotateNum*rewardCount+targetIdx     --最终步数
    --log.Warning("StartLotteryAnim startIdx:", ease_b,"targetIdx:", targetIdx, ",step:", ease_c)
    local step
    local t_Index
    if animTimer then
        util.RemoveDelayCall(animTimer)
    end
    -- animTimer = util.DelayCall(0,function()
    --     while(curDuration<=duration)do
    --         step= easeFun(curDuration,ease_b,ease_c,duration)
    --         --将value归化到1~8
    --         t_Index = math.floor(step%8 ) 
    --         t_Index=t_Index==0 and 8 or t_Index
    --         if t_Index~=curIndex then
    --             curIndex=t_Index
    --             processCallBack(curIndex)
    --         end

    --         coroutine.yield(0.02)
    --         curDuration=Time.time-startTime
    --     end

    --     curDuration=duration
    --     step= easeFun(curDuration,ease_b,ease_c,duration)
    --     --print("last>>",step)
    --     --将value归化到1~8
    --     t_Index = math.floor(step%8 ) 
    --     t_Index=t_Index==0 and 8 or t_Index
    --     curIndex=t_Index
    --     processCallBack(curIndex)
    --     completeCallcack(curIndex)
    -- end)

    animTimer = timer_mgr:AddTimer(0, function()
        if curDuration<=duration then
            step= easeFun(curDuration,ease_b,ease_c,duration)
            --将value归化到1~8
            t_Index = math.floor(step%8 ) 
            t_Index=t_Index==0 and 8 or t_Index
            if t_Index~=curIndex then
                curIndex=t_Index
                processCallBack(curIndex)
            end

            curDuration=Time.time-startTime
            return 0.02
        else
            curDuration=duration
            step= easeFun(curDuration,ease_b,ease_c,duration)
            --print("last>>",step)
            --将value归化到1~8
            t_Index = math.floor(step%8 ) 
            t_Index=t_Index==0 and 8 or t_Index
            curIndex=t_Index
            processCallBack(curIndex)
            completeCallcack(curIndex)
            util.RemoveDelayCall(animTimer)
            animTimer = nil
            return
        end
    end)
end

--[[ Ease函数的参数
    t 当前时间（秒）
    b 起始价值
    c 最终价值
    d 动画的持续时间
]]
function OneCurve(t,  b,  c,  d)
    if not animCurve then return 1 end
    local r = b + animCurve.scale:Evaluate(t)*(c-b)
    return r<c and r or c
end

function TenCurve(t,b,c,d) 
    if not animCurve then return 1 end
    local r = b + animCurve.alpha:Evaluate(t)*(c-b)
    return r<c and r or c
end  



function DisplayReward(dataList)
    local reward_mgr = require "reward_mgr"
    local data = {}
    for i,v in ipairs(dataList) do
        table.insert(data, reward_mgr.GetRewardGoods(v.rewardID))
    end
	local list = {}
	local resultData = {title = "", dataList = data}
	table.insert(list, resultData)
	local ui_reward_result = require "ui_reward_result"
	ui_reward_result.SetInputParam(list)
	local adsTypes = {lotterywish_pb.emLotteryWishType_Advertisement1, lotterywish_pb.emLotteryWishType_Advertisement2, lotterywish_pb.emLotteryWishType_Advertisement3}
	local randomTable = {}
	for i, v in ipairs(adsTypes) do
		local remainTime =  lottery_data.GetLotteryCountdown(v)
		if not remainTime then
			table.insert(randomTable, v)
		end
	end
	local showNextAds = false
	nextType = nil
	if #randomTable > 0 then
		math.randomseed(os.time())
		for i = 1, 2 do
			local value = math.random(1, #randomTable)
			if i == 2 then
				nextType = randomTable[value]
			end
		end
		showNextAds = true
	end
	ui_reward_result.SetShowAdsBtnEnable(showNextAds)
	ui_window_mgr:ShowModule("ui_reward_result")
end


function SetLotteryType(iType, callback)
	wishType = iType
	closeCallBack = callback
end
--@endregion 

--@region ScrollItem
--@endregion 

--@region WindowInherited
local CUIAdsLottery = class(ui_base, nil, UIAdsLottery)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIAdsLottery()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uiadslottery.prefab", nil, nil, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

--@endregion 
