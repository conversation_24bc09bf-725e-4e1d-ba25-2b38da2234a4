local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil
local GameObject = CS.UnityEngine.GameObject
local GWAssetMgr = GWAssetMgr

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local gw_totalrecharge_mgr = require "gw_totalrecharge_mgr"
local goods_item = require "goods_item_new"
local item_data = require "item_data"
local iui_item_detail = require "iui_item_detail"
local log = require "log"
local color_palette = require "color_palette"
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local typeof = typeof
local binding = require "ui_halloween_daily_gift_binding"

--region View Life
module("ui_halloween_daily_gift")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil

    if self.rechargeList then
        for k, rechargeItem in pairs(self.rechargeList) do
            if rechargeItem and rechargeItem.item then
                local item = rechargeItem.item
                local getBtn = item:Get("GetBtn")
                local buyBtn = item:Get("BuyBtn")
                local specialGo_Btn = item:Get("SpecialGo_Btn")
                getBtn.onClick:RemoveAllListeners()
                buyBtn.onClick:RemoveAllListeners()
                specialGo_Btn.onClick:RemoveAllListeners()
            end
            if rechargeItem.goodsList then
                for i, goodsItem in pairs(rechargeItem.goodsList) do
                    if goodsItem then
                        goodsItem:Dispose()
                    end
                end
                rechargeItem.goodsList = nil
            end
        end
    end
    self.btn_DrawActivityCoin.onClick:RemoveAllListeners()
    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic


function UIView:InitRechargeList(rechargeDataArr, clickFun, clickKey)
    self.rechargeList = self.rechargeList or {}
    UIUtil.SetActive(self.scrItem_RechargeItem.gameObject, false)
    
    for k, v in ipairs(rechargeDataArr) do
        local rechargeItem = self.rechargeList[k] or {}
        local item = rechargeItem.item or GameObject.Instantiate(self.scrItem_RechargeItem.gameObject, self.scrItem_RechargeItem.transform.parent):GetComponent(typeof(ScrollRectItem))
        rechargeItem.item = item
        UIUtil.SetActive(item, true)
        
        local bg = item:Get("BG")
        local chargeParent = item:Get("CharegeParent")
        local specialParent = item:Get("SpecialParent")

        UIUtil.SetActive(chargeParent, false)
        UIUtil.SetActive(specialParent, false)
        if v.isSpecial then
            UIUtil.SetActive(specialParent, true)
            bg:Switch(0)
            
            local specialGo_Btn = item:Get("SpecialGo_Btn")
            specialGo_Btn.onClick:RemoveAllListeners()
            specialGo_Btn.onClick:AddListener(clickFun)
        else
            UIUtil.SetActive(chargeParent, true)
            
            local titleText = item:Get("TitleText")
            local multiplesText = item:Get("MultiplesText")
            local coinIcon = item:Get("CoinIcon")
            local getBtn = item:Get("GetBtn")
            local buyBtn = item:Get("BuyBtn")
            local priceText = item:Get("PriceText")
            --local rewardItem = item:Get("RewardItem")
            local comRechargeScore = item:Get("ComRechargeScore")
            gw_totalrecharge_mgr.SetRechargeScore(v.rechargeGoodsID, comRechargeScore.transform, self)
            local rewardContent = item:Get("RewardContent")

            if v.rechargeGoodsCfg.PackageQuality then
                bg:Switch(v.rechargeGoodsCfg.PackageQuality)
            else
                bg:Switch(1)
            end
            if not v.nameLangID then
                log.Error("UI_Halloween_Daily_Gift:InitRechargeList: nameLangID(strGoodsNameID) is nil, rechargegoodsid = "..tostring(v.rechargeGoodsID))
            end
            titleText.text = lang.Get(v.nameLangID)
            UIUtil.SetActive(getBtn, v.isFree)     -- 免费礼包才显示领取按钮

            -- 设置付费礼包的显示
            if v.canBuyNum > 0 and not v.isFree then
                multiplesText.text = string.format("%d%%", v.rechargeGoodsCfg.GiftValue)
                priceText.text = v.priceText
                -- 设置图标
                if v.CoinIcon then
                    GWAssetMgr:LoadGoodsIcon(v.CoinIcon, function(sprite)
                        if sprite then
                            coinIcon.sprite = sprite
                        end
                    end)
                end
            end

            getBtn.onClick:RemoveAllListeners()
            getBtn.onClick:AddListener(function()
                v.GetRechargeFun(v)
            end)

            buyBtn.onClick:RemoveAllListeners()
            buyBtn.onClick:AddListener(function()
                v.BuyRechargeFun(v)
            end)

            -- 初始化奖励列表
            local goodsList = rechargeItem and rechargeItem.goodsList or {}
            for key, value in ipairs(v.rewardList) do
                local goodsItem = goodsList[key] or goods_item.CGoodsItem()
                goodsItem:Init(rewardContent.transform, nil, 0.6)
                goodsItem:SetGoods(nil, value.id, value.num, function()
                    iui_item_detail.Show(value.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, value.num, nil, nil, value.rewardid)
                end)
                goodsList[key] = goodsItem
            end
            rechargeItem.goodsList = goodsList
        end

        self.rechargeList[k] = rechargeItem
    end

    self.btn_DrawActivityCoin.onClick:RemoveAllListeners()
    self.btn_DrawActivityCoin.onClick:AddListener(clickKey)

    self:UpdateRechargeList(rechargeDataArr)
end

-- 刷新礼包列表
function UIView:UpdateRechargeList(rechargeDataArr)
    if not self.rechargeList then return end

    for k, v in ipairs(rechargeDataArr) do
        if not v.isSpecial then
            local rechargeItem = self.rechargeList[k]
            if rechargeItem and rechargeItem.item then
                local item = rechargeItem.item
                local limitText = item:Get("LimitText")
                local canBuyShow = item:Get("CanBuyShow")
                local buyOverText = item:Get("BuyOverText")
                local getBtnGray = item:Get("GetBtnGray")
                local getBtnText = item:Get("GetBtnText")

                if v.canBuyNum > 0 and not v.isFree then
                    limitText.text = string.format2(lang.Get(1001028), v.canBuyNum) -- 还可以购买{%s1}次限购
                end
                getBtnGray:SetEnable(v.hasGet)      -- 已领取按钮置灰
                local color = v.hasGet and "#6E6E6E" or "#3A7222"
                getBtnText.color = color_palette.HexToColor(color)
                UIUtil.SetActive(canBuyShow, v.canBuyNum > 0 and not v.isFree)      -- 不是免费礼包且还可以购买
                UIUtil.SetActive(buyOverText, v.canBuyNum <= 0 and not v.isFree)    -- 购买次数已用完
            end 
        end
    end
end

-- 刷新钥匙数量
function UIView:UpdateBase(keyNum)
    self.txt_DrawActivityCoinsNum.text = keyNum
end


--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, true)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, true)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
