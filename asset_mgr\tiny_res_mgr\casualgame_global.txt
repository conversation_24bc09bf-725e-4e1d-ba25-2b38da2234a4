local print = print
local require = require
local table = table
local string = string

local Path = CS.System.IO.Path
local Application = CS.UnityEngine.Application
local event          = require "event"
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local WWW = CS.UnityEngine.WWW
local DateTime = CS.System.DateTime
local AssetsUpdator = CS.AssetsUpdator

module("casualgame_global")

local http_inst = require "http_inst"
local dkjson = require "dkjson"	
local util = require "util"
local log = require "log"

local LuaSupportMiniGameStr = ""

-- 小游戏事件上报
function ReportMiniGameEvent(minigamename, isstandalong, state,downloadrescount,levelid)
	local event = require "event"
    local reportMsg = 
    {
        mini_game_name = minigamename,
        is_standa_long = isstandalong,
        download_res_count = downloadrescount,
        level_id = levelid and levelid or 0
    }
    --log.Warning("上报 RePortMiniGameEvent 事件")
    event.EventReport("tiny_"..state, reportMsg)
end
function DownLoadStreamAssetsRes(path,timeout)
    print("DownLoadStreamAssetsRes",path)
    local www = WWW(path)
    local l = DateTime.Now
    while www.isDone == false do
        if (DateTime.Now - l).TotalSeconds > timeout then
            log.Warning("DownLoadStreamAssetsRes timeout")
            return nil
        end
    end
    if www.error ~= nil then
        log.Warning("DownLoadStreamAssetsRes www.error",path,www.error)
        return nil
    end
    return www
end
function GetStreamAssetPath(iswww)
    local streamAssetUrl = { Application.streamingAssetsPath, "/AssetBundles/", GetCurPlatformPath(), "/"}
    local platform = nil
    if Application.isEditor and util.IsSimulateHybridCLR() then
        platform = util.GetSimulateHybridCLRTinyPlatformString()
    elseif not Application.isEditor and Application.platform == RuntimePlatform.IPhonePlayer then
        platform = "ios"
    end

    if platform == "ios" and not iswww then
        table.insert(streamAssetUrl, 1, "file://")
    end
    
    return table.concat(streamAssetUrl)
end
function GetCurPlatformPath()
    if Application.isEditor and util.IsSimulateHybridCLR() then
        local platform = util.GetSimulateHybridCLRTinyPlatformString()
        if platform == "android" then
            return "TAndroid"
        elseif platform == "ios" then
            return "TiOS"
        else
            return "TAndroid"
        end
    else
        if Application.platform == RuntimePlatform.Android then
            return "TAndroid"
        elseif Application.platform == RuntimePlatform.IPhonePlayer then
            return "TiOS"
        else
            return "TAndroid"
        end
    end
end
-- function ReadCSVString(csvString)
--     print("csvString"..csvString)
--     -- 保存 CSV 数据的表
--     local data = {}
--     -- 逐行解析 CSV 数据
--     for line in csvString:gmatch("[^\n]+") do
--         local fields = {}
--         for value in line:gmatch("[^,]+") do
--             table.insert(fields, value)
--         end
--         table.insert(data, fields)
--     end
--     return data
-- end


-- --独立热更supportgame.csv中XMan的支持是第几列
-- local supportGame_XMan_Oversea = 1

-- function CheckSupportGameCSVIsContainReskey(minigamereskey,csvstring)
--     local csvdata = ReadCSVString(csvstring)
--     for i = 1, #csvdata, 1 do
--         for j = 1, #csvdata[i], 1 do
--             if csvdata[i][1] == minigamereskey and csvdata[i][supportGame_XMan_Oversea] == "1" then
--                 print("minigamereskey is ok")
--                 return true
--             end
--         end
--     end
--     print("minigamereskey is not ok")
--     return false
-- end
function CheckReskeyIsSupportStandAlong(minigamereskey) --判断reskey 是否支持独立热更
    local SupportMiniGameStr = AssetsUpdator.SupportMiniGameStr
    if (SupportMiniGameStr == nil or SupportMiniGameStr == "") and LuaSupportMiniGameStr == "" then--读本地
        local supportgameFilePath = Path.Combine(GetStreamAssetPath(), "supportgame.csv")
        print("local supportgameFilePath==",supportgameFilePath)
        local www = DownLoadStreamAssetsRes(supportgameFilePath,1)
        if www then
            print("read local SupportMiniGameCSV==",www.text)
            return CheckSupportGameCSVIsContainReskey(minigamereskey,www.text)
        else
            print("read local SupportMiniGameCSV  fail")
            return false
        end
    else
        print("read Server SupportMiniGameCSV==",SupportMiniGameStr)
        print("read Server LuaSupportMiniGameStr==",LuaSupportMiniGameStr)
        if LuaSupportMiniGameStr ~= "" then
            return CheckSupportGameCSVIsContainReskey(minigamereskey,LuaSupportMiniGameStr)
        else
            return CheckSupportGameCSVIsContainReskey(minigamereskey,SupportMiniGameStr)
        end
    end
end
-- 下载独立热更小游戏supportgame上报
function ReportMiniGameSupportgameEvent(supportgameUrl, isSucceed)
	local event = require "event"
    local reportMsg = 
    {
        supportgame_Url = supportgameUrl,
        is_Succeess = isSucceed,
    }
    --log.Warning("上报 RePortMiniGameEvent 事件")
    event.EventReport("tiny_supportgame", reportMsg)
end
-- function DownLoadMiniGameSupportStandAlongGame(callBack) --下载独立热更小游戏各个项目支持的独立热更小游戏
--     local files_version_mgr = require "files_version_mgr"
--     local reskey = files_version_mgr.GetFinalTargetResKey()
--     if (string.find(reskey, "Combine") == 1) then
--         LuaSupportMiniGameStr = ""
--         local supportgameUrl = ""
--         if  Application.isEditor then
--             supportgameUrl = "http://172.16.126.185:3122/CasualGame_Pure_Res_105_002/TinyRes/" .."supportgame.csv"
--             log.Warning("Editor supportgame.csv", supportgameUrl)
--         else
--             local url = files_version_mgr.GetStandAloneUrl()
--             if url  == nil or url == "" then
--                 log.Warning("jenkens and server not set p_stand_alone_server_root_url")
--             else
--                 supportgameUrl = url .."supportgame.csv"
--                 log.Warning("finaly supportgameUrl==",supportgameUrl)
--             end
--         end
--         if supportgameUrl ~= "" then
--             http_inst.Req_Timeout(supportgameUrl,2,function ( str, hasError, bytes )
--                 if hasError then
--                     --updateJsonUrl = oldUpdateJsonMap.resource_url .. "/"..GetUpdateFileNameByApkUpdateJson()
--                     log.Warning("====== supportgameUrl error ", supportgameUrl,hasError)
--                     ReportMiniGameSupportgameEvent(supportgameUrl,false)
--                 else
--                     LuaSupportMiniGameStr = str
--                     ReportMiniGameSupportgameEvent(supportgameUrl,true)
--                 end
--                 if callBack then
--                     callBack()
--                 end
--             end)
--         else
--             log.Warning("supportgameUrl is empty")
--             if callBack then
--                 callBack()
--             end
--         end
--     else
--         if callBack then
--             callBack()
--         end
--     end
-- end