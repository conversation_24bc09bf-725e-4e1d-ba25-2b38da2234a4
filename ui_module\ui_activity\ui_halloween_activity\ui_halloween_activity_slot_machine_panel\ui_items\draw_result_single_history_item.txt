local string = string
local tostring = tostring

local reward_mgr = require "reward_mgr"

local game_scheme = require("game_scheme")
local lang = require "lang"
local halloween_activity_slot_machine_const = require "halloween_activity_slot_machine_const"
local halloween_activity_slot_machine_rate_data = require "halloween_activity_slot_machine_rate_data"

local time_util = require "time_util"

local draw_result_single_history_item = {}

---@public 显示一个抽奖结果历史item
---@param rtf_item transform transform
---@param data table 数据
function draw_result_single_history_item.Render(rtf_item, cfg, act_id, data)
    local result_item = rtf_item:Find("draw_result_item")

    local result_id = data.result_id
    local rate = halloween_activity_slot_machine_rate_data.rate_dist[result_id]
    local count = data.count

    local draw_result_item = require "draw_result_item"
    draw_result_item.Render(result_item, cfg, act_id, rate, count)

    local txt_title = rtf_item:Find("txt_title"):GetComponent("Text")
    local txt_time = rtf_item:Find("txt_time"):GetComponent("Text")

    if count > 1 then
        txt_title.text = lang.Get(halloween_activity_slot_machine_const.lang_key.times_title_1)
    else
        txt_title.text = lang.Get(halloween_activity_slot_machine_const.lang_key.times_title_5)
    end

    local server_date = time_util.GetLocalDate(data.time_stamp or 0)
    local server_date_string = string.format("%s-%s %02d:%02d:%02d", server_date.month, server_date.day, server_date.hour, server_date.min, server_date.sec)
    txt_time.text = server_date_string
end



return draw_result_single_history_item