-- local require = require
-- local pairs = pairs

-- local Application = CS.UnityEngine.Application
-- local RuntimePlatform = CS.UnityEngine.RuntimePlatform
-- local PlayerPrefs = CS.UnityEngine.PlayerPrefs

-- module("casualgame_replaced_reskey")

-- local http_inst = require "http_inst"
-- local dkjson = require "dkjson"	
-- local util = require "util"
-- local log = require "log"

-- local reskeyList =
-- {
--     open = true,
--     reskey =
--     {
--         SaveDogLife =
--         {
--             "RiseUp",
--             "HumanCannon",
--             "ModeControlAnt",
--             "FlexyRing",
--             "Aquapark",
--             "SameColorRunner",
--             "HideSeek",
--             "DeliverIt3D",
--             "ThiefPuzzle",
--             "ZenMatch",
--             "CityDefence",
--             "FlexCamo",
--             "Downthehole",
--             "LineDrawingCar",
--             "StrongestFish",
--             "DrawLineMotorcycle",
--             "LineChallenge",
--             "BoardingThatShip",
--             "JewelSancient",
--             "Fishdom",
--             "BattleDisc",
--             "CubesControl",
--             "BeansMaster",
--             "SnowRace",
--             "TrafficCars",
--             "SaveTheFish",
--             "Weigher",
--             "PuffUp",
--             "ShotFactor",
--             "RopeSavior3D",
--             "CarBattle",
--             "JoyErase",
--             "BuildingBlocks",
--             "LZDtz",
--             "ZDefence",
--             "DrawMaster",
--             "DLYHuoChaiRen",
--             "DeliverIt3D",
--             "HappyRunning",
--             "aquapark",
--             "GravelJet",
--             "ZYSheJi",
--             "DrawLinesToEscape",
--             "FindFaultTogether",
--             "CharacterLline"
--         }
--     }
-- }

-- function ReplaceReskeyToStandAloneReskey() 
--     local files_version_mgr = require "files_version_mgr"
--     local reskey = files_version_mgr.GetResKey()
--     local p_stand_alone_res_key = files_version_mgr.GetStandAloneTinyRes()
--     local collection_res_key = files_version_mgr.ApkUpdateConfigTryGetValue("collection_res_key")
--     if  Application.isEditor or Application.platform == RuntimePlatform.WebGLPlayer then
--     elseif reskey ~= nil and reskey ~= "" and  (p_stand_alone_res_key == nil or p_stand_alone_res_key == "") and (collection_res_key == nil or collection_res_key == "") then
--        if reskeyList.open == true then
--             for k, v in pairs(reskeyList.reskey) do
--                 for i = 1, #v, 1 do
--                     if reskey == v[i] then
--                         log.Warning("Reskey2Standalong==",k)
--                         PlayerPrefs.SetString("LocalStandAloneReskey",k)
--                         return
--                     end
--                 end
--             end
--        else
--            log.Warning("reskeyList.open is false")
--        end
--     end
-- end