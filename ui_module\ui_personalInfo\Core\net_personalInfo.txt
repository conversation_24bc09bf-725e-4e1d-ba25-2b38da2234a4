---
--- Created by: yuan<PERSON>.
--- DateTime: 2024/8/29.
--- Desc: 个性化相关的网络请求模块
---

local print = print
local require = require
local tonumber = tonumber
local tostring = tostring
local type = type

local error_code_pb = require "error_code_pb"
local msg_pb = require "msg_pb"
local role_pb = require "role_pb"
local frame_pb = require "frame_pb"
local roleplate_pb = require "roleplate_pb"
local title_pb = require "title_pb"
local prop_pb = require "prop_pb"
local package_pb = require "package_pb"
local DroneCenter_pb = require "DroneCenter_pb"
local GWG = GWG

local net = require "net"
local net_route = require "net_route"
local xManMsg_pb = require "xManMsg_pb"

local data_personalInfo = require "data_personalInfo"
local const_personalInfo = require "const_personalInfo"
local event_personalInfo = require "event_personalInfo"
local game_scheme = require "game_scheme"

local flow_text = require "flow_text"
local lang = require "lang"
local event = require "event"
local util = require "util"
local red_const = require("red_const")
local red_system = require "red_system"
local os = os

module("net_personalInfo")

local function OnErrorCode(enErr)
    flow_text.Clear()
    flow_text.Add(util.GetErrorLangText(enErr))
end

---[请求修改名字]
function MSG_ZONE_ROLE_UPDATE_NAME_REQ(name)
    flow_text.Clear()
    if not name or name == "" then
        flow_text.Add(lang.Get(const_personalInfo.lang_Name_Null))
        return
    end

    local roleNameCD = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleNameCD)
    if roleNameCD and roleNameCD > os.server_time() then
        flow_text.Add(lang.Get(const_personalInfo.lang_Cooling))
        return
    end

    if data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleName) == name then
        flow_text.Add(lang.Get(const_personalInfo.lang_RepeatName))
        return
    end

    local mgr_personalInfo = require "mgr_personalInfo"
    local player_mgr = require "player_mgr"
    local item_data = require "item_data"
    local goodsCount = player_mgr.GetGoodsCountByType(item_data.Item_Type_Enum.ChangeNameCard)
    if goodsCount == 0 and mgr_personalInfo.GetChangeNameDiamonds() > player_mgr.GetPlayerAllDiamond() then
        flow_text.Add(lang.Get(const_personalInfo.lang_DiamondNotEnough))
        return
    end

    if util.GetStringByteLen(name) < const_personalInfo.value_NameMinLimit then
        flow_text.Add(lang.Get(const_personalInfo.lang_Name_Short))
        return
    end

    if util.GetStringByteLen(name) > const_personalInfo.value_NameMaxLimit then
        flow_text.Add(lang.Get(const_personalInfo.lang_Name_Long))
        return
    end

    local msg = role_pb.TMSG_ZONE_ROLE_UPDATE_NAME_REQ()
    msg.name = name
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_ZONE_ROLE_UPDATE_NAME_REQ, msg)
end

function MSG_ZONE_ROLE_UPDATE_NAME_RSP(msg)
    if msg and msg.err == error_code_pb.enErr_NoError then
        flow_text.Add(lang.Get(const_personalInfo.lang_ModifiedSuccess))

        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleName, msg.name)
        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleNameFree, false)
        
        if msg.allowmodifytime and msg.allowmodifytime > 0 then
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleNameCD, msg.allowmodifytime)
        end
        event.EventReport("PlayerInfo_ChangeName", {})
    else
        OnErrorCode(msg.err)
    end
end

---[请求修改性别 0:未知 1:男 2：女 ]
function MSG_ZONE_ROLE_UPDATE_SEX_REQ(sexType)
    if not sexType then
        return
    end

    flow_text.Clear()
    local roleSexCD = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleSexCD)
    if roleSexCD and roleSexCD > os.server_time() then
        flow_text.Add(lang.Get(const_personalInfo.lang_Cooling))
        return
    end

    if sexType == data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleSex) then
        flow_text.Add(lang.Get(const_personalInfo.lang_SexEqual))
        return
    end

    local msg = role_pb.TMSG_ZONE_ROLE_UPDATE_SEX_REQ()
    msg.sexType = sexType
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_ZONE_ROLE_UPDATE_SEX_REQ, msg)
end

function MSG_ZONE_ROLE_UPDATE_SEX_RSP(msg)
    if msg and msg.errorcode == error_code_pb.enErr_NoError then
        flow_text.Add(lang.Get(const_personalInfo.lang_ModifiedSuccess))

        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleSex, msg.finlsexType)
        if msg.allowmodifytime and msg.allowmodifytime > 0 then
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleSexCD, msg.allowmodifytime)
        end
        event.EventReport("PlayerInfo_SetGender", {})
    else
        OnErrorCode(msg.errorcode)
    end
end

---[修改头像]
local cacheFaceID = nil
function MSG_ZONE_ROLE_FACE_UPDATE_REQ(faceID)
    if not faceID or type(faceID) ~= "number" or data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FaceID) == faceID then
        return
    end

    cacheFaceID = faceID
    local msg = role_pb.TMSG_ZONE_ROLE_FACE_UPDATE_REQ()
    msg.faceID = faceID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_ZONE_ROLE_FACE_UPDATE_REQ, msg)
end

function MSG_ZONE_ROLE_FACE_UPDATE_RSP(msg)
    if msg and msg.err == error_code_pb.enErr_NoError then
        --flow_text.Add(lang.Get(const_personalInfo.lang_ModifiedSuccessfully))
        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.FaceID, cacheFaceID)
    else
        OnErrorCode(msg.err)
    end
end

---[修改铭牌 nowID：当前Id，goalID:需要替换的Id]
function MSG_ZONE_ROLE_PLATE_UPDATE_REQ(goalID)
    local nowId = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.NamePlateID)
    if not nowId or not goalID or nowId == goalID then
        return
    end

    local msg = roleplate_pb.TMSG_ZONE_ROLE_PLATE_UPDATE_REQ()
    msg.id = goalID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_ZONE_ROLE_PLATE_UPDATE_REQ, msg)
end

---[修改铭牌 消息返回
function MSG_ZONE_ROLE_PLATE_UPDATE_RSP(msg)
    if msg and ((not msg.errorcode) or msg.errorcode == error_code_pb.enErr_NoError) then
        --flow_text.Add(lang.Get(const_personalInfo.lang_ModifiedSuccessfully))
        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.NamePlateID, msg.id)
    else
        OnErrorCode(msg.errorcode)
    end
end

---[修改城堡 nowID：当前城堡Id，goalID:需要替换的城堡Id]
function MSG_ZONE_ROLE_CITY_UPDATE_REQ(goalID)
    local nowId = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.CityID)
    if not nowId or not goalID or nowId == goalID then
        return
    end

    local msg = role_pb.TMSG_ZONE_ROLE_CITY_UPDATE_REQ()
    msg.nowDecoration = nowId
    msg.goalDecoration = goalID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_ZONE_ROLE_CITY_UPDATE_REQ, msg)
end

function MSG_ZONE_ROLE_CITY_UPDATE_RSP(msg)
    if msg and msg.errorcode == error_code_pb.enErr_NoError then
        --flow_text.Add(lang.Get(const_personalInfo.lang_ModifiedSuccessfully))
        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.CityID, msg.finlDecoration)
    else
        OnErrorCode(msg.errorcode)
    end
end

---[修改头像框]
function MSG_FRAME_SELECT_REQ(frameId)
    if not frameId or data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FrameID) == frameId then
        return
    end

    local msg = frame_pb.TMSG_FRAME_SELECT_REQ()
    msg.id = frameId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_FRAME_SELECT_REQ, msg)
end

function MSG_FRAME_SELECT_RSP(msg)
    if msg and msg.errorcode == error_code_pb.enErr_NoError then
        --flow_text.Add(lang.Get(const_personalInfo.lang_ModifiedSuccessfully))
        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.FrameID, msg.id)
    else
        OnErrorCode(msg.errorcode)
    end
end

---[修改称号]
function MSG_TITLE_SELECT_REQ(titleId)
    if not titleId or data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.TitleID) == titleId then
        return
    end

    local msg = title_pb.TMSG_TITLE_SELECT_REQ()
    msg.id = titleId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_TITLE_SELECT_REQ, msg)
end

function MSG_TITLE_SELECT_RSP(msg)
    if msg and msg.errorcode == error_code_pb.enErr_NoError then
        --flow_text.Add(lang.Get(const_personalInfo.lang_ModifiedSuccessfully))
        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.TitleID, msg.id)
    else
        OnErrorCode(msg.errorcode)
    end
end

---@public 请求城堡特效 ---[ nowID：当前城堡特效Id，goalID:需要替换的城堡特效Id]
function MSG_ZONE_ROLE_CITY_EFFECT_UPDATE_REQ(goalID)
    local nowId = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.EffectID)
    if not nowId or not goalID or nowId == goalID then
        return
    end

    local msg = role_pb.TMSG_ZONE_ROLE_CITY_EFFECT_UPDATE_REQ()
    msg.noweffectid = nowId
    msg.goaleffectid = goalID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_ZONE_ROLE_CITY_EFFECT_UPDATE_REQ, msg)
end

function MSG_ZONE_ROLE_CITY_EFFECT_UPDATE_RSP(msg)
    if msg and msg.errorcode == error_code_pb.enErr_NoError then
        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.EffectID, msg.finaleffectID)
    else
        OnErrorCode(msg.errorcode)
    end
end

---[点赞请求] 该接口已迁移至net_click_liking
-- function MSG_ZONE_ROLE_PRAISE_UPDATE_REQ(playId,likeSysType)
--     if not playId then
--         return
--     end

--     flow_text.Clear()
--     local praiseMaxCount = tonumber(game_scheme:InitBattleProp_0(const_personalInfo.cfgID_praiseCount).szParam.data[0])
--     if data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RolePraiseCount) >= praiseMaxCount then
--         flow_text.Add(lang.Get(const_personalInfo.lang_PraiseLimit))
--         return
--     end

--     local msg = role_pb.TMSG_ZONE_ROLE_PRAISE_UPDATE_REQ()
--     msg.playId = playId
--     msg.nNums = 1
--     if likeSysType then
--         msg.nLikeSysType = likeSysType
--     end
--     net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_ZONE_ROLE_PRAISE_UPDATE_REQ, msg)
-- end

-- function MSG_ZONE_ROLE_PRAISE_UPDATE_RSP(msg)
--     if msg and msg.errorcode == error_code_pb.enErr_NoError then
--         local rolePraiseCount = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RolePraiseCount)
--         data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RolePraiseCount, rolePraiseCount + msg.nNums)
--         -- 点赞成功消息
--         event.Trigger(event_personalInfo.ROLE_PRAISE_UPDATE_MSG, msg)
--     else
--         OnErrorCode(msg.errorcode)
--     end
-- end

---[请求查看个人信息] 可以是自己也可以是别人
function TMSG_ZONE_NEW_ROLEINFO_REQ(roleId)
    if not roleId then
        return
    end

    local msg = role_pb.TMSG_ZONE_NEW_ROLEINFO_REQ()
    msg.roleId = roleId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_ZONE_NEW_ROLEINFO_REQ, msg)
    -- 用于国旗，在登陆时给服务器发送 包名、CountryID
    require("net_national_flag_module").MSG_LOGIN_NATIONAL_FLAG_REQ()
end

function TMSG_ZONE_NEW_ROLEINFO_RSP(msg)
    if msg and msg.errorcode == error_code_pb.enErr_NoError then
        local mgr_personalInfo = require "mgr_personalInfo"
        mgr_personalInfo.UpdateRoleInfo(msg)
        event.Trigger(event_personalInfo.PLAYER_INFO_SUCCESS, msg)
        -- 国旗设置信息用
        require("national_flag_mgr").SetNationalFlagInfoByServer(msg.roleInfo)
    else
        OnErrorCode(msg.errorcode)
        event.Trigger(event_personalInfo.PLAYER_INFO_FAIL)
    end
end
---[请求切换无人机装饰]
function MSG_DRONECENTER_ADORN_REQ(adorneID, decorationAdorneID)
    if not adorneID or not decorationAdorneID then
        return
    end
    local msg = DroneCenter_pb.TMSG_DRONECENTER_ADORN_REQ()
    msg.nDroneId = adorneID
    msg.adornId = decorationAdorneID
    print("MSG_DRONECENTER_ADORN_REQ", msg.adornId)
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_DRONECENTER_ADORN_REQ, msg)
end
--[响应切换无人机装饰]
function MSG_DRONECENTER_ADORN_RSP(msg)
    if msg and msg.errorCode == 0 then
        print("MSG_DRONECENTER_ADORN_RSP", msg.adornId)
        GWG.GWHomeMgr.droneData.SetCurAdroneSkin(msg.adornId)
        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.AnimalsID, msg.adornId)
        event.Trigger(event.UPDATE_MAGIC_WEAPON_ITEM)
        event.Trigger(event.UPDATE_MAGIC_WEAPON)
        event.Trigger(event.REFRESH_SHOW_MODEL)
    else
        OnErrorCode(msg.errorCode)
    end
end

---[服务器通知]
function MSG_PROP_CREATEENTITY_NTF(msg)
    if msg then
        local mgr_personalInfo = require "mgr_personalInfo"
        mgr_personalInfo.InitPersonalInfo(msg)
        -- 登录初始化个人信息数据
        event.Trigger(event_personalInfo.Init_PERSONAL_INFO)
    end
end

---@protected 新铭牌通知
function MSG_ZONE_ROLE_PLATE_NTF(msg)
    if msg then
        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.NamePlateID, msg.id)
    end
end

function MSG_PROP_UPDATE_NTF(msg)
    if msg then
        local mgr_personalInfo = require "mgr_personalInfo"
        mgr_personalInfo.UpdatePersonalInfo(msg)

        --个性化装扮刷新红点
        red_system.TriggerRed(red_const.Enum.Personalised)

        --个性化装扮刷新列表显示,延迟1秒，保证数据更新完成
        util.DelayCall(0.5, function()
            event.Trigger(event_personalInfo.UPDATE_CACHE_GOODS)
        end)
    end
end

function MSG_PROP_UPDATE_TOPICS_NTF(msg)
    if msg then
        local mgr_personalInfo = require "mgr_personalInfo"
        mgr_personalInfo.UpdatePersonalTopicInfo(msg)
    end
end
--服务器下发道具到背包时触发自动使用逻辑
function MSG_GOODS_GET_REASON_NTF(msg)
    local helper_personalInfo = require "helper_personalInfo"
    if msg and msg.itemid then
        local cfg = helper_personalInfo.GetPersonalisedItemConfig(msg.itemid)
        if cfg then
            if msg.num > 0 and cfg.automaticEquipment == 1 then
                -- 获得自动使用的装扮，直接向服务器发送切换请求
                if cfg.propId == data_personalInfo.PropEnum.FaceID then
                    MSG_ZONE_ROLE_FACE_UPDATE_REQ(msg.itemid)
                elseif cfg.propId == data_personalInfo.PropEnum.CityID then
                    MSG_ZONE_ROLE_CITY_UPDATE_REQ(msg.itemid)
                elseif cfg.propId == data_personalInfo.PropEnum.FrameID then
                    MSG_FRAME_SELECT_REQ(msg.itemid)
                elseif cfg.propId == data_personalInfo.PropEnum.TitleID then
                    MSG_TITLE_SELECT_REQ(msg.itemid)
                elseif cfg.propId == data_personalInfo.PropEnum.AnimalsID then
                    MSG_DRONECENTER_ADORN_REQ(cfg.adornID, msg.itemid)
                    --数数打点
                    event.Trigger(event.GAME_EVENT_REPORT, "AccessoryAdorn_Use", { adornID = msg.itemid, use_type = 1 })
                    event.Trigger(event.GAME_EVENT_REPORT, "AccessoryAdorn_Activate", { adornID = msg.itemid })
                elseif cfg.propId == data_personalInfo.PropEnum.EffectID then
                    MSG_ZONE_ROLE_CITY_EFFECT_UPDATE_REQ(msg.itemid)
                end
            end

            --个性化装扮刷新红点
            red_system.TriggerRed(red_const.Enum.Personalised)

            --个性化装扮刷新列表显示,延迟1秒，保证数据更新完成
            util.DelayCall(0.5, function()
                event.Trigger(event_personalInfo.UPDATE_CACHE_GOODS)
            end)
        end
    end
end

function MSG_ZONE_ROLE_NEW_FACE_NTF(msg)
    if msg then
        --data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.FaceUnlockList, msg.faceID)
    end
end

function MSG_ROLE_CUSTOM_TITLE_NTF(msg)
    if msg then
        local titles = msg.titles
        for i = 1, #titles do
            -- data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.TitleUnlockList, titles[i].titleid)
            -- todo 处理定制协议
        end
    end
end

--下发默认装扮道具接口
function MSG_DRONECENTER_ADORN_NTF(msg)
    if msg then
        local adorneID = msg.nDroneId
        local decorationAdorneID = msg.adornId
        --功能开启时服务器下发默认无人机装饰（皮肤）
        GWG.GWHomeMgr.droneData.SetCurAdroneSkin(decorationAdorneID)
        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.AnimalsID, decorationAdorneID)
        event.Trigger(event.UPDATE_MAGIC_WEAPON_ITEM)
        event.Trigger(event.UPDATE_MAGIC_WEAPON)
        event.Trigger(event.REFRESH_SHOW_MODEL)
        --数数打点
        event.Trigger(event.GAME_EVENT_REPORT, "AccessoryAdorn_Use", { adornID = decorationAdorneID, use_type = 1 })
        event.Trigger(event.GAME_EVENT_REPORT, "AccessoryAdorn_Activate", { adornID = decorationAdorneID })
    end
end

--上传自定义头像请求
function MSG_CUSTOM_FACE_UPLOAD_REQ(pos)
    print("自定义头像 MSG_CUSTOM_FACE_UPLOAD_REQ SUCCESS")
    local msg = role_pb.TMSG_CUSTOM_FACE_UPLOAD_REQ()
    msg.pos = pos
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_CUSTOM_FACE_UPLOAD_REQ, msg)
end

function MSG_CUSTOM_FACE_UPLOAD_RSP(msg)
    if msg.errorCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorCode))
    else
        -- Success Handling
        print("自定义头像 MSG_CUSTOM_FACE_UPLOAD_RSP SUCCESS")
        event.Trigger(event_personalInfo.UPLOAD_CUSTOM_AVATAR_REQUEST, msg)
    end
end

--自定义头像审核结果通知
function MSG_CUSTOM_FACE_VERIFY_NTF(msg)
    if msg then
        print("自定义头像 MSG_CUSTOM_FACE_VERIFY_NTF SUCCESS")
        local custom_avatar_data = require "custom_avatar_data"
        local faceDatas = msg.datas
        local listCount = faceDatas and #faceDatas or 0
        for i = 1, listCount do
            custom_avatar_data.UpdateAvatarVerify(faceDatas[i].imageId, faceDatas[i].status, faceDatas[i].used)
        end
        event.Trigger(event_personalInfo.CUSTOM_AVATAR_VERIFY)
    end
end

--自定义头像是否激活
function MSG_CUSTOM_FACE_ACTIVATE_NTF(msg)
    if msg then
        print("自定义头像 MSG_CUSTOM_FACE_ACTIVATE_NTF SUCCESS")
        event.Trigger(event_personalInfo.CUSTOM_AVATAR_ACTIVE, msg)
        event.EventReport("CustomizeHead_Unlock", { }) --自定义头像解锁 上报
    end
end

--上传自定义头像结果上报
function MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ(imageId, curl, pos)
    local msg = role_pb.TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ()
    msg.imageId = imageId
    msg.curl = curl
    msg.pos = pos
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ, msg)
end

function MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP(msg)
    if msg.errorCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorCode))
    else
        -- 返回 TCustomFaceData
        print("自定义头像 MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP SUCCESS")
        event.Trigger(event_personalInfo.UPLOAD_CUSTOM_AVATAR_RESULT_REPORT, msg)
        flow_text.Add(lang.Get(650083))
    end
end

--自定义头像使用
function MSG_CUSTOM_FACE_USE_REQ(imageId, pos)
    print("自定义头像 MSG_CUSTOM_FACE_USE_REQ , imageId = ", imageId)
    local msg = role_pb.TMSG_CUSTOM_FACE_USE_REQ()
    msg.imageId = imageId
    msg.pos = pos
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_CUSTOM_FACE_USE_REQ, msg)
    if imageId then
        local custom_avatar_data = require "custom_avatar_data"
        local isSystemClick = custom_avatar_data.GetAvatarIsSystemClick()
        --如果isSystemClick是true 传0 false传1
        local reportVal = isSystemClick and 0 or 1
        event.EventReport("CustomizeHead_Use", { Use_Type = reportVal }) --使用自定义头像 上报
    end
end

function MSG_CUSTOM_FACE_USE_RSP(msg)
    if msg.errorCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorCode))
    else
        print("自定义头像 MSG_CUSTOM_FACE_USE_RSP , imageId = ", msg.data.imageId)
        event.Trigger(event_personalInfo.CUSTOM_AVATAR_USE, msg)
        --event.Trigger(event_personalInfo.UPDATE_PERSONALISED_INFO, msg)
    end
end

--自定义头像删除
function MSG_CUSTOM_FACE_REMOVE_REQ(imageId, pos)
    local msg = role_pb.TMSG_CUSTOM_FACE_REMOVE_REQ()
    msg.imageId = imageId
    msg.pos = pos
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_CUSTOM_FACE_REMOVE_REQ, msg)
end

function MSG_CUSTOM_FACE_REMOVE_RSP(msg)
    if msg.errorCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorCode))
    else
        --flow_text.Add(lang.Get(650090))
        print("自定义头像 MSG_CUSTOM_FACE_REMOVE_RSP SUCCESS")
        local custom_avatar_data = require "custom_avatar_data"
        custom_avatar_data.RemoveAvatar(msg.imageId)
        --event.Trigger(event_personalInfo.UPDATE_CUSTOM_AVATAR_STATUS)
        event.Trigger(event_personalInfo.CUSTOM_AVATAR_REMOVE, msg)
    end
end

-- --点赞记录请求 该接口已迁移至net_click_liking
-- function MSG_LIKE_GET_RECORD_REQ(type)
--     local msg = role_pb.TMSG_LIKE_GET_RECORD_REQ()
--     msg.nType = type
--     net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_LIKE_GET_RECORD_REQ, msg)
-- end

-- --点赞记录回复 该接口已迁移至net_click_liking
-- function MSG_LIKE_GET_RECORD_RSP(msg)
--     if msg.errorcode and msg.errorcode~=0 then 
--         flow_text.Add(lang.Get(100000 + msg.errorcode))
--     else
--         local click_liking_data = require "click_liking_data"
--         click_liking_data.InitDataFromServer(msg)
--     end
-- end

local MessageTable = {
    --- 修改名字
    { msg_pb.MSG_ZONE_ROLE_UPDATE_NAME_RSP, MSG_ZONE_ROLE_UPDATE_NAME_RSP, role_pb.TMSG_ZONE_ROLE_UPDATE_NAME_RSP },
    --- 修改性别
    { msg_pb.MSG_ZONE_ROLE_UPDATE_SEX_RSP, MSG_ZONE_ROLE_UPDATE_SEX_RSP, role_pb.TMSG_ZONE_ROLE_UPDATE_SEX_RSP, },
    --- 修改头像
    { msg_pb.MSG_ZONE_ROLE_FACE_UPDATE_RSP, MSG_ZONE_ROLE_FACE_UPDATE_RSP, role_pb.TMSG_ZONE_ROLE_FACE_UPDATE_RSP, },
    --- 城堡相关
    { msg_pb.MSG_ZONE_ROLE_CITY_UPDATE_RSP, MSG_ZONE_ROLE_CITY_UPDATE_RSP, role_pb.TMSG_ZONE_ROLE_CITY_UPDATE_RSP, },
    --- 铭牌相关
    { msg_pb.MSG_ZONE_ROLE_PLATE_UPDATE_RSP, MSG_ZONE_ROLE_PLATE_UPDATE_RSP, roleplate_pb.TMSG_ZONE_ROLE_PLATE_UPDATE_RSP, },
    --- 铭牌通知
    { msg_pb.MSG_ZONE_ROLE_PLATE_NTF, MSG_ZONE_ROLE_PLATE_NTF, roleplate_pb.TMSG_ZONE_ROLE_PLATE_NTF, },
    --- 修改头像框
    { msg_pb.MSG_FRAME_SELECT_RSP, MSG_FRAME_SELECT_RSP, frame_pb.TMSG_FRAME_SELECT_RSP, },
    --- 修改称号
    { msg_pb.MSG_TITLE_SELECT_RSP, MSG_TITLE_SELECT_RSP, title_pb.TMSG_TITLE_SELECT_RSP, },
    --- 查看他人信息
    { msg_pb.MSG_ZONE_NEW_ROLEINFO_RSP, TMSG_ZONE_NEW_ROLEINFO_RSP, role_pb.TMSG_ZONE_NEW_ROLEINFO_RSP },
    ---点赞 该接口已迁移至net_click_liking
    -- { msg_pb.MSG_ZONE_ROLE_PRAISE_UPDATE_RSP, MSG_ZONE_ROLE_PRAISE_UPDATE_RSP, role_pb.TMSG_ZONE_ROLE_PRAISE_UPDATE_RSP, },

    --- [消息通知]
    --- 创建玩家实体通知
    { msg_pb.MSG_PROP_CREATEENTITY_NTF, MSG_PROP_CREATEENTITY_NTF, prop_pb.TMSG_PROP_CREATEENTITY_NTF, },
    --- 更新实体属性
    { msg_pb.MSG_PROP_UPDATE_NTF, MSG_PROP_UPDATE_NTF, prop_pb.TMSG_PROP_UPDATE_NTF, },
    --- 更新任务数据
    { msg_pb.MSG_PROP_UPDATE_TOPICS_NTF, MSG_PROP_UPDATE_TOPICS_NTF, prop_pb.TMSG_PROP_UPDATE_TOPICS_NTF },
    --- 获得新的头像
    { msg_pb.MSG_ZONE_ROLE_NEW_FACE_NTF, MSG_ZONE_ROLE_NEW_FACE_NTF, role_pb.TMSG_ZONE_ROLE_NEW_FACE_NTF, },
    --- 道具获取或消耗上报
    { msg_pb.MSG_GOODS_GET_REASON_NTF, MSG_GOODS_GET_REASON_NTF, package_pb.TMSG_GOODS_GET_REASON_NTF },

    --无人机装扮（皮肤）
    { xManMsg_pb.MSG_DRONECENTER_ADORN_RSP, MSG_DRONECENTER_ADORN_RSP, DroneCenter_pb.TMSG_DRONECENTER_ADORN_RSP }, -- 切换无人机装饰
    { xManMsg_pb.MSG_DRONECENTER_ADORN_NTF, MSG_DRONECENTER_ADORN_NTF, DroneCenter_pb.TMSG_DRONECENTER_ADORN_NTF }, --功能开启时下发默认无人机装饰（皮肤）通知

    --- todo 下面两个不知道是干啥的
    { msg_pb.MSG_ROLE_RESOURCE_STAT_NTF, MSG_ZONE_ROLE_NEW_FACE_NTF, role_pb.TMSG_ROLE_RESOURCE_STAT_NTF },
    { msg_pb.MSG_ROLE_CUSTOM_TITLE_NTF, MSG_ROLE_CUSTOM_TITLE_NTF, prop_pb.TCustomTitleData },

    --- 请求自定义头像上传
    { msg_pb.MSG_CUSTOM_FACE_UPLOAD_RSP, MSG_CUSTOM_FACE_UPLOAD_RSP, role_pb.TMSG_CUSTOM_FACE_UPLOAD_RSP, },
    -- 上传自定义头像结果上报
    { msg_pb.MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP, MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP, role_pb.TMSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP, },
    -- 自定义头像审核结果通知
    { msg_pb.MSG_CUSTOM_FACE_VERIFY_NTF, MSG_CUSTOM_FACE_VERIFY_NTF, role_pb.TMSG_CUSTOM_FACE_VERIFY_NTF },
    -- 自定义头像是否激活
    { msg_pb.MSG_CUSTOM_FACE_ACTIVATE_NTF, MSG_CUSTOM_FACE_ACTIVATE_NTF, role_pb.TMSG_CUSTOM_FACE_ACTIVATE_NTF },
    -- 自定义头像使用
    { msg_pb.MSG_CUSTOM_FACE_USE_RSP, MSG_CUSTOM_FACE_USE_RSP, role_pb.TMSG_CUSTOM_FACE_USE_RSP },
    -- 自定义头像删除
    { msg_pb.MSG_CUSTOM_FACE_REMOVE_RSP, MSG_CUSTOM_FACE_REMOVE_RSP, role_pb.TMSG_CUSTOM_FACE_REMOVE_RSP },
    
    -- 城堡效果
    { msg_pb.MSG_ZONE_ROLE_CITY_EFFECT_UPDATE_RSP, MSG_ZONE_ROLE_CITY_EFFECT_UPDATE_RSP, role_pb.TMSG_ZONE_ROLE_CITY_EFFECT_UPDATE_RSP },

    --点赞记录 该接口已迁移至net_click_liking
    -- { xManMsg_pb.MSG_LIKE_GET_RECORD_RSP, MSG_LIKE_GET_RECORD_RSP, role_pb.TMSG_LIKE_GET_RECORD_RSP },
}

net_route.RegisterMsgHandlers(MessageTable)