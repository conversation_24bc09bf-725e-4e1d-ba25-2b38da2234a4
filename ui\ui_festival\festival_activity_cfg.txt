-- -----------------------------------------------
-- description:  活动配置模块
-- author:       陈皮皮
-- -----------------------------------------------

local require = require
local tostring = tostring
local table = table
local ipairs = ipairs
local type = type
local setmetatable = setmetatable
local rawget = rawget
local rawset = rawset

local main_slg_const = require "main_slg_const"
local log = require "log"
local cfg_util = require "cfg_util"
local game_scheme = require "game_scheme"

-- ======================================= 模块 BEGIN =======================================

---@module festival_activity_cfg 活动配置模块
module("festival_activity_cfg")

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 启用兼容模式
local _ENABLE_COMPATIBILITY_MODE = true

--- 启用缓存
local _ENABLE_CACHE = true

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 活动类型枚举（对照表：https://q1doc.yuque.com/kiob3t/gynxh4/dgnd7vqltn95sqyh） 旧的活动枚举
-----活动类型枚举（对照表：https://q1doc.yuque.com/kiob3t/bs9797/ua33spfgm49n848x#zC9L） XMan2的活动枚举
---对应 HeadingCode
ActivityCodeType = {
    --region 位面1旧定义
    --- 0 缺省值、普通周活动
    Default = 0,
    --- 1 付费签到
    Sign = 1,
    --- 2 任务
    Task = 2,
    --- 3 礼包
    Gift = 3,
    --- 4 周卡
    WeekCard = 4,
    --- 5 Boss试炼
    BossChallenge = 5,
    --- 6 升星礼包
    StarRisingGift = 6,
    --- 7 心愿抽
    WishListLottery = 7,
    --- 8 圣物（饰品）抽卡
    DecorationLottery = 8,
    --- 9 战甲宝典
    ArmorTreasureBook = 9,
    --- 10 神匣遗迹
    Relics = 10,
    --- 11 位面宝箱
    TreasureBox = 11,
    --- 12 免费签到
    FreeSign = 12,
    --- 13 符文抽取活动（占星屋）
    SigilLottery = 13,
    --- 14 英雄召唤
    HeroSummon = 14,
    --- 15 英雄宝典
    HeroTreasureBook = 15,
    --- 16 英雄试炼（试炼副本）
    HeroExclusive = 16,
    --- 17 Bingo
    Bingo = 17,
    --- 18 踏青
    Hiking = 18,
    --- 19 新版徽章活动-道具累积获得
    BadgeCumulativeProps = 19,
    --- 20 新版徽章活动-徽章累积付费
    BadgeCumulativePayment = 20,
    --- 21 新版徽章活动-兑换商店
    BadgeExchangeShop = 21,
    --- 22 英雄重生
    HeroRespawn = 22,
    --- 23 Boss试炼2（中秋圆月之战）
    BossChallengeV2 = 23,
    --- 24 圣诞树
    ChristmasTree = 24,
    --- 25 非活动页面
    NonActivity = 25,
    --- 26 周年庆创角好礼
    AnniversaryCreationGift = 26,
    --- 27 双旦活动黑金卡
    BlackGoldCard = 27,
    --- 28 情人节-送花
    ValentineDayFlower = 28,
    --- 29 情人节-射箭
    ValentineDayArchery = 29,
    --- 30 情人节-七日特惠礼包
    ValentineDaySevenDayGift = 30,
    --- 31 创世宝库-星矿探索
    GenesisTreasureLottery = 31,
    --- 32 创世宝库-星矿探索
    GenesisTreasureShop = 32,
    --- 33 节日皮肤礼包（包括运营活动的皮肤礼包，可使用actType来判断）
    FestivalSkinGift = 33,
    --- 34 新英雄皮肤礼包
    NewHeroSkinGift = 34,
    --- 35 跳转url
    Url = 35,
    --- 36 大富翁
    Monopoly = 36,
    --- 37 常驻皮肤礼包（虫母）
    ResidentSkinGift = 37,
    --- 42 端午节抽奖
    DragonBoatGift = 42,
    --- 43 做菜小游戏
    MakeFoods = 43,
    --- 44 聊天发红包
    ChatRedPackage = 44,
    --- 45 英雄试用
    HeroTrial = 45,
    --- 46 抢红包小游戏
    SnatchRedEnvelopGame = 46,
    --- 47 全服抢红包
    WorldSnatchRedEnvelop = 47,
    -- 55 万圣节装扮南瓜灯活动
    HalloweenDressGame = 49,
    --- 156 双旦救援活动
    PickTheRoute = 50,
    --- 155 双旦拼团活动
    ChristmasNewyear = 51,
    --- 52 画糖人活动
    DrawSugarman = 52,
    --- 158 春节存钱罐
    PiggyBank = 53,
    --- 54 新春祝福
    NewYearBlessing = 54,
    --- 162 迎春好礼
    ChineseNewYearReward = 56,
    --- 61 翻牌活动
    TurnOverCard = 61,
    --endregion
    --XMan2--新的活动从>1000开始
    --原则上和headingCode设置一样，至于为什么同时存在headingCode和ActivityCodeType，是为了和以前的逻辑同步  
    --------------------------日常活动-------------
    WorldBoss = 201, --世界boss    
    ZombiesAttack = 206,--丧尸来袭
    Arena_New = 207, --新手竞技场
    Arena_Weekend = 208, --巅峰赛
    Arena_3v3 = 209, --3v3竞技场
    CampTrial = 210, --阵营试炼
    
    --------------------------日常活动End-------------
    --------------------------商业活动-------------
    BattlePass2 = 1001, -- 二档战令
    BattlePass3 = 1002, --三档战令
    BattlePass2UnTask = 1003, --二档战令无任务战令
    PioneerActivity = 1004, --先锋目标
    FullyWar = 1006,        --全面备战
    LuckyWheel = 1007,      --幸运转盘
    BestLeader = 1008,      --最强指挥官
    TotalRecharge = 1009,   --累计充值
    Universal = 101, --通用活动类型
    UniversalRecharge = 102, -- 通用付费活动
    DailyGift1 = 104, --每日礼包
    Lottery = 105, --抽奖    
    MiniGame = 106, --风暴营救小游戏
    
    HalloweenSlotMachineMain = 1028, --万圣节老虎机主活动
    SlotMachine = 108, --老虎机
    
    ArmCompetition = 202, --军备竞赛
    GeneralTrail = 203, --将军试炼
    AllianceBoss = 204, --联盟BOSS
    WarRally = 205, --集结大作战
    CitySiege = 211, --城市竞赛
    AllianceDuel = 212, --联盟对决
    WarZoneDuel = 215, --战区对决
    Doomsday = 217,--末日降临
    CommunityGift = 218, --社区好礼
    StormRescue = 220, --风暴营救
    HeroFirstRecharge = 222, --英雄首冲活动
    StormRescue2 = 229, --风暴营救2
    StormRescue3 = 50001, --风暴营救3
    
    DailyGift = 1010, --每日特惠
    WeekendCard = 1011, --周卡
    MonthCard = 1012, --月卡
    DailyGifts = 1014,--每日必买
    FirstRecharge = 1016,--首冲
	LoginGift=1017, --登录好礼
    DiamondGift=1015, --钻石商店
    LimitGift = 1018,    --限时礼包 
    LimitGiftSpecial=1013, --特殊限时礼包
    GiftShop=1013, --礼包商城
    MythicalBeastBingo = 1034, --神兽bingo 
    MythicalBestSignIn = 1033, --神兽签到
    ShiningShop=1019, --闪金集市
    MiracleBox = 1020,--神迹宝箱
    DesertStorm = 216, --沙漠风暴
    FindTreasure = 1021, --寻宝大作战
    OptionalWeekCard = 1022, -- 自选周卡
    SevenDayLogin = 219, -- 七日登录
    ZombieStorm = 221, --丧尸灾变

    SuperMonthGift = 1024, --超值月卡 （月月超值）

    LandRevivalMain = 1025, --领地复兴主界面
    LandRevivalTask = 1026, --领地复兴任务
    LandRevivalBattlePass = 1027, --领地复兴战令
    
    HalloweenExchangeShop = 1029, --万圣节兑换商店
    HalloweenWingding = 1030, --万圣节-狂欢宴会
    HalloweenWingdingRank = 109, --狂欢宴会-排行榜
    MadnessLord = 225, --狂暴领主
    StormArena = 223, --风暴竞技场
    --------------------------商业活动End-------------
}

--- 活动组（子活动归属的活动类型） --XMan2中 暂时不再使用，预留这些活动是否也分大类
ActivityActType = {
    --region 活动组
    --- 1 新英雄活动
    NewHero = 1,
    --- 2 感恩节活动（带宝典）
    Thanksgiving = 2,
    --- 3 周活动
    Festival = 3,
    --- 4 运营活动
    Operation = 4,
    --- 5 战甲宝典
    ArmorTreasureBook = 5,
    --- 6 圣物活动
    Decoration = 6,
    --- 7 星河神殿活动
    StarRiverTemple = 7,
    --- 8 天命星途
    HeavenlyFate = 8,
    --- 9 周年庆活动
    Anniversary = 9,
    --- 11 春节存钱罐
    PiggyBank = 11,
    --- 13 年中庆典
    MidYearCelebrate = 13,
    --endregion    
}

ActivityEntranceType = {
    --XMan2--活动入口
    Mall = 1001, --商城
    Benefits = 1002, --超值活动
    Special = 1003, --  特殊事件
    WarZonePK = 1004, --战区对决系列
    AlliancePK = 1005, --同盟对决系列
    Congress = 1006, --congress 国会系列
    Pioneer = 1007, --先锋系列
    LimitTimeActivity = 1009, --限时活动
    FirstRecharge = 1010, --首充
	LoginGift=1011, --登录活动  
    LimitGift=1012, --限时活动
    TotalRecharge = 1013, --累充活动
    SevenDayLogin = 1014, --七天登录
    ChangePackage = 1015, -- 换包好礼
    ResourceDownload = 1016, -- 资源下载
    Questionnaire = 1017, -- 问卷
    MythicalBeastBingo = 1018, --神兽binggo/神兽签到
    LandRevival = 1019, --领地复兴
    Halloween = 1021, --万圣节活动
}
---@public 自定义活动解锁条件
---目的： 有部分活动，除了受活动总表和FunctionOpen控制条件外，还需要自定义条件
---eg. 比如，1，首冲礼包，活动周期是一直开的；但是如果购买了，活动数据其实一直有，但入口不能展示了 2，竞技场，活动周期是一直开的；但自己有特定的解锁条件，
CustomActivityUnlockCondition = {
    [ActivityCodeType.FirstRecharge] = function(activityID)
        local gw_firstrecharge_mgr = require "gw_firstrecharge_mgr"
        local isOpen = gw_firstrecharge_mgr.CheckIsOpen(activityID)
        return isOpen
    end,
    [ActivityCodeType.LoginGift]=function(activityID)
        local login_gift_data =require "login_gift_data"
        local isOpen =login_gift_data.IsOpen(activityID)
        return isOpen
    end,
    [ActivityCodeType.LimitGift]=function(activityID)
        local limit_gift_data =require "limit_gift_data"
        local isOpen =limit_gift_data.GetIsOpen(activityID)
        return isOpen
    end,
    [ActivityCodeType.DesertStorm] = function(activityID)
        local gw_storm_mgr = require "gw_storm_mgr"
        local isOpen = gw_storm_mgr.CheckStormOpen()
        return isOpen
    end,
    [ActivityCodeType.WarZoneDuel] = function(activityID)
        local war_zone_duel_define = require "war_zone_duel_define"
        local war_zone_duel_data = require "war_zone_duel_data"
        local isEnd = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.AtyEnd)
        if isEnd then
            return false
        end
        local isOpen = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.None)
        if not isOpen then
            return true
        end
        return false
    end,
    [ActivityCodeType.SevenDayLogin] = function(activityID)
        local gw_seven_day_login_mgr = require "gw_seven_day_login_mgr"
        local isOpen = gw_seven_day_login_mgr.CheckIsOpen()
        return isOpen
    end,
    [ActivityCodeType.SuperMonthGift] = function(activityID) 
        local super_month_gift_mgr = require "super_month_gift_mgr"
        return super_month_gift_mgr.GetModuleOpen()
    end,
    [ActivityCodeType.Universal] = function(activityID)
        local ReviewingUtil = require "ReviewingUtil"
        if ReviewingUtil.IsReviewing() then
            if not ActivityReviewingWhiteListByActId[activityID] then
                return false
            end
        end
        return true
    end,
    [ActivityCodeType.StormRescue] = function(activityID)
        local moduleOpenPro_pb = require "moduleOpenPro_pb"
        local net_module_open = require "net_module_open"
        local isOpen = net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_MiniGameWeekendActivity)
        return isOpen
    end,
    [ActivityCodeType.StormRescue2] = function(activityID)
        local moduleOpenPro_pb = require "moduleOpenPro_pb"
        local net_module_open = require "net_module_open"
        local isOpen = net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_MiniGameWeekendActivity)
        return isOpen
    end,
    [ActivityCodeType.StormRescue3] = function(activityID)
        local moduleOpenPro_pb = require "moduleOpenPro_pb"
        local net_module_open = require "net_module_open"
        local isOpen = net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_MiniGameWeekendActivity)
        return isOpen
    end,
    
    [ActivityCodeType.HeroFirstRecharge] = function(activityID)
        local moduleOpenPro_pb = require "moduleOpenPro_pb"
        local net_module_open = require "net_module_open"
        local isOpen = net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_MiniGameFirstRechargeHeroActivity) 
        return isOpen
    end,
}

--- 活动类型: festivalActivity.csv > type
ActivityType = {
    NewYear = 20
}

-- 基地跨服状态下, 需要显示的活动headingCode
CrossServerActivity = {
    [ActivityCodeType.ArmCompetition] = true, --军备竞赛
    [ActivityCodeType.DiamondGift] = true, --直冲钻石
    [ActivityCodeType.AllianceDuel] = true, --同盟对决
    [ActivityCodeType.WarZoneDuel] = true, --战区对决
}

--不在右上角显示的活动列表
ActivityBlackList={
    [ActivityCodeType.LoginGift]=ActivityEntranceType.LoginGift
}
----提审包黑名单
ActivityReviewingWhiteList ={
    [ActivityCodeType.ArmCompetition]= true,
    [ActivityCodeType.DiamondGift]= true,
    [ActivityCodeType.AllianceDuel] = true,
    [ActivityCodeType.WarZoneDuel] = true,
    [ActivityCodeType.WorldBoss] = true,
    [ActivityCodeType.AllianceBoss] = true,
    [ActivityCodeType.ZombiesAttack] = true,
}
---提审包黑名单 根据活动ID
ActivityReviewingWhiteListByActId = {
    
}

---@field 预告活动类型 table 完全无视活动未开启，客户端读表判断决定显示。当活动开启时，优先走活动开启逻辑
PreviewActivityList = {
    [ActivityEntranceType.WarZonePK] = { 
        activityID = function()
            local war_zone_duel_data = require "war_zone_duel_data"
            local id = war_zone_duel_data.GetActivityID()
            return id
        end,
        mainBtnGroup = main_slg_const.MainButtonGroupType.RightTop, --主界面右上角按钮
        isShowFunction = function()
            local war_zone_duel_data = require "war_zone_duel_data"
            local war_zone_duel_define = require "war_zone_duel_define"
            local isPre = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.PreNotice)
            return isPre
        end
    },
    [ActivityEntranceType.AlliancePK] = {
        activityID = function()
            return 210
        end,
        mainBtnGroup = main_slg_const.MainButtonGroupType.RightTop, --主界面右上角按钮
        isShowFunction = function()
            local activity_preview_mgr = require "activity_preview_mgr"
            local activity_preview_cfg = require "activity_preview_cfg"
            local isPreview = activity_preview_mgr.IsJudgePreview(activity_preview_cfg.ActivityPreviewID.AllianceDuel)
            return isPreview
        end,
        isIgnoreBaseCondition = true --忽略活动的基本条件
    },
}

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 获取活动配置数量
---@return number 活动配置数量
function GetActivityCount()
    return game_scheme:ActivityMain_nums()
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 获取活动时间配置
---@param index number 下标
---@return table|nil 活动时间配置（ActivityMain.csv）
function GetActivityTimeCfgByIndex(index)
    return game_scheme:ActivityMain(index)
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 获取活动开始时间
---@param AtyID number 活动ID
---@return number|nil 活动开始时间
function GetActivityStartTime(AtyID)
    local activityTimeCfg = GetActivityMainCfgByActivityID(AtyID)
    if (not activityTimeCfg) then
        return nil
    end
    return ParseActivityTimeStamp(activityTimeCfg.TimeType, activityTimeCfg.TimeStart, 1)
end
--- 获取活动结束时间
---@param AtyID number 活动ID
---@return number|nil 活动结束时间
function GetActivityEndTime(AtyID)
    local activityTimeCfg = GetActivityMainCfgByActivityID(AtyID)
    if (not activityTimeCfg) then
        return nil
    end
    return ParseActivityTimeStamp(activityTimeCfg.TimeType, activityTimeCfg.TimeEnd, 2)
end

--- 获取活动开始时间Str
---@param AtyID number 活动ID
---@return number|nil 活动开始时间Str
function GetActivityStartTimeStr(AtyID)
    local activityTimeCfg = GetActivityMainCfgByActivityID(AtyID)
    if (not activityTimeCfg) then
        return nil
    end
    return ParseActivityTimeStr(activityTimeCfg.TimeType, activityTimeCfg.TimeStart, 1)
end

--- 获取活动结束时间Str
---@param AtyID number 活动ID
---@return number|nil 活动结束时间Str
function GetActivityEndTimeStr(AtyID)
    local activityTimeCfg = GetActivityMainCfgByActivityID(AtyID)
    if (not activityTimeCfg) then
        return nil
    end
    return ParseActivityTimeStr(activityTimeCfg.TimeType, activityTimeCfg.TimeEnd, 2)
end

--- 获取活动开始时间Str
---@param  cfg table   活动配置行
---@return number|nil 活动开始时间Str
function GetActivityStartTimeStrByCfg(cfg)
    local activityTimeCfg = GetActivityMainCfgByActivityID(AtyID)
    if (not activityTimeCfg) then
        return nil
    end
    return ParseActivityTimeStamp(cfg.TimeType, cfg.TimeStart, 1)
end

--- 获取活动结束时间Str
---@param cfg number 活动配置行
---@return number|nil 活动结束时间Str
function GetActivityEndTimeStrByCfg(cfg)
    return ParseActivityTimeStr(cfg.TimeType, cfg.TimeEnd, 2)
end

--- 获取活动开始和结束时间
---@param AtyID number 活动ID
---@return number|nil, number|nil 活动开始时间, 活动结束时间
function GetActivityTime(AtyID)
    local activityTimeCfg = GetActivityMainCfgByActivityID(AtyID)
    if (not activityTimeCfg) then
        return nil
    end
    return ParseActivityTimeStamp(activityTimeCfg.TimeType, activityTimeCfg.TimeStart, 1), ParseActivityTimeStamp(activityTimeCfg.TimeType, activityTimeCfg.TimeEnd, 2)
end

---获取活动的持续时长，s
function GetActivityDuration(AtyID)
    local activityTimeCfg = GetActivityMainCfgByActivityID(AtyID)
    if (not activityTimeCfg) then
        return nil
    end
    return ParseActivityTimeStamp(activityTimeCfg.TimeType, activityTimeCfg.TimeEnd, 2) - ParseActivityTimeStamp(activityTimeCfg.TimeType, activityTimeCfg.TimeStart, 1)
end
---@public 获取活动时间
---@param type number 时间类型
---@param content number 时间内容
---@param startType number 开始类型 1:活动开始时间 2:活动结束时间
---@return number
function ParseActivityTimeStamp(type, content, startType)
    log.Error("客户端本地获取时间，临时返回类型 todo by bxz,目前请直接使用 festival_activity_mgr.GetActivityTimeStampByActivityID(activityID);如果需要活动未开启前知道活动时间，该功能未开发")
    local defaultStartTime = "2024-11-11-0:00"
    local defaultEndTime = "2024-11-17-0:00"
    if type == 1 then
        return (startType and startType == 1) and cfg_util.ToTimestamp(defaultStartTime) or cfg_util.ToTimestamp(defaultEndTime)
    elseif type == 2 then
        return (startType and startType == 1) and cfg_util.ToTimestamp(defaultStartTime) or cfg_util.ToTimestamp(defaultEndTime)
    end
    return cfg_util.ToTimestamp(content)
end

---@public 获取活动时间Str
---@param type number 时间类型
---@param content number 时间内容
---@param startType number 开始类型 1:活动开始时间 2:活动结束时间
---@return number
function ParseActivityTimeStr(type, content, startType)
    local defaultStartTime = "2024-11-11-0:00"
    local defaultEndTime = "2024-11-17-0:00"
    if type == 1 then
        return (startType and startType == 1) and defaultStartTime or defaultEndTime
    elseif type == 2 then
        return (startType and startType == 1) and defaultStartTime or defaultEndTime
    end
    return content
end

--- 预缓存 ActivityMain 表的数据
local _PRECACHE_ACTIVITY_TIME_CFG_KEYS = false

--- ActivityMain 表的字段
local _ACTIVITY_TIME_CFG_KEYS = {
    "AtyID", "headingCode", "AtyEntrance","AtyEntrance2", "uiTemplateID","Seqencing", "SubAtyID", "ActivityGroup","PriorityInGroup","bindActivity","associatedID","HooklevelID", "BaseLevel",
    "SeverTime", "OpenServerId", "TimeType", "TimeStart", "TimeContinue", "TimeRecycle", "TimeEnd", "SeverUnion",
    "bindActivity", "associatedID"
}

--- 兼容化配置
---@param activityCfg table 活动配置
---@param activityTimeCfg table 活动时间配置
---@return table 活动配置（festivalActivity.csv+festivalActivityTime.csv）
local function _MakeCompatible(activityCfg, activityTimeCfg)
    local t = {}
    -- 预缓存
    if _PRECACHE_ACTIVITY_TIME_CFG_KEYS then
        for _, v in ipairs(_ACTIVITY_TIME_CFG_KEYS) do
            t[v] = activityTimeCfg[v]
        end
    end
    -- 设置元表
    setmetatable(t, {
        __index = function(self, key)
            if rawget(self, key) then
                return rawget(self, key)
            elseif activityCfg[key] then
                return activityCfg[key]
            elseif activityTimeCfg[key] then
                rawset(self, key, activityTimeCfg[key])
                return activityTimeCfg[key]
            end
        end,
        __newindex = function(self, key, value)
            log.Error('Attempt to modify read-only table')
        end
    })
    return t
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 活动配置缓存
---@type table<table, table>
local _activityCfgCache = {}

--- 获取活动配置
---@param activityMainCfg table 活动时间配置（festivalActivityMain.csv）
---@return table|nil 活动配置（festivalActivity.csv）
local function _GetActivityCfgByActivityMainCfg(activityMainCfg)
    -- 从缓存中获取
    if _ENABLE_CACHE and _activityCfgCache[activityMainCfg] then
        return _activityCfgCache[activityMainCfg]
    end
    --兼容一下 
    local uId = activityMainCfg.cfgID and activityMainCfg.cfgID or activityMainCfg.AtyID
    -- 活动模板配置
    local activityCfg = _GetActivityCfgByActivityID(uId)
    if (not activityCfg) then
        return nil
    end
    -- 兼容模式
    if _ENABLE_COMPATIBILITY_MODE then
        activityCfg = _MakeCompatible(activityCfg, activityMainCfg)
    end
    -- 缓存起来
    if _ENABLE_CACHE then
        _activityCfgCache[activityMainCfg] = activityCfg
    end
    return activityCfg
end

--- 获取活动配置
---@param AtyID number 活动ID
---@return table|nil, table|nil 活动配置（festivalActivity.csv），活动时间配置（ActivityMain.csv）
function GetActivityCfgByAtyID(AtyID)
    -- 活动前瞻 特殊处理 --已被删除
    local mainCfg = GetActivityMainCfgByActivityID(AtyID)
    return GetActivityCfgByActivityID(AtyID), mainCfg
end

--- 获取活动配置
---@param headingCode number 活动识别码
---@param versionNumber number 活动版本号
---@return table|nil, table|nil 活动配置（festivalActivity.csv），活动时间配置（festivalActivityTime.csv）
function GetActivityCfgByCodeAndVersion(headingCode, versionNumber)
    log.Error("versionNumber已被删除，不应该有调用")
end

--- 获取活动时间配置
---@param index number 下标
---@return table|nil 活动配置（festivalActivity.csv）
function GetActivityCfgByIndex(index)
    -- 活动时间配置
    local activityTimeCfg = GetActivityTimeCfgByIndex(index)
    if (not activityTimeCfg) then
        return nil
    end
    return _GetActivityCfgByActivityMainCfg(activityTimeCfg)
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 获取新手活动配置
---@param AtyID number 活动ID
---@return table|nil 新手活动配置（NewActivity.csv）
function GetNewbieActivityCfgByAtyID(AtyID)
    return game_scheme:NewActivity_0(AtyID)
end

local newActivity_cache = {}
local newActivityNone_cache = {}
--- 获取新手活动配置
---@param headingCode number 活动识别码
---@param versionNumber number 活动版本号
---@return table|nil 新手活动配置（NewActivity.csv）
function GetNewbieActivityCfgByCodeAndVersion(headingCode, versionNumber)
    if _ENABLE_CACHE then

        if headingCode == nil or versionNumber == nil then
            return nil
        end

        local nonTable = newActivityNone_cache[headingCode]
        if nonTable and nonTable[versionNumber] then
            return nil
        end

        local firstTable = newActivity_cache[headingCode]
        if not firstTable then
            firstTable = {}
            newActivity_cache[headingCode] = firstTable
        end

        local result = firstTable[versionNumber]
        if not result then
            result = game_scheme:NewActivity_1(headingCode, versionNumber)
            firstTable[versionNumber] = result
        end

        --表内依旧无法取到的话，标记起来
        if not result then
            local markTable = newActivityNone_cache[headingCode]
            if not markTable then
                markTable = {}
                newActivityNone_cache[headingCode] = markTable
            end
            markTable[versionNumber] = true
        end

        return result

    else
        return game_scheme:NewActivity_1(headingCode, versionNumber)
    end
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 获取活动入口配置
---@param cfgID number 配置ID ActivityMain中的入口id  --AtyEntrance/AtyEntrance1
---@return table|nil 活动入口配置（FunctionBar.csv）
function GetActivityEntranceCfg(cfgID,skipLog)
    local activityUICfg = game_scheme:FunctionBar_0(cfgID) 
    if (not activityUICfg) then
        if not skipLog then
            log.Error("PP* 活动入口配置异常！| FunctionBar.csv > cfgID: " .. tostring(cfgID))
        end
        return nil       
    end
    return activityUICfg
end

-- - - - - - - - - - - - - - - - - - - - - 新活动总表配置修改 - - - - - - - - - - - - - - - - - - - -
--- 获取活动配置 festivalActivity.csv
---@param actId number 活动id  实际上就是festivalActivity中的cfgID
function GetActivityCfgByActivityID(actId)
    local mainCfg = GetActivityMainCfgByActivityID(actId)
    if not mainCfg then
        return nil
    end
    --这里通过ActivityMain缓存来读取配置 按原来的逻辑
    return _GetActivityCfgByActivityMainCfg(mainCfg)
    --local cfg = game_scheme:festivalActivity_0(actId)
    --if (not cfg) then
    --    log.Error("PP* 活动配置异常！| festivalActivity.csv > cfgID: " .. tostring(actId))
    --    return nil
    --end
    --return cfg
end

--- 获取活动配置 festivalActivity.csv
---@param actId number 活动id  实际上就是festivalActivity中的cfgID
function _GetActivityCfgByActivityID(actId)
    local cfg = game_scheme:festivalActivity_0(actId)
    if (not cfg) then
        --log.Error("PP* 活动配置异常！| festivalActivity.csv > cfgID: " .. tostring(actId))
        return nil
    end
    return cfg
end

--- 获取活动Main配置 ActivityMain.csv
---@param actId number 活动id  ActivityMain中的AtyID
function GetActivityMainCfgByActivityID(actId)
    local cfg = game_scheme:ActivityMain_0(actId)
    if (not cfg) then
        --log.Error("PP* 活动配置异常！| ActivityMain.csv > actId: ", actId)
        return nil
    end
    return cfg
end

--- 获取活动Main配置 ActivityCommonUI.csv
---@param actId number 活动id
function GetActivityCommonUICfgByActivityID(actId)
    local cfg = GetActivityCfgByActivityID(actId)
    if (not cfg) then
        --log.Error("PP* 活动配置异常！| festivalActivity.csv > actId: ", actId)
        return nil
    end
    local commonCfg = game_scheme:ActivityCommonUI_0(cfg.uiTemplateID)
    if (not commonCfg) then
        --log.Error("PP* 活动配置异常！| ActivityCommonUI.csv > Id: ", cfg.uiTemplateID)
        return nil
    end
    return commonCfg
end
-- ======================================= 模块 END =======================================
--登录时自动请求活动时间数据，配置的是活动ID，注意活动id要求策划不要随意变动（保持下面的一致），否则会导致异常
--[[AutoRequestDataWhenLogin ={
    106, --新手竞技场
    209,-- 巅峰竞技场
    501, --3v3竞技场
    115, --新手竞技场2
    119, --新手竞技场2
}]]
AutoRequestDataWhenLogin = {
    ActivityCodeType.Arena_3v3,
    ActivityCodeType.Arena_Weekend,
    ActivityCodeType.Arena_New,
    ActivityCodeType.StormArena,
}

