﻿--- base_cache_child.lua -------------------------------
--- author:  fgy
--- date: 2024/4/9 16:06
--- desc:子模块缓存内容，该内容相对来说比较独立
--------------------------------------------------
local require = require
local pairs = pairs
local util = require "util"
local ui_base_data = require "ui_base_data"
local Component = require "base_comp"
local newclass = newclass
 
module("base_cache_child")
local M = newclass("base_cache_child",Component,true)

function M:ctor()
    M.__base.ctor(self, "base_cache_child")
end

function M:SetCacheName(name)
    self._NAME = name
    self.childCacheModule = self.childCacheModule or {}
end
------------------------------------------------子页面缓存Start------------------------------------------------------------
--添加子模块显示
function M:AddChildCacheModuleShow(uiModuleName, parentTrans, showCallback, refreshSortOrder)
    if not uiModuleName then
        return
    end
    self:HideChildCacheModule()
    local ui_window_mgr = require "ui_window_mgr"
    if self.childCacheModule[uiModuleName] then
        self.childCacheModule[uiModuleName]:_SetVisible(true)
        local v = self.childCacheModule[uiModuleName]
        if v.UIRoot and not util.IsObjNull(v.UIRoot) then
            v.UIRoot:SetActive(true)
        end
        if refreshSortOrder then  --多个panel缓存时,重新打开时刷新排序
            local moduleWin = ui_window_mgr:ShowModule(uiModuleName, showCallback, function(uiModuleNameE)
                 self:OnHideCallBack(uiModuleNameE)
            end)
            self.childCacheModule[uiModuleName] = moduleWin
        end
    else
        ui_base_data:AddParentCache(uiModuleName, parentTrans)
        local moduleWin = ui_window_mgr:ShowModule(uiModuleName, showCallback, function(uiModuleNameE)
             self:OnHideCallBack(uiModuleNameE)
        end)
        self.childCacheModule = self.childCacheModule or {}
        self.childCacheModule[uiModuleName] = moduleWin
    end

    return self.childCacheModule[uiModuleName]
end

--子页面卸载回调
function M:OnHideCallBack(uiModuleName)
    if not self.childCacheModule or not uiModuleName then
        return
    end

    if self.childCacheModule[uiModuleName] then
        self.childCacheModule[uiModuleName] = nil
    end
end

--记录子模块缓存  --针对模块内部已经实现缓存
function M:RecordChildCacheModule(uiModuleName, moduleWin)
    if not uiModuleName or not moduleWin then
        return
    end

    self.recordChildCacheModule = self.recordChildCacheModule or {}
    self.recordChildCacheModule[uiModuleName] = moduleWin

    local v = moduleWin
    if v.UIRoot and not util.IsObjNull(v.UIRoot) then
        --v:_SetVisible(true)
        v.UIRoot:SetActive(true)
    end
end

--清除记录子模块缓存
function M:ClearRecordChildCacheModule()
    self.recordChildCacheModule = nil
end

--隐藏记录所有子模块缓存
function M:HideAllRecordChildCacheModule(expectModuleName)
    if self.recordChildCacheModule then
        for k, v in pairs(self.recordChildCacheModule) do
            if k ~= expectModuleName and v then
                --v:_SetVisible(false)
                if v.UIRoot and not util.IsObjNull(v.UIRoot) then
                    v.UIRoot:SetActive(false)
                end
                break
            end
        end
    end
end

--隐藏记录指定子模块缓存
function M:HideRecordChildCacheModule(moduleName)
    if self.recordChildCacheModule then
        for k, v in pairs(self.recordChildCacheModule) do
            if k == moduleName and v then
                --v:_SetVisible(false)
                if v.UIRoot and not util.IsObjNull(v.UIRoot) then
                    v.UIRoot:SetActive(false)
                end
                break
            end
        end
    end
end

--是否是模块缓存
function M:IsCacheModule()
    local parentCache = ui_base_data:GetParentCache(self._NAME)
    if parentCache then
        return true
    end
end

--是否有子模块缓存
function M:HasChildCacheModule(uiModuleName)
    if not uiModuleName or not self.childCacheModule or not self.childCacheModule[uiModuleName] then
        return
    end

    return true
end

--卸载子模块缓存
function M:UnloadChildCacheModule()
    if self.childCacheModule then
        local ui_window_mgr = require "ui_window_mgr"
        for k, v in pairs(self.childCacheModule) do
            ui_base_data:ClearParentCache(k)
            ui_window_mgr:UnloadModuleImmediate(k)
        end
    end

    self.childCacheModule = nil
    self:ClearParentCache()
    self:ClearRecordChildCacheModule()
end

--隐藏子模块缓存
function M:HideChildCacheModule()
    if self.childCacheModule then
        for k, v in pairs(self.childCacheModule) do
            if v then
                v:_SetVisible(false)
                if v.UIRoot and not util.IsObjNull(v.UIRoot) then
                    v.UIRoot:SetActive(false)
                end
            end
        end
    end
end

--清空子模块父物体缓存
function M:ClearParentCache()
    ui_base_data:ClearParentCache(self._NAME)
end

--检测父物体缓存
function M:CheckParentCache(parentTrans)
    local parentCache = ui_base_data:GetParentCache(self._NAME)
    if parentCache and parentCache.parent and not util.IsObjNull(parentCache.parent) then
        return parentCache.parent
    end

    return parentTrans
end

----自定义的curOrder值变化逻辑
--function M:CustomOrderProcess()
--    --Empty On Base
--end


function M:exportMethods()
    self:exportMethods_({
        "AddChildCacheModuleShow",
        "OnHideCallBack",
        "RecordChildCacheModule",
        "ClearRecordChildCacheModule",
        "HideAllRecordChildCacheModule",
        "HideRecordChildCacheModule",
        "IsCacheModule",
        "HasChildCacheModule",
        "UnloadChildCacheModule",
        "HideChildCacheModule",
        "ClearParentCache",
        "CheckParentCache",
        "SetCacheName"
    })
    return self.target_
end
------------------------------------------------子页面缓存End--------------------------------------------------------------
return M
