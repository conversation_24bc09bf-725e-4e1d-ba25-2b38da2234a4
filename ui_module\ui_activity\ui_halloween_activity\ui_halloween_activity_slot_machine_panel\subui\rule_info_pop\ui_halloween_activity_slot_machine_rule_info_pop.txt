local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type

local CS = CS

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_halloween_activity_slot_machine_rule_info_pop_binding"

local slot_machine_ani_helper = require "slot_machine_ani_helper"

--region View Life
module("ui_halloween_activity_slot_machine_rule_info_pop")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}

    local item_prefab = self.item_draw_result_item
    --item_prefab.gameObject:SetActive(false)
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:RenderPageButtonsGroup(index)
    self.btn_reward_dis.gameObject:SetActive(index ~= 1)
    self.btn_reward.gameObject:SetActive(index == 1)
    self.btn_rule_dis.gameObject:SetActive(index ~= 2)
    self.btn_rule.gameObject:SetActive(index == 2)
end

function UIView:RenderRewardPage(act_id, same3_data_list, same2_data_list, same0_data_list)
    local item_prefab = self.item_draw_result_item
    local draw_result_item = require "draw_result_item"

    slot_machine_ani_helper.ClearChildren(self.rtf_reward_page_3same)
    slot_machine_ani_helper.ClearChildren(self.rtf_reward_page_2same)
    slot_machine_ani_helper.ClearChildren(self.rtf_reward_page_0same)

    for i, v in ipairs(same3_data_list) do
        local item = CS.UnityEngine.GameObject.Instantiate(item_prefab, self.rtf_reward_page_3same)
        draw_result_item.RenderByData(item:GetComponent("RectTransform") , act_id, v)
        --item.transform:SetParent(self.rtf_reward_page_3same.transform, false)
    end
    for i, v in ipairs(same2_data_list) do
        local item = CS.UnityEngine.GameObject.Instantiate(item_prefab, self.rtf_reward_page_2same)
        draw_result_item.RenderByData(item:GetComponent("RectTransform") , act_id, v)
        --item.transform:SetParent(self.rtf_reward_page_2same.transform, false)
    end
    for i, v in ipairs(same0_data_list) do
        local item = CS.UnityEngine.GameObject.Instantiate(item_prefab, self.rtf_reward_page_0same)
        draw_result_item.RenderByData(item:GetComponent("RectTransform") , act_id, v)
        --item.transform:SetParent(self.rtf_reward_page_0same.transform, false)
    end
end

function UIView:SelectPage(index)
    self:RenderPageButtonsGroup(index)
    self.rtf_reward_page.gameObject:SetActive(index == 1)
    self.rtf_rule_page.gameObject:SetActive(index == 2)
end

function UIView:RenderRulePage(help_string_list)
    slot_machine_ani_helper.ClearChildren(self.rtf_rule_page_content)
    for i, v in ipairs(help_string_list) do
        local item = CS.UnityEngine.GameObject.Instantiate(self.txt_rule_page_content_template, self.rtf_rule_page_content)
        item.transform:SetParent(self.rtf_rule_page_content.transform, false)
        item.gameObject:SetActive(true)
        item.text = v
    end
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
