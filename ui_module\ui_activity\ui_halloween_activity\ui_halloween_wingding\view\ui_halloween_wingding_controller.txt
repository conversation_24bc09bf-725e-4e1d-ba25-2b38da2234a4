local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local log = require "log"
local event_task_define = require "event_task_define"
local gw_task_const = require "gw_task_const"
local gw_task_mgr = require "gw_task_mgr"
local taskpart_pb = require "taskpart_pb"
local gw_task_data = require "gw_task_data"
local game_scheme = require "game_scheme"
local os = require "os"
local time_util = require "time_util"
local halloween_wingding_data = require "halloween_wingding_data"
local event_halloween_define = require "event_halloween_define"
local net_activity_module = require "net_activity_module"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_halloween_wingding_controller")
local controller = nil
local UIController = newClass("ui_halloween_wingding_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    self.activityID = data.activityID
    self:GetActivityEndTime()
    self:InitViewPanel()
    self:InitRankReward()
    self:GetNextLvUnlockTime()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    self.taskUpdate = function(eventName, taskData, moduleId, moduleList)
        if moduleList[gw_task_const.TaskModuleType.HalloweenSignIn] then
            self:InitViewPanel()
        end
    end
    self:RegisterEvent(event_task_define.REFRESH_TASK, self.taskUpdate)
end

function UIController:AutoUnsubscribeEvents() 
end

--region 自动生成 无用
function  UIController:OnBtnPumpkin5ClickedProxy()
end
function  UIController:OnBtnPumpkin4ClickedProxy()
end
function  UIController:OnBtnPumpkin3ClickedProxy()
end
function  UIController:OnBtnPumpkin2ClickedProxy()
end
function  UIController:OnBtnPumpkin1ClickedProxy()
end

--endregion

--endregion

--region Controller Logic
function  UIController:OnSliderLeftSliderValueChange(value)
end
function  UIController:OnBtnGiftClickedProxy()
end

---@public function 点击排行榜
function  UIController:OnBtnRankClickedProxy()
    --打开排行榜
    local activityId
    local cfg = game_scheme:ActivityMain_0(self.activityID)
    if cfg then
       activityId = cfg.bindActivity.data[2]
    end
    if activityId then
        local halloween_wingding_rank_mgr = require "halloween_wingding_rank_mgr"
        local MainToggleType = halloween_wingding_rank_mgr.MainToggleType
        local SubToggleType = halloween_wingding_rank_mgr.SubToggleType
        local data = {activeID = activityId,mainToggleType = MainToggleType.Personal,subToggleType = SubToggleType.Rank}
        ui_window_mgr:ShowModule("ui_halloween_wingding_rank",nil,nil,data)
    end
end

---@public function 点击排行榜奖励气泡
function UIController:OnBtnRankBubbleClickedProxy()
    local activityId
    local cfg = game_scheme:ActivityMain_0(self.activityID)
    if cfg then
       activityId = cfg.bindActivity.data[2]
    end
    if activityId then
        local halloween_wingding_rank_mgr = require "halloween_wingding_rank_mgr"
        local MainToggleType = halloween_wingding_rank_mgr.MainToggleType
        local SubToggleType = halloween_wingding_rank_mgr.SubToggleType
        local data = {activeID = activityId,mainToggleType = MainToggleType.Personal,subToggleType = SubToggleType.Reward}
        ui_window_mgr:ShowModule("ui_halloween_wingding_rank",nil,nil,data)
    end
end

function  UIController:OnBtnPartyClickedProxy()
end
function  UIController:OnBtnSubmitClickedProxy()
    local itemID,submitMinCount,preReward = halloween_wingding_data.GetSubmitData()
    ui_window_mgr:ShowModule("ui_goods_submit",nil,nil,{itemID = itemID,submitMinCount = submitMinCount,preReward = preReward})
end

---@public function 点击领取奖励
function UIController:OnClickReceiveGift(index)
    --发送领奖协议
    if self.taskDataArr then
       local taskData = self.taskDataArr[index]
        local activityID = halloween_wingding_data.GetActivityTaskID()
        local msg = taskpart_pb.TMSG_ACT_TASK_GETREWARD_REQ()
        msg.taskId:append(taskData.taskId)
        gw_task_mgr.TakeTaskReward(msg,activityID, gw_task_const.TaskModuleType.HalloweenSignIn)
    end
end

---@public function 获取活动结束时间
function UIController:GetActivityEndTime()
    local activityData = halloween_wingding_data.GetActivityData()
    if activityData then
        local endTime = activityData.endTimeStamp
        self:TriggerUIEvent("SetActivityTimer", endTime)
    end
end

function UIController:GetNextLvUnlockTime()
    local nextUnlockTime = time_util.GetTimeOfZero(os.server_zone(),os.server_time())
    self:TriggerUIEvent("SetNextUnlockTimer", nextUnlockTime)
end

function UIController:InitViewPanel()
    local taskIDArr = halloween_wingding_data.GetWingdingTaskIDArr()
    local preLvTaskData = nil
    local curLvTaskData = nil  --当前等级任务数据
    local nextLvTaskData = nil  --下一等级任务数据
    local curLevel = 1
    if taskIDArr then
        self.taskDataArr = {}
        for i, v in ipairs(taskIDArr) do
            local taskData = gw_task_data.GetTaskData(v)
            if not curLvTaskData and taskData and not taskData.status then
                curLvTaskData = taskData
                preLvTaskData = gw_task_data.GetTaskData(taskIDArr[i - 1])
                nextLvTaskData = gw_task_data.GetTaskData(taskIDArr[i + 1])
                curLevel = i
            end
            self.taskDataArr[i] = taskData
        end
        self:RefreshTaskProgress(preLvTaskData, curLvTaskData, nextLvTaskData,curLevel)
        
        self:TriggerUIEvent("RefreshGiftArea", self.taskDataArr)
    end
end

function UIController:RefreshTaskProgress(preLvTaskData, curLvTaskData, nextLvTaskData, curLevel)
    local viewData = {}
    viewData.curLevel = curLevel
    if curLvTaskData then
        local preOffset = 0
        if preLvTaskData then
            preOffset = preLvTaskData.completeValue
        end
        viewData.curProgress = curLvTaskData.rate --当前进度
        viewData.maxProgress = curLvTaskData.completeValue --当前进度上限
        viewData.preOffset = preOffset
    end

    --下一级的前置任务是否解锁
    if nextLvTaskData then
        local taskCfg = game_scheme:TaskMain_0(nextLvTaskData.TaskID)
        if taskCfg then
            local frontTaskData = gw_task_data.GetTaskData(taskCfg.FrontTaskID2)
            if frontTaskData and frontTaskData.isGet then
                viewData.isNextLvUnlock = true
            end
        end
    end
    
    self:TriggerUIEvent("RefreshTaskProgress", viewData)
end

---@public function 初始化排行榜奖励
function UIController:InitRankReward()
    --获取道具
    local halloween_wingding_rank_mgr = require "halloween_wingding_rank_mgr"
    local rewardData = halloween_wingding_rank_mgr.GetFirstRewardItemBySubType(halloween_wingding_rank_mgr.MainToggleType.Personal)
    if rewardData then
        self:TriggerUIEvent("RefreshRankReward", rewardData)
    end
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
