-- sort_order.txt ------------------------------------------------
-- author:  zjw
-- date:    2022.5.23
-- ver:     1.0
-- desc:    ui特效加载管理器（ui上）
-------------------------------------------------------------------

local table = table
local require = require
local typeof = typeof
local print = print

local util  = require "util"
local event = require "event"

local DynamicGameObjectLoader = CS.War.UI.DynamicGameObjectLoader
module("effect_load_mgr")

--当时执行加载的ui对象缓存 key:uiModuleName\value:uiEffects
local uiList = {}
--key:uiModuleName\value:预制体 键值缓存
local uiRootTransList = {}

--执行加载ui上的异步特效
function LoadUIEffects(uiRootTrans, uiModuleName)
    local uiEffects = uiList[uiModuleName]
    if uiEffects or util.IsObjNull(uiRootTrans) then
        --已执行过加载的ui跳过
        --预制体为空的跳过
        return;
    end
    uiEffects = {}
    local dynamicLoaders = uiRootTrans:GetComponentsInChildren(typeof(DynamicGameObjectLoader), true)
	local maxDuration = 0
	local len = dynamicLoaders.Length - 1
	local dynamicLoader
    local ticker = nil
	for i = 0, len do
		dynamicLoader = dynamicLoaders[i]
        if not util.IsObjNull(dynamicLoader) then
            ticker = nil
            if dynamicLoader.delayLoadTime > 0 then 
                dynamicLoader.delayTickerId = util.DelayCallOnce(dynamicLoader.delayLoadTime, function()
                    RemoveDelayTimeId(dynamicLoader)
                    dynamicLoader:Load() 
                     --print("加载特效：", dynamicLoader.abnames[0])
                end)
            else
                dynamicLoader:Load()
                 --print("加载特效：", dynamicLoader.abnames[0])
            end
            table.insert(uiEffects, dynamicLoader)
        end
	end
    uiRootTransList[uiModuleName] = uiRootTrans
    uiList[uiModuleName] = uiEffects
     --print("加载特效长度：", #uiEffects, "   :", uiRootTrans.name, " uiModuleName:", uiModuleName)
end

function RemoveDelayTimeId(loader)
    if not util.IsObjNull(loader) and loader.delayTickerId > -1 then 
        util.RemoveDelayCall(loader.delayTickerId)
        loader.delayTickerId = -1
    end
end

--通过模块名卸载ui上的异步特效
function UnloadUIEffectsByName(uiModuleName)
    if not uiModuleName then
        return
    end

    local uiEffects = uiList[uiModuleName]
    if not uiEffects then 
        return 
    end
    local len = #uiEffects
    -- --print("UnloadUIEffects 卸载特效：", uiRootTrans.name, len)
	local dynamicLoader
	for i = 1, len do
		dynamicLoader = uiEffects[i]
        -- --print("卸载特效：", dynamicLoader.name, "   ", i)
        if not util.IsObjNull(dynamicLoader) then
            if dynamicLoader.delayTickerId > -1 then 
                 --print("移除延迟特效timeId：", dynamicLoader.delayTickerId, "  abName:", dynamicLoader.abnames[0])
                util.RemoveDelayCall(dynamicLoader.delayTickerId)
                dynamicLoader.delayTickerId = -1
            else
                dynamicLoader:Unload()
                 --print("卸载特效：", dynamicLoader.abnames[0])
            end
        end
	end
     --print("ui特效卸载：", #uiEffects, " uiModuleName:", uiModuleName, " rootName:", uiRootTransList[uiModuleName])
    uiEffects = nil
    uiList[uiModuleName] = nil
    uiRootTransList[uiModuleName] = nil
end

--执行卸载ui上的异步特效
function UnloadUIEffects(uiRootTrans)
    if util.IsObjNull(uiRootTrans) then
        return
    end
    local uiModuleName = uiRootTransList[uiRootTrans]
    UnloadUIEffectsByName(uiModuleName)
end


function OnSceneDestroy()
    --清理当前缓存
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)

function OnModuleClose(eventName, moduleName, assetBundleName)
    --卸载ui特效
end
event.Register(event.UI_MODULE_CLOSE, OnModuleClose)


function OnModuleShow(eventname, count, moduleName)
    --执行加载ui特效
end
event.Register(event.SHOW_MODULE, OnModuleShow)


function OnModuleLoadComplete()
    --ui加载完成
end

event.Register(event.UI_MODULE_LOAD_COMPLETE, OnModuleLoadComplete)

 
