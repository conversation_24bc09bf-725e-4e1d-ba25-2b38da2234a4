local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local RectTransform = CS.UnityEngine.RectTransform


module("ui_arena_battle_history_binding")

UIPath = "ui/prefabs/gw/gw_arena/uiarenabattlehistory.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	srt_historyList = { path = "List/Viewport/srt_historyList", type = ScrollRectTable, },
	rtf_nothingShow = { path = "rtf_nothingShow", type = RectTransform, },

}
