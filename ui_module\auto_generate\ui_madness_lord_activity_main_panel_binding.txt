local require = require
local typeof = typeof

local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Transform = CS.UnityEngine.Transform
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local GameObject = CS.UnityEngine.GameObject
local Toggle = CS.UnityEngine.UI.Toggle


module("ui_madness_lord_activity_main_panel_binding")

UIPath = "ui/prefabs/gw/activity/madnesslord/uimadnesslordactivitymainpanel.prefab"

WidgetTable ={
	ss_CityBg = { path = "Bg/ss_CityBg", type = SpriteSwitcher, },
	tf_BtnGroup = { path = "Bg/Panel/tf_BtnGroup", type = Transform, },
	btn_GoTo = { path = "Bg/Panel/tf_BtnGroup/btn_GoTo", type = Button, event_name = "OnBtnGoToClickedProxy"},
	txt_timeLeft = { path = "Bg/Panel/tf_BtnGroup/btn_GoTo/txt_timeLeft", type = Text, },
	img_slider = { path = "Bg/Panel/hpSlider/img_slider", type = Image, },
	txt_hpText = { path = "Bg/Panel/hpSlider/txt_hpText", type = Text, },
	ss_heardBroken = { path = "Bg/Panel/hpSlider/ss_heardBroken", type = SpriteSwitcher, },
	txt_BossName = { path = "Bg/Panel/bossName/txt_BossName", type = Text, },
	txt_timeTips = { path = "Bg/Panel/tipsBg/txt_timeTips", type = Text, },
	btn_help = { path = "Bg/Panel/btn_help", type = Button, event_name = "OnBtnHelpClickedProxy"},
	tf_topRankBg = { path = "Bg/Panel/rankBg/tf_topRankBg", type = Transform, },
	tf_Players = { path = "Bg/Panel/rankBg/tf_topRankBg/tf_Players", type = Transform, },
	tf_Rank = { path = "Bg/Panel/rankBg/tf_topRankBg/tf_Rank", type = Transform, },
	txt_myRank = { path = "Bg/Panel/MyRankBg/txt_myRank", type = Text, },
	obj_noRank = { path = "Bg/Panel/MyRankBg/obj_noRank", type = GameObject, },
	txt_myDamage = { path = "Bg/Panel/damageBg/txt_myDamage", type = Text, },
	txt_100Damage = { path = "Bg/Panel/damageBg/txt_100Damage", type = Text, },
	tf_bossTipsBg = { path = "Bg/Panel/tf_bossTipsBg", type = Transform, },
	txt_bossTips = { path = "Bg/Panel/tf_bossTipsBg/txt_bossTips", type = Text, },
	txt_bossDamageRate = { path = "Bg/Panel/tf_bossTipsBg/txt_bossDamageRate", type = Text, },
	obj_weakBoss = { path = "Bg/Panel/obj_weakBoss", type = GameObject, },
	tog_monster_1 = { path = "Bg/toggleGroup/tog_monster_1", type = Toggle, value_changed_event = "OnTogMonster_1ValueChange"},
	img_boss1Img = { path = "Bg/toggleGroup/tog_monster_1/img_boss1Img", type = Image, },
	txt_boss1Lv = { path = "Bg/toggleGroup/tog_monster_1/txt_boss1Lv", type = Text, },
	ss_boss1StateIcon = { path = "Bg/toggleGroup/tog_monster_1/stateBg/ss_boss1StateIcon", type = SpriteSwitcher, },
	tog_monster_2 = { path = "Bg/toggleGroup/tog_monster_2", type = Toggle, value_changed_event = "OnTogMonster_2ValueChange"},
	img_boss2Img = { path = "Bg/toggleGroup/tog_monster_2/img_boss2Img", type = Image, },
	txt_boss2Lv = { path = "Bg/toggleGroup/tog_monster_2/txt_boss2Lv", type = Text, },
	ss_boss2StateIcon = { path = "Bg/toggleGroup/tog_monster_2/stateBg/ss_boss2StateIcon", type = SpriteSwitcher, },
	tog_monster_3 = { path = "Bg/toggleGroup/tog_monster_3", type = Toggle, value_changed_event = "OnTogMonster_3ValueChange"},
	img_boss3Img = { path = "Bg/toggleGroup/tog_monster_3/img_boss3Img", type = Image, },
	txt_boss3Lv = { path = "Bg/toggleGroup/tog_monster_3/txt_boss3Lv", type = Text, },
	ss_boss3StateIcon = { path = "Bg/toggleGroup/tog_monster_3/stateBg/ss_boss3StateIcon", type = SpriteSwitcher, },

}
