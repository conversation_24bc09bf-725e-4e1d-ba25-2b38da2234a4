-- admob.txt -------------------------------------------------
-- author:  李婉璐
-- date:    2020.11.11
-- ver:     1.0
-- desc:    谷歌移动广告sdk
--------------------------------------------------------------
local require = require
local os      = os

local log         = require "log"
local event       = require "event"
local flow_text   = require "flow_text"
local lang        = require "lang"
local version_mgr = require "version_mgr"
local q1sdk       = require "q1sdk"

local MobileAds = CS.GoogleMobileAds.AdMob
local RewardedAd = CS.GoogleMobileAds.Api.RewardedAd
local Q1Helper = CS.Q1.Q1Helper

module("admob")

local AD_UUID = 0
local useLuaRegister = version_mgr.CheckSvnTrunkVersion(49392)
local shouldRunGameThread = nil

function GetNextUUID()
	AD_UUID = AD_UUID + 1
	return AD_UUID
end

local AdMod = {}

function AdMod:ctor(selfType)
	self.adUnitId = nil
	self.rewardedAd = nil
	self.index = 0  --第几个广告位
	self.startTime = 0
	self.endTime = 0
	self.uuid = GetNextUUID()
	self.isVideoFinished = false -- 观看完视频
	self.showAd = false
end

function AdMod:RequestAndLoadRewardedAd(adUnitId, index)
	-- log.Warning("Admob, RequestAndLoadRewardedAd:", adUnitId)

	-- -- 根据内网外网，平台区分不同广告单元ID， RewardedAd 是一次性对象
	-- event.Trigger(event.GAME_EVENT_REPORT, "ads_start_load", {iType = index})
	-- self.index = index
	-- --log.Error("RequestAndLoadRewardedAd：", index)

	-- -- MobileAds.Instance.CreateRewardedAd 回调函数会被覆盖，直接使用 lua 进行消息注册，by YH
	-- if useLuaRegister then
	-- 	self.rewardedAd = MobileAds.Instance:CreateRewardedAd(adUnitId, nil)
	-- 	self:RegisterAdListener(true)
	-- else
	-- 	self.rewardedAd = MobileAds.Instance:CreateRewardedAd(adUnitId, function(value, tag1, tag2, msg)
	-- 		-- 此回调函数在并发调用时逻辑上会被最后一次调用覆盖
	-- 		-- 这里的index有问题
	-- 		self:AdCallBack(value, tag1, tag2, msg, self.index)	
	-- 	end)
	-- 	MobileAds.Instance:RegisterAdLoadedHandler(self.rewardedAd)
	-- 	MobileAds.Instance:RegisterAdFailedToLoadHandler(self.rewardedAd)
	-- 	MobileAds.Instance:RegisterAdOpeningHandler(self.rewardedAd)
	-- 	MobileAds.Instance:RegisterAdFailedToShowHandler(self.rewardedAd)
	-- 	MobileAds.Instance:RegisterAdClosedHandler(self.rewardedAd)
	-- 	MobileAds.Instance:RegisterUserEarnedHandler(self.rewardedAd)
	-- end

	-- MobileAds.Instance:LoadRewardedAd(self.rewardedAd)
	-- self.startTime = os.time()
end

function AdMod:RunAdCallbackOnThread(callback)
	if shouldRunGameThread == nil then
		shouldRunGameThread = q1sdk.IsEnable()
	end

	if shouldRunGameThread then
		Q1Helper.RunOnGameThread(callback)
	else
		callback()
	end
end

function AdMod:RegisterAdListener(bRegister)
	if self.rewardedAdOnAdLoaded == nil and bRegister then
		-- rewardedAdOnAdLoaded, rewardedAdOnAdFailedToLoad, rewardedAdOnAdOpening, rewardedAdOnAdFailedToShow, rewardedAdOnAdClosed, rewardedAdOnAdEarnedReward
		-- 等回调函数中不能调用打印日志，Unity 相关函数，此时并不在 Unity 主线程中， iOS 上概率引发崩溃问题
		self.rewardedAdOnAdLoaded = function (sender, args)
			self:RunAdCallbackOnThread(function () self:AdCallBack(true, 1, 0, "", self.index, self.rewardedAd) end)
		end
		self.rewardedAdOnAdFailedToLoad = function (sender, args)
			local message = args.Message
			if args.LoadAdError then
				message = args.LoadAdError:GetMessage()
			end
			self:RunAdCallbackOnThread(function () self:AdCallBack(true, 2, 0, message, self.index, self.rewardedAd) end)
		end
		self.rewardedAdOnAdOpening = function (sender, args)
			self:RunAdCallbackOnThread(function () self:AdCallBack(true, 3, 0, "", self.index, self.rewardedAd) end)
		end
		self.rewardedAdOnAdFailedToShow = function (sender, args)
			self:RunAdCallbackOnThread(function () self:AdCallBack(true, 4, 0, "", self.index, self.rewardedAd) end)
		end
		self.rewardedAdOnAdClosed = function (sender, args)
			self:RunAdCallbackOnThread(function () self:AdCallBack(true, 5, 0, "", self.index, self.rewardedAd) end)
		end
		self.rewardedAdOnAdEarnedReward = function (sender, args)
			self:RunAdCallbackOnThread(function () self:AdCallBack(true, 6, 0, "", self.index, self.rewardedAd) end)
		end
	end

	-- OnAdLoaded 声明为 event
	local opetion = nil
	if bRegister then
		opetion = "+"
	else
		opetion = "-"
	end
	-- 可能在主动销毁后，创建新广告时，ad_mgr 判定旧广告缓存的对象存在，再销毁一次。增加 self.rewardedAd 判断
	if self.rewardedAdOnAdLoaded and self.rewardedAd then
		self.rewardedAd:OnAdLoaded(opetion, self.rewardedAdOnAdLoaded)
		self.rewardedAd:OnAdFailedToLoad(opetion, self.rewardedAdOnAdFailedToLoad)
		self.rewardedAd:OnAdFailedToShow(opetion, self.rewardedAdOnAdFailedToShow)
		self.rewardedAd:OnAdOpening(opetion, self.rewardedAdOnAdOpening)
		self.rewardedAd:OnAdClosed(opetion, self.rewardedAdOnAdClosed)
		self.rewardedAd:OnUserEarnedReward(opetion, self.rewardedAdOnAdEarnedReward)
	end
end

function AdMod:ShowRewardedAd()
	if self.rewardedAd and self.rewardedAd:IsLoaded() then
		self.rewardedAd:Show()
	else
		log.Error("Rewarded ad is not ready yet.")
	end
end

function AdMod:IsLoaded()
	return self.rewardedAd and self.rewardedAd:IsLoaded()
end

function AdMod:AdResponseInfo( properties, rewardedAd )
	if rewardedAd == nil then
		return
	end
	local responseInfo = rewardedAd:GetResponseInfo()
	if responseInfo == nil then
		return
	end
	properties.responseId = responseInfo:GetResponseId()
	properties.adapter_class_name = responseInfo:GetMediationAdapterClassName()
	--log.Warning("admob >>ResponseInfo responseId:", properties.responseId, "class_name:", properties.adapter_class_name)
	properties.adapter_responses = responseInfo:ToString()
	--log.Warning("admob >>ResponseInfo responseId:", properties.responseId, "class_name:", properties.adapter_class_name, "responses:", properties.adapter_responses)
end

-- 广告加载回调
function AdMod:AdCallBack(value, tag1, tag2, msg, index, rewardedAd)
	if tag1 == 1 then
		-- 广告加载完成
		log.Warning("Admob, OnAdLoaded:", index)
		self.endTime = os.time()
		local properties = {iType = self.index, loadTime = self.endTime - self.startTime}
		self:AdResponseInfo(properties, rewardedAd)
		event.Trigger(event.GAME_EVENT_REPORT, "ads_loaded", properties)
		if self.showAd == true then
			self.showAd = false
			self:ShowRewardedAd()
		end
	end
	if tag1 == 2 then
		-- 广告加载失败
		log.Error("Admob, OnAdFailedToLoad:", index, msg)
		self.endTime = os.time()
		local properties = {iType = self.index, loadTime = self.endTime - self.startTime, errorMsg = msg}
		--self:AdResponseInfo(properties, rewardedAd)
		event.Trigger(event.GAME_EVENT_REPORT, "ads_loaded_failed", properties)
		if self.showAd == true then
			self.showAd = false
			flow_text.Add(lang.Get(184004))
		end
	end
	if tag1 == 3 then
		-- 广告开始展示
		log.Warning("Admob, OnAdOpening:", index)
	end
	if tag1 == 4 then
		-- 广告展示失败
		log.Error("Admob, OnAdFailedToShow:", index, msg)
	end
	if tag1 == 5 then
		-- 广告关闭时
		log.Warning("Admob, OnAdClosed:", index)
		event.Trigger(event.EVENT_REWAEDEDAD_CLOSED, index)
		event.Trigger(event.UNION_AD_GET_REWARDED, self.isVideoFinished)
	end
	if tag1 == 6 then
		-- 在用户因观看视频而应获得奖励时
		self.isVideoFinished = true
		log.Warning("Admob, OnAdEarnedReward:", index)
	end
	
	local puzzlegame_mgr = require "puzzlegame_mgr"
	if not puzzlegame_mgr.IsSockPackage() then
		event.Trigger(event.EVENT_REWARDEDAD_CALLBACK, tag1, index, msg)
	end
end

function AdMod:SetNeedShowAd(value)
	self.showAd = value
end

function AdMod:Dispose()
	-- log.Warning("Admob, Dispose:", self.index, ",uuid:", self.uuid)
	self:RegisterAdListener(false)

	if self.rewardedAd then
		if not useLuaRegister then
			MobileAds.Instance:UnregisterAdLoadedHandler(self.rewardedAd)
			MobileAds.Instance:UnregisterAdFailedToLoadHandler(self.rewardedAd)
			MobileAds.Instance:UnregisterAdOpeningHandler(self.rewardedAd)
			MobileAds.Instance:UnregisterAdFailedToShowHandler(self.rewardedAd)
			MobileAds.Instance:UnregisterAdClosedHandler(self.rewardedAd)
			MobileAds.Instance:UnregisterUserEarnedHandler(self.rewardedAd)
		end
		self.rewardedAd = nil
	end	
	self.index = 0
	self.isVideoFinished = false
	self.showAd = false
end

local class = require "class"
local object = require "object"

return class(object, nil, AdMod)