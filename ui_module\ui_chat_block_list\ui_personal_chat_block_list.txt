local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_personal_chat_block_list_binding"
local time_util = require "time_util"

--region View Life
module("ui_personal_chat_block_list")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)
    self:InitScrollRectTable()
    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if not util.IsObjNull(self.srt_Content) then 
        self.srt_Content:ItemsDispose()
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end

--设置记录列表刷新事件和销毁时间
function UIView:InitScrollRectTable()
    self.srt_Content.onItemRender = function (scroll_rect_item,index,dataItem)
        self:onRenderItem(scroll_rect_item,index,dataItem)
    end
    self.srt_Content.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data  then
          --rect_table Dispose时 Item上相关的资源是否需要Dispose
            -- scroll_rect_item.data["faceItem"][index]:Dispose()
            -- scroll_rect_item.data["faceItem"][index] = nil
            for i,v in pairs(scroll_rect_item.data["faceItem"]) do 
                v:Dispose()
                scroll_rect_item.data["faceItem"][i] = nil
            end
        end       
    end
end

--更新记录列表
function UIView:RefreshView(data,len) 
    UIUtil.SetActive(self.rtf_Nolist,not data or #data == 0) 
    if not data then 
        return
    end
    self.srt_Content:SetData(data,len or #data)
    self.srt_Content:Refresh(0, -1)
end

function UIView:onRenderItem(scroll_rect_item,index,dataItem) 
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local Root = scroll_rect_item:Get("Root")
    local name = scroll_rect_item:Get("name")
    local greetings = scroll_rect_item:Get("greetings")
    local time = scroll_rect_item:Get("time")
    local unlockBtn = scroll_rect_item:Get("btn_unlock")

    local data = dataItem

    if data.name then 
        name.text = data.name
    end

    if data.worldName then
        greetings.text = data.worldName
    end
    
    if scroll_rect_item.data["faceItem"] and scroll_rect_item.data["faceItem"][index] then 
        scroll_rect_item.data["faceItem"][index]:Dispose()
        scroll_rect_item.data["faceItem"][index] = nil
    else
        scroll_rect_item.data["faceItem"] = {}
    end

    if data.faceStr and data.frameID then 
        -- --点击头像事件
        -- local onClickFunc = function ()
        --     local mgr_personalInfo = require "mgr_personalInfo" 
        --     mgr_personalInfo.ShowRoleInfoView(data.dbid)
        -- end
        local face_item = require "face_item_new"
        scroll_rect_item.data["faceItem"][index] = face_item.CFaceItem():Init(Root.transform,nil,1.25)
        scroll_rect_item.data["faceItem"][index]:SetFaceInfo(data.faceStr,nil)
        scroll_rect_item.data["faceItem"][index]:SetFrameID(data.frameID,true)
        scroll_rect_item.data["faceItem"][index]:SetEnableState(true,false)
    end

    local onClickUnlockFunc = function ()
        local event = require "event"
        local log = require "log"
        log.Warning("onClickUnlockFunc dbid : ", data and data.dbid)
        event.Trigger(event.CHAT_BLOCK_PLAYER_UNLOCK_CLICK,data)
    end

    unlockBtn.onClick:RemoveAllListeners()
    unlockBtn.onClick:AddListener(onClickUnlockFunc)
end
--endregion

--region View Logic

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
