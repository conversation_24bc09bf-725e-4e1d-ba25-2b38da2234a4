-- battle_switch_manager.txt ------------------------------------------
-- author:  赖嘉明
-- date:    2021.07.01
-- ver:     1.0
-- desc:    战斗切换管理
--------------------------------------------------------------

local pairs = pairs
local require = require
local dump = dump

local util = require "util"
local event = require "event"
local module_jumping = require "module_jumping"
local battle_data = require "battle_data"
local common_new_pb = require "common_new_pb"
local ui_window_mgr = require "ui_window_mgr"
local time_scale_mgr = require "time_scale_mgr"
module("battle_switch_manager")

local battleDataForType = {}
local saveBattleData = nil

local timer = nil

--配置可以切出战斗的类型
local switchableConfig = {
    [common_new_pb.GameGoal] =  {switchable = true, openModuleID = "SceneItemType.Laymain", shielded = true}, --位面战纪
	[common_new_pb.TheTowerOfIllusion] =  {switchable = true, openModuleID = "SceneItemType.TowerBattle", shielded = true}, --星际通缉
    [common_new_pb.KillingTower] =  {switchable = true, openModuleID = "SociatyItemType.secretPlace"}, --杀戮场
    [common_new_pb.MultiKillingTower] =  {switchable = true, openModuleID = "SociatyItemType.secretPlace"}, --杀戮场多人
	[common_new_pb.Arena] =  {switchable = false, openModuleID = "SceneItemType.MatchPlace"}, --竞技场
	[common_new_pb.Ashdungeon] =  {switchable = false, openModuleID = "SceneItemType.Dungeon"}, --遗落之境
	[common_new_pb.BrokenSpaceTime] =  {switchable = false, openModuleID = "SceneItemType.ActivityCopy"}, --虚空锦标赛
	[common_new_pb.LeagueBoss] =  {switchable = false, openModuleID = "SceneItemType.Union"}, --联盟黑暗首脑
	[common_new_pb.Compete] =  {switchable = false, openModuleID = "SceneItemType.Union"}, --联盟切磋
	[common_new_pb.Maze] =  {switchable = false, openModuleID = "SceneItemType.DimondFazheng"}, --星际迷航
	[common_new_pb.Friend] =  {switchable = false, openModuleID = "MenuType.Friend"}, --好友切磋
	[common_new_pb.FactionType1] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --阵营通缉1
	[common_new_pb.FactionType2] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --阵营通缉2
	[common_new_pb.FactionType3] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --阵营通缉3
	[common_new_pb.FactionType4] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --阵营通缉4
	[common_new_pb.PeakOfTime] =  {switchable = false, openModuleID = "SceneItemType.BravePractice"}, --深空试炼
	[common_new_pb.EquipEctype] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --装备副本
	[common_new_pb.LeagueWar] =  {switchable = false, openModuleID = "SceneItemType.Union"}, --联盟战
	[common_new_pb.LeaActivityBoss] =  {switchable = false, openModuleID = "SceneItemType.Union"}, --联盟枢纽
	[common_new_pb.LeagueBossWar] =  {switchable = false, openModuleID = "SceneItemType.Union"}, --量子入侵
	[common_new_pb.VoidArena] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --虚空擂台
	[common_new_pb.TopRace] =  {switchable = false, openModuleID = "SceneItemType.MatchPlace"}, --巅峰赛
	[common_new_pb.SpaceGap] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --时空裂缝
	[common_new_pb.Plot] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --剧情副本
	[common_new_pb.SpaceExplorePirates] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --星际探索海盗
	[common_new_pb.EquipEctypeFuWen1] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --符文副本1
	[common_new_pb.EquipEctypeFuWen2] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --符文副本2
	[common_new_pb.EquipEctypeFuWen3] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --符文副本3	
	[common_new_pb.RookiePlot] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"},	--新手试炼剧情副本
	[common_new_pb.EquipEctypeFuWen4] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --符文副本4	
	[common_new_pb.EquipEctypeFuWen5] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --符文副本5	
	[common_new_pb.Legend] =  {switchable = false, openModuleID = "SceneItemType.MatchPlace"}, --传奇锦标赛
	[common_new_pb.EquipEctypeZhuanShu] =  {switchable = false, openModuleID = "SceneItemType.TowerBattle"}, --装备专属本
}

--是否在某个类型的战斗中
function IsInBattle(type)
    return false
end

--是否正在战斗
function IsBattling(type)
    local isBattling = battleDataForType[type] and battleDataForType[type].isBattling or false
    return IsSwitchable(type) and isBattling
end

--是否在战斗中屏蔽切出（只客户端屏蔽）
function IsShielded(type)
    return switchableConfig[type] and switchableConfig[type].shielded
end

--是否可以切出
function IsSwitchable(type)
    return switchableConfig[type] and switchableConfig[type].switchable or false
end

--保存对应类型战斗的相关回调函数,切回战斗时设置
function SetBattleFunction(type, canSkipBattle)
    if battleDataForType[type] == nil then
        battleDataForType[type] = {}
    end

    battleDataForType[type].canSkipBattle = canSkipBattle
end

function GetBattleFunction(type)
    if battleDataForType[type] == nil then
        return nil
    end
    return  battleDataForType[type].canSkipBattle
end


--切回战斗
function SwitchIn(type)
    local battle_manager = require "battle_manager"
    if util.GetServerTime() >= battle_manager.GetMultipleBattleEndTimeBy(type) and IsBattling(type) then
        return
    end

    local menu_bot_data = require "menu_bot_data"
    menu_bot_data.CloseAllPage()

    battle_manager.CutOverBackGroundBattle(type)
end

--切出战斗
function SwitchOut(battleID)
    local type = battle_data.stageType

    if not IsSwitchable(type) then
        return
    end
    if not IsBattling(type) then
        --return
    end

    local battle_manager = require "battle_manager"
    battle_manager.CutOutCurrentBattle()

    InitTimer()

    local scene_manager = require "scene_manager"
    scene_manager.instance():DestroySceneByName("scene_battle")

    time_scale_mgr.ResetTimeScale()

    battle_manager.UnregisterHandler()
    local player = battle_manager.GetBattlePlayer()
    if player and (not util.IsObjNull(player)) then
        player:ClearBattle()
    end
    ui_window_mgr:UnloadModule("ui_in_battle")

    if battleDataForType[type] == nil then
        battleDataForType[type] = {}
    end
    battleDataForType[type].isBattling = true

    event.Trigger(event.SCENE_BATTLESTATE, type, nil, true)

    local menu_bot_data = require "menu_bot_data"
    menu_bot_data.SetLastPage(4)
    ui_window_mgr:ShowModule("ui_lobby")
end

--设置当前类型战斗是否在战斗中
function SetBattleState(type, isBattling)
    if battleDataForType[type] then
        battleDataForType[type].isBattling = isBattling
    end
end

--战斗结束
function OnBattleEnd(type, battleID)
    if not IsSwitchable(type) then
        return
    end

    if battleDataForType[type] then
        battleDataForType[type].isBattling = false
        battleDataForType[type].canSkipBattle = nil
    end
    event.Trigger(event.SCENE_BATTLESTATE, type, nil, false)

    --战斗结束时领奖,领奖后服务器再次给客户端下发战斗结束战报（MSG_GAMEOVER_NTF）
    if battleID then
        local battle_message = require "battle_message"
        battle_message.C2SBattleResult(battleID)
    end
    local idle = require "idle"
    idle.ClearNormalPool(0)
    -- if IsInBattle(type) then
    --     return
    -- end
    -- local scene_manager = require "scene_manager"
    -- local scene_manager_instance = scene_manager.instance()
    -- if scene_manager_instance:IsCurrentScene("scene_loading") or scene_manager_instance:IsCurrentScene("scene_battle") then
    --     --在战斗逻辑场景，先跳出不显示结算
    --     return
    -- end
end

--战斗结束跳转回对应玩法
function Jump(type)
    if not switchableConfig[type] then
        return
    end
    ui_window_mgr:CloseAllUIAndScene()
    module_jumping.Jump(switchableConfig[type].openModuleID)
end

--检测后台运行的战斗是否结束
function CheckBattleOver()
    for type, report in pairs(battle_data.battleReports) do
        local battle_manager = require "battle_manager"
        local endTime = battle_manager.GetMultipleBattleEndTimeBy(type)
        local nowTime = util.GetServerTime()
        if IsBattling(type) and endTime and nowTime >= endTime then
            OnBattleEnd(type, report.battleID)
        end
    end
end

--初始化检测定时器
function InitTimer()
    if not timer then
        if util.get_len(battle_data.battleReports) == 0 then
            return
        end
        timer = util.IntervalCall(1, function()
            CheckBattleOver()
            if util.get_len(battle_data.battleReports) == 0 then
                if timer then
                    util.RemoveDelayCall(timer)
                    timer = nil
                end
                return true
            end
        end)
    end
end

function SaveBattleData(stageType, report, battleID, rewards, scores, battlePartyName)
    saveBattleData = {stageType = stageType, report = report, battleID = battleID, rewards = rewards, scores = scores, battlePartyName = battlePartyName}
end

function ShowSaveResultAfterBattle()
    util.DelayCallOnce(0.5, function()

        --dump(saveBattleData)
        local data = saveBattleData
        if data == nil then
            return
        end
        local force_guide_system = require "force_guide_system"
        if force_guide_system.GetState() == force_guide_system.ENUM_STATE.IN_GUIDE then
            saveBattleData = nil
            return
        end
        local report = data.report
        if report then
            battle_data.rewards = data.rewards
			battle_data.scores = data.scores

            local multiple_battle_report_creator = require "multiple_battle_report_creator"
            local battle_manager = require "battle_manager"
            local bits = battle_manager.ParseRealBattleReport(report)
            local tbs_pb = require "tbs_pb"
            local pbMsg = tbs_pb.TbsReports()  
            pbMsg:ParseFromString(bits)
            --重新构建battle_data的数据
            multiple_battle_report_creator.CreateNewReportBy(pbMsg, #pbMsg.reports + 1)

            battle_manager.SetBattleRecordData(data.battleID, data.battlePartyName)

            if #data.battleID == 1 then
                battle_manager.OnBattleEvent("BattleEnd", true, data.stageType)
            else
                battle_manager.OnMultiTbsBattleEvent("BattleEnd", true, data.stageType)
            end
        end
        saveBattleData = nil
    end)
end
event.Register(event.BATTLE_RESULT_CLOSE, ShowSaveResultAfterBattle)

function Clear()
    battleDataForType = {}
    saveBattleData = nil
    if timer then
        util.RemoveDelayCall(timer)
        timer = nil
    end
end
event.Register(event.SCENE_DESTROY_NEW, Clear)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, Clear)
