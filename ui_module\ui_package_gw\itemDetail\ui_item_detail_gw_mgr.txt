---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by <PERSON><PERSON><PERSON>.
--- DateTime: 2024/9/3 10:58
local require = require
local math = math
local string = string
local util = require "util"
local event = require "event"
local evt_item_define = require "evt_item_define"
local item_data = require "item_data"
local lang = require "lang"
local game_scheme = require "game_scheme"
local ui_item_detail_gw_data = require "ui_item_detail_gw_data"
local ui_window_mgr = require "ui_window_mgr"
local flow_text = require "flow_text"
local GWG = GWG
---@class ui_item_detail_gw_mgr
local M = {}
function M.Init()

end

---等级资源宝箱和自选等级资源宝箱类型均为enUseItem_OtherResourceBox（特殊，服务器定制）
local function UseDefaultItem(data, count, rewardId)
    local useData = {
        goodsId = data.id,
        count = count,
        rewardId = rewardId
    }
    event.Trigger(evt_item_define.Evt_UseItemOne, useData)
end

function M.UseDefaultItemFunc(data, useType, rewardId)
    local count = 1
    if not useType or useType == ui_item_detail_gw_data._CanUseType.AutoUseOne then
        count = 1
    end
    UseDefaultItem(data, count, rewardId)
end

--region 道具使用前置函数处理入口，道具使用成功后回调函数处理入口
---使用城市保护道具
local function UseCityProtectItem(data, count)
    --  DYF 城市保护道具，使用前要判断是否已有护罩
    local evt_cityProtect_define = require "evt_cityProtect_define"
    local useData = {
        goodsId = data.id,
        count = 1,
    }

    local sandBuff_module_data = require "sandBuff_module_data"
    local isHaveSameType = sandBuff_module_data.IsHaveSameTypeData_CityProtect()
    if isHaveSameType then
        local cityProtect_data = require "cityProtect_data"
        local buffCfg = cityProtect_data.GetGWMapBuffCfgByItemId(useData.goodsId)
        --弹出提示弹窗
        local tipStr = string.format2(lang.Get(560242), math.round(buffCfg.time / 3600))
        local message_box = require "message_box"
        local function messageBoxCallback(callbackData, nRet)
            if message_box.RESULT_YES == nRet then
                event.Trigger(evt_cityProtect_define.Evt_UseCityProtectItem, useData)
            end
        end
        message_box.Open(tipStr, message_box.STYLE_YESNO, messageBoxCallback, 0, lang.KEY_OK, lang.KEY_CANCEL, lang.KEY_SYSTEM_TIPS)

    else
        event.Trigger(evt_cityProtect_define.Evt_UseCityProtectItem, useData)
    end
end

local function GetFragementLimit(sid)
    local limit = 100
    local ui_item_detail = require "ui_item_detail_gw"
    if ui_item_detail.CheckHasEquip(sid) then
        --装备合成判断装备背包
        local equipment_mgr = require "equipment_mgr"
        local remain = (equipment_mgr.GetMaxEquipCount() - equipment_mgr.GetEquipCount())
        if remain > 0 then
            limit = math.min(limit, remain)
        end
    else
        --英雄合成判断英雄背包
        local hero_mgr = require "hero_mgr"
        local full, remain = hero_mgr.CheckHeroPackageFull()
        if remain > 0 then
            limit = math.min(limit, remain)
        end
    end
    return limit
end

---合成道具
local function GoodsCompose(data, count)
    count = math.min(count, GetFragementLimit())
    event.Trigger(evt_item_define.Evt_GoodsCompose, { sid = data.sid, count = count })
end

--固定资源宝箱的rewardId固定只有一个
local function GetRewardId_DeterminateBox(boxCfgId)
    return game_scheme:BoxChoose_0(boxCfgId).reward.data[0]
end

---gw使用宝箱处理
local function UseChestItem(data, count)
    local rewardId
    if data.boxType == item_data.BoxChooseUnpackingType.Normal then
        rewardId = GetRewardId_DeterminateBox(data.id)
    end

    local useData = {
        goodsId = data.id,
        count = count,
        rewardId = rewardId
    }
    event.Trigger(evt_item_define.Evt_UseItemOne, useData)
end

local function UseGwHeroDebris(data, count)
    --通过英雄碎片,可以得到英雄的HeroID吗
    local gw_hero_data = require "gw_hero_data"

    local heroData = gw_hero_data.GetHeroEntityByDebris(data.id)

    if heroData then
        if heroData.isUnLock then
            --已经解锁显示军衔
            ui_window_mgr:ShowModule("ui_gwhero_base", nil, nil, {
                heroCfgId = heroData.heroCfg.heroID,
                index = 3,
                isUnLock = true
            })
        else
            --没解锁的显示背包
            ui_window_mgr:ShowModule("ui_gw_hero_list")
        end
    end
    ui_window_mgr:UnloadModule("ui_item_detail_gw")

end

local function UseSurvivorSplinter(data, count)
    local gw_home_survivor_data = require "gw_home_survivor_data"
    local log = require "log"
    local survivorId = gw_home_survivor_data.GetSurvivorIdByItemId(data.id)
    if survivorId and survivorId ~= 0 then
        local showIndex = {
            sid = survivorId
        }
        --打开幸存者列表，同时滚动到对应的幸存者标签处。
        ui_window_mgr:ShowModule("ui_survivor_list", nil, nil, showIndex)
        --幸存者已解锁的话，则显示幸存者的详情页
        local showData = {
            id = survivorId
        }
        ui_window_mgr:ShowModule("ui_survivor_info_panel", nil, nil, showData)

    else
        log.Error("id为" .. data.id .. "的物品判定为幸存者碎片，但幸存者表格中并未找到对应的幸存者！")
    end
end

local function UseChangeNameCard(data, count)
    local mgr_personalInfo = require "mgr_personalInfo"
    mgr_personalInfo.ShowPersonalChangeView()
end

local function UseStaminaItem(data, count)
    local useNum = count        --未有批量使用方法
    local useId = data.id  --消耗道具id
    local reportMsg = {
        power_use_item = string.format("%d#%d", useId, useNum), -- 消耗道具（道具ID#道具数量）
    }
    event.EventReport("stamina_UseItem", reportMsg)
    local sandbox_ui_mgr = require("sandbox_ui_mgr")
    sandbox_ui_mgr.StaminaOperate_UseItem(data.id)
    ui_window_mgr:UnloadModule("ui_item_detail_gw")
end

local function UseAllianceMoveCity(data, count)
    -- 请求时就设置下一次进入的视角高度
    local gw_camera_const = require "gw_camera_const"
    local gw_sand_camera_mgr = require "gw_sand_camera_mgr"
    gw_sand_camera_mgr.OnSetNextResetDxf(gw_camera_const.SCameraDefaultDfxLevel5)
    UseDefaultItem(data, count)
end

---使用道具流程
M._PackageUseItem_Map = {
    [item_data.Item_Type_Enum.CityProtect] = UseCityProtectItem,
    [item_data.Item_Type_Enum.TimeRewardItem] = UseDefaultItem,
    [item_data.Item_Type_Enum.Fragement] = GoodsCompose,
    [item_data.Item_Type_Enum.Chest] = UseChestItem,
    [item_data.Item_Type_Enum.GWHeroDebris] = UseGwHeroDebris,
    [item_data.Item_Type_Enum.GWSurvivorSplinter] = UseSurvivorSplinter,
    [item_data.Item_Type_Enum.ChangeNameCard] = UseChangeNameCard,
    [item_data.Item_Type_Enum.StaminaItem] = UseStaminaItem,
    [item_data.Item_Type_Enum.MoveCity_Alliance] = UseAllianceMoveCity,
    [item_data.Item_Type_Enum.NamePlate] = UseDefaultItem,
}

local function ClosePackage()
    ui_window_mgr:UnloadModule("ui_item_detail_gw")
    ui_window_mgr:UnloadModule("ui_package_gw")
end

local function UseRandomMoveCityItem_Rsp()
    ClosePackage()
    local gw_common_util = require "gw_common_util"
    gw_common_util.JumpToBase()
end

local function UseAllianceMoveCityItem_Rsp()
    ClosePackage()
    local gw_common_util = require "gw_common_util"
    local gw_camera_const = require "gw_camera_const"
    gw_common_util.SetCameraDxfAndJumpToGrid(gw_camera_const.SCameraDefaultDfxLevel5, nil, nil, gw_common_util.GetSandZoneSandBoxSid())
end

local function UseNamePlateItem_Rsp()
    --ClosePackage()
    --ui_window_mgr:ShowModule("ui_personalised", nil, nil)
    local LANG_ID = 1008895 --使用成功
    flow_text.Add(lang.Get(LANG_ID))
    ui_window_mgr:UnloadModule("ui_item_detail_gw")
end


---使用道具成功特殊流程
M._PackageUseItem_Rsp_Success_Map = {
    [item_data.Item_Type_Enum.MoveCityItem_Random] = UseRandomMoveCityItem_Rsp,
    [item_data.Item_Type_Enum.MoveCity_Alliance] = UseAllianceMoveCityItem_Rsp,
    [item_data.Item_Type_Enum.NamePlate] = UseNamePlateItem_Rsp,
}

--endregion

--region道具点击详情处理入口

--装饰物点击详情
local function ClickDetail_Decoration(data)
    local baseBuildingData = GWG.GWHomeMgr.buildingData.GetDecorateDataByItemID(data.id)
    if baseBuildingData then
        baseBuildingData.buildingLv = baseBuildingData.buildingLv > 0 and baseBuildingData.buildingLv or 1
        local tmp_data = {
            data = baseBuildingData,
        }
        GWG.GWHomeMgr.buildingData.SetDecorateBuildingData(tmp_data)
        ui_window_mgr:ShowModule("ui_decorate_building_tips_panel", nil, nil, { show_cur = false })
    end
end

--结晶点击详情
local function ClickDetail_Crystal(data)
    local crystalCfg = game_scheme:MagicWeaponCrystal_0(data.id,0)
    if crystalCfg then
        ui_window_mgr:ShowModule("ui_new_magic_weapon_crystal_upgrade_simple",nil,nil,{crystalCfg = crystalCfg,openType = 1 })
    end
end

--不走通用道具详情界面，特殊处理函数入口
M._ItemClickDetail_Map = {
    [item_data.Item_Type_Enum.Decoration] = ClickDetail_Decoration,
    [item_data.Item_Type_Enum.Crystal] = ClickDetail_Crystal,
    
}

--endregion

return M