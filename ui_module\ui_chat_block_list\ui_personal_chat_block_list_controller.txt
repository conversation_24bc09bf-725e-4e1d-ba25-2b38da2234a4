local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local event = require "event"
local net_click_liking = require "net_click_liking"
local net_chat_module_new = require "net_chat_module_new"
local net_personalInfo = require "net_personalInfo"
local event_personalInfo= require "event_personalInfo"
local role_pb = require "role_pb"
local click_liking_mgr = require "click_liking_mgr"
local ui_util = require "ui_util"
--region Controller Life
module("ui_personal_chat_block_list_controller")
local controller = nil
local UIController = newClass("ui_personal_chat_block_list_controller", controller_base)

local CONST_MAXMESSAGE = 100 --最大可接收记录数
--点赞描述
local LikeDialog = {
    [role_pb.enLikeSysType_RolePage] = 1007321,         --个人界面
    [role_pb.enLikeSysType_SxMainCity] = 1007322,       --沙盘主城
    [role_pb.enLikeSysType_Radar] = 1007323,            --雷达
    [role_pb.enLikeSysType_AcornPub] = 1007324,         --橡果酒馆
    [role_pb.enLikeSysType_SurpriseBox] = 1007325,      --惊喜盒
    [role_pb.enLikeSysType_PresidentialMail] = 1007326, --总统邮件
    [role_pb.enLikeSysType_AllianceMail] = 1007327,     --联盟邮件
    [role_pb.enLikeSysType_AllianceShip] = 1007328,     --联盟火车
    [role_pb.enLikeSysType_TreasureMap] = 1007329,      --藏宝图
    [role_pb.enLikeSysType_BattleDuel] = 1007330,       --战区对决
    [role_pb.enLikeSysType_AllianceFire] = 1007331,     --联盟灭火
    [role_pb.enLikeSysType_ZombieDisaster] = 1007332,   --丧尸灾变
}

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)    
end

function UIController:OnShow()
    self.__base.OnShow(self)
    local log = require "log"
    log.Warning("ui_personal_chat_block_list_controller OnShow")
    net_chat_module_new.Send_CHAT_BLOCK_LIST()
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

--设置列表数据
function UIController:BuildRenderData(playerList)
    local finalData = {}
    local log = require "log"
    log.Warning("BuildRenderData playerList count : ", playerList and #playerList)
    if playerList and #playerList > 0 then 
        for i,v in ipairs(playerList) do 
            log.Warning("BuildRenderData playerList : ", v.leagueName, v.name, v.roleID)
            local temp = {
                dbid = v.roleID,
                name = self:ComposeName(v.leagueShortName,v.name),
                faceStr = v.faceStr,
                frameID = v.frameID,
                worldName = self:ComposeWorldName(v.worldId),
                occurTime = "",
                realName = v.name,
            }
            table.insert(finalData,temp)
        end
    end
    return finalData
end

function UIController:ComposeWorldName(worldId) 
    local str = ""
    local tipStr = lang.Get(650101)
    str = string.format2(tipStr,worldId)
    return str
end

function UIController:ComposeName(leagueShortName,name) 
    local str = ""
   
    if leagueShortName and leagueShortName ~= "" then 
        str = string.format("%s[%s] ",str,leagueShortName)
    end
    if name then 
        str = string.format("%s%s",str,name)
    end
    return str
end

--刷新列表
function UIController:RefreshView(playerList)
    local viewData = self:BuildRenderData(playerList)
    self:TriggerUIEvent("RefreshView",viewData,#viewData)
end


function UIController:AutoSubscribeEvents() 
    self.RefreshList = function (evt,playerList)
        self:RefreshView(playerList)
    end

    self.OnBtnUnlockClicked =function(evt,playerData)
        local log = require "log"
        log.Warning("OnBtnUnlockClicked dbid : ", playerData and playerData.dbid)
        if not playerData or not playerData.dbid then
            return
        end

        net_chat_module_new.Send_CHAT_BLOCK(false,playerData.dbid)

        local flow_text = require "flow_text"
        flow_text.Add(string.format2(lang.Get(650103),playerData.realName))
    end
    self:RegisterEvent(event.CHAT_BLOCK_PLAYER_LIST_RSP,self.RefreshList)
    self:RegisterEvent(event.CHAT_BLOCK_PLAYER_UNLOCK_CLICK,self.OnBtnUnlockClicked)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
