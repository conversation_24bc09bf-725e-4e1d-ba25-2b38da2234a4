# 怪物位置重叠解决方案测试说明

## 功能概述
提供两种方案解决Timer模式下同时生成多个怪物队伍时的位置重叠问题：
- **方案A（当前启用）**：渐进式改进 - 增强随机偏移 + 动态NavMeshAgent延迟
- **方案B（可选）**：智能位置分散 - 自动检测冲突并重新分配位置

## 当前启用：方案A - 渐进式改进

### 方案A改进内容

#### 1. 增强的随机偏移
- **横向偏移**：从±1.5单位增加到±4单位（8单位范围）
- **纵向偏移**：从0-5单位增加到0-8单位
- **初始位置偏移**：新增±3单位的初始位置随机偏移

#### 2. 动态NavMeshAgent延迟
- **基础延迟**：2秒（原来的固定延迟）
- **动态调整**：根据周围10单位内的队伍数量调整
- **延迟范围**：2-4秒
  - 0-1个队伍：2秒
  - 2-3个队伍：2.5秒
  - 4-5个队伍：3秒
  - 6+个队伍：4秒

#### 3. 详细日志
- 初始位置偏移日志：`[ENHANCED_POSITIONING] TeamID: X applied initial offset`
- NavMeshAgent启用日志：`[ENHANCED_POSITIONING] TeamID: X NavMeshAgent enabled after X seconds`

## 方案B - 智能位置分散（暂时关闭）

### 开关控制
在 `kingshot_team.txt` 中修改配置：
```lua
local SMART_POSITIONING_CONFIG = {
    ENABLE_SMART_POSITIONING = true,  -- 改为true启用方案B
    SAFE_DISTANCE = 2.0,
    MIN_RADIUS = 3.0,
    RADIUS_PER_TEAM = 1.0,
    ENABLE_LOGGING = true,
}
```

### 实现原理
- 使用并查集算法检测位置冲突
- 圆形分布重新分配冲突队伍位置
- 保持相对位置关系的同时确保安全间距

## 测试场景

### 方案A测试场景

#### 场景1：完全重叠位置
配置7-8个队伍在完全相同的位置(x=100, z=100)同时生成
**期望结果**：
- 每个队伍获得±3单位的初始位置偏移
- 路径移动时有±4单位的横向偏移
- NavMeshAgent启用延迟2-4秒，密度高的区域延迟更长

#### 场景2：接近位置
配置队伍在接近位置生成（距离3-5单位）
**期望结果**：
- 初始偏移和路径偏移进一步拉开距离
- 根据密度调整NavMeshAgent启用时间

#### 场景3：分散位置
配置队伍在较远位置生成（距离>10单位）
**期望结果**：
- 保持原有偏移逻辑
- NavMeshAgent延迟保持在2秒左右

### 方案B测试场景（需要先启用）

#### 场景1：完全重叠位置
**期望结果**：队伍会以圆形分布重新排列，间距约2单位

#### 场景2：部分重叠位置
**期望结果**：只有距离小于2单位的队伍会被重新分配

## 日志监控

### 方案A日志
- `[ENHANCED_POSITIONING] TeamID: X applied initial offset: X Y new position: X Y Z`
- `[ENHANCED_POSITIONING] TeamID: X detected X nearby teams, delay set to X seconds`
- `[ENHANCED_POSITIONING] TeamID: X NavMeshAgent enabled after X seconds`

### 方案B日志（启用时）
- `[SMART_POSITIONING] Processing X teams for smart positioning`
- `[SMART_POSITIONING] Redistributing positions for X conflicting teams`
- `[SMART_POSITIONING] TeamID: X repositioned to: X, Y, Z`

## 方案切换

### 启用方案B
在 `kingshot_team.txt` 第11行修改：
```lua
ENABLE_SMART_POSITIONING = true,  -- 改为true
```

### 回到方案A
```lua
ENABLE_SMART_POSITIONING = false,  -- 改为false
```

## 调试建议
1. **方案A调试**：
   - 观察初始位置偏移是否足够
   - 检查NavMeshAgent延迟是否合理
   - 监控密集区域的推挤情况

2. **方案B调试**：
   - 调整SAFE_DISTANCE参数
   - 观察圆形分布效果
   - 检查重新分配的位置是否合理

3. **性能监控**：
   - 方案A：几乎无性能影响
   - 方案B：O(n²)复杂度，但n通常很小
