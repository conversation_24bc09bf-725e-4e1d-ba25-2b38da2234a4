local require = require
local print = print
local pairs = pairs
local assert = assert
local type = type
local typeof = typeof

local GameObject = CS.UnityEngine.GameObject

local LuaGameObjectPool = require "LuaGameObjectPool"

local UI = CS.UnityEngine.UI

local class = require "class"
local base_object = require "base_object"
local util = require "util"
local log = require "log"
local asset_loader = require "asset_loader"
local dump = dump
local string = string
module("base_item_object")

local BaseItemObject = {}
BaseItemObject.widget_table = {}
local ItemTypePool = {}

function BaseItemObject:ctor(selfType, tag)
    self.gameObject = nil
    self.transform = nil
    self.tag = tag or "base_item_object"
end

function BaseItemObject:LoadResource(assetBundleName, assetName, callback, useIO, parentTrans, keyName, comTypeCfg)
    if not assetBundleName then
        log.Error("BaseItemObject assetBundleName nil", assetBundleName)
        return
    end

    if keyName then
        self.gameObjectPoolKey = assetBundleName .. "@" .. keyName;
        --self.gameObjectPoolKey=nil;
    else
        self.gameObjectPoolKey = assetBundleName;
    end

    self.useIO = useIO;
    --print("LoadResource",assetBundleName)
    self.isDisposed = false
    self.__bg_callback = callback
    self.comTypeCfg = comTypeCfg;
    --dump("baseitemobject0000")
    local itemTypeObject = ItemTypePool[self.gameObjectPoolKey];
    if util.IsObjNull(itemTypeObject) then
        --dump("baseitemobject2222")
        self.io_loader = self.io_loader or asset_loader(assetBundleName, self.tag)
        self.io_loader.assetPath = assetBundleName
        self.io_loader:load(function(loader)
            local UIPrefab = loader.asset
            if self:IsDisposed() or (parentTrans and util.IsObjNull(parentTrans)) then
                return
            end
            -- 加载资源回调，如果当前物体与当前加载资源不一致，销毁
            if self.gameObject and not util.IsObjNull(self.gameObject) then
                if self._go_assetBundleName ~= loader.assetPath then
                    GameObject.DestroyImmediate(self.gameObject)
                    self.gameObject = nil
                    self.UIRoot = nil
                end
            end

            if util.IsObjNull(self.gameObject) then
                --dump(ItemTypePool,"baseitemobject000")
                itemTypeObject = ItemTypePool[self.gameObjectPoolKey];
                if util.IsObjNull(itemTypeObject) then
                    itemTypeObject = GameObject.Instantiate(UIPrefab)
                    itemTypeObject.name = UIPrefab.name
                    ItemTypePool[self.gameObjectPoolKey] = itemTypeObject;
                    if self.comTypeCfg then
                        for i = itemTypeObject.transform.childCount - 1, 0, -1 do
                            local childTrans = itemTypeObject.transform:GetChild(i);
                            local isDel = true;
                            if self.comTypeCfg.name_dic[childTrans.name] then
                                isDel = false;
                            end
                            if isDel then
                                GameObject.DestroyImmediate(childTrans.gameObject)
                                -- dump(childTrans.name,"baseitemobject88888888")
                            end
                        end

                        --dump(keyName,"ItemTypePoolItemTypePool")
                    end
                    --local LuaProfiler = MikuLuaProfiler.LuaProfiler
                    --LuaProfiler.BeginSampleCustom("UpdateScrollList")
                end
                self.UIRoot = GameObject.Instantiate(itemTypeObject, parentTrans, false)
                self.gameObject = self.UIRoot
            else
                log.Warning("BaseItemObject self.gameObject exist:skip Instantiate", assetBundleName)
            end
            --dump(self.gameObject:GetInstanceID(),"baseitemobject000")
            self._go_assetBundleName = assetBundleName
            self.transform = self.gameObject.transform
            self.gameObject:SetActive(true)

            util.DontDestroyOnLoad(self.gameObject)

            local lang_util = require "lang_util"
            lang_util.SetFont(self.gameObject)

            self:_OnLoadComplete(self.__bg_callback)
        end)

    else
        if self:IsDisposed() or (parentTrans and util.IsObjNull(parentTrans)) then
            return
        end
        -- 加载资源回调，如果当前物体与当前加载资源不一致，销毁
        if self.gameObject and not util.IsObjNull(self.gameObject) then
            if self._go_assetBundleName ~= loader.assetPath then
                GameObject.DestroyImmediate(self.gameObject)
                self.gameObject = nil
                self.UIRoot = nil
            end
        end

        if self.useIO then
            self.UIRoot = GameObject.Instantiate(itemTypeObject, parentTrans, false);
        else
            self.UIRoot = LuaGameObjectPool.Instantiate(self.gameObjectPoolKey, itemTypeObject, parentTrans, false)
        end

        self.gameObject = self.UIRoot

        self._go_assetBundleName = assetBundleName
        self.transform = self.gameObject.transform
        self.gameObject:SetActive(true)
        util.DontDestroyOnLoad(self.gameObject)
        local lang_util = require "lang_util"
        lang_util.SetFont(self.gameObject)
        self:_OnLoadComplete(self.__bg_callback)

    end

end

function BaseItemObject:_OnLoadComplete(callback)
    local rootTrans = self.gameObject.transform
    self.transform = rootTrans

    local temp_widget = self.widget_table
    if self.comTypeCfg then
        temp_widget = self.comTypeCfg.widget_table;
    end

    for k, v in pairs(temp_widget) do
        local child = nil
        if v.path == "" then
            child = rootTrans
        else
            child = rootTrans:Find(v.path)
        end
        -- dump(v,"_OnLoadComplete=="..v.path)
        assert(child, self.gameObject.name .. " " .. v.path .. " not found!")
        if type(v.type) == "string" then
            self[k] = child:GetComponent(typeof(UI[v.type]))
            assert(v.optional or self[k], self.gameObject.name .. " " .. v.type .. " component not found!")
        else
            self[k] = child:GetComponent(typeof(v.type))
            assert(v.optional or self[k], self.gameObject.name .. " " .. typeof(v.type).Name .. " Component not found!")
        end
    end

    self:OnResLoad()

    if callback ~= nil then
        callback(self.gameObject)
    end
end

function BaseItemObject:OnResLoad()
    --------print("BaseGameObject:OnResLoad")
end

function BaseItemObject:IsDisposed()
    return self.isDisposed
end

function BaseItemObject:Dispose()


    if self.___prevent then
        log.Error("BaseItemObject prevent", self.loading_assetBundleName)
    end

    self.isDisposed = true

    if self.useIO then
        if self.gameObject ~= nil then
            if not self.gameObject:IsNull() then
                GameObject.DestroyImmediate(self.gameObject)
            end
        end
        if self.io_loader then
            self.io_loader:Dispose()
        end

    else
        LuaGameObjectPool.Release(self.gameObjectPoolKey, self.gameObject)
        self.loading_assetBundleName = nil
    end

    local temp_widget = self.widget_table
    if self.comTypeCfg then
        temp_widget = self.comTypeCfg.widget_table;
    end

    for k, v in pairs(temp_widget) do
        self[k] = nil
    end

    self.gameObject = nil
    self.UIRoot = nil
    self.__bg_callback = nil

    self.__base:Dispose()
end

function BaseItemObject:IsValid()
    if self and self.UIRoot and not self.UIRoot:IsNull() and not self.isDisposed then
        return true
    else
        return false
    end
end

return class(base_object, nil, BaseItemObject)